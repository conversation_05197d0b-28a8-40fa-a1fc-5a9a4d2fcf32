<template>
  <t-dialog
    v-model:visible="visible"
    :z-index="2500"
    attach="body"
    class="dialogSet20240801 dialogSetHeader dialogSetFooter_top_0 dialogNoDefault"
    width="480px"
  >
    <template #header>
      <div class="header">
        <!-- <svg class="iconpark-icon header-svg"><use href="#attention-6ebn71gl"></use></svg>
				<div class="header-title">提示</div> -->
        {{ $t("member.bolit.b") }}
      </div>
    </template>
    <template #body>
      <div class="toBody">
        <div class="area">
          <t-textarea
            v-model="select.area"
            :autosize="{ minRows: 4 }"
            clearable
            :maxlength="200"
            class="textarea"
            :placeholder="$t('member.bolit.a')"
          />
          <span class="count">{{select.area?.length}}/200</span>
        </div>
      </div>
    </template>
    <template #closeBtn>
      <!-- <iconpark-icon name="close" size="16"></iconpark-icon> -->
      <!-- <svg class="iconpark-icon" style="width: 16px; height: 16px">
        <use href="#close" />
      </svg> -->
      <iconpark-icon name="iconerror" style="font-size: 24px"></iconpark-icon>
    </template>
    <template #footer>
      <div class="footer">
        <t-button
          theme="default"
          variant="outline"
          @click="onClose"
        >取消</t-button>
        <t-button theme="primary" class="ml-8" @click="onSave" :disabled="!select.area?.trim()">{{
          $t("member.sure")
        }}</t-button>
      </div>
    </template>
  </t-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, Ref } from "vue";
import { MessagePlugin } from "tdesign-vue-next";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const emits = defineEmits(["onSend"]);

// 从公司组织架构导入
// const onImportFromCompany = () => {
// 	emits('onImportFromCompany');
// };
// // 手工建立组织架构
// const onHandCreate = () => {
// 	emits('onHandCreate');
// };

const select = reactive({
  id: 0,
  area: ""
});

const onChange = () => {};

const visible = ref(false);
const onOpen = (data?: any) => {
  if (data) {
    select.id = data.id;
    select.area = "";
  }
  visible.value = true;
};
const onClose = () => {
  visible.value = false;
};
// 确定发送消息
const onSave = () => {
  if (!select.area) {
    MessagePlugin.error(t("member.active.please_input_rejecto_reason"));
    return;
  }
  emits("onSend", select);
};
defineExpose({
  onOpen,
  onClose
});
</script>

<style lang="less" scoped>
:deep(.t-textarea__limit) {
  display: none;
}
.t-button + .t-button {
  margin-left: 0;
}
.header {
  display: flex;
  &-svg {
    width: 22px;
    height: 22px;
    color: #2069e3;
    margin-right: 8px;
  }
  &-title {
    font-size: 16px;

    font-weight: 700;
    color: #13161b;
  }
}
.toBody {
  padding: 0 24px;
  .msg {
    margin-bottom: 16px;
    margin-left: 30px;
  }
  .btns {
    display: flex;
    margin-left: 30px;
    .btn {
      width: 320px;
      margin-bottom: 16px;
    }
  }
  .area {
    // margin-left: 30px;
    width: auto !important;
    // margin-top: 10px;
    padding: 12px;
    padding-bottom: 4px;
    border-radius: var(--textarea-kyy_radius_textarea, 4px);
    border: 1px solid var(--textarea-kyy_color_textarea_border_default, #D5DBE4);
    // background: var(--textarea-kyy_color_textarea_bg_default, #FFF);
    :deep(.t-textarea__inner) {
      border: 0;
      padding: 0 !important
    }
    .count {
      display: flex;
      justify-content: flex-end;
      color: var(--text-kyy_color_text_5, #ACB3C0);
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px; /* 166.667% */
      width: 100%;

    }
  }
}
// :deep(.t-dialog--default) {
// 	padding-bottom: 0;
// }

.footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  padding: 24px;
}

.t-dialog__body {
}
</style>
