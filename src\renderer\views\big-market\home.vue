<template>
  <desiredCompoent v-if="searching" ref="desiredCompoentRef" type-key="null" @up-title="upTitleRun" :home="true"
    @homeinit="homeRefresh" />
  <div v-else class="big" ref="bigRef" @scroll="onScroll" @click.stop="closeLat">
    <header-compoent ref="latWidgetRef"
     :branData="fixdHeaderShow ? ['首页'] : []"
    :fixdHeaderShow="fixdHeaderShow" :colfixdBoolean="colfixdHeader"
      @homeinit="homeRefresh" @toSaerch="toSeach" :keyword="keyword" :addressText="addressText"
      @editIsMapPageFlag="editIsMapPageFlag" />
    <div class="big-box">
      <div class="banner">
        <div class="bottom">
          <div class="types-box" @mouseleave="mouseleaveRun">
            <template v-for="(typeItem, index) in classifyData" :key="typeItem.id">
              <div v-show="index < typeShowFunc" class="type-item" @click="toClassify(typeItem)">
                {{ typeItem.name }}
              </div>
            </template>

            <div v-if="classifyData?.length > 8" id="type-item-more" class="type-item-more"
              :class="{ moreAct: moreTypeShow }" @mouseenter="mouseMoreRun">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                <path
                  d="M2.10794 1.64246H6.06201C6.72107 1.64246 7.05044 1.97199 7.05044 2.63089V6.5848C7.05044 7.24386 6.72091 7.57324 6.06201 7.57324H2.10794C1.44888 7.57324 1.11951 7.24371 1.11951 6.5848V2.63105C1.11935 1.97199 1.44888 1.64246 2.10794 1.64246ZM2.10794 9.05371H6.06201C6.72107 9.05371 7.05044 9.38324 7.05044 10.0421V13.9962C7.05044 14.6553 6.72091 14.9846 6.06201 14.9846H2.10794C1.44888 14.9846 1.11951 14.6551 1.11951 13.9962V10.0421C1.11935 9.38324 1.44888 9.05371 2.10794 9.05371ZM9.52154 9.05371H13.4756C14.1347 9.05371 14.464 9.38324 14.464 10.0421V13.9962C14.464 14.6553 14.1345 14.9846 13.4756 14.9846H9.52154C8.86248 14.9846 8.5331 14.6551 8.5331 13.9962V10.0421C8.5331 9.38324 8.86248 9.05371 9.52154 9.05371Z"
                  fill="#8986FF" />
                <path
                  d="M12.088 1.35299L14.5344 3.7994C15.0005 4.26534 15.0005 4.73143 14.5344 5.19737L12.088 7.64362C11.6221 8.10956 11.156 8.10956 10.69 7.64362L8.24362 5.19737C7.77768 4.73143 7.77768 4.26534 8.24362 3.7994L10.69 1.35299C11.156 0.887056 11.6221 0.887056 12.088 1.35299Z"
                  fill="#778DFE" />
              </svg>

              {{ t("market.more") }}
              <iconpark-icon name="iconarrowright" class="iconmore" />
              <div v-if="moreTypeShow" class="more-types"
                :style="{ width: calculateDivWidth(classifyData?.length - 7) }" @mouseenter="mouseMoreRun"
                @mouseleave="mouseleaveRun">
                <div v-for="(typeItem, idx) in classifyData" v-show="idx > 6" :key="typeItem.id" class="more-item"
                  @click="toClassify(typeItem)">
                  {{ typeItem.name }}
                </div>
              </div>
            </div>
          </div>
          <div class="ad-box">
            <!-- 广告轮播 -->
            <advertising-space v-if="addressData" :addressData="addressData"
              ref="advertisingSpaceRef"></advertising-space>
          </div>
        </div>
      </div>
      <div class="conetnt" v-infinite-scroll="handleInfiniteOnLoad" :infinite-scroll-immediate-check="false"
        :infinite-scroll-distance="20" :style="{ marginTop: colfixdHeader ? '110px' : '16px' }" id="conetntDom"
        ref="conetntDom">
        <column style="margin-bottom: 16px;" :activeColumn="marketColumnId"
        :orderBy="orderBy" @switchColumn="switchColumn" @clickSort="clickSort" :colfixdHeader="colfixdHeader" />
        <sq-conetnt :sqData="sqData" :keyword="keyword" :skeletonShow="skeletonShow" :loadingStatus="loadingStatus" :scroll-container="bigRef" />
      </div>
    </div>
    <totop />
    <BMap :height="0" @initd="onMapInit" />
    <Tricks :offset="{ x: '-32', y: '-100' }" uuid="大市场-首页" />
  </div>
</template>

<script setup lang="ts" name="bigMarketHome">
import { computed, onActivated, onBeforeUnmount, onMounted, ref, watch, nextTick } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ClientSide } from "@renderer/types/enumer";
import { useI18n } from "vue-i18n";
import { getProfilesInfo } from "@renderer/utils/auth";

import { useNicheStore } from "@renderer/store/modules/niche";
import totop from "@/views/big-market/components/totop.vue";
import { nicheMarketClassifyGet, querySquares } from "@/views/big-market/apis";
import desiredCompoent from "@/views/big-market/components/desiredCompoent.vue";
import sqConetnt from "@/views/big-market/components/sq-conetnt.vue";
import advertisingSpace from "@/views/big-market/components/advertising-space.vue";
import { useBaiduMap } from "@renderer/components/common/map/hooks";
import LynkerSDK from "@renderer/_jssdk";
const { ipcRenderer } = LynkerSDK;
import headerCompoent from "@/views/big-market/components/header-compoent.vue";
import column from "@/views/big-market/components/column.vue";
const router = useRouter();
const route = useRoute();
const latWidgetRef = ref(null);
const { t, locale } = useI18n();
const advertisingSpaceRef = ref(null);
const sqData = ref([]);
const classifyData = ref([]);

const type = ref(1);
const moreTypeShow = ref(false);
const searching = ref(false);
const loadingStatus = ref(false);
const keyword = ref("");
const skeletonShow = ref(true)
const settabItem = () => {
  ipcRenderer.invoke("set-big-market-tab-item", {
    path: `/bigMarketIndex/home`,
    path_uuid: "bigMarket",
    title: t("market.bigmarket"),
    name: "bigMarketHome",
    updateKey: "bigMarket",
    type: ClientSide.BIGMARKET,
  });
};
ipcRenderer.on("edit-ad-swiper", (event, val) => {
  advertisingSpaceRef.value.editAdSwiper(val);
  // 把轮播暂停edit-ad-swiper
  console.log(val, "把组件暂停");
});
onBeforeUnmount(() => {
  ipcRenderer.removeAllListeners("edit-ad-swiper");
});
onMounted(() => {
  settabItem();
});
onActivated(() => {
  fixdHeaderShow.value = false;
  colfixdHeader.value = false;
});
const addressData = ref(null);
const addressText = ref(null);
const nicheStore = useNicheStore();

// 地图 IP 定位
const { location, markerInfo, onMapInit } = useBaiduMap({
  onIPLocation() {
    const { point } = location.value;
    addressData.value = [point.lng, point.lat];
  },
  onPointGeocoder() {
    addressText.value = markerInfo.value.address;
    homeDataReq();
  },
})

const editAddressText = (text) => {
  addressText.value = text;
  nicheStore.positionText = text;
};


const pageNum = ref(1);
const marketClassifyId = ref(undefined);
const marketColumnId = ref(undefined);
const orderBy = ref(1);
const total = ref(0);
const lastPage = ref(false);
const profileInfo = getProfilesInfo();
const scrollDisabled = computed(() => sqData.value.length >= total.value);
const pageParams = ref({});

const querySquaresReq = async () => {
  const params = {
    marketColumnId: marketColumnId.value,
    marketClassifyId: marketClassifyId.value,
    orderBy: orderBy.value,
    'latLng.latitude': addressData.value[1],
    'latLng.longitude': addressData.value[0],
    'pagination.size': 30,
    'pagination.number': pageNum.value,
    adCode: profileInfo.area,
    ...pageParams.value
  }
  const res = await querySquares(params)
  sqData.value = sqData.value.concat(res.data?.data?.squares);
  total.value = res.data?.data?.pagination?.total;
  lastPage.value = res.data?.data?.pagination?.lastPage;
  pageParams.value = res.data?.data?.pagination;
  handlePageParams(res.data?.data?.pagination)
  skeletonShow.value = false;
  disOnLoad.value = false;
  console.log('querySquaresRes', res);
}

const handlePageParams = (params) => {
  for (const key in params) {
    if (params.hasOwnProperty(key)) {
      params[`pagination.${key}`] = params[key];
      params[key] = undefined;
    }
  }
}


const conetntDom = ref(null)
const bigRef = ref(null)

const clickSort = async (type: number) => {
  orderBy.value = type;
  skeletonShow.value = true;
  disOnLoad.value = true;
  sqData.value = [];
  pageParams.value = {}
  pageNum.value = 1;
  await querySquaresReq();
  nextTick(() => {
    bigRef.value?.scrollTo({
      top: 280,
      behavior: 'smooth'
    });
  });
}
const disOnLoad = ref(false)
const switchColumn = async (id: number) => {
  marketColumnId.value = id;
  skeletonShow.value = true;
  disOnLoad.value = true;
  sqData.value = [];
  pageParams.value = {};
  pageNum.value = 1;
  await querySquaresReq();
  nextTick(() => {
    bigRef.value?.scrollTo({
      top: 280,
      behavior: 'smooth'
    });
  });
}

const homeDataReq = async () => {
  const classRes = await nicheMarketClassifyGet({
    type: 2,
    longitude: addressData.value[0],
    latitude: addressData.value[1],
  });
  if (classRes.data) {
    classifyData.value = classRes.data.data.list;
  }
  skeletonShow.value = true;
  disOnLoad.value = true;
  querySquaresReq()
};

const editAddressData = (data) => {
  addressData.value = data;
  nicheStore.position = addressData.value;
};
const editIsMapPageFlag = (val, name?) => {
    console.log('editIsMapPageFlag',val, name);
  if (val) {
    console.log(val, name);
    addressText.value = name;
    addressData.value = [val.location.lng, val.location.lat];
    nicheStore.position = addressData.value;
    nicheStore.positionText = addressText.value;
    sqData.value = [];
    homeDataReq();
  }
};
const calculateDivWidth = (count) => {
  const itemWidth = 104;
  const columnGap = 8;
  const padding = 12;
  let numberOfColumns = Math.ceil(count / 8);
  if (count < 8) {
    numberOfColumns = 1;
  }
  const totalWidth = padding * 2 + (itemWidth + columnGap) * numberOfColumns - columnGap;
  return `${totalWidth}px`;
};

const fixdHeaderShow = ref(false);
const colfixdHeader = ref(false);

const onScroll = (e) => {
  console.log(e.target.scrollTop);
  if (e.target.scrollTop >= 72) {
    fixdHeaderShow.value = true;
  } else {
    fixdHeaderShow.value = false;
  }
  if (e.target.scrollTop >= 301) {
    colfixdHeader.value = true;
  } else {
    colfixdHeader.value = false;
  }
  closeLat();
};

const toClassify = (typeData) => {
  const path = "/bigMarketIndex/classify";
  const query = { classifyId: typeData.business_classify.toString(), className: typeData.name };
  ipcRenderer.invoke("set-big-market-tab-item", {
    path: `/bigMarketIndex/classify`,
    path_uuid: "bigMarket",
    title: typeData.name,
    addNew: true,
    query,
    name: "bigMarketClassify",
    type: ClientSide.BIGMARKET,
  });
  router.push({ path, query });
};
const desiredCompoentRef = ref(null);
const toSeach = (word) => {
  console.log('toSeachindex',word);
  if(searching.value) {
    return
  }
  keyword.value = word
  if (!keyword.value || !keyword.value?.length) {
    return;
  }
  searching.value = true;
  setTimeout(() => {
    desiredCompoentRef.value.homeSearch({
      keyword: keyword.value,
      addressText: addressText.value,
      position: addressData.value,
    });
  }, 1);
};
const homeinit = () => {
  searching.value = false;
  keyword.value = "";
  fixdHeaderShow.value = false;
  colfixdHeader.value = false;
  // bigRef.value?.scrollTo({
  //   top: 0,
  //   behavior: 'smooth'
  // });
  homeDataReq()
};
const emits = defineEmits(["update-title", "openRefresh"]);

const upTitleRun = (e) => {
  console.log(e, "upTitleRun");
  emits("update-title", { title: e || "大市场", name: "bigMarketHome" });
};

const homeRefresh = () => {
  emits("openRefresh");
};

watch(
  () => route.path,
  () => {
    console.log(" route.path", route.path);
    if (route.path === "/bigMarketIndex/home" && route.query?.refresh) {
      homeDataReq();
      console.log("homeRefresh");
    }
  },
);
const closeLat = () => {
  latWidgetRef.value?.latClose();
  latWidgetRef?.value.closeSearchDr();
};
const some = () => {
  console.log("方法有用，请勿删除");
};
const typeSelectEvent = (e) => {
  type.value = e;
};

const typeShowFunc = computed(() => {
  if (classifyData.value.length > 8) {
    return 7;
  }
  return 8;
});

const setTimeoutTemp = ref(null);
const mouseleaveRun = () => {
  setTimeoutTemp.value = setTimeout(() => {
    moreTypeShow.value = false;
  }, 300);
};
const mouseMoreRun = () => {
  console.log("mouseMoreRun");
  clearTimeout(setTimeoutTemp.value);
  moreTypeShow.value = true;
};


const handleInfiniteOnLoad = () => {
  if (!scrollDisabled.value && !lastPage.value && !disOnLoad.value) {
    pageNum.value += 1;
    loadingStatus.value = true;
    querySquaresReq();
    setTimeout(() => {
      loadingStatus.value = false;
    }, 500);
  }
};
</script>

<style lang="less" scoped>
.big::-webkit-scrollbar {
  width: 0px;
}

.big {
  display: flex;
  width: 100%;
  flex-direction: column;
  align-items: center;
  height: 100vh;
  background-image: url(@/assets/big-market/bg.png);
  overflow-y: auto;
  background-position: center;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding: 16px;
  .big-box {
    width: 1184px;
  }

  .index-header {
    display: flex;
    // justify-content: space-between;
    margin-bottom: 16px;
    width: 100%;
    align-items: center;
    gap: 16px;

    .logo-box {
      display: flex;
      align-items: center;
      gap: 12px;
      cursor: pointer;
      width: 268px;

      .logo {
        width: 32px;
        height: 32px;
      }

      .font_cn {
        width: 89px;
        height: 40px;
      }

      .name {
        color: var(--text-kyy_color_text_2, #516082);
        font-family: YouSheBiaoTiHei;
        font-size: 32px;
        font-style: normal;
        font-weight: 400;
        line-height: 40px;
        /* 125% */
      }
    }

    .search-bar {
      display: flex;
      width: 600px;
      height: 32px;
      padding: 2px 2px 2px 8px;
      align-items: center;
      gap: 12px;
      flex-shrink: 0;
      border-radius: 4px;
      border: 2px solid var(--brand-kyy_color_brand_hover, #707eff);
      background: var(--input-kyy_color_input_bg_default, #fff);

      .search-btn {
        width: 48px;
        height: 24px;
        flex-shrink: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        border-radius: var(--radius-kyy_radius_button_s, 4px);
        background: var(--brand-kyy_color_brand_hover, #707eff);

        .iconsearch {
          font-size: 20px;
          color: #fff;
        }
      }
    }
  }

  .banner {
    display: flex;
    padding: 16px;
    flex-direction: column;
    align-items: flex-start;
    align-self: stretch;
    border-radius: 8px;
    background: #fff;

    .top {
      width: 100%;
    }

    .bottom {
      display: flex;
      gap: 12px;

      .types-box {
        width: 136px;
        height: 308px;
        border-radius: 8px;
        padding: 12px 8px;
        display: flex;
        flex-direction: column;
        gap: 4px;

        background-image: url(@/assets/big-market/bg_category.png);
        background-position: center;
        background-size: 100% 100%;
        background-repeat: no-repeat;

        .type-item {
          color: var(--text-kyy_color_text_1, #1a2139);
          font-family: "PingFang SC";
          font-size: 14px;
          height: 32px;
          font-style: normal;
          font-weight: 400;
          cursor: pointer;
          padding: 10px;
          display: flex;
          align-items: center;
        }

        .type-item:hover,
        .type-item-more:hover {
          border-radius: 4px;
          background: var(--bg-kyy_color_bgBrand_hover, #eaecff);
        }

        .moreAct {
          border-radius: 4px;
          background: var(--bg-kyy_color_bgBrand_hover, #eaecff);
        }

        .type-item-more {
          display: flex;
          height: 32px;
          padding: 10px;
          align-items: center;
          gap: 4px;
          align-self: stretch;
          border-radius: 4px;
          cursor: pointer;
          color: var(--text-kyy_color_text_1, #1a2139);
          position: relative;
        }

        .more-types {
          display: flex;
          height: 308px;
          padding: 12px;
          justify-content: start;
          align-items: flex-start;
          flex-shrink: 0;
          flex-direction: column;
          flex-wrap: wrap;
          align-content: start;
          background: #f4f6ff;
          position: absolute;
          border-radius: 8px;
          gap: 4px;
          top: -264px;
          left: 130px;
          z-index: 100;
          column-gap: 8px;

          .more-item {
            display: flex;
            width: 104px;
            height: 32px;
            min-height: 32px;
            max-height: 32px;
            padding: 0px 8px;
            align-items: center;
            gap: 12px;
            color: var(--kyy_color_dropdown_text_default, #1a2139);
            font-family: "PingFang SC";
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;
            /* 157.143% */
          }

          .more-item:hover {
            border-radius: 4px;
            background: var(--bg-kyy_color_bgBrand_hover, #eaecff);
          }
        }
      }

      .ad-box {
        width: 988px;
        height: 308px;
      }
    }
  }

  .conetnt {
    width: 100%;
    margin-top: 16px;
    background: #fff;
    padding: 16px;
    border-radius: 8px;
    .title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      align-self: stretch;
      margin-bottom: 16px;

      .title-name {
        position: relative;

        .text {
          font-family: "PingFang SC";
          font-size: 24px;
          font-style: normal;
          font-weight: 600;
          line-height: 32px;
          /* 133.333% */
          z-index: 2;
          position: relative;
        }

        .text-supply {
          color: var(--cyan-kyy_color_cyan_default, #11bdb2);
        }

        .text-desired {
          color: var(--warning-kyy_color_warning_default, #fc7c14);
        }

        .tag {
          position: absolute;
          bottom: 4px;
          width: 144px;
          height: 8px;
          flex-shrink: 0;
          background: linear-gradient(270deg, rgba(247, 255, 254, 0.3) 4%, rgba(49, 228, 217, 0.3) 100%);
          z-index: 1;
        }

        .tag2 {
          position: absolute;
          bottom: 0px;
          width: 144px;
          height: 8px;
          flex-shrink: 0;
          z-index: 1;
          background: linear-gradient(270deg, rgba(141, 250, 243, 0) 4%, rgba(133, 231, 221, 0.7) 100%);
        }

        .tag3 {
          position: absolute;
          bottom: 4px;
          width: 144px;
          height: 8px;
          flex-shrink: 0;
          background: linear-gradient(270deg, rgba(255, 239, 226, 0.3) 4%, rgba(253, 146, 86, 0.3) 100%);
          z-index: 1;
        }

        .tag4 {
          position: absolute;
          bottom: 0px;
          width: 144px;
          height: 8px;
          flex-shrink: 0;
          z-index: 1;
          background: linear-gradient(270deg, rgba(252, 124, 20, 0) 4%, rgba(255, 168, 98, 0.56) 100%);
        }
      }

      .more {
        display: flex;
        height: 24px;
        min-height: 24px;
        max-height: 24px;
        padding: 0px 4px 0px 12px;
        justify-content: center;
        align-items: center;
        gap: 4px;
        border-radius: 123px;
        background: var(--color-button_border-kyy_color_buttonBorder_bg_default, #fff);
        color: var(--color-button_border-kyy_color_buttonBorder_text_default, #516082);
        text-align: center;
        cursor: pointer;
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;

        /* 157.143% */
        .iconmore {
          font-size: 20px;
        }
      }

      .more:hover {
        background: var(--bg-kyy_color_bgBrand_hover, #eaecff);
        color: var(--brand-kyy_color_brand_hover, #707eff);
      }
    }

    .list-box {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
      column-gap: 12px;
    }
  }
}

.fixd-header-box {
  width: 100%;
  margin: 0 auto;
  position: fixed;
  top: 40px;
  background: #fff;
  box-shadow: 0px 3px 8px 0px rgba(0, 0, 0, 0.12);
  z-index: 1000;

  .fixd-header {
    display: flex;
    width: 1216px;
    gap: 16px;
    background: #fff;
    padding: 16px 24px;
    align-items: center;
    margin: 0 auto;

    .logo-box {
      display: flex;
      align-items: center;
      gap: 12px;
      min-width: 140px;
      cursor: pointer;

      .logo {
        width: 32px;
        height: 32px;
      }

      .font_cn {
        width: 89px;
        height: 40px;
      }

      .name {
        color: var(--text-kyy_color_text_2, #516082);
        font-family: YouSheBiaoTiHei;
        font-size: 32px;
        font-style: normal;
        font-weight: 400;
        line-height: 40px;
        /* 125% */
      }
    }

    .line {
      display: flex;
      height: 24px;
      min-width: 1px;
      max-width: 1px;
      align-items: flex-start;
      gap: 4px;
      background: var(--divider-kyy_color_divider_deep, #d5dbe4);
    }

    .search-bar {
      display: flex;
      width: 424px;
      height: 32px;
      padding: 0px 2px 0px 9px;
      align-items: center;
      gap: 12px;
      flex-shrink: 0;
      border-radius: 4px;
      border: 2px solid var(--brand-kyy_color_brand_hover, #707eff);
      background: var(--input-kyy_color_input_bg_default, #fff);

      .search-btn {
        width: 48px;
        height: 24px;
        flex-shrink: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        border-radius: var(--radius-kyy_radius_button_s, 4px);
        background: var(--brand-kyy_color_brand_hover, #707eff);

        .iconsearch {
          font-size: 20px;
          color: #fff;
        }
      }
    }
  }
}

:deep(.t-back-top) {
  background: var(--bg-kyy_color_bg_mask, rgba(0, 0, 0, 0.5));
  border: none;
  box-shadow: none;
}

.mock-select {
  padding-left: 10px;
  color: var(--text-kyy_color_text_1, #1a2139);
  font-family: "PingFang SC";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  /* 157.143% */
  display: flex;
  gap: 4px;
  align-items: center;
  cursor: pointer;

  .line {
    display: flex;
    height: 16px;
    min-width: 1px;
    max-width: 1px;
    justify-content: flex-end;
    align-items: center;
    background: var(--divider-kyy_color_divider_deep, #d5dbe4);
  }

  .iconarrowdown {
    font-size: 20px;
    color: #828da5;
  }
}
</style>
