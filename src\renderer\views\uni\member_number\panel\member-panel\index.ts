import { defineAsyncComponent } from "vue";

const PHome = defineAsyncComponent(() => import("./home-panel.vue"));
const PSquare = defineAsyncComponent(() => import("@renderer/views/uni/member_number/components/square-comp.vue"));
const PMy = defineAsyncComponent(() => import("./my-panel.vue"));
// 这一块是组织端的组件
export const panels = {
  PHome, // 首页
  PSquare, // 会员广场号
  PMy
  // PMember, // 会员中心
  // PLeaflets, // 宣传页
  // PPlatform, // 选择端页面
  // PManage, // 管理端
};
