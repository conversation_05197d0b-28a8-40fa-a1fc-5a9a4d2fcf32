import { defineStore } from 'pinia';
import { PostRequestData } from '@renderer/api/square/models/post';

export const useStateStore = defineStore('square-state', {
  state: () => ({
    // 是否关注了当前浏览的用户
    followedTarget: {},
    // 是否被当前浏览用户关注
    beFollowedTarget: {},
    // 是否正在切换账号（包括组织，个人等所有类型账号）
    isSwitchAccount: false,
    // 视频播放的状态管理
    currentPlayVideoUrl: '', // 当前播放的视频路径
    // 发布文章的状态管理
    editingFormData: null,
    publishFormData: [] as Array<PostRequestData & { id: number }>,
    publishIsDraft: false,
    refreshKey: 0,
    isPublishing: false,
    position: [], // 位置信息
    nearbyFolowedList: [], // 附近动态关注的广场号，用来统一状态的
    menuBarCollapsed: false,
    isTurnToNearbyListPage: false, // 从好友圈跳转到附近动态页面状态
    // 对话框状态管理
    isConfirmDialogOpen: false, // 确认对话框是否打开
  }),
  actions: {
    // 保存文章的发布记录
    savePublishFormData(publishFormData: PostRequestData & { id: number }) {
      if (!Array.isArray(this.publishFormData)) {
        this.publishFormData = [];
      }
      const index = this.publishFormData.findIndex((item) => item.id === publishFormData.id);
      if (index < 0) {
        this.publishFormData.push(publishFormData);
        return;
      }
      this.publishFormData[index] = publishFormData;
    },
    setPublishIsDraft(isDraft: boolean) {
      this.publishIsDraft = isDraft;
    },
    refreshArticlePublic() {
      this.refreshKey++;
    },
    updatePosition(position) {
      this.position = position;
    },
    updateNearbyFolowedList(id: string) {
      const squareId = Number(id);
      const index = this.nearbyFolowedList.findIndex((item) => item === squareId);
      if (index > -1) {
        this.nearbyFolowedList.splice(index, 1);
      } else {
        this.nearbyFolowedList.push(squareId);
      }
    },
  },
});
