import { DialogPlugin } from 'tdesign-vue-next';
import { configDataStore } from '@renderer/store/modules/configData';
import { logHandler } from '@renderer/log';
import { isEqual, remove, sortBy } from 'lodash';
import moment from 'moment';
import { getCommonTeams } from '@renderer/api/workBench';
import LynkerSDK from '@renderer/_jssdk';
import { useAppStore } from '@renderer/store/modules/appStore';
import { allRouters, defaultRouters, defaultRoutersUuid } from '@/constants';
import { i18n } from '@/i18n';
import { getAccountAuth, getProfile, getCfgCommon } from '@/api/account';
import { checkNewVersion, configInfo } from '@/views/setting/util';
import { checkContactConfig } from '@/api/identity/api/card';
import { isRemindDetermineWhetherCompleted } from '@/views/zhixing/judge';

const pcptapp = new URL('@/assets/member/pcptapp.png', import.meta.url);
const pcpthome = new URL('@/assets/member/pcpthome.png', import.meta.url);
const pcptys = new URL('@/assets/member/pcptys.png', import.meta.url);
const pcptyx = new URL('@/assets/member/pcptyx.png', import.meta.url);
const pcschome = new URL('@/assets/member/pcschome.png', import.meta.url);
const pcscys = new URL('@/assets/member/pcscys.png', import.meta.url);
const pcscyx = new URL('@/assets/member/pcscyx.png', import.meta.url);
const pcscapp = new URL('@/assets/member/pcscapp.png', import.meta.url);
const Store = LynkerSDK.eStore;
const store = new Store();
let ringkolDataStore = store;
LynkerSDK.ipcRenderer.invoke('get-app-info', { appPath: 'userData', customPath: 'ringkol-data' }).then((res) => {
  console.log('get-app-info=', res);
  ringkolDataStore = new Store({
    cwd: res.getAppPath,
  });
});

export function viewAdModel(index) {
  const imgArr = [pcschome, pcscys, pcscyx, pcscapp, pcpthome, pcptys, pcptyx, pcptapp];
  LynkerSDK.ipcRenderer.invoke(
    'preview-file',
    JSON.stringify({
      url: imgArr[index],
      type: 'jpg',
    }),
  );
}

export function setIsTeamShow(value) {
  return window.localStorage.setItem('is_team_show', value);
}
export function getIsTeamShow() {
  return window.localStorage.getItem('is_team_show');
}
export function removeIsTeamShow() {
  return window.localStorage.removeItem('is_team_show');
}

export function setOrganizationTips(value) {
  return window.localStorage.setItem('organization_tips', value);
}
export function getOrganizationTips() {
  return window.localStorage.getItem('organization_tips');
}
export function removeOrganizationTips() {
  return window.localStorage.removeItem('organization_tips');
}

export function getAccesstoken() {
  let token = ringkolDataStore.get('main_token');
  if (typeof token === 'undefined') {
    token = window.localStorage.getItem('main_token');
  }
  LynkerSDK.config = {
    token,
  };
  return token;
}
export function getRefreshtoken() {
  return window.localStorage.getItem('refresh_token');
}
export function getRefreshCode() {
  return safetyJsonParse(window.localStorage.getItem('refresh'));
}
export function getOpenid() {
  return getKey('openid');
}
export function getPersonSquareId() {
  return window.localStorage.getItem('personSquareId');
}
export function getAutoLogin() {
  const auto_login = ringkolDataStore.get('auto_login');
  if (typeof auto_login !== 'undefined') {
    return auto_login;
  }
  return window.localStorage.getItem('auto_login');
}
export function getKey(key) {
  const _key = ringkolDataStore.get(key);
  if (typeof _key !== 'undefined') {
    return _key;
  }
  return window.localStorage.getItem(key);
}
export function getRegister() {
  return window.localStorage.getItem('register');
}
export function getLang() {
  return window.localStorage.getItem('lang');
}
export function getShouldRinging() {
  const ring = window.localStorage.getItem('shouldRinging');
  return ring ? safetyJsonParse(ring) : true;
}

export function getServerLang() {
  const localLang = window.localStorage.getItem('lang');
  const lang = { 'zh-tc': 'zh-hant-MO', 'zh-cn': 'zh-hans-CN' };
  return lang?.[localLang] || lang['zh-cn'];
}

export function getOpenImToken() {
  return getKey('open_im_token');
}
export function getProfilesInfo() {
  return safetyJsonParse(window.localStorage.getItem('profile'));
}
export function getProfilesInfoString() {
  return window.localStorage.getItem('profile');
}

export function getGuidesInfo() {
  return safetyJsonParse(window.localStorage.getItem('guides'));
}
export function goLkAdDetail(adId, teamId) {
  console.log('goLkAdDetailgoLkAdDetail', teamId);
  const params = {
    jumpPath: '/workBenchIndex/rkAdDetails',
    name: '广告详情',
    activationGroupItemTeamId: teamId,
    teamId,
    uuid: 'ad-lk',
    adId,
    toAdmin: true,
    toLkAd: true,
    from: 'message',
    module: 'ad-lk'
  };
  if (!adId) {
    params.jumpPath = '/workBenchIndex/ringkolAdvertisement'
    params.name = '另可广告'
    params.from = 'square'
  }

  LynkerSDK.ipcRenderer.invoke('click-menu-item', {
    url: '/workBenchIndex/workBenchHome',
    ...params,
    query: params,
    reload:true

  }).then(res=>{
    if(res){
      LynkerSDK.ipcRenderer.send('update-nume-index', 'workBench');
    }
  })
  // LynkerSDK.workBench.openTab({
  //   icon: "http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/workbech/lkad.svg",
  //   title: '广告详情',
  //   path_uuid: 'rkAdDetails' + adId,
  //   pathName: 'rkAdDetails',
  //   fullPath: "/workBenchIndex/rkAdDetails",
  //   query: {
  //     teamId,
  //     id: adId,
  //   },
  // });
}
export const goWorkerToPlatform = (teamId, path, paramsQuery, flag) => {
  // flag ===workBenchIndex就是数字工场
  // 数智工场跳转 path "/workBenchIndex/AdDetails"
  // paramsQuery
  // uuid
  // munber 21 politics 20  cbd 23  association 28
  // title 广告详情
  // 跳转到数字平台商协广告详情  /digitalPlatformIndex/member_adDetails
  // 跳转到数字平台政企广告详情  /digitalPlatformIndex/politics_adDetails
  // 跳转到数字平台cbd广告详情  /digitalPlatformIndex/cbd_adDetails
  const HomePage = flag === 'workBench' ? '/workBenchIndex/workBenchHome' : '/digitalPlatformIndex/digital_platform_home';
  const { title, uuid, id, type } = paramsQuery;
  const params = {
    name: title,
    uuid,
    jumpPath: path,
    singleTab: true,
    id,
    flag: flag === 'workBench' ? 'edit' : 'view',
    type,
    isClose: true,
    path: flag,
    affix: false,
    from: 'message',
    activationGroupItemTeamId: teamId,
    teamId,
    toAdmin: true
  };
  console.log(params, 'paramsparams');

  LynkerSDK.ipcRenderer
    .invoke('click-menu-item', {
      url: HomePage,
      ...params,
      query: params,
    })
    .then((res) => {
      if (res) {
        LynkerSDK.ipcRenderer.send('update-nume-index', flag);
      }
    });
};
export function getPlatform() {
  return safetyJsonParse(window.localStorage.getItem('platform'));
}
export function getStaff(hasPlatform = false) {
  // 新增兼容返回平台身份
  if (hasPlatform) {
    const platform = safetyJsonParse(window.localStorage.getItem('platform'));
    const staff = safetyJsonParse(window.localStorage.getItem('staff'));
    let res;
    if (!platform && !staff) return res;
    if (Array.isArray(staff)) {
      res = staff;
    }
    if (Array.isArray(platform)) {
      res = res ? [...res, ...platform] : platform;
    }
    return res;
  }
  return safetyJsonParse(window.localStorage.getItem('staff'));
}
export function getCards() {
  return safetyJsonParse(window.localStorage.getItem('cards'));
}
export function getArea() {
  return safetyJsonParse(window.localStorage.getItem('area'));
}
export function getCurrentArea() {
  return safetyJsonParse(window.localStorage.getItem('profile'))?.area;
}
// 申请添加联系人备注信息
export function getApplyCards() {
  return safetyJsonParse(window.localStorage.getItem('apply_cards'));
}
export function getCreateGroupProfileCard() {
  return safetyJsonParse(window.localStorage.getItem('create_group_profile_card'));
}
export function getAccountSwitchList() {
  return safetyJsonParse(window.localStorage.getItem('accountSwitchList'));
}
export function getCurrentAccount() {
  return safetyJsonParse(window.localStorage.getItem('currentAccount'));
}
export function getLoginType() {
  return safetyJsonParse(window.localStorage.getItem('account_login_info'));
}
export function getSMDeviceId() {
  return window.localStorage.getItem('SMDeviceId');
}
export function getVersion() {
  return safetyJsonParse(window.localStorage.getItem('appVersion'));
}
export function getMsgWinRload() {
  return safetyJsonParse(window.localStorage.getItem('msgWinRload'));
}

export function setSMDeviceId(id) {
  return window.localStorage.setItem('SMDeviceId', id);
}
export function setLoginType(data) {
  return window.localStorage.setItem('account_login_info', JSON.stringify(data));
}
export function setAccesstoken(token) {
  try {
    LynkerSDK.config = {
      token,
    };
    // 存到指定系统文件目录，是因为这里要做更新app后自动登录，数据不能被清掉
    ringkolDataStore.set('main_token', token);
  } catch (e) {
    console.log(e);
  }
  return window.localStorage.setItem('main_token', token);
}
export function setRefreshtoken(token) {
  return window.localStorage.setItem('refresh_token', token);
}
export function setRefreshCode(refresh) {
  return window.localStorage.setItem('refresh', JSON.stringify(refresh));
}
export function setOpenid(jwt) {
  return setKey('openid', jwt);
}
export function setAutoLogin(isAuto) {
  ringkolDataStore.set('auto_login', isAuto);
  return window.localStorage.setItem('auto_login', isAuto);
}
export function setKey(key, values) {
  console.log('setkey.....', key, values);
  ringkolDataStore.set(key, values);
  return window.localStorage.setItem(key, values);
}
export function setRegister(register) {
  return window.localStorage.setItem('register', register);
}
export function setLang(lang) {
  return window.localStorage.setItem('lang', lang);
}
export function setShouldRinging(ring) {
  return window.localStorage.setItem('shouldRinging', ring);
}
export function setOpenImToken(token) {
  return setKey('open_im_token', token);
}
export function setPersonSquareId(squareId) {
  return window.localStorage.setItem('personSquareId', squareId);
}

/**
 * 登录成功设置localstorage
 * @param param jwt,openid,rong_cloud_token,openim_token
 */
export function loginSucSetLocal({ jwt, openid, openim_token, openimToken }) {
  setAccesstoken(jwt || '');
  setOpenid(openid || '');
  setOpenImToken(openim_token || openimToken || '');
}

export function setProfilesInfo(profile, isChangeDetail = false) {
  // 保存登录类型
  profile = { ...profile, account_login_info: getLoginType() };
  // 更新当前账号信息，用于多账号切换 isChangeDetail 是否变更名称头像等详情数据更新
  setCurrentAccount(profile, isChangeDetail);
  return window.localStorage.setItem('profile', JSON.stringify(profile));
}

// 存储绑定当前用户进入商协会、是否引导
export function setGuides(guides) {
  return window.localStorage.setItem('guides', JSON.stringify(guides));
}

export function setPlatform(platform) {
  return window.localStorage.setItem('platform', JSON.stringify(platform));
}
export function setStaff(staff) {
  return window.localStorage.setItem('staff', JSON.stringify(staff));
}
export function setCards(cards) {
  return window.localStorage.setItem('cards', JSON.stringify(cards));
}
export function setApplyCards(cards) {
  return window.localStorage.setItem('apply_cards', JSON.stringify(cards));
}
export function setArea(area) {
  return window.localStorage.setItem('area', JSON.stringify(area));
}
// 记录创建群组选中身份卡
export function setCreateGroupProfileCard(cards) {
  return window.localStorage.setItem('create_group_profile_card', JSON.stringify(cards));
}
export function setAccountSwitchList(list) {
  return window.localStorage.setItem('accountSwitchList', JSON.stringify(list));
}
export function setCurrentAccount(profile, isChangeDetail) {
  let info = {};
  if (isChangeDetail) {
    info = {
      ...profile,
      name: profile.title,
      jwt: getAccesstoken(),
    };
  } else {
    info = {
      ...profile,
      name: profile.title,
      jwt: getAccesstoken(),
    };
  }
  filterAccountSwitchList(info);
  return window.localStorage.setItem('currentAccount', JSON.stringify(info));
}
export function setAppVersion(list) {
  return window.localStorage.setItem('appVersion', JSON.stringify(list));
}
/**
 *
 * @param flag 已经reloadmsgwindows
 * @returns
 */
export function setMsgWinRload(flag) {
  return window.localStorage.setItem('msgWinRload', flag);
}

export function removeSMDeviceId() {
  return window.localStorage.removeItem('SMDeviceId');
}
export function removeAccesstoken() {
  try {
    LynkerSDK.config = {
      token: '',
    };
    ringkolDataStore.delete('main_token');
  } catch (e) {
    console.log(e);
  }
  return window.localStorage.removeItem('main_token');
}
export function removeRefreshtoken() {
  return window.localStorage.removeItem('refresh_token');
}
export function removeRefreshCode() {
  return window.localStorage.removeItem('refresh');
}
export function removeOpenid() {
  return removeKey('openid');
}
export function removeRegister() {
  return window.localStorage.removeItem('register');
}
export function removeAutoLogin() {
  try {
    ringkolDataStore.delete('auto_login');
  } catch (e) {
    console.log(e);
  }
  return window.localStorage.removeItem('auto_login');
}
export function removeKey(key) {
  try {
    ringkolDataStore.delete(key);
  } catch (e) {
    console.log(e);
  }
  return window.localStorage.removeItem(key);
}
export function removeLang() {
  return window.localStorage.removeItem('lang');
}
export function removeOpenImToken() {
  return removeKey('open_im_token');
}
export function removeProfilesInfo() {
  return window.localStorage.removeItem('profile');
}
export function removePlatform() {
  return window.localStorage.removeItem('platform');
}
export function removeStaff() {
  return window.localStorage.removeItem('staff');
}
export function removeCards() {
  return window.localStorage.removeItem('cards');
}
function removeApprovalTeamId() {
  window.localStorage.removeItem('approvalteamid');
}
function removeDeviceTeamId() {
  window.localStorage.removeItem('deviceTeamId');
}
function removeCustomerTeamId() {
  window.localStorage.removeItem('customerTeamId');
}
function removeSupplierTeamId() {
  window.localStorage.removeItem('supplierTeamId');
}
function removePartnerTeamId() {
  window.localStorage.removeItem('partnerTeamId');
}
function removeActivityTeamId() {
  window.localStorage.removeItem('activityTeamId');
}
export function removeAppTeamId() {
  // removeApprovalTeamId();
  // removeDeviceTeamId();
  removePartnerTeamId();
  removeCustomerTeamId();
  removeSupplierTeamId();
  removeActivityTeamId();
  removeSMDeviceId();
}
export function removeAccountSwitchList() {
  return window.localStorage.removeItem('accountSwitchList');
}
export function removeCurrentAccount() {
  return window.localStorage.removeItem('currentAccount');
}
export function safetyJsonParse(obj: string) {
  try {
    return JSON.parse(obj);
  } catch (e) { }
  return undefined;
}

export function getImCardIds(): string[] {
  const myIds = [];
  const openId = getOpenid();
  openId?.length && myIds.push(openId);
  // 内部身份信息
  const staff = getStaff(true);
  Array.isArray(staff) && myIds.push(...staff.map((item) => item.uuid));
  // 外部身份卡
  const cards = getCards();
  Array.isArray(cards) && myIds.push(...cards.map((item) => item.uuid));

  return myIds;
}

export const loadProfile = () => {
  getProfile().then((res) => {
    if (res.status === 200) {
      res.data && setProfilesInfo(res.data);
      res.data.openid && setOpenid(res.data.openid);
      LynkerSDK.ipcRenderer.invoke("change-profiles-info", {});
    }
  });
};

// 获取多账号切换状态
export const changeMoreAccountInfo = (isGet = false, isFromMoreAccount = false) => {
  if (isGet) {
    return store.get('isFromMoreAccount') ?? false;
  }
  store.set('isFromMoreAccount', isFromMoreAccount);
};

export const removeStore = (name) => {
  store.delete(name);
};

// todo 数智工厂workBench常驻，但是需要判断是否有组织，先在这里根据身份卡过滤处理
export const checkCommonTeams = async (tranRouters?, hasIdentityCard = false) => {
  if (hasIdentityCard) {
    const staff = getStaff(true);
    const cards = getCards();
    if (!staff?.length && !cards?.length) {
      const newRouters = tranRouters?.filter((v) => !['workBench'].includes(v.path_uuid));
      store.set('accountAuthRouters', newRouters);
    }
  } else {
    const teamData = await getCommonTeams();
    console.log(teamData, 'teamDatateamDatateamData');

    if (teamData.data?.code === 0 && !teamData.data?.data?.length) {
      const routers = tranRouters || (store.get('accountAuthRouters') || defaultRouters).filter((v) => v);
      const newRouters = routers?.filter((v) => !['workBench'].includes(v?.path_uuid));
      store.set('accountAuthRouters', newRouters);
    }
  }
};

// 刷新获取应用模块 登录和接口拦截器异常时调用
export const setAccountAuthRouters = (type = 'intercept', params = { owner: 0 }) => {

  // 初始化重置
  if (type === 'login') store.set('isShowAccountAuthTip', false);
  if (store.get('isShowAccountAuthTip')) return;
  return new Promise((resolve, reject) => {
    getAccountAuth(params)
      .then(async (res: any) => {
        if (res.status === 200) {
          const routers = res?.data?.data?.filter((item) => !defaultRoutersUuid.includes(item.uuid) && item?.auth);
          const tranRouters = routers?.map((items) => {
            const model = allRouters?.find((item) => item.path_uuid === items.uuid);
            return model;
          });
          // 特殊处理前后端workshop/workBench匹配
          if (routers.some((v) => v.uuid === 'workshop')) {
            tranRouters.push(allRouters?.find((item) => item.path_uuid === 'workBench'));
          }
          const oldRouters = JSON.parse(JSON.stringify(store.get('accountAuthRouters') || []));
          const newRouters = defaultRouters.concat(tranRouters);
          const noChange = isEqual(sortBy(newRouters), sortBy(oldRouters));
          store.set('accountAuthRouters', newRouters);
          await checkCommonTeams(newRouters, false);
          LynkerSDK.ipcRenderer.invoke('set-auth-routers', store.get('accountAuthRouters'));
          resolve(newRouters);
          if (noChange) return;
          if (type === 'intercept') {
            // 不是正常登录，拦截器报错触发，唤起提醒弹框
            store.set('isShowAccountAuthTip', true);
            LynkerSDK.ipcRenderer.invoke('set-popbv', {
              show: true,
              type: 'accountAuthTip',
              data: { isAppDisabled: false },
            });
          }
        } else {
          reject();
        }
      })
      .catch(async (err) => {
        const newRouters = defaultRouters;
        store.set('accountAuthRouters', newRouters);
        await checkCommonTeams(newRouters, false);
        LynkerSDK.ipcRenderer.invoke('set-auth-routers', store.get('accountAuthRouters'));
        reject(err);
      });
  });
};

// 应用模块停用刷新，关闭当前窗口，重定向消息首页
export const refreshAccountAuthPage = (data) => {
  store.set('isShowAccountAuthTip', false);
  if (data?.data?.selected_path_uuid === data?.data?.click_path_uuid) {
    LynkerSDK.ipcRenderer.invoke('window-close');
    LynkerSDK.ipcRenderer.send('update-nume-index', 0);
  }
};

// 应用模块入口权限校验
export const checkAccountAuthRouters = (checkType = 'path', path_uuid: string) => {
  const routers = store.get('accountAuthRouters');
  if (routers?.some((item) => item[checkType] === path_uuid)) {
    return true;
  }
  const confirmDia = DialogPlugin.confirm({
    header: i18n.global.t('account.tip'),
    body: i18n.global.t('account.appAuthTip'),
    confirmBtn: i18n.global.t('contacts.iknow'),
    cancelBtn: null,
    closeBtn: null,
    closeOnOverlayClick: false,
    className: 'appAuthTipDialog',
    theme: 'info',
    onConfirm: async () => {
      confirmDia.destroy();
    },
    onClose: () => {
      confirmDia.hide();
    },
  });
  return false;
};

// 版本号比较 version1 最新  version2 当前
export const compareVersion = (version1, version2) => {
  const newVersion1 = `${version1}`.split('.').length < 3 ? `${version1}`.concat('.0') : `${version1}`;
  const newVersion2 = `${version2}`.split('.').length < 3 ? `${version2}`.concat('.0') : `${version2}`;
  function toNum(a) {
    const c = a.toString().split('.');
    const num_place = ['', '0', '00', '000', '0000'];
    const r = num_place.reverse();
    for (let i = 0; i < c.length; i++) {
      const len = c[i].length;
      c[i] = r[len] + c[i];
    }
    return c.join('');
  }
  function checkPlugin(a, b) {
    const numA = toNum(a);
    const numB = toNum(b);
    return numA > numB;
    // return numA > numB ? 1 : numA < numB ? -1 : 0;
  }
  return checkPlugin(newVersion1, newVersion2);
};

// 对比版本号和时间戳
export const compareVersionAndNumber = (remoteInfo, localInfo) => {
  const isNewVersion = compareVersion(remoteInfo.version, localInfo.version);
  let result = false;
  if (isNewVersion) {
    result = true;
  } else if (localInfo.version === remoteInfo.version) {
    const localNumber = localInfo.number || localInfo.buildNumber;
    const remoteNumber = remoteInfo.number || remoteInfo.buildNumber;
    // 相同版本号对比时间戳
    if (localNumber && remoteNumber && (Number(localNumber) < Number(remoteNumber))) {
      result = true;
    }
  }
  console.log('compareVersionAndNumber', result, remoteInfo, localInfo);
  return result;
};

// 消息通知检查应用更新
export const checkAppUpdate = (data: {
  update_reminder: string;
  download: string;
  explain: string;
  system_type: string;
  country_region: string;
}) => {
  const osInfo = { windows: 'win32', macOS: 'darwin' };
  // 校验分区分包
  // const isRightLocalAPP = ['CN', 'MO'].includes(data?.country_region);
  const isRightLocalAPP = __APP_ENV__.VITE_APP_REGION === data?.country_region;
  const isMas = __APP_ENV__.VITE_APP_MAS;
  // MAC商店的包要单独判断
  const isCurrent = !isMas ? osInfo?.[data.system_type] === process.platform : (isMas && data.system_type === 'macStore');
  if (isCurrent && isRightLocalAPP) {
    checkNewVersionApi();
  }
};

export const checkNewVersionApi = () => {
  checkNewVersion().then((res) => {
    const info = res.data?.data;
    let newPackConfig: any = {};
    if (info?.package_info_two) {
      const config = info.package_info_two.find((v) => v && v.type === 'config');
      if (config) {
        newPackConfig = config;
      }
    }
    const version = {
      version: configInfo.version,
      number: configInfo.buildNumber,
    };
    const newVersion = {
      version: res.data?.data?.external_version,
      number: newPackConfig.buildNumber,
    };
    if (res?.status === 200 && compareVersionAndNumber(newVersion, version)) {
      // 先检查是否设置当前版本不更新
      if (info?.update_reminder === 'force' || (info?.update_reminder === 'prompt' && !isNotTipsUpdate(newVersion.version, newVersion.number))) {
        LynkerSDK.ipcRenderer.invoke('check-update', { winType: 'main', data: res.data?.data });
      }
    }
  });
};
// 全局提醒消息通知
export const openRemind = (remindDetail, remindType) => {
  // LynkerSDK.ipcRenderer.invoke('set-popbv', { show: true, type: 'dialogRemind', data: { remindType, remindDetail } });
  openWinRemindDialog(remindDetail, remindType, 'remind');
};

// 全局活动提醒消息通知remindType: scene场景值
export const openActivityRemind = (remindDetail, remindType) => {
  // LynkerSDK.ipcRenderer.invoke('set-popbv', { show: true, type: 'dialogActivityRemind', data: { remindType, remindDetail } });
  console.log('openActivityRemind---------------', remindDetail, remindType);
  openWinRemindDialog(remindDetail, remindType, 'activity');
};

// 独立窗口提醒
export const openWinRemindDialog = async (remindDetail, remindType, type) => {
  const data = { remindType, remindDetail, type };
  if (type === 'remind') {
    // console.log('res-------------------');
    const user = remindDetail?.mentionUsers?.find((v) => getImCardIds().includes(v.card_id));
    data.done = !!user?.finishAt;
    const res = await isRemindDetermineWhetherCompleted(remindDetail?.openid);
    if (res) return;
  }

  LynkerSDK.ipcRenderer.invoke('create-remind-dialog', {
    url: 'layoutWinRemindDialog',
    data,
    opts: {
      width: 325,
      minWidth: 325,
      height: 280,
      minHeight: 185,
    },
  });
};

// 关闭全局活动提醒消息通知remindType: scene场景值
export const removeActivityRemind = (remindDetail, remindType) => {
  removeWinRemindDialog(remindDetail, remindType, 'activity-close');
};

// 关闭窗口提醒，目前只针对活动
export const removeWinRemindDialog = (remindDetail, remindType, type) => {
  LynkerSDK.ipcRenderer.invoke('remove-remind-dialog', {
    url: 'layoutWinRemindDialog',
    data: { remindType, remindDetail, type },
    opts: {
      width: 325,
      minWidth: 325,
      height: 280,
      minHeight: 185,
    },
  });
};

// 退出登陆清空操作 refreshMainWindow 多账号切换，不退出到登录窗口，只刷新主窗口
export const emptyAndQuit = (emptyToken = false, options?: { refreshMainWindow?: boolean; openid?: string }) => {
  logHandler({ name: '调用退出', info: `${JSON.stringify(options)}`, desc: 'emptyAndQuit' });
  emptyToken && removeAccesstoken();
  // removeOpenid();
  !options?.refreshMainWindow && (removeAccountSwitchList(), removeCurrentAccount());
  LynkerSDK.ipcRenderer.invoke('im.bridge.invoke', { action: 'disconnect' });
  LynkerSDK.ipcRenderer.invoke('quit-login', options);
};

export const filterAccountSwitchList = (data, isDelete = false) => {
  let list = getAccountSwitchList() ?? [];
  if (isDelete) {
    list = list?.filter((item) => item.openid !== data.openid);
  } else {
    const index = list?.findIndex((item) => item.openid === data.openid);
    if (index === -1) {
      list.push(data);
    } else {
      list[index] = data;
    }
  }
  setAccountSwitchList(list);
};

// 设置moment国际化 繁体待确定
export const changeMomentLocal = () => {
  const lang = getLang() || 'zh-cn';
  moment.locale(lang, {
    weekdays: ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'],
    weekdaysShort: ['周日', '周一', '周二', '周三', '周四', '周五', '周六'],
    weekdaysMin: ['日', '一', '二', '三', '四', '五', '六'],
  });
};

// 检查是否可联系我
export const checkContactAuth = async (params) => {
  const { data } = await checkContactConfig(params);
  return data?.pass;
};

export const cfgCommon = () => {
  getCfgCommon().then((res) => {
    console.log('====>cfgCommon', res);
    if (res.status === 200) {
      const resourceDomains = res.data?.resourceDomains;
      resourceDomains && window.localStorage.setItem('resourceDomains', JSON.stringify(resourceDomains));
    }
  });
};
export const getEnableLog = () => {
  window.localStorage.getItem('enable_log');
};

// 检查是否设置了不提示更新
export const isNotTipsUpdate = (version, buildNumber) => {
  const Store = LynkerSDK.eStore;
  const store = new Store({ name: 'appStore' });
  const storeInfo = store?.store?.notTipsUpdate;
  let result = false;
  const isMas = __APP_ENV__.VITE_APP_MAS;
  const isStoreInfo = isMas ? storeInfo?.version : storeInfo?.version && storeInfo?.buildNumber;
  console.log('isStoreInfo', isStoreInfo, storeInfo, store);
  if (isStoreInfo) {
    const isNew = compareVersionAndNumber({
      version,
      number: buildNumber,
    }, {
      version: storeInfo.version,
      number: storeInfo.buildNumber,
    });
    // 比当前版本新，设置不提示就失效
    if (!isNew) {
      result = true;
    } else {
      result = false;
    }
  }
  console.log('isNotTipsUpdate', result, storeInfo, version, buildNumber)
  return result
}
