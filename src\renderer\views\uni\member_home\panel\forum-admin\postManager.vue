<template>
  <div class="admin-container">
    <div class="more-search">
      <div class="in-box">
        <t-input
          v-model="searchParams.keyword"
          :placeholder="t('forum.keywordPlaceholder')"
          :maxlength="50"
          clearable
          style="width: 304px"
          @blur="handleSearch"
          @enter="handleSearch"
          @clear="handleSearch"
          class="inSearch"
        >
          <template #prefix-icon>
            <iconpark-icon name="iconsearch" class="iconsearch"></iconpark-icon>
          </template>
        </t-input>
      </div>

      <div :class="{ 'f-icon': true, factive: filterList.length }" @click="postFilterDrawerRef.openDrawer">
        <iconpark-icon name="iconscreen" class="iconscreen"></iconpark-icon>
      </div>
    </div>

    <div v-if="filterList.length" class="filter-box">
      <div class="filter-box-title">{{ t("forum.advancedFilter") }}</div>
      <ul class="filter-list">
        <li v-for="(item, index) in filterList" :key="item.key" class="filter-item">
          <span>{{ item.label }}：{{ item.text }}</span>
          <span class="close-btn" @click="filterList.splice(index, 1)">
            <iconpark-icon class="close-icon" name="iconerror"></iconpark-icon>
          </span>
        </li>
        <div class="filter-clear-btn" style="height: 26px; padding: 2px 0" @click="filterList = []">
          <img src="@renderer/assets/approval/icons/del8.svg" alt="" />
          <a>{{ t("approval.approval_data.clearFilters") }}</a>
        </div>
      </ul>
    </div>

    <div class="table-container" :style="`height: ${tableMaxHeight}px`">
      <t-table
        row-key="post.id"
        :pagination="pagination.total > pagination.pageSize - 1 ? pagination : null"
        :columns="tableColumns"
        :data="tableData"
        @row-click="({ row }) => postPreviewDialogRef.openModal(row)"
        :loading="loading"
      >
        <template #empty>
          <div class="empty" :style="`height: ${tableMaxHeight - 70}px`">
            <Empty
              :name="searchEmpty ? 'no-search-contact' : 'no-data-new'"
              :tip="searchEmpty ? t('forum.searchEmpty') : t('forum.postManagerEmpty')"
            />
          </div>
        </template>

        <template #post="{ row }">
          <div class="post-cell">
            <div class="post-cell-content">
              <div class="post-cell-text">
                <span v-if="row.post.managerPin" class="post-status-tag">{{ t("forum.sticky") }}</span>
                <t-tooltip :content="row.post.text">
                  <span>{{ row.post.text }}</span>
                </t-tooltip>
              </div>

              <div class="post-cell-topic" v-if="row.topics.length">
                <t-tooltip :content="row.topics[0].name">
                  {{ row.topics[0].name }}
                </t-tooltip>
              </div>
            </div>

            <template v-if="row.post.content?.objects.length">
              <div class="post-cell-cover">
                <template v-if="row.post.postType === 'VIDEO'">
                  <video :src="row.post.content.objects[0].url" />
                  <img class="post-video-play-icon" src="@/assets/digital/icon/icon-play.png" alt="" />
                </template>
                <img v-else class="rounded-4" :src="row.post.content.objects[0].url" alt="" />
              </div>
            </template>
          </div>
        </template>

        <template #owner="{ row }">
          <span>{{ row.owner.name }}</span>
        </template>

        <template #postedAt="{ row }">
          <span>{{ row.post.postedAt ? dayjs(row.post.postedAt).format("YYYY-MM-DD HH:mm") : "" }}</span>
        </template>

        <template #operate="{ row }">
          <div class="operate-cell">
            <a @click.stop="togglePostSticky(row)">{{
                row.post.managerPin ? t("forum.stickyCl") : t("forum.sticky")
              }}</a>
            <a @click.stop="togglePostShield(row)">{{
                row.post.status === "BLOCK" ? t("forum.shieldCl") : t("forum.shield")
              }}</a>
          </div>
        </template>
      </t-table>
    </div>

    <!--高级筛选抽屉组件-->
    <post-filter-drawer ref="postFilterDrawerRef" v-model:filter-list="filterList" />

    <!--帖子详情预览组件-->
    <post-preview-dialog ref="postPreviewDialogRef" />
  </div>
</template>

<script setup lang="ts">
import { onBeforeUnmount, onMounted, computed, reactive, ref, watch, nextTick } from "vue";
import { useI18n } from "vue-i18n";
import PostFilterDrawer from "./components/post-filter-drawer.vue";
import Empty from "@/components/common/Empty.vue";
import PostPreviewDialog from "./components/post-preview-dialog.vue";
import dayjs from "dayjs";
import { debounce } from "lodash";
import to from "await-to-js";
import { getManagerPostList, setPostShield, setPostSticky } from "@/api/uni/api/forumAdminApi";
import { MessagePlugin } from "tdesign-vue-next";

const { t } = useI18n();

const postFilterDrawerRef = ref(null);
const postPreviewDialogRef = ref(null);

const loading = ref(false);

// 搜索参数
const searchParams = reactive({
  keyword: null,
});

// 高级筛选项列表
const filterList = ref([]);

const filterParams = computed(() =>
  filterList.value.reduce((params, { key, value }) => {
    if (key === "dateTime") {
      params["start_time"] = value[0];
      params["end_time"] = value[1];
    } else {
      params[key] = value;
    }
    return params;
  }, {}),
);

// 搜索结果为空
const searchEmpty = ref(false);

// 分页对象
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
  onChange: ({ current, pageSize }) => {
    pagination.current = current;
    pagination.pageSize = pageSize;
    loadTableData();
  },
});

// 表格宽度
const tableWidth = 944;

// 表格高度
const tableMaxHeight = ref(document.body.clientHeight - 128);

// 表格列
const tableColumns = [
  {
    colKey: "post",
    title: t("forum.posts"),
    width: `${440 / tableWidth}%`,
  },
  {
    colKey: "owner",
    title: t("forum.owner"),
    width: `${160 / tableWidth}%`,
  },
  {
    colKey: "postedAt",
    title: t("forum.postedAt"),
    width: `${160 / tableWidth}%`,
  },
  {
    colKey: "operate",
    title: t("forum.operate"),
    width: `${184 / tableWidth}%`,
  },
];

// 表格数据
const tableData = ref([]);

// 加载表格数据
const loadTableData = async (isSearch = false) => {
  loading.value = true;

  const res = await getManagerPostList({
    "page.number": pagination.current,
    "page.size": pagination.pageSize,
    keyword: searchParams.keyword,
    ...filterParams.value,
  });

  loading.value = false;

  const {
    data: { page, posts },
  } = res.data;
  searchEmpty.value = isSearch ? page.total < 1 : false;

  tableData.value = posts;

  pagination.total = page.total;
};

const handleSearch = debounce(() => {
  pagination.current = 1;
  loadTableData(true);
}, 300);

// 切换帖子顶置状态
const togglePostSticky = async (row) => {
  const [error, res] = await to(setPostSticky({ postId: row.post.id }));

  if (error) {
    MessagePlugin.warning(error.message);
    return;
  }

  MessagePlugin.success(`${row.post.managerPin ? t("forum.stickyCl") : t("forum.sticky")}成功`);
  loadTableData();
};

// 切换帖子屏蔽状态
const togglePostShield = async (row) => {
  await setPostShield({ postId: row.post.id });
  MessagePlugin.success(`${row.post.status === "BLOCK" ? t("forum.shieldCl") : t("forum.shield")}成功`);
  loadTableData();
};

// 重置表格最大高度
const resizeTableMaxHeight = () => {
  tableMaxHeight.value = document.body.clientHeight - (document.querySelector(".filter-box")?.clientHeight || 0) - 128;
};

loadTableData();

onMounted(() => {
  window.addEventListener("resize", resizeTableMaxHeight);
});

onBeforeUnmount(() => {
  window.removeEventListener("resize", resizeTableMaxHeight);
});

watch(
  () => filterList.value.length,
  async () => {
    handleSearch();
    await nextTick();
    resizeTableMaxHeight();
  },
);
</script>

<style lang="less" scoped>
@import "@renderer/views/member/member_home/panel/public.less";

.iconsearch {
  font-size: 20px;
}

.admin-container {
  padding: 16px;

  .filter-box {
    padding-top: 24px;
    display: flex;
    gap: 8px;

    .filter-box-title {
      color: var(--text-kyy-color-text-2, #516082);
      font-size: 14px;
      font-weight: 600;
      line-height: 22px;
    }

    .filter-list {
      flex: 1;
      display: flex;
      flex-wrap: wrap;

      .filter-item {
        display: flex;
        padding: 2px 8px;
        gap: 8px;
        color: var(--kyy-color-tag-text-black, #1a2139);
        cursor: pointer;
        line-height: 22px;
        border-radius: 4px;
        background: var(--kyy-color-tag-bg-gray, #eceff5);
        margin-right: 8px;

        .close-btn {
          display: flex;
          align-items: center;
          img {
            width: 12px;
            height: 12px;
          }

          .close-icon {
            font-size: 20px;
            color: #828da5;
            transition: all 0.25s linear;
          }
        }
      }

      .filter-clear-btn {
        display: flex;
        margin-left: 4px;
        cursor: pointer;

        img {
          width: 14px;
          height: 14px;
          margin-top: 4px;
          margin-right: 4px;
        }

        a {
          display: inline-block;
        }
      }
    }
  }

  .table-container {
    margin-top: 24px;
    overflow: auto;

    :deep(.t-table__body > tr > td) {
      cursor: pointer;
    }

    :deep(.t-table__pagination) {
      padding-bottom: 0;
    }

    .post-cell {
      display: flex;
      line-height: 22px;
      justify-content: space-between;
      gap: 12px;

      .post-cell-content {
        flex: 1;

        .post-cell-text {
          display: -webkit-box;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
          -webkit-line-clamp: 2;

          .post-status-tag {
            margin-right: 4px;
            padding: 2px 4px;
            border-radius: 4px;
            background: var(--kyy_color_tag_bg_brand, #eaecff);
            font-size: 12px;
            color: var(--kyy_color_tag_text_brand, #4d5eff);
          }
        }

        .post-cell-topic {
          margin-top: 4px;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
          -webkit-line-clamp: 1;
          color: var(--text-kyy_color_text_3, #828da5);
        }
      }

      .post-cell-cover {
        border-radius: 4px;
        width: 72px;
        height: 72px;
        position: relative;

        img,
        video {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .post-video-play-icon {
          width: 36px;
          height: 36px;
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
        }
      }
    }

    .operate-cell {
      display: flex;
      gap: 8px;

      a {
        padding: 4px;
        color: var(--brand-kyy_color_brand_default, #4d5eff);
        line-height: 22px;
        border-radius: 4px;

        &:hover {
          background: var(--bg-kyy_color_bgBrand_hover, #eaecff);
        }
      }
    }
  }
}
</style>
