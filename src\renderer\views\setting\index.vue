<template>
  <div>
    <div class="drag">
      <span class="title">{{ t('zx.setting.preferences') }}</span>
      <div
        v-if="isNotMac"
        class="windows-icon-bg close-icon"
        @click="Close"
      >
        <!-- <img
          src="@renderer/assets/icons/svg/close.svg"
          class="icon-size"
        > -->
        <iconpark-icon class="icon-size" name="iconerror"></iconpark-icon>
      </div>
    </div>
    <div class="f" style="height: calc(100% - 44px)">
      <!-- <div class="title">{{ t('zx.setting.preferences') }}</div> -->
      <div class="tabs">
        <div v-for="item in tabArr" :key="item.name" :class="['f', 'tab-item',item.active ? 'active' : '']" @click="changeTab(item)">
          <iconpark-icon class="tab-item-icon" :name="item.svg" :style="{color: item.value === 1 ? '#3E4CD1' : '',fontSize:item.size+'px'}"></iconpark-icon>
          <div>{{ item.name }}</div>
        </div>
      </div>
      <div class="view">
        <common v-if="activeValue === 1" />
        <privacy v-else-if="activeValue === 2" />
        <!-- <notice v-else-if="activeValue === 3" /> -->
        <account v-else-if="activeValue === 4" />
        <keyboard v-else-if="activeValue === 5" />
        <up v-else-if="activeValue === 6" @closeUp="closeUp" />
        <about v-else-if="activeValue === 7" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';
import settingCommon from '@renderer/assets/svg/icon_setting_common.svg';
import settingPrivacy from '@renderer/assets/svg/icon_setting_privacy.svg';
import settingNotice from '@renderer/assets/svg/icon_setting_notice.svg';
import settingAccount from '@renderer/assets/svg/icon_setting_account.svg';
import settingKeyboard from '@renderer/assets/svg/icon_setting_keyboard.svg';
import settingUp from '@renderer/assets/svg/icon_setting_up.svg';
import settingAbout from '@renderer/assets/svg/icon_setting_about.svg';
import common from './components/common.vue';
import privacy from './components/privacy.vue';
import notice from './components/notice.vue';
import account from './components/account.vue';
import keyboard from './components/keyboard.vue';
import up from './components/up.vue';
import about from './components/about.vue';
import { isNotMac } from './util';
import LynkerSDK from '@renderer/_jssdk';

const { ipcRenderer } = LynkerSDK;
const { t } = useI18n();
// mac商店类型不展示更新
const isShowUpdate = ref(!__APP_ENV__.VITE_APP_MAS);
const tabArr = ref([
  {
    svg: 'iconset-b78mbmnh',
    name: t('zx.setting.common'),
    value: 1,
    active: true,
    size:20,
  },
  {
    svg: 'iconprivate',
    name: t('zx.setting.privacy'),
    value: 2,
    active: false,
    size:20,
  },
  // {
  //   svg: 'iconremind',
  //   name: t('zx.setting.notice'),
  //   value: 3,
  //   active: false,
  // },
  {
    svg: 'iconadmin-b78kodj7',
    name: t('zx.setting.account'),
    value: 4,
    active: false,
    size:21,
  },
  {
    svg: 'iconkeyboard',
    name: t('zx.setting.keyboard'),
    value: 5,
    active: false,
    size:20,
  },
  {
    svg: 'iconabout',
    name: t('zx.setting.about'),
    value: 7,
    active: false,
    size:21,
  }
]);
const activeValue = ref(1);
const heightMap = new Map([[1, 560], [2, 360], [3, 403], [4, 508], [5, 492], [6, 360], [7, 400]]);
const changeTab = (item) => {
  tabArr.value.map(v => {
    v.active = false;
    v.name === item.name && (v.active = true, activeValue.value = item.value);
    return v;
  });
  ipcRenderer.invoke('set-window-sise', {window: 'settingWindow', width: 784, height: heightMap.get(item.value)});
}
const Close = () => {
  ipcRenderer.invoke('window-hide');
};

const closeUp = () => {
  changeTab(tabArr.value[0]);
  Close();
}

//if (isShowUpdate.value) {
  const _update = {
    svg: 'iconnew',
    name: t('zx.setting.update'),
    value: 6,
    active: false,
    size:21,
  }
  tabArr.value.splice(tabArr.value.length-1, 0, _update);
//}
</script>

<style lang="less" scoped>
.f {
  display: flex;
}
.f-c-c {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.drag {
  width: 100%;
  // height: 24px;
  height: 44px;
  -webkit-app-region: drag;
  text-align: center;
  background: var(--bg-kyy-color-bg-deep, #F5F8FE);
}

.windows-icon-bg {
  position: absolute;
  right: 12px;
  top: 0px;
  -webkit-app-region: no-drag;
  line-height: 44px;
  .icon-size {
    width: 20px;
    height: 20px;
    cursor: pointer;
  }
}
.title {
  color: var(--kyy_color_modal_title, #1A2139);
  text-align: center;

  /* kyy_fontSize_2/bold */
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 44px; /* 157.143% */
}
.tabs {
  // justify-content: center;
  // margin-bottom: 24px;
  padding: 16px;
  width: 207px;
  height: 100%;
  border-right: 1px solid var(--divider-kyy-color-divider-light, #ECEFF5);
// background: #FFF;
  .tab-item {
    display: flex;
    align-items: center;
    width: 100%;
    // height: 52px;
    padding: 12px;
    border-radius: 4px;
    cursor: pointer;
    color: var(--text-kyy-color-text-1, #1A2139);

    /* kyy_fontSize_3/bold */
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px; /* 150% */
    margin:2px 0;
  }
  .active {
    background: #daecff;
  }
  .tab-item-icon {
    // width:20px;
    // height: 17px;
    margin-right: 8px;
    font-size: 17px;
    // margin-bottom: 2px;
  }
}
.tab-item:hover{
  background-color: rgba(243, 246, 250, 1);
}
.view {
  // max-height: calc(100vh - 140px);
  // overflow: scroll;
  // padding-bottom: 24px;
  height: 100%;
  width: 100%;
  padding: 32px;
}
</style>
