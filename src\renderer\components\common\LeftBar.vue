<template>
  <div
    class="left-bar"
    :style="{
      width: !collapsed ? '80px' : '144px',
    }"
  >
    <!-- 64px 144px -->
    <t-menu
      :value="configData.$state.path_uuid"
      :width="!collapsed ? '80px' : '144px'"
      theme="dark"
      class="left-menu"
      :class="{ active: collapsed }"
    >
      <t-menu-item
        v-for="(item, index) in routers"
        :key="item.path_uuid"
        :value="item.path_uuid"
        :class="!collapsed ? 'narrow-bar' : ''"
        @mouseup.stop="hideDialog"
        @click="onclick(item, index)"
        @dblclick="dblclick(item, index)"
      >
        <template #icon>
          <div v-for="badge in badgeArr" :key="badge.path" class="!z-a">
            <t-badge
              v-if="item.path == badge.path"
              :count="badge.count"
              :color="'#FF4AA1'"
              class="redpoint"
              :class="{ 'width-auto': badge.count > 9 }"
              :style="
                collapsed ? 'left: 32px;top:13px;right: auto;z-index: 2;' : 'z-index: 2;'
              "
            />
          </div>

          <t-badge
            v-if="getBadgeCount(item)"
            :count="getBadgeCount(item)"
            :dot="showBadgeDot(item)"
            color="#FF4AA1"
            class="redpoint"
            :class="{ 'width-auto': getBadgeCount(item) > 9 }"
            :style="
              collapsed ? 'left: 32px;top:13px;right: auto;z-index: 2;' : 'z-index: 2;'
            "
          />
          <t-badge
            v-if="item.path_uuid === 'workBench' && getWorkBenchBadgeCount"
            :count="getWorkBenchBadgeCount"
            :dot="1"
            color="#FF4AA1"
            class="redpoint"
            :class="{ 'width-auto': getWorkBenchBadgeCount > 9 }"
            :style="
              collapsed ? 'left: 32px;top:13px;right: auto;z-index: 2;' : 'z-index: 2;'
            "
          />
          <t-badge
            v-if="item.path_uuid === 'digital_platform' && digitalPlatformHasRed"
            :count="digitalPlatformHasRed"
            :dot="1"
            color="#FF4AA1"
            class="redpoint"
            :style="
              collapsed ? 'left: 32px;top:13px;right: auto;z-index: 2;' : 'z-index: 2;'
            "
          />

          <icon
            class="menu-icons"
            :name="item.path_uuid === configData.$state.path_uuid ? item.activeIcon : item.icon"
            :url="iconUrl"
          />
        </template>
        <span class="menus-title">{{ item.path_uuid ? t("application." + item.path_uuid) : item.title }}</span>
      </t-menu-item>
      <t-menu-item
        value="more-menu-item"
        :class="[!collapsed ? 'narrow-bar' : '', { active: getShowMoreSubMenu }, 'more-menu']"
        @mouseup.stop="hideDialog"
        @click="onClickMore"
      >
        <template #icon>
          <t-badge
            v-if="getMoreMenuHasTips"
            color="#ff4aa1"
            size="small"
            :dot="getMoreMenuHasTips"
            :count="getMoreMenuHasTips ? 1 : 0"
            :style="collapsed ? 'left: 30px;right: auto;z-index: 2;' : 'z-index: 2;'"
          />
          <icon
            class="menumoreicon"
            name="more"
            :fill-opacity="getShowMoreSubMenu ? 1 : 0.7"
            :style="{ color: '#fff' }"
            :url="iconUrl"
          />
        </template>
        <span class="menus-title">更多</span>
      </t-menu-item>
    </t-menu>

    <!-- <div class="show-menu" @click.stop="changeCollapsed">
      <div class="leftbar-show-menu">
        <div class="show-menu-arrow" :class="{ active: collapsed }"></div>
      </div>
    </div> -->

    <!-- <div
      class="menubarIconShowHoverBox"
      @mouseenter="onMouseEnter"
      @mouseleave="onMouseLeave"
    ></div> -->
  </div>
</template>

<script setup lang="ts">
import { Icon } from "tdesign-icons-vue-next";
import { computed, nextTick, onMounted, ref, watch } from "vue";

import { useRoute, useRouter } from "vue-router";
import { useMessageStore } from "@/views/message/service/store";

import { iconUrl } from "@renderer/plugins/KyyComponents";
import { configDataStore } from "@renderer/store/modules/configData";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
import { setLanguage } from "@renderer/i18n";
import { useMemberStore } from "@renderer/views/member/store/member";
import { updateAllCount } from "@renderer/views/member/hooks/total";
import { useContactsStore } from "@renderer/store/modules/contacts";
import { allRouters, defaultRouters, allRoutersUuid } from "@renderer/constants/index";
import { hideDialog } from "@renderer/utils/DialogBV";
import { businessCount } from "@renderer/api/business";
import { msgEmit } from "@renderer/views/message/service/msgEmit";
import { getWorkshopRedMenuTotal, getDigitalPlatformRedMenuTotal } from '@renderer/api/customerService';
import to from 'await-to-js';
import { needRedirectLastLogin } from "@renderer/views/square/utils/business";
import { approveCenterCount, approveForeignCount } from "@/api/approve";
import { useApprovalStore } from "@/store/modules/approval";
import { onMountedOrActivated } from "@/hooks/onMountedOrActivated";
import { useSquareStore } from "@/views/square/store/square";
import { zhixingRedPoint } from "@/views/zhixing/util";
import { useStateStore } from '../../views/square/store/state';
import {
  setAccountAuthRouters,
  getOpenid
} from "@/utils/auth";
import { filterImg } from '@/views/workBench/utils';
import LynkerSDK from '@renderer/_jssdk';

setLanguage(window.localStorage.getItem("lang") || "zh-cn");
const log = LynkerSDK.eLog;

const configData = configDataStore();
const approvalStore = useApprovalStore();
const contactsStore = useContactsStore();
const stateStore = useStateStore();
const workshopHasRed = ref(false);
const digitalPlatformHasRed = ref(false);

const msgStore = useMessageStore();
const memberStore = useMemberStore();
const leftMenuNum = ref(0);
// const banMenuIconVisible = ref(false); // 禁用更多按钮的显示
// const banMenuIconHidden = ref(false); // 禁用更多按钮的隐藏
// const visibleTimer = ref();

// 消息需要显示未读数总计
const showCountAtCollapsed = ref(["/main/message", "/square/friend-circle"]);
const badgeArr = ref([
  {
    path: "/memberIndex/member_number",
    count: memberStore.getAllTeamAndAll
  },
  {
    path: "/approvalIndex/approve_home",
    count: approvalStore.approveCenterCountData,
  },
  {
    path: "/businessIndex/businessReleaseList",
    count: approvalStore.nicheCountData,
  },
  {
    path: "/main/contactsIndex",
    count: approvalStore.approveCenterCountData,
  },
  {
    path: '/zhixing',
    count: 0
  }
]);

const getBadgeCount = (item) => {
  const count = dotCount.value[item.title];
  // 广场有未读的动态，确保数量大于0以让其显示红点。同时有未读动态和消息通知，优先显示消息通知数量
  // eslint-disable-next-line no-nested-ternary
  if (item.path === "/square/friend-circle") return count > 0 ? count : hasUnreadPost.value ? 1 : 0;
  return count;
};

/**
 * 获取数智工场的消息阅读情况
 */
const getWorkshopMessageInfo = async () => {
  const [err, res] = await to(getWorkshopRedMenuTotal({
    is_app: 0
  }));
  if (err) {
    return;
  }
  workshopHasRed.value = res.data.data.is_red;
};


const getDigitalPlatformMessageInfo = async () => {
  const [err, res] = await to(getDigitalPlatformRedMenuTotal({}));
  if (err) {
    return;
  }
  digitalPlatformHasRed.value = res?.data?.data.is_red || false;
};



const showBadgeDot = (item) => {
  const showDot = showCountAtCollapsed.value.includes(item.path) ? false : !collapsed.value;
  // 广场有未读的动态，只显示红点
  if (item.path === "/square/friend-circle") return !(dotCount.value[item.title] > 0);
  return showDot;
};
LynkerSDK.ipcRenderer.on("new-window-left-rest", () => {
  // 执行相应的操作
  configData.$state.numeIndex = 0;
  configData.$state.path_uuid = "message";
});

// 菜单项红点提醒
const dotCount = ref({});
LynkerSDK.ipcRenderer.on("sidebar-count-change", (event, { title, count }) => {
  console.log("sidebar-count-change", title, count);
  dotCount.value[title] = count;

  // if (title === "广场") hasUnreadPost.value = count > 0;
});

// 有新的广场动态
const hasUnreadPost = ref(false);
LynkerSDK.ipcRenderer.on("on-unread-post", (_, unread = true) => {
  console.log("on-unread-post", unread);
  hasUnreadPost.value = unread;
});

LynkerSDK.ipcRenderer.on("square-notify", () => {
  console.log("square-notify");
  squareStore.getSquaresList();
});

const route = useRoute();
const router = useRouter();

const collapsed = computed({
  get() {
    console.log('用时stateStore.menuBarCollapsed~stateStore.menuBarCollapsed1', stateStore.menuBarCollapsed, Date.now());
    return stateStore.menuBarCollapsed;
  },
  set(val) {
    stateStore.menuBarCollapsed = val;
  }
});

const Store = LynkerSDK.eStore;
const store = new Store();
const routers = ref([]);
const moreRouters = ref([
  {
    title: "活动",
    icon: "appuuidactivities",
    activeIcon: "appuuidactivitiesactive",
    path: "/activity/activityList",
    path_uuid: "activities"
  },
  {
    title: "云盘",
    icon: "appuuiddisk",
    activeIcon: "appuuiddiskactive",
    path: "/clouddiskIndex/clouddiskhome",
    path_uuid: "disk"
  },
]);

/**
 * 获取是否有消息提示的红点
 */
const getHasTip = (paths) => {
  let hasTip = false;
  for (const item of paths) {
    for (const badge of badgeArr.value) {
      if (item === badge.path && badge.count > 0) {
        hasTip = true;
        break;
      }
    }
  }
  return hasTip;
};

/**
 * 更多菜单是否需要显示红点
 */
 const getMoreMenuHasTips = computed(() => getHasTip(moreRouters.value.map((item) => item.path)));

/**
 * 获取数智工厂的红点状态
 */
const getWorkBenchBadgeCount = computed(() => {
  const workBenchPaths = [
    '/businessIndex/businessReleaseList', // 商机
    '/approvalIndex/approve_home', // 审批
    '/memberIndex/member_number', // 数字商协/会员
    '/politicsIndex/politics_number', // 数字城市
  ];
  return workshopHasRed.value ? 1 : 0;
});

/**
 * 获取需要显示左侧菜单栏的列表
 */
const getLeftbarMenuList = (_routers) => {
  console.log("🚀 ~ getLeftbarMenuList ~ _routers:", _routers);
  const showList = ['/main/message', '/square/friend-circle', '/zhixing', '/bigMarketIndex/home', '/workBenchIndex/workBenchHome', '/digitalPlatformIndex/digital_platform_home', '/main/contactsIndex'];
  const menuList = [];
  leftMenuNum.value = 0;
  for (const item of showList) {
    const target = _routers.find((v) => v.path === item);
    if (target) {
      leftMenuNum.value++;
      menuList.push(target);
    }
  }
  return menuList;
};

/**
 * 是否选中了更多菜单中的菜单项
 */
 const getShowMoreSubMenu = computed(() => {
  console.log('configData.$state.path_uuid', configData.$state.path_uuid);
  const activeSubMenu = moreRouters.value.find((item) => item.path_uuid === configData.$state.path_uuid);
  return !!activeSubMenu;
});

const setRouters = (_routers) => {
  routers.value = getLeftbarMenuList(_routers);
  console.log("🚀 ~ setRouters ~ _routers:", _routers);
};

/**
 * 点击更多时，发送事件弹窗
 */
const onClickMore = async () => {
  LynkerSDK.ipcRenderer.invoke("show-more-menu", {
    data: {},
    isCollapse: collapsed.value ? 1 : 0, // 菜单栏的折叠状态
    leftMenuNum: leftMenuNum.value || 1 // 左侧菜单栏的数量
  });
};

/**
 * 当鼠标移入菜单栏，没有显示更多菜单时，显示图标
 */
// const onMouseEnter = () => {
//   if (!banMenuIconVisible.value) {
//     ipcRenderer.invoke('menubar-icon-show', { isCollapse: collapsed.value ? 1 : 0 });
//   }
// };
// /**
//  * 当鼠标移出菜单栏，没有显示更多菜单时，隐藏图标
//  */
// const onMouseLeave = () => {
//   visibleTimer.value && clearTimeout(visibleTimer.value);
//   visibleTimer.value = setTimeout(() => {
//     console.log('banMenuIconVisible.value', banMenuIconVisible.value, banMenuIconHidden.value);
//     if (!banMenuIconVisible.value && !banMenuIconHidden.value) {
//       ipcRenderer.invoke('menubar-icon-hide');
//     }
//   }, 100);
// };
// ipcRenderer.on('menuicon-mouse-enter', () => {
//   banMenuIconHidden.value = true;
// });
// ipcRenderer.on('menuicon-mouse-leave', () => {
//   banMenuIconHidden.value = false;
//   ipcRenderer.invoke('menubar-icon-hide');
// });
// /**
//  * 当关闭了弹出的BV弹窗时触发该事件
//  */
// ipcRenderer.on('close-popbv', (event, value) => {
//   banMenuIconVisible.value = false;
//   banMenuIconHidden.value = false;
// });
// /**
//  * 当弹出的BV弹窗时触发该事件
//  */
//  ipcRenderer.on('show-popbv', (event, value) => {
//   if (value !== 'moreMenuIcon') {
//     banMenuIconVisible.value = true;
//   }
// });

LynkerSDK.ipcRenderer.on("refresh-auth-routers", (event, value) => {
  const res = (value || defaultRouters).filter((v) => v);
  setRouters(res);
});

LynkerSDK.ipcRenderer.on("update-nume-index", (event, value) => {
  let query = {};
  // 跳转通讯录带参数，/main/路由ipcRenderer click-menu-item 直接return了，在这里带参处理。
  if (value.constructor === Object) {
    query = value.query;
    value = value.value;
  }
  // value 可能是index/uuid
  const new_index = (allRoutersUuid.includes(value) ? allRoutersUuid.findIndex((v) => v === value) : value) ?? 0;
  const new_uuid = (allRoutersUuid.includes(value) ? value : allRoutersUuid?.[value]) ?? "message";
  configData.$state.numeIndex = new_index;
  configData.$state.path_uuid = new_uuid;
  console.log('update-nume-index', value, allRoutersUuid, new_index, new_uuid, routers);
  if (["message", "address_book"].includes(new_uuid) && window.location.hash.includes("/main/")) {
    const routePath = new_uuid === "message" ? "/main/message" : "/main/contactsIndex";
    // 非mainBW需要触发click-menu-item跳转
    LynkerSDK.ipcRenderer
      .invoke("click-menu-item", {
        url: routePath,
        query,
        click_path_uuid: new_uuid,
      })
      .then((res) => {
        router.replace({ path: routePath, query });
      });
  }
  // if (new_uuid === "member") {
  //   // 更新会员数量
  //   updateAllCount();
  // }

  console.log(value, window.location.hash);
});

LynkerSDK.ipcRenderer.on("set-unread-post-approval", () => {
  console.log("on-unread-post");
  // approveCenterCountFn();
  // 知行中的红点统计包含审批
  zhixingCount();
  getWorkshopMessageInfo();
  getDigitalPlatformMessageInfo();
  // store.fetchNewsStats();
  // 个人广场好友圈有新动态
});

LynkerSDK.ipcRenderer.on("update-niche-reddot", () => {
  nicheCountFn();
});

LynkerSDK.ipcRenderer.on('IM-refresh', (e, data) => {
  console.log('leftBar')
  getWorkshopMessageInfo();
  getDigitalPlatformMessageInfo();
});

LynkerSDK.ipcRenderer.on("set-zhixing-count", () => {
  zhixingCount();
});

const goPath = async (item, index) => {
  console.log(item, "itemmmmmmmmm");
  if (item.path_uuid === 'square') {
    const org = needRedirectLastLogin();
    const queryStr = org ? 'org=true' : '';
    item.query = item.query ? `${item.query}&${queryStr}` : queryStr;
  }

  nextTick(async () => {
    window.localStorage.setItem('LeftBarpath_uuid', item.path_uuid);
    console.log(item, "itemmmmmmmmm11");

    await setAccountAuthRouters("click-menu-item");

    // todo 测试避免快速切换 窗口异步 选中值异常 前置设置选中值 看效果
    // const old_selected_path_uuid = configData.$state.path_uuid;
    configData.$state.numeIndex = index;
    configData.$state.path_uuid = item.path_uuid;
    LynkerSDK.ipcRenderer
      .invoke("click-menu-item", {
        url: item.path,
        query: item.query,
        selected_path_uuid: configData.$state.path_uuid,
        click_path_uuid: item.path_uuid,
      })
      .then((res) => {
        if (res) {
          // configData.$state.numeIndex = index;
          // configData.$state.path_uuid = item.path_uuid;
        }
        setSize();
        if (item.path_uuid === "message") {
          useMessageStore().sendReadReceipt();
        }
      })
      .catch((err) => {
        console.log("goPath error: ", err);
      });

    if (item.path_uuid === "message") { // 工作台暂时注释
      router.replace({ path: "/main/message" });
    } else if (item.path_uuid === "address_book") {
      router.replace({ path: "/main/contactsIndex" });
      // 重新进入通讯录模块，获取最新组织数据，目前是多窗口，后期修改成一个窗口到通讯录模块做watch监听路由
      LynkerSDK.ipcRenderer.invoke("refresh-page-data", { page: "address_book" });
    }
    if (item.path_uuid === "niche") {
      nicheCountFn();
    } // 工作台暂时注释
  });
};

const onclick = (item, index) => {
  localStorage.setItem("curMenuValue", item.path_uuid);
  if (item.path_uuid === 'workBench') {
    getWorkshopMessageInfo();
  } else if(item.path_uuid === 'digital_platform') {
    getDigitalPlatformMessageInfo();
  }
  if (item.path_uuid === 'message') {
    onClickMesageMenuItem(item, index);
  } else {
    goPath(item, index);
  }
};

const onClickMesageMenuItem = (item, index) => {
  if (configData.$state.path_uuid === item.path_uuid) {
    return;
  }

  goPath(item, index);
};


const dblclick = async (item, index) => {
  if (item.path_uuid === "message" && configData.$state.path_uuid === item.path_uuid) {
    const isDefault = msgStore.activeGroupType !== "all"
    isDefault && msgStore.onSwitchGroup('all');
    setTimeout(() => {
      msgEmit.emit("locate", isDefault);
    }, 100);
  }
};

const changeCollapsed = () => {
  const cv = collapsed.value;
  collapsed.value = !collapsed.value;

  // 如果是伸展
  if (!cv) {
    // 菜单移动比较慢，做一个延时
    setTimeout(() => {
      setSize();
    }, 200);
  } else {
    // 收缩不需要设置延时
    setSize();
  }
};

const setSize = () => {
  LynkerSDK.ipcRenderer
    .invoke("set-size", {
      flag: collapsed.value,
    })
    .then((res) => {
      console.log(res, "ressssssssss");
    })
    .catch((err) => {
      console.log(err, "resssssssssserrerrerr");
    });
};

const updateBadageCount = (path: string, count: number) => {
  console.log("updateBadageCount");
  const badge = badgeArr.value.find((v) => v.path === path);
  badge.count = count;
};

// watch(
//   () => approvalStore.approveCenterCountData,
//   (newValue, oldValue) => {
//     console.log("路由aaa");
//     updateBadageCount("/approvalIndex/approve_home", newValue);
//   },
//   {
//     deep: true,
//   }
// );
// watch(
//   () => approvalStore.updateNew,
//   () => {
//     nicheCountFn();
//   },
// );
watch(
  () => contactsStore.newContactsNum,
  (newValue) => {
    updateBadageCount("/main/contactsIndex", newValue);
  },
);
// 商协会1.2暂时去掉
// watch(
//   () => memberStore.getAllTeamAndAll,
//   (newValue) => {
//     updateBadageCount("/memberIndex/member_number", newValue);
//   }
// );

// watch(
//   () => contactsStore.contactsOrgJumpId,
//   (newValue) => {
//     configData.$state.numeIndex = 2;
//   }
// );
// watch(
//   () => memberStore.getAllTeamApplyCount,
//   (newValue, oldValue) => {
//     // approvalStore.approveCenterCountData = newValue;
//     console.log("到我者了嘛", newValue);
//     console.log("到我者了嘛", memberTotalCount);
//   },
//   {
//     deep: true,
//     immediate: true
//   }
// );
// watch(
//   () => memberStore.getAllTeamActiveCount,
//   (newValue, oldValue) => {
//     // approvalStore.approveCenterCountData = newValue;
//     console.log("到我者了嘛", newValue);
//     console.log("到我者了嘛", memberTotalCount);
//   },
//   {
//     deep: true,
//     immediate: true
//   }
// );
// const memberTotalCount = computed(
//   () => memberStore.getAllTeamApplyCount + memberStore.getAllTeamActiveCount
// );

onMounted(() => {
  const res = (store.get("accountAuthRouters") || defaultRouters).filter(
    (v) => v
  );
  localStorage.setItem("curMenuValue", 'message');
  window.localStorage.setItem('LeftBarpath_uuid', 'message');

  setRouters(res);
  setTimeout(() => {
    handleProtocolInvoke();
  }, 0);
  setSize();
});

onMountedOrActivated(() => {
  // approveCenterCountFn();
  nicheCountFn();
  getSquareCount();
  updateAllCount(); // 获取会员待审核总数
  zhixingCount();
  getWorkshopMessageInfo();
  getDigitalPlatformMessageInfo();
});

const zhixingCount = async () => {
  const countObj = await zhixingRedPoint();
  const zhixingRedCount = countObj.manifestAll + countObj.approval + countObj.later + countObj.notice + countObj.activity;
  updateBadageCount("/zhixing", zhixingRedCount);
};

// 处理 ringkol:// 协议跳转指定模块
const handleProtocolInvoke = () => {
  // 注册协议唤起App的模块名，映射到侧边栏路由 routers 的路径
  const moduleMap = {
    chat: "/main/message", // 消息
    square: "/square/friend-circle", // 广场号
    contacts: "/main/contactsIndex", // 联系人
    disk: "/clouddiskIndex/clouddiskhome", // 云盘
    approve: "/approvalIndex/approve_home", // 审批
    project: "/engineerIndex/engineer_home", // 工程
    member: "/memberIndex/member_number", // 会员
    politics: "/politicsIndex/politics_number", // 城市
    niche: "/businessIndex/businessReleaseList", // 商机
    serve: "/serviceIndex/service_home", // 服务
    activities: "/activity/activityList", // 活动
    workBench: "/workBenchIndex/workBenchHome", // 工作台
    "chain-device": "/deviceIndex/device-list", // 设备
    "chain-customer": "/customerIndex/customer-list", // 客户
    "chain-supplier": "/supplierIndex/supplier-list", // 供应商
    "chain-partners": "/partnerIndex/partner-list", // 合作伙伴
  };

  // module 为模块名，在上面的 moduleMap 定义; query 为查询参数（可在模块入口内解析并处理后续逻辑）
  log.info("handleProtocolInvoke:", route.query);
  let { module, ...query } = route.query || {};
  console.log("1111111111");
  console.log(module, query, route.query, store.get("secondInstanceParams"));
  const secondInstanceParams = store.get("secondInstanceParams");
  if (!module) {
    if (secondInstanceParams) {
      module = secondInstanceParams?.module;
    } else {
      return;
    }
  }
  store.delete("secondInstanceParams");

  const path = moduleMap[module as string];
  const index = routers.value.findIndex((v) => v.path === path);
  if (index > -1) {
    const menu = routers.value[index];
    log.info("handleProtocolInvoke: menu:", menu);
    if (menu) {
      const path_uuid = module === 'store' ? 'workBench' : allRouters?.find((v) => v.path === path)?.path_uuid;
      console.log("secondInstanceParams?.openid", secondInstanceParams?.openid);
      if (secondInstanceParams?.openid === getOpenid()) {
        if (["disk", "activities", "square"].includes(path_uuid)) {
          goPath({ path: menu.path, title: menu.title, query, path_uuid }, index);

        } else {
          goPath({ query: {
            ...query,
            jumpPath: '/workBenchIndex/workBenchHome',
uuid: path_uuid,
name: filterImg(path_uuid).name,
          },
           path: '/workBenchIndex/workBenchHome',
title: menu.title,
path_uuid }, index);
        }
      } else if (secondInstanceParams?.openid) {
        LynkerSDK.ipcRenderer.invoke("global-message-plugin", {
          theme: "warning",
          content: "当前账号与管理端账号不一致，无法打开",
          duration: 3000,
          zIndex: 9999999999999,
          offset: ["0", "30"],
        });
      }
    }
  }
};


LynkerSDK.ipcRenderer.removeAllListeners("go-path");
LynkerSDK.ipcRenderer.on('go-path', (e, item) => {
  const menu = JSON.parse(item);
  goPath(menu, 0);
});
LynkerSDK.ipcRenderer.on('menubar-toggle', (e, cv) => {
  collapsed.value = cv;
  setSize();

  // // 如果是伸展
  // if (!cv) {
  //   // 菜单移动比较慢，做一个延时
  //   setTimeout(() => {
  //     setSize();
  //   }, 200);
  // } else {
  //   // 收缩不需要设置延时
  //   setSize();
  // }
});

LynkerSDK.ipcRenderer.removeAllListeners("on-second-instance-path");
LynkerSDK.ipcRenderer.on('on-second-instance-path', (event, value) => {
  const query = value.query || {};
  const module = value.module;
  const path = value.path;
  const index = routers.value.findIndex((v) => v.path === path);
  if (index > -1) {
    const menu = routers.value[index];
    if (menu) {
      const path_uuid = ['store', 'culture-travel','ad-lk', 'external_app'].includes(module) ? 'workBench' : allRouters?.find((v) => v.path === path)?.path_uuid;
      console.log("value?.openid", value?.openid);
      if (!value?.openid) return;
      if (value?.openid === getOpenid()) {
        if (["disk", "activities", "square"].includes(path_uuid)) {
          goPath({ path: menu.path, title: menu.title, query, path_uuid }, index);

        } else {
          goPath({ query: {
            ...query,
            jumpPath: '/workBenchIndex/workBenchHome',
uuid: path_uuid,
name: filterImg(path_uuid).name,
          },
           path: '/workBenchIndex/workBenchHome',
title: menu.title,
path_uuid }, index);
        }
      } else {
        LynkerSDK.ipcRenderer.invoke("global-message-plugin", {
          theme: "warning",
          content: "当前账号与管理端账号不一致，无法打开",
          duration: 3000,
          zIndex: 9999999999999,
          offset: ["0", "30"],
        });
      }
    }
  }
});

const approveCenterCountFn = async () => {
  const res = await approveForeignCount();
  if (res.data.code === 0) {
    // approvalStore.setApproveCenterCountData(approveCenterCountData.value.pending_approval);
    approvalStore.approveCenterCountData = res.data.data.pending_approval;
  }
  const res1 = await approveCenterCount();
  if (res1.data?.code === 0) {
    // approvalStore.setApproveCenterCountData(approveCenterCountData.value.pending_approval);
    approvalStore.pendingApprovalCountData = res1.data.data.pending_approval;
  }
  console.log(res.data.data, "approveCenterCountFn打印");
};

const nicheCountFn = async () => {
  const res = await businessCount();
  if (res.data.code === 0) {
    // approvalStore.updateNew = +new Date();
    console.log("nicheCountFnLeftBar.vue");
    approvalStore.nicheCountData = res.data.data.audit;
    getWorkshopMessageInfo();
  }
};

const squareStore = useSquareStore();
const getSquareCount = async () => {
  squareStore.fetchNewsStats();
  await squareStore.getSquaresList();
};
</script>

<style lang="less" scoped>
.left-bar:hover {
  .show-menu {
    display: block;
  }
}

.menubarIconShowHoverBox{
  position: absolute;
  top: 0;
  right: 0;
  width: 8px;
  height: calc(100vh - 48px);
}

.left-bar {
  border-right: 1px solid #e5e5e5;
  position: relative;
  // transition: all .28s ;
  z-index: 3000;
  :deep(.t-default-menu) {
    transition: none !important;
  }
  .show-menu {
    position: absolute;
    top: 50%;
    right: 0px;
    display: none;
    cursor: pointer;
  }
  .menu-icons {
    width: 32px !important;
    height: 32px !important;
  }
  :deep(.menus-title) {
    width: 100%;
    line-height: 20px;

    text-align: center;
  }
  .menumoreicon {
    width: 32px !important;
    height: 32px !important;
    padding: 3px;
  }
  .narrow-bar {
    display: flex;
    flex-wrap: wrap;
    text-align: center;
    font-size: 12px;
    text-align: center;
    padding: 0 !important;
    margin: 4px 0 !important;
    // padding: 6px 0 4px !important;
    width: 56px;
    height: 56px !important;
    &.more-menu {
      svg {
        width: 32px !important;
        height: 32px !important;
        padding: 3px;
        margin-right: 0;
      }
      justify-content: center;
    }
    &:first-child {
      margin-top: 0 !important;
    }
    .menu-icons {
      margin: 0 auto;
      width: 32px !important;
      height: 32px !important;
      // padding: 6px 7px 6px 5px;
    }
    &.t-menu__item {
      font-size: 12px;
      width: 100%;
      height: 60px !important;
      margin: 0 0 12px 0 !important;
    }
    :deep(.menus-title) {
      width: 100%;
      line-height: 20px;
      text-align: center;
    }
    :deep(.t-menu__content) {
      margin: 0 !important;
      font-size: 12px;

      width: 100%;
      text-align: center;
    }
  }
  .t-menu__item {
    font-size: 14px;
    width: 100%;
    height: 48px !important;
    padding: 0 8px 0 8px !important;
    margin: 0 0 8px 0 !important;
  }
  .left-menu {
    //background: #f1f2f5;
    background: linear-gradient(
        0deg,
        var(--bg-kyy-color-bg-software-foucs, #272b4f) 0%,
        var(--bg-kyy-color-bg-software-foucs, #272b4f) 100%
      ),
      var(--bg-kyy-color-bg-software-lose-foucs, rgba(20, 26, 66, 0.8));
    :deep(.t-menu) {
      padding: 12px 8px;
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    &.active {
      :deep(.t-menu) {
        align-items: flex-start;
      }
    }
  }
  :deep(.t-menu__content) {
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: 400;
    color: var(--kyy_color_sidebar_item_text_default, rgba(255, 255, 255, 0.7));
    font-family: PingFang SC;
  }

  :deep(.t-badge--circle) {
    display: flex;
    width: 16px;
    height: 16px;
    justify-content: center;
    align-items: center;
    color: #fff;
    font-size: 12px;
    padding: 0 !important;
    border-radius: var(--kyy_radius_badge_full, 999px);
    background: var(--kyy_color_badge_bg, #FF4AA1) !important;
  }
  :deep(.t-badge.width-auto .t-badge--circle) {
    width: auto;
    padding: 0 6px !important;
  }

  :deep(.t-menu__item) {
    border-radius: 8px;
  }
  :deep(.t-menu__item:hover:not(.t-is-active)) {
    background: var(--kyy_color_sidebar_item_bg_hover, rgba(255, 255, 255, 0.16)) !important;
  }
  :deep(.t-menu__item.t-is-active),
  :deep(.t-menu__item:active),
  :deep(.t-menu__item:visited) {
    background: var(--brand-7-normal, #4d5eff);
    .t-menu__content {
      color: var(--kyy_color_sidebar_item_text_active, #fff);
      font-family: PingFang SC;
    }
  }
}
.show-menu {
  position: absolute;
  top: 50%;
  right: 0px;
  display: none;
  transform: translateY(-50%);
  cursor: pointer;
  z-index: 9999;
}

.more-menu.t-menu__item {
  &.active {
    color: #fff;
    border-radius: 8px;
    background: #4D5EFF !important;
    &:hover {
      background: #4D5EFF !important;
    }
  }
}
.redpoint {
  position: absolute;
  right: 24px;
  top: 8px;
  z-index: 999 !important;
  :deep(.t-badge--dot) {
    width: 10px;
    height: 10px;
    box-sizing: border-box;
    border: 1px solid #fff;
  }
}
</style>

