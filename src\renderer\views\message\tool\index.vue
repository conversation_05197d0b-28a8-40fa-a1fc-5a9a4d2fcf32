<template>
  <!-- 专属名称续期 -->
  <exclusiveSetting :inWin="true" show-in-attached-element @close-drawer="store.hideChatDialog" v-if="store.dialogInfo?.type === 'exclusive-renew'" ref="exclusiveSettingRef" :activeAccount="activeAccount"></exclusiveSetting>
  <chat-popularize-post v-if="store.dialogInfo?.type === 'popularize-post'" :msg="store.dialogInfo?.msg" />
  <chat-square v-if="store.dialogInfo?.type === 'square-helper'" :msg="store.dialogInfo?.msg" />
  <chat-merged-detail v-if="store.dialogInfo?.type === 'merged-msg-detail'" :msg="store.dialogInfo?.msg"
    @close="store.hideChatDialog" />
  <ZhiXingRemindRight v-if="store.zhixingRemindVisible" :key="store.dialogInfo.msg?.messageUId"
    :openid="store.zhixingRemindId" is-todo-popup topopenid="remind" from-chat />

  <ZhiXingNoteRight v-if="store.zhixingNoteVisible" :key="store.dialogInfo.msg?.messageUId"
    :note-openid="store.dialogInfo.msg?.contentExtra?.data?.openid" lock="open" from-chat
    :data-from-chat="store.zhixingNoteData" />

  <!-- 审批助手详情 -->
  <approval-details-drawer-component v-if="store.dialogInfo?.type === 'approve-helper'" :key="store.approvalRefreshKey"
    :visible="true" :approval-detail-item="store.approveData" :auto-action="store.approvAutoAction"
    :show-in-attached-element="true" @visible-fn="store.hideChatDialog" @status="updateMsgStatus" />

  <!-- 审批分享详情 -->
  <approval-details-drawer-component v-if="store.dialogInfo?.type === 'approve'" :visible="true"
    :approval-detail-item="store.approveData" :auto-action="store.approvAutoAction" @visible-fn="store.hideChatDialog"
    @status="updateMsgStatus" />

  <!-- 审批评论详情  -->
  <approval-details-drawer-component v-if="store.dialogInfo?.type === 'approve-comment'" :visible="true"
    :show-in-attached-element="true" :approval-detail-item="store.approveData" @visible-fn="store.hideChatDialog" />

  <!-- 广场分享 - 朋友圈 -->
  <PostDetail v-if="store.dialogInfo?.type === 'square-moment'"
    :id="store.dialogInfo?.msg?.contentExtra?.data?.post?.id" :model-value="true" is-preview class="mt-48" from-outer
    top="68px" @update:model-value="store.hideChatDialog" />
  <!-- 广场分享 - 预览 -->
  <PostPreview v-if="store.dialogInfo?.type === 'square-preview'" ref="squarePreviewRef" :model-value="true"
    @close="store.hideChatDialog" />

  <orderDrawer v-if="store.msgOrderDetailVisible" ref="msgOrderRef" :visible="true" show-in-attached-element
    @close-drawer="store.hideChatDialog" />
  <vcardDrawer :inWin="true" v-if="store.msgVcardDetailVisible" class="tool-vcard-drawer" ref="vcardDrawerRef"
    :visible="true" show-in-attached-element @close-drawer="store.hideChatDialog" />
  <sendVcardDrawer :inWin="true" v-if="store.msgSendVcardDetailVisible" class="tool-vcard-drawer"
    ref="sendVcardDrawerRef" :visible="true" show-in-attached-element @close-drawer="store.hideChatDialog" />
  <InvoiceDrawer v-if="store.msgInvoiceVisible || store.msgInvoiceRefuseVisible" ref="InvoiceDrawerRef"
    show-in-attached-element @close-drawer="store.hideChatDialog" />

  <ComboDrawer v-if="store.comboVisible" :model-value="true" :square-id="store.comboSquareId"
    :group-id="store.comboGroupId" @update:model-value="store.hideChatDialog" />

  <!-- inviteCode -->
  <OpenSquare v-if="store.squareInviteVisible" show-in-attached-element :model-value="store.squareInviteVisible"
    :invite-code="store.squareInviteCode" @update:model-value="store.hideChatDialog" @success="store.hideChatDialog" />

  <Prepayment v-if="store.albumExpireVisible" ref="albumExpireRef" show-in-attached-element
    @close="store.hideChatDialog" />

  <BizDetail v-if="store.bizOpportunityHelperDetailVisible" ref="bizDetailRef" @detail-close="store.hideChatDialog"
    @operate-succ="store.updateBizOpportunityHelperMsg" />
  <BizDetailReadonly v-if="store.bizOpportunityDetailVisible" ref="bizDetailReadonlyRef" :im="true"
    @close="store.hideChatDialog" />
  <!-- v-if="store" -->
  <link-file v-if="store.dialogInfo?.type === 'cloud-disk-link'" :key="store.dialogInfo?.link"
    :link="store.dialogInfo?.link" :visible="true" @update:visible="store.hideChatDialog" />
  <ServiceContentDetailDrawer v-if="showServiceContent" ref="serviceContentDetailDrawerRef" type="inTop"
    @on-close="closeServiceContent" />
  <ServiceReportDetailDrawer v-if="showServiceReport" ref="serviceReportDetailDrawerRef" type="inTop"
    @on-close="closeServiceReport" />
  <ServiceViolationDetailDrawer v-if="showServiceViolation" ref="serviceViolationDetailDrawerRef" type="inTop"
    @on-close="closeServiceViolation" />
  <ServiceAppealDetailDrawer v-if="showServiceAppealDetail" ref="serviceAppealDetailDrawerRef" type="inTop"
    @on-close="closeServiceAppealDetail" />
  <ServiceAppealDrawer v-if="showServiceAppeal" ref="serviceAppealDrawerRef" type="inTop"
    @on-close="closeServiceAppeal" />
  <!-- 荣誉榜安全审核 -->
  <SecurityVerification v-if="showSecurityVerificationHonor" ref="SecurityVerificationHonorRef"
    :data="store.dialogInfo.msg?.contentExtra?.data?.extend" :config="{
      showOverlay: false,
      closeOnEscKeydown: false,
      closeOnOverlayClick: false,
      showInAttachedElement: true,
      verificationType,
    }" from="IM" @close-drawer="closeSecurityVerificationHonor" />
  <PreviewRelease v-if="showPreviewReleaseHonor" :visible="true" type="detail"
    :data="store.dialogInfo.msg?.contentExtra?.data?.extend" @close-drawer="closePreviewReleaseHonor" />
  <!-- 党建审核详情 -->
  <IMReviewIndex v-if="store.dialogInfo?.type === 'pb-detail'"
    :approval-visible="store.dialogInfo?.type === 'pb-detail'" :dialog-info="store.dialogInfo"
    @on-close="store.hideChatDialog" />
  <!-- 平台风采审核详情 -->
  <IMReviewFcIndex v-if="store.dialogInfo?.type === 'fc-detail'"
    :approval-visible="store.dialogInfo?.type === 'fc-detail'" :dialog-info="store.dialogInfo"
    @on-close="store.hideChatDialog" />
  <AboutDialog v-if="previewPublishVertifyVisible" v-model="previewPublishVertifyVisible" type="timeline"
    :data="aboutInfo"></AboutDialog>

  <orderDrawer v-if="store.dialogInfo?.type === 'partner-order-info'" ref="partnerOrderInfoRef" :visible="true"
    show-in-attached-element @close-drawer="store.hideChatDialog" />
  <ImRefundDrawer v-if="store.dialogInfo?.type === 'partner-refund-fill'"
    v-model:ImRefundDrawerFlag="ImRefundDrawerFlag" show-in-attached-element showType="detail" type="inTop"
    :order-info="ImRefundDrawerInfo" @close-drawer="store.hideChatDialog" />

  <consultDrawer v-if="store.dialogInfo?.type === 'consult-info'" ref="consultInfoRef" :visible="true"
    show-in-attached-element @close-drawer="store.hideChatDialog" />
  <consultOrderDrawer v-if="store.dialogInfo?.type === 'consult-order-info'" ref="consultOrderInfoRef" :visible="true"
    show-in-attached-element @close-drawer="store.hideChatDialog" />
  <AdDrawer v-if="store.dialogInfo?.type === 'ad-info'" ref="adInfo" :visible="true" show-in-attached-element
    @close-drawer="store.hideChatDialog" />
  <digitalApplyDrawer v-if="store.dialogInfo?.type === 'digital-apply-info'" ref="digitalApplyInfo" :visible="true"
    show-in-attached-element @close-drawer="store.hideChatDialog" />
  <WithdrawalDetailDrawer v-if="store.dialogInfo?.type === 'receipt-withdraw-detail'" ref="withdrawalDetailRef" :visible="true"
    show-in-attached-element @close-drawer="store.hideChatDialog" />
  <JoinDrawer v-if="store.dialogInfo?.type === 'activity-join-info'" ref="activityJoinInfo" :visible="true"
    :type="JoinDrawerType.IM" show-in-attached-element @close-drawer="store.hideChatDialog" />
  <PolicyReviewResult v-if="store.dialogInfo?.type === 'policyExpross'" :dialog-info="store.dialogInfo"
    @on-close="store.hideChatDialog" />
  <ShopApplyDetail v-if="store.dialogInfo?.type === 'shop-apply'" :team-id="store.dialogInfo.msg.teamId"/>
  <ShopCategoryResult v-if="store.dialogInfo?.type === 'shop-category-apply'" :dialog-info="store.dialogInfo"
    @on-close="store.hideChatDialog" />
  <ShopBusinessesDetail v-if="store.dialogInfo?.type === 'shop-check-detail'" :dialog-info="store.dialogInfo"
    @on-close="store.hideChatDialog" />

  <NaasResult show-in-attached-element v-if="store.dialogInfo?.type === 'naas-result'" ref="naasResultRef" @on-close="store.hideChatDialog" />
  <CultureTourism show-in-attached-element v-if="store.dialogInfo?.type === 'culture-tourism'" :dialog-info="store.dialogInfo"  @on-close="store.hideChatDialog" />
  <cultureApply show-in-attached-element v-if="store.dialogInfo?.type === 'culture-apply'" :dialog-info="store.dialogInfo"  @on-close="store.hideChatDialog" />
</template>

<script setup lang="ts">
  import ApprovalDetailsDrawerComponent from "@renderer/views/approve/approve_home/components/approval_details_drawer_component.vue";
  import orderDrawer from "@renderer/components/order/components/orderDrawer.vue";
  import sendVcardDrawer from "@/windows/vcard/components/sendVcardDrawer.vue";
  import vcardDrawer from "@/windows/vcard/components/vcardDrawer.vue";

  import consultDrawer from "@renderer/components/order/components/consult/consult.vue";
  import consultOrderDrawer from "@renderer/components/order/components/consult/consultOrder.vue";
  import InvoiceDrawer from "@renderer/components/order/components/InvoiceDrawer.vue";
  import AdDrawer from "@renderer/views/workBench/advertSing/AdDetailsModal.vue";
  import digitalApplyDrawer from "@renderer/views/digital-platform/modal/im-join-detail-drawer.vue";

  import ImRefundDrawer from "@renderer/components/order/components/ImRefundDrawer.vue";
  import JoinDrawer from "@renderer/views/activity/manage/sign/components/JoinDrawer.vue";

  // 专属名称续期exclusive-renew
  import exclusiveSetting from '@renderer/views/digital-platform/modal/exclusiveSetting.vue';

  import NaasResult from '@renderer/views/message/chat/msgTypeContent/AppSecretary/naas/Result.vue';

  // 提现详情
  import WithdrawalDetailDrawer from '@renderer/views/workBench/receipt/settlement-fund/WithdrawalDetail.vue';
  import BizDetail from "@renderer/components/business/examine/detail.vue";
  // import BizDetailReadonly from "@renderer/components/business/manage/detailsReadonly.vue";
  import BizDetailReadonly from "@renderer/views/square/niche/components/detail.vue";

  import ZhiXingRemindRight from "@renderer/views/zhixing/todo/right.vue";
  import ZhiXingNoteRight from "@renderer/views/zhixing/note/right.vue";

  import linkFile from "@renderer/views/clouddisk/clouddiskhome/components/linkFile.vue";
  import ServiceContentDetailDrawer from "@renderer/views/square/service/modal/service-content-detail-drawer.vue";
  import ServiceReportDetailDrawer from "@renderer/views/square/service/modal/service-report-detail-drawer.vue";
  import ServiceViolationDetailDrawer from "@renderer/views/square/service/modal/service-violation-detail-drawer.vue";
  import ServiceAppealDetailDrawer from "@renderer/views/square/service/modal/service-appeal-detail-drawer.vue";
  import ServiceAppealDrawer from "@renderer/views/square/service/modal/sub-violation-modal.vue";
  import { getGrowthDetailById, getFrontGrowthDetailById } from "@renderer/api/workBench/growth";
  import { getIntroduceDetailById, getFrontIntroduceDetailById } from "@renderer/api/workBench/introduce";


  import { ref, watch, nextTick, computed, defineAsyncComponent, reactive } from "vue";
  import { PackageType } from "@renderer/views/message/chat/common/constant";
  import {
    // 申诉详情
    getHelperViolationDetailAxios,
  } from "@renderer/api/service/index";
  import SecurityVerification from "@renderer/views/workBench/components/SecurityVerification.vue";
  import PreviewRelease from '@renderer/views/workBench/components/previewRelease.vue';
  import AboutDialog from '@renderer/views/workBench/components/AboutDialog.vue';
  import { useMessageStore } from '@renderer/views/message/service/store';
  import { useChatExtendStore } from "../service/extend";
  import ComboDrawer from "@/views/square/components/ringkol-circle/components/ComboDrawer.vue";

  import PostDetail from "@/views/square/components/post/PostDetail.vue";
  import PostPreview from "@/views/square/components/im/shared/PostPreview.vue";

  import OpenSquare from "@/views/square/components/OpenSquare.vue";
  import Prepayment from "@/views/square/phone-album/components/combo/prepayment.vue";

  import ChatSquare from "./ChatSquare.vue";
  import ChatMergedDetail from "./ChatMergedDetail.vue";
  import ChatPopularizePost from "./ChatPopularizePost.vue"

  import { getAppApprovalStatus, getAppShareApprovalStatus } from "../service/extend/statusUtils";
  import { JoinDrawerType } from '@renderer/views/activity/manage/sign/constant';
  import { useImToolStore } from './service/tool';
  import ShopApplyDetail from './shopDetail/IMStoreApplyDetail.vue'
  import ShopCategoryResult from './shopDetail/categoryApply.vue'
  import CultureTourism from './drawerWrap/IMReviewResult.vue'
  import cultureApply from './drawerWrap/cultureApply.vue'

  const IMReviewIndex = defineAsyncComponent(() => import('@renderer/components/pb/imReview/reviewer/index.vue'));
  const IMReviewFcIndex = defineAsyncComponent(() => import('@renderer/components/fengcai/imReview/reviewer/index.vue'));
  const PolicyReviewResult = defineAsyncComponent(() => import('@/views/policy-express/im-review/IMReviewResult.vue'));
  const ShopBusinessesDetail = defineAsyncComponent(() => import('./shopDetail/checkDetail.vue'));


  const store = useChatExtendStore();
  const toolStore = useImToolStore();
  const squarePreviewRef = ref();
  const msgOrderRef = ref();
  const InvoiceDrawerRef = ref(null);

  const partnerOrderInfoRef = ref(null);
  const consultOrderInfoRef = ref(null);
  const adInfo = ref(null);
  const digitalApplyInfo = ref(null);
  const activityJoinInfo = ref(null);
  const consultInfoRef = ref(null);
  const refundDrawerRef = ref(null);
  const ImRefundDrawerFlag = ref(false);
  const ImRefundDrawerInfo = ref({
    region: "CN",
    refund_id: 40
  });

  const verificationType = ref('');

  const bizDetailRef = ref(null);

  const albumExpireRef = ref(null);
  const naasResultRef = ref(null);
  // 提现详情
  const withdrawalDetailRef = ref(null);

  const aboutInfo = ref();
  const serviceContentDetailDrawerRef = ref(null);
  const serviceReportDetailDrawerRef = ref(null);
  const serviceViolationDetailDrawerRef = ref(null);
  const serviceAppealDetailDrawerRef = ref(null);
  const serviceAppealDrawerRef = ref(null);
  const showServiceContent = ref(false);
  const showServiceReport = ref(false);
  const vcardDrawerRef = ref(null);
  const sendVcardDrawerRef = ref(null);
  const showServiceViolation = ref(false);
  const showServiceAppealDetail = ref(false);
  const showServiceAppeal = ref(false);
  const serviceAppealMsg = ref(null);
  const SecurityVerificationHonorRef = ref(null);
  const showPreviewReleaseHonor = ref(false);
  const showSecurityVerificationHonor = ref(false);
  const previewPublishVertifyVisible = ref(false);
  const exclusiveSettingRef = ref(false); // 专属名称续期
  const activeAccount = reactive({
    teamLogo: '',
    teamFullName: '',
    teamId: ''
  })


  // watch(() => store.bizOpportunityHelperDetailVisible, (val) => {
  //     if (val) {
  //       nextTick(() => {
  //         bizDetailRef.value.deOpen(
  //           store.bizOpportunityHelperId.id,
  //           store.bizOpportunityHelperId.team_id
  //         );
  //       });
  //     }
  //   }, {immediate: true});

  const bizDetailReadonlyRef = ref(null);
  // watch(() => store.bizOpportunityDetailVisible, (val) => {
  //     if (val) {
  //       nextTick(() => {
  //         bizDetailReadonlyRef.value.detailsOpen(store.bizOpportunityId);
  //       });
  //     }
  //   }, {immediate: true});
  const showShopCheckDetail = ref(true);
  watch(() => showShopCheckDetail.value, (val) => {
    if(!val){
      store.hideChatDialog();
    }
  })
  watch(
    () => store.dialogInfo,
    (val, old) => {
      console.log("🚀 ~ val: data", val, val?.msg?.contentExtra?.data);
      const data = val?.msg?.contentExtra?.data;
      const extend = val?.extend;
      if (val && val.type === "vcard-chat-dialog") {
        nextTick(() => {
          vcardDrawerRef.value?.setInfo(val.msg);
        });
      } else if (val && val.type === "send-vcard-chat-dialog") {
        sendVcardDrawerRef.value?.setInfo(val.msg);

      } else if (val && val.type === "square-preview") {
        toolStore.msgDetailOption.zIndex = -1;
        nextTick(() => {
          squarePreviewRef.value?.open(data.publishId);
        });
      } else if (val && val.type === "order") {
        nextTick(() => {
          const openData = {
            sn: data?.extend?.order_sn,
            invoiceId: data?.extend?.invoice_id,
            flag: "开票信息",
            teamId: data?.extend?.team_id
          };
          msgOrderRef.value.openWin(openData);
        });
      } else if (val && val.type === "invoice") {
        nextTick(() => {
          const openData = {
            sn: data?.extend?.order_sn,
            invoiceId: data?.extend?.invoice_id,
            flag: "查看发票",
          };
          InvoiceDrawerRef.value.openWin(openData);
        });
      }
      else if (val && val.type === "partner-order-info") {
        nextTick(() => {
          const openData = {
            sn: data?.extend?.order_sn,
            teamId: data?.extend?.team_id
          };
          partnerOrderInfoRef.value.openWin(openData);
        });
      }
      else if (val && val.type === "naas-result") {
        nextTick(() => {
          naasResultRef.value?.open(data?.extend || '');
        })
      }
      else if (val && val.type === "receipt-withdraw-detail") {
        nextTick(() => {
          withdrawalDetailRef.value.open(data?.extend?.extract_sn, data?.extend?.team_id);
        });
      }
      else if (val && val.type === "consult-order-info") {
        nextTick(() => {
          const openData = {
            ...data,
            sn: data?.data?.info?.sn,
            ...val.extend
          };
          consultOrderInfoRef.value.openWin(openData);
        });
      }
      else if (val && val.type === "ad-info") {
        nextTick(() => {
          const openData = {
            id: data?.extend?.ad_id,
            teamId: data?.extend?.team_id,
          };
          adInfo.value.onOpen(openData);
        });
      }
      else if (val && val.type === "digital-apply-info") {
        nextTick(() => {
          const openData = {
            id: data?.extend?.id,
            tag: data?.extend?.platform_uuid,
            teamId: data?.extend?.teamId,
            isCard: 1
          };
          digitalApplyInfo.value.onOpen(openData);
        });
      }
      else if (val && val.type === "activity-join-info") {
        nextTick(() => {
          console.error('extend', extend);

          activityJoinInfo.value.open(extend);
        });
      }
      else if (val && val.type === "consult-info") {
        nextTick(() => {
          console.log('execise', data)
          let sn = '';
          if(data?.type === PackageType.ExclusiveName) { // 专属名称套餐
            sn = data?.data?.consult?.sn;
          } else {
            sn = data?.type === PackageType.Square ? data?.data?.consultOrderDetail?.sn : data?.data?.consult_data?.sn;
          }

          const openData = {
            ...data,
            sn,
            ...val?.extend
          };

          consultInfoRef.value.openWin(openData);
        });
      } else if(val && val.type === "exclusive-renew") { // 专属名称续期弹窗
        activeAccount.teamLogo = data?.header?.team?.logo;
        activeAccount.teamFullName = data?.header?.team?.name;
        activeAccount.teamId = data?.extend?.team_id;

        nextTick(()=> {
          exclusiveSettingRef.value?.onOpen( {etype: 2});
        })
      }
      else if (val && val.type === "partner-refund-fill") {
        nextTick(() => {
          ImRefundDrawerFlag.value = true;
          ImRefundDrawerInfo.value = {
            region: "CN",
            refund_id: data?.extend?.refund_id
          };
        });
      } else if (val && val.type === "partner-refund-info") {
        nextTick(() => {
          refundDrawerRef.value.openWin(data?.extend?.refund_id);
        });
      } else if (val && val.type === "invoice-refuse") {
        nextTick(() => {
          const openData = {
            sn: data?.extend?.order_sn,
            invoiceId: data?.extend?.invoice_id,
            flag: "拒绝开票原因",
          };
          InvoiceDrawerRef.value.openApplyInvoiceDialog(openData);
        });
      } else if (val && val.type === "service-content") {
        const info = val.msg;
        showServiceContent.value = true;
        nextTick(() => {
          serviceContentDetailDrawerRef.value.onOpen(info);
        });
      } else if (val && val.type === "service-report") {
        const info = val.msg;
        console.log(
          "serviceReportDetailDrawerRef.....",
          serviceReportDetailDrawerRef
        );
        showServiceReport.value = true;
        nextTick(() => {
          serviceReportDetailDrawerRef.value.onOpen(info);
        });
      } else if (val && val.type === "service-violation") {
        const info = val.msg;
        showServiceViolation.value = true;
        nextTick(() => {
          console.log("serviceViolationDetailDrawerRef.value", info);
          serviceViolationDetailDrawerRef.value.onOpen(info);
        });
      } else if (val && val.type === "service-appeal") {
        const info = val.msg;
        showServiceAppeal.value = true;
        console.log('service-appeal===', info, info.msg);
        serviceAppealMsg.value = info.msg;
        nextTick(() => {
          serviceAppealDrawerRef.value.onOpen({ id: info.id });
        });
      } else if (val && val.type === "service-appeal-detail") {
        const info = val.msg;
        showServiceAppealDetail.value = true;
        nextTick(() => {
          serviceAppealDetailDrawerRef.value.onOpen(info);
        });
      } else if (val && val.type === "album-expire") {
        nextTick(() => {
          console.log("albumExpireRef.value", albumExpireRef.value);
          albumExpireRef.value.openByMsg(val.msg?.contentExtra?.data?.extend);
        });
      } else if (val && val.type === "biz-opportunity") {
        // 已脱离im直接打开独立窗口详情
        // ipcRenderer.invoke(
        //   "niche-read",
        //   JSON.stringify({
        //     uuid: data?.uuid || data?.extend?.uuid,
        //     teamId: data?.extend?.team_id,
        //     from: "im",
        //   }),
        // );
        // nextTick(() => {
        //   bizDetailReadonlyRef.value.detailsOpen(store.bizOpportunityId);
        // });
      } else if (val && val.type === "biz-opportunity-helper") {
        // 已脱离im直接打开独立窗口详情
        // ipcRenderer.invoke(
        //   "niche-examine",
        //   JSON.stringify({
        //     id: data?.extend?.id,
        //     teamId: data?.extend?.team_id,
        //     from: "im",
        //   }),
        // );
        // nextTick(() => {
        //   bizDetailRef.value.deOpen(
        //     store.bizOpportunityHelperId.id,
        //     store.bizOpportunityHelperId.team_id
        //   );
        // });
      } else if (val && val.type === "security-verification-honor") {
        verificationType.value = 'approvalConfirmHonor';
        showSecurityVerificationHonor.value = true;
        nextTick(() => {
          SecurityVerificationHonorRef.value?.onOpen(
            'approvalVisible',
            true
          );
        });
      } else if (val && val.type === "security-verification-process") {
        verificationType.value = 'approvalConfirmProcess';
        showSecurityVerificationHonor.value = true;
        nextTick(() => {
          SecurityVerificationHonorRef.value?.onOpen(
            'approvalVisible',
            true
          );
        });
      } else if (val && val.type === 'security-verification-introduce') {
        verificationType.value = 'approvalConfirmIntroduce';
        showSecurityVerificationHonor.value = true;
        nextTick(() => {
          SecurityVerificationHonorRef.value?.onOpen(
            'approvalVisible',
            true
          );
        });
      } else if (val && val.type === "security-verification-notice") {
        verificationType.value = 'approvalConfirmNotice';
        showSecurityVerificationHonor.value = true;
        nextTick(() => {
          SecurityVerificationHonorRef.value?.onOpen(
            'approvalVisible',
            true
          );
        });
      } else if (val && val.type === "preview-release-honor") {
        // 通过消息卡片中单独引入触发，这里暂已废弃，先屏蔽保留
        // nextTick(() => {
        //   showPreviewReleaseHonor.value = true;
        // });
      } else if (val && val.type === 'preview-release-process') {
        nextTick(async () => {
          const res = await getFrontGrowthDetailById(data.extend.id, data.extend.team_id);
          aboutInfo.value = res.data.data;
          previewPublishVertifyVisible.value = true;
        });
      } else if (val && val.type === 'preview-release-introduce') {
        nextTick(async () => {
          const res = await getFrontIntroduceDetailById(data.extend.id, data.extend.team_id);
          aboutInfo.value = res.data.data;
          previewPublishVertifyVisible.value = true;
        });
      }
    },
    { immediate: true }
  );

  const closeServiceContent = () => {
    store.hideChatDialog();
    showServiceContent.value = false;
  };

  const closeServiceReport = () => {
    store.hideChatDialog();
    showServiceReport.value = false;
  };

  const closeServiceViolation = () => {
    store.hideChatDialog();
    showServiceViolation.value = false;
  };

  const closeServiceAppeal = () => {
    checkAppealInfo(serviceAppealMsg.value);
    store.hideChatDialog();
    showServiceAppeal.value = false;
  };

  const closeServiceAppealDetail = () => {
    store.hideChatDialog();
    showServiceAppealDetail.value = false;
  };

  const closePreviewReleaseHonor = () => {
    store.hideChatDialog();
    showPreviewReleaseHonor.value = false;
  };

  const closeSecurityVerificationHonor = () => {
    store.hideChatDialog();
    showSecurityVerificationHonor.value = false;
  };

  // 查询申诉信息
  const checkAppealInfo = async (msg: MessageToSave) => {
    console.log('tool, checkAppealInfo:', msg);
    const data = msg.contentExtra?.data;
    const id = data?.extend.id;
    const res = await getHelperViolationDetailAxios(id);
    if (res.status === 200 && data?.extend?.status !== res.data.data.status) {
      useChatExtendStore().commonUpdateMsgStatus(
        msg,
        { ...data.extend, status: res.data.data.status },
        "extend"
      );
    }
  };

  const updateMsgStatus = (skip_approve_id?: number | string) => {
    console.log("审批更新审批消息状态", store.dialogInfo, skip_approve_id);
    const msgType = store.dialogInfo?.msg?.contentExtra?.contentType;
    if (msgType === 'APP_APPROVAL') {
      if (skip_approve_id) {
        if (store.dialogInfo?.msg?.contentExtra?.data?.extend?.approval_id === skip_approve_id) {
          getAppApprovalStatus(store.dialogInfo.msg, true);
        } else {
          const msgs = useMessageStore().chatingMessages.find(val => val.msg?.contentExtra?.data?.extend?.approval_id === skip_approve_id);
          console.log('====>审批更新', msgs.msg);
          msgs && getAppApprovalStatus(msgs.msg, true);
        }
      } else {
        getAppApprovalStatus(store.dialogInfo.msg, true);

      }
    } else if (msgType === 'approve') {
      getAppShareApprovalStatus(store.dialogInfo.msg);
    }
  };
</script>

<style lang="less">
  @import "./style/style.less";

  .combo-drawer {
    position: absolute;
  }
</style>
