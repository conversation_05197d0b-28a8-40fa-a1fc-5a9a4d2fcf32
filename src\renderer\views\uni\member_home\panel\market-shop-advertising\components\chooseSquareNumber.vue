<template>
  <div>
    <t-dialog v-model:visible="visible"  @close="closeWin()"  :footer="false" :header="t('ad.xzgch')" width="460">
      <!-- <template #header>
        <div style="display: flex; align-items: center; width: 100%; justify-content: space-between">
          <div></div>
          <img
            style="width: 24px; cursor: pointer; height: 24px"
            src="@/assets/<EMAIL>"
            @click="closeWin()"
          />
        </div>
      </template> -->
      <div class="box">
        <div class="head-box" @keyup.enter="fetchData()">
          <t-input
          
            v-model="searchValue"
            :placeholder="t('clouddisk.searchKeywords')"
            clearable 
            style="padding-right: 8px"
            @compositionend="fetchData()"
            @bluer="fetchData()"
            @input="fetchData()"
          >
            <template #prefix-icon>
              <img style="width: 20px; height: 20px" src="@/assets/svg/icon_search.svg" />
            </template>
          </t-input>
          <t-button style="min-width: 80px" theme="primary" @click="fetchData()">搜索</t-button>
        </div>
        <div class="flexbox" ref="scrollContainer">
          <div v-for="(item, index) in displayedItems" @click="changeItem(item)" :key="index" class="gchbox">
            <div class="gchbox-left">
              <img v-if="item.avatar" class="gchbox-img" :src="item.avatar" />
              <avatar
                v-else-if="(item?.squareType||item?.square_type) === 'INDIVIDUAL'"
                avatarSize="48px"
                :image-url="item.avatar ?? ''"
                :user-name="item.name"
                :round-radius="true"
              />
              <img v-else class="gchbox-img" src="@/assets/svg/clouddisk/temaavatar.svg" />

            </div>
            <div class="sjbox-info-box">
              <div class="sjbox-info">
                <span class="star" v-if="(item?.squareType||item?.square_type) !== 'INDIVIDUAL'">
                  <iconpark-icon
                    v-if="(item?.squareType||item?.square_type) === SquareType.Enterprise"
                    name="iconenterprise"
                    class="star-icon"
                  ></iconpark-icon>
                  <iconpark-icon
                    v-else-if="(item?.squareType||item?.square_type) === SquareType.BusinessAssociation"
                    name="iconbusiness"
                    class="star-icon"
                  ></iconpark-icon>
                  <iconpark-icon
                    v-else-if="(item?.squareType||item?.square_type) === SquareType.IndividualBusiness"
                    name="iconindividual"
                    class="star-icon"
                  ></iconpark-icon>
                  <iconpark-icon
                    v-else-if="(item?.squareType||item?.square_type) === SquareType.Other"
                    name="iconother"
                    class="star-icon"
                  ></iconpark-icon>
                  <iconpark-icon
                    v-else-if="(item?.squareType||item?.square_type) === SquareType.Government"
                    name="icongov"
                    class="star-icon"
                  ></iconpark-icon>
                </span>
                <div class="gchbox-title" style="color: #1A2139;">{{ item.name }}</div>
              </div>
              <div class="gchbox-lable">广场号ID: {{ item?.pid ||item?.id}}</div>
              <div class="gchbox-tag">{{ item.teamName ? item.teamName : "个人广场号" }}</div>
            </div>
          </div>
          <img 
            v-if="!searchValue && displayedItems.length === 0"
            style="width: 250px;  margin: 190px auto 0"
            src="@/assets/member/Vector1.svg"
          />
          <div class="list-box empty" style="margin: 0 auto" v-if="searchValue && displayedItems.length === 0">
            <div>
              <img class="empty-img" src="@/assets/noApplyData.svg" alt="" />
            </div>
            <div class="font14">
              {{ t("zx.schedule.nosearch") }}
            </div>
          </div>
        </div>

        <!-- <div v-if="!items.length && !loading" class="no-results">
            没有找到匹配的结果。
          </div> -->
      </div>
    </t-dialog>
  </div>
</template>
<script setup lang="ts">
import { DialogPlugin, MessagePlugin } from "tdesign-vue-next";
import { useI18n } from "vue-i18n";
import { ref, onMounted, computed, watch, reactive, onUnmounted } from "vue";
import { useRoute } from "vue-router";
import { useDigitalPlatformStore } from "@renderer/views/digital-platform/store/digital-platform-store";
import { platform } from "@renderer/views/digital-platform/utils/constant";
import { searchSquare } from "@renderer/api/business/manage";
import { getMemberTeamID } from "@renderer/views/member/utils/auth";
import avatar from "@renderer/components/kyy-avatar/index.vue";
import { SquareType } from "@/api/square/enums";
import { getSrcThumbnail, getSrcLogo } from "@renderer/views/message/service/msgUtils";

const digitalPlatformStore = useDigitalPlatformStore();

const route = useRoute();

const searchValue = ref("");
const props = defineProps({
  platform: {
    type: String,
    default: "",
  },
});
const { t } = useI18n();

// 状态管理
const visible = ref(false);
const items = ref([]); // 存储所有搜索结果
const displayedItems = ref([]); // 当前显示的分页结果
const loading = ref(false); // 是否正在加载数据
const loadingMore = false; // 是否正在加载更多数据
const currentPage = ref(1); // 当前页码
const pageSize = ref(10); // 每页显示的条目数量

const platformCpt = computed(() => {
  return props.platform || route.query?.platform;
});
const emits = defineEmits(["changSquare"]);

const changeItem = (item) => {
  visible.value = false;
  emits("changSquare", item);
};
const currentTeamId = computed(() => {
  if (platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount?.teamId;
  } else if (platformCpt.value === platform.digitalWorkbench) {
    return route.query?.teamId || 0;
  } else {
    return getMemberTeamID();
  }
});
const total = ref(0);
// 获取数据的函数
const fetchData = async () => {
  console.log(currentTeamId, "触发这里");
  searchSquare(
    {
      keyword: searchValue.value,
      "page.number": currentPage.value,
      "page.size": pageSize.value,
      disable_ignore_self: true,
    },
    currentTeamId.value,
  ).then((res) => {
    console.log(res, "广场号数据");
    displayedItems.value = res.data.squares;
    total.value = res.data.total;
  });
};

// 加载更多的数据
const loadMore = async () => {
  if (displayedItems.value.length <= total.value) {
    currentPage.value += 1;
    searchSquare(
      {
        keyword: searchValue.value,
        "page.number": currentPage.value,
        "page.size": pageSize.value,
        disable_ignore_self: true,
      },
      currentTeamId.value,
    ).then((res) => {
      displayedItems.value.push(...res.data.squares);
      total.value = res.data.total;
    });
  }
};

// 监听滚动事件
const scrollContainer = ref(null);
watch(scrollContainer, async (container) => {
  if (!container) return;
  container.addEventListener("scroll", handleScroll);
});

onMounted(() => {
  fetchData();
});
const closeWin = () => {
  visible.value = false;
  searchValue.value = "";
  currentPage.value = 1;
  displayedItems.value = [];
};
// 处理滚动事件
const handleScroll = () => {
  const container = scrollContainer.value;
  if (container.scrollHeight - container.scrollTop === container.clientHeight) {
    loadMore();
  }
};

// 清理事件监听器
onUnmounted(() => {
  const container = scrollContainer.value;
  if (container) {
    container.removeEventListener("scroll", handleScroll);
  }
});

// 监听搜索值变化，重置分页
watch(searchValue, () => {
  currentPage.value = 1;
  displayedItems.value = [];
  fetchData();
});
const openWin = () => {
  searchValue.value=''
  visible.value = true;
  fetchData();
};
defineExpose({
  openWin,
});
const seachEnter = () => {};
</script>
<style lang="less" scoped>
/* 保持原有样式 */
.loading-more {
  display: flex;
  justify-content: center;
  margin: 16px 0;
}
::-webkit-scrollbar {
  width: 4px;
  background-color: transparent;
}
/*定义滚动条轨道 内阴影+圆角*/
::-webkit-scrollbar-track {
  background-color: transparent;
}
/*定义滑块 内阴影+圆角*/
::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background: var(--divider-kyy_color_divider_deep, #D5DBE4);
}
.no-results {
  text-align: center;
  color: #828da5;
  margin: 16px 0;
}
.sjbox {
  display: flex;
  padding: 12px;
  align-items: center;
  border: 1px solid transparent;
  width: 100%;

  gap: 12px;
  align-self: stretch;
  height: 145px;
  border-radius: 8px;
  background: var(--bg-kyy_color_bg_deep, #f5f8fe);
}
.head-box {
  display: flex;
  align-items: center;
  margin-top: 16px;
  margin-bottom: 12px;
}
.flexbox {
  display: flex;
    flex-wrap: wrap;
    gap: 12px;
    height: 436px;
    align-content: flex-start;
    overflow: auto;
}
.sjbox:hover {
  border: 1px solid var(--brand-kyy_color_brand_default, #4d5eff);
}
.sjbox-info-box {
  display: flex;
  align-items: center;
  gap: 4px;
  flex-wrap: wrap;
  .sjbox-info {
    display: flex;
    width: 100%;
    align-items: center;
    gap: 8px;
  }
  .tags {
    display: flex;
    height: 20px;
    min-height: 20px;
    max-height: 20px;
    padding: 0px 4px;
    justify-content: center;
    align-items: center;
    gap: 4px;
    border-radius: var(--kyy_radius_tag_s, 4px);
    background: var(--kyy_color_tag_bg_cyan, #e6f9f8);
    color: var(--kyy_color_tag_text_cyan, #11bdb2);
    text-align: center;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px; /* 166.667% */
  }
  .sjbox-title {
    overflow: hidden;
    color: var(--text-kyy_color_text_1, #1a2139);
    text-overflow: ellipsis;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px; /* 157.143% */
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    word-break: break-all;
    max-width: 300px;

  }
  .info-lable {
    overflow: hidden;
    color: var(--text-kyy_color_text_3, #828da5);
    text-overflow: ellipsis;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
  .sjbox-info-group {
    width: 24px;
    height: 24px;
    border-radius: 50%;
  }
  .info-value {
    overflow: hidden;
    color: var(--text-kyy_color_text_1, #1a2139);
    text-overflow: ellipsis;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
}
.sjbox-left {
  img {
    width: 121px;
    height: 121px;
    border-radius: 8px;
  }
}
.gchbox:last-child {
  margin-bottom: 0px;
}
.star-icon {
  width: 24px;
  font-size: 24px;
}
.gchbox:hover {
  border: 1px solid var(--brand-kyy_color_brand_default, #4d5eff);
}
.star {
  font-size: 24px;
  width: 24px;
  height: 24px;
}
.gchbox {
  align-items: center;
  border: 1px solid transparent;

  border-radius: 8px;
  background: var(--bg-kyy_color_bg_deep, #f5f8fe);
  display: flex;
  margin-bottom: 12px;
  width: 412px;
  height: 96px;
  padding: 12px;
  gap: 16px;
}
.gchbox-img {
  width: 48px;
  height: 48px;
  border-radius: 50%;
}
.gchbox-lable {
  overflow: hidden;
  color: var(--text-kyy_color_text_3, #828da5);
  text-overflow: ellipsis;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px; /* 166.667% */
  width: 100%;
  margin: 0 4px;
}
.gcbox {
  display: flex;
  padding: 12px;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  gap: 12px;
  border-radius: 8px;
  border: 1px solid var(--brand-kyy_color_brand_default, #4d5eff);
  background: var(--bg-kyy_color_bg_deep, #f5f8fe);

  .gchbox-title {
    overflow: hidden;
    color: var(--text-kyy_color_text_1, #1a2139);
    text-overflow: ellipsis;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px; /* 150% */
  }
}
.gchbox-tag {
  display: flex;
  height: 20px;
  min-height: 20px;
  max-height: 20px;
  padding: 0px 4px;
  justify-content: center;
  align-items: center;
  gap: 4px;
  border-radius: var(--kyy_radius_tag_s, 4px);
  background: var(--kyy_color_tag_bg_kyyBlue, #e4f5fe);
  overflow: hidden;
  color: var(--kyy_color_tag_text_kyyBlue, #21acfa);
  text-align: center;
  text-overflow: ellipsis;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px; /* 166.667% */
}
</style>
