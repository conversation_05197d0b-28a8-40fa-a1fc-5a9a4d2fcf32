<template>
  <!-- 文件预览bar -->
  <div class="win-title">
    <div class="app-title-controls">
      <template v-if="platform === 'win32'">
        <div style="display: flex; width: 100vw; height: 56px">
          <div class="business-bar-title">
            <slot name="content"></slot>
          </div>
          <div
            style="
              display: flex;
              justify-content: end;
              width: 190px;
              -webkit-app-region: no-drag;position: relative;z-index: ***********;"
          >
            <div class="windows-icon-bg" @click="Mini">
              <img
                src="@renderer/assets/svg/clouddisk/icon_minus.svg"
                class="icon-size"
              />
            </div>
            <div class="windows-icon-bg" @click="MixOrReduction">
              <img
                src="@renderer/assets/svg/clouddisk/icon_zoomAll.svg"
                class="icon-size"
              />
            </div>
            <div class="windows-icon-bg close-icon" @click="Close">
              <img
                src="@renderer/assets/svg/clouddisk/icon_error.svg"
                class="icon-size"
              />
            </div>
          </div>
        </div>
      </template>
      <div v-else class="business-bar-title win-title-center" style="">
        <slot name="content"></slot>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import _ from "lodash";
import LynkerSDK from '@renderer/_jssdk';
import { removeAppTeamId } from "@renderer/utils/auth";
const emit = defineEmits(["close"]);

const platform = ref(process.platform);
const props = defineProps({
  isTitle: {
    type: String,
    default: "",
  },

});

const Mini = () => {
  LynkerSDK.ipcRenderer.invoke("windows-mini");
};

const MixOrReduction = () => {
  LynkerSDK.ipcRenderer.invoke("window-max");
};

const Close = () => {
  emit("close", true);
  LynkerSDK.ipcRenderer.invoke("window-destory");
  // if (props.isTitle === "图片预览") {
  //   LynkerSDK.ipcRenderer.invoke("view-img-window-close");
  // } else {
  //   emit("close", true);
  //   LynkerSDK.ipcRenderer.invoke("window-close");
  // }
  // removeAppTeamId();
};
</script>

<style lang="less" scoped>
.windows-icon-bg:hover {
  background-color: rgba(182, 182, 182, 0.2);
  cursor: pointer;
}
// .close-icon:hover {
//    background: red;
//   color: #fff;
// }
.windows-icon-bg {
  width: 56px;
}
.win-title-center {
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: row;
  text-align: center;
  justify-content: center;
  align-items: center;
  width: 100vw;
}
.business-bar-title {
  flex: 1 1 0%;
  text-align: center;
  margin-right: -40px;
  color: var(--text-kyy-color-text-1, #1a2139);
  text-align: center;

  /* kyy_fontSize_2/bold */
  font-family: PingFang SC;
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 56px; /* 157.143% */
}
.win-title {
  height: 56px;
  line-height: 56px;
  -webkit-app-region: drag;
  display: flex;
  position: relative;
  z-index: 9999999;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid var(--divider-kyy-color-divider-deep, #f5f8fe);
}
.app-title-controls {
  -webkit-app-region: drag;
  user-select: none;
  flex-basis: 200px;
  flex-grow: 0;
  flex-shrink: 0;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  .windows-icon-bg {
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(129, 129, 129, 0.6);
    cursor: pointer;
    .icon-size {
      width: 20px;
      height: 20px;
      fill: currentColor;
      overflow: hidden;
      -webkit-user-drag: none;
    }
  }

  // .windows-icon-bg:hover {
  //   background-color: rgba(182, 182, 182, 0.2);
  // }

  // .close-icon:hover {
  //   background-color: rgba(232, 17, 35, 0.9);
  //   color: #fff;
  // }
}
</style>
