{"name": "@lynker-desktop/web", "version": "0.0.61", "description": "lynker-web-sdk", "keywords": ["electron"], "types": "./index.d.ts", "main": "./index.js", "module": "./esm/index.js", "browser": "./index.js", "exports": {".": {"node": {"require": "./index.js", "import": "./esm/index.js"}, "default": "./index.js"}}, "author": "", "license": "MIT", "repository": {"type": "git", "url": ""}, "scripts": {"postinstall": "", "prepublishOnly": "npm run build", "build": "rollup -c --bundleConfigAsCjs ", "dev": "concurrently \"npm run dev:renderer\" \"node ./scripts/dev.js\"", "dev:renderer": "cd example/renderer && npm run dev", "dev:main": "npm run build && electron --inspect=8888 example/main.js"}, "engines": {"node": ">=18.18.0", "pnpm": ">=9.6.0"}, "packageManager": "pnpm@1.22.22", "dependencies": {"@lynker-desktop/electron-ipc": "0.0.4-alpha.52", "eventemitter2": "^6.4.9", "events": "^3.3.0", "lodash": "^4.17.21", "md5": "^2.3.0", "uuid": "^10.0.0"}, "devDependencies": {"@babel/core": "^7.24.9", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-commonjs": "^26.0.1", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-replace": "^5.0.7", "@rollup/plugin-typescript": "^11.1.6", "@types/lodash": "^4.17.7", "@types/md5": "^2.3.5", "@types/uuid": "^10.0.0", "classnames": "^2.2.6", "concurrently": "^8.2.2", "fs-extra": "^11.2.0", "lodash.merge": "^4.6.2", "rimraf": "^6.0.1", "rollup": "^4.18.1", "rollup-obfuscator": "^4.1.1", "rollup-plugin-multi-input": "^1.4.2", "rollup-plugin-node-builtins": "^2.1.2", "tslib": "^2.6.3", "typescript": "^5.5.3"}}