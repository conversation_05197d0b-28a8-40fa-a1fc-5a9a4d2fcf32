<template>
  <div class="dailog-iframe">
    <t-dialog
      class="visible"
      :visible="true"
      :closeOnEscKeydown="false"
      :closeOnOverlayClick="false"
      :footer="false"
      :width="dialogIframeData.width || '448px'"
      @close="close"
    >
      <template #header>
        <div class="title">文旅资讯</div>
      </template>
      <template #body>
        <div :style="{width: dialogIframeData.contentWidth || '395px',height: dialogIframeData.height || '460px',overflowX: 'hidden'}">
          <iframe-component
            v-if="dialogIframeData.url"
            class="dailog-iframe-wrap"
            type="webview"
            :id="dialogIframeData.id"
            ref="iframeRef"
            :url="dialogIframeData.url"
            :is-load-in-component="true"
          />
        </div>
      </template>
     
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import {computed, ref, watch, toRefs} from 'vue'
import { useI18n } from 'vue-i18n';
import LynkerSDK from '@renderer/_jssdk';
import IframeComponent from '@renderer/_jssdk/components/iframe/index.vue';
import { useChatActionStore } from '@renderer/views/message/service/actionStore';

const { ipcRenderer } = LynkerSDK;
const { t } = useI18n();
const props = defineProps({
  data: { type: Object, default: {} },
})
const chatActionStore = useChatActionStore();
const {dialogIframeData} = toRefs(chatActionStore);
const emits = defineEmits(['close']);
const visible = ref(false)
const close = () => {  
  chatActionStore.showDialogIframe(false)
}


</script>

<style lang="less" scoped>
.dailog-iframe{
  :deep(.t-dialog){
  background: url('http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/h5/dialog-default-bg.png') no-repeat;
  background-size: contain;
  background-color: rgba(245, 248, 254, 1);
    .t-dialog__body{
     padding-bottom: 0;
   }
  }
  .dailog-iframe-wrap{
    background: transparent;
  }
}

</style>
