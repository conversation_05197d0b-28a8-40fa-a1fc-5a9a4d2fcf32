<template>
  <div :class="['container', isNotMac ? 'scrollbar' : 'scroll-style']">
    <!-- 个人身份卡 -->
    <section class="card-section personal">
      <div class="top-wrap">
        <div class="section-title">{{ t('contacts.guide_profile_card') }}</div>
        <div class="bg-right"></div>
      </div>
      <div class="card-item" @click="viewSelf" :data-current="selfInfo.openid === props.current">
        <!-- <div class="item-top">
          <span>{{ t('identity.personal') }}</span>
        </div> -->
        <div class="item-info">
          <div class="f">
            <span class="info-key">{{ t('identity.name') }}</span>
            <span class="content">{{ selfInfo.title }}</span>
            <div class="card-qr" @click.stop="openApply(_tran(selfInfo))">
              <t-tooltip :content="t('identity.applyText')">
                <div class="viewApplyLanguageIcon"></div>
              </t-tooltip>
            </div>
          </div>
          <div class="f mt4">
            <span class="info-key">{{ t('identity.lkID') }}</span>
            <span class="content" style="font-size:17px;">{{ selfInfo.link_id }}</span>
            <t-tooltip :content="t('identity.qrcode')">
              <div class="card-qr" @click.stop="openProfileQr">
                <iconpark-icon class="item-icon" name="icondimensional"></iconpark-icon>
              </div>
            </t-tooltip>
          </div>
        </div>
      </div>
    </section>
    <!-- 外部身份卡 -->
    <section v-if="cards.length" class="card-section">
      <div class="top-wrap">
        <div class="section-title">{{ t('contacts.guide_out_card') }}</div>
        <div class="bg-right"></div>
      </div>
      <div class="card-item-wrap">
        <div
          class="card-item-for"
          v-for="(item, i) in cards"
          :key="item.uuid"
          @click="viewCard(item)"
          :data-current="item.uuid === props.current"
        >
          <div class="card-item">
            <div class="item-top">
              <img style="margin-right: 8px;" :src="item.logo || logoIcon" alt="">
              <t-tooltip :content="item.team">
                <span>{{ item.team }}</span>
              </t-tooltip>
            </div>
            <div class="item-info">
              <div class="f">
                <span class="info-key">{{ t('identity.name') }}</span>
                <span class="content">{{ item.name }}</span>
              </div>
              <div class="f">
                <span class="info-key">{{ t('identity.departmentLabel') }}</span>
                <span class="content">{{ item?.positions[0]?.departmentName || '- -' }}</span>
                <div class="card-qr" @click.stop="openApply(item)">
                  <t-tooltip :content="t('identity.applyText')">
                    <div class="viewApplyLanguageIcon"></div>
                  </t-tooltip>
                </div>
              </div>
              <div class="f">
                <span class="info-key">{{ t('identity.position') }}</span>
                <span class="content">{{ item?.positions[0]?.jobName || '- -' }}</span>
                <t-tooltip :content="t('identity.qrcode')">
                  <div class="card-qr" @click.stop="openCardQr(item)">
                    <iconpark-icon class="item-icon" name="icondimensional"></iconpark-icon>
                  </div>
                </t-tooltip>
              </div>
            </div>
          </div>
          <div v-if="i !== cards.length - 1" class="space"></div>
        </div>
      </div>
    </section>

    <!-- 内部身份卡 -->
    <section v-if="staffs.length" class="card-section inner">
      <div class="top-wrap">
        <div class="section-title">{{ t('contacts.guide_inner_card') }}</div>
        <div class="bg-right"></div>
      </div>
      <div class="card-item-wrap">
        <div
          class="card-item-for"
          v-for="(item, i) in staffs"
          :key="item.uuid"
          @click="viewCard(item)"
          :data-current="item.uuid === props.current"
        >
          <div class="card-item">
            <div class="item-top">
              <img style="margin-right: 8px;" :src="item.logo || logoIcon" alt="">
              <t-tooltip :content="item.team">
                <span>{{ item.team }}</span>
              </t-tooltip>
            </div>
            <div class="item-info">
              <div class="f">
                <span class="info-key">{{ t('identity.name') }}</span>
                <span class="content">{{ item.name }}</span>
              </div>
              <div class="f">
                <span class="info-key">{{ t('identity.departmentLabel') }}</span>
                <span class="content">{{ item?.positions[0]?.departmentName || '- -' }}</span>
              </div>
              <div class="f">
                <span class="info-key">{{ t('identity.position') }}</span>
                <span class="content">{{ item?.positions[0]?.jobName || '- -' }}</span>
              </div>
            </div>
          </div>
          <div v-if="i !== staffs.length - 1" class="space"></div>
        </div>
      </div>
    </section>

    <!-- 平台身份卡 -->
    <section v-if="platforms.length" class="card-section inner">
      <div class="top-wrap">
        <div class="section-title">{{ t('contacts.guide_platform_card') }}</div>
        <div class="bg-right"></div>
      </div>
      <div class="card-item-wrap">
        <div
          class="card-item-for"
          v-for="(item, i) in platforms"
          :key="item.uuid"
          @click="viewCard(item)"
          :data-current="item.uuid === props.current"
        >
          <div class="card-item">
            <div class="item-top">
              <img style="margin-right: 8px;" :src="item.logo || logoIcon" alt="">
              <t-tooltip :content="item.team">
                <span>{{ item.team }}</span>
              </t-tooltip>
            </div>
            <div class="item-info">
              <div class="f">
                <span class="info-key">{{ t('identity.name') }}</span>
                <span class="content">{{ item.name }}</span>
              </div>
              <div class="f" v-if="item?.member_position">
                <span class="info-key">{{ t('identity.associationJob') }}</span>
                <span class="content">{{ item?.member_position || '- -' }}</span>
              </div>
            </div>
          </div>
          <div v-if="i !== platforms.length - 1" class="space"></div>
        </div>
      </div>
    </section>

  </div>

  <CardQr :visible="Boolean(qrItem)" :card-id="qrItem?.card" :card-info="qrItem?.info" @close=" qrItem = null" destroyOnClose></CardQr>
</template>

<script setup lang="ts">

import { ref, defineProps, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRouter, useRoute } from "vue-router";
import CardQr from './components/CardQr.vue'
import { isNotMac } from '../zhixing/util';
import { getProfilesInfo, getCards, getStaff, getPlatform } from '@renderer/utils/auth';
import logoIcon from '@renderer/assets/defaultOrgLogo.svg';
import { getIdentityPlatformData } from '@renderer/utils/apiInit';

import LynkerSDK from "@renderer/_jssdk";
const { ipcRenderer } = LynkerSDK;

const { t } = useI18n();

const props = defineProps({
  current: {
    type: String,
    default: ''
  }
})
const emits = defineEmits(['close', 'openApply']);

const router = useRouter();
const route = useRoute();
const selfInfo = getProfilesInfo();
const staffs = getStaff() || [];
const cards = getCards() || [];
const platforms = ref(getPlatform() || []);

const qrItem = ref<{ card: string, info: any}>();

const viewSelf = () => {
  if(props.current === selfInfo.openid){
    emits('close')
    return
  }
  const id = encodeURIComponent(selfInfo.openid);
  router.replace({path: `/identitycard/view/${id}/${id}`, query: {...route.query,showMoreCard: '1',fromIdentityPage: 'fromIdentityPage'}});
}
const viewCard = (item) => {
  emits('close');
  const id = encodeURIComponent(item.uuid);
  router.replace({path: `/identitycard/view/${id}/${id}`, query: {...route.query,showMoreCard: '1',fromIdentityPage: 'fromIdentityPage'}});
}
ipcRenderer.on('load-card', (e, val) => {
  viewSelf()
})

const openProfileQr = () => {
  qrItem.value = {
    card: selfInfo.openid,
    info: {
      ...selfInfo,
      name: selfInfo.title
    }
  }
}

const openCardQr = (item) => {
  qrItem.value = {
    card: item.uuid,
    info: item
  }
}

const openApply = (item) => {
  emits('openApply', item);
}

const _tran = (item) => {
  return {
    uuid: item.openid,
    staffId: item.openid,
    avatar: item.avatar || '',
    name: item?.title || '',
    team: '',
    teamId: '',
    internal_team_id: '',
    jobs: '',
    logo: '',
    positions: [],
  }
}

onMounted(async () => {
  platforms.value = await getIdentityPlatformData(platforms.value);
})

</script>

<style lang="less" scoped>
@import '../../style/mixins.less';
.f {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  height: 28px;
}
.mt4 {
  margin-top: 4px;
}
::-webkit-scrollbar {
  width: 6px;
}
/*定义滚动条轨道 内阴影+圆角*/
::-webkit-scrollbar-track {
  background-color: transparent;
}
/*定义滑块 内阴影+圆角*/
::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background: var(--divider-kyy_color_divider_deep, #D5DBE4);
}
.container {
  height: 100%;
  padding: 12px 12px 0;
  background: var(--cyan-kyy_color_cyan_hover, #3CC9C0);

  /* kyy_shadow_m */
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.12);
  overflow-y: scroll;
  overflow-x: hidden;
  font-size: 14px;

  font-weight: 400;
  text-align: left;
  color: #13161b;
  line-height: 22px;

  border-radius: 16px;
  //border-top: 8px solid var(--brand-kyy-color-brand-default, #4D5EFF);
}

.card-item:hover {
  outline: 2px solid var(--brand-kyy-color-brand-default, #4D5EFF);
}

.card-item[data-current='true']{
  outline: 2px solid var(--brand-kyy-color-brand-default, #4D5EFF);
}
.card-item-wrap {
  margin-top: -6px;
  border-radius: 8px;
  background: #fff;
  .card-item-for {
    .space {
      height: 15px;
    }
  }
}
.card-item {
  padding: 8px 12px 0px;
  // border: 2px solid #fff;
  // border-bottom: 2px solid #fff;
  background: linear-gradient(180deg, #FFF0E5 0%, #FFF 100%);
  border-radius: 8px;
  margin: 0 auto;
  cursor: pointer;
  position: relative;
  // margin-top: -8px;
  // top: -8px;
  .item-top {
    display: flex;
    align-items: center;
    overflow: hidden;
    color: var(--text-kyy_color_text_1, #1A2139);
    text-overflow: ellipsis;
    white-space: nowrap;
    /* kyy_fontSize_2/bold */
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px; /* 157.143% */

    height: 24px;
    margin-bottom: 4px;

    img {
      width: 24px;
      height: 24px;
      border-radius: 50%;
    }
  }
  .item-info {
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
    font-family: "PingFang SC";

    .info-key {
      color: var(--text-kyy_color_text_3, #828DA5);
      width: 80px;
      flex-shrink: 0;
    }
    .content {
      color: var(--text-kyy_color_text_1, #1A2139);
      flex-grow: 1;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
  }
}
.inner .card-item {
  background: linear-gradient(180deg, #E4F5FE 0%, #FFF 100%);
}
.top-wrap {
  display: flex;
  // position: relative;
  // margin-bottom: 30px;
  // top: 8px;
  background: var(--cyan-kyy_color_cyan_hover, #3CC9C0);
  .section-title {
    // position: absolute;
    // z-index: 4;
    width: 80px;
    height: 38px;
    padding: 0 12px;
    color: var(--text-kyy_color_text_2, #828DA5);

    border-radius: 8px 8px 0 0;
    background: var(--bg-kyy_color_bg_light, #FFF);
    font-family: "PingFang SC";
    font-size: 14px;
    font-weight: 400;
    line-height: 30px; /* 166.667% */
  }
  .bg-right {
    // position: absolute;
    // z-index: 1;
    flex: 1;
    background: transparent;
  }
}

.card-section {
  // padding: 8px;
  border-radius: 8px;
  // background: var(--cyan-kyy_color_cyan_hover, #3CC9C0);
  // background: var(--bg-kyy_color_bg_light, #FFF);
  margin-bottom: 12px;
}
.personal {
  .section-title {
    color: var(--text-kyy_color_text_2, #516082);
    font-family: "PingFang SC";
  }
  .card-item {
    background: linear-gradient(180deg, #E0F2E5 0%, #FFF 100%);
    border-radius: 8px;
    padding-bottom: 8px;
    margin-top: -6px;
  }
}

.card-qr {
    width: 28px;
    height: 28px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .card-qr:hover {
    background-color: #EAECFF;
  }

  .item-icon {
    font-size: 18px;
    width: 20px;
    height: 20px;
    color: #828DA5
  }
  .item-icon:hover {
    color: #707EFF;
  }

  .viewApplyLanguageIcon{
    width: 28px;
    height: 28px;
    background-image: url(@/assets/identity/viewApplyLanguage.svg);
    background-size: cover;
  }
  .viewApplyLanguageIcon:hover{
    background-image: url(@/assets/identity/viewApplyLanguage-hover.svg);
  }


</style>
