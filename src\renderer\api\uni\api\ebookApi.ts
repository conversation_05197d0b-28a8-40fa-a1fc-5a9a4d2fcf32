import { lssClientOrganizeMemRequest as client_orgRequest, lssSquareMemRequest } from "@renderer/utils/apiRequest";
import { getAssociationTeamID } from "@renderer/views/association/utils/auth";

// 获取会刊设置信息
export function getEbookSettingAxios(params?, teamId?) {
  return client_orgRequest({
    method: "get",
    url: '/journal/setting',
    params: {
      ...params,
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}


// 重新生成刊物
export function retryEbookAxios(data, teamId?) {
  return client_orgRequest({
    method: "post",
    url: `/journal/retry/${data.id}`,
    data: {
      ...data,
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}


// 更新渠道类型
export function updateJournalAxios(data, teamId?) {
  return client_orgRequest({
    method: "post",
    url: `/journal/channel-type`,
    data: {
      ...data,
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}


export function getEbookListAxios(params?, teamId?) {
  return client_orgRequest({
    method: "get",
    url: '/journal',
    params: {
      ...params,
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}

export function getEbookDetail(id, teamId?) {
  return client_orgRequest({
    method: "get",
    url: `/journal/${id}`,
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}

export function createEbookAxios(data, teamId?) {
  return client_orgRequest({
    method: "post",
    url: `/journal`,
    data: {
      ...data,
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}

export function putEbookAxios(id, data, teamId?) {
  return client_orgRequest({
    method: "put",
    url: `/journal/${id}`,
    data: {
      ...data,
    },
    headers: {
      teamId: teamId ||getAssociationTeamID(),
    },
  });
}

export function deleteEbookAxios(id, teamId?) {
  return client_orgRequest({
    method: "delete",
    url: `/journal/${id}`,
    headers: {
      teamId: teamId ||  getAssociationTeamID(),
    },
  });
}


export function getEbookShareList(params?) {
  return client_orgRequest({
    method: "get",
    url: `/journal/share`,
    params: {
      ...params,
    },
    headers: {
      teamId: getAssociationTeamID(),
    },
  });
}

export function journalSort(data, teamId?) {
  return client_orgRequest({
    method: "post",
    url: `/journal/sort`,
    data: {
      ...data,
    },
    headers: {
      teamId: teamId || getAssociationTeamID(),
    },
  });
}
