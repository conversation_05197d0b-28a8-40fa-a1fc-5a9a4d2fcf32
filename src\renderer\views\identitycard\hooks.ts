import { computed, onMounted, ref, watch } from "vue";
import to from "await-to-js";
import { DialogPlugin } from "tdesign-vue-next";
import { useRoute } from "vue-router";
import { SquareEntryResponse } from "@/api/square/models/common";
import { cardIdType } from "@/views/identitycard/data";
import { getIDCardSquareInfo } from "@/api/square/common";
import { i18n } from "@/i18n";
import { goSquareModule } from "@/views/square/utils/business";
import { getSquareNumber, setSquareNumber } from "@/api/identity/api/card";
import LynkerSDK from "@renderer/_jssdk";
const { ipcRenderer } = LynkerSDK;

type CardInfo = { cardId: string, openid: string, teamId: string, [key: string]: any }
export const useSquareEntry = (card: CardInfo, isSelf) => {
  const info = ref<SquareEntryResponse>({});
  const orgInfo = ref<SquareEntryResponse>({});
  const isPersonal = computed(() => card.value?.cardId && cardIdType(card.value.cardId) === 'personal');
  // const showPersonalEntry = computed(() => (isSelf.value || (!isSelf.value && info.value.opened)));
  // 个人身份卡：如他人未开通广场号，则不显示广场入口
  const showSquareEntry = computed(() => info.value.opened || orgInfo.value.opened);
  // 他人或本人的组织身份卡，对应的组织是否开通了广场号
  const isSquareOpened = computed(() => orgInfo.value.opened);
  const squareConfirmDia = ref(null);

  // 查询广场信息
  const getInfo = async (open_id?: string, team_id?: string) => {
    // 传open_id查个人，传team_id查广场
    if (!open_id && !team_id) return;
    const [err, res] = await to(getIDCardSquareInfo({ open_id, team_id }));
    if (err) return;
    res.data.latestPictures = res.data.latestPictures.slice(0, 4).map((v) => ({
      ...v,
      url: v.isVideo ? `${v.url}?x-oss-process=video/snapshot,t_1000,f_jpg,w_50,h_50,m_fast,ar_auto` : v.url
    }));

    if (open_id) info.value = res.data;
    if (team_id) orgInfo.value = res.data;
  };

  watch(() => card.value, async (val) => {
    if (!val ) return;
    val.openid && await getInfo(val.openid);
    val.teamId && await getInfo(undefined, val.teamId);
  }, { immediate: true,deep:true });

  // 跳转到广场主页
  const goSquare = async (selfOpened = true, isPersonal = true) => {
    const query = { redirect: '/square/info', id: isPersonal ? info.value.squareId : orgInfo.value.squareId, needShowDialog: true };
    if (!selfOpened) query.redirect = '';

    // 跳转广场关闭身份卡
    ipcRenderer.invoke("hide-identWin");

    await goSquareModule(query);
  };

  const redirect = (isPersonal: boolean) => {
    // 本人未开通广场号
    if (isPersonal ? !info.value.selfOpened : !orgInfo.value.selfOpened) {
      squareConfirmDia.value = DialogPlugin.confirm({
        // @ts-ignore
        header: i18n.global.t('account.tip'),
        body: i18n.global.t('identity.enableSquareTip'),
        confirmBtn: i18n.global.t('identity.confirm'),
        cancelBtn: i18n.global.t('identity.cancel'),
        closeBtn: null,
        closeOnOverlayClick: true,
        theme: 'info',
        onConfirm: async () => {
          squareConfirmDia.value.destroy();
          await goSquare(false, isPersonal);
        },
        onClose: () => {
          squareConfirmDia.value.hide();
        },
      });
      return;
    }

    // 目标广场号已开通
    if (info.value.opened || orgInfo.value?.opened) {
      goSquare(true, isPersonal);
    }
  };

  return {
    isPersonal,
    showSquareEntry,
    squareInfo: info,
    orgSquareInfo: orgInfo,
    squareRedirect: redirect,
    isSquareOpened,
    squareConfirmDia
  };
};

// 显示个人广场入口控制
export const useSquareEnable = (id?: string) => {
  const route = useRoute();
  const squareEnable = ref(1);
  const cardId = id || route.query.cardId;

  const getSquare = async () => {
    const [err, res] = await to(getSquareNumber({ cardId }));
    if (err) return;
    // 备注：接口文档跟字段名实际意义不符，isDisable 为 1 代表开启（后端杨雄文不改）
    squareEnable.value = res.data.data.detail.isDisable;
  };

  const squareEnableChange = async () => {
    const [err] = await to(setSquareNumber({ cardId, isDisable: squareEnable.value }));
    if (err) {
      squareEnable.value = squareEnable.value === 0 ? 1 : 0;
    }
  };

  onMounted(async () => {
    await getSquare();
  });

  return {
    squareEnable,
    squareEnableChange,
  };
};
