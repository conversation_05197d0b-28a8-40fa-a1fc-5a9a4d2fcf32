<template>
  <div
    class="page big"
    @click="pageClick"
    v-infinite-scroll="handleInfiniteOnLoad"
    :infinite-scroll-immediate-check="false"
    :infinite-scroll-distance="50"
    @scroll="onScroll"
    ref="bigRef"
  >
    <div class="top-header">
        <!-- :colfixdHeader="colfixdHeader"
        :fixdHeaderShow="fixdHeaderShow" -->

      <header-compoent
        ref="latWidgetRef"
        :back="true"
        :colfixdBoolean="colfixdHeader"
        @homeinit="homeinit"
        @toSaerch="toSeach"
        :keyword="keyword"
        :addressText="addressText"
        @editIsMapPageFlag="editIsMapPageFlag"
        :branData="branData"
        :classify="props.classify"
      />
      <column
      v-if="sqData.length || props.classify"
        style="padding: 0 16px 16px"
        :classify="props.classify"
        @handleSearch="handleSearch"
        :showCol="false"
        @switchColumn="switchColumn"
        :orderBy="orderBy"
        @clickSort="clickSort"
      />
    </div>
    <div class="cont-box">
      <div class="conetnt">
        <sq-conetnt
          :sqData="sqData"
          :keyword="keyword"
          :skeletonShow="skeletonShow"
          :scroll-container="bigRef"
          :loadingStatus="loadingStatus"
        />
      </div>
    </div>
  </div>
  <totop container=".big" />
</template>

<script setup lang="ts">
import { computed, onActivated, onMounted, ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { useNicheStore } from '@renderer/store/modules/niche';
import totop from '@/views/big-market/components/totop.vue';
import { querySquares,  } from '@/views/big-market/apis';
import LynkerSDK from '@renderer/_jssdk';
const { ipcRenderer } = LynkerSDK;
import headerCompoent from '@/views/big-market/components/header-compoent.vue';
import { getProfilesInfo } from '@renderer/utils/auth';
import column from '@/views/big-market/components/column.vue';
import sqConetnt from '@/views/big-market/components/sq-conetnt.vue';

const props = defineProps({
  typeKey: {
    type: String,
    default: 'desired',
  },
  home: {
    type: Boolean,
    default: false,
  },
  classify: {
    type: Boolean,
    default: false,
  },
});

const { t, locale } = useI18n();
const bigRef = ref(null);
const params = ref({
  market_classify_id: '',
  classify_id: undefined,
  type: props.typeKey === 'desired' ? 2 : 1,
  market_keyword: '',
  longitude: '',
  latitude: '',
  page: 1,
  pageSize: 20,
});
const branData = ref([]);
const nicheStore = useNicheStore();
const supplyData = ref([]);
const nicheClassId = ref(0);
const loadingStatus = ref(false);
const addressData = ref(null);
const typeKeyVal = ref(props.typeKey);
const addressText = ref(null);
const editAddressData = (data) => {
  addressData.value = data;
};
const route = useRoute();
onActivated(() => {
  console.log('onActivated.query.className',route.query.className);
  // setBranDataRun()
});
onMounted(() => {
    console.log('onMounted.query.className',route.query.className);
  setBranDataRun()
});

watch(() => route.query, (newVal, oldVal) => {
  console.log('watch',newVal, oldVal);
  setBranDataRun()
}, {deep: true});


const setBranDataRun = () => {
if (!props.home) {
    const className = route.query.className;
    console.log('route.query.className',route.query.className);

    branData.value = ['首页', className];
    const classifyId = route.query.classifyId as string;
    marketClassifyId.value = classifyId ? classifyId.split(',') : undefined;
    init();
  } else {
    branData.value = ['首页'];
  }
}

const init = async () => {
  if (!addressText.value) {
    addressData.value = nicheStore.position;
    addressText.value = nicheStore.positionText;
  }
  refReqRun();
};
const editIsMapPageFlag = (val, name?) => {
  if (val) {
    console.log(val, name);
    addressText.value = name;
    addressData.value = [val.location.lng, val.location.lat];
    refReqRun();
  }
};
const editAddressText = (text) => {
  addressText.value = text;
};

const handleInfiniteOnLoad = () => {
  console.log('触发了');
  if (!scrollDisabled.value && !lastPage.value && !disOnLoad.value) {
    params.value.page += 1;
    querySquaresReq();
  }
};

const tagsContainer = ref(null);
const tagList = ref([]);
const hiddenTags = ref([]);
const moreTagShow = ref(false);

const latWidgetRef = ref(null);
const pageClick = () => {
  latWidgetRef?.value.latClose();
  latWidgetRef?.value.closeSearchDr();
};

const emit = defineEmits(['homeinit', 'up-title']);

const homeinit = () => {
  emit('homeinit');
};

const notype = computed(() => {
  if (!typeKeyVal.value) {
    return 1;
  }
  return 2;
});
const reqOk = ref(false);
const changeKeyword = () => {
  reqOk.value = false;
};
const some = () => {
  console.log('方法有用，请勿删除');
};
const moreTagSe = computed(() => hiddenTags.value.some((item) => item.id === nicheClassId.value));
const typeSelectEvent = (e) => {
  params.value.type = e;
};
const setTimeoutTemp = ref(null);
const mouseleaveRun = () => {
  console.log('mouseleave');
  setTimeoutTemp.value = setTimeout(() => {
    moreTagShow.value = false;
  }, 300);
};
const mouseMoreRun = () => {
  console.log('mouseMoreRun');
  clearTimeout(setTimeoutTemp.value);
  moreTagShow.value = true;
};

const fixdHeaderShow = ref(false);
const onScroll = (e) => {
  console.log(e.target.scrollTop);

  if (e.target.scrollTop >= 30) {
    fixdHeaderShow.value = true;
  } else {
    fixdHeaderShow.value = false;
  }
  if (e.target.scrollTop >= 100) {
    colfixdHeader.value = true;
  } else {
    colfixdHeader.value = false;
  }

  // if (props.home) {

  // } else {
  //   if (e.target.scrollTop >= 50) {
  //     colfixdHeader.value = true;
  //   } else {
  //     colfixdHeader.value = false;
  //   }
  // }

  latWidgetRef?.value.latClose();
};
const keyword = ref('');
const skeletonShow = ref(false);
const colfixdHeader = ref(false);
const sqData = ref([]);
const pageNum = ref(1);
const marketClassifyId = ref(undefined);
const marketColumnId = ref(undefined);
const orderBy = ref(1);
const total = ref(0);
const profileInfo = getProfilesInfo();
const scrollDisabled = computed(() => sqData.value.length >= total.value);
const pageParams = ref({});
const handlePageParams = (params) => {
  for (const key in params) {
    if (params.hasOwnProperty(key)) {
      params[`pagination.${key}`] = params[key];
      params[key] = undefined;
    }
  }
};
const lastPage = ref(false);
const disOnLoad = ref(false);
const querySquaresReq = async () => {
  const params = {
    marketColumnId: marketColumnId.value,
    marketClassifyIds: marketClassifyId.value,
    orderBy: orderBy.value,
    'latLng.latitude': addressData.value[1],
    'latLng.longitude': addressData.value[0],
    'pagination.size': 30,
    'pagination.number': pageNum.value,
    adCode: profileInfo.area,
    keyword: keyword.value,
    ...pageParams.value,
  };
  const res = await querySquares(params);
  sqData.value = sqData.value.concat(res.data?.data?.squares);
  total.value = res.data?.data?.pagination?.total;
  lastPage.value = res.data?.data?.pagination?.lastPage;
  pageParams.value = res.data?.data?.pagination;
  handlePageParams(res.data?.data?.pagination);
  skeletonShow.value = false;
  disOnLoad.value = false;
  console.log('querySquaresRes', res);
};

const refReqRun = async () => {
  skeletonShow.value = true;
  disOnLoad.value = true;
  pageNum.value = 1;
  pageParams.value = {};
  sqData.value = [];
  await querySquaresReq();
};

const clickSort = async (type: number) => {
  orderBy.value = type;
  refReqRun();
};
const switchColumn = async (id: number) => {
  marketColumnId.value.value = id;
  refReqRun();
};
const homeSearch = (homeParams) => {
  keyword.value = homeParams.keyword;
  addressText.value = homeParams.addressText;
  addressData.value = homeParams.position;
  refReqRun();
};

const handleSearch = (wrod) => {
  keyword.value = wrod;
  refReqRun();
};

const toSeach = (word) => {
  keyword.value = word;
  skeletonShow.value = true;
  sqData.value = [];
  pageNum.value = 1;
  pageParams.value = {};
  querySquaresReq();
};
defineExpose({
  homeSearch,
});
</script>

<style lang="less" scoped>
:deep(.t-back-top.t-size-s) {
  background: var(--bg-kyy_color_bg_mask, rgba(0, 0, 0, 0.5));
  border: none;
  box-shadow: none;
}

.page::-webkit-scrollbar {
  width: 0px;
}

.page {
  display: flex;
  width: 100%;
  flex-direction: column;
  align-items: center;
  height: 100vh;
  background-image: url(@/assets/big-market/bg.png);
  overflow-y: auto;
  background-position: center;
  background-size: 100% 100%;
  background-repeat: no-repeat;

  .top-header {
    position: sticky;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
    width: 100%;
  }

  .cont-box {
    width: 1184px;
    margin-top: 16px;
    padding: 16px;
    border-radius: 8px;
    background: var(--bg-kyy_color_bg_light, #fff);

    .conetnt {
      width: 100%;

      .types {
        display: flex;
        height: 32px;
        align-items: center;
        gap: 16px;
        margin-bottom: 12px;
        flex-wrap: wrap;

        .type-item-clay {
          display: flex;
          padding: 4px 16px;
          justify-content: center;
          align-items: center;
          gap: 10px;
          cursor: pointer;
          border-radius: 16px;
          background: var(--bg-kyy_color_bg_light, #fff);
          color: var(--text-kyy_color_text_1, #1a2139);
          text-align: center;
          font-family: 'PingFang SC';
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px;
          /* 157.143% */
        }

        .type-item-clay:hover {
          border-radius: 16px;
          color: var(--icon-kyy_color_icon_hover, #707eff);
          background: var(--bg-kyy_color_bgBrand_hover, #eaecff);
        }

        .typeItemAct {
          border-radius: 16px;
          background: var(--bg-kyy_color_bgBrand_foucs, #dbdfff);
          color: var(--icon-kyy_color_icon_active, #4d5eff);
          text-align: center;
          font-family: 'PingFang SC';
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px;
          /* 157.143% */
        }

        .more-b {
          display: flex;
          padding: 4px 8px 4px 12px;
          justify-content: center;
          align-items: center;
          gap: 4px;
          color: var(--text-kyy_color_text_2, #516082);
          text-align: center;
          border-radius: 16px;
          background: #fff;
          font-family: 'PingFang SC';
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px;
          /* 157.143% */
          cursor: pointer;
          position: relative;

          .iconarrowdwon {
            font-size: 20px;
          }

          .more-tag-box::-webkit-scrollbar {
            width: 4px;
          }

          .more-tag-box {
            display: inline-flex;
            padding: 4px;
            flex-direction: column;
            align-items: center;
            gap: 2px;
            position: absolute;
            right: 0px;
            top: 34px;
            border-radius: var(--kyy_radius_dropdown_m, 8px);
            background: var(--kyy_color_dropdown_bg_default, #fff);
            box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.12);
            z-index: 100;
            max-height: 278px;
            overflow-y: auto;

            .tag-li {
              display: flex;
              height: 32px;
              min-width: 136px;
              min-height: 32px;
              max-height: 32px;
              padding: 0px 8px;
              align-items: center;
              gap: 12px;
              align-self: stretch;
              color: var(--kyy_color_dropdown_text_default, #1a2139);
              font-family: 'PingFang SC';
              font-size: 14px;
              font-style: normal;
              font-weight: 400;
              line-height: 22px;
              /* 157.143% */
            }

            .tag-li:hover {
              border-radius: var(--kyy_radius_dropdown_s, 4px);
              background: var(--kyy_color_dropdown_bg_hover, #f3f6fa);
            }

            .tagLA {
              border-radius: var(--kyy_radius_dropdown_s, 4px);
              background: var(--kyy_color_dropdown_bg_active, #e1eaff);
              color: var(--kyy_color_dropdown_text_active, #4d5eff);
              font-family: 'PingFang SC';
              font-size: 14px;
              font-style: normal;
              font-weight: 400;
              line-height: 22px;
              /* 157.143% */
            }
          }
        }

        .more-b:hover {
          .iconarrowdwon {
            font-size: 20px;
            color: var(--icon-kyy_color_icon_hover, #707eff);
          }

          color: var(--icon-kyy_color_icon_hover, #707eff);
          border-radius: 16px;
          background: var(--bg-kyy_color_bgBrand_foucs, #eaecff);
        }

        .moreBA {
          .iconarrowdwon {
            font-size: 20px;
            color: var(--icon-kyy_color_icon_hover, #707eff);
          }

          color: var(--icon-kyy_color_icon_hover, #707eff);
          border-radius: 16px;
          background: var(--bg-kyy_color_bgBrand_foucs, #eaecff);
        }

        .moreBC {
          .iconarrowdwon {
            font-size: 20px;
            color: var(--icon-kyy_color_icon_hover, #4d5eff);
          }

          color: var(--icon-kyy_color_icon_hover, #4d5eff);
          border-radius: 16px;
          background: var(--bg-kyy_color_bgBrand_foucs, #dbdfff);
        }
      }

      .list-box {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        gap: 12px;
        column-gap: 12px;
      }
    }
  }
}

.fontWeight6 {
  font-weight: 600 !important;
}

.headerboxshow {
  box-shadow: 0px 3px 8px 0px rgba(0, 0, 0, 0.12);
}
</style>
