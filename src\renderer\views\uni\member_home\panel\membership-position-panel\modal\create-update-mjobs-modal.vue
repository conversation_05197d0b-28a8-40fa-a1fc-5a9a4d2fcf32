<template>

  <t-drawer
    v-model:visible="visible"
    :header="autoHeaderText"
    :close-on-overlay-click="false"
    class="drawerSetForm"
    :z-index="2500"
    :close-btn="true"
    @close="onClose"
    attach="body"
    :size="'472px'"
  >

    <!-- <template #closeBtn>

      <iconpark-icon name="iconerror" style="font-size: 24px; color: #516082;"></iconpark-icon>
    </template> -->
    <div class="toBodys">
      <!-- <t-alert theme="info">
					<template #message>
						<span>批量调整后原来的角色都会被替换为新的角色 </span>
					</template>
				</t-alert> -->
      <div class="form formSet">
        <t-form
          ref="form"
          :data="formData"
          label-align="top"
          :label-width="60"
          :rules="rules"
        >
          <t-form-item :label="$t('member.impm.quick_9')" name="name">
            <t-input
              v-model="formData.name"
              :maxlength="50"
              :placeholder="t('member.impm.input_1')"
            />
          </t-form-item>
          <t-form-item :label="t('member.impm.input_2')" name="level_name">
            <t-input
              v-model="formData.level_name"
              :maxlength="50"
              :placeholder="t('member.impm.input_1')"
            />
          </t-form-item>
          <!-- <t-form-item :label="'币种'" name="currency">

            <t-select v-replace-svg
              v-model="formData.currency"
              :options="optionsCurrency"
              placeholder="请选择"
            />
          </t-form-item> -->

          <t-form-item :label="t('member.impm.input_3')" name="money">
            <div class="inputFlex">
              <t-input
                v-model="formData.money"
                :maxlength="50"
                :placeholder="t('member.impm.input_1')"
              />
              <span class="tips mt-6"> {{ t('member.impm.input_4') }} </span>
            </div>
          </t-form-item>
          <t-form-item :label="t('member.impm.input_5')" name="remark" style="margin-bottom: 4px">
            <t-textarea
              v-model="formData.remark"
              :maxlength="500"
              :placeholder="t('member.impm.input_1')"
            />
          </t-form-item>
          <t-form-item :label="t('member.impm.input_6')" name="show_status">
            <t-radio-group
              v-model="formData.show_status"
              :options="showStatusOptions"
            />
          </t-form-item>

          <t-form-item :label="t('member.impm.input_7')" name="status">
            <t-radio-group v-model="formData.status" :options="statusOptions" />
          </t-form-item>
        </t-form>
      </div>
    </div>
    <template #footer>
      <div class="footer">
        <t-button
          theme="default"
          variant="outline"
          class="min-w-80px"
          @click="onClose"
        >{{ t('member.impm.input_8') }}</t-button>
        <t-button theme="primary"  class="min-w-80px" :loading="loading" @click="onSave">{{ t('member.impm.input_9') }}</t-button>
      </div>
    </template>
  </t-drawer>

  <!-- <t-dialog
    :visible="false"
    :header="autoHeaderText"
    class="createUpdate"
    :z-index="2500"
    attach="body"
    width="624px"
  >
    <template #body>
      <div class="toBody">

        <div class="form">
          <t-form
            ref="form"
            :data="formData"
            label-align="top"
            :label-width="60"
            :rules="rules"
          >
            <t-form-item :label="'职务名称'" name="name">
              <t-input
                v-model="formData.name"
                :maxlength="50"
                :placeholder="'请输入'"
              />
            </t-form-item>
            <t-form-item :label="'对应会员级别'" name="level_name">
              <t-input
                v-model="formData.level_name"
                :maxlength="50"
                :placeholder="'请输入'"
              />
            </t-form-item>
            <t-form-item :label="'币种'" name="currency">


              <t-select v-replace-svg
                v-model="formData.currency"
                :options="optionsCurrency"
                placeholder="请选择"
              />
            </t-form-item>

            <t-form-item :label="'会费'" name="money">
              <div class="inputFlex">
                <t-input
                  v-model="formData.money"
                  :maxlength="50"
                  :placeholder="'请输入'"
                />
                <span class="tips mt-6"> 设为 0元时无需缴纳会费 </span>
              </div>
            </t-form-item>
            <t-form-item :label="'描述'" name="remark">
              <t-textarea
                v-model="formData.remark"
                :maxlength="500"
                :placeholder="'请输入'"
              />
            </t-form-item>
            <t-form-item :label="'入会展示状态'" name="show_status">
              <t-radio-group
                v-model="formData.show_status"
                :options="showStatusOptions"
              />
            </t-form-item>

            <t-form-item :label="'使用状态'" name="status">
              <t-radio-group
                v-model="formData.status"
                :options="statusOptions"
              />
            </t-form-item>
          </t-form>
        </div>
      </div>
    </template>
    <template #closeBtn>

      <svg class="iconpark-icon" style="width: 16px; height: 16px">
        <use href="#close" />
      </svg>
    </template>
    <template #footer>
      <div class="footer">
        <t-button
          theme="default"
          variant="outline"
          @click="onClose"
        >取消</t-button>
        <t-button theme="primary" @click="onSave">确定</t-button>
      </div>
    </template>
  </t-dialog> -->
</template>

<script lang="ts" setup>
/**
 * @description 批量调整角色弹层
 * <AUTHOR>
 */
import { debounce } from "lodash";

import { ref, reactive, Ref, computed, toRaw } from "vue";
import { MessagePlugin } from "tdesign-vue-next";
import {
  createMemberJobsAxios,
  editMemberJobsAxios
} from "@renderer/api/uni/api/businessApi";
import { getResponseResult, priceRecovery } from "@renderer/utils/myUtils";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const type = ref(0); // 0创建 1编辑

const form = ref(null);
const rules = {
  name: [
    {
      required: true,
      message: t('member.impm.input_10'),
      type: "error",
      trigger: "blur"
    }
  ],

  level_name: [
    {
      required: true,
      message: t('member.impm.input_11'),
      type: "error",
      trigger: "blur"
    }
  ],
  // currency: [
  //   {
  //     required: true,
  //     message: "请选择币种",
  //     type: "error",
  //     trigger: "blur"
  //   }
  // ],
  money: [
    {
      required: true,
      message: t('member.impm.input_1'),
      type: "error",
      trigger: "blur"
    }
  ],
  show_status: [
    {
      required: true,
      message: t('member.impm.input_12'),
      type: "error",
      trigger: "blur"
    }
  ],
  status: [
    {
      required: true,
      message: t('member.impm.input_12'),
      type: "error",
      trigger: "blur"
    }
  ],
  remark: [
    {
      required: false,
      message: t('member.impm.input_1'),
      type: "error",
      trigger: "blur"
    }
  ]
};
const visible = ref(false);
const optionsCurrency = [
  { label: "CNY", value: "CNY" },
  { label: "MOP", value: "MOP" },
  { label: "HKD", value: "HKD" }
];

const showStatusOptions = [
  { label: "可选", value: 1 },
  { label: "不可选", value: 2 }
];
const statusOptions = [
  { label: "停用", value: 1 },
  { label: "启用", value: 2 }
];
const zone = JSON.parse(
        window.localStorage.getItem("profile")
      ).area;
const formData = ref({
  name: "", // 职务名称
  level_name: "", // 对应会员级别
  money: 0, // 缴费金额，单位：元
  currency: zone === 'CN'? 'CNY': 'MOP', // 币种
  remark: "", // 描述
  status: 2, // 使用状态，1：停用，2：正常 默认启用
  show_status: 1 // 入会展示状态 1：展示，2：隐藏
});

const initForm = () => {
  formData.value = {
    id: 0,
    name: "", // 职务名称
    level_name: "", // 对应会员级别
    money: 0, // 缴费金额，单位：元
    currency: zone === 'CN'? 'CNY': 'MOP', // 币种
    remark: "", // 描述
    status: 2, // 使用状态，1：停用，2：正常 默认启用
    show_status: 1 // 入会展示状态 1：展示，2：隐藏
  };
};

const autoHeaderText = computed(() => (type.value ? "编辑职务" : "新建职务"));

// const props = defineProps({
// 	type: {
// 		type: Number,
// 		default: 0,
// 	}, // 0创建 1编辑
// });

const emits = defineEmits(["reload"]);
const loading= ref(false);
const onSave = debounce(() => {
  form.value
    .validate({ showErrorMessage: true })
    .then(async (validateResult) => {
      if (validateResult && Object.keys(validateResult).length) {
        console.log("abcd", validateResult);
      } else {
        const params = {
          ...toRaw(formData.value)
        };
        params.money = priceRecovery(params.money);
        let res = null;
        loading.value = true;
        try {
          if (!type.value) {
            res = await createMemberJobsAxios(params);
          } else {
            res = await editMemberJobsAxios(formData.value?.id, params);
          }
          res = getResponseResult(res);
          loading.value = false;

          if (!res) return;
          MessagePlugin.success("操作成功");
          setTimeout(() => {
            onClose();
            emits("reload");
          }, 500);
        } catch (error) {
          MessagePlugin.error(error.message);
        }
        loading.value = false;

      }
    });
}, 500);
/**
 *
 * @param data 值不为空说明为编辑状态
 */
const onOpen = (data?: any) => {
  if (data) {
    type.value = 1;
    // formData.name = data.name;
    // formData.short_name = data.short_name;
    // formData.id = data.id;
    form.value.reset();
    data.money = Number(data.money / 100);
    formData.value = data;
  } else {
    type.value = 0;
    // form.value.reset();
    initForm();
  }
  visible.value = true;
};
const onClose = () => {
  visible.value = false;
};

defineExpose({
  onOpen,
  onClose
});
</script>

<style lang="less" scoped>
@import "@renderer/views/engineer/less/common.less";
.t-alert--info {
  padding: 8px 16px;
}
.form {
}

// :deep(.t-dialog__body) {
//   overflow: hidden !important;
//   padding-bottom: 0;
// }

.inputFlex {
  display: flex;
  flex-direction: column;
  width: 100%;
  .tips {
    color: var(--text-kyy-color-text-2, #516082);

    /* kyy_fontSize_1/regular */
    font-family: PingFang SC;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px; /* 166.667% */
  }
}
.toBodys {
  // padding: 0 8px;
}

:deep(.t-form__item) {
  margin-bottom: 24px;
}

.t-dialog__ctx .t-dialog__position {
  padding: 0;
}

// :deep(.t-dialog--default) {
//   padding: 0 !important;
// }
</style>
<style lang="less" scoped>
@import "@renderer/views/engineer/less/common.less";

.createUpdate {
  .toBodys {
    padding: 0 8px;
  }
  .t-dialog__header {
    padding: 0 24px;
  }
  .t-dialog__footer {
    padding: 0 24px;
  }

  .t-dialog--default {
    padding-left: 0;
    padding-right: 0;
  }
}
.footer {
  display: flex;
  justify-content: flex-end;
}


</style>
