<template>
  <div class="box-flex">
    <div class="right-box" style="background: #FFF;">
      <div class="right-box-head">
        <span class="addzj">{{t('banch.endaddapp')}}</span>
        <t-button @click="openAddModuleDialogRef">
          <template #icon><add-icon /></template>
          {{t('banch.addzj')}}
        </t-button>
      </div>
      <div class="right-box-content">
        <div class="right-box-item flex-j-s" v-for="(item, index) in allAppList" :key="index">
          <!-- workbench1.0 -->
          <!-- <div class="flex-a">
            <img :src="getLogo(item.module.uuid)" />
            <div>
              <div class="title-box">
                <div class="title">{{ item.module.uuid === "app" ? t('banch.allapp') : '公告'}}</div>
                <div class="title-tag">{{ item.type === 1 ?t('banch.allsee') :t('banch.bfsee') }}</div>
              </div>
              <div class="app-tips">
                {{ item.module.uuid === "app" ?t('banch.groupapp') : t('banch.groupgga') }}
              </div>
            </div>
          </div> -->
          <!-- workbench1.2迭代 -->
          <div class="flex-a">
            <img v-lazy="item.module.logo" />
            <div>
              <div class="title-box">
                <div class="title">{{item.module.name}}</div>
                <div class="title-tag" :class='item.type === 1 ?"":"notAllSee"'>{{item.type === 1 ? t('banch.allsee') :
                  t('banch.bfsee')}}</div>
              </div>
              <div class="app-tips">
                {{item.module.intro}}
              </div>
            </div>
          </div>
          <div class="btn" v-if="item.module.uuid !== 'app'">
            <span @click="openSeting(item)">{{t('banch.set')}}</span>
            <span @click="removeApp(item)">移除</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 组件设置 -->
    <t-dialog v-model:visible="moduleSeetingDialog" :close-btn="false" :header="true" :footer="true" width="480">
      <template #header>
        <div style="display: flex; align-items: center; width: 100%; justify-content: space-between">
          <div @click="moduleSeetingDialog = true">{{t('banch.appsetting')}}</div>
          <img style="width: 24px; cursor: pointer; height: 24px" src="@/assets/<EMAIL>"
            @click="moduleSeetingDialog = false" />
        </div>
      </template>
      <div class="content-box">
        <div class="item-box" style="width: 100%; margin-bottom: 4px">
          <span class="title-box-seeting">{{t('banch.appname')}}</span>

          <!-- <span class="value-box-seeting">{{ templateData?.module.uuid === "app" ? t('banch.allyy') :'公告' }}</span> -->
          <span class="value-box-seeting">{{ templateData?.module.name }}</span>
        </div>
        <div class="item-box" style="width: 100%; margin-bottom: 4px">
          <span class="title-box-seeting">{{t('banch.apptip')}}</span>
          <!-- <span class="value-box-seeting">{{
            templateData?.module.uuid === "app" ? t('banch.groupapp') : t('banch.groupgga')
          }}</span> -->
          <span class="value-box-seeting">{{ templateData?.module.intro }}</span>
        </div>
        <div class="item-box" style="width: 100%; margin-bottom: 4px">
          <span class="title-box-seeting">{{t('banch.apply')}}</span>
          <span class="value-box-seeting">{{ templateData?.module.app_name }}</span>
        </div>
        <div class="item-box" style="width: 100%; margin-bottom: 4px">
          <span class="title-box-seeting">{{t('banch.syfw')}}
            <t-tooltip :content="t('banch.ccappbsx')">
              <img style="margin-left: 4px" src="@/assets/bench/icon_attention.svg" />
            </t-tooltip>
          </span>
          <span class="value-box-seeting">
            <t-radio-group style="display: block" :default-value="templateData?.type" v-model="isShowFlag">
              <t-radio style="display: block" :value="1">{{t('banch.allkj')}}</t-radio>

              <t-radio v-if="templateData.module?.uuid!='activities'" style="display: block"
                :value="2">{{t('banch.bfkj')}}</t-radio>
            </t-radio-group>
            <t-tag-input v-show="isShowFlag === 2" excess-tags-display-type="break-line" v-model="members"
              :clearable="false" readonly class="tag-list-box" placeholder="">
              <template #valueDisplay="{ value, onClose }">
                <div class="tag-list">
                  <t-button theme="default" @click="addnumber" size="medium" variant="dashed" style="color:#1A2139;">
                    + {{t('banch.addlxr')}}
                  </t-button>
                  <t-tag v-for="(item, index) in selectedActorsList" :key="item" closable style="margin-right: 4px"
                    @close="() => tagClose(item)">
                    <span class="displayItem">
                      <avatar avatarSize="24px" :image-url="item.avatar ?? ''" :user-name="item.name"
                        :round-radius="true" />
                      <span style="margin-left: 4px">{{ item.name }}</span>
                    </span>
                  </t-tag>
                </div>
              </template>
            </t-tag-input>
          </span>
        </div>
      </div>
      <template #footer>
        <div style="display: flex; align-items: center; width: 100%; justify-content: flex-end">
          <t-button class="min-80" theme="default" variant="outline" @click="moduleSeetingDialog = false">
            取消
          </t-button>
          <t-button class="min-80" @click="confirmAdmin">
            {{ t("home.openPreloadWindowError.confirm") }}
          </t-button>
        </div>
      </template>
    </t-dialog>
    <!-- 选择可見 -->

    <!-- :is-admin-del="true" -->
    <approval-organize-select-modal ref="selectPersonnelsRef" :radio-flag="false" :is-only="false"
      :team-id="activationGroupItem.teamId" :header="t('approval.add_user')" :close-on-overlay-click="false"
      :show-modal-list-index-array="[1]" :is-filter="true" :selected="selectedActorsList.map((item) => item.idStaff)"
      :is-department="false" :only-project="true" @on-filter-select-persons="onSelectItem" />
    <addModuleDialog ref="addModuleDialogRef" @getList="getList"></addModuleDialog>
  </div>
</template>
<script setup>
  import allapp from "@/assets/bench/allapp.svg";
  import allapp1 from "@/assets/bench/allapp1.svg";
  import culturalWall from "@/assets/bench/cultureWall.svg";
  import { ref, onMounted, computed, watch, nextTick, onUnmounted } from "vue";
  import avatar from "@renderer/components/kyy-avatar/index.vue";
  import { useI18n } from "vue-i18n";
  import { AddIcon } from "tdesign-icons-vue-next";
  import { DialogPlugin, MessagePlugin } from "tdesign-vue-next";
  import selectMember from "@renderer/components/selectMember/audio-add-members.vue";
  // import SelectPersonnel from "@renderer/views/engineer/components/select-personnel/index.vue";

  import { workshopteam, teamdelapp, adminAppStaff, workshopTeamSet } from "@renderer/api/workBench/index.ts";
  import approvalOrganizeSelectModal from "@renderer/views/approve/approve_home/components/approvalOrganizeSelectModal.vue";

  import addModuleDialog from "../components/addModuleDialog.vue";
  const moduleSeetingDialog = ref(false);
  const addModuleDialogRef = ref(null);
  const selectPersonnelsRef = ref(null);
  const addAdminData = ref([]);
  const selectedActorsList = ref([]);
  const disabledArray = ref([]);

  const selected = ref([]);
  const { t } = useI18n();

  const isShowFlag = ref(1);
  const templateData = ref(null);
  const props = defineProps({
    activationGroupItem: {
      type: Object,
      default: () => { },
    },
  });
  const auditList = ref([])
  const onSelectItem = (val) => {
    selectedActorsList.value = val;
    console.log(val, '啊實打實');

  }
  const confirmAdmin = () => {
    let objs = {
      id: templateData.value.id,
      type: isShowFlag.value,
    };

    if (isShowFlag.value === 2) {
      objs.users = selectedActorsList.value.map((e) => e.idStaff);
    }
    workshopTeamSet(objs, props.activationGroupItem.teamId)
      .then((res) => {
        getList()
        moduleSeetingDialog.value = false
        MessagePlugin.success("操作成功");

        console.log(res, "ssssssssssss");
      })
      .catch((error) => {
        MessagePlugin.error(error.response.data.message);
      });
    console.log(templateData.value, "templateDatatemplateDatatemplateData");
    console.log(selectedActorsList.value, "selectedActorsListselectedActorsList");
  };
  const onSelectItems = (val, val1111) => {
    console.log(val, "vaaaaaaaaa");
    disabledArray.value = val.map((e) => e.staffId);
    selectedActorsList.value = val;
  };

  watch(
    () => moduleSeetingDialog.value,
    (val) => {
      console.log(val, "valllllllllll");
      if (!val) {
        isShowFlag.value = 1;
        selectedActorsList.value = [];
        disabledArray.value = [];
        getList()

      }
    },
  );


  const getLogo = (uuid) => {
    // item.module.uuid === 'app' ? allapp1 : allapp
    if (uuid === 'app') {
      return allapp1;
    } else if (uuid === 'culturalWall') {
      return culturalWall;
    } else {
      return allapp;
    }
  }

  const getModule = (uuid, type) => {

    const obj = {
      logo: '',
      title: '',
      tag: type === 1 ? t('banch.allsee') : t('banch.bfsee'),
      tips: '',
    };
    if (uuid === 'app') {
      obj.logo = allapp1;
      obj.title = t('banch.allapp');
      obj.tips = t('banch.groupapp');
    } else if (uuid === 'culturalWall') {
      obj.logo = culturalWall;
      obj.title = '文化墙';
      obj.tips = '组织文化宣传';

    } else {
      obj.logo = allapp;
      obj.title = '公告';
      obj.tips = t('banch.groupgga');
    }
    return obj;
  }


  let addnumberFlag = false;

  const openSeting = (item) => {
    templateData.value = item;
    isShowFlag.value = item.type;
    disabledArray.value = item.users;
    // 邏輯漏洞
    adminAppStaff(props.activationGroupItem.teamId)
      .then((res) => {
        addnumberFlag = false;
        for (let index = 0; index < res.data.data.length; index++) {
          const element = res.data.data[index];
          element.staffId = element.idStaff;
          for (let index1 = 0; index1 < item.users.length; index1++) {
            const element1 = item.users[index1];
            if (element.idStaff === element1) {
              selectedActorsList.value.push(element);
            }
          }
        }
        addAdminData.value = res.data.data;
      })
      .catch((error) => {
        addnumberFlag = false;
        MessagePlugin.error(error.response.data.message);
      });
    moduleSeetingDialog.value = true;
  };
  const addnumber = () => {
    selectPersonnelsRef.value.onOpen();
    return
    adminAppStaff(props.activationGroupItem.teamId)
      .then((res) => {
        console.log(res, "resssssssssss");
        addnumberFlag = false;
        for (let index = 0; index < res.data.data.length; index++) {
          const element = res.data.data[index];
          element.staffId = element.idStaff;
        }
        addAdminData.value = res.data.data;
        auditList.value = res.data.data;

        // selectPersonnelsRef.value.setvalue(templateData.value?.users);
      })
      .catch((error) => {
        addnumberFlag = false;
        MessagePlugin.error(error.response.data.message);
      });
  };

  const members = ref([
    {
      avatar: "",
      name: "sl",
    },
  ]);
  const rowData = ref(null);
  watch(
    () => props.activationGroupItem.teamId,
    (newValue) => {
      getList();
    },
  );

  const allAppList = ref([]);
  const getList = () => {
    workshopteam(props.activationGroupItem.teamId).then((res) => {
      allAppList.value = res.data.data;
      console.log(res, "resssssssssss");
    });
  };
  if (props.activationGroupItem.teamId) {
    getList();
  }
  const openAddModuleDialogRef = () => {
    addModuleDialogRef.value.openWin(props.activationGroupItem.teamId);
  };
  const removeApp = (item) => {
    const myDialog = DialogPlugin({
      header: "确定移除",
      theme: "info",
      body: "确定要移除组件吗?",
      className: "t-dialog-new-class1 t-dialog-new-class2",
      confirmBtn: "移除",
      onConfirm: () => {
        teamdelapp(item.id, props.activationGroupItem.teamId)
          .then((res) => {
            MessagePlugin.success("操作成功");
            getList();
          })
          .catch((error) => {
            MessagePlugin.error(error.response.data.message);
          });
        myDialog.hide();
      },
      onCancel: () => {
        myDialog.hide();
      },
    });
  };
  const tagClose = (item) => {
    console.log(item, "itemmmmmmmmmm");
    const indexs = selectedActorsList.value.find((e) => e.id == item.id);
    selectedActorsList.value.splice(indexs, 1);
    disabledArray.value.splice(indexs, 1);
  };
  const administratorOrenterprise = ref(false);
</script>

<style lang="less" scoped>
  .tag-list-box {
    margin-top: 4px;

    :deep(.t-input.t-size-l) {
      height: auto !important;
    }

    :deep(.t-input__inner) {
      display: none;
    }

    :deep(.t-input) {
      padding-right: 0 !important;
      padding-left: 0 !important;
    }
  }

  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  /* 滚动条滑块 */
  ::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background: rgba(0, 0, 0, 0.1);
    -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.5);
  }

  .title-box-seeting {
    color: var(--text-kyy_color_text_3, #828da5);
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    /* 157.143% */
    width: 80px;
    margin-right: 16px;
    display: flex;
    align-items: center;
  }

  .value-box-seeting {
    color: var(--text-kyy_color_text_1, #1a2139);
    width: 336px;
    font-size: 14px;
    font-style: normal;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    word-break: break-all;

    font-weight: 400;
    line-height: 22px;
    /* 157.143% */
  }

  .tag-list {
    overflow: auto;
    display: flex;
    flex-wrap: wrap;
    max-height: 330px;
    min-height: 40px;
    align-items: center;
    gap: 8px;
    padding: 5px 12px;
  }

  .item-box {
    display: flex;
    align-items: baseline;
  }

  .right-box-head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
  }

  .flex-a {
    display: flex;
    align-items: center;
  }

  .flex-j-s {
    display: flex;
    justify-content: space-between;
  }

  .btn {
    display: flex;
    align-items: center;
    gap: 16px;
    cursor: pointer;

    span {
      color: var(--color-button_text_brand-kyy_color_button_text_brand_font_default, #4d5eff);
      text-align: center;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      /* 157.143% */
    }

    span:hover {
      color: #707eff;
    }
  }

  :global(.t-tag .displayItem) {
    display: flex;
    align-items: center;

    span {
      margin-left: 4px;
    }
  }

  :deep(.t-tag-input) {
    .t-tag {
      margin: 0;
      min-height: 32px;
    }
  }

  .right-box-content {
    height: calc(100% - 40px);
    overflow: auto;

    .right-box-item {
      border-radius: 8px;
      background: var(--bg-kyy_color_bg_deep, #f5f8fe);
      display: flex;
      padding: 8px 12px;
      align-items: center;
      align-self: stretch;
      margin-bottom: 8px;

      img {
        width: 36px;
        height: 36px;
        margin-right: 12px;
      }

      .title-box {
        display: flex;
        align-items: center;

        .title {
          color: var(--text-kyy_color_text_1, #1a2139);
          font-size: 14px;
          margin-right: 4px;
          font-style: normal;
          font-weight: 600;
          line-height: 22px;
          /* 157.143% */
        }

        .notAllSee {
          background: var(--kyy_color_tag_bg_kyyBlue, #E4F5FE) !important;
          color: #21ACFA !important;
        }

        .title-tag {
          border-radius: var(--kyy_radius_tag_full, 999px);
          background: var(--kyy_color_tag_bg_success, #e0f2e5);
          display: flex;
          height: 20px;
          min-height: 20px;
          max-height: 20px;
          padding: 1px 8px;
          justify-content: center;
          align-items: center;
          gap: 10px;
          color: var(--kyy_color_tag_text_success, #499d60);
          text-align: right;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
        }

        .app-tips {
          color: var(--text-kyy_color_text_3, #828da5);
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          line-height: 20px;
          /* 166.667% */
        }
      }
    }
  }

  .addzj {
    color: var(--text-kyy_color_text_1, #1a2139);
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
    /* 150% */
  }

  .right-box {
    padding: 16px 16px 0;
    width: 100%;
  }

  .items {
    cursor: pointer;
    height: 48px;
    background: #fff;
    border-radius: 4px;
    margin-bottom: 16px;
    padding: 5px 16px;
    display: flex;
    align-items: center;
    color: var(--lingke-black-90, #1a2139) !important;
    font-weight: 600;

    img {
      width: 20px;
      height: 20px;
      margin-right: 8px;
    }
  }

  .items:hover {
    background: var(--lingke-select, #e1eaff) !important;
  }

  .isactive {
    background: var(--bg-kyy-color-bg-list-foucs, #e1eaff) !important;

    color: #2069e3 !important;
  }

  .head-text {
    font-size: 16px;

    font-weight: 700;
    color: #13161b;
    padding: 20px 0 20px 16px;
    line-height: 24px;
  }

  .box-flex {
    display: flex;
    flex: 1;
    width: 100%;
    background: #fff;
    height: 100%;
  }
</style>
