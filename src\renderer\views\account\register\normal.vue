<template>
  <div class="h-full px-24 py-32 relative">
    <loading-percent
      v-if="showStep === 4"
      ref="loadingPer"
      @login-err="showStep = 1"
      :isMoreAccount="props.isMoreAccount"
    />

    <div class="h-full" v-if="showStep === 1">
      <div class="inline-flex items-center gap-4 cursor-pointer" @click="goBack">
        <iconpark-icon class="text-20 text-[#828DA5]" name="iconarrowlift"></iconpark-icon>
        <span class="text-[#516082]">{{ t('account.back') }}</span>
      </div>
      <div class="info">
        <div class="text-20 text-[#1A2139] font-bold leading-28 text-center">{{ t('account.registerkyy') }}</div>
        <div class="mt12">
          <div class="select-area mb12">
            <t-select v-replace-svg v-model="registerParams.region" @change="regionChange">
              <t-option v-for="item in [...countList, {code: 'OTHER', name: t('square.annualFee.other')}]" :key="item.code" :value="item.code" :label="item.name"></t-option>
            </t-select>
            <div class="select-area-tips">
              <span>{{ t('account.region') }}</span>
              <iconpark-icon
                class="text-[#828DA5] text-20 cursor-pointer"
                name="iconhelp"
                @click="showRegisterTips"
              ></iconpark-icon>
            </div>
          </div>
          <t-form
            ref="registerFormRef"
            class="register-form"
            label-align="top"
            :data="registerParams.mobile"
            resetType="initial"
          >
            <t-form-item name="mobile">
              <div class="adornment-box">
                <t-input-adornment>
                  <template #prepend>
                    <div>
                      <area-code
                        v-model="registerParams.mobile.region"
                        :isMO="registerParams.region === 'MO'"
                        :disabled="registerParams.region !== 'OTHER'"
                        class="login-area-code"
                        @change="errText = null"
                        :placeholder="t('account.areaCode')"
                        popupClassName="area-code-select-popup"
                      />
                    </div>
                  </template>
                </t-input-adornment>
                <div class="flex items-center">
                  <div class="h-16 w-1 bg-[#ECEFF5]"></div>
                  <t-input class="phone-input" v-model="registerParams.mobile.mobile" :placeholder="t('account.inputTel')" clearable @change="errText = null" />
                </div>
              </div>
            </t-form-item>
          </t-form>
          <t-input v-model="registerParams.mobile.code" class="mt12 input-code" :placeholder="t('account.inputCode')" clearable @change="errText = null">
            <template #suffix>
              <div style="display: flex; align-items: center">
                <div class="suffix--line" />
                <div v-if="countdown <= 0" class="verify pointer" aria-role="button" @click="checkSM">
                  {{ t('account.sendCode') }}
                </div>
                <div v-else class="verify">{{ `${countdown}s` }}</div>
              </div>
            </template>
          </t-input>
        </div>

        <div v-if="errText" class="mt-4 text-12 leading-20 text-[#D54941]">{{ errText }}</div>

        <div class="mt-24">
          <t-checkbox class="mb-12 protocol-checkbox" v-model="isProtocolChecked">
            {{ t('account.read') }}<span class="text-[#4D5EFF]" @click.stop.prevent="showIframe('PlatformServices')">《{{ t('account.agreement1') }}》</span
            >和<span class="text-[#4D5EFF]" @click.stop.prevent="showIframe('PrivacyPolicy')">《{{ t('account.privacy') }}》</span>
          </t-checkbox>
        </div>

        <t-button
          v-loading="loginLoading"
          class="login-btn"
          :disabled="loginBtnDisabled"
          block
          theme="primary"
          variant="base"
          @click="registerAcc"
          >{{ t('account.registerLogin') }}</t-button
        >
      </div>
    </div>

    <set-phone ref="setPhoneDom" v-if="showStep === 2" @back="setBack" @confirm="getPhone" />
    <set-password
      ref="setPwDom"
      v-if="showStep === 3"
      :show-skip="true"
      :btn-confirm="t('zx.other.confirm')"
      @back="setBack"
      @confirm="setPw"
      @skip="loginSuc"
    />
  </div>

  <tip
    v-model:visible="protocolTipVisible"
    :btn-cancel="t('account.reject')"
    :btn-confirm="t('account.agree')"
    @onconfirm="onProtocolTipConfirm"
  >
    <template #content>
      {{ t('account.read') }}<span class="text-[#4D5EFF]" @click.stop="showIframe('PlatformServices')">《{{ t('account.agreement1') }}》</span>和<span
        class="text-[#4D5EFF]"
        @click.stop="showIframe('PrivacyPolicy')"
        >《{{ t('account.privacy') }}》</span
      >
    </template>
  </tip>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';
import tip from '@renderer/views/setting/dialog/tip.vue';
import setPhone from './setPhone.vue';
import setPassword from './setPassword.vue';
import { ref, watch, nextTick, onActivated, onMounted } from 'vue';
import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next';
import {loginSucSetLocal, setAutoLogin, setOrganizationTips, setRegister, setSMDeviceId} from '@renderer/utils/auth';
import {
  getIdentifyCode,
  resetPassword,
  loginAccountV2,
  createAccount, accountExist,
} from '@renderer/api/account';
import { useI18n } from 'vue-i18n';
import loadingPercent from '../../../components/account/Loading.vue';
import areaCode from '../../../components/account/AreaCode.vue';
import { err_reason, noMatchCountry, packageArea, countList, allMatchCountry } from '../constant';
import { checkPhoneAndMatch } from '@renderer/components/account/util';
import { initSM, dealSmDeviceId, getSMCaptcha, getSMCaptchaResult } from '@renderer/utils/shumei';
import { logHandler } from '@/log';
import { encrypt } from "@/utils/myUtils";
import { isEmpty } from "lodash";
import LynkerSDK from '@renderer/_jssdk'

const { ipcRenderer } = LynkerSDK;
const router = useRouter();

const { t } = useI18n();

const isProtocolChecked = ref(false);
const errText = ref(null);
const loginBtnDisabled = ref(true);

const registerFormRef = ref(null);

const registerParams = ref({
  mobile: {
    region: '86',
    mobile: '',
    code: '',
  },
  email: {
    mail: '',
    code: '',
    password: '',
    region: '',
    mobile: '',
    mobile_code: '',
  },
  region: packageArea,
});
const countdown = ref(0);
const loginLoading = ref(false);
const protocolTipVisible = ref(false);
const showStep = ref(1);
const loadingPer = ref(null);
const timer = ref(null);
const checkPhoneTip = ref('');
const setPhoneDom = ref(null);
let checkRegion = 0;

const props = defineProps({
  // 多账号使用
  isMoreAccount: {
    type: Boolean,
    default: false,
  },
});
const emits = defineEmits(['goLogin']);

const regionChange = (val) => {
  registerParams.value.mobile.region = allMatchCountry.get(val)?.code ;
};

const goBack = () => {
  document.removeEventListener('keydown', keyDown);
  if (props.isMoreAccount) {
    emits('goLogin');
  } else {
    router.go(-1);
  }
};

const setBack = () => {
  countdown.value = 0;
  registerParams.value.email.code = '';
  showStep.value = 1;
};

const showRegisterTips = () => {
  const confirmDia = DialogPlugin.alert({
    header: '提示',
    body: t('zx.account.registerTip'),
    theme: 'info',
    confirmBtn: '知道了',
    closeBtn: null,
    onConfirm: () => {
      confirmDia.hide();
    },
    onClose: () => {
      confirmDia.hide();
    },
  });
};

onActivated(() => {
  document.addEventListener('keydown', keyDown);
});

const checkSM = async () => {
  if(isEmpty(registerParams.value.mobile.mobile)){
    registerFormRef.value.setValidateMessage({ mobile: [{ type: 'error', message: t('account.inputTel') }] });
    return;
  }

  const checkRegion = checkPhoneAndMatch(
    +registerParams.value.mobile.region,
    registerParams.value.mobile.mobile,
  );
  if(!checkRegion){
    registerFormRef.value.setValidateMessage({ mobile: [{ type: 'error', message: t('zx.account.phoneIllegalNew') }] });
    return;
  }

  if (!SMCaptcha.value) {
    return;
  }

  const res = await accountExist({ acc: registerParams.value.mobile.mobile});
  if(res.data.data.done){
    registerFormRef.value.setValidateMessage({ mobile: [{ type: 'error', message: t('zx.account.userAlreadyExist') }] });
    return;
  }

  getSMCaptchaResult(SMCaptcha.value, getCode);
};

const SMCaptcha = ref(null);
onMounted(async () => {
  await initSM();
  try {
    SMCaptcha.value = await getSMCaptcha({ width: 300 });
    console.error(SMCaptcha.value);
  } catch (error) {
    console.error(error);
  }
});

const keyDown = (e) => {
  if (e.keyCode === 13) {
    !loginBtnDisabled.value && showStep.value === 1 && registerAcc();
  }
};

const onProtocolTipConfirm = () => {
  protocolTipVisible.value = false;
  isProtocolChecked.value = true;

  registerAcc();
};

const showIframe = (uuid) => {
  ipcRenderer.invoke('create-iframe', uuid);
};

const getPhone = (v) => {
  registerParams.value.email.region = v.region;
  registerParams.value.email.mobile = v.mobile;
  registerParams.value.email.mobile_code = v.code;
  registerAcc();
};

const changeRegion = () => {
  registerParams.value.mobile.region = checkRegion.toString();
  registerParams.value.mobile.code ? registerAcc() : getCode();
};

const handleTrim = (data) => {
  for (let key in data) {
    if (typeof data[key] === 'string') {
      data[key] = data[key].trim();
    }
  }
  return data;
};
const registerAcc = () => {
  dealSmDeviceId(async (deviceId) => {
    const region = registerParams.value.region;
    console.log('回调执行成功，设备标识为：' + deviceId);
    const checkRegion = checkPhoneAndMatch(
      +registerParams.value.mobile.region,
      registerParams.value.mobile.mobile,
    );
    if(!checkRegion){
      registerFormRef.value.setValidateMessage({ mobile: [{ type: 'error', message: t('zx.account.phoneIllegalNew') }] });
      return;
    }

    if (!isProtocolChecked.value) {
      protocolTipVisible.value = true;
      return;
    }

    const res = await accountExist({ acc: registerParams.value.mobile.mobile});
    if(res.data.data.done){
      registerFormRef.value.setValidateMessage({ mobile: [{ type: 'error', message: t('zx.account.userAlreadyExist') }] });
      return;
    }

    setSMDeviceId(deviceId);
    registerParams.value.mobile = handleTrim(registerParams.value.mobile);
    registerParams.value.email = handleTrim(registerParams.value.email);

    loginLoading.value = true;
    loginBtnDisabled.value = true;
    let regionCode = 'CN';
     if (region === 'MO') {
      regionCode = 'MO';
    } else {
      regionCode = 'CN';
    }
    debugger
    const params = {
      mobile: registerParams.value.mobile,
      app: 'RINGKOL',
      from: 'PC_China',
      platform: 'PC',
      region: regionCode,
    }
    createAccount(params)
      .then((res: any) => {
        if (res.status === 200) {
          loginSucSetLocal(res.data.data);
          setRegister('true');
          // 注册新账号把之前的组织弹框标识清空，只要是新账号就弹
          setOrganizationTips('false');

          setAutoLogin(false);

          showStep.value = 3;
          loginLoading.value = false;
          loginBtnDisabled.value = false;
        }
      })
      .catch((err) => {
        loginLoading.value = false;
        loginBtnDisabled.value = false;
        const reason = err.response.data.reason;
        timer.value && clearInterval(timer.value);
        countdown.value = 0;
        setPhoneDom.value && setPhoneDom.value.clearCount();
        if (err.response.status === 418) {
          return;
        }
        if (err_reason[reason]) {
          errText.value = err_reason[reason];
        } else {
          MessagePlugin.error({
            content: err.response.data?.message || t('zx.other.requestFail'),
            duration: 3000,
          });
        }
      });
  }).catch((e) => {
    console.error(e);
    MessagePlugin.error('网络繁忙，请稍后尝试重新启动另可');
  });
};

const setPw = (pw) => {
  const resetParams = {
    new: pw.password,
    forgot: true,
  };
  resetPassword(resetParams)
    .then(async () => {
      // 获取设备唯一ID
      const uniqueId = await LynkerSDK.getUniqueId();

      const params = {
        account: {
          account: encrypt(registerParams.value.mobile.mobile),
          password: pw.password,
        },
        info: {
          encode: true,
          app: 'RINGKOL',
          region: 'CN',
          platform: 'PC',
          deviceId: uniqueId,
        }
      }
      // 修改密码需要重新获取token
      dealSmDeviceId(async (deviceId) => {
        console.log('回调执行成功，设备标识为：' + deviceId);
        setSMDeviceId(deviceId);
        logHandler({
          name: 'iam登录',
          info: `params:${JSON.stringify(params)}; extraParams:${JSON.stringify(
            pw.loginParams,
          )}; deviceId:${deviceId}`,
          desc: `${new Date()}; normal.vue=>resetPassword`,
        });
        loginAccountV2(params)
          .then((res: any) => {
            console.log(res, 'loginAccount');
            if (res.status === 200) {
              loginSucSetLocal(res.data.data);
              loginSuc();
            }
          })
          .catch((err) => {
            if (err.response.status === 418) return;
            const reason = err.response.data.reason;
            MessagePlugin.error({
              content: err_reason[reason] || err.response.data?.message || t('zx.other.requestFail'),
              duration: 3000,
            });
          });
      }).catch((e) => {
        console.error(e);
        MessagePlugin.error('网络繁忙，请稍后尝试重新启动另可');
      });
    })
    .catch((err) => {
      const reason = err.response.data.reason;
      MessagePlugin.error({
        content: err_reason[reason] || err.response.data?.message || t('zx.other.requestFail'),
        duration: 3000,
      });
    });
};

const loginSuc = () => {
  showStep.value = 4;
  nextTick(() => {
    loadingPer.value.loadingPercent();
  });
}

const getCode = (data?) => {
  const mobileParams = handleTrim(registerParams.value.mobile);

  // 区分手机和邮箱获取验证码
  const params = {
    typ: 'REGISTER',
    mobile: {
      mobile: mobileParams.mobile,
      region: mobileParams.region,
    },
  }
  if (data) {
    params.captcha = {
      code: data?.rid,
      mode: 'slide',
    };
  }
  getIdentifyCode(params)
    .then((res: any) => {
      if (res.status === 200) {
        countdown.value = 60;
        timer.value = setInterval(() => {
          countdown.value--;
          if (countdown.value <= 0) {
            clearInterval(timer.value);
            timer.value = null;
          }
        }, 1000);
      }
      console.log(res, 'getIdentifyCode');
    })
    .catch((err) => {
      const reason = err.response.data.reason;

      if (err_reason[reason]) {
        errText.value = err_reason[reason];
      } else {
        MessagePlugin.error({
          content: err.response.data?.message || t('zx.other.requestFail'),
          duration: 3000,
        });
      }
    });
};
const checkLoginDisabled = () => {
  loginBtnDisabled.value = !(registerParams.value.mobile.mobile && registerParams.value.mobile.code);
};
const checkAreaRegion = () => {
  const match = noMatchCountry.find((v) => v.area === registerParams.value.region);
  match && (registerParams.value.mobile.region = match.code);
};

watch(
  registerParams,
  (newValue, oldValue) => {
    checkLoginDisabled();
  },
  {
    deep: true,
  },
);
watch(
  () => registerParams.value.region,
  (newValue) => {
    countdown.value = 0;
    checkAreaRegion();
  },
);
</script>

<style lang="less" scoped>
a:hover {
  color: var(--color-button-text-brand-kyy-color-button-text-brand-font-default, #4d5eff);
}
.mt12 {
  margin-top: 12px;
}
.mb12 {
  margin-bottom: 12px;
}
.mt16 {
  margin-top: 16px;
}
.pointer {
  cursor: pointer;
}

.back {
  margin-left: 48px;
  margin-top: 41px;
  position: relative;
  color: var(--text-kyy-color-text-3, #828da5);

  /* kyy_fontSize_2/regular */
  font-family: PingFang SC;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
  &:before {
    content: '';
    width: 5px;
    height: 5px;
    border-top: 2px solid #a1a2a4;
    border-left: 2px solid #a1a2a4;
    position: absolute;
    left: -15px;
    top: 50%;
    transform: translateY(-50%) rotate(-45deg);
  }
}
.logo {
  margin-top: 25px;
  text-align: center;
  img {
    vertical-align: bottom;
    width: 64px;
    height: 64px;
  }
}
.logo-name {
  text-align: center;
  margin-top: 12px;
  margin-bottom: 32px;
  color: var(--text-kyy-color-text-1, #1a2139);
  text-align: center;

  /* kyy_fontSize_4/bold */
  font-family: PingFang SC;
  font-size: 18px;
  font-style: normal;
  font-weight: 600;
  line-height: 26px; /* 144.444% */
}
.suffix--line {
  width: 1px;
  height: 16px;
  background-color: #f6f6f6;
  margin-right: 12px;
}
.verify {
  color: var(--color-button-text-brand-kyy-color-button-text-brand-font-default, #4d5eff);

  /* kyy_fontSize_3/regular */
  font-family: PingFang SC;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px; /* 150% */
}
.info {
  margin-top: 10px;

  :deep(.t-input) {
    height: 40px;
  }

  :deep(.t-input-adornment__prepend) {
    background: #fff;
  }

  .t-form-item__mobile {
    :deep(.t-input) {
      max-width: 188px;
    }
  }

  .select-area {
    position: relative;

    :deep(.t-input__inner) {
      text-align: right;
      margin-right: 4px;
    }

    .select-area-tips {
      position: absolute;
      left: 12px;
      top: 50%;
      transform: translateY(-50%);
      z-index: 1;
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 16px;
      color: #1a2139;
    }
  }
  .login-btn {
    height: 40px;
    font-size: 16px;

    color: #ffffff;
    line-height: 24px;
  }
}
.dialog-body {
  margin: 10px 0;
  .input-title {
    font-size: 14px;

    color: #13161b;
    line-height: 22px;
    margin-bottom: 6px;
  }
  .btn {
    width: 60px;
    height: 32px;
    font-size: 14px;

    line-height: 22px;
  }
  .cancel {
    color: #13161b;
  }
  .confirm {
    color: #ffffff;
  }
}

.login-tab {
  margin-top: 25px;
  display: flex;
  margin-left: 80px;
  color: var(--text-kyy-color-text-1, #1a2139);
  text-align: center;

  /* kyy_fontSize_3/regular */
  font-family: PingFang SC;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px; /* 150% */
  .tab-item {
    cursor: pointer;
    margin-bottom: 9px !important;
  }
  .active {
    color: var(--brand-kyy-color-brand-default, #4d5eff);

    /* kyy_fontSize_3/bold */
    font-family: PingFang SC;
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px; /* 150% */
    overflow: visible;
    position: relative;
    &:before {
      height: 3px;
      width: 13px;
      content: '';
      border-radius: 1.5px;
      background: var(--brand-kyy-color-brand-default, #4d5eff);
      position: absolute;
      left: 50%;
      bottom: -9px;
      transform: translateX(-50%);
    }
  }
}
:deep(.t-input input) {
  font-size: 16px;
}

:deep(input::-webkit-input-placeholder) {
  color: var(--text-kyy_color_text_5, #acb3c0) !important;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px; /* 150% */
}
:deep(input::input-placeholder) {
  color: var(--text-kyy_color_text_5, #acb3c0) !important;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px; /* 150% */
}

:deep(.register-form) {
  .t-input {
    height: 40px;
    border-color: #d5dbe4 !important;
    padding: 0 12px;

    &:hover:not(.t-is-disabled) {
      border-color: var(--input-kyy-color-input-border-hover, #707EFF) !important;
    }

    .t-input__clear{
      .t-icon {
        color: #828da5 !important;
      }
    }
  }

   .t-input-adornment__prepend {
     background: #fff;
     border-top-left-radius: 4px;
     border-bottom-left-radius: 4px;

    .t-select-input{
      .t-input{
        border: none !important;

        &.t-is-disabled{
          border-radius: 0;
        }
      }

      .t-input__suffix{
        margin-left: 4px !important;
      }
    }
  }

  .adornment-box{
    display: flex;
    width: 100%;
    border: 1px solid #d5dbe4;
    border-radius: 4px;

    &:hover{
      border-color: #707EFF;
    }

    .t-input{
      height: 38px !important;
      border-radius: 4px !important;

      &.t-is-disabled{
        background: transparent !important;

        .t-input__inner{
          color: #ACB3C0;
        }
      }
    }
  }

  .phone-input{
    .t-input{
      border-radius: 0;
      border: none !important;
    }
  }
}

.protocol-checkbox {
  :deep(.t-checkbox__label) {
    font-size: 12px;
  }

  :deep(.t-checkbox__input) {
    border-radius: 50%;
  }
}

.h-full {
  height: 100%;
}

.px-24 {
  padding-left: 24px;
  padding-right: 24px;
}

.py-32 {
  padding-top: 32px;
  padding-bottom: 32px;
}

.relative {
  position: relative;
}

.inline-flex {
  display: inline-flex;
}

.items-center {
  align-items: center;
}

.gap-4 {
  gap: 4px;
}

.cursor-pointer {
  cursor: pointer;
}

.text-20 {
  font-size: 20px;
}

.text-\[\#828DA5\] {
  color: #828DA5;
}

.text-\[\#516082\] {
  color: #516082;
}

.text-\[\#1A2139\] {
  color: #1A2139;
}

.font-bold {
  font-weight: bold;
}

.leading-28 {
  line-height: 28px;
}

.text-center {
  text-align: center;
}

.mt12 {
  margin-top: 12px;
}

.mb12 {
  margin-bottom: 12px;
}

.text-\[\#828DA5\] {
  color: #828DA5;
}

.text-20 {
  font-size: 20px;
}

.cursor-pointer {
  cursor: pointer;
}

.mt12 {
  margin-top: 12px;
}

.pointer {
  cursor: pointer;
}

.mt-4 {
  margin-top: 4px;
}

.text-12 {
  font-size: 12px;
}

.leading-20 {
  line-height: 20px;
}

.text-\[\#D54941\] {
  color: #D54941;
}

.mt-24 {
  margin-top: 24px;
}

.mb-12 {
  margin-bottom: 12px;
}

.text-\[\#4D5EFF\] {
  color: #4D5EFF;
}

.block {
  display: block;
}

.input-code {
  :deep(.t-input__clear) {
    position: absolute;
    right: 115px;
  }
}
</style>
<style>
.login-area-code .t-input--auto-width {
  min-width: 90px;
}
.area-code-select-popup {
  max-height: 250px!important;
}
</style>
