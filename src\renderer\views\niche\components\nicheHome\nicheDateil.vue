<template>
  <div class="detail-box">
    <div class="detail-top">
      <div class="detail-img-box">
        <div class="main-img" @click="preview">
          <img :src="mainImage" alt="" />
        </div>
        <div class="img-list">
          <div v-for="(item, index) in props.nicheDetail?.images" :key="item">
            <div
              class="img-item"
              :class="{ activeImg: item.file_name === mainImage }"
              @click="setMainImg(item.file_name, index)"
            >
              <img :src="item.file_name + '?x-oss-process=image/resize,m_fill,w_200,quality,q_60'" alt="" />
            </div>
          </div>
        </div>
      </div>
      <div class="detail-info">
        <div class="gname">
          <!-- <div v-if="props.nicheDetail?.type === 1" class="tag1">{{ t("niche.gy") }}</div>
          <div v-else class="tag2">{{ t("niche.xq") }}</div> -->
          {{ props.nicheDetail?.title }}
        </div>

        <div v-if="props.nicheDetail?.type === 1" class="price-box">
          <div v-if="props.nicheDetail?.price_option === 1" class="price">
            <span>{{ t("niche.de1") }}</span>
          </div>
          <div v-else class="price">
            <span class="pix">{{ props.nicheDetail?.region === "MO" ? "MOP" : "￥" }}</span>
            <span>{{ formatPrice(props.nicheDetail?.price) }}</span>
          </div>
          <div class="feature">
            <div class="lab">
              <span style="display: flex; margin-top: 2px">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                  <path
                    d="M2.5 3.8566L10.0036 1.66675L17.5 3.8566V8.34746C17.5 13.0677 14.4792 17.2582 10.0011 18.7503C5.52171 17.2582 2.5 13.0667 2.5 8.34537V3.8566Z"
                    fill="#BE5A00"
                  />
                  <path
                    d="M6.25 9.58333L9.16667 12.5L14.1667 7.5"
                    stroke="white"
                    stroke-width="1.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </span>
              <span>{{ t("niche.de2") }}</span>
            </div>
            <div class="lines"></div>
            <div class="cont">{{ props.nicheDetail?.selling_point?.join("·") }}</div>
          </div>
        </div>
        <div v-else class="price-box2">
          <div class="feature">
            <div class="lab">
              <span style="display: flex; padding-top: 2px">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                  <path
                    d="M2.5 3.8566L10.0036 1.66675L17.5 3.8566V8.34746C17.5 13.0677 14.4792 17.2582 10.0011 18.7503C5.52171 17.2582 2.5 13.0667 2.5 8.34537V3.8566Z"
                    fill="#BE5A00"
                  />
                  <path
                    d="M6.25 9.58333L9.16667 12.5L14.1667 7.5"
                    stroke="white"
                    stroke-width="1.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </span>
              <span>{{ t("niche.de2") }}</span>
            </div>
            <div class="lines"></div>
            <div class="cont">{{ props.nicheDetail?.selling_point?.join("·") }}</div>
          </div>
        </div>

        <div class="time-box">
          <template v-if="props.nicheDetail?.template_uuid === 'physical_products'">
            <div class="time-box-item">
              <div class="time-tit">{{ t("niche.guige") }}</div>
              <span class="cont">{{ props.nicheDetail?.spec }}</span>
            </div>

            <div class="time-box-item">
              <div class="time-tit">{{ t("niche.de3") }}</div>
              <span class="cont">{{ props.nicheDetail?.quantity }} {{ props.nicheDetail?.unit?.name }}</span>
            </div>
          </template>
          <div v-if="props.nicheDetail?.effective_unlimited" class="time-box-item">
            <div class="time-tit">{{ t("niche.de4") }}</div>
            <span class="cont">{{ t("niche.de5") }}</span>
          </div>
          <div v-else class="time-box-item">
            <div class="time-tit">{{ t("niche.de4") }}</div>
            <span class="cont">{{ props.nicheDetail?.effective_begin }} ~ {{ props.nicheDetail?.effective_end }}</span>
            <!-- <template v-if="props.showEffective">
              <span
                v-if="!props.review && props.nicheDetail?.effective_state === 0"
                class="extend1"
              >{{ t("niche.jsxiao") }}{{ props.nicheDetail?.effective_day }}天</span>
              <span
                v-if="!props.review && props.nicheDetail?.effective_state === 1"
                class="extend2"
              >{{ t("niche.jsx") }}{{ props.nicheDetail?.effective_day }}天</span>
            </template> -->
          </div>
          <div class="time-box-item">
            <div class="time-tit">{{ t("niche.de6") }}</div>
            <span v-if="props.review" class="cont">
              {{ props.nicheDetail?.classify_name }}
            </span>
            <span v-else class="cont">
              <span>{{ props.nicheDetail?.classify[0]?.alias }} </span>
              <span v-if="props.nicheDetail?.classify[1]?.alias">/ {{ props.nicheDetail?.classify[1]?.alias }}</span>
            </span>
          </div>
          <div class="time-box-item">
            <div class="time-tit">{{ t("niche.de7") }}</div>
            <span class="cont">{{ props.nicheDetail?.address }}</span>
          </div>
        </div>
        <div class="share-box">
          <!-- <t-dropdown
            v-if="dropdownOpts?.length"
            :minColumnWidth="108"
            :disabled="dropdownOpts.length <= 1"
            :options="dropdownOpts"
            placement="bottom"
            @click="onCSClick"
          >
            <t-button class="iconshare-btn" theme="default" @click="singleCSClick">
              <iconpark-icon class="iconshare" name="sidebaricon" style="font-size: 24px; margin-right: 4px" />客服
            </t-button>
          </t-dropdown> -->

          <specialist :key="specialistKey" v-if="props.showCsList" ref="specialistRef" channel="businessOpportunity" :team-id="props.nicheDetail?.team_id">
            <t-button class="iconshare-btn" theme="default">
              <iconpark-icon class="iconshare" name="sidebaricon" style="font-size: 24px; margin-right: 4px" />客服
            </t-button>
          </specialist>

          <div v-if="props.review" class="mbt">
            <iconpark-icon class="iconshare" name="iconshare" style="font-size: 24px; margin-right: 4px" />分享
          </div>
          <t-button v-else class="iconshare-btn" theme="default" @click="shareRun">
            <iconpark-icon class="iconshare" name="iconshare" style="font-size: 24px; margin-right: 4px" />分享
          </t-button>
        </div>
      </div>
    </div>
    <div class="detail-line"></div>
    <div class="detail-bottom">
      <div class="sq-black" :style="{top:  props.review ? '0px' : '16px'}">
        <div class="sq-name">
          <img class="icon" src="@renderer/assets/niche/square.png" alt="" />
          <span :title="props.nicheDetail?.square_data?.name">{{
            nameSlice(props.nicheDetail?.square_data?.name)
          }}</span>
        </div>
        <div v-if="props.review" class="sq-btn1">{{ t("niche.jinqr") }}</div>
        <div v-else class="sq-btn" @click="jump">{{ t("niche.jinqr") }}</div>
      </div>
      <div class="goods-info">
        <div class="goods-info-t " style="width: 100%; height: 0px"></div>

        <div class="detab" v-if="props.review ? props.nicheDetail?.attributeShow : props.nicheDetail?.attribute?.length">
          <div class="item-box">
            <div class="item" :class="{ itemacv: detailIndex === 1 }" @click="changeDetailIndex(1)">商机详情</div>
            <div
              class="item"
              :class="{ itemacv: detailIndex === 2 }"
              @click="changeDetailIndex(2)"
            >
            商机属性
            </div>
            <div :class="`tag${detailIndex}`"></div>
          </div>
          <div style="width: 100%; height: 16px"></div>
        </div>

        <div v-if="!props.nicheDetail?.content" class="logo-box">
          <img class="logo" src="@renderer/assets/niche/Vector.svg" alt="" />
        </div>
        <div v-else class="text-box" id="niche-detail">
          <!-- <lk-editor
            ref="quillRef"
            :key="'content'"
            :editor-type="'B'"
            :special="true"
            :show-tool="false"
            class="niche-detail-editors niche-detail-readOnly"
            :options="{ toolbar: ['annex', 'link'], readOnly: true, showTool: false }"
            @preview-image="onPreviewImage"
          ></lk-editor> -->

          <editor-playground :content="quillRefcontent" />

        </div>
        <div
          class="attributes"
          v-if="props.review ? props.nicheDetail?.attributeShow : props.nicheDetail?.attribute?.length"
        >
          <div class="atable">
          <!-- <div class="attributesark" style="width: 100%; height: 16px" ></div> -->
          <div class="row" v-for="(item, index) in props.nicheDetail?.attribute" :key="index" :class="{attributesark: index === 0}">
              <div class="title">{{ item.title }}</div>
              <div class="content">{{ item.content }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <share ref="shareRef" />
</template>

<script setup lang="ts">
import { onMounted, ref, watch, onUnmounted } from "vue";
import { useI18n } from "vue-i18n";
import { DialogPlugin, MessagePlugin } from "tdesign-vue-next";
import to from "await-to-js";
import { AxiosError, AxiosResponse } from "axios";
import { createSession, getKeFuGroupList } from "@renderer/api/customerService";
import { getOpenid } from "@renderer/utils/auth";
import { SquareData } from "@renderer/api/square/models/square";
import { getIndividualSquare, getSquareInfo } from "@renderer/api/square/home";
import share from "@renderer/components/business/manage/share.vue";
import useNavigate from "@renderer/views/square/hooks/navigate";
import { toSquareHome } from "@renderer/views/square/utils/ipcHelper";
import { getSquareByopenId } from "@/api/business/manage";
import _ from "lodash";
import specialist from "@renderer/views/customerService/specialist.vue";
import EditorPlayground from '@/components/editor/playground.vue';
import LynkerSDK from '@renderer/_jssdk';

const { ipcRenderer } = LynkerSDK;

const { t } = useI18n();
const quillRef = ref(null);

const props = defineProps({
  newJupm: {
    type: Boolean,
    default: true,
  },
  showEffective: {
    type: Boolean,
    default: false,
  },
  im: {
    type: Boolean,
    default: false,
  },
  showCsList: {
    type: Boolean,
    default: false,
  },
  review: {
    type: Boolean,
    default: false,
  },
  nicheDetail: {
    type: Object,
    default: () => ({}),
  },
  teamId: {
    type: String,
    default: null,
  },
});

const mainImage = ref("");
const imgIndex = ref(0);
const setMainImg = (img, index) => {
  mainImage.value = img;
  imgIndex.value = index;
};
const preview = () => {
  // const temp = imgs.map((item) => ({ url: item }));
  // ipcRenderer.invoke("view-img", JSON.stringify(temp));
  const temp = props.nicheDetail?.images.map((item) => ({
    title: item.original_name,
    url: item.file_name,
    type: item.type,
    size: item.size / 1024,
    officeId: item.officeId || null,
    imgIndex: imgIndex.value,
  }));
  ipcRenderer.invoke("view-img", JSON.stringify(temp));
};
const specialistKey = ref(1002)
watch(
  () => props.nicheDetail,
  (newValue, oldValue) => {
    if (newValue) {
      console.log("newValue.nicheDetail", props.nicheDetail);
      setMainImg(props.nicheDetail?.images[0]?.file_name, 0);
      square.value = props.nicheDetail.square_data;
      if (props.showCsList) {
        // getCSGroupList(props.nicheDetail?.team_id);
        specialistKey.value+=1;
      }
      setTimeout(() => {
        renderContent();
      }, 0);
    }
  },
);
const quillRefcontent = ref(null)
const renderContent = () => {
  if (props.nicheDetail?.content) {
    if (props.nicheDetail?.content.includes('"insert":')) {
      // quillRef.value?.renderContent({ ops: JSON.parse(props.nicheDetail?.content) });
      quillRefcontent.value = { ops: JSON.parse(props.nicheDetail?.content) };
    } else {
      // quillRef.value?.renderContent({ ops: [{ insert: `${props.nicheDetail?.content}` }] });
      quillRefcontent.value = { ops: [{ insert: `${props.nicheDetail?.content}` }] };
    }
  } else {
    // quillRef.value?.renderContent({ ops: [] });
    quillRefcontent.value = { ops: [] };
  }
  const srcBox = document.querySelector('.niche-src-box');
  srcBox.scrollTop = 0;
};

onMounted(() => {
  console.log("props.nicheDetail", props.nicheDetail);
  setMainImg(props.nicheDetail?.images[0]?.file_name, 0);
  square.value = props.nicheDetail.square_data;
  if (props.showCsList) {
    getCSGroupList(props.nicheDetail?.apply_team_id);
  }
  renderContent();
  listenScroll();
});


const dropdownOpts = ref([]);
const getCSGroupList = async (teamId) => {
  const [err, res] = await to(getKeFuGroupList({ channelType: "businessOpportunity" }, teamId));
  if (!err) {
    const { data } = res.data;
    dropdownOpts.value = data.list.map((v) => ({
      content: v.kefu_group_name,
      value: v.id,
    }));
  }
};

const onCSClick = async (action) => {
  const teamId = props.teamId || props.nicheDetail?.team_id;
  const openId = getOpenid();

  // 创建会话，根据 groupId 获取相关数据
  const [err, res] = await to<AxiosResponse, AxiosError<{ message: string }>>(
    createSession({ main: openId, groupId: action.value }, teamId),
  );
  if (err) {
    await MessagePlugin.error(err.response.data.message);
    return;
  }
  const { main, peer } = res.data.data;

  await ipcRenderer.invoke("im.chat.open", { main, peer });
  ipcRenderer.send("update-nume-index", 0);
};
const singleCSClick = () => {
  if (dropdownOpts.value.length !== 1) return;
  onCSClick(dropdownOpts.value[0]);
};

const shareRef = ref(null);
const shareRun = () => {
  if (props.review) {
    return false;
  }
  shareRef.value.shareOpen(props.nicheDetail, true);
};

const isSelfSquare = async () => {
  const [err, res] = await to(getSquareByopenId(getOpenid()));
  if (err) return false;
  const { data } = res;
  console.log("data", data);
  return data.opened && data.selfOpened;
};
const jump = () => {
  if (props.review) {
    return false;
  }
  goHomePageRun();
};

const { goHomePage } = useNavigate();
const goHomePageRun = async () => {
  const res = await isSelfSquare();
  console.log(res, "res");
  if (res) {
    jumpRun();
  } else {
    const myDialog = DialogPlugin({
      header: "提示",
      theme: "info",
      body: t("niche.sqqiypon"),
      className: "dialog-classp32",
      confirmBtn: t("niche.qding"),
      onConfirm: () => {
        openSquare();
        myDialog.hide();
      },
      onCancel: () => {
        myDialog.hide();
      },
    });
  }
};

const square: any = ref({});
const jumpRun = () => {
  if (props.newJupm) {
    toSquareHome(square.value.id, { needShowDialog: true });
  } else {
    goHomePage({ squareId: square.value.id, name: square.value.name });
  }
};

const openSquare = async () => {
  const data = { ip_region: "" };
  const [err, res] = await to(getIndividualSquare(data));
  if (err) return;
  jumpRun();
};
const formatPrice = (price) => {
  // 转换为数字，如果是字符串的话
  const number = typeof price === "string" ? parseFloat(price) : price;

  // 检查是否为有效数字
  if (isNaN(number)) {
    return "无效的价格";
  }
  // 格式化为千分位数字，不包括货币符号
  return number.toLocaleString("zh-CN", {
    style: "decimal",
    minimumFractionDigits: 2,
  });
};
const nameSlice = (name) => {
  if (!name) {
    return "-";
  }
  if (name.length > 13) {
    return `${name.slice(0, 13)}...`;
  }
  return name;
};

const onPreviewImage = (imageData) => {
  const temp = imageData.images.map((item) => ({
    url: item,
    imgIndex: imageData.index,
  }));
  ipcRenderer.invoke("view-img", JSON.stringify(temp));
};

const detailIndex = ref(1);
const changeDetailIndex = (idx) => {
  detailIndex.value = idx;
  scrollTo(idx === 1 ? '.goods-info-t' : '.attributes');
};
const scrollTo = (ele) => {
  const element = document.querySelector(ele);
  const srcBox = document.querySelector('.niche-src-box');
  if (element) {
    element.scrollIntoView({ behavior: 'auto', block: 'nearest', parent: srcBox });
    if(ele === '.attributes' && props.nicheDetail?.attribute?.length > 2) {
      srcBox.scrollTop =  srcBox.scrollTop - 76
    }
  }
};

const handleScroll = () => {
  const srcBox = document.querySelector('.niche-src-box');
  const textb = srcBox.querySelector('.text-box');
  if (textb) {
    const conditionMet = srcBox.scrollTop > textb.scrollHeight - 100;
    detailIndex.value = conditionMet ? 2 : 1;
  }
};

const throttle = _.throttle(() => {
  const hasattr = props.review ? props.nicheDetail?.attributeShow : props.nicheDetail?.attribute?.length;
  if (hasattr) {
    handleScroll();
  }
}, 500);

onUnmounted(() => {
  unlinst();
});
const listenScroll = () => {
  const srcBox = document.querySelector('.niche-src-box');
  srcBox.addEventListener('scroll', throttle);
}
const unlinst = () => {
  const srcBox = document.querySelector('.niche-src-box');
  srcBox?.removeEventListener('scroll', throttle);
}
</script>

<style lang="less" scoped>
.detail-box {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  .detail-top {
    display: flex;
    align-items: flex-start;
    gap: 24px;
    align-self: stretch;
    .detail-img-box {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;
      .main-img {
        display: flex;
        width: 392px;
        height: 392px;
        flex-direction: column;
        align-items: flex-start;
        cursor: pointer;
        border: 1px solid var(--border-kyy_color_border_default, #d5dbe4);
        img {
          width: 100%;
          height: 100%;
          // border-radius: 8px;
          object-fit: cover;
        }
      }
      .img-list {
        display: flex;
        align-items: center;
        gap: 8px;
        .img-item {
          display: flex;
          cursor: pointer;
          img {
            width: 100%;
            height: 100%;
            // border-radius: 8px;
            // object-fit: cover;
          }
          display: flex;
          width: 72px;
          height: 72px;
          flex-direction: column;
          align-items: flex-start;
          // border-radius: 8px;
          border: 1px solid var(--icon-kyy_color_icon_white, #fff);
        }
        .activeImg {
          // border-radius: 8px;
          border: 1px solid var(--brand-kyy_color_brand_default, #4d5eff);
        }
      }
    }
    .detail-info {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
      flex: 1 0 0;
      align-self: stretch;
      position: relative;
      .gname {
        color: var(--text-kyy_color_text_1, #1a2139);
        font-family: "PingFang SC";
        font-size: 18px;
        font-style: normal;
        font-weight: 600;
        line-height: 26px; /* 144.444% */
        word-break: break-all;
        .tag1 {
          display: flex;
          height: 24px;
          flex-shrink: 0;
          padding: 0 8px;
          justify-content: center;
          align-items: center;
          margin-right: 4px;
          border-radius: var(--kyy_radius_tag_s, 4px);
          background: var(--kyy_color_tag_bg_cyan, #e6f9f8);
          color: var(--kyy_color_tag_text_cyan, #11bdb2);
          text-align: center;
          font-family: "PingFang SC";
          font-style: normal;
          text-align: center;
          font-size: 14px;
          font-weight: 600;
          float: left;
          margin-top: 2px;
        }
        .tag2 {
          display: flex;
          height: 24px;
          flex-shrink: 0;
          padding: 0 8px;
          justify-content: center;
          align-items: center;
          margin-right: 4px;
          border-radius: var(--kyy_radius_tag_s, 4px);
          background: var(--kyy_color_tag_bg_warning, #ffe5d1);
          color: var(--kyy_color_tag_text_warning, #fc7c14);
          text-align: center;
          font-size: 14px;
          font-weight: 600;
          float: left;
          margin-top: 2px;
        }
      }
      .price-box {
        display: flex;
        width: 704px;
        padding: 12px 16px 12px 16px;
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
        background-image: url(@/assets/niche/big_pic.png);
        background-position: center;
        background-size: 100% 100%;
        .price {
          .pix {
            overflow: hidden;
            color: var(--error-kyy_color_error_default, #d54941);
            text-overflow: ellipsis;
            font-family: "PingFang SC";
            font-size: 18px;
            font-style: normal;
            font-weight: 600;
            line-height: 26px; /* 144.444% */
          }
          overflow: hidden;
          color: var(--error-kyy_color_error_default, #d54941);
          text-overflow: ellipsis;
          font-family: "PingFang SC";
          font-size: 28px;
          font-style: normal;
          font-weight: 600;
          line-height: 36px; /* 128.571% */
        }
      }
      .feature {
        display: flex;
        // height: 40px;
        gap: 8px;
        .lab {
          color: var(--warning-kyy_color_warning_active, #be5a00);
          font-family: "PingFang SC";
          font-size: 16px;
          font-style: normal;
          font-weight: 600;
          line-height: 24px; /* 150% */
          display: flex;
          // align-items: center;
          gap: 4px;
          .iconsecure {
            color: #be5a00;
          }
        }
        .lines {
          width: 1px;
          align-self: stretch;
          background: var(--warning-kyy_color_warning_active, #be5a00);
          height: 15px;
          margin-top: 5px;
        }
        .cont {
          color: var(--warning-kyy_color_warning_active, #be5a00);
          font-family: "PingFang SC";
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          line-height: 24px;
          max-width: 599px;
        }
      }
      .price-box2 {
        display: flex;
        width: 704px;
        gap: 16px;
        background-image: url(@/assets/niche/big_pic.png);
        background-position: center;
        background-size: 100% 100%;
        padding: 12px 16px;
      }
      .gsp {
        display: flex;
        padding: 12px 16px;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
        gap: 12px;
        align-self: stretch;
        border-radius: 8px;
        background: var(--bg-kyy_color_bg_deep, #f5f8fe);
        .gsp-item {
          display: flex;
          align-items: center;
          gap: 16px;
          align-self: stretch;
          .gsp50 {
            width: 50%;
            display: flex;
            align-items: center;
            gap: 12px;
            flex: 1 0 0;
          }
          .gsp100 {
            width: 100%;
            display: flex;
            align-items: center;
            gap: 12px;
            flex: 1 0 0;
          }
          .label {
            color: var(--text-kyy_color_text_3, #828da5);
            font-family: "PingFang SC";
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
            // width: 56px;
          }
          .val {
            color: var(--text-kyy_color_text_1, #1a2139);
            font-family: "PingFang SC";
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
          }
        }
      }
      .time-box {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        align-self: stretch;
        gap: 12px;
        .time-box-item {
          display: flex;
          align-items: start;
          .time-tit {
            color: var(--text-kyy_color_text_3, #828da5);
            min-width: 64px;
            font-family: "PingFang SC";
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 24px; /* 150% */
          }
          .icon {
            font-size: 20px;
            color: #828da5;
          }
          .cont {
            color: var(--text-kyy_color_text_1, #1a2139);
            font-family: "PingFang SC";
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
            margin-left: 16px;
            // margin-right: 8px;
          }
          .extend1 {
            color: var(--success-kyy_color_success_active, #499d60);
            font-family: "PingFang SC";
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
          }
          .extend2 {
            color: var(--error-kyy_color_error_default, #d54941);
            font-family: "PingFang SC";
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
          }
        }
      }
      .share-box {
        display: flex;
        width: 100%;
        margin-top: 16px;
        gap: 12px;
        .iconshare {
          color: #828da5;
        }
        .iconshare-btn {
          width: 124px;
          height: 48px;
          font-size: 16px;
          font-weight: 600;
        }

        .iconshare-btn:hover {
          .iconshare {
            color: #707eff;
          }
        }
      }
    }
  }
  .detail-line {
    height: 1px;
    align-self: stretch;
    margin: 24px 0;
    background: var(--divider-kyy_color_divider_light, #eceff5);
  }
  .detail-bottom {
    display: flex;
    align-items: flex-start;
    gap: 24px;
    align-self: stretch;
    .sq-black {
      display: flex;
      width: 200px;
      padding: 16px;
      flex-direction: column;
      align-items: center;
      gap: 16px;
      border-radius: 8px;
      border: 1px solid var(--divider-kyy_color_divider_light, #eceff5);
      position: sticky;
      top: 16px;
      .sq-name {
        .icon {
          width: 20px;
          height: 20px;
          float: left;
          margin-right: 4px;
        }
        color: var(--text-kyy_color_text_1, #1a2139);
        font-family: "PingFang SC";
        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        line-height: 22px; /* 157.143% */
      }
      .sq-btn1 {
        display: flex;
        height: 32px;
        min-height: 32px;
        max-height: 32px;
        padding: 0px 16px;
        justify-content: center;
        align-items: center;
        gap: 4px;
        align-self: stretch;
        border-radius: var(--radius-kyy_radius_button_s, 4px);
        border: 1px solid var(--color-button_secondaryBrand-kyy_color_button_secondrayBrand_border_disabled, #c9cfff);
        color: var(--color-button_secondaryBrand-kyy_color_button_secondrayBorder_text_disabled, #c9cfff);
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
      }
      .sq-btn {
        display: flex;
        height: 32px;
        min-height: 32px;
        max-height: 32px;
        padding: 0px 16px;
        justify-content: center;
        align-items: center;
        gap: 4px;
        align-self: stretch;
        border-radius: var(--radius-kyy_radius_button_s, 4px);
        border: 1px solid var(--color-button_secondaryBrand-kyy_color_button_secondaryBrand_border_dedault, #4d5eff);
        background: var(--color-button_secondaryBrand-kyy_color_button_secondrayBorder_bg_default, #eaecff);
        color: var(--color-button_secondaryBrand-kyy_color_button_secondrayBorder_text_default, #4d5eff);
        cursor: pointer;
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
      }
    }
    .goods-info {
      width: 100%;
      min-height: 124px;
      .detab {
        position: sticky;
        top: 0px;
        width: 100%;
        background: #fff;
        z-index: 1001;

        .item-box {
          height: 56px;
          position: relative;
          gap: 32px;
          width: 100%;
          display: flex;
          display: flex;
          align-items: center;
          align-self: stretch;
          border-bottom: 1px solid var(--divider-kyy_color_divider_deep, #d5dbe4);
        }

        .item {
          display: flex;
          flex-direction: column;
          align-items: center;
          color: var(--text-kyy_color_text_1, #1a2139);
          text-align: center;
          font-family: "PingFang SC";
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          line-height: 36px; /* 150% */
          height: 36px;
          cursor: pointer;
        }
        .itemacv {
          color: var(--brand-kyy_color_brand_default, #4d5eff);
          font-family: "PingFang SC";
          font-size: 16px;
          font-style: normal;
          font-weight: 600;
        }
        .tag1 {
          width: 16px;
          height: 3px;
          border-radius: 1.5px;
          background: var(--brand-kyy_color_brand_default, #4d5eff);
          position: absolute;
          bottom: 0px;
          left: 20px;
        }
        .tag2 {
          width: 16px;
          height: 3px;
          border-radius: 1.5px;
          background: var(--brand-kyy_color_brand_default, #4d5eff);
          position: absolute;
          bottom: 0px;
          left: 120px;
        }
      }
      .logo-box {
        width: 100%;
        height: 100%;
        height: 124px;
        display: flex;
        align-items: center;
        justify-content: center;
        .logo {
          width: 250px;
          height: 56px;
        }
      }
      .text-box {
        min-height: 124px;
        color: var(--text-kyy_color_text_1, #1a2139);
        width: 100%;
        font-family: "PingFang SC";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
        white-space: pre-wrap;
        word-break: break-all;
        position: relative;
      }
    }
  }
}

.customer-support {
  position: fixed;
  bottom: 120px;
  right: 12px;
  width: 45px;
  height: 45px;
  border-radius: 50%;
  background: var(--color-button_primary-kyy_color_button_primary_bg_default, #4d5eff);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  .icon {
    font-size: 32px;
    color: var(--td-brand-color);
  }
}
.iconorientation {
  color: #21acfa;
  font-size: 20px;
  margin-right: 4px;
  position: relative;
  top: 5px;
}
.mbt {
  display: flex;
  padding: 0px 16px 0px 12px;
  justify-content: center;
  align-items: center;
  gap: 4px;
  border-radius: var(--radius-kyy_radius_button_s, 4px);
  border: 1px solid var(--color-button_border-kyy_color_buttonBorder_border_disabled, #d5dbe4);
  color: var(--color-button_border-kyy_color_buttonBorder_text_disabled, #acb3c0);
  text-align: center;
  font-family: "PingFang SC";
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: 22px;
  width: 124px;
  height: 48px;
  .iconshare {
    color: #d5dbe4 !important;
  }
}
.niche-detail-editors {
  width: 100%;
  border-radius: 4px;
  .lk-toolbar {
    border-bottom: none !important;
    border-radius: 4px 4px 0 0 !important;
  }
  .ql-container {
    border-radius: 4px;
    border-top: none !important;
    // text-indent: 12px;
    position: unset !important;
  }
  .ql-editor {
    overflow-y: inherit !important;
    padding: 0;
  }
}
.kefu {
}

:deep(.iconshare-btn .t-button__text) {
  font-size: 16px;
  display: flex;
  align-items: center;
}

.attributes {
  .atable {
    border: 1px solid var(--divider-kyy_color_divider_light, #eceff5);
    border-bottom: none;
    .row {
      display: flex;
    }
    .title {
      display: flex;
      width: 216px;
      padding: 12px 16px;
      flex-direction: column;
      justify-content: center;
      background: var(--bg-kyy_color_bg_deep, #f5f8fe);
      color: var(--text-kyy_color_text_1, #1a2139);

      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
      border-right: 1px solid var(--divider-kyy_color_divider_light, #eceff5);
      border-bottom: 1px solid var(--divider-kyy_color_divider_light, #eceff5);
    }
    .content {
      display: flex;
      width: 680px;
      padding: 12px 16px;
      align-items: center;
      background: var(--bg-kyy_color_bg_light, #fff);
      color: var(--text-kyy_color_text_1, #1a2139);
      flex: 1;
      /* kyy_fontSize_2/regular */
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
      border-bottom: 1px solid var(--divider-kyy_color_divider_light, #eceff5);
    }
  }
}
</style>
