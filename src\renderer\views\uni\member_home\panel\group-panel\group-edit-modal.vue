<template>
  <div class="detail-review">
    <t-dialog
      v-model:visible="visible"
      :header="props.etype === 1 ? t('ebook.gai') : t('ebook.gdi')"
      :on-close="onCancel"
      :destroy-on-close="true"
      :confirmLoading="confirmLoading"
      width="536px"
      :confirm-btn="'确定'"
      @confirm="onConfirm"
    >
      <div class="form-box">
        <t-form
          ref="formref"
          :rules="FORM_RULES"
          :data="formData"
          :colon="false"
          scroll-to-first-error="smooth"
          label-align="top"
          @submit="onSubmit"
        >
          <div v-if="props.etype === 2" class="title-box">{{ t("ebook.gri") }}</div>
          <t-form-item :label="t('ebook.grn')" name="group_name">
            <t-input
              v-model="formData.group_name"
              :maxlength="50"
              show-limit-number
              clearable
              :placeholder="t('ebook.grni')"
            />
          </t-form-item>
          <t-form-item
            :label="t('ebook.llr')"
            name="userids"
            :rules="[
              {
                message: t('ebook.llri'),
                validator: validatorUser,
                required: true,
                trigger: 'change',
              },
            ]"
          >
            <t-input
              v-model="formData.userids"
              :maxlength="50"
              show-limit-number
              clearable
              v-show="false"
              :placeholder="t('ebook.llri')"
            />
            <div class="user">
              <div class="addbtn" @click="addRun">
                <iconpark-icon name="iconadd" class="iconadd"></iconpark-icon>
                {{ t("ebook.ad") }}
              </div>
              <t-tag
                v-for="(tag, index) in userList"
                :key="tag.idStaff"
                class="tagText"
                theme="default"
                :closable="true"
                :max-width="124"
                @close="handleCloseUser(index)"
              >
                <template #icon>
                  <kyy-avatar
                    round-radius
                    avatar-size="20px"
                    :image-url="tag.avatar"
                    :user-name="tag.name"
                    style="margin-right: 8px"
                  />
                </template>
                {{ tag.name }}
              </t-tag>
            </div>
          </t-form-item>
          <!-- v-if="!(formData.auto_im && formData.member_number < 2)" -->
          <template v-if="props.etype === 2  && (formData.auto_im&&formData.member_number >= 2)">
            <div class="title-box">
              <span>{{ t("ebook.grq") }}</span>
              <span class="dissolve" v-if="formData.group_im" @click="dissolveRun">{{ t("ebook.gre") }}</span>
            </div>
            <template v-if="formData.group_im">
              <t-form-item :label="t('ebook.qn')" name="signature" class="readonly">
                <t-input
                  v-model="formData.group_im_name"
                  show-limit-number
                  readonly
                  clearable
                  :placeholder="t('ebook.qn')"
                />
              </t-form-item>
              <t-form-item
                :label="t('ebook.qno')"
                name="im_group_leader"
                :rules="[
                  {
                    message: t('ebook.qno'),
                    validator: validator,
                    required: true,
                    trigger: 'change',
                  },
                ]"
                requiredMark
              >
                <t-input
                  @click="groupOwnerOpen"
                  v-model="formData.im_group_leader.name"
                  show-limit-number
                  clearable
                  readonly
                  :placeholder="t('ebook.qno')"
                >
                  <template #suffix-icon>
                    <iconpark-icon name="iconpeople-1" class="icon" style="font-size: 20px; color: #828da5" />
                  </template>
                </t-input>
              </t-form-item>
              <t-form-item :label="t('ebook.qrs')" name="signature" class="readonly">
                <t-input v-model="formData.num" show-limit-number clearable readonly :placeholder="t('ebook.qrs')" />
              </t-form-item>
            </template>
            <template v-else>
              <Empty
                name="no-data-new"
                v-if="loaded"
                :tip="
                  formData.member_number < 2 ? (formData.auto_im ? t('ebook.gr2q') : t('ebook.zqw')) : t('ebook.zqw')
                "
              />

              <div class="btn-box" v-if="!(formData.auto_im && formData.member_number < 2)">
                <div class="mock-btn" @click="createGroup">{{ t("ebook.cjq") }}</div>
              </div>
            </template>
          </template>
        </t-form>

        <!-- <div class="create-tip" v-if="props.etype === 1">
          <div class="label">
            {{ t("ebook.cjqz")
            }}<t-tooltip :showArrow="false">
              <iconpark-icon
                name="iconhelp"
                class="icon"
                style="font-size: 20px; color: #828da5; position: relative; top: 5px; left: 4px"
              />
              <template #content>
                <div style="width: 240px">
                  <p>{{ t("ebook.tip1") }}</p>
                  <p>{{ t("ebook.tip2") }}</p>
                  <p>{{ t("ebook.tip3") }}</p>
                  <p>{{ t("ebook.tip4") }}</p>
                </div>
              </template>
            </t-tooltip>
          </div>
          <div class="ch-box">
            <t-checkbox v-model="formData.auto_im"> {{ t("ebook.auc") }}</t-checkbox>
          </div>
        </div> -->
      </div>
    </t-dialog>
  </div>
  <group-owner-select
    ref="groupOwnerSelectRef"
    :single="single"
    :header="sheader"
    :tip="selectTip"
    @send-user-data="sendUserData"
  />
</template>

<script setup lang="ts">
import { computed, reactive, ref } from "vue";
import { useI18n } from "vue-i18n";
import { DialogPlugin, MessagePlugin } from "tdesign-vue-next";
import kyyAvatar from "@renderer/components/kyy-avatar/index.vue";
import { useDigitalPlatformStore } from "@renderer/views/digital-platform/store/digital-platform-store";
import { platform } from "@renderer/views/digital-platform/utils/constant";
import { useRoute } from "vue-router";
import Empty from "@renderer/components/common/Empty.vue";
import {
  getAppStaffAxios,
  governmentDissolve,
  governmentGroupCreate,
  governmentGroupDetail,
  governmentGroupEdit,
} from "@renderer/api/uni/api/businessApi";
import { getResponseResult } from "@renderer/utils/myUtils";
import { getUniTeamID } from "@renderer/views/uni/utils/auth";
import groupOwnerSelect from "./group-owner-select.vue";
const { t } = useI18n();
const visible = ref(false);

const digitalPlatformStore = useDigitalPlatformStore();
const nicheDetail = ref(null);
const userList = ref([]);
const stype = ref(1);

const formref = ref(null);
const formData = reactive({
  group_name: "",
  userids: "",
  new_im_leader: "",
  im_group_leader: {
    name: "",
    card_id: "",
    openid: "",
    related_id: "",
    idStaff: "",
  },
  num: 1,
  id: undefined,
  group_im_name: "",
  group_im: "",
  member_number: 0,
  auto_im: false,
});
const FORM_RULES = {
  group_name: [{ required: true, message: t("ebook.grni") }],
  userids: [{ required: true, message: t("ebook.llri") }],
};
const onSubmit = ({ validateResult, firstError }) => {
  console.log(formData.userids);

  if (validateResult === true) {
  confirmLoading.value = true;
  if (props.etype === 1) {
      addReq();
    } else {
      editReq();
    }
  } else {
    console.log("Validate Errors: ", firstError, validateResult);
  }
};
const selectTip = ref("");
const sheader = ref("");
const single = ref(false);
const loaded = ref(false);
const confirmLoading = ref(false);

const validatorUser = () => {
  if (!formData.userids || !formData.userids.length) {
    return {
      message: t("ebook.llri"),
      required: true,
      trigger: "change",
    };
  }
  return { result: true, message: "", type: "success" };
};
const validator = () => {
  if (!formData.im_group_leader.idStaff) {
    return {
      message: t("ebook.leader"),
      required: true,
      trigger: "change",
    };
  }
  return { result: true, message: "", type: "success" };
};
const addRun = async () => {
  stype.value = 1;
  single.value = false;
  selectTip.value = "";
  sheader.value = t("ebook.llri2");
  const sedata = userList.value.map((item) => item.idStaff);
  await getAppMemberList();
  // const sedata = props.etype === 1 ? [] : userList.value.map((item) => item.idStaff);
  groupOwnerSelectRef.value.ownerOpen(optionsMembers.value, sedata);
};
const route = useRoute();
const props = defineProps({
  etype: {
    type: Number,
    default: 1,
  },
  teamId: {
    type: String,
    default: "",
  },
});
const platformCpt: any = computed(() => route.query?.platform);


const editOpen = (id) => {
  visible.value = true;
  setTimeout(() => {
    formref.value.reset();
    formData.auto_im = false;
    userList.value = [];
  }, 1);
  if (id) {
    formData.id = id;
    getgovernmentGroupDetail(id);
  }
};

const onConfirm = () => {
  formref.value.submit({ showErrorMessage: true });
};
const onCancel = () => {
  visible.value = false;
};

const groupLeaderClear = () => {
  formData.im_group_leader.name = undefined;
  formData.im_group_leader.card_id = undefined;
  formData.im_group_leader.openid = undefined;
  formData.im_group_leader.related_id = undefined;
  formData.im_group_leader.idStaff = undefined;
};

const handleCloseUser = (index) => {
  console.log(index);
  if (userList.value[index].idStaff == formData.im_group_leader.idStaff) {
    groupLeaderClear();
  }
  userList.value.splice(index, 1);
  formData.userids = userList.value.map((item) => item.idStaff);
};

const edim = ref(false);
const editDataHandle = () => {
  const data = editDataHandlebasc();
  const params = {
    ...data,
    is_create_group: edim.value ? 1 : formData.auto_im ? 1 : 0,
    id: formData.id,
    new_im_leader: formData.im_group_leader.card_id || formData.im_group_leader?.cardId,
  };
  return params;
};
const editReq = () => {
  const data = editDataHandle();
  governmentGroupEdit(data, currentTeamId.value)
    .then((res: any) => {
  confirmLoading.value = false;
  if (res.data?.code === 0) {
        MessagePlugin.success(t("ebook.setsu"));
        emit("edit-ok");
        visible.value = false;
      } else {
        MessagePlugin.error(res.msg);
      }
    })
    .catch((err) => {
  confirmLoading.value = false;
  console.log(err);
      MessagePlugin.error(err.message || "编辑失败");
    });
};

const dissolveReq = () => {
  governmentDissolve(formData.id, currentTeamId.value).then((res) => {
    if (res.data.code === 0) {
      MessagePlugin.success(t("ebook.exs"));
      getgovernmentGroupDetail(formData.id);
    }
  });
};
const dissolveRun = () => {
  const myDialog = DialogPlugin({
    header: t("niche.tip"),
    theme: "info",
    body: t("ebook.ext"),
    className: "dialog-classp32",
    confirmBtn: t("ebook.ex"),
    onConfirm: () => {
      dissolveReq();
      myDialog.hide();
    },
    onCancel: () => {
      myDialog.hide();
    },
  });
};

const groupOwnerSelectRef = ref(null);
const groupOwnerOpen = async () => {
  stype.value = 2;
  single.value = true;
  selectTip.value = t("ebook.sxzt");
  sheader.value = t("ebook.sxz");
  await getAppMemberList();
  groupOwnerSelectRef.value.ownerOpen(userList.value, [formData.im_group_leader.idStaff]);
};

const sendUserData = (e) => {
  console.log(e);
  if (stype.value === 1) {
    userList.value = e;
    formData.userids = e.map((item) => item.idStaff);
    console.log(formData.userids, "formData.userids");
    console.log(formData.im_group_leader.idStaff);

    if (!formData.userids.includes(formData.im_group_leader.idStaff)) {
      groupLeaderClear();
    }
  } else {
    ownerChangeTip(e);
  }
};

const ownerChangeTip = (e) => {
  const item = e[0];
  if (!formData.im_group_leader.idStaff || formData.im_group_leader.idStaff === item.idStaff) {
    formData.im_group_leader = item;
    formData.im_group_leader.card_id = `$${item.idStaff}`;
    // onConfirm();
    return;
  }
  const myDialog = DialogPlugin({
    header: t("niche.tip"),
    theme: "info",
    body: t("ebook.changet", { name: formData.im_group_leader.name, name2: item.name }),
    className: "dialog-classp32",
    confirmBtn: `确定`,
    onConfirm: () => {
      formData.im_group_leader = item;
      formData.im_group_leader.card_id = `$${item.idStaff}`;
      console.log(formData.im_group_leader);
      // onConfirm();
      myDialog.hide();
    },
    onCancel: () => {
      myDialog.hide();
    },
  });
};

const onListenMembers = async (arr) => {
  console.log(arr);
};
const currentTeamId = computed(() => {
  if (platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount?.teamId;
  }
  if (platformCpt.value === platform.digitalWorkbench) {
    return route.query?.teamId || 0;
  }
  return getUniTeamID();
});
const optionsMembers = ref([]);
const getAppMemberList = async () => {
  let result = null;
  try {
    result = await getAppStaffAxios({}, currentTeamId.value);
    result = getResponseResult(result);
    if (!result) {
      return;
    }
    optionsMembers.value = result.data;
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    MessagePlugin.error(errMsg);
  }
};

const emit = defineEmits(["edit-ok"]);
const addDataHandle = () => {
  const params = {
    auto_im: formData.auto_im ? 1 : 0,
    group_name: formData.group_name,
    group_contact: [],
  };
  for (const item of userList.value) {
    params.group_contact.push({
      card_id: `$${item.idStaff}`,
      openid: item.openId || item.openid,
      related_id: item.idStaff,
    });
  }
  return params;
};

const editDataHandlebasc = () => {
  const params = {
    auto_im: formData.auto_im ? 1 : 0,
    group_name: formData.group_name,
    group_contact: [],
  };
  for (const item of userList.value) {
    params.group_contact.push({
      card_id: `$${item.idStaff}`,
      openid: item.openId || item.openid,
      related_id: item.idStaff,
    });
  }
  return params;
};

const addReq = () => {
  const data = addDataHandle();
  governmentGroupCreate(data, currentTeamId.value)
    .then((res: any) => {
  confirmLoading.value = false;
  if (res.data?.code === 0) {
        MessagePlugin.success(t("ebook.asu"));
        emit("edit-ok");
        visible.value = false;
      } else {
        MessagePlugin.error(res.msg);
      }
    })
    .catch((err) => {
  confirmLoading.value = false;
  console.log(`err`, err);
      MessagePlugin.error(err?.message || "添加失败");
    });
};
getAppMemberList();
const is_create_group_im = ref(0);
const getgovernmentGroupDetail = (id) => {
  loaded.value = false;
  governmentGroupDetail(id, currentTeamId.value).then((res: any) => {
    if (res.data?.code === 0) {
      const { detail } = res.data.data;
      formData.group_name = detail.group_name;
      formData.group_im = detail.group_im;
      formData.im_group_leader = detail.im_group_leader;
      if (formData.im_group_leader.idStaff) {
        formData.im_group_leader.idStaff = Number(formData.im_group_leader.idStaff);
      }
      formData.num = detail.group_im_member;
      formData.group_im_name = detail.group_im_name;
      formData.auto_im = !!detail.auto_im;
      formData.member_number = detail.member_number;
      userList.value = detail.group_contact;
      is_create_group_im.value = detail.is_create_group_im;
      formData.userids = detail.group_contact.map((item) => item.openid);
      loaded.value = true;
    } else {
      MessagePlugin.error(res.msg);
    }
  });
};

const createGroup = () => {
  if (formData.member_number < 2) {
    return MessagePlugin.warning(t("ebook.ttipss1"));
  }
  const myDialog = DialogPlugin({
    header: t("niche.tip"),
    theme: "info",
    body: t("ebook.cst"),
    className: "dialog-classp32",
    confirmBtn: `确定`,
    onConfirm: () => {
      edim.value = true;
      onConfirm();
      myDialog.hide();
    },
    onCancel: () => {
      myDialog.hide();
    },
  });
};

defineExpose({
  editOpen,
});
</script>

<style lang="less" scoped>
.form-box {
  margin-top: 24px;
  margin-bottom: 16px;
  max-height: 460px;
  overflow-y: auto;
  padding-right: 16px;
  .title-box {
    display: flex;
    padding: 12px 16px;
    align-items: center;
    gap: 8px;
    align-self: stretch;
    color: var(--kyy_color_table_hrading_text, #516082);
    background: var(--kyy_color_table_hrading_bg, #e2e6f5);
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px;
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
    .dissolve {
      color: var(--color-button_text_error-kyy_color_button_text_error_font_default, #d54941);
      text-align: center;
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
      cursor: pointer;
    }
  }
}
.create-tip {
  .label {
    color: var(--text-kyy_color_text_3, #828da5);
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
  .ch-box {
    margin-top: 8px;
  }
}
.user {
  display: flex;
  padding: 4px 12px;
  align-items: flex-start;
  align-self: stretch;
  border-radius: var(--input-kyy_radius_input, 4px);
  border: 1px solid #d5dbe4;
  max-height: 96px;
  width: 100%;
  overflow-y: auto;
  width: 100%;
  margin-top: 8px;
  flex-wrap: wrap;
  gap: 8px;
  .addbtn {
    display: flex;
    height: 24px;
    min-height: 24px;
    max-height: 24px;
    padding: 0px 12px 0px 8px;
    justify-content: center;
    align-items: center;
    gap: 4px;
    border-radius: var(--radius-kyy_radius_button_s, 4px);
    border: 1px solid var(--color-button_secondaryBrand-kyy_color_button_secondaryBrand_border_dedault, #4d5eff);
    background: var(--color-button_secondaryBrand-kyy_color_button_secondrayBorder_bg_default, #eaecff);
    color: var(--color-button_secondaryBrand-kyy_color_button_secondrayBorder_text_default, #4d5eff);
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    cursor: pointer;
    .iconadd {
      font-size: 20px;
    }
  }

  .tagText {
    display: flex;
    height: 24px;
    min-height: 24px;
    max-height: 24px;
    padding: 2px 8px;
    align-items: center;
    border-radius: 4px;
    background: var(--kyy_color_tag_bg_gray, #eceff5);
  }
}

.btn-box {
  display: flex;
  justify-content: center;
  margin-top: 12px;
  .mock-btn {
    display: flex;
    height: 32px;
    min-width: 80px;
    min-height: 32px;
    max-height: 32px;
    padding: 0px 16px;
    justify-content: center;
    align-items: center;
    gap: 4px;
    border-radius: var(--radius-kyy_radius_button_s, 4px);
    border: 1px solid var(--color-button_secondaryBrand-kyy_color_button_secondaryBrand_border_dedault, #4d5eff);
    background: var(--color-button_secondaryBrand-kyy_color_button_secondrayBorder_bg_default, #eaecff);
    color: var(--color-button_secondaryBrand-kyy_color_button_secondrayBorder_text_default, #4d5eff);
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
    cursor: pointer;
  }
}
:deep(.t-input__limit-number) {
  display: none !important;
}
:deep(.tip) {
  margin-top: 2px;
}
:deep(.t-dialog--default) {
  padding-right: 2px;
}
:deep(.t-dialog__header) {
  padding-right: 18px;
}
:deep(.t-dialog__footer) {
  padding-right: 22px;
}

.form-box::-webkit-scrollbar {
  width: var(--kyy_radius_toopltip, 6px);
  border-radius: 7px;
}
.form-box::-webkit-scrollbar-thumb {
  border-radius: 7px;
  background: var(--divider-kyy_color_divider_deep, #d5dbe4);
}
.form-box::-webkit-scrollbar-track {
  background-color: #fff;
}
:deep(.readonly .t-input__inner) {
  color: #acb3c0;
}
</style>
