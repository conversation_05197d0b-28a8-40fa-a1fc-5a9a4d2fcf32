import { webContents, Menu, MenuItemConstructorOptions } from 'electron'
import { showDevTool } from "@main/env";
import menuconfig from "./menu";

export const updateDevMenuList = () => {
    if (showDevTool) {
        setTimeout(() => {
            const menus = [...menuconfig];

            const openedList = getOpenedList();
            openedList.unshift(
                {
                    label: '关闭所有',
                    click: () => {
                        const allWebs = webContents.getAllWebContents().filter(item => ['window' , 'browserView'].includes(item.getType()));
                        allWebs.forEach(item => {
                            if (item.isDevToolsOpened()) {
                                item.closeDevTools();
                            }
                        });
                    },
                }, 
                { 
                    type: 'separator'
                }
            );
            
            menus.push({
                label: "开发者设置",
                submenu: openedList
            });
            const appMenu = Menu.buildFromTemplate(menus);

            Menu.setApplicationMenu(appMenu)
        }, 1000);
    }
}

const getOpenedList = () => {
    const allWebs = webContents.getAllWebContents().filter(item => ['window' , 'browserView'].includes(item.getType()));
    const subMenus = allWebs.map(item => {
        return {
            label: item.getURL(),
            click: () => {
                const web = webContents.getAllWebContents().find(it => it.id === item.id);
                if (web && !web.isDevToolsOpened()) {
                    web.openDevTools({mode: "detach", activate: true })
                }
            }
        }
    })
    return subMenus as MenuItemConstructorOptions[];
}
