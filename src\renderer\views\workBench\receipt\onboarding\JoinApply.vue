<script setup lang="ts">
import { computed } from 'vue';
import { useRoute } from 'vue-router';
import { onMountedOrActivated } from '@renderer/hooks/onMountedOrActivated';
import ApplyStep from './components/ApplyStep.vue';
import ApplyForm from './components/ApplyForm.vue';
import VerifyStatus from './components/VerifyStatus.vue';
import MerchantActualName from './components/MerchantActualName.vue';
import MerchantSplitAccount from './components/MerchantSplitAccount.vue';

import DebugPanel from './components/DebugPanel.vue';
import useMerchantFlow from './hooks/useMerchantFlow';
import { useFlowState } from './hooks/useFlowState';
import { usePageFlow } from './hooks/usePageFlow';
import { useComponentComm } from './hooks/useComponentComm';
import { useStepRenderer } from './hooks/useStepRenderer';
import { useDebug } from './hooks/useDebug';

defineEmits(['close']);

const route = useRoute();
const { initMerchantFlow } = useMerchantFlow();

const {
  step,
  isResubmit,
  flowControl,
  isCommission,
  setFlowStatus,
  updateFlowStatus,
  shouldShowVerifyStatus,
  setCommissionMode,
} = useFlowState();

// 页面流程控制
const {
  toSplitApply,
  toRealName,
  toSplitAccount,
  toJoinSuccess,
  toCommissionSuccess,
  formSubmit,
  splitSubmit,
  handleResubmit,
  refreshPage,
  handleBeforeClose,
  getPrevStepAction,
} = usePageFlow({
  step,
  isResubmit,
  flowControl,
  isCommission,
  setFlowStatus,
  updateFlowStatus,
});

// 统一组件通信机制（初始化事件处理系统）
useComponentComm({
  step,
  flowControl,
  isCommission,
  setFlowStatus,
  updateFlowStatus,
});

// 调试工具（传递主应用状态管理函数）
const debugState = useDebug({
  step,
  flowControl,
  setFlowStatus,
  isCommission,
  setCommissionMode,
});

// 步骤渲染控制
const {
  verifyStatusConfig,
  applyFormConfig,
  merchantActualNameConfig,
  merchantSplitAccountConfig,
  currentStepTitle: _currentStepTitle,
} = useStepRenderer({
  step: computed(() => step.value),
  flowControl: computed(() => flowControl.value),
  isCommission: computed(() => isCommission.value),
  isResubmit: computed(() => isResubmit.value),
  shouldShowVerifyStatus,
});

const teamId = computed(() => route.query.teamId as string || '');

onMountedOrActivated(() => {
  initMerchantFlow(teamId);
  updateFlowStatus();
});

defineExpose({
  handleBeforeClose,
});
</script>

<template>
  <div class="page-container">
    <div class="page-inner">
      <!-- 步骤指示器 -->
      <div class="step-wrap">
        <ApplyStep v-model="step" :is-commission="isCommission" />
      </div>

      <div class="component-wrapper">
        <!-- 验证状态组件 -->
        <VerifyStatus
          v-if="verifyStatusConfig.visible"
          v-model="flowControl[step].status"
          v-bind="verifyStatusConfig.props"
          @resubmit="handleResubmit"
          @close="$emit('close', route.name)"
          @to-real-name="toRealName"
          @to-split-account="toSplitAccount"
          @refresh-status="refreshPage"
        />

        <!-- 商户入网表单 -->
        <ApplyForm
          v-if="applyFormConfig.visible"
          :is-resubmit="applyFormConfig.props.isResubmit"
          :is-commission="applyFormConfig.props.isCommission"
          @submit="formSubmit"
        />

        <!-- 商户实名认证 -->
        <MerchantActualName
          v-if="merchantActualNameConfig.visible"
          v-bind="merchantActualNameConfig.props"
          @prev-step="toJoinSuccess"
          @next-step="toSplitApply"
        />

        <!-- 商户分账申请 -->
        <MerchantSplitAccount
          v-if="merchantSplitAccountConfig.visible"
          v-bind="merchantSplitAccountConfig.props"
          @prev-step="getPrevStepAction"
          @submit="splitSubmit"
          @success="toCommissionSuccess"
        />
      </div>
    </div>

    <!-- 调试面板（仅开发环境） -->
    <DebugPanel v-bind="debugState" />
  </div>
</template>

<style lang="less" scoped>
.page-container {
  width: 100%;
  // IMPORTANT: 设置最小高度，预留顶部导航栏空间，避免表单验证滚动（scroll-to-first-error）时遮挡导航栏
  min-height: calc(100vh - 40px);
  background: url('@/assets/workbench/bg_receipt.png') center no-repeat;
  background-size: cover;
  display: flex;
  justify-content: center;
  padding: 16px 0px 32px;
}

.page-inner {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 872px;
}

.step-wrap {
  display: flex;
  justify-content: center;
  width: 100%;
  padding: 16px 0px;
  background-color: #fff;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.component-wrapper {
  width: 100%;
  min-height: 400px;
  display: flex;
  flex-direction: column;
}
</style>
