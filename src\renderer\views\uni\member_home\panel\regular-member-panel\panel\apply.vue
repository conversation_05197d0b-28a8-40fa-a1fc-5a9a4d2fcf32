<template>
  <!-- v-lkloading="{ show:loading, height:false, opacity:false }" -->
  <div  class="container">
    <div class="header">
      <!-- 申请列表 -->
      <div class="h cursor" @click="onGoBack">
        <iconpark-icon
          name="iconarrowlift"
          style="font-size: 24px; color: #516082;"
        ></iconpark-icon>

        <span class="text">返回</span>
        <span class="line"></span>
        <span class="sTitle">{{ tabList[0].label }}</span>
        <span class="count" v-show="applyCount">（{{ applyCount }}）</span>
      </div>

      <!-- <div class="tabs">

        <template
          v-for="(tab, tabIndex) in tabList"
          :key="tabIndex"
        >
          <t-badge :offset="[10, -2]" :count="tab.value === 'PApplyMemberPanel' ? applyCount : activeCount">
            <span
              :class="{tab:true, cursor: true, active: tab.value === currentPanel}"
              @click="onSetPanel(tab)"
            >
              {{ tab.label }}
              <span class="line"></span>
            </span>
          </t-badge>
        </template>
      </div> -->
    </div>
    <div class="body">

      <component
        :is="panels[currentPanel ? currentPanel : '']"
        @onBrush="onBrushCount"
      />
    </div>

  </div>
</template>

<script setup lang="ts">
import { computed, reactive, ref, toRaw, watch } from "vue";
import { panels } from "@renderer/views/uni/member_home/panel/regular-member-panel/panel";
import { useUniStore } from "@renderer/views/uni/store/uni";
import { useRouter, useRoute } from "vue-router";
import { updateAllCount } from "@renderer/views/uni/hooks/total";
import { useI18n } from "vue-i18n";
import { platform } from "@renderer/views/digital-platform/utils/constant";
import { useDigitalPlatformStore } from "@renderer/views/digital-platform/store/digital-platform-store";
import { getUniTeamID } from "@renderer/views/uni/utils/auth";
import { onMountedOrActivated } from "@renderer/hooks/onMountedOrActivated";
import { refreshRegularAssociationBus } from "@renderer/views/digital-platform/utils/eventBus";
const { t } = useI18n();
const digitalPlatformStore = useDigitalPlatformStore();

const store = useUniStore();
const route = useRoute();
const emits = defineEmits(["onSetCurrentPanel", "onSetCurrentRow"]);
// const currentPanel = ref("PApplyMemberPanel");
const currentPanel = ref("PApplyMemberPanel");
const tabList = [
    {
        label: t('member.digital.q'), // 加入申请
        value: 'PApplyMemberPanel'
    },
    // {
    //     // label: '激活申请',
    //     label: t('member.impm.set_7'),
    //     value: 'PActiveMemberPanel'
    // }
];
// 帮我写下注解

const props = defineProps({
  platform: {
    type: String,
    default: '',
  },
})
// 平台类型 目前只有digital-platform
const platformCpt = computed(() => {
  return props.platform || route.query?.platform
})

const activeAccount = computed(() => {
  if(platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount
  } else if(platformCpt.value === platform.digitalWorkbench)  {
    return route.query;
  } else {
    return store.activeAccount
  }
})


const currentTeamId = computed(() => {
  if(platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount?.teamId
  } else if(platformCpt.value === platform.digitalWorkbench){
    return route.query?.teamId || 0;
  } else {
    return  getUniTeamID()
  }
})

const loading = ref(false);
const onGoBack = () => {
  emits("onSetCurrentPanel", "PRegular");
};
const onLoadingShow = () => {
  loading.value = true;
  setTimeout(() => {
    loading.value = false;

  }, 900);
};

const applyCount = ref(0);
const activeCount = ref(0);
const onSetPanel = (val) => {
  currentPanel.value = val.value;
  onLoadingShow();
  onBrushCount();
};
const onBrushCount = ()=> {
  updateAllCount(currentTeamId.value).then((res) => {
    console.log(res)
    if(res && res.length > 1) {
      activeCount.value = res[0].find((v) => v.teamId === currentTeamId.value)?.total || 0;
      applyCount.value = res[1].find((v) => v.teamId === currentTeamId.value)?.total || 0;
    }
  });
  refreshRegularAssociationBus.emit('refreshRegular', true);
}

// 进入入会或激活
// 用于判定来自其他bv跳转的问题
const goApplyOrActive = () => {
    // 检查路由查询参数是否存在，并且 origin 是否为 'message'
    if (route.query && route.query.origin === 'message') {
      // 从路由查询参数中解构出 teamId 和 redirect
      const { teamId, redirect } = route.query;
      // 检查 teamId 和 redirect 是否都存在
      if (teamId && redirect) {
        // 如果 redirect 包含 'PActiveMemberPanel'，则将当前面板设置为 'PActiveMemberPanel'
        if (redirect.includes('PActiveMemberPanel')) {
          currentPanel.value = 'PActiveMemberPanel';
        } else if (redirect.includes('PApplyMemberPanel')) {
          currentPanel.value = 'PApplyMemberPanel';
        }
        route.query.origin = '';
        onLoadingShow();
      }
    }
};

const onSetCurrentRow = (val) => {
  return new Promise((resolve) => {
    // 写一个await to请求

  })
}
// /**
//  * 帮我写一个跟这样一样结构的数据，数据为  Member: 'DP_BUSINESS',
//   Government: 'DP_GOVERNMENT',
//   CBD: 'DP_CBD',
//   Association: 'DP_COMMUNITY'
//  */
// const platformMap = {
//   [platform.digitalPlatform]: 'DP_BUSINESS',
//   [platform.digitalWorkbench]: 'DP_COMMUNITY',
//   [platform.digitalCBD]: 'DP_CBD',
// }


onMountedOrActivated(() => {
  goApplyOrActive();
  onBrushCount();
});


</script>

<style lang="less" scoped>
.container {
    .header {
        height: 56px;
        display: flex;
        align-items: center;
        justify-content:center;
        position: relative;
        border-bottom: 1px solid var(--divider-kyy-color-divider-light, #ECEFF5);

        .h {
            position: absolute;
            left: 16px;
            display: flex;
            align-items: center;
            .text {
              color: var(--text-kyy_color_text_1, #1A2139);

              /* kyy_fontSize_2/regular */
              font-family: "PingFang SC";
              font-size: 14px;
              font-style: normal;
              font-weight: 400;
              line-height: 22px; /* 157.143% */
            }

            .line {
              background: var(--divider-kyy_color_divider_deep, #D5DBE4);
              width: 1px;
              height: 16px;
              margin: 0 12px;
            }
            .sTitle {
              color: var(--text-kyy_color_text_1, #1A2139);

              /* kyy_fontSize_3/bold */
              font-family: "PingFang SC";
              font-size: 16px;
              font-style: normal;
              font-weight: 600;
              line-height: 24px; /* 150% */
            }
            .count {
              color: var(--text-kyy_color_text_1, #1A2139);

              /* kyy_fontSize_3/bold */
              font-family: "PingFang SC";
              font-size: 16px;
              font-style: normal;
              font-weight: 600;
              line-height: 24px; /* 150% */
            }
        }

        .tabs {
            display: flex;
            gap: 44px;
            .tab {
                color: var(--text-kyy-color-text-1, #1A2139);
                text-align: center;
                font-size: 16px;
                font-style: normal;
                font-weight: 400;
                line-height: 24px; /* 150% */
                position: relative;
                .line {
                    visibility: hidden;

                }
            }
            .active {
                color: var(--brand-kyy-color-brand-default, #4D5EFF);
                font-size: 16px;
                font-style: normal;
                font-weight: 600;
                line-height: 24px; /* 150% */

                .line {
                    visibility: visible;
                    position: absolute;
                    border-radius: 1.5px;
                    width: 16px;
                    height: 3px;
                    flex-shrink: 0;
                    background: var(--brand-kyy-color-brand-default, #4D5EFF);
                    bottom: -16px;
                    left: 0;
                    right: 0;
                    margin: 0 auto;
                }
            }
        }
    }
}

:deep(.t-badge) {
  margin-right: 0 !important;
}
:deep(.t-badge--dot) {
  right: 3px;
  margin-top: 2px;

}
:deep(.t-badge--circle) {

  color: var(--kyy_color_tag_text_magenta, #ff4aa1);

  background: var(--kyy_color_badge_bg, #FF4AA1) !important;
}

</style>
