<template>
  <t-dialog
    v-model:visible="visible"
    :header="'编辑提醒范围'"
    attach="body"
    width="440px"
  >
    <template #body>
      <div class="toBody">
        <!-- <div class="texts">{{ $t("applicationClassification") }}</div> -->
        <div class="radios">
          <t-radio-group v-model="form.type" range>
            <t-radio :value="1">全部会员</t-radio>
            <t-radio :value="2">自定义范围</t-radio>
          </t-radio-group>
        </div>
        <span v-show="form.type === 2" class="selectRange">
          <!-- <span
            v-for="(item, itemIndex) in form.members"
            :key="itemIndex"
            class="selectRange-item"
          >
            {{ item }}
            <svg
              class="iconpark-icon ml-4 cursor"
              style="width: 16px; height: 16px"
              @click="onRemoveRange(itemIndex)"
            >
              <use href="#close" />
            </svg>
          </span>
          <span class="selectRange-btn cursor">选择范围</span> -->

          <t-cascader
            v-model="form.members"
            :options="options"
            multiple
            clearable
          />
        </span>
      </div>
    </template>
    <template #closeBtn>
      <!-- <svg class="iconpark-icon" style="width: 16px; height: 16px">
        <use href="#close" />
      </svg> -->
      <iconpark-icon name="iconerror" style="font-size: 24px"></iconpark-icon>
    </template>
    <template #footer>
      <div class="footer">
        <t-button
          theme="default"
          variant="outline"
          @click="onClose"
        >取消</t-button>
        <t-button
          theme="primary"
          @click="onSaveAndTranslate"
        >保存并生效</t-button>
      </div>
    </template>
  </t-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, toRaw } from "vue";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
import { getResponseResult } from "@renderer/utils/myUtils";
import { MessagePlugin } from "tdesign-vue-next";

import { getMemberLevelAxios } from "@renderer/api/uni/api/businessApi";

const emits = defineEmits(["onEmits"]);

const form = reactive({
  type: 1,
  members: []
});

const options = ref([]);

const visible = ref(false);

// 获取会员职务列表
const onGetMembersAndJobs = () => {
  let result = null;

  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await getMemberLevelAxios();
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      resolve(result.data);
      // switchMemberJobs(result.data);
      options.value = result.data;
      console.log(options.value);
      // MessagePlugin.success(t("member.save") + t("member.success"));
    } catch (error) {
      const errMsg = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
      reject();
    }
  });
};

const switchMemberJobs = (arr) => {
  if (arr && arr.length > 0) {
    arr.forEach((element) => {
      element.label = element.level_name;
      element.value = `${element.id}`;
      // element.value = element.level;
      element.children = element.members
        ? element.members.map((v) => {
          v.label = v.name;
          v.value = `${element.id}-${v.id}`;
          return v;
        })
        : [];
    });
  }
};

const onRemoveRange = (index) => {
  form.members.splice(index, 1);
};

const onSaveAndTranslate = () => {
  emits("onEmits", toRaw(form));
};

const onOpen = (data) => {
  onGetMembersAndJobs().then(() => {
    form.type = data.type;
    form.members = data.members;
    visible.value = true;
  });
  // form.type = data.type;
  // form.members = data.members;
  // visible.value = true;
};
const onClose = () => {
  visible.value = false;
};

defineExpose({
  onOpen,
  onClose
});
</script>

<style scoped lang="less">
.toBody {
  // height: 76px;
}
:deep(.t-radio-group) {
  flex-direction: column;
  align-items: flex-start;
}

.selectRange {
  margin-top: 8px;
  display: flex;
  gap: 8px;
  &-item {
    padding: 0 8px;
    background: #f1f2f5;
    border-radius: 4px;

    font-size: 14px;

    font-weight: 400;
    color: #13161b;
    display: flex;
    align-items: center;
  }
  &-btn {
    font-size: 14px;

    font-weight: 400;
    color: #2069e3;
  }
}
</style>
