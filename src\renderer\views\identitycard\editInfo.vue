<template>
  <div class="main-card">
    <div class="w-close">
      <div class="header-title">
        {{
          cardIdTypeName === "personal"
            ? t("identity.editPersonalInfo")
            : t("identity.editOrgInfo")
        }}
      </div>
      <div class="btn-item close" @click="closeCard" />
    </div>
    <div class="w-header">
      <t-space style="align-items: center;gap: 12px;">
        <div class="box-avatar">
          <!--          <t-upload-->
          <!--            :disabled="formDataInner.avatarEditable === 0"-->
          <!--            v-model="images"-->
          <!--            theme="custom"-->
          <!--            accept="image/*"-->
          <!--            :request-method="uploadImage"-->
          <!--            :before-upload="beforeUpload"-->
          <!--          >-->
          <!--            <div class="avatar-btn" v-if="formDataInner.avatarEditable === 1"></div>-->
          <!--          </t-upload>-->
          <!-- <div
            v-if="formDataInner.avatarEditable === 1"
            class="avatar-btn"
            @click="openUploadAvatar(cardInfo.avatar)"
          /> -->
          <t-image-viewer :images="[cardInfo.avatar]">
            <template #trigger="{ open }">
              <div class="tdesign-demo-image-viewer__ui-image" @mouseover="showAct" @mouseleave="hiddenAct">
                <kyy-avatar
                  avatar-size="44px"
                  :image-url="cardInfo.avatar"
                  :user-name="cardInfo.name"
                  roundRadius
                  @click="cardInfo.avatar && open()"
                />
                <div :class="['kyy-avatar-bg', hoverValue ? 'kyy-avatar-hover' : '']">
                  <img @click="openUploadAvatar(cardInfo.avatar)" src="../../assets/identity/icon_edit.svg" alt="" />
                </div>
              </div>
              <UploadAvatar
                ref="uploadAvatarRef"
                @confirm="uploadAvatarConfirm"
                @close="onCancelUpload"
              />
            </template>
          </t-image-viewer>
        </div>
        <div class="w-1">
          <div class="nickname-box" @click="editNickName">
            <div v-if="!isEditName" style="display: flex;align-items: center;">
              <div class="nickname">{{ cardInfo.name }}</div>
              <div
                v-if="formDataInner.nameEditable === 1"
                class="btn-item edit"
              />
            </div>

            <div v-else class="t-text ellipsis">
              <t-input
                ref="cardInfoNameRef"
                v-model="cardInfoName"
                maxlength="20"
                @blur="endNameInput"
              />
            </div>
          </div>
          <div style="height: 6px" />
          <t-space v-if="cardIdTypeName === 'personal'">
            <div class="nickname-box" @click="showEditLkId && editLkId()">
              <div>{{ t("identity.lkID") }}:</div>
              <div class="lkId">{{ cardInfo.linkId }}</div>
              <div v-if="showEditLkId" class="btn-item edit" />
            </div>
          </t-space>
        </div>
      </t-space>
    </div>

    <div class="w-info">
      <div class="personal_tabs">
        <div
          :class="[
            'default-tab-item',
            tabValue === 1 ? 'active-tab-item' : ''
          ]"
        >
          <t-button
            theme="default"
            variant="text"
            @click="tabValue = 1"
          >{{ t("identity.moreInfo") }}</t-button>
          <div class="tab-item-border"></div>
        </div>
        <div
          v-if="cardIdTypeName !== 'inner' && cardIdTypeName !== 'platform'"
          :class="[
            'default-tab-item',
            tabValue === 2 ? 'active-tab-item' : ''
          ]"
        >
          <t-button
            theme="default"
            variant="text"
            @click="tabValue = 2"
          >{{ t("identity.otherOrg") }}</t-button>
          <div class="tab-item-border"></div>
        </div>
        <div
          v-if="bussinessList.length"
          :class="[
            'default-tab-item',
            tabValue === 3 ? 'active-tab-item' : ''
          ]"
        >
          <t-button
            theme="default"
            variant="text"
            @click="tabValue = 3"
          >{{ t("identity.relatedBusinessAssociation") }}</t-button>
          <div class="tab-item-border"></div>
        </div>
      </div>
      <div class="t-tabs">
        <div
          :class="['t-tab-panel', cardIdTypeName === 'personal' ? '' : 't-tab-panel-height']"
          v-if="tabValue === 1"
          :value="1"
          :label="t('identity.moreInfo')"
          :destroy-on-hide="false"
        >
          <t-form
            :key="personalFormKey"
            v-if="cardIdTypeName === 'personal'"
            id="form"
            ref="form"
            :rules="FORM_RULES"
            :data="formData"
            :colon="true"
            @submit="onSubmit"
          >
            <draggable
              class="form-item-box"
              style="padding: 0;"
              item-key="type"
              ghost-class="form-item-ghostClass"
              chosen-class="form-item-chosenClass"
              :list="formData.options"
              handle=".btn-item"
              @end="draggableEnd"
            >
              <template #item="{ element, index }">
                <div v-if="element.type === 'phone'" class="form-item-box">
                  <div class="form-item">
                    <div class="form-item-label-box">
                      <div class="btn-item item" />
                      <div class="form-item-label">
                        {{ t("identity.phone") }}
                      </div>
                    </div>
                    <div class="form-item-select" v-if="element.visible">

                      <t-popup placement="left" :visible="element.showSelect" showArrow overlayClassName="identity-visible-popup" :disabled="!showMemberPopup(element)">
                        <t-select v-replace-svg  autoWidth :popup-props="{overlayClassName: 'identity-visible-popup-select'}" class="select-hover exclude-downfill"
                          :value="element.visible_type" @click="element.showSelect = false" @mouseenter="showMemberPopup(element) && (element.showSelect = true)" @mouseleave="element.showSelect = false"
                        >
                          <t-option
                            @click="itemChange(optionItem.value, element, {form: 'formData', key: 'phone'})"
                            v-for="optionItem in showTypeOptions"
                            :key="optionItem.value"
                            :value="optionItem.value"
                            :label="optionItem.label"
                            ></t-option>
                        </t-select>
                        <template #content>
                          <div class="member-popup-box">
                            <div class="member-popup-title">{{ showshowMemberPopupTitle(element) }}</div>
                            <div class="member-popup-content">
                              <span
                                v-for="(name, nameIndex) in element.visible_users_name"
                                :key="nameIndex"
                                >
                                {{ `${name}${nameIndex === element.visible_users_name?.length - 1 ? '' : '、'}` }}
                              </span>
                            </div>
                          </div>
                        </template>
                      </t-popup>
                    </div>
                  </div>
                  <t-form-item
                    v-for="(item, index_in) in formData.newTelItemList"
                    style="margin-bottom: 8px"
                    label=""
                    label-width="36px"
                    :required-mark="false"
                    :name="`newTelItemList[${index_in}].phone`"
                  >
                    <t-input-adornment>
                      <template #append>
                        <img
                          v-if="index_in === 0"
                          class="close-icon"
                          src="../../assets/identity/add.svg"
                          alt=""
                          @click="addTelItem"
                        />
                        <t-popconfirm
                          v-else
                          class="close-popconfirm"
                          :content="t('identity.deleteField')"
                          placement="top-right"
                          :popup-props="{overlayClassName: 'close-popconfirm-popup'}"
                          :confirm-btn="{
                            content: t('identity.delete'),
                            theme: 'danger'
                          }"
                          @confirm="deleteTelItem(index_in)"
                        >
                          <template #icon>
                            <img
                              class="close-icon"
                              src="../../assets/identity/icon_info.svg"
                              alt=""
                            />
                          </template>
                          <img
                            class="close-icon"
                            src="../../assets/identity/0.icon_icon-close-fill.png"
                            alt=""
                          />
                        </t-popconfirm>
                      </template>
                      <template #prepend>
                        <!--                      <t-select v-replace-svg  v-model="item.code" :options="telTypeOptions">-->
                        <!--                      </t-select>-->
                        <area-code v-model="formData.newTelItemList[index_in].code" />
                      </template>
                      <t-input
                        v-model="formData.newTelItemList[index_in].phone"
                        :placeholder="t('identity.inputContent')"
                        type="number"
                        @enter="onEnter"
                      />
                    </t-input-adornment>
                  </t-form-item>
                </div>

                <div v-else-if="element.type === 'email'" class="form-item-box">
                  <div class="form-item">
                    <div class="form-item-label-box">
                      <div class="btn-item item" />
                      <div class="form-item-label">
                        {{ t("identity.email") }}
                      </div>
                    </div>
                    <div class="form-item-select" v-if="element.visible">

                      <t-popup placement="left" :visible="element.showSelect" showArrow overlayClassName="identity-visible-popup" :disabled="!showMemberPopup(element)">
                        <t-select v-replace-svg  autoWidth :popup-props="{overlayClassName: 'identity-visible-popup-select'}" class="select-hover exclude-downfill"
                          :value="element.visible_type" @click="element.showSelect = false" @mouseenter="showMemberPopup(element) && (element.showSelect = true)" @mouseleave="element.showSelect = false"
                        >
                          <t-option
                            @click="itemChange(optionItem.value, element, {form: 'formData', key: 'email'})"
                            v-for="optionItem in showTypeOptions"
                            :key="optionItem.value"
                            :value="optionItem.value"
                            :label="optionItem.label"
                            ></t-option>
                        </t-select>
                        <template #content>
                          <div class="member-popup-box">
                            <div class="member-popup-title">{{ showshowMemberPopupTitle(element) }}</div>
                            <div class="member-popup-content">
                              <span
                                v-for="(name, nameIndex) in element.visible_users_name"
                                :key="nameIndex"
                                >
                                {{ `${name}${nameIndex === element.visible_users_name?.length - 1 ? '' : '、'}` }}
                              </span>
                            </div>
                          </div>
                        </template>
                      </t-popup>
                    </div>
                  </div>
                  <t-form-item
                    label=""
                    label-width="36px"
                    :required-mark="false"
                    name="email"
                  >
                    <t-input
                      v-model="element.value"
                      :placeholder="t('identity.inputContent')"
                      @enter="onEnter"
                    />
                  </t-form-item>
                </div>

                <div
                  v-else-if="element.type === 'gender'"
                  class="form-item-box"
                >
                  <div class="form-item">
                    <div class="form-item-label-box">
                      <div class="btn-item item" />
                      <div class="form-item-label">
                        {{ t("identity.gender") }}
                      </div>
                    </div>
                    <div class="form-item-select" v-if="element.visible">
                      <t-popup placement="left" :visible="element.showSelect" showArrow overlayClassName="identity-visible-popup" :disabled="!showMemberPopup(element)">
                        <t-select v-replace-svg  autoWidth :popup-props="{overlayClassName: 'identity-visible-popup-select'}" class="select-hover exclude-downfill"
                          :value="element.visible_type" @click="element.showSelect = false" @mouseenter="showMemberPopup(element) && (element.showSelect = true)" @mouseleave="element.showSelect = false"
                        >
                          <t-option
                            @click="itemChange(optionItem.value, element, {form: 'formData', key: 'gender'})"
                            v-for="optionItem in showTypeOptions"
                            :key="optionItem.value"
                            :value="optionItem.value"
                            :label="optionItem.label"
                            ></t-option>
                        </t-select>
                        <template #content>
                          <div class="member-popup-box">
                            <div class="member-popup-title">{{ showshowMemberPopupTitle(element) }}</div>
                            <div class="member-popup-content">
                              <span
                                v-for="(name, nameIndex) in element.visible_users_name"
                                :key="nameIndex"
                                >
                                {{ `${name}${nameIndex === element.visible_users_name?.length - 1 ? '' : '、'}` }}
                              </span>
                            </div>
                          </div>
                        </template>
                      </t-popup>
                    </div>
                  </div>
                  <t-form-item
                    label=""
                    label-width="36px"
                    :required-mark="false"
                    name="gender"
                  >
                    <t-radio-group v-model="element.value">
                      <t-radio value="1">{{ t("identity.man") }}</t-radio>
                      <t-radio value="2">{{ t("identity.woman") }}</t-radio>
                    </t-radio-group>
                  </t-form-item>
                </div>

                <div
                  v-else-if="element.type === 'birthday'"
                  class="form-item-box"
                >
                  <div class="form-item">
                    <div class="form-item-label-box">
                      <div class="btn-item item" />
                      <div class="form-item-label">
                        {{ t("identity.birthday") }}
                      </div>
                    </div>
                    <div class="form-item-select" v-if="element.visible">
                      <t-popup placement="left" :visible="element.showSelect" showArrow overlayClassName="identity-visible-popup" :disabled="!showMemberPopup(element)">
                        <t-select v-replace-svg  autoWidth :popup-props="{overlayClassName: 'identity-visible-popup-select'}" class="select-hover exclude-downfill"
                          :value="element.visible_type" @click="element.showSelect = false" @mouseenter="showMemberPopup(element) && (element.showSelect = true)" @mouseleave="element.showSelect = false"
                        >
                          <t-option
                            @click="itemChange(optionItem.value, element, {form: 'formData', key: 'birthday'})"
                            v-for="optionItem in showTypeOptions"
                            :key="optionItem.value"
                            :value="optionItem.value"
                            :label="optionItem.label"
                            ></t-option>
                        </t-select>
                        <template #content>
                          <div class="member-popup-box">
                            <div class="member-popup-title">{{ showshowMemberPopupTitle(element) }}</div>
                            <div class="member-popup-content">
                              <span
                                v-for="(name, nameIndex) in element.visible_users_name"
                                :key="nameIndex"
                                >
                                {{ `${name}${nameIndex === element.visible_users_name?.length - 1 ? '' : '、'}` }}
                              </span>
                            </div>
                          </div>
                        </template>
                      </t-popup>
                    </div>
                  </div>
                  <t-form-item
                    label=""
                    label-width="36px"
                    :required-mark="false"
                    name="birthday"
                  >
                    <t-date-picker
                      :popup-props="{overlayClassName: 'date-birthday-picker'}"
                      v-model="element.value"
                      :placeholder="t('identity.Y_M_D')"
                      :disable-date="{ after: moment().format('YYYY-MM-DD') }"
                    >
                      <template #suffixIcon>
                        <img src="../../assets/identity/date_picker.svg" alt="">
                      </template>
                    </t-date-picker>
                  </t-form-item>
                </div>

                <div v-else-if="element.type === 'area'" class="form-item-box">
                  <div class="form-item">
                    <div class="form-item-label-box">
                      <div class="btn-item item" />
                      <div class="form-item-label">
                        {{ t("identity.area") }}
                      </div>
                    </div>
                    <div class="form-item-select" v-if="element.visible">
                      <t-popup placement="left" :visible="element.showSelect" showArrow overlayClassName="identity-visible-popup" :disabled="!showMemberPopup(element)">
                        <t-select v-replace-svg  autoWidth :popup-props="{overlayClassName: 'identity-visible-popup-select'}" class="select-hover exclude-downfill"
                          :value="element.visible_type" @click="element.showSelect = false" @mouseenter="showMemberPopup(element) && (element.showSelect = true)" @mouseleave="element.showSelect = false"
                        >
                          <t-option
                            @click="itemChange(optionItem.value, element, {form: 'formData', key: 'area'})"
                            v-for="optionItem in showTypeOptions"
                            :key="optionItem.value"
                            :value="optionItem.value"
                            :label="optionItem.label"
                            ></t-option>
                        </t-select>
                        <template #content>
                          <div class="member-popup-box">
                            <div class="member-popup-title">{{ showshowMemberPopupTitle(element) }}</div>
                            <div class="member-popup-content">
                              <span
                                v-for="(name, nameIndex) in element.visible_users_name"
                                :key="nameIndex"
                                >
                                {{ `${name}${nameIndex === element.visible_users_name?.length - 1 ? '' : '、'}` }}
                              </span>
                            </div>
                          </div>
                        </template>
                      </t-popup>
                    </div>
                  </div>
                  <t-form-item
                    label=""
                    label-width="36px"
                    :required-mark="false"
                    name="area"
                  >
                    <t-cascader
                      v-model="element.value"
                      :options="regionOptions"
                      :keys="{
                        label: 'name',
                        value: 'code',
                        children: 'children'
                      }"
                      :load="onRegionLoad"
                      value-type="full"
                      clearable
                      :placeholder="t('identity.selectArea')"
                      @change="onRegionChange"
                    />
                  </t-form-item>
                </div>

                <div v-else-if="element?.type?.startsWith('custom_')" class="form-item-box newInfoItemList">
                  <div class="form-item">
                      <div class="form-item-label-box">
                        <div class="btn-item item" />
                        <div class="form-item-label">
                          <t-form-item
                            label=""
                            label-width="0"
                            :name="`options[${index}].name`"
                          >
                            <div
                              v-if="element.name && (element.showLabel || element.showLabel === undefined)"
                              class="newInfoItemLabel"
                              @click="editNewInfoItemLabel(index)"
                            >
                              <span class="label">{{ element.name }}</span>
                              <img
                                class="edit-icon"
                                src="../../assets/identity/0.icon_icon-edit.svg"
                                alt=""
                              />
                            </div>
                            <t-input
                              v-else
                              v-model="formData.options[index].name"
                              auto-width
                              autofocus
                              class="newInfoItemLabel"
                              maxlength="10"
                              :placeholder="t('identity.inputInfoName')"
                              @blur="element.showLabel = true"
                              @enter="
                                () => {
                                  element.showLabel = true;
                                  onEnter();
                                }
                              "
                            />
                          </t-form-item>
                        </div>
                      </div>
                      <div class="form-item-select" v-if="element.visible">

                        <t-popup placement="left" :visible="element.showSelect" showArrow overlayClassName="identity-visible-popup" :disabled="!showMemberPopup(element)">
                            <t-select v-replace-svg  autoWidth :popup-props="{overlayClassName: 'identity-visible-popup-select'}"
                              :value="formData.options[index].visible_type"
                              class="newInfoItem-select select-hover exclude-downfill"
                              @click="element.showSelect = false" @mouseenter="showMemberPopup(element) && (element.showSelect = true)" @mouseleave="element.showSelect = false"
                            >
                              <t-option
                                @click="itemChange(optionItem.value, element, {form: 'newInfoItemList', key: index})"
                                v-for="optionItem in showTypeOptions"
                                :key="optionItem.value"
                                :value="optionItem.value"
                                :label="optionItem.label"
                                ></t-option>
                            </t-select>
                            <template #content>
                              <div class="member-popup-box">
                                <div class="member-popup-title">{{ showshowMemberPopupTitle(element) }}</div>
                                <div class="member-popup-content">
                                  <span
                                    v-for="(name, nameIndex) in element.visible_users_name"
                                    :key="nameIndex"
                                    >
                                    {{ `${name}${nameIndex === element.visible_users_name?.length - 1 ? '' : '、'}` }}
                                  </span>
                                </div>
                              </div>
                            </template>
                          </t-popup>
                        <t-popconfirm
                          class="close-popconfirm"
                          :content="t('identity.deleteField')"
                          placement="top-right"
                          :popup-props="{overlayClassName: 'close-popconfirm-popup'}"
                          :confirm-btn="{
                            content: t('identity.delete'),
                            theme: 'danger'
                          }"
                          @confirm="deleteInfoItem(index)"
                        >
                          <template #icon>
                            <img
                              class="close-icon"
                              src="../../assets/identity/icon_info.svg"
                              alt=""
                            />
                          </template>
                          <template #default>
                            <div
                              v-if="element.name || element.value"
                              class="btn-item delete"
                            />
                            <div
                              v-else
                              class="btn-item delete"
                              @click="deleteInfoItem(index)"
                            />
                          </template>
                        </t-popconfirm>
                      </div>
                    </div>
                    <t-form-item
                      label=""
                      label-width="36px"
                      :required-mark="false"
                      :name="`options[${index}].value`"
                    >
                      <t-input
                        v-model="formData.options[index].value"
                        :placeholder="t('identity.inputContent')"
                        maxlength="50"
                        @enter="onEnter"
                      />
                    </t-form-item>
                </div>

              </template>
            </draggable>

            <!-- <div style="margin-bottom: 34px">
              <div
                v-for="(item, index) in formData.newInfoItemList"
                :key="index"
                class="form-item-box newInfoItemList"
              >
                <div class="form-item">
                  <div class="form-item-label-box">
                    <div class="btn-item item" />
                    <div class="form-item-label">
                      <t-form-item
                        label=""
                        label-width="0"
                        :name="`newInfoItemList[${index}].name`"
                      >
                        <div
                          v-if="item.name && (item.showLabel || item.showLabel === undefined)"
                          class="newInfoItemLabel"
                          @click="editNewInfoItemLabel(index)"
                        >
                          <span class="label">{{ item.name }}</span>
                          <img
                            class="edit-icon"
                            src="../../assets/identity/0.icon_icon-edit.svg"
                            alt=""
                          />
                        </div>
                        <t-input
                          v-else
                          v-model="formData.newInfoItemList[index].name"
                          auto-width
                          autofocus
                          class="newInfoItemLabel"
                          maxlength="10"
                          :placeholder="t('identity.inputInfoName')"
                          @blur="item.showLabel = true"
                          @enter="
                            () => {
                              item.showLabel = true;
                              onEnter();
                            }
                          "
                        />
                      </t-form-item>
                    </div>
                  </div>
                  <div class="form-item-select" v-if="item.visible">

                    <t-popup placement="left" showArrow overlayClassName="identity-visible-popup" :disabled="!showMemberPopup(item)">
                        <t-select v-replace-svg  autoWidth :popup-props="{overlayClassName: 'identity-visible-popup-select'}"
                          :value="formData.newInfoItemList[index].visible_type"
                          class="newInfoItem-select select-hover exclude-downfill"
                        >
                          <t-option
                            @click="itemChange(optionItem.value, item, {form: 'newInfoItemList', key: index})"
                            v-for="optionItem in showTypeOptions"
                            :key="optionItem.value"
                            :value="optionItem.value"
                            :label="optionItem.label"
                            ></t-option>
                        </t-select>
                        <template #content>
                          <div class="member-popup-box">
                            <div class="member-popup-title">{{ showshowMemberPopupTitle(item) }}</div>
                            <div class="member-popup-content">
                              <span
                                v-for="(name, nameIndex) in item.visible_users_name"
                                :key="nameIndex"
                                >
                                {{ `${name}${nameIndex === item.visible_users_name?.length - 1 ? '' : '、'}` }}
                              </span>
                            </div>
                          </div>
                        </template>
                      </t-popup>
                    <t-popconfirm
                      class="close-popconfirm"
                      :content="t('identity.deleteField')"
                      placement="top-right"
                      :popup-props="{overlayClassName: 'close-popconfirm-popup'}"
                      :confirm-btn="{
                        content: t('identity.delete'),
                        theme: 'danger'
                      }"
                      @confirm="deleteInfoItem(index)"
                    >
                      <template #icon>
                        <img
                          class="close-icon"
                          src="../../assets/identity/icon_info.svg"
                          alt=""
                        />
                      </template>
                      <template #default>
                        <div
                          v-if="item.name || item.value"
                          class="btn-item delete"
                        />
                        <div
                          v-else
                          class="btn-item delete"
                          @click="deleteInfoItem(index)"
                        />
                      </template>
                    </t-popconfirm>
                  </div>
                </div>
                <t-form-item
                  label=""
                  label-width="36px"
                  :required-mark="false"
                  :name="`newInfoItemList[${index}].value`"
                >
                  <t-input
                    v-model="formData.newInfoItemList[index].value"
                    :placeholder="t('identity.inputContent')"
                    maxlength="50"
                    @enter="onEnter"
                  />
                </t-form-item>
              </div>
            </div> -->

            <div
              class="form-item-box"
              style="
                position: fixed;
                bottom: 4px;
                width: calc(100vw - 48px);
                padding: 24px 0 20px 0;
                background: white;
                z-index: 2;
              "
            >
              <div class="form-item" style="margin-bottom: 0;">
                <div class="form-item-label-box" @click="addInfoItem">
                  <div class="btn-item add" />
                  <div class="form-item-label add-label">
                    {{ t("identity.addInfo") }}
                  </div>
                </div>
                <div class="form-item-select">
                  <t-form-item>
                    <t-space size="small">
                      <t-button style="width: 80px;" theme="primary" type="submit" :disabled='isEditName'>{{
                        t("identity.save")
                      }}</t-button>
                    </t-space>
                  </t-form-item>
                </div>
              </div>
            </div>
          </t-form>
          <t-form
            v-else
            ref="formInner"
            :rules="FORM_RULES_INNER"
            :data="formDataInner"
            :colon="false"
            scroll-to-first-error="smooth"
            @submit="onSubmitInner"
          >
            <t-form-item name="showSquare" labelAlign="left" label-width="220px" class="mb-4 mt-8" style="padding: 8px;margin-bottom: 4px;">
              <template #label>
                <span class="color-text-1 font-bold" style="font-weight: 600;">显示个人广场号</span>
              </template>
              <div class="flex justify-end w-full">
                <t-switch v-model="squareEnable" :custom-value="[1, 0]" @change="squareEnableChange" />
              </div>
            </t-form-item>

            <t-form-item v-if="cardIdTypeName === 'inner' || cardIdTypeName === 'platform'" labelAlign="left" label-width="220px" class="mb-4" style="padding: 8px;margin-bottom: 4px;">
              <template #label>
                <span class="color-text-1 font-bold" style="font-weight: 600;">显示个人身份</span>
              </template>
              <div class="flex justify-end w-full">
                <t-switch v-model="cardInfo.is_card" :custom-value="[1, 0]" />
              </div>
            </t-form-item>

            <t-form-item v-if="cardIdTypeName === 'platform' || cardInfo.contact_auth" name="showSquare" labelAlign="left" label-width="220px" class="mb-4 form-item-box" style="margin-bottom: 4px;">
              <template #label>
                <span class="color-text-1 font-bold" style="font-weight: 600;">谁可联系我</span>
              </template>
              <div class="flex justify-end w-full form-item" style="justify-content: end;">
                <div class="form-item-select">

                  <t-popup placement="left" showArrow overlayClassName="identity-visible-popup" :disabled="!showMemberPopup(formDataInner.contactData)">
                          <t-select v-replace-svg  autoWidth :popup-props="{overlayClassName: 'identity-visible-popup-select'}" class="select-hover exclude-downfill"
                            :value="formDataInner.contactData.visible_type"
                          >
                            <t-option
                              @click="itemChange(optionItem.value, formDataInner.contactData, {form: 'formDataInner', key: 'contactData', type: 'contactData'}, 'contactData')"
                              v-for="optionItem in showTypeOptionsContact"
                              :key="optionItem.value"
                              :value="optionItem.value"
                              :label="optionItem.label"
                              ></t-option>
                          </t-select>
                        <template #content>
                          <div class="member-popup-box">
                            <div class="member-popup-title">{{ showshowMemberPopupTitle(formDataInner.contactData) }}</div>
                            <div class="member-popup-content">
                              <span
                                v-for="(name, nameIndex) in formDataInner.contactData.visible_users_name"
                                :key="nameIndex"
                                >
                                {{ `${name}${nameIndex === formDataInner.contactData.visible_users_name?.length - 1 ? '' : '、'}` }}
                              </span>
                            </div>
                          </div>
                        </template>
                      </t-popup>
                </div>
              </div>
            </t-form-item>

            <div class="form-item-box">
              <div class="form-item">
                <div class="form-item-label-box">
                  <!-- <div class="btn-item item" /> -->
                  <div class="form-item-label" style="margin-left: 0;">{{ t("identity.phone") }}</div>
                </div>
                <div class="form-item-select" v-if="formDataInner?.phoneData?.visible">
                      <t-popup placement="left" showArrow overlayClassName="identity-visible-popup" :disabled="!showMemberPopup(formDataInner?.phoneData)">
                        <t-select v-replace-svg  autoWidth :popup-props="{overlayClassName: 'identity-visible-popup-select'}" class="select-hover exclude-downfill"
                          :value="formDataInner.phoneData.visible_type"
                        >
                          <t-option
                            @click="itemChange(optionItem.value, formDataInner?.phoneData, {form: 'formDataInner', key: 'phoneData', type: 'phone'})"
                            v-for="optionItem in showTypeOptions"
                            :key="optionItem.value"
                            :value="optionItem.value"
                            :label="optionItem.label"
                            ></t-option>
                        </t-select>
                        <template #content>
                          <div class="member-popup-box">
                            <div class="member-popup-title">{{ showshowMemberPopupTitle(formDataInner?.phoneData) }}</div>
                            <div class="member-popup-content">
                              <span
                                v-for="(name, nameIndex) in formDataInner?.phoneData?.visible_users_name"
                                :key="nameIndex"
                                >
                                {{ `${name}${nameIndex === formDataInner?.phoneData?.visible_users_name?.length - 1 ? '' : '、'}` }}
                              </span>
                            </div>
                          </div>
                        </template>
                      </t-popup>
                    </div>
              </div>
              <t-form-item
                v-for="(item, index_in) in formDataInner.newTelItemList"
                :style="index_in === formDataInner.newTelItemList?.length - 1 ? 'margin-bottom: 0' : 'margin-bottom: 8px'"
                label=""
                label-width="0"
                :required-mark="false"
                :name="`newTelItemList[${index_in}].phone`"
              >
                <t-input-adornment>
                  <template v-if="formDataInner.phoneEditable === 1" #append>
                    <img
                      v-if="index_in === 0 && formDataInner.phoneEditable === 1"
                      class="close-icon"
                      src="../../assets/identity/add.svg"
                      alt=""
                      @click="addTelItem"
                    />
                    <t-popconfirm
                      v-else
                      class="close-popconfirm"
                      :content="t('identity.deleteField')"
                      placement="top-right"
                      :popup-props="{overlayClassName: 'close-popconfirm-popup'}"
                      :confirm-btn="{
                        content: t('identity.delete'),
                        theme: 'danger'
                      }"
                      @confirm="deleteTelItem(index_in)"
                    >
                      <template #icon>
                        <img
                          class="close-icon"
                          src="../../assets/identity/icon_info.svg"
                          alt=""
                        />
                      </template>
                      <img
                        v-if="formDataInner.phoneEditable === 1"
                        class="close-icon"
                        src="../../assets/identity/0.icon_icon-close-fill.png"
                        alt=""
                      />
                    </t-popconfirm>
                  </template>
                  <template #prepend>
                    <t-select v-replace-svg
                      v-if="formDataInner.phoneEditable === 0"
                      v-model="formDataInner.newTelItemList[index_in].code"
                      :options="telTypeOptions"
                      disabled
                    />
                    <area-code v-else v-model="formDataInner.newTelItemList[index_in].code" />
                  </template>
                  <t-input
                    v-model="formDataInner.newTelItemList[index_in].phone"
                    :disabled="formDataInner.phoneEditable === 0"
                    :placeholder="t('identity.inputContent')"
                    type="number"
                    @enter="onEnter"
                  />
                </t-input-adornment>
              </t-form-item>
            </div>

            <div class="form-item-box" v-if="!(cardIdTypeName === 'platform' && !formDataInner.jobNumberData.value)">
              <div class="form-item">
                <div class="form-item-label-box">
                  <!-- <div class="btn-item item" /> -->
                  <div class="form-item-label"  style="margin-left: 0;">
                    {{ cardIdTypeName === 'platform' ? t("identity.associationJob") : t("identity.jobNumber") }}
                  </div>
                </div>
                <div class="form-item-select" v-if="formDataInner?.jobNumberData?.visible">
                      <t-popup placement="left" showArrow overlayClassName="identity-visible-popup" :disabled="!showMemberPopup(formDataInner?.jobNumberData)">
                        <t-select v-replace-svg  autoWidth :popup-props="{overlayClassName: 'identity-visible-popup-select'}" class="select-hover exclude-downfill"
                          :value="formDataInner.jobNumberData.visible_type"
                        >
                          <t-option
                            @click="itemChange(optionItem.value, formDataInner?.jobNumberData, {form: 'formDataInner', key: 'jobNumberData', type: 'jobNumber'})"
                            v-for="optionItem in showTypeOptions"
                            :key="optionItem.value"
                            :value="optionItem.value"
                            :label="optionItem.label"
                            ></t-option>
                        </t-select>
                        <template #content>
                          <div class="member-popup-box">
                            <div class="member-popup-title">{{ showshowMemberPopupTitle(formDataInner?.jobNumberData) }}</div>
                            <div class="member-popup-content">
                              <span
                                v-for="(name, nameIndex) in formDataInner?.jobNumberData?.visible_users_name"
                                :key="nameIndex"
                                >
                                {{ `${name}${nameIndex === formDataInner?.jobNumberData?.visible_users_name?.length - 1 ? '' : '、'}` }}
                              </span>
                            </div>
                          </div>
                        </template>
                      </t-popup>
                    </div>
              </div>
              <t-form-item
                label=""
                label-width="0"
                :required-mark="false"
                name="email"
              >
                <t-input
                  v-model="formDataInner.jobNumberData.value"
                  :disabled="formDataInner.jobNumberData.editable === 0"
                  :placeholder="
                    formDataInner.jobNumberData.editable === 0
                      ? '--'
                      : t('identity.inputContent')
                  "
                  @enter="onEnter"
                />
              </t-form-item>
            </div>

            <div class="form-item-box" v-if="cardIdTypeName !== 'platform'">
              <div class="form-item">
                <div class="form-item-label-box">
                  <!-- <div class="btn-item item" /> -->
                  <div class="form-item-label"  style="margin-left: 0;">
                    {{ t("identity.department_position") }}
                  </div>
                </div>
                <div class="form-item-select" v-if="formDataInner?.positionsData?.visible">
                      <t-popup placement="left" showArrow overlayClassName="identity-visible-popup" :disabled="!showMemberPopup(formDataInner?.positionsData)">
                        <t-select v-replace-svg  autoWidth :popup-props="{overlayClassName: 'identity-visible-popup-select'}" class="select-hover exclude-downfill"
                          :value="formDataInner.positionsData.visible_type"
                        >
                          <t-option
                            @click="itemChange(optionItem.value, formDataInner?.positionsData, {form: 'formDataInner', key: 'positionsData', type: 'positions'})"
                            v-for="optionItem in showTypeOptions"
                            :key="optionItem.value"
                            :value="optionItem.value"
                            :label="optionItem.label"
                            ></t-option>
                        </t-select>
                        <template #content>
                          <div class="member-popup-box">
                            <div class="member-popup-title">{{ showshowMemberPopupTitle(formDataInner?.positionsData) }}</div>
                            <div class="member-popup-content">
                              <span
                                v-for="(name, nameIndex) in formDataInner?.positionsData?.visible_users_name"
                                :key="nameIndex"
                                >
                                {{ `${name}${nameIndex === formDataInner?.positionsData?.visible_users_name?.length - 1 ? '' : '、'}` }}
                              </span>
                            </div>
                          </div>
                        </template>
                      </t-popup>
                    </div>
              </div>
              <t-form-item
                v-for="(item, index) in formDataInner.positionsData.value"
                :key="index"
                label=""
                label-width="0"
                :required-mark="false"
                name="email"
              >
                <t-input
                  v-model="item.departmentAndJob"
                  :disabled="formDataInner.positionsData.editable === 0"
                  :placeholder="t('identity.inputContent')"
                  @enter="onEnter"
                />
              </t-form-item>
            </div>

            <div class="form-item-box">
              <div class="form-item">
                <div class="form-item-label-box">
                  <!-- <div class="btn-item item" /> -->
                  <div class="form-item-label"  style="margin-left: 0;">
                    {{ t("identity.emailAddress") }}
                  </div>
                </div>
                <div class="form-item-select" v-if="formDataInner?.emailData?.visible">
                      <t-popup placement="left" showArrow overlayClassName="identity-visible-popup" :disabled="!showMemberPopup(formDataInner?.emailData)">
                        <t-select v-replace-svg  autoWidth :popup-props="{overlayClassName: 'identity-visible-popup-select'}" class="select-hover exclude-downfill"
                          :value="formDataInner.emailData.visible_type"
                        >
                          <t-option
                            @click="itemChange(optionItem.value, formDataInner?.emailData, {form: 'formDataInner', key: 'emailData', type: 'email'})"
                            v-for="optionItem in showTypeOptions"
                            :key="optionItem.value"
                            :value="optionItem.value"
                            :label="optionItem.label"
                            ></t-option>
                        </t-select>
                        <template #content>
                          <div class="member-popup-box">
                            <div class="member-popup-title">{{ showshowMemberPopupTitle(formDataInner?.emailData) }}</div>
                            <div class="member-popup-content">
                              <span
                                v-for="(name, nameIndex) in formDataInner?.emailData?.visible_users_name"
                                :key="nameIndex"
                                >
                                {{ `${name}${nameIndex === formDataInner?.emailData?.visible_users_name?.length - 1 ? '' : '、'}` }}
                              </span>
                            </div>
                          </div>
                        </template>
                      </t-popup>
                    </div>
              </div>
              <t-form-item
                label=""
                label-width="0"
                :required-mark="false"
                name="emailData"
              >
                <t-input
                  v-model="formDataInner.emailData.value"
                  :disabled="formDataInner.emailData.editable === 0"
                  :placeholder="
                    formDataInner.emailData.editable === 0
                      ? '--'
                      : t('identity.inputContent')
                  "
                  @enter="onEnter"
                />
              </t-form-item>
            </div>

            <div style="margin-bottom: 60px">
              <div
                v-for="(item, index) in formDataInner.newInfoItemList"
                :key="index"
                class="form-item-box newInfoItemList"
              >
                <div class="form-item">
                  <div class="form-item-label-box">
                    <!-- <div class="btn-item item" /> -->
                    <div class="form-item-label"  style="margin-left: 0;">
                      {{ item.name }}
                    </div>
                  </div>
                  <div class="form-item-select" v-if="item?.visible">
                        <t-popup placement="left" showArrow overlayClassName="identity-visible-popup" :disabled="!showMemberPopup(item)">
                          <t-select v-replace-svg  autoWidth :popup-props="{overlayClassName: 'identity-visible-popup-select'}" class="select-hover exclude-downfill"
                            :value="item.visible_type"
                          >
                            <t-option
                              @click="itemChange(optionItem.value, item, {form: 'formDataInner_newInfoItemList', key: index, type: item.type})"
                              v-for="optionItem in showTypeOptions"
                              :key="optionItem.value"
                              :value="optionItem.value"
                              :label="optionItem.label"
                              ></t-option>
                          </t-select>
                          <template #content>
                            <div class="member-popup-box">
                              <div class="member-popup-title">{{ showshowMemberPopupTitle(item) }}</div>
                              <div class="member-popup-content">
                                <span
                                  v-for="(name, nameIndex) in item?.visible_users_name"
                                  :key="nameIndex"
                                  >
                                  {{ `${name}${nameIndex === item?.visible_users_name?.length - 1 ? '' : '、'}` }}
                                </span>
                              </div>
                            </div>
                          </template>
                        </t-popup>
                      </div>
                </div>
                <t-form-item
                  label=""
                  label-width="0"
                  :required-mark="false"
                  name="emailData"
                >
                  <t-input
                    v-model="item.value"
                    :disabled="item.editable === 0"
                    :placeholder="
                      item.editable === 0
                        ? '--'
                        : t('identity.inputContent')
                    "
                    @enter="onEnter"
                  />
                </t-form-item>
              </div>
            </div>

            <div
              class="form-item-box"
              style="position: fixed; bottom: 0; background: white; width: calc(100vw - 50px);padding: 24px 0;"
            >
              <div class="form-item" style="justify-content: flex-end;margin-bottom: 0;">
                <div class="form-item-select">
                  <t-form-item>
                    <t-space size="small">
                      <t-button style="width: 80px" theme="primary" type="submit">{{
                        t("identity.save")
                      }}</t-button>
                    </t-space>
                  </t-form-item>
                </div>
              </div>
            </div>
          </t-form>
        </div>
        <div
          style="padding: 0 0 0 16px;"
          class="t-tab-panel"
          v-if="tabValue === 2 && cardIdTypeName !== 'inner' && cardIdTypeName !== 'platform'"
          :value="2"
          :label="t('identity.relatedOrg')"
          :destroy-on-hide="false"
        >
          <div class="org-box">
            <div class="form-item-box">
              <div class="form-item addOrgButtonBox">
                <t-button
                  variant="text"
                  class="addOrgButton"
                  block
                  @click="openOrgDialog(true)"
                >
                  <div class="form-item-label-box">
                    <div class="btn-item add" />
                    <div class="form-item-label orgAddLabel">
                      {{ t("identity.addOrg") }}
                    </div>
                  </div>
                </t-button>
              </div>
            </div>
            <div v-if="orgList.length">
              <draggable
                class="form-item-box"
                item-key="type"
                ghost-class="form-item-ghostClass"
                chosen-class="form-item-chosenClass"
                :list="orgList"
                @end="onDraggableEnd"
                handle=".btn-item"
              >
                <template #item="{ element, index }">
                  <div class="form-item" style="margin-bottom: 2px;height: 40px;padding: 8px;">
                    <div class="form-item-label-box">
                      <div class="btn-item item" />
                      <div class="form-item-label orgLabel">
                        <span class="orgLabelName">{{ element.name }}</span>
                        <t-tag
                          v-if="element.relation"
                          class="associationTag"
                          theme="success"
                          variant="light"
                        >{{ t("identity.related") }}</t-tag>
                      </div>
                    </div>
                    <div class="form-item-select" style="gap: 8px;">
                      <t-popup placement="left" showArrow overlayClassName="identity-visible-popup" :disabled="!showMemberPopup(element, 'display')">
                        <t-select v-replace-svg  autoWidth :popup-props="{overlayClassName: 'identity-visible-popup-select'}"
                          :value="element.display"
                          class="newInfoItem-select select-hover exclude-downfill"
                        >
                          <t-option
                            @click="orgItemChange(optionItem.value, element, {form: 'orgList', key: index})"
                            v-for="optionItem in showTypeOptions"
                            :key="optionItem.value"
                            :value="optionItem.value"
                            :label="optionItem.label"
                            ></t-option>
                        </t-select>
                        <template #content>
                          <div class="member-popup-box">
                            <div class="member-popup-title">{{ showshowMemberPopupTitle(element, 'display') }}</div>
                            <div class="member-popup-content">
                              <span
                                v-for="(name, nameIndex) in element.visible_users_name"
                                :key="nameIndex"
                                >
                                {{ `${name}${nameIndex === element.visible_users_name?.length - 1 ? '' : '、'}` }}
                              </span>
                            </div>
                          </div>
                        </template>
                      </t-popup>
                      <div
                        v-if="element.editable"
                        class="btn-item editOrg"
                        @click="openOrgDialog(false, element)"
                      >
                        <img src="@/assets/identity/icon_edit_org.svg" alt="">
                      </div>
                      <t-popconfirm
                        v-if="element.delete"
                        class="close-popconfirm"
                        :content="t('identity.deleteOrg') + '？'"
                        placement="top-right"
                        :popup-props="{overlayClassName: 'close-popconfirm-popup'}"
                        :confirm-btn="{
                          content: t('identity.delete'),
                          theme: 'danger'
                        }"
                        @confirm="deleteOrgList(element, index)"
                      >
                        <template #icon>
                          <img
                            class="close-icon"
                            src="../../assets/identity/icon_info.svg"
                            alt=""
                          />
                        </template>
                        <div class="btn-item deleteOrg" >
                          <img src="@/assets/identity/icon_delete.svg" alt="">
                          </div>
                      </t-popconfirm>
                    </div>
                  </div>
                </template>
              </draggable>
            </div>
            <div v-else class="defaultPage-box">
              <img
                class="defaultPage_metadata"
                src="../../assets/identity/98.defaultPage_metadata.png"
                alt=""
              />
              <div>{{ t("identity.noData") }}</div>
            </div>
          </div>
        </div>
        <div
          class="t-tab-panel"
          v-if="tabValue === 3 && cardIdTypeName !== 'inner' && cardIdTypeName !== 'platform'"
          :value="3"
          :label="
            bussinessList.length ? t('identity.relatedBusinessAssociation') : t('identity.relatedBusinessAssociation')
          "
          :destroy-on-hide="false"
        >
          <div class="org-box">
            <!-- <div class="form-item-box">
              <div class="form-item">
                <t-button
                  variant="text"
                  class="addOrgButton"
                  @click="openOrgDialog(true)"
                >
                  <div class="form-item-label-box">
                    <div class="btn-item add" />
                    <div class="form-item-label orgAddLabel">
                      {{ "添加组织" }}
                    </div>
                  </div>
                </t-button>
              </div>
            </div> -->
            <div v-if="bussinessList.length">
              <draggable
                class="form-item-box"
                item-key="type"
                ghost-class="form-item-ghostClass"
                chosen-class="form-item-chosenClass"
                :list="bussinessList"
                @end="onDraggableBusinessEnd"
              >
                <template #item="{ element, index }">
                  <div class="form-item" style="margin-bottom: 5px">
                    <div class="form-item-label-box">
                      <!-- <div class="btn-item item" /> -->
                      <img
                        class="img-move-item"
                        src="@renderer/assets/identity/move.svg"
                        style="margin-right: 8px"
                      />
                      <div class="form-item-label orgLabel">
                        <span class="orgLabelName">{{
                          element.team_name
                        }}</span>
                        <t-tag
                          v-if="element.relation"
                          class="associationTag"
                          theme="success"
                          variant="light"
                        >{{ t("identity.related") }}</t-tag>
                      </div>
                    </div>
                    <div class="form-item-select">
                      <t-popup placement="left" showArrow overlayClassName="identity-visible-popup" :disabled="!showMemberPopup(element, 'display')">
                        <t-select v-replace-svg  autoWidth :popup-props="{overlayClassName: 'identity-visible-popup-select'}"
                          :value="element.display"
                          class="newInfoItem-select select-hover exclude-downfill"
                        >
                          <t-option
                            @click="businessItemChange(optionItem.value, element, {form: 'bussinessList', key: index})"
                            v-for="optionItem in showTypeOptions"
                            :key="optionItem.value"
                            :value="optionItem.value"
                            :label="optionItem.label"
                            ></t-option>
                        </t-select>
                        <template #content>
                          <div class="member-popup-box">
                            <div class="member-popup-title">{{ showshowMemberPopupTitle(element, 'display') }}</div>
                            <div class="member-popup-content">
                              <span
                                v-for="(name, nameIndex) in element.visible_users_name"
                                :key="nameIndex"
                                >
                                {{ `${name}${nameIndex === element.visible_users_name?.length - 1 ? '' : '、'}` }}
                              </span>
                            </div>
                          </div>
                        </template>
                      </t-popup>
                      <div
                        v-if="element.editable"
                        class="btn-item editOrg"
                        @click="openOrgDialog(false, element)"
                      >
                        <img src="@/assets/identity/icon_edit_org.svg" alt="">
                      </div>
                      <t-popconfirm
                        v-if="element.delete"
                        class="close-popconfirm"
                        :content="t('identity.deleteOrg') + '？'"
                        placement="top-right"
                        :popup-props="{overlayClassName: 'close-popconfirm-popup'}"
                        :confirm-btn="{
                          content: t('identity.delete'),
                          theme: 'danger'
                        }"
                        @confirm="deleteOrgList(element, index)"
                      >
                        <template #icon>
                          <img
                            class="close-icon"
                            src="../../assets/identity/icon_info.svg"
                            alt=""
                          />
                        </template>
                        <div class="btn-item deleteOrg">
                          <img src="@/assets/identity/icon_delete.svg" alt="">
                        </div>
                      </t-popconfirm>
                    </div>
                  </div>
                </template>
              </draggable>
            </div>
            <div v-else class="defaultPage-box">
              <img
                class="defaultPage_metadata"
                src="../../assets/identity/98.defaultPage_metadata.png"
                alt=""
              />
              <div>{{ t("identity.noData") }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <t-dialog
      v-model:visible="visibleLkId"
      width="99%"
      class="visibleLkId"
      theme="info"
      :close-btn="isConfirmVisibleLkId"
      :confirm-btn="
        isConfirmVisibleLkId ? t('identity.nextStep') : t('identity.confirm')
      "
      @confirm="onClickConfirm"
      @close="closeVisibleLkId"
    >
      <template #header>
        <span v-if="isConfirmVisibleLkId">{{ t("identity.lkIDEdit") }}</span>
        <span v-else>{{ t("identity.lkID") + ":" + formDataLkId.LkId }}</span>
      </template>
      <template #body>
        <span v-if="isConfirmVisibleLkId">
          <t-form
            ref="formValidatorStatus"
            :data="formDataLkId"
            :rules="rulesLkId"
            :label-width="0"
            status-icon
          >
            <t-form-item
              label=""
              name="LkId"
              :help="showHelp ? t('identity.editIdTip2') : ''"
            >
              <t-input
                v-model="formDataLkId.LkId"
                :placeholder="t('identity.lkIDInputTip')"
              />
            </t-form-item>
          </t-form>
        </span>
        <span v-else>{{ t("identity.editIdTip1") }}</span>
      </template>
    </t-dialog>
    <t-drawer
      v-model:visible="visibleOrg"
      class="visibleOrg"
      :close-btn="false"
      :cancel-btn="null"
      :confirm-btn="null"
      size="100%"
      @close="closeDrawerOrg"
    >
      <template #header>
        <div>
          <span>{{
            isAddVisibleOrg ? t("identity.addOrg") : t("identity.edit")
          }}</span>
        </div>
        <div><img src="../../assets/identity/0.icon_close.svg" alt="" @click="closeDrawerOrg" /></div>
      </template>
      <template #body>
        <div class="w-info">
          <t-form
            id="formOrg"
            ref="formOrg"
            :rules="FORM_RULES_ORG"
            :data="formDataOrg"
            :colon="true"
            @submit="onSubmitOrg"
          >
            <div class="form-item-box">
              <div class="form-item">
                <div class="form-item-label-box">
                  <div style="color: red">*</div>
                  <div class="form-item-label">{{ t("identity.orgName") }}</div>
                </div>
              </div>
              <t-form-item
                label=""
                label-width="0"
                :required-mark="false"
                name="orgName"
              >
                <t-input
                  v-model="formDataOrg.orgName"
                  maxlength="50"
                  :placeholder="t('identity.inputContent')"
                  @enter="onEnter"
                />
              </t-form-item>
            </div>

            <div class="form-item-box">
              <div class="form-item">
                <div class="form-item-label-box">
                  <div style="color: red">*</div>
                  <div class="form-item-label">
                    {{ t("identity.position") }}
                  </div>
                </div>
              </div>
              <t-form-item
                label=""
                label-width="0"
                :required-mark="false"
                name="post"
              >
                <t-input
                  v-model="formDataOrg.post"
                  maxlength="50"
                  :placeholder="t('identity.inputContent')"
                  @enter="onEnter"
                />
              </t-form-item>
            </div>

            <div class="form-item-box">
              <div class="form-item">
                <div class="form-item-label-box">
                  <div class="form-item-label">
                    {{ t("identity.telephone") }}
                  </div>
                </div>
              </div>
              <t-form-item
                v-for="(item, index) in formDataOrg.newTelItemListOrg"
                :style="index === formDataOrg.newTelItemListOrg?.length - 1 ? 'margin-bottom: 0' : 'margin-bottom: 8px'"
                label=""
                label-width="0"
                :required-mark="false"
                :name="`newTelItemListOrg[${index}].phone`"
              >
                <t-input-adornment>
                  <template #append>
                    <img
                      v-if="index === 0"
                      class="close-icon"
                      src="../../assets/identity/add.svg"
                      alt=""
                      @click="addTelItemOrg"
                    />
                    <t-popconfirm
                      v-else
                      class="close-popconfirm"
                      :content="t('identity.deleteField')"
                      placement="top-right"
                      :popup-props="{overlayClassName: 'close-popconfirm-popup'}"
                      :confirm-btn="{
                        content: t('identity.delete'),
                        theme: 'danger'
                      }"
                      @confirm="deleteTelItemOrg(index)"
                    >
                      <template #icon>
                        <img
                          class="close-icon"
                          src="../../assets/identity/icon_info.svg"
                          alt=""
                        />
                      </template>
                      <img
                        class="close-icon"
                        src="../../assets/identity/0.icon_icon-close-fill.png"
                        alt=""
                      />
                    </t-popconfirm>
                  </template>
                  <template #prepend>
                    <!--                    <t-select v-replace-svg  v-model="item.code" :options="telTypeOptions">-->
                    <!--                    </t-select>-->
                    <area-code v-model="formDataOrg.newTelItemListOrg[index].code" />
                  </template>
                  <t-input
                    v-model="formDataOrg.newTelItemListOrg[index].phone"
                    type="number"
                    :placeholder="t('identity.inputContent')"
                    @enter="onEnter"
                  />
                </t-input-adornment>
              </t-form-item>
            </div>

            <div class="form-item-box fixed-telephone">
              <div class="form-item">
                <div class="form-item-label-box">
                  <div class="form-item-label">
                    {{ t("identity.fixedTelephone") }}
                  </div>
                </div>
              </div>
              <t-form-item
                v-for="(item, index) in formDataOrg.newFixedTelItemListOrg"
                :style="index === formDataOrg.newFixedTelItemListOrg?.length - 1 ? 'margin-bottom: 0' : 'margin-bottom: 8px'"
                label=""
                label-width="0"
                :required-mark="false"
                :name="`newFixedTelItemListOrg[${index}].number`"
              >
                <t-input-adornment>
                  <template #append>
                    <t-input
                      v-model="formDataOrg.newFixedTelItemListOrg[index].ext"
                      class="tel"
                      type="number"
                      :placeholder="t('identity.extensionNumber')"
                      @enter="onEnter"
                    />
                    <img
                      style="margin-left: 6px;"
                      v-if="index === 0"
                      class="close-icon"
                      src="../../assets/identity/add.svg"
                      alt=""
                      @click="addFixedTelItemOrg"
                    />
                    <t-popconfirm
                      v-else
                      class="close-popconfirm"
                      :content="t('identity.deleteField')"
                      placement="top-right"
                      :popup-props="{overlayClassName: 'close-popconfirm-popup'}"
                      :confirm-btn="{
                        content: t('identity.delete'),
                        theme: 'danger'
                      }"
                      @confirm="deleteFixedTelItemOrg(index)"
                    >
                      <template #icon>
                        <img
                          class="close-icon"
                          src="../../assets/identity/icon_info.svg"
                          alt=""
                        />
                      </template>
                      <img
                        style="margin-left: 6px;"
                        class="close-icon"
                        src="../../assets/identity/0.icon_icon-close-fill.png"
                        alt=""
                      />
                    </t-popconfirm>
                  </template>
                  <template #prepend>
                    <!--                    <t-select v-replace-svg  v-model="item.global_code" :options="telTypeOptions">-->
                    <!--                    </t-select>-->
                    <area-code v-model="formDataOrg.newFixedTelItemListOrg[index].global_code" />
                    <t-input
                      v-if="formDataOrg.newFixedTelItemListOrg[index].global_code == 86"
                      v-model="formDataOrg.newFixedTelItemListOrg[index].code"
                      class="global_code"
                      type="number"
                      :placeholder="t('identity.areaCode')"
                      @enter="onEnter"
                    />
                  </template>
                  <t-input
                    v-model="formDataOrg.newFixedTelItemListOrg[index].number"
                    :class="
                      formDataOrg.newFixedTelItemListOrg[index].global_code == 86
                        ? 'global_phone_in'
                        : 'global_phone_out'
                    "
                    :placeholder="t('identity.inputFixedTelephone')"
                    type="number"
                    @enter="onEnter"
                  />
                </t-input-adornment>
              </t-form-item>
            </div>

            <div class="form-item-box">
              <div class="form-item">
                <div class="form-item-label-box">
                  <div class="form-item-label">
                    {{ t("identity.orgAddress") }}
                  </div>
                </div>
              </div>
              <t-form-item
                label=""
                label-width="0"
                :required-mark="false"
                name="region"
                style="margin-bottom: 8px"
              >
                <t-cascader
                  v-model="formDataOrg.region"
                  :options="regionOptionsOrg"
                  :keys="{ label: 'name', value: 'code', children: 'children' }"
                  :load="onRegionLoadOrg"
                  value-type="full"
                  clearable
                  :laceholder="t('identity.selectArea')"
                  @change="onRegionChangeOrg"
                />
              </t-form-item>
              <t-form-item
                label=""
                label-width="0"
                :required-mark="false"
                name="address"
              >
                <t-input
                  v-model="formDataOrg.address"
                  :placeholder="t('identity.inputDetailAddress')"
                  @enter="onEnter"
                />
              </t-form-item>
            </div>

            <div class="form-item-box">
              <div class="form-item">
                <div class="form-item-label-box">
                  <div class="form-item-label">
                    {{ t("identity.orgEmail") }}
                  </div>
                </div>
              </div>
              <t-form-item
                label=""
                label-width="0"
                :required-mark="false"
                name="email"
              >
                <t-input
                  v-model="formDataOrg.email"
                  :placeholder="t('identity.inputOrgEmail')"
                  @enter="onEnter"
                />
              </t-form-item>
            </div>

            <div class="form-item-box">
              <div class="form-item">
                <div class="form-item-label-box">
                  <div class="form-item-label">{{ t("identity.website") }}</div>
                </div>
              </div>
              <t-form-item
                label=""
                label-width="0"
                :required-mark="false"
                name="website"
              >
                <t-input
                  v-model="formDataOrg.website"
                  :placeholder="t('identity.inputWebsite')"
                  @enter="onEnter"
                />
              </t-form-item>
            </div>

            <div class="form-item-box">
              <div class="form-item">
                <div class="form-item-label-box">
                  <div class="form-item-label">
                    {{ t("identity.businessCard") }}
                  </div>
                </div>
              </div>
              <t-form-item
                label=""
                label-width="0"
                :required-mark="false"
                name="businessCard"
              >
                <t-upload
                  ref="uploadRef"
                  v-model="businessCardImages"
                  class="businessCardUpload"
                  theme="image"
                  multiple
                  :max="2"
                  :upload-all-files-in-one-request="false"
                  :request-method="businessCardUploadImage"
                  :size-limit="{
                    size: 1,
                    unit: 'MB',
                    message: '请选择{sizeLimit}MB以内的图片'
                  }"
                  :locale="{
                    triggerUploadText: {
                      image: t('activity.activity.clickUpload'),
                    },
                  }"
                  :beforeUpload="beforeUpload"
                  @click="onOpenUpload"
                  @preview="openPreview"
                >
                  <template #tips>
                    <span>{{ t("identity.pictureTip1") }} </span>
                    <span style="color: #e66800">{{
                      t("identity.pictureTip2")
                    }}</span>
                  </template>
                </t-upload>
              </t-form-item>
            </div>

            <div style="margin-bottom: 20px">
              <div
                v-for="(item, index) in formDataOrg.newInfoItemListOrg"
                :key="index"
                class="form-item-box newInfoItemList newInfoItemListOrg"
              >
                <div class="form-item">
                  <div class="form-item-label-box">
                    <div class="form-item-label">
                      <t-form-item
                        label=""
                        label-width="0"
                        :name="`newInfoItemListOrg[${index}].name`"
                      >
                        <div
                          v-if="item.name && (item.showLabel || item.showLabel === undefined)"
                          class="newInfoItemLabel"
                          @click="editNewInfoItemLabelOrg(index)"
                        >
                          <span class="label">{{ item.name }}</span>
                          <img
                            class="edit-icon"
                            src="../../assets/identity/0.icon_icon-edit.svg"
                            alt=""
                          />
                        </div>
                        <t-input
                          v-else
                          v-model="formDataOrg.newInfoItemListOrg[index].name"
                          auto-width
                          autofocus
                          class="newInfoItemLabel"
                          maxlength="10"
                          :placeholder="t('identity.inputInfoName')"
                          @blur="item.showLabel = true"
                          @enter="
                            () => {
                              item.showLabel = true;
                              onEnter();
                            }
                          "
                        />
                      </t-form-item>
                    </div>
                  </div>
                  <div class="form-item-select">
                    <t-popconfirm
                      class="close-popconfirm"
                      :content="t('identity.deleteField')"
                      placement="top-right"
                      :popup-props="{overlayClassName: 'close-popconfirm-popup'}"
                      :confirm-btn="{
                        content: t('identity.delete'),
                        theme: 'danger'
                      }"
                      @confirm="deleteInfoItemOrg(index)"
                    >
                      <template #icon>
                        <img
                          class="close-icon"
                          src="../../assets/identity/icon_info.svg"
                          alt=""
                        />
                      </template>
                      <template #default>
                        <div
                          v-if="item.name || item.value"
                          class="btn-item delete"
                        />
                        <div
                          v-else
                          class="btn-item delete"
                          @click="deleteInfoItemOrg(index)"
                        />
                      </template>
                    </t-popconfirm>
                  </div>
                </div>
                <t-form-item
                  label=""
                  label-width="4px"
                  :required-mark="false"
                  :name="`newInfoItemListOrg[${index}].value`"
                >
                  <t-input
                    v-model="formDataOrg.newInfoItemListOrg[index].value"
                    :placeholder="t('identity.inputContent')"
                    maxlength="50"
                    @enter="onEnter"
                  />
                </t-form-item>
              </div>
            </div>

            <div
              class="form-item-box"
              style="
                position: fixed;
                bottom: 0;
                width: calc(100vw - 10px);
                background-color: white;
                padding: 24px 8px;
              "
            >
              <div class="form-item" style="margin-bottom: 0;">
                <div class="form-item-label-box" @click="addInfoItemOrg">
                  <div class="btn-item add" />
                  <div class="form-item-label add-label">
                    {{ t("identity.addInfo") }}
                  </div>
                </div>
                <div class="form-item-select" style="margin-right: 24px">
                  <t-form-item>
                    <t-space size="small">
                      <t-button style="width: 80px;" theme="primary" type="submit" :disabled='isEditName'>{{
                        t("identity.save")
                      }}</t-button>
                    </t-space>
                  </t-form-item>
                </div>
              </div>
            </div>
          </t-form>
        </div>
      </template>
    </t-drawer>

    <edit-nick-name-dialog
      v-model:visible="visibleNickName"
      :card-info="originalCardInfo"
      :card-id="route.query.cardId"
      @refreshData="refreshData"
    />
    <card-check v-model:visible="checkVisible" @onconfirm="checkLkId" />

  </div>
</template>

<script lang="ts" setup>
import kyyAvatar from "@renderer/components/kyy-avatar/index.vue";
import draggable from "vuedraggable";
import editNickNameDialog from "@renderer/views/identitycard/dialog/editNickName.vue";
import { watch, reactive, ref, onMounted, nextTick, computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import { getProfilesInfo, setProfilesInfo } from "@renderer/utils/auth";
import { MessagePlugin } from "tdesign-vue-next";
import cardCheck from "@renderer/components/account/CardCheck.vue";
import moment from "moment";
import { useI18n } from "vue-i18n";
import UploadAvatar from "@/components/common/UploadAvatar.vue";
import { cardData, cardDataForEdit, cardIdType, tranStaffs } from "./data";
import useRegion from "@/components/common/region/hooks";
import { useImageUpload } from "@/views/square/hooks/upload";
import {
  addRelatedOrganize,
  checkLinkId,
  checkRepeatLinkId,
  deleteRelatedOrg,
  getRelatedOrg,
  relatedBusiness,
  relatedOrganize,
  setExternalCard,
  setInnerCard,
  setLinkId,
  setProfilesCard,
  setRelatedOrg,
  setRelatedOrganizeDisplay,
  setBusinessDisplay,
  setRelatedOrganizeSort,
  setRelatedBusinessSort, getSquareNumber, setSquareNumber, setIdentityVisible, setIdentityIsCard, getContactConfig, setContactConfig
} from "@/api/identity/api/card";
import areaCode from "@/components/account/AreaCode.vue";
import { getProfile } from "@/api/account";
import { setIdentityCardFocus, setIdentityEditInfoFocus } from "@/views/setting/util";
import to from "await-to-js";
import { useSquareEnable } from "@/views/identitycard/hooks";
import {handelCardIds} from "@/views/contacts/utils";
import LynkerSDK from "@renderer/_jssdk";
const { ipcRenderer } = LynkerSDK;

const { t } = useI18n();

const props = defineProps({
  cardId: { type: String, default: "" },
  myId: { type: String, default: "" }
});

const cardInfo: any = ref({});
const myInfo: any = ref({});
const originalCardInfo: any = ref({});
// const curCard = reactive({});

const isFriend = false;
const hasLogo = false;
const isGroup = false;

let areaTemp = "";
let areaTempOrg = "";
const checkVisible = ref(false);
const showEditLkId = ref(true);
const orgList = ref([]); // 组织
const bussinessList = ref([]); // 商协会
const tabValue = ref(1);
const cardIdTypeName = ref("inner");
const files = ref([
  {
    name: "demo-image-1.png",
    url: "https://tdesign.gtimg.com/demo/demo-image-1.png"
  }
]);
const validatorTel = (val) => {
  // let data = newTelItemList.value;
  let data = newTelItemList.value?.filter((item) => item.phone === val);
  if (data.length === 0) {
    return {
      result: false,
      message: t("identity.inputTipOneItem"),
      type: "warning"
    };
  }
  const emptyLength = data.filter((item) => item.phone === "").length;
  const errorLength = data.filter(
    (item) => !checkPhoneNumber(item.code, item.phone)
  ).length;
  if (emptyLength !== 0) {
    return {
      message: t("identity.inputTipPhone"),
      required: true,
      trigger: "blur"
    };
  }
  if (errorLength !== 0) {
    return {
      message: t("identity.inputTipPhoneFormat"),
      required: true,
      trigger: "blur"
    };
  }
  return { result: true, message: "", type: "success" };
};
const validatorTelOrg = (val) => {
  // let data = newTelItemListOrg.value;
  let data = newTelItemListOrg.value?.filter((item) => val && item.phone === val);
  // if (!newTelItemListOrg.value?.some(item => item.phone)) {
  //   return {
  //     result: false,
  //     message: t("identity.inputTipOneItem"),
  //     type: "warning"
  //   };
  // }
  // const emptyLength = data.filter(
  //   (item) => item.number === ""
  // ).length;
  const errorLength = data.filter(
    (item) => item.phone && !checkPhoneNumber(item.code, item.phone)
  ).length;
  // if (emptyLength !== 0) {
  //   return {
  //     message: "联系电话不能为空",
  //     required: true,
  //     trigger: "blur",
  //   };
  // }
  if (errorLength !== 0) {
    return {
      message: t("identity.inputTipTelPhoneFormat"),
      required: true,
      trigger: "blur"
    };
  }
  return { result: true, message: "", type: "success" };
};
const validatorTelFixedTelOrg = (val) => {
  // let data = newFixedTelItemListOrg.value;
  let data = newFixedTelItemListOrg.value?.filter((item) => val && item.number === val);
  // if (data.length === 0) {
  //   return {
  //     result: false,
  //     message: t("identity.inputTipOneItem"),
  //     type: "warning"
  //   };
  // }
  // const emptyLength = data.filter(
  //   (item) => item.number === ""
  // ).length;
  const errorLength = data.filter(
    (item) => item.number && !checkPhoneNumber(item.global_code, item.number)
  ).length;
  // if (emptyLength !== 0) {
  //   return {
  //     message: "固定电话不能为空",
  //     required: true,
  //     trigger: "blur",
  //   };
  // }
  if (errorLength !== 0) {
    return {
      message: t("identity.inputTipTelPhoneFormat"),
      required: true,
      trigger: "blur"
    };
  }
  return { result: true, message: "", type: "success" };
};
const FORM_RULES = {
  // name: [{ required: false, message: t("identity.inputTipRequiredName") }],
  phone: [{ validator: validatorTel }],
  name: [{ required: true, message: t("identity.inputTipRequiredInfoName") }],
  value: [{ required: true, message: t("identity.inputTipRequiredInfoDetail")  }],
  // gender: [
  //   {
  //     trigger: "blur",
  //     validator: () =>
  //       !!formData?.options?.find((item) => item.type === "gender")?.value,
  //     message: t("identity.required"),
  //     type: "warning"
  //   }
  // ],
  email: [
    {
      trigger: "blur",
      validator: () => {
        let val = formData?.options?.find(
          (item) => item.type === "email"
        )?.value;
        let test =
          /^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z]{2,4})+$/.test(
            val
          );
        return (val && test) || !val;
      },
      message: t("identity.inputTipEmail"),
      type: "warning"
    }
  ],
  // birthday: [
  //   {
  //     trigger: "blur",
  //     validator: () =>
  //       !!formData?.options?.find((item) => item.type === "birthday")?.value,
  //     message: t("identity.required"),
  //     type: "warning"
  //   }
  // ],
  area: [
    {
      trigger: "blur",
      validator: () =>
        !!formData?.options?.find((item) => item.type === "area")?.value,
      message: t("identity.required"),
      type: "warning"
    }
  ],
  newInfoItemLabel: [
    {
      trigger: "blur",
      validator: () => newInfoItemList.value.every((item) => item.name),
      message: t("identity.inputTipRequiredInfoName"),
      type: "warning"
    }
  ],
  newInfoItemValue: [
    {
      trigger: "blur",
      validator: () => newInfoItemList.value.every((item) => item.value),
      message: t("identity.inputTipRequiredInfoDetail"),
      type: "warning"
    }
  ]
};
const formData = reactive({
  name: "",
  phone: "",
  gender: "",
  email: "",
  birthday: "",
  area: "",
  newInfoItemLabel: "",
  newInfoItemValue: "",
  showSquare: false,
  newTelItemList: [
    {
      phone: "",
      code: "86"
    }
  ],
  newTelItemListOrg: [
    {
      phone: "",
      code: "86"
    }
  ],
  newFixedTelItemListOrg: [
    {
      number: "",
      code: "",
      global_code: "86",
      ext: ""
    }
  ],
  newInfoItemList: [{
    name: "",
    value: "",
    showLabel: false,
    display: 1,
    editable: 1,
    visible: 1,
    visible_type: 1,
    visible_users: {
      departments: [],
      staffs: []
    }
  }]
});
const form = ref(null);
const FORM_RULES_INNER = {
  phone: [{ validator: validatorTel }],
  emailData: [
    {
      trigger: "blur",
      validator: () => {
        let val = formDataInner?.emailData?.value;
        let test =
          /^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z]{2,4})+$/.test(
            val
          );
        return (val && test) || !val;
      },
      message: t("identity.inputTipEmail"),
      type: "warning"
    }
  ],
};
const formDataInner = reactive({
  jobNumberData: {
    value: "",
    editable: 1
  },
  phone: "",
  phoneData: {
    value: null,
    editable: 1
  },
  emailData: {
    value: "",
    editable: 1
  },
  positionsData: {
    value: [],
    departmentAndJob: "",
    editable: 1
  },
  phoneEditable: 1,
  avatarEditable: 1,
  nameEditable: 1,
  newInfoItemList: [],
  newTelItemList: [
    {
      phone: "",
      code: "86"
    }
  ],
  newTelItemListOrg: [
    {
      phone: "",
      code: "86"
    }
  ],
  newFixedTelItemListOrg: [
    {
      number: "",
      code: "",
      global_code: "86",
      ext: ""
    }
  ],
  visible_type: 1,
  contactData: {
    visible: 1,
    visible_type: 0,
    visible_users: {}
  },
  originalContactData: {
    visible: 1,
    visible_type: 0,
    visible_users: {}
  },
});
const formInner = ref(null);
const FORM_RULES_ORG = {
  orgName: [
    { required: true, message: t("identity.inputTipRequiredOrgName") },
    {
      trigger: "change",
      validator: (val) => /^[\u4E00-\u9FA5A-Za-z0-9]+$/.test(val),
      message: t("identity.inputTipSpecialChar"),
      type: "warning"
    }
  ],
  post: [
    { required: true, message: t("identity.inputTipRequiredPosition") },
    {
      trigger: "change",
      validator: (val) => /^[\u4E00-\u9FA5A-Za-z0-9]+$/.test(val),
      message: t("identity.inputTipSpecialChar"),
      type: "warning"
    }
  ],
  name: [{ required: true, message: t("identity.inputTipRequiredInfoName") }],
  value: [{ required: true, message: t("identity.inputTipRequiredInfoDetail")  }],
  phone: [{ validator: validatorTelOrg }],
  number: [{ validator: validatorTelFixedTelOrg }],
  email: [
    {
      trigger: "blur",
      validator: (val) => {
        let test =
          /^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z]{2,4})+$/.test(
            val
          );
        return (val && test) || !val;
      },
      message: t("identity.inputTipEmail"),
      type: "warning"
    }
  ],
  website: [
    {
      trigger: "blur",
      validator: (val) => {
        // let test = /^(?:http(s)?:\/\/)?[\w.-]+(?:\.[\w\.-]+)+[\w\-\._~:/?#[\]@!\$&'\*\+,;=.]+$/.test(val)
        let test =
          /(http|ftp|https):\/\/[\w\-_]+(\.[\w\-_]+)+([\w\-\.,@?^=%&:/~\+#]*[\w\-\@?^=%&/~\+#])?/.test(
            val
          );
        return (val && test) || !val;
      },
      message: t("identity.inputTipWebsite"),
      type: "warning"
    }
  ]
};
const formDataOrg = reactive({
  orgName: "",
  post: "",
  tel: [],
  phone: [],
  region: [],
  email: "",
  address: "",
  website: "",
  businessCard: [],
  display: 2,
  newTelItemList: [
    {
      phone: "",
      code: "86"
    }
  ],
  newTelItemListOrg: [
    {
      phone: "",
      code: "86"
    }
  ],
  newFixedTelItemListOrg: [
    {
      number: "",
      code: "",
      global_code: "86",
      ext: ""
    }
  ],
  newInfoItemListOrg: []
});
const formOrg = ref(null);
const formDataLkId = reactive({
  LkId: "",
  NickName: ""
});
const formValidatorStatus = ref(null);
const showHelp = ref(true);
const rulesLkId = {
  LkId: [
    {
      required: true,
      message: t("identity.editIdTip3"),
      type: "warning"
    },
    {
      trigger: "change",
      validator: (val) => /^[a-zA-Z][a-zA-Z0-9_\-\.]{5,19}$/.test(val),
      message: t("identity.editIdTip3"),
      type: "warning"
    },
    {
      trigger: "blur",
      validator: (val) => /^[a-zA-Z][a-zA-Z0-9_\-\.]{5,19}$/.test(val),
      message: t("identity.editIdTip3"),
      type: "warning"
    },
    {
      trigger: "blur",
      validator: (val) => val !== cardInfo.value.linkId,
      message: t("identity.editIdTip4"),
      type: "warning"
    },
    {
      trigger: "blur",
      validator: async (val) => {
        let res = await checkRepeatLinkId({
          acc: val
        });
        return !res?.data?.done;
      },
      message: t("identity.editIdTip5"),
      type: "warning"
    }
  ],
  NickName: [
    { required: true, message: t("identity.required"), type: "warning" }
  ]
};
const showTypeOptions = computed(() => {
  if (cardIdTypeName.value === 'inner' || cardIdTypeName.value === 'platform') {
    return [
      { label: t("identity.everyoneShow"), value: 0 },
      { label: t("identity.everyoneNotShow"), value: 2 },
      { label: t("identity.anyoneShow"), value: 3 },
      { label: t("identity.anyoneNotShow"), value: 4 }
    ]
  } else {
    return [
      { label: t("identity.everyoneShow"), value: 0 },
      { label: t("identity.onlyShow"), value: 1 },
      { label: t("identity.everyoneNotShow"), value: 2 },
      { label: t("identity.anyoneShow"), value: 3 },
      { label: t("identity.anyoneNotShow"), value: 4 }
    ]
  }
});
const showTypeOptionsContact = ref([
      { label: t("identity.everyoneShowContact"), value: 0 },
      { label: t("identity.anyoneShowContact"), value: 3 },
      { label: t("identity.anyoneNotShowContact"), value: 4 }
    ]);
const showType = ref(0);
const telTypeOptions = [
  { label: "+86", value: "86" },
  { label: "+853", value: "853" },
  { label: "+852", value: "852" }
];
const telType = ref("86");
const newInfoItemList = ref([]);
const newTelItemList = ref([
  {
    phone: "",
    code: "86"
  }
]);
const newInfoItemListOrg = ref([]);
const newTelItemListOrg = ref([
  {
    phone: "",
    code: "86"
  }
]);
const newFixedTelItemListOrg = ref([
  {
    number: "",
    code: "",
    global_code: "86",
    ext: ""
  }
]);
const visibleLkId = ref(false);
const visibleNickName = ref(false);
const visibleOrg = ref(false);
const isConfirmVisibleLkId = ref(true);
const isAddVisibleOrg = ref(true);
const uploadAvatarRef = ref(null);

const router = useRouter();
const route = useRoute();
const selfInfo = getProfilesInfo();

const checkLkId = () => {
  checkVisible.value = false;
  visibleLkId.value = true;
};
const closeVisibleLkId = () => {
  showHelp.value = true;
  formValidatorStatus.value?.clearValidate();
  formValidatorStatus.value?.reset();
  isConfirmVisibleLkId.value = true;
};
const checkPhoneNumber = (mobileZone, mobile) => {
  let reg = null;
  let passed = true;
  switch (Number(mobileZone)) {
    case 86:
      reg = /^[1][3-9]\d{9}$/;
      if (!reg.test(mobile)) {
        passed = false;
      }
      break;
    case 852:
      reg = /^([5|6|7|8|9])\d{7}$/;
      if (!reg.test(mobile)) {
        passed = false;
      }
      break;
    case 853:
      reg = /^[6]\d{7}$/;
      if (!reg.test(mobile)) {
        passed = false;
      }
      break;
  }
  return passed;
};
const openUploadAvatar = (url) => {
  onOpenUpload();
  if (!url) {
    uploadAvatarRef.value.open();
    return;
  } else {
    uploadAvatarRef.value.open(url);
    return;
  }
  // let image = new Image();
  // image.src = url;
  // image.crossOrigin = "*";
  // image.onload = function () {
  //   let canvas = document.createElement("canvas");
  //   canvas.width = image.width;
  //   canvas.height = image.height;
  //   canvas.getContext("2d").drawImage(image, 0, 0, image.width, image.height);
  //   let base64 = canvas.toDataURL("image/png");
  //   uploadAvatarRef.value.open(base64);
  // };
  // image.onerror = () => {
  //   uploadAvatarRef.value.open(url);
  // };
};
const uploadAvatarConfirm = (url) => {
  cardInfo.value.avatar = url;
  console.log(form.value);
  if (cardIdTypeName.value === 'personal') {
    form.value?.submit();
    refreshProfileData();
  } else {
    formInner.value?.submit();
  }

};
// 图片上传
const { images, uploadImage } = useImageUpload((res) => {
  cardInfo.value.avatar = res.url;
});
// 名片上传
const { images: businessCardImages, uploadImage: businessCardUploadImage } =
  useImageUpload((res, images) => {});
// 区域选择
const { regionOptions, regionNames, onRegionLoad, getRegionInfo } =
  useRegion(4);
const onRegionChange = (val, { node }) => {
  regionNames.value.push(node.label);
};
// 区域选择
const {
  regionOptions: regionOptionsOrg,
  regionNames: regionNamesOrg,
  onRegionLoad: onRegionLoadOrg,
  getRegionInfo: getRegionInfoOrg
} = useRegion(4);
const onRegionChangeOrg = (val, { node }) => {
  regionNamesOrg.value.push(node.label);
};

const openPreview = (options) => {
  console.log(options)
  // ipcRenderer.invoke(
  //   'preview-file',
  //   JSON.stringify({
  //     title: options.file.name,
  //     url: options.file.url,
  //   })
  // );
}

const onOpenUpload = () => {
  setIdentityCardFocus(true);
  // setIdentityEditInfoFocus(true);
};
const onCancelUpload = () => {
  setIdentityCardFocus(false);
  // setIdentityEditInfoFocus(false);
};
const closeDrawerOrg = () => {
  onCancelUpload();
  formOrg.value.reset();
  setTimeout(() => {
    formOrg.value.clearValidate();
  }, 0);
  document.getElementById('formOrg').scrollIntoView();
  newTelItemListOrg.value = [
    {
      phone: "",
      code: "86"
    }
  ];
  newFixedTelItemListOrg.value = [
    {
      number: "",
      code: "",
      global_code: "86",
      ext: ""
    }
  ];
  newInfoItemListOrg.value = [];
  businessCardImages.value = [];
  formDataOrg.newTelItemListOrg = newTelItemListOrg.value;
  formDataOrg.newFixedTelItemListOrg = newFixedTelItemListOrg.value;
  formDataOrg.newInfoItemListOrg = newInfoItemListOrg.value;
  visibleOrg.value = false;
};
const closeCard = () => {
  routerBack();
};
const handleFail = (context) => {
  console.log(context);
  cardInfo.value.avatar = context?.response?.url;
};
const addInfoItem = () => {
  formData.options.push({
    type: 'custom_' + Date.now(),
    name: "",
    value: "",
    showLabel: false,
    showSelect: false,
    display: 1,
    editable: 1,
    visible: 1,
    visible_type: 1,
    visible_users: {
      departments: [],
      staffs: []
    }
  });
  // newInfoItemList.value.push({
  //   name: "",
  //   value: "",
  //   showLabel: false,
  //   display: 1,
  //   editable: 1,
  //   visible: 1,
  //   visible_type: 1,
  //   visible_users: {
  //     departments: [],
  //     staffs: []
  //   }
  // });
  // formData.newInfoItemList = newInfoItemList.value;
  nextTick(() => {
    // document.getElementById('form').scrollTo(0,120);
    let elementList = document.getElementsByClassName("newInfoItemList");
    elementList[elementList.length - 1].scrollIntoView(true);
  });
};
const deleteInfoItem = (index) => {
  formData.options.splice(index, 1);
  // newInfoItemList.value.splice(index, 1);
  // formData.newInfoItemList = newInfoItemList.value;
};
const addTelItem = () => {
  newTelItemList.value.push({
    phone: "",
    code: "86"
  });
  formData.newTelItemList = newTelItemList.value;
};
const deleteTelItem = (index) => {
  newTelItemList.value.splice(index, 1);
  formData.newTelItemList = newTelItemList.value;
};
const addInfoItemOrg = () => {
  newInfoItemListOrg.value.push({
    name: "",
    value: "",
    showLabel: false
  });
  formDataOrg.newInfoItemListOrg = newInfoItemListOrg.value;
  nextTick(() => {
    // document.getElementById('formOrg').scrollTo(0,100);
    let elementList = document.getElementsByClassName("newInfoItemListOrg");
    elementList[elementList.length - 1].scrollIntoView(true);
  });
};
const deleteInfoItemOrg = (index) => {
  newInfoItemListOrg.value.splice(index, 1);
  formDataOrg.newInfoItemListOrg = newInfoItemListOrg.value;
};
const deleteOrgList = (item, index) => {
  deleteRelatedOrg(item.organizeId).then((res) => {
    if (res?.status === 200) {
      orgList.value.splice(index, 1);
    }
  });
};
const orgListChange = (value, item, has_visible_users = false) => {
  let data = {
    cardId: route.query.cardId,
    organizeId: item.organizeId,
    display: value
  }
  if (has_visible_users) {
    data.visible_users = item.visible_users
  }
  setRelatedOrganizeDisplay(data).then((res) => {
    if (res?.status === 200) {
      MessagePlugin.success({
        content: t("identity.editSuccess"),
        duration: 2000
      });
    }
  });
};
// 商协会
const orgListBusinessChange = (value, item, has_visible_users = false) => {
  let data = {
    // cardId: route.query.cardId,
    // organizeId: item.organizeId,
    id: item.id,
    display: value
  }
  if (has_visible_users) {
    data.visible_users = item.visible_users
  }
  setBusinessDisplay(data).then((res) => {
    if (res?.status === 200) {
      MessagePlugin.success({
        content: t("identity.editSuccess"),
        duration: 2000
      });
    }
  });
};
const addTelItemOrg = () => {
  newTelItemListOrg.value.push({
    phone: "",
    code: "86"
  });
  formDataOrg.newTelItemListOrg = newTelItemListOrg.value;
};
const deleteTelItemOrg = (index) => {
  newTelItemListOrg.value.splice(index, 1);
  formDataOrg.newTelItemListOrg = newTelItemListOrg.value;
};
const addFixedTelItemOrg = () => {
  newFixedTelItemListOrg.value.push({
    number: "",
    code: "",
    global_code: "86",
    ext: ""
  });
  formDataOrg.newFixedTelItemListOrg = newFixedTelItemListOrg.value
};
const deleteFixedTelItemOrg = (index) => {
  newFixedTelItemListOrg.value.splice(index, 1);
  formDataOrg.newFixedTelItemListOrg = newFixedTelItemListOrg.value
};
// 禁用 Input 组件，按下 Enter 键时，触发 submit 事件
const onEnter = (_, { e }) => {
  e.preventDefault();
};

const beforeUpload = (file) => {
  const isLt1M = file.size / 1024 / 1024 < 1;
  if (!isLt1M) {
    MessagePlugin.error(t("identity.pictureTip5"));
    return false;
  }
  const isJpgOrPng =
    file.type === 'image/webp' ||
    file.type === 'image/gif' ||
    file.type === 'image/bmp' ||
    file.type === 'image/png' ||
    file.type === 'image/jpg' ||
    file.type === 'image/jpeg';
  if (!isJpgOrPng) {
    MessagePlugin.error(t("identity.pictureTip6"));
    return false;
  }
  return isJpgOrPng;
};

const isEditName = ref(false);
const cardInfoName = ref("");
const cardInfoNameRef = ref(null);
const endNameInput = async () => {
  const name = cardInfoName.value.trim();
  if (name && name !== cardInfo.value.name) {
    if (cardIdTypeName.value === 'personal') {
      let data = {
        openid: cardInfo.value.openid,
        title: name,
        cellphone: String(cardInfo.value.options?.find((item) => item.type === "phone")
          ?.value[0].phone),
        avatar: cardInfo.value.avatar,
        attachments: {
          options: cardInfo.value.options
        }
      };
      const validPhone = await form.value.validateOnly('phone');
      if (validPhone !== true) {
        return;
      }
      setProfilesCard(data).then((res) => {
        if (res?.status === 200) {
          cardInfo.value.name = name;
          originalCardInfo.value.name = name;
          MessagePlugin.success(t("identity.editSuccess"));
          ipcRenderer.invoke("change-profiles-info", {
            avatar: cardInfo.value.avatar,
            title: name
          });
        }
      }).catch(err => {
        if (err.response.status === 418) return;
        const reason = err.response.data.reason;
        MessagePlugin.error({
          content: err.response.data?.message || t('zx.other.requestFail'),
          duration: 3000,
        });
      });
    } else {
      let data = {
        cardId: route.query.cardId,
        options: formDataInner?.options?.filter(v => v.type === 'name')?.map(v => {
          return {
            ...v,
            value: name
          }
        })
      };
      removeVisibleName('outer', data);
      setInnerCard(data).then((res) => {
        if (res?.status === 200) {
          cardInfo.value.name = name;
          originalCardInfo.value.name = name;
          MessagePlugin.success(t("identity.editSuccess"));
        }
      });
    }
  }
  isEditName.value = false;
};

const editNickName = () => {
  if (formDataInner.nameEditable === 1) {
    // visibleNickName.value = true;
    cardInfoName.value = cardInfo.value.name || "";
    isEditName.value = true;
    nextTick(() => {
      cardInfoNameRef.value.focus();
    });
  }
};
const editLkId = () => {
  // visibleLkId.value = true
  checkVisible.value = true;
};
const onClickConfirm = () => {
  if (isConfirmVisibleLkId.value) {
    formValidatorStatus.value.validate().then((res) => {
      if (res === true) {
        showHelp.value = true;
        checkRepeatLinkId({
          acc: formDataLkId.LkId
        }).then((res) => {
          if (res?.data?.done) {
            MessagePlugin.error(t("identity.editIdTip5"));
          } else {
            isConfirmVisibleLkId.value = false;
          }
        });
      } else {
        showHelp.value = false;
      }
    });
  } else {
    cardInfo.value.linkId = formDataLkId.LkId;
    setLinkId({
      id: formDataLkId.LkId
    })
      .then(async (res) => {
        if (res?.status === 200) {
          visibleLkId.value = false;
          showEditLkId.value = false;
          const profile = JSON.parse(window.localStorage.getItem('profile'));
          profile.link_id = formDataLkId.LkId;
          window.localStorage.setItem('profile', JSON.stringify(profile));
        }
      })
      .catch((err) => {
        const reason = err.response.data.reason;
        if (reason === "RING_KOL_EXISTED") {
          MessagePlugin.error(t("identity.editIdTip5"));
        }
      });
  }
};
const onClickConfirmNickName = () => {
  formValidatorStatus.value.validate().then((res) => {
    if (res === true) {
      cardInfo.value.name = formDataLkId.NickName;
      visibleNickName.value = false;
    }
  });
};
const onClickConfirmOrg = () => {
  formOrg.value.validate().then((res) => {
    if (res === true) {
      visibleOrg.value = false;
    }
  });
};
const editNewInfoItemLabel = (index) => {
  formData.options[index].showLabel = false;
  // newInfoItemList.value[index].showLabel = false;
};
const editNewInfoItemLabelOrg = (index) => {
  newInfoItemListOrg.value[index].showLabel = false;
};
const onSubmit = async ({ validateResult, firstError }) => {
  if (validateResult === true) {
    let data = {
      attachments: {
        options: formData.options?.map(
          (item) => {
            if (item.type.startsWith("custom_")) {
              return {
                display: item.display,
                editable: item.editable,
                name: item.name,
                type: `custom_${item.name}`,
                value: item.value,
                visible: item.visible ?? 1,
                visible_type: item.visible_type ?? 1,
                visible_users: item.visible_users ?? {departments: [], staffs: []}
              }
            } else {
              delete item.showSelect;
              return item
            }
          }
        )
      }
    };
    // data.attachments.options.push(
    //   ...newInfoItemList.value?.map((item) => ({
    //     display: item.display,
    //     editable: item.editable,
    //     name: item.name,
    //     type: `custom_${item.name}`,
    //     value: item.value,
    //     visible: item.visible ?? 1,
    //     visible_type: item.visible_type ?? 1,
    //     visible_users: item.visible_users ?? {departments: [], staffs: []}
    //   }))
    // );
    data.openid = route.query.cardId;
    data.title = cardInfo.value.name;
    data.cellphone = formData.options?.find(
      (item) => item.type === "phone"
    )?.value[0].phone?.toString();
    data.avatar = cardInfo.value.avatar;
    data.area = selfInfo?.area;
    data.gender = formData.options?.find(
      (item) => item.type === "gender"
    )?.value;
    // data.email = formData.options?.find(item => item.type === 'email')?.value
    // data.birthday = new Date(formData.options?.find(item => item.type === 'birthday')?.value).getTime()
    // data.region = formData.options?.find(item => item.type === 'phone')?.value[0].code
    // data.hidden = []
    // data.slogan = ''
    formData.options?.forEach((item) => {
      if (item.type === "area") {
        item.value =
          item.value.length === 0
            ? ""
            : item.value.length === 1 && Array.isArray(item.value)
            ? item.value[0]
            : regionNames.value.join("-");
        item.type = "address";
      }
    });
    removeVisibleName('personal', data);
    let res = await setProfilesCard(data).catch(err => {
        if (err.response.status === 418) return;
        const reason = err.response.data.reason;
        MessagePlugin.error({
          content: err.response.data?.message || t('zx.other.requestFail'),
          duration: 3000,
        });
      });
    if (res?.status === 200) {
      MessagePlugin.success(t("identity.saveSuccess"));
      refreshProfileData();
      routerBack();
    }
  }
};
const onSubmitInner = async ({ validateResult, firstError }) => {
  if (validateResult === true) {
    let data = {};
    if (cardIdTypeName.value === "inner") {
      let formDataInnerTemp = { ...formDataInner };
      formDataInnerTemp?.options?.forEach((item) => {
        if (item.type === "phone") {
          item.visible = formDataInner.phoneData.visible;
          item.visible_type = formDataInner.phoneData.visible_type;
          item.visible_users = formDataInner.phoneData.visible_users;
          item.value = newTelItemList.value.filter((item) => item.phone);
        } else if (item.type === "jobNumber") {
          item.visible = formDataInner.jobNumberData.visible;
          item.visible_type = formDataInner.jobNumberData.visible_type;
          item.visible_users = formDataInner.jobNumberData.visible_users;
          item.value = formDataInner.jobNumberData.value;
        } else if (item.type === "positions") {
          item.visible = formDataInner.positionsData.visible;
          item.visible_type = formDataInner.positionsData.visible_type;
          item.visible_users = formDataInner.positionsData.visible_users;
          item.value = formDataInner.positionsData.value?.map((item) => {
            let jobNameList = item.departmentAndJob?.split(
              `${item.departmentName}/`
            );
            return {
              departmentId: item.departmentId,
              departmentName: item.departmentName,
              jobName: jobNameList[jobNameList.length - 1]
            };
          });
        } else if (item.type === "email") {
          item.visible = formDataInner.emailData.visible;
          item.visible_type = formDataInner.emailData.visible_type;
          item.visible_users = formDataInner.emailData.visible_users;
          item.value = formDataInner.emailData.value;
        } else if (item.type === "avatar") {
          item.value = cardInfo.value.avatar;
        } else if (item.type === "name") {
          item.value = cardInfo.value.name;
        } else if (item.type.startsWith('sys_custom')) {
          item.visible = formDataInner.newInfoItemList?.find(v => v.type === item.type).visible;
          item.visible_type = formDataInner.newInfoItemList?.find(v => v.type === item.type).visible_type;
          item.visible_users = formDataInner.newInfoItemList?.find(v => v.type === item.type).visible_users;
          item.value = formDataInner.newInfoItemList?.find(v => v.type === item.type).value;
        }
      });
      data = {
        cardId: route.query.cardId,
        options: formDataInnerTemp.options
      };
      removeVisibleName('inner', data);
      await setIdentityIsCard({
        cardId: route.query.cardId,
        is_card: cardInfo.value.is_card
      })
      await setContactData();
      let res = await setInnerCard(data);
      if (res?.status === 200) {
        MessagePlugin.success(t("identity.saveSuccess"));
        refreshProfileData();
        routerBack();
      }
    } else if (cardIdTypeName.value === "platform") {
      let formDataInnerTemp = { ...formDataInner };
      formDataInnerTemp?.options?.forEach((item) => {
        if (item.type === "phone") {
          item.visible = formDataInner.phoneData.visible;
          item.visible_type = formDataInner.phoneData.visible_type;
          item.visible_users = formDataInner.phoneData.visible_users;
          item.value = newTelItemList.value.filter((item) => item.phone);
        } else if (item.type === "member_position") {
          item.visible = formDataInner.jobNumberData.visible;
          item.visible_type = formDataInner.jobNumberData.visible_type;
          item.visible_users = formDataInner.jobNumberData.visible_users;
          item.value = formDataInner.jobNumberData.value;
        } else if (item.type === "email") {
          item.visible = formDataInner.emailData.visible;
          item.visible_type = formDataInner.emailData.visible_type;
          item.visible_users = formDataInner.emailData.visible_users;
          item.value = formDataInner.emailData.value;
        } else if (item.type === "avatar") {
          item.value = cardInfo.value.avatar;
        } else if (item.type === "name") {
          item.value = cardInfo.value.name;
        } else if (item.type.startsWith('sys_custom')) {
          item.visible = formDataInner.newInfoItemList?.find(v => v.type === item.type).visible;
          item.visible_type = formDataInner.newInfoItemList?.find(v => v.type === item.type).visible_type;
          item.visible_users = formDataInner.newInfoItemList?.find(v => v.type === item.type).visible_users;
          item.value = formDataInner.newInfoItemList?.find(v => v.type === item.type).value;
        }
      });
      data = {
        cardId: route.query.cardId,
        options: formDataInnerTemp.options
      };
      removeVisibleName('inner', data);
      await setIdentityIsCard({
        cardId: route.query.cardId,
        is_card: cardInfo.value.is_card
      })
      await setContactData();
      let res = await setInnerCard(data);
      if (res?.status === 200) {
        MessagePlugin.success(t("identity.saveSuccess"));
        refreshProfileData();
        routerBack();
      }
    } else {
      // 外部身份卡不能编辑 直接返回
      // data = {
      //   avatar: cardInfo.value.avatar,
      //   name: cardInfo.value.name,
      // }
      // let res = await setExternalCard(route.query.cardId, data)

      let formDataInnerTemp = { ...formDataInner };
      formDataInnerTemp.options = saveTransOuter(formDataInnerTemp.options);
      data = {
        cardId: route.query.cardId,
        options: formDataInnerTemp.options
      };
      removeVisibleName('outer', data);
      await setContactData();
      let res = await setInnerCard(data);
      if (res?.status === 200) {
        MessagePlugin.success(t("identity.saveSuccess"));
        refreshProfileData();
        routerBack();
      }
    }
  }
};
const saveTransOuter = (data) => {
  let res = data?.filter(item => item.editable);
  res?.forEach((item) => {
        if (item.type === "phone") {
          item.value = newTelItemList.value.filter((item) => item.phone)?.map(item => {
            return {
              telcode: item.code,
              telephone: item.phone,
            }
          });
          item.type = 'telephone';
        } else if (item.type === "jobNumber") {
          item.value = formDataInner.jobNumberData.value;
          item.type = 'no';
        } else if (item.type === "positions") {
          item.value = formDataInner.positionsData.value?.map((v) => {
            let jobNameList = v.departmentAndJob?.split(
              `${v.departmentName}/`
            );
            return {
              staffId: v.staffId,
              jobId: v.jobId,
              departmentId: v.departmentId,
              departmentName: v.departmentName,
              jobName: jobNameList[jobNameList.length - 1] === '--' ? '' : jobNameList[jobNameList.length - 1]
            };
          });
        } else if (item.type === "email") {
          item.value = formDataInner.emailData.value;
        } else if (item.type === "avatar") {
          item.value = cardInfo.value.avatar;
        } else if (item.type === "name") {
          item.value = cardInfo.value.name;
        }
      });
      return res;
};
const onSubmitOrg = async ({ validateResult, firstError }) => {
  if (validateResult === true) {
    let data = {};
    data.cardId = route.query.cardId;
    data.name = formDataOrg.orgName;
    data.post = formDataOrg.post;
    data.address = formDataOrg.address;
    data.email = formDataOrg.email;
    data.website = formDataOrg.website;
    data.display = formDataOrg.display;

    data.phone = newTelItemListOrg.value?.map(item => { return { number: item.phone, code: item.code }}).filter((item) => item.number);
    data.tel = newFixedTelItemListOrg.value.filter((item) => item.number);
    data.businessCard = businessCardImages.value?.map((item) => item.url);
    data.custom = newInfoItemListOrg.value?.map((item) => ({
      name: item.name,
      value: item.value
    }));
    data.region =
      formDataOrg.region.length === 0
        ? ""
        : formDataOrg.region.length === 1 && Array.isArray(formDataOrg.region)
        ? formDataOrg.region[0]
        : regionNamesOrg.value.join("-");
    if (isAddVisibleOrg.value) {
      data.display = 2
    }
    let res = isAddVisibleOrg.value
      ? await addRelatedOrganize(data)
      : await setRelatedOrg(formDataOrg.organizeId, data);
    if (res?.status === 200) {
      MessagePlugin.success(t("identity.saveSuccess"));
      closeDrawerOrg();
      visibleOrg.value = false;
      getRelatedOrganizeList();
    }
  }
};
const routerBack = () => {
  const id = encodeURIComponent(route.query.cardId);
  ipcRenderer.invoke("set-window-sise", {
    window: "identWin",
    width: 336,
    height: 500
  });
  router.replace({
    path: `/identitycard/view/${id}/${id}`,
    query: {
      showMoreCard: route.query.showMoreCard,
      fromIdentityPage: "fromIdentityPage"
    }
  });
  onCancelUpload();
};
const openOrgDialog = async (isAddType, item = {}) => {
  isAddVisibleOrg.value = isAddType;
  if (!isAddType) {
    let res = await getRelatedOrg(item.organizeId);
    if (res?.data?.code === 0) {
      Object.assign(formDataOrg, {
        ...formDataOrg,
        ...res?.data?.data,
        orgName: res?.data?.data.name
      });
      if (formDataOrg.phone?.length) {
        newTelItemListOrg.value = formDataOrg.phone?.map(ite => {
          return {
            phone: ite.number,
            code: ite.code,
          };
        });
        formDataOrg.newTelItemListOrg = newTelItemListOrg.value;
      }
      if (formDataOrg.tel?.length) {
        newFixedTelItemListOrg.value = formDataOrg.tel;
        formDataOrg.newFixedTelItemListOrg = newFixedTelItemListOrg.value;
      }
      if (formDataOrg.custom?.length) {
        newInfoItemListOrg.value = formDataOrg.custom?.map((item) => ({
          ...item,
          showLabel: true
        }));
        formDataOrg.newInfoItemListOrg = newInfoItemListOrg.value;
      }
      if (formDataOrg.businessCard?.length) {
        businessCardImages.value = formDataOrg.businessCard?.map((item) => ({
          url: item,
          name: item
        }));
      }
      if (formDataOrg.region) {
        areaTempOrg = formDataOrg.region;
        formDataOrg.region = [formDataOrg.region];
        await getRegionInfoOrg("");
      }
    }
  } else {
    await getRegionInfoOrg("");
  }
  visibleOrg.value = true;
};
const getRelatedOrganizeList = () => {
  relatedOrganize({
    cardId: route.query.cardId,
    viewCardId: route.query.cardId
  }).then((res) => {
    if (res?.data?.code === 0) {
      orgList.value = res?.data?.data || [];
      getVisibleName('orgList', res?.data?.data);
    }
  });
};
// 获取商协会列表
const getRelatedBusinessList = () => {
  relatedBusiness({
    cardId: route.query.cardId,
    viewCardId: route.query.cardId
  }).then((res) => {
    if (res?.data?.code === 0) {
      bussinessList.value = res?.data?.data || [];
      getVisibleName('bussinessList', res?.data?.data);
    }
  });
};

// todo 使用index做索引 排序后设置有问题，先强刷，后面再优化重构（先只处理个人身份的自定义排序，内部外部自定义暂不支持排序待优化）
const personalFormKey = ref(0);
const draggableEnd = async () => {
  personalFormKey.value++;
};

const onDraggableEnd = async () => {
  let ids = orgList.value?.map((item) => item.organizeId);
  const res = await setRelatedOrganizeSort({
    cardId: route.query.cardId,
    ids
  });
  if (res?.status === 200) {
    MessagePlugin.success(t("identity.operationSuccess"));
  }
};

// 商协会列表-id
const onDraggableBusinessEnd = async () => {
  let ids = orgList.value?.map((item) => item.id);
  const res = await setRelatedBusinessSort({
    // cardId: route.query.cardId,
    ids
  });
  if (res?.status === 200) {
    MessagePlugin.success(t("identity.operationSuccess"));
  }
};

const returnFormDataInnerData = (type, cardIdType = "inner") => {
  let obj = cardInfo.value?.options?.find((item) => item.type === type);
  if (obj) {
    if (Array.isArray(obj.value)) {
      obj.value.forEach((item) => {
        if (type === 'phone') {
          item.code = String(item.code);
        } else {
          item.departmentAndJob = `${item.departmentName || "--"}/${
            item.jobName || "--"
          }`;
        }
      });
    }
    // 数据返回空串
    if (type === 'positions' && !obj.value) {
      obj.value = [{
        departmentAndJob: '--'
      }];
    }
    // return cardIdType === "outer" ? { ...obj, editable: 0 } : { ...obj };
    return cardIdType === "outer" ? { ...obj } : { ...obj };
  }
  return {
    editable: 0,
    value: ""
  };
};
const refreshProfileData = () => {
  getProfile().then((res) => {
    if (res.status === 200) {
      ipcRenderer.invoke("change-profiles-info", res.data);
      setProfilesInfo(res.data);
    }
  });
};
const refreshData = (item) => {
  cardInfo.value.name = item.name;
  refreshProfileData();
};
const initData = async () => {
  if (!route.query.cardId) return;
  cardIdTypeName.value = cardIdType(route.query.cardId);
  cardInfo.value = await cardDataForEdit(route.query.cardId, true, route.query.cardId);
  originalCardInfo.value = JSON.parse(JSON.stringify(cardInfo.value));
  if (cardIdTypeName.value === "inner") {
    Object.assign(formDataInner, {
      ...formDataInner,
      ...cardInfo.value
    });
    formDataInner.jobNumberData = returnFormDataInnerData("jobNumber");
    formDataInner.positionsData = returnFormDataInnerData("positions");
    formDataInner.emailData = returnFormDataInnerData("email");
    formDataInner.phoneData = returnFormDataInnerData("phone");
    formDataInner.phoneEditable = cardInfo.value?.options?.find(
      (item) => item.type === "phone"
    )?.editable;
    formDataInner.avatarEditable = cardInfo.value?.options?.find(
      (item) => item.type === "avatar"
    )?.editable;
    formDataInner.nameEditable = cardInfo.value?.options?.find(
      (item) => item.type === "name"
    )?.editable;
    newTelItemList.value = cardInfo.value?.options?.find(
      (item) => item.type === "phone"
    )?.value;
    formDataInner.newTelItemList = newTelItemList.value;
    formDataInner.newInfoItemList = cardInfo.value?.options?.filter(
      (item) => item.type.startsWith('sys_custom')
    );
    console.log(formDataInner)
    getVisibleName('inner', formDataInner);
    if (cardInfo.value.contact_auth) getContactData();
  } else if (cardIdTypeName.value === "platform") {
    Object.assign(formDataInner, {
      ...formDataInner,
      ...cardInfo.value
    });
    formDataInner.jobNumberData = returnFormDataInnerData("member_position");
    formDataInner.emailData = returnFormDataInnerData("email");
    formDataInner.phoneData = returnFormDataInnerData("phone");
    formDataInner.phoneEditable = cardInfo.value?.options?.find(
      (item) => item.type === "phone"
    )?.editable;
    formDataInner.avatarEditable = cardInfo.value?.options?.find(
      (item) => item.type === "avatar"
    )?.editable;
    formDataInner.nameEditable = cardInfo.value?.options?.find(
      (item) => item.type === "name"
    )?.editable;
    newTelItemList.value = cardInfo.value?.options?.find(
      (item) => item.type === "phone"
    )?.value;
    formDataInner.newTelItemList = newTelItemList.value;
    formDataInner.newInfoItemList = cardInfo.value?.options?.filter(
      (item) => item.type.startsWith('sys_custom')
    );
    console.log(formDataInner)
    getVisibleName('platform', formDataInner);
    if (cardInfo.value.contact_auth) getContactData();
  } else if (cardIdTypeName.value === "outer") {
    Object.assign(formDataInner, {
      ...formDataInner,
      ...cardInfo.value
    });
    // 外部用户只能添加组织
    formDataInner.jobNumberData = returnFormDataInnerData("jobNumber", "outer");
    formDataInner.positionsData = returnFormDataInnerData("positions", "outer");
    formDataInner.emailData = returnFormDataInnerData("email", "outer");
    formDataInner.phoneData = returnFormDataInnerData("phone", "outer");
    formDataInner.phoneEditable = cardInfo.value?.options?.find(
      (item) => item.type === "phone"
    )?.editable;
    formDataInner.avatarEditable = cardInfo.value?.options?.find(
      (item) => item.type === "avatar"
    )?.editable;
    formDataInner.nameEditable = cardInfo.value?.options?.find(
      (item) => item.type === "name"
    )?.editable;
    newTelItemList.value = cardInfo.value?.options?.find(
      (item) => item.type === "phone"
    )?.value;
    formDataInner.newTelItemList = newTelItemList.value;
    formDataInner.newInfoItemList = cardInfo.value?.options?.filter(
      (item) => item.type.startsWith('sys_custom')
    );
    console.log(formDataInner)
    getVisibleName('outer', formDataInner);
    if (cardInfo.value.contact_auth) getContactData();
    await getRegionInfo("");
    await getRegionInfoOrg("");
    getRelatedOrganizeList();
    getRelatedBusinessList();
  } else {
    Object.assign(formData, {
      ...formData,
      ...cardInfo.value
    });
    newTelItemList.value = cardInfo.value?.options?.find(
      (item) => item.type === "phone"
    )?.value;
    formData.newTelItemList = newTelItemList.value;
    newInfoItemList.value = cardInfo.value?.options?.filter((item) =>
      item.type.startsWith("custom_"));
    formData.newInfoItemList = newInfoItemList.value;
    // areaTemp = JSON.stringify(formData.options?.find(item => item.type === 'area')?.key)
    formData.options?.forEach((item) => {
      item.showSelect = false;
      if (item.type === "area") {
        areaTemp = JSON.stringify(item);
        // item.value = item.key
        item.value = [item.value];
      } else if (item.type === "address") {
        areaTemp = JSON.stringify(item);
        // item.value = item.key
        item.value = [item.value];
        item.type = "area";
      }
    });
    getVisibleName('personal', formData);
    // await getRegionInfo(areaTemp ? JSON.parse(areaTemp)?.key || '' : '');
    await getRegionInfo("");
    await getRegionInfoOrg("");
    getRelatedOrganizeList();
    getRelatedBusinessList();
    checkLinkId().then((res) => {
      showEditLkId.value = !!res?.data?.done;
    });
  }
};

watch(
  () => route.query,
  async (newValue) => {
    setIdentityEditInfoFocus(true);
    await initData();
  },
  {
    immediate: true
  }
);
onMounted(async () => {
  ipcRenderer.invoke("set-window-sise", {
    window: "identWin",
    width: 464,
    height: 600
  });
});
ipcRenderer.on("load-card", (e, val) => {
  routerBack();
});

const { squareEnable, squareEnableChange } = useSquareEnable();


const activeCardId = [route.query.cardId]
const selectMemberVisible = ref(false);
const selectedActorsList = ref([]);
const selectMemberList = (val) => {
  console.log(val);
};
const selectedElement = ref({});

const hoverValue = ref(false);
const showAct = () => {
  if (formDataInner.avatarEditable === 1) {
    hoverValue.value = true;
  }
};
const hiddenAct = () => {
  hoverValue.value = false;
};

const getformDataInnerOption = (data, type) => {
  return data?.find(v => v.type === type)
}

const showMemberPopup = (item, key = 'visible_type') => {
  return [3, 4].includes(item[key])
    && item?.visible_users?.staffs?.length;
}
const showshowMemberPopupTitle = (item, key = 'visible_type') => {
  return showTypeOptions.value?.find(v => v.value === item[key])?.label;
}

const getContactData = async () => {
  let res = await getContactConfig(encodeURIComponent(route.query.cardId));
  if (res?.status === 200) {
    formDataInner.contactData = {
      visible: 1,
      visible_type: Number(res?.data?.code) ?? 0,
      visible_users: {
        departments: [],
        staffs: res?.data?.card_ids ?? []
      }
    }
    formDataInner.originalContactData = JSON.parse(JSON.stringify(formDataInner.contactData));
    if ([3,4].includes(Number(res?.data?.code)) && res?.data?.card_ids?.length) {
      let re = await handelCardIds(res?.data?.card_ids);
      formDataInner.contactData.visible_users_name = re?.map(v => v.comment || v.staffName);
    }
  }
}

const setContactData = async () => {
  try {
      let res = await setContactConfig({
      card_id: route.query.cardId,
      code: formDataInner.contactData.visible_type,
      card_ids: formDataInner.contactData?.visible_users?.staffs ?? []
    });
  } catch (error) {
    console.log('=====>',error);
  }

}

const getVisibleName = async (type, data) => {
  if (type === 'orgList') {
    orgList.value?.map(async (item) => {
      if (item.display && [3,4].includes(item.display) && item.visible_users?.staffs?.length) {
        let re = await handelCardIds(tranStaffs(item.visible_users?.staffs, cardIdTypeName.value === 'platform' ? 'platform' : 'inner'));
        item.visible_users_name = re?.map(v => v.comment || v.staffName);
      }
    });
  } else if (type === 'bussinessList') {
    bussinessList.value?.map(async (item) => {
      if (item.display && [3,4].includes(item.display) && item.visible_users?.staffs?.length) {
        let re = await handelCardIds(tranStaffs(item.visible_users?.staffs, cardIdTypeName.value === 'platform' ? 'platform' : 'inner'));
        item.visible_users_name = re?.map(v => v.comment || v.staffName);
      }
    });
  } else if (type === 'outer') {
    data.options?.map(async (item) => {
      if (item.visible && [3,4].includes(item.visible_type) && item.visible_users?.staffs?.length) {
        let re = await handelCardIds(tranStaffs(item.visible_users?.staffs, cardIdTypeName.value === 'platform' ? 'platform' : 'inner'));
        item.visible_users_name = re?.map(v => v.comment || v.staffName);
        if (item.type.startsWith('sys_custom')) {
          // formDataInner.newInfoItemList.visible_users_name = item.visible_users_name;
        } else {
          formDataInner[`${item.type}Data`].visible_users_name = item.visible_users_name;
        }
      }
    });
  } else if (type === 'inner') {
    data.options?.map(async (item) => {
      if (item.visible && [3,4].includes(item.visible_type) && item.visible_users?.staffs?.length) {
        let re = await handelCardIds(tranStaffs(item.visible_users?.staffs, cardIdTypeName.value === 'platform' ? 'platform' : 'inner'));
        item.visible_users_name = re?.map(v => v.comment || v.staffName);
        formDataInner[`${item.type}Data`].visible_users_name = item.visible_users_name;
      }
    });
  } else if (type === 'platform') {
    data.options?.map(async (item) => {
      if (item.visible && [3,4].includes(item.visible_type) && item.visible_users?.staffs?.length) {
        let re = await handelCardIds(tranStaffs(item.visible_users?.staffs, cardIdTypeName.value === 'platform' ? 'platform' : 'platform'));
        item.visible_users_name = re?.map(v => v.comment || v.staffName);
        formDataInner[`${item.type}Data`].visible_users_name = item.visible_users_name;
      }
    });
  } else {
    data.options?.map(async (item) => {
      if (item.visible && [3,4].includes(item.visible_type) && item.visible_users?.staffs?.length) {
        let re = await handelCardIds(tranStaffs(item.visible_users?.staffs, cardIdTypeName.value === 'platform' ? 'platform' : 'inner'));
        item.visible_users_name = re?.map(v => v.comment || v.staffName);
      }
    });
  }
}

const removeVisibleName = (type, data) => {
  if (type === 'outer') {
    data?.options?.forEach((item) => {
      delete item.visible_users_name
      delete item.visible_users_type
    });
  } else if (type === 'inner') {
    data?.options?.forEach((item) => {
      delete item.visible_users_name
      delete item.visible_users_type
    });
  } else if (type === 'platform') {
    data?.options?.forEach((item) => {
      delete item.visible_users_name
      delete item.visible_users_type
    });
  } else {
    data?.attachments?.options?.forEach((item) => {
      delete item.visible_users_name
      delete item.visible_users_type
    });
  }
}
const orgItemChange = (value, item, sourceObj) => {
  console.log(value, item, originalCardInfo.value);
  if (![3,4].includes(value)) {
    orgList.value[sourceObj.key].display = value
    orgListChange(value, item);
    return;
  }
  if (item.visible_users_type) {
    selectedActorsList.value = item.visible_users_type === value ? tranStaffs(item.visible_users?.staffs, cardIdTypeName.value === 'platform' ? 'platform' : 'inner') : [];
  } else {
    selectedActorsList.value = orgList.value[sourceObj.key]?.display === value ? tranStaffs(item.visible_users?.staffs, cardIdTypeName.value === 'platform' ? 'platform' : 'inner') : [];
  }
  if ([3,4].includes(value)) {
    selectedElement.value = item;
    setIdentityEditInfoFocus(false);
    ipcRenderer.invoke("openDialogIdentity", {
      sourceObj: { ...sourceObj, value },
      identityType: 'orgList',
      activeCardId: activeCardId,
      selectedActorsList: JSON.parse(JSON.stringify(selectedActorsList.value))
    });
  }
};

const businessItemChange = (value, item, sourceObj) => {
  console.log(value, item, originalCardInfo.value);
  if (![3,4].includes(value)) {
    bussinessList.value[sourceObj.key].display = value
    orgListBusinessChange(value, item);
    return;
  }
  if (item.visible_users_type) {
    selectedActorsList.value = item.visible_users_type === value ? tranStaffs(item.visible_users?.staffs, cardIdTypeName.value === 'platform' ? 'platform' : 'inner') : [];
  } else {
    selectedActorsList.value = bussinessList.value[sourceObj.key]?.display === value ? tranStaffs(item.visible_users?.staffs, cardIdTypeName.value === 'platform' ? 'platform' : 'inner') : [];
  }
  if ([3,4].includes(value)) {
    selectedElement.value = item;
    setIdentityEditInfoFocus(false);
    ipcRenderer.invoke("openDialogIdentity", {
      sourceObj: { ...sourceObj, value },
      identityType: 'bussinessList',
      activeCardId: activeCardId,
      selectedActorsList: JSON.parse(JSON.stringify(selectedActorsList.value))
    });
  }
};

const itemChange = (value, item, sourceObj, type) => {
  console.log(value, item, sourceObj, originalCardInfo.value);
  if (item.visible_users_type) {
    if (type === 'contactData') {
      selectedActorsList.value = item.visible_users_type === value ? item.visible_users?.staffs : [];
    } else {
      selectedActorsList.value = item.visible_users_type === value ? tranStaffs(item.visible_users?.staffs, cardIdTypeName.value === 'platform' ? 'platform' : 'inner') : [];
    }

  } else {
    if (type === 'contactData') {
      selectedActorsList.value = formDataInner.originalContactData?.visible_type === value ? formDataInner.originalContactData?.visible_users?.staffs : [];
    } else {
      selectedActorsList.value = originalCardInfo.value?.options?.find(v => v.type === item.type)?.visible_type === value ? tranStaffs(item.visible_users?.staffs, cardIdTypeName.value === 'platform' ? 'platform' : 'inner') : [];
    }

  }
  if ([3,4].includes(value)) {
    selectedElement.value = item;
    setIdentityEditInfoFocus(false);
    setTimeout(() => {
      ipcRenderer.invoke("openDialogIdentity", {
      sourceObj: { ...sourceObj, value },
      identityType: type ?? cardIdTypeName.value,
      activeCardId: activeCardId,
      selectedActorsList: JSON.parse(JSON.stringify(selectedActorsList.value)),
      setNoAlwaysOnTop: true
    });
    }, 500);

  } else {
    if (sourceObj.form === 'formData') {
      formData.options?.forEach(v => {
        if (v.type === sourceObj.key) {
          v.visible_type = value
        }
      })
    } else if (sourceObj.form === 'newInfoItemList') {
      formData.options[sourceObj.key].visible_type = value
      // newInfoItemList.value[sourceObj.key].visible_type = value
    } else if (sourceObj.form === 'formDataInner' || sourceObj.form === 'formDataInner_newInfoItemList') {
      if (sourceObj.type.startsWith('sys_custom')) {
        formDataInner.newInfoItemList[sourceObj.key].visible_type = value;
      } else {
        formDataInner[sourceObj.key].visible_type = value;
      }
      if (type === 'contactData') {
        return;
      }
      const _tranNameObj = {
        phone: 'telephone',
        jobNumber: 'no',
        positions: 'positions',
        email: 'email',
      };
      setIdentityVisible({
        cardId: route.query.cardId,
        name: cardIdTypeName.value === "outer" ? (_tranNameObj[sourceObj.type] ?? sourceObj.type) : sourceObj.type,
        visible_type: value,
        visible_users: {}
      });
    }
  }
};
// watch(() => selectMemberVisible.value, (val) => {
  // if (val) {
  //   ipcRenderer.invoke("set-window-full-sise", {
  //     window: "identWin",
  //   });
  // } else {
  //   ipcRenderer.invoke("set-window-sise", {
  //     window: "identWin",
  //     width: 448,
  //     height: 600
  //   });
  // }
// });
ipcRenderer.on('identWin-visible-users', async (e, val) => {
  // identityType: personal/inner/outer/orgList/bussinessList/contactData
  if (!val.data?.length) return;
  if (val?.sourceData?.sourceObj.form === 'formData') {
    formData.options?.forEach(v => {
      if (v.type === val?.sourceData?.sourceObj.key) {
        v.visible_type = val?.sourceData?.sourceObj.value
      }
    })
  } else if (val?.sourceData?.sourceObj.form === 'newInfoItemList') {
    formData.options[val?.sourceData?.sourceObj.key].visible_type = val?.sourceData?.sourceObj.value
    // newInfoItemList.value[val?.sourceData?.sourceObj.key].visible_type = val?.sourceData?.sourceObj.value
  } else if (val?.sourceData?.sourceObj.form === 'formDataInner') {
    formDataInner[val?.sourceData?.sourceObj.key].visible_type = val?.sourceData?.sourceObj.value
  } else if (val?.sourceData?.sourceObj.form === 'formDataInner_newInfoItemList') {
    formDataInner.newInfoItemList[val?.sourceData?.sourceObj.key].visible_type = val?.sourceData?.sourceObj.value
  } else if (val?.sourceData?.sourceObj.form === 'orgList') {
    orgList.value[val?.sourceData?.sourceObj.key].display = val?.sourceData?.sourceObj.value
  } else if (val?.sourceData?.sourceObj.form === 'bussinessList') {
    bussinessList.value[val?.sourceData?.sourceObj.key].display = val?.sourceData?.sourceObj.value
  }
  if (val?.sourceData?.identityType === 'personal') {
    formData.options?.forEach(v => {
      if (v.type === selectedElement.value.type) {
        v.visible_users = {
          departments: [],
          staffs: tranStaffs(val.data, 'id')
        }
        v.visible_users_name = tranStaffs(val.data, 'name');
        v.visible_users_type = v.visible_type;
      }
    })
  } else if (val?.sourceData?.identityType === 'inner' || val?.sourceData?.identityType === 'platform' || val?.sourceData?.identityType === 'outer') {
    let visible_users = {
          departments: [],
          staffs: tranStaffs(val.data, 'id')
        }
    const _tranNameObj = {
      phone: 'telephone',
      jobNumber: 'no',
      positions: 'positions',
      email: 'email',
    };
    let res = await setIdentityVisible({
      cardId: route.query.cardId,
      name: cardIdTypeName.value === "outer" ? (_tranNameObj[selectedElement.value.type] ?? selectedElement.value.type) : selectedElement.value.type,
      visible_type: val?.sourceData?.sourceObj.form === 'formDataInner_newInfoItemList' ? formDataInner.newInfoItemList[val?.sourceData?.sourceObj.key].visible_type : formDataInner[`${selectedElement.value.type}Data`].visible_type,
      visible_users
    })
    if (res?.status === 200) {
      let visible_users_name = tranStaffs(val.data, 'name');
      let visible_users_type = selectedElement.value.visible_type;

      if (val?.sourceData?.sourceObj.form === 'formDataInner_newInfoItemList') {
        formDataInner.newInfoItemList[val?.sourceData?.sourceObj.key] = {
          ...formDataInner.newInfoItemList[val?.sourceData?.sourceObj.key],
          visible_users,
          visible_users_name,
          visible_users_type
        }
        formDataInner.options?.forEach(v => {
          if (v.type === selectedElement.value.type) {
            v.visible_type = formDataInner.newInfoItemList[val?.sourceData?.sourceObj.key].visible_type;
            v.visible_users = formDataInner.newInfoItemList[val?.sourceData?.sourceObj.key].visible_users;
            v.visible_users_name = formDataInner.newInfoItemList[val?.sourceData?.sourceObj.key].visible_users_name;
            v.visible_users_type = formDataInner.newInfoItemList[val?.sourceData?.sourceObj.key].visible_users_type;
          }
        })
      } else {
        formDataInner[`${selectedElement.value.type}Data`] = {
          ...formDataInner[`${selectedElement.value.type}Data`],
          visible_users,
          visible_users_name,
          visible_users_type
        }
        formDataInner.options?.forEach(v => {
          if (v.type === selectedElement.value.type) {
            v.visible_type = formDataInner[`${selectedElement.value.type}Data`].visible_type;
            v.visible_users = formDataInner[`${selectedElement.value.type}Data`].visible_users;
            v.visible_users_name = formDataInner[`${selectedElement.value.type}Data`].visible_users_name;
            v.visible_users_type = formDataInner[`${selectedElement.value.type}Data`].visible_users_type;
          }
        })
      }
    }


  } else if (val?.sourceData?.identityType === 'contactData') {
    let visible_users = {
          departments: [],
          staffs: tranStaffs(val.data, 'cardId')
        }
    let visible_users_name = tranStaffs(val.data, 'name');
    let visible_users_type = selectedElement.value.visible_type;
    formDataInner['contactData'] = {
      ...formDataInner['contactData'],
      visible_users,
      visible_users_name,
      visible_users_type
    }
  } else if (val?.sourceData?.identityType === 'orgList') {
    orgList.value?.forEach(v => {
      if (v.organizeId === selectedElement.value.organizeId) {
        v.visible_users = {
          departments: [],
          staffs: tranStaffs(val.data, 'id')
        }
        v.visible_users_name = tranStaffs(val.data, 'name');
        v.visible_users_type = v.display;
        orgListChange(v.display, v, true);
      }
    })
  } else if (val?.sourceData?.identityType === 'bussinessList') {
    bussinessList.value?.forEach(v => {
      if (v.id === selectedElement.value.id) {
        v.visible_users = {
          departments: [],
          staffs: tranStaffs(val.data, 'id')
        }
        v.visible_users_name = tranStaffs(val.data, 'name');
        v.visible_users_type = v.display;
        orgListBusinessChange(v.display, v, true);
      }
    })
  }

})
</script>

<style lang="less">
.date-birthday-picker{
  -webkit-app-region: no-drag !important;
}
.t-popconfirm__body {
  align-items: center;
  .close-icon {
    margin-right: 8px;
  }
}
.t-cascader__popup {
  max-width: 100vw;
}
.identity-visible-popup{
  display: flex;
  width: 256px;
  max-width: 256px;
  max-height: 214px;
  padding: 4px 8px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 4px;
  border-radius: var(--kyy_radius_toopltip, 6px);
  background: var(--kyy_color_toopltip_text, #FFF);
  .t-popup__content{
    width: 100%;
    height: 100%;
    padding: 4px 8px;
  }
  .member-popup-box{
    width: 100%;
    height: 100%;
  }
  .member-popup-title{
    color: var(--text-kyy_color_text_1, #1A2139);

    /* kyy_fontSize_2/bold */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px; /* 157.143% */
  }
  .member-popup-content{
    color: var(--text-kyy_color_text_1, #1A2139);

    /* kyy_fontSize_1/regular */
    font-family: PingFang SC;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px; /* 166.667% */
  }
}
.identity-visible-popup-select{
  border-radius: var(--kyy_radius_dropdown_m, 8px);
  .t-popup__content{
  }
  .t-select__list{
    display: flex;
    padding: 4px;
    flex-direction: column;
    align-items: center;
    gap: 2px;
  }
  .t-select-option{
    width: 136px;
    height: 32px;
    padding: 0 16px !important;
    color: var(--kyy_color_dropdown_text_default, #1A2139);

    /* kyy_fontSize_2/regular */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
}
.close-popconfirm-popup{
  .t-popup__content{
    margin-bottom: 3px;
    padding: 0;
  }
  .t-popconfirm__cancel{
    height: 24px;
    min-height: 24px;
    max-height: 24px;
    padding: 0px 12px;
    border-radius: var(--radius-kyy_radius_button_s, 4px);
    border: 1px solid var(--color-button_border-kyy_color_buttonBorder_border_dedault, #D5DBE4);
    background: var(--color-button_border-kyy_color_buttonBorder_bg_default, #FFF);
    color: var(--color-button_border-kyy_color_buttonBorder_text_default, #516082);
    text-align: center;

    /* kyy_fontSize_2/regular */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
    .t-button__text{
      font-weight: 400;
    }
  }
  .t-popconfirm__confirm{
    height: 24px;
    min-height: 24px;
    max-height: 24px;
    padding: 0px 12px;
    border-radius: var(--radius-kyy_radius_button_s, 4px);
    border: 1px solid var(--color-button_secondaryError-kyy_color_button_secondaryError_border_dedault, #D54941);
    background: var(--color-button_secondaryError-kyy_color_button_secondrayError_bg_default, #FDF5F6);
    color: var(--color-button_secondaryError-kyy_color_button_secondrayError_text_default, #D54941);

    /* kyy_fontSize_2/regular */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
    .t-button__text{
      font-weight: 400;
    }
  }
}
</style>
<style lang="less" scoped>


.tdesign-demo-image-viewer__ui-image {
  .kyy-avatar-bg {
    z-index: -1;
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 44px;
    height: 44px;
    border-radius: 50%;
    background-color: var(--bg-kyy_color_bg_mask, rgba(0, 0, 0, 0.50));
    img{
      width: 20px;
      height: 20px;
      cursor: pointer;
    }
  }
  .kyy-avatar-hover {
    z-index: 2;
  }
}

:deep(input::-webkit-outer-spin-button),
:deep(input::-webkit-inner-spin-button) {
  -webkit-appearance: none !important;
}
:deep(input[type="number"]) {
  -moz-appearance: textfield;
}
::-webkit-scrollbar {
  width: 0;
  height: 8px;
}
/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  border-radius: 3px;
  background: rgba(0, 0, 0, 0.1);
  -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.5);
}
.main-card {
  //width: 448px;
  width: 464px;
  min-height: 600px;
  height: auto;
  background-color: #ffffff;
  border-radius: 4px;
  overflow: hidden;

  .w-close {
    -webkit-app-region: drag;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24px 24px;
    .header-title {
      font-size: 16px;

      font-weight: 700;
      text-align: left;
      color: #13161b;
      line-height: 24px;
    }
    .btn-item {
      width: 16px;
      height: 16px;
      cursor: pointer;

      &.close {
        -webkit-app-region: no-drag;
        background-image: url(@/assets/identity/0.icon_close.svg);
        background-size: cover;
      }
    }
  }

  .w-header {
    padding: 0 24px;

    .box-avatar {
      width: 44px;
      height: 44px;
      position: relative;
      cursor: pointer;

      .t-upload {
        position: absolute;
        right: 0;
        bottom: 0;
      }
      .avatar-btn {
        width: 20px;
        height: 20px;
        background-image: url(@/assets/identity/0.icon_avatar.png);
        background-size: cover;
        position: absolute;
        right: 1px;
        bottom: 1px;
        z-index: 2;
      }

      .avatar {
        width: 60px;
        height: 60px;
        border-radius: 10px;
        background-size: cover;
      }
    }

    .w-1 {
      width: 100%;

      .nickname-box {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        .btn-item {
          margin-left: 8px;
          width: 20px;
          height: 20px;
          cursor: pointer;

          &.edit {
            background-image: url(@/assets/identity/icon_edit_default.svg);
            background-size: cover;
          }
          &.edit:hover {
            background-image: url(@/assets/identity/icon_edit_hover.svg);
            background-size: cover;
          }
        }

        :deep(.t-input){
          height: 24px;
        }
      }

      .nickname {
        max-width: 300px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        color: var(--text-kyy_color_text_1, #1A2139);

        /* kyy_fontSize_3/bold */
        font-family: PingFang SC;
        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        line-height: 24px; /* 150% */
        cursor: text;
        -webkit-app-region: no-drag;
      }

      .lkId {
        max-width: 120px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        color: var(--text-kyy_color_text_3, #828DA5);

        /* kyy_fontSize_2/regular */
        font-family: PingFang SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
        cursor: text;
        -webkit-app-region: no-drag;
      }
    }
  }

  .w-info {
    :deep(.t-input-adornment) {
      width: 100%;
      .t-input-adornment__prepend {
        width: 80px;
        margin-right: 8px;
        background-color: #ffffff;
        .t-input {
          border-radius: 4px;
        }
      }
      .t-input__wrap {
        // margin-right: 8px;
        .t-input {
          //max-width: 137px;
          border-radius: 4px;
          min-width: 80px;
        }
      }
      .t-input-adornment__append {
        margin-left: 8px;
        background-color: #ffffff;
        display: flex;
        align-items: center;
        width: 24px;
        cursor: pointer;
      }
    }
    //padding: 0 32px;
    :deep(.t-tabs) {
      .t-tabs__nav-item-wrapper {
        //padding-left: 0;
        //margin-left: 0;
      }
      .t-tabs__bar {
        //background-color: transparent;
      }
      .t-tabs__header {
        padding: 0 24px;
      }
      .t-tab-panel {
        overflow: auto;
        height: 370px;
        padding: 0 0 0 24px;
      }
      .t-tab-panel-height{
        height: 388px;
      }
      .t-form {
        padding-right: 24px;
      }
    }
    .personal_tabs {
      width: calc(100% - 48px);
      background-color: #FFFFFF;
      padding-top: 16px;
      border-bottom: 1px solid var(--divider-kyy_color_divider_light, #ECEFF5);
      display: flex;
      align-items: center;
      gap: 12px;
      margin: 0 24px 8px 24px;
      .default-tab-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        .t-button {
          padding: 0 8px;
          color: var(--kyy_color_tabbar_item_text, #1a2139);
          text-align: center;

          /* kyy_fontSize_2/regular */
          font-family: PingFang SC;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px; /* 157.143% */
          border: none !important;
        }
        .tab-item-border {
          width: 28px;
          height: 3px;
          border-radius: 1.5px;
          background: transparent;
        }
      }
      .active-tab-item {
        .t-button {
          font-weight: 600;
          color: var(--brand-kyy-color-brand-default, #4d5eff) !important;
        }
        .tab-item-border {
          width: 28px;
          height: 3px;
          border-radius: 1.5px;
          background: var(--brand-kyy-color-brand-default, #4d5eff);
        }
      }
    }
    .org-box {
      padding-right: 16px;
      margin-top: 8px;
      .form-item-box {
        padding-top: 0;
        .form-item-label-box {
          flex: 1;
        }
        .orgLabel {
          display: flex;
          .orgLabelName {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            color: var(--text-kyy_color_text_1, #1A2139);

            /* kyy_fontSize_2/regular */
            font-family: PingFang SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
          }
          .associationTag {
            margin-left: 4px;
            display: flex;
            height: 20px;
            min-height: 20px;
            max-height: 20px;
            padding: 2px 4px;
            justify-content: center;
            align-items: center;
            gap: 4px;
            border-radius: var(--kyy_radius_tag_s, 4px);
            background: var(--kyy_color_tag_bg_success, #E0F2E5);
            color: var(--kyy_color_tag_text_success, #499D60);
            text-align: center;

            /* kyy_fontSize_1/regular */
            font-family: PingFang SC;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px; /* 166.667% */
          }
        }
      }
      .addOrgButtonBox{
        padding: 9px 8px;
        border-radius: 4px;
        border: 1px solid var(--brand-kyy_color_brand_default, #4D5EFF);
        background: var(--bg-kyy_color_bgBrand_hover, #EAECFF);
        margin-bottom: 2px !important;
      }
      .addOrgButton {
        height: 22px;
        width: 100%;
        padding-left: 0;
        justify-content: flex-start;
        border: none !important;
        color: var(--brand-kyy_color_brand_default, #4D5EFF);
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
        background-color: transparent !important;
      }
      .defaultPage-box {
        font-size: 14px;

        font-weight: 400;
        text-align: center;
        color: #13161b;
        line-height: 22px;

        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        img {
          width: 200px;
          height: 200px;
          margin-bottom: 10px;
        }
      }
    }

    .form-item-chosenClass{
      border-radius: 8px;
      background: var(--bg-kyy_color_bg_default, #FFF);
      box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.12);
    }
    .form-item-ghostClass{
      border-top-left-radius: 0;
      border-top-right-radius: 0;
      border-top: 2px solid var(--brand-kyy_color_brand_default, #4D5EFF);
    }
    .form-item-box {
      // padding: 16px 0 0 0;
      padding: 8px;
      .form-item {
        margin-bottom: 8px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .form-item-label-box {
          display: flex;
          align-items: center;
          cursor: pointer;
        }
        .newInfoItemLabel {
          display: flex;
          align-items: center;
          .label {
            max-width: calc(100vw - 304px);
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
          .edit-icon {
            display: none;
          }
        }
        .newInfoItemLabel:hover {
          .edit-icon {
            display: inline-block;
          }
        }
        .btn-item {
          width: 16px;
          height: 16px;
          cursor: pointer;
          &.img-move-item {
            width: 20px;
            height: 20px;
          }
          &.item {
            width: 28px;
            height: 28px;
            margin-right: 4px;
            background-image: url(@/assets/identity/icon_drag_PC.svg);
            background-size: cover;
          }
          &.add {
            width: 20px;
            height: 20px;
            margin-right: 4px;
            background-image: url(@/assets/identity/icon_add.svg);
            background-size: cover;
          }
          &.delete {
            width: 20px;
            height: 20px;
            margin: 4px 4px 4px 5px;
            background-image: url(@/assets/identity/icon_delete.svg);
            background-size: cover;
          }
          &.delete:hover {
            width: 28px;
            height: 28px;
            margin: 0 0 0 1px;
            background-image: url(@/assets/identity/icon_delete_hover.svg);
            background-size: cover;
          }
          &.deleteOrg {
            width: 28px;
            height: 28px;
            padding:4px;
            margin: 4px 4px 4px 4px;
            border-radius: 4px;
            // margin-left: 16px;
          }
          &.deleteOrg:hover {
           background-color: var(--bg-kyy_color_bgBrand_hover, #EAECFF)
          }
          &.edit {
            margin-left: 16px;
            background-image: url(@/assets/identity/0.icon_icon-edit.svg);
            background-size: cover;
          }
          &.editOrg {
            width: 28px;
            height: 28px;
            padding:4px;
            border-radius: 4px;
          }
          &.editOrg:hover {
            background-color: var(--bg-kyy_color_bgBrand_hover, #EAECFF)
          }
        }
        .form-item-label {
          margin-left: 4px;
          text-align: left;
          color: var(--text-kyy_color_text_1, #1A2139);

          /* kyy_fontSize_2/bold */
          font-family: PingFang SC;
          font-size: 14px;
          font-style: normal;
          font-weight: 600;
          line-height: 22px; /* 157.143% */
        }
        .add-label {
          color: var(
            --color-button-text-brand-kyy-color-button-text-brand-font-default,
            #4d5eff
          );
          font-family: PingFang SC;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px;
        }
        .orgAddLabel {
          font-size: 14px;

          font-weight: 400;
          text-align: center;
          color: #2069e3;
          line-height: 22px;
        }
        .orgLabel {
          font-size: 14px;

          font-weight: 400;
          text-align: left;
          color: #13161b;
          line-height: 22px;

          width: 0;
          flex: 1;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;

          align-items: center;
        }
        :deep(.form-item-select) {
          display: flex;
          align-items: center;
          justify-content: end;
          .newInfoItem-select {
            // width: 100px;
            width: auto;
            max-width: 110px;
          }
          .t-input {
            border: none !important;
            padding: 0;
          }
          .t-input__inner {
            font-size: 12px;

            font-weight: 400;
            text-align: right;
            color: #4d5eff;
            line-height: 20px;
          }
          .t-select__right-icon {
            color: #4d5eff;
          }
        }
        :deep(.select-hover) {
          .t-input__inner{
            color: var(--brand-kyy_color_brand_default, #4D5EFF) !important;
            text-align: center;

            /* kyy_fontSize_1/regular */
            font-family: PingFang SC;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px; /* 166.667% */
          }
          .t-input__suffix{
            margin-left: 1px;
          }
          .t-select-input--popup-visible .t-input {
            border-radius: 4px;
            background: var(--bg-kyy_color_bgBrand_foucs, #DBDFFF);
          }
        }
        :deep(.select-hover:hover .t-input){
          // padding: 4px 0px 4px 4px;
          border-radius: 4px;
          background: var(--bg-kyy_color_bgBrand_foucs, #DBDFFF);
        }
      }
      .t-date-picker {
        width: 100%;
      }
      :deep(.t-is-disabled .t-input__inner){
        color: var(--input-kyy_color_input_text_disabled, #ACB3C0) !important;
      }
    }
    .form-item-box .form-item-box:last-child{
      margin-bottom: 30px;
    }
    .newInfoItemList {
      :deep(.newInfoItemLabel .t-input) {
        height: 25px;
        width: auto;
        min-width: 80px;
        max-width: calc(100vw - 290px);
      }
      :deep(.t-form-item__newInfoItemValue .t-form__controls) {
        //margin-left: 4px !important;
      }
    }
  }

  .w-footer {
    padding: 16px 22px;
  }
}

:deep(.visibleLkId) {
  .t-dialog__header {
    font-size: 16px;

    font-weight: 700;
    text-align: left;
    color: #13161b;
    line-height: 24px;
  }
  .t-dialog__body__icon {
    font-size: 14px;

    font-weight: 400;
    text-align: left;
    color: #717376;
    line-height: 22px;
  }
  .t-input__extra{
    white-space: normal;
  }
}
:deep(.visibleOrg) {
  border-radius: 16px;
  border: 1px solid rgba(0, 0, 0, 0.12);
  width: 100vw;
  min-height: 100vh;
  .t-drawer__header {
    min-height: auto;
    border-bottom: none;
    padding: 24px;
    justify-content: space-between;
    img{
      cursor: pointer;
      -webkit-app-region: no-drag;
    }
  }
  .t-drawer__footer {
    border-top: none;
  }
  .t-drawer__body {
    padding: 0 16px 20px 16px;
    .form-item-label {
      margin-left: 2px !important;
    }
    .t-input-adornment {
      width: 100%;
    }
    .w-info .t-input-adornment .t-input__wrap .t-input {
      //max-width: 185px;
    }
    .fixed-telephone {
      .t-input {
        //padding: 2px;
      }
      .t-input-adornment {
        .t-input__wrap {
          width: auto;
          .t-input {
            //width: 86px;
            width: 139px;
          }
        }
        .global_phone_in {
          .t-input {
          }
        }
        .global_phone_out {
          margin-left: -8px;
          .t-input {
            width: 206px;
          }
        }
      }
      .t-input-adornment__append {
        .t-input__wrap {
          width: auto;
          padding-right: 0;
        }
        .tel {
          .t-input {
            min-width: 40px;
            //width: 40px;
            width: 78px;
          }
        }
      }
      .t-input-adornment__prepend {
        display: flex;
        width: auto;
        .t-select__wrap {
          .t-input {
            //width: 60px;
            width: auto;
          }
        }
        .t-input__wrap {
          width: auto;
          padding-right: 9px;
          margin-right: 0;
        }
        .global_code {
          padding-right: 0;
          .t-input {
            min-width: 40px;
            //width: 40px;
            width: 60px;
          }
        }
      }
    }
    .businessCardUpload {
      display: flex;
      align-items: center;
      .t-upload__card {
        flex-wrap: nowrap;
        .t-upload__card-mask-item-divider {
          margin: 0 5px;
        }
        .t-link.t-upload__card-name{
          display: none;
        }
      }
      .t-upload__card-content {
        display: inline-block;
        width: 78px;
        height: 78px;
        border: 1px solid var(--kyy_color_upload_border_default, #D5DBE4);
        border-radius: 8px;
      }
      .t-upload__card-container {
        width: 78px;
        height: 78px;
        border: 1px solid var(--kyy_color_upload_border_default, #D5DBE4);
        border-radius: 8px;

        background-image: url(@/assets/identity/upload.svg);
        background-size: cover;
        svg{
          display: none;
        }
        .t-size-s {
          display: none;
          color: var(--kyy_color_upload_text_default, #516082);
          text-align: center;

          /* kyy_fontSize_1/regular */
          font-family: PingFang SC;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          line-height: 20px; /* 166.667% */
        }
        .t-icon {
          margin-bottom: 0;
        }
      }
      .t-upload__tips {
        margin-left: 8px;
        margin-top: 0;
        text-align: left;

        color: var(--text-kyy_color_text_3, #828DA5);

        /* kyy_fontSize_1/regular */
        font-family: PingFang SC;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px; /* 166.667% */
      }
    }
  }
  .t-drawer__close-btn {
    -webkit-app-region: no-drag;
  }
}

.flex {
  display: flex;
}

.flex-1 {
  flex-grow: 1;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.t-item {
  & + .t-item {
    margin-top: 8px;
  }

  .t-title {
    display: inline-block;
    width: 96px;
    font-size: 14px;
    font-weight: 400;
    color: #717376;
    line-height: 22px;
    white-space: nowrap;
  }

  .t-text {
    display: inline-block;
    font-size: 14px;
    font-weight: 600;
    color: #13161b;
    line-height: 22px;
  }
}

.icon {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}

.f-blue {
  color: #2069e3 !important;
}
</style>
