<template>
  <div style="width: 100%;">
    <div class="fullCalendar-box">
      <FullCalendar class="fullCalendar fullCalendar_schedule_right" ref="fullCalendar" :options="calendarOptions" />
      <SearchManifest v-if="showList" ref="fullCalendarListMonth"
        style="position: absolute;top: 60px;right: 20px;width: calc(100% - 280px);" v-model:visible="searchVisible"
        :isDrawer="false" :sourceListDataProps="sourceListData" :listDataProps="listData" :leftActiveDate="leftActiveDate"
        :rightActiveDate="rightActiveDate" :allConfig="props.allConfig" :promiseList="props.promiseList" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, nextTick, onMounted, reactive, computed } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { useZhixingStore } from '../store';
import _ from 'lodash';
import { useI18n } from 'vue-i18n';
import SearchManifest from '@renderer/views/zhixing/schedule/dialog/SearchManifest.vue'
const { Solar, Lunar, HolidayUtil } = require('lunar-javascript')

import FullCalendar from '@fullcalendar/vue3'
import dayGridPlugin from '@fullcalendar/daygrid'
import timeGridPlugin from '@fullcalendar/timegrid'
import interactionPlugin from '@fullcalendar/interaction'
import listPlugin from '@fullcalendar/list'
import momentPlugin from '@fullcalendar/moment';
import moment from "moment";
import { getNoticesMatters } from "@/api/zhixing/api/todo";
import { getOpenid } from "@/utils/auth";
import { getActivityDetail, getCalendars } from "@/api/activity";
import { formatStatus } from "@/views/activity/utils";
import { getImCardIds } from "@renderer/utils/auth";
import {flattenObject, saveList} from '../util'
import { getImportantDay } from '@renderer/api/important/index'
import { insertRemind, queryRemind } from "@/views/zhixing/dbService/remind";//提醒缓存接口
import { insertActivity, queryActivity } from "@/views/zhixing/dbService/activity";//活动缓存接口
import { insertShared, queryShared, insertSharedUser, querySharedUser } from "@/views/zhixing/dbService/shared";//共享缓存接口
import { insertSubscribe, querySubscribe, insertSubscribeUser, querySubscribeUser } from "@/views/zhixing/dbService/subscribe.ts";
import LynkerSDK from '@renderer/_jssdk';

const { ipcRenderer, shell } = LynkerSDK;
//订阅缓存接口
const { t } = useI18n();

const zhixingStore = useZhixingStore();
const fullCalendar = ref(null);
const showList = ref(false);
const searchVisible = ref('');
const myOpenid = computed(() => getOpenid());
const listData = ref(null);
const sourceListData = ref([]);
const currentYearAndMonth = ref(moment().format('YYYY-MM-DD'));
const leftActiveDate = ref('');
const rightActiveDate = ref('');
const fullCalendarListMonth = ref(null);
const isLeftActiveDateChange = ref(false);
const isFirst = ref(true);
const isRightActiveDate = ref(true);
const detailId = ref('')//详情id
const visible = ref(false)//详情抽屉

const eventMouseEnter = (arg) => {
  arg.el.style.boxShadow = '0px 8px 16px 0px rgba(0, 0, 0, 0.12)';
};
const eventMouseLeave = (arg) => {
  arg.el.style.boxShadow = 'none';
};
//打开各种详情弹框
const eventClick = async (arg) => {
  // 关闭事件弹框
  document.querySelector('.fc .fc-popover-close')?.click();
  if (arg.event._def?.extendedProps.notices_matters_type === 'manifest') {
    if (arg.event._def?.extendedProps.owner === getOpenid()) {
      zhixingStore.manifestDetailDialogData.props = { ...arg.event._def?.extendedProps };
      zhixingStore.manifestDetailDialogData.type = 'edit';
      zhixingStore.manifestDetailDialogData.visible = true;
    } else {
      if (viewAuthTip(arg.event._def?.extendedProps)) return;
      zhixingStore.manifestDetailDialogData.props = { ...arg.event._def?.extendedProps };
      zhixingStore.manifestDetailDialogData.type = 'detail';
      zhixingStore.manifestDetailDialogData.visible = true;
    }
  } else if (arg.event._def?.extendedProps.notices_matters_type === 'important') {
    // detailId.value=arg.event._def.extendedProps.recordId
    // visible.value=true
    //importantDetail
    zhixingStore.importantDetailDialog.props = arg.event._def.extendedProps.recordId;
    zhixingStore.importantDetailDialog.type = 'important';
    zhixingStore.importantDetailDialog.visible = true;
    emits('changeDetail', true, arg.event._def.extendedProps.recordId)

  } else if (arg.event._def?.extendedProps.notices_matters_type === 'remind') {
    if (zhixingStore.checkNet == 'good') {
      if (arg.event._def?.extendedProps.owner === getOpenid()) {
        zhixingStore.remindDetailDialogData.type = 'edit';
      } else {
        if (viewAuthTip(arg.event._def?.extendedProps)) return;
        zhixingStore.remindDetailDialogData.type = 'detail';
      }
      zhixingStore.remindDetailDialogData.props = arg.event._def?.extendedProps;
      zhixingStore.remindDetailDialogData.visible = true;
      zhixingStore.remindDetailDialogData.showOverlay = true
      zhixingStore.addRemindDialogData.visible = false
    } else {
      MessagePlugin.error('网络连接失败，请检查网络后重试')
    }
  } else if (arg.event._def?.extendedProps.notices_matters_type === 'activity') {
    if (zhixingStore.checkNet == 'good') {
      const row = arg.event._def?.extendedProps;
      console.log('活动数据', row);
      ipcRenderer.invoke("create-dialog", {
        url: `layoutActivity/activityParticipantDetailLayout/${row.activityId}?subject=${encodeURIComponent(row.subject)}&teamId=${encodeURIComponent(row.myself.teamId)}&cardId=${encodeURIComponent(row.myself.cardId)}`,
        opts: {
          x: 50,
          y: 50,
          width: 1296,
          minWidth: 1296,
          height: 720,
        },
      });
    } else {
      MessagePlugin.error('网络连接失败，请检查网络后重试')
    }
  } else {
    MessagePlugin.info('未定义数据类型')
  }
};

//关闭弹窗的事件
const handleClose4 = (flag) => {
  visible.value = flag;
};

const viewAuthTip = (item) => {
  let name = props.promiseList?.find(it => it.user === item.owner)?.title || item.owner;
  if (['TITLE', 'STATUS'].includes(item?.notices_matters_status)) {
    MessagePlugin.warning(`${name}未授权查看详情`);
    return true;
  }
  return false;
};
//点击日期格子的事件
const dateClick = (arg) => {
  console.log('dateClick', arg)
};
//设置日期格子
const datesSet = async (arg) => {
  if (isRightActiveDate.value) {
    emits('rightActiveDate', arg);
  }
  isRightActiveDate.value = true;
  currentYearAndMonth.value = arg?.start;
  await getAllListData(false, arg?.start);
  nextTick(() => {
    let element = document.querySelector('.fc-timegrid-now-indicator-line.nowIndicatorClassNames');
    if (element) {
      let son = document.createElement('div');
      son.setAttribute('class', 'nowIndicatorClassNamesSon');
      element.appendChild(son);
    }
    changeClassDom(props.activeDate);
  });
  // 切换时间滚动至中心视图
  if (arg?.view?.type === "timeGridDay" || arg?.view?.type === "timeGridWeek") {
    firstViewScrollToTime(arg);
  } else if (arg?.view?.type === "listMonth") {
    rightActiveDate.value = arg?.start;
    if (!isLeftActiveDateChange.value) {
      // 判断是否是当前月
      const isClickToday = moment().isSame(arg?.start, 'month');
      // 如果是当月就不执行此处
      !isClickToday && fullCalendarListMonth.value?.changeRightActiveDate(rightActiveDate.value);
    }
    isLeftActiveDateChange.value = false;
  }
};

const props = defineProps({
  openid: {
    type: String,
    default: '',
  },
  activeDate: {
    type: String,
    default: '',
  },
  promiseList: {
    type: Array,
    default: () => []
  },
  allConfig: {
    type: Object,
    default: () => { }
  },
  firstDayOfWeek: {
    type: Number,
    default: 0,
  },
  myCfg: {
    type: Object,
    default: () => { }
  }
});
const emits = defineEmits(['rightActiveDate', 'changeDetail']);

const innerTextDom = (type?) => {
  document.querySelectorAll('colgroup col')?.forEach(item => {
    item.setAttribute('style', 'width: 75px;');
  });
  document.querySelector('.fc-scrollgrid-section-header .fc-timegrid-axis .fc-timegrid-axis-frame').innerText = 'GMT+08';
};

const DrawTipTagsSpace = () => {
  const tagsArr = [];
  const currentViewType = fullCalendar.value?.calendar?.currentData?.currentViewType || '';
  document.querySelectorAll('.fc-timegrid-event-harness').forEach(t => {
    if (t.style.zIndex === '1') {
      tagsArr[tagsArr.length] = [t]
    } else {
      tagsArr[tagsArr.length - 1].push(t)
    }
  })
  tagsArr.forEach(arr => {
    if (arr.length > 1) {
      arr.forEach((tag, i) => {
        if (i > 0 && currentViewType === 'timeGridDay') {
          tag.style.left = 'calc(' + (100 * i / arr.length) + '% + 4px'
        }
        if (i < arr.length - 1 && currentViewType === 'timeGridWeek') {
          tag.style.width = 'calc(' + (100 / arr.length) + '% - 4px'
        }
      })
    }
  })
}

const dayPopoverFormat = (arg) => {
  let f = document.createElement('div')
  let w = document.createElement('span')
  let d = document.createElement('span')
  f.setAttribute('class', 'dayPopover_box')
  w.setAttribute('class', 'dayPopover_week')
  d.setAttribute('class', 'dayPopover_day')

  let week = moment(arg.date.marker).format('dddd')
  let day = moment(arg.date.marker).format('DD')
  w.innerHTML = week;
  d.innerHTML = day;

  f.appendChild(d)
  f.appendChild(w)
  let arrayOfDomNodes = [f]
  return `${day} ${week}`
}
const timeGridDay_dayHeaderContent = (arg) => {
  let f = document.createElement('div')
  let w = document.createElement('span')
  let d = document.createElement('span')
  let l = document.createElement('span')
  let today = document.createElement('span')
  f.setAttribute('class', 'timeGridDay_box')
  w.setAttribute('class', 'timeGridDay_week')
  d.setAttribute('class', 'timeGridDay_day')
  l.setAttribute('class', 'timeGridDay_day')
  today.setAttribute('class', 'timeGridDay_today')

  let week = moment(arg.date).format('dddd')
  let day = moment(arg.date).format('MM-DD')
  w.innerHTML = week;
  d.innerHTML = day;
  l.innerHTML = Lunar.fromDate(arg.date).getMonthInChinese() + '月' + Lunar.fromDate(arg.date).getDayInChinese();

  f.appendChild(w)
  if (arg?.isToday) {
    props.myCfg?.isShowLunar ? f.appendChild(l) : f.appendChild(d);
    // today.innerHTML = '今';
    // f.appendChild(today);
  } else {
    props.myCfg?.isShowLunar ? f.appendChild(l) : f.appendChild(d);
  }
  let arrayOfDomNodes = [f]
  return { domNodes: arrayOfDomNodes }
};

const timeGridWeek_dayHeaderContent = (arg) => {
  let f = document.createElement('div')
  let w = document.createElement('div')
  let d = document.createElement('span')
  let l = document.createElement('span')
  let today = document.createElement('span')
  f.setAttribute('class', 'timeGridWeek_box')
  w.setAttribute('class', 'timeGridWeek_week')
  d.setAttribute('class', 'timeGridWeek_day')
  l.setAttribute('class', 'timeGridWeek_day')
  today.setAttribute('class', 'timeGridWeek_today')

  let week = moment(arg.date).format('dddd')
  let day = moment(arg.date).format('DD')
  w.innerHTML = week
  d.innerHTML = day
  l.innerHTML = Lunar.fromDate(arg.date).getDayInChinese()

  f.appendChild(w)
  if (arg?.isToday) {
    today.innerHTML = '今';
    f.appendChild(today);
    props.myCfg?.isShowLunar ? f.appendChild(l) : f.appendChild(d);
  } else {
    f.appendChild(d);
    props.myCfg?.isShowLunar && f.appendChild(l);
  }
  let arrayOfDomNodes = [f]
  return { domNodes: arrayOfDomNodes }
};

const dayGridMonth_dayCellContent = (arg) => {
  let f = document.createElement('div')
  let d = document.createElement('span')
  let l = document.createElement('span')
  let today = document.createElement('span')
  f.setAttribute('class', 'dayGridMonth_box')
  d.setAttribute('class', 'dayGridMonth_day')
  l.setAttribute('class', 'dayGridMonth_day')
  today.setAttribute('class', 'dayGridMonth_today')

  let day = moment(arg.date).format('DD')
  d.innerHTML = day
  l.innerHTML = Lunar.fromDate(arg.date).getDayInChinese()

  if (arg?.isToday) {
    today.innerHTML = '今';
    f.appendChild(today);
    props.myCfg?.isShowLunar ? f.appendChild(l) : f.appendChild(d);
  } else {
    f.appendChild(d);
    props.myCfg?.isShowLunar && f.appendChild(l);
  }
  let arrayOfDomNodes = [f]
  return { domNodes: arrayOfDomNodes }
};

const titleFormat = ({ date }) => {
  // return `${moment(date).format('YYYY[年]MM[月]DD[日]')}  ${moment(date).isSame(moment(), 'day') ? '今天' : ''}`;
  return `${moment(date).format('YYYY[年]MM[月]DD[日]')}`;
}

const eventOrder = (event1, event2) => {
  // 默认开始时间start》时间范围end》本人owner》创建时间createAt
  if (event2.end - event1.end === 0) {
    if (event1.owner === myOpenid.value) {
      if (event2.owner === myOpenid.value) {
        return event2.createAt - event1.createAt;
      } else {
        return 1;
      }
    } else {
      if (event2.owner === myOpenid.value) {
        return -1;
      } else {
        // return event2.createAt - event1.createAt;
        /**
         * 如果根据创建时间排序-->应该是正序
         */
        return event1.createAt - event2.createAt;
      }
    }
  } else {
    // return event2.createAt - event1.createAt;
    /**
     * 如果根据创建时间排序-->应该是正序
     */
    return event1.createAt - event2.createAt;
  }
}

const moreLinkContent = (org) => {
  return `${t('zx.schedule.more')}${org.num}${t('zx.schedule.ge')}日程`;
}

const calendarOptions = reactive({
  plugins: [dayGridPlugin, interactionPlugin, timeGridPlugin, listPlugin, momentPlugin],
  initialView: 'timeGridDay', // 'dayGridMonth', 'dayGridWeek', 'timeGridDay', 'listWeek'
  titleFormat: 'YYYY[年]MM[月]',
  nowIndicator: false,
  nowIndicatorClassNames: 'nowIndicatorClassNames',
  nowIndicatorContent: moment().format('HH:mm'),
  moreLinkClassNames: 'moreLinkClassNames',
  moreLinkContent: moreLinkContent,
  views: {
    timeGridDay: {
      titleFormat: titleFormat,
      dayHeaderFormat: `dddd MM[月]DD[日]`,
      slotLabelFormat: 'HH:mm',
      dayMaxEventRows: 2,
      eventMinHeight: 30,
      slotEventOverlap: false,
      dayHeaderClassNames: 'timeGridDayHeader',
      dayPopoverFormat: dayPopoverFormat,
      dayHeaderContent: timeGridDay_dayHeaderContent,
      dayCellClassNames: 'timeGridDayCell'
    },
    timeGridWeek: {
      titleFormat: 'YYYY[年]MM[月]',
      dayHeaderFormat: `dddd DD[日]`,
      slotLabelFormat: 'HH:mm',
      dayMaxEventRows: 2,
      eventMinHeight: 30,
      dayHeaderClassNames: 'dayGridWeekHeader',
      dayPopoverFormat: dayPopoverFormat,
      dayHeaderContent: timeGridWeek_dayHeaderContent,
      dayCellClassNames: 'dayGridWeekCell'
    },
    dayGridMonth: {
      titleFormat: 'YYYY[年]MM[月]',
      dayHeaderFormat: (arg) => moment(arg.date).format('dddd'),
      dayMaxEventRows: 2,
      dayPopoverFormat: dayPopoverFormat,
      eventMinHeight: 30,
      dayCellContent: dayGridMonth_dayCellContent,
      dayHeaderClassNames: 'dayGridMonthHeader',
      dayCellClassNames: 'dayGridMonthCell'
    },
    listWeek: {
      // dayHeaderFormat: { year: 'numeric', month: '2-digit', day: '2-digit' }
    }
  },
  allDaySlot: false,
  customButtons: {
    my_timeGridDay: {
      text: '日',
      click: function () {
        document.getElementsByClassName('fc-my_timeGridDay-button')[0].classList.add('myActiveCustomButton')
        document.getElementsByClassName('fc-my_timeGridWeek-button')[0].classList.remove('myActiveCustomButton')
        document.getElementsByClassName('fc-my_dayGridMonth-button')[0].classList.remove('myActiveCustomButton')
        document.getElementsByClassName('fc-my_listWeek-button')[0].classList.remove('myActiveCustomButton')
        document.querySelector('.fullCalendar_schedule_right .fc-view-harness').setAttribute('style', 'display: block;')
        showList.value = false;
        fullCalendar.value?.calendar.changeView('timeGridDay');
        nextTick(() => {
          innerTextDom();
        })
      }
    },
    my_timeGridWeek: {
      text: '周',
      click: function () {
        document.getElementsByClassName('fc-my_timeGridDay-button')[0].classList.remove('myActiveCustomButton')
        document.getElementsByClassName('fc-my_timeGridWeek-button')[0].classList.add('myActiveCustomButton')
        document.getElementsByClassName('fc-my_dayGridMonth-button')[0].classList.remove('myActiveCustomButton')
        document.getElementsByClassName('fc-my_listWeek-button')[0].classList.remove('myActiveCustomButton')
        document.querySelector('.fullCalendar_schedule_right .fc-view-harness').setAttribute('style', 'display: block;')
        showList.value = false;
        fullCalendar.value?.calendar.changeView('timeGridWeek');
        nextTick(() => {
          innerTextDom('timeGridWeek');
        })
      }
    },
    my_dayGridMonth: {
      text: '月',
      click: function () {
        document.getElementsByClassName('fc-my_timeGridDay-button')[0].classList.remove('myActiveCustomButton')
        document.getElementsByClassName('fc-my_timeGridWeek-button')[0].classList.remove('myActiveCustomButton')
        document.getElementsByClassName('fc-my_dayGridMonth-button')[0].classList.add('myActiveCustomButton')
        document.getElementsByClassName('fc-my_listWeek-button')[0].classList.remove('myActiveCustomButton')
        document.querySelector('.fullCalendar_schedule_right .fc-view-harness').setAttribute('style', 'display: block;')
        showList.value = false;
        fullCalendar.value?.calendar.changeView('dayGridMonth');
      }
    },
    my_listWeek: {
      text: '列表',
      click: function () {
        document.getElementsByClassName('fc-my_timeGridDay-button')[0].classList.remove('myActiveCustomButton')
        document.getElementsByClassName('fc-my_timeGridWeek-button')[0].classList.remove('myActiveCustomButton')
        document.getElementsByClassName('fc-my_dayGridMonth-button')[0].classList.remove('myActiveCustomButton')
        document.getElementsByClassName('fc-my_listWeek-button')[0].classList.add('myActiveCustomButton')
        document.querySelector('.fullCalendar_schedule_right .fc-view-harness').setAttribute('style', 'display: none;')
        showList.value = true;
        // fullCalendar.value?.calendar.changeView('listDay');
        // fullCalendar.value?.calendar.changeView('listWeek');
        fullCalendar.value?.calendar.changeView('listMonth');
      }
    },
    my_today: {
      text: '今天',
      click: function () {
        // fullCalendar.value?.calendar.gotoDate(moment().format('YYYY-MM-DD'));
        isRightActiveDate.value = false;
        emits('rightActiveDate', { isClickToday: true, currentViewType: fullCalendar.value?.calendar.currentData.currentViewType });
        fullCalendar.value?.calendar.today();
      }
    }
  },
  headerToolbar: {
    left: 'my_today prev,next title',
    center: '',
    // right: 'timeGridDay,timeGridWeek,dayGridMonth,listMonth',
    right: 'my_timeGridDay,my_timeGridWeek,my_dayGridMonth,my_listWeek',
  },
  buttonText: {
    today: '今天',
    month: '月',
    week: '周',
    day: '日',
    list: '列表'
  },
  displayEventTime: false, // 隐藏日程时间显示
  firstDay: 0, // 每周第一天 0-6，周天-周六
  eventSources: [],
  // eventOrder: ['start', eventOrder], // 排序写法这个是错误的-->不生效
  eventOrder: eventOrder,
  eventOrderStrict: true,
  // eventMaxStack: 3, // 产品说不要暂时个数限制-->注释掉就没有限制(只针对日和周)
  eventClick: eventClick,
  eventMouseEnter: eventMouseEnter,
  eventMouseLeave: eventMouseLeave,
  dateClick: dateClick,
  datesSet: datesSet
})

const getAllListData = async (isHideShowData = false, searchTime = currentYearAndMonth.value) => {
  // calendarOptions.nowIndicatorContent = moment().format('HH:mm');
  if (isHideShowData) {
    listData.value = _transData(sourceListData.value);
    calendarOptions.eventSources = listData.value;
    return
  }
  const res = await Promise.all(props.promiseList?.filter(item => (item.user === myOpenid.value) || item.mark)?.map(item => {
    return getList(item.user, searchTime)
  }) || [])
  let arr = res.flat(Infinity);
  let activityList = [];
  try {
    // 当前这版不做共享人的活动展示，只查询自己的活动展示
    const resActivity = (await getCalendars()).data;
    if (zhixingStore.checkNet == 'good') {
      activityList = resActivity?.data?.activities?.map(item => {
        return {
          ...item,
          notices_matters_type: 'activity',
          activityId: item.id,
          title: item.subject,
          knockAt: moment.unix(item.duration?.startTime).valueOf(),
          start: moment.unix(item.duration?.startTime).valueOf(),
          end: moment.unix(item.duration?.endTime).valueOf(),
          done: formatStatus(item, 'activityTypeText'),
          owner: myOpenid.value,
        };
      });
      let list = saveList(activityList);//获取当月数据
      let res = await insertActivity({ content: JSON.stringify(list), current: moment().startOf('month').valueOf() });//将数据保存到本地
      arr = [...arr, ...activityList];
    }
  } catch (error) {
    if (zhixingStore.checkNet == 'bad') {//无网络
      let res = await queryActivity()//获取数据
      console.log(res.data,'右侧获取活动的缓存数据-----------------------');
      activityList = res?.data
      arr = [...arr, ...activityList];
    }
    console.log(error);
  }

  try {
    const resImportant = await getImportantDay();
    let ImportantList = [];
    if (resImportant?.status === 200 && zhixingStore.checkNet == 'good') {//网络状态良好
      ImportantList = resImportant?.data?.items?.map(item => {
        return {
          ...item,
          notices_matters_type: 'important',
          activityId: item.groupId,
          title: item.title,
          knockAt: moment.unix(item.when.timestamp).valueOf(),
          start: moment.unix(item.when.timestamp).valueOf(),
          end: moment.unix(item.when.timestamp + 5).valueOf(),
          done: item.countDown,
          owner: myOpenid.value,
        };
      });
      let list = saveList(ImportantList);//获取当月数据
      let a = await insertSubscribe({ content: JSON.stringify(list), current: moment().startOf('month').valueOf() });//将数据保存到本地
      arr = [...arr, ...ImportantList];
    }
  } catch (error) {
    if (zhixingStore.checkNet == 'bad') {//无网络
      let res = await querySubscribe()//获取数据
      arr = [...arr, ...res.data]
      console.log(res.data,'右侧获取订阅的缓存数据-----------------------');
    }
  }
  arr.sort((a, b) => {
    let sortTypeA = 'knockAt';
    let sortTypeB = 'knockAt';
    if (a.notices_matters_type === 'manifest') {
      sortTypeA = a.knockAt > 0 ? 'knockAt' : 'mission_at';
    }
    if (b.notices_matters_type === 'manifest') {
      sortTypeB = b.knockAt > 0 ? 'knockAt' : 'mission_at';
    }
    return a?.[sortTypeA] - b?.[sortTypeB];
  });
  sourceListData.value = arr;
  listData.value = _transData(sourceListData.value);
  // console.log(listData.value)
  calendarOptions.eventSources = listData.value;
  if (isFirst.value) {
    isFirst.value = false;
    setTimeout(() => {
      firstViewScrollToTime({ start: moment().startOf('days').valueOf(), end: moment().endOf('days').valueOf() });
    }, 1500);
  } else {
    nextTick(() => {
      firstViewScrollToTime({ start: moment().startOf('days').valueOf(), end: moment().endOf('days').valueOf() });
    })
  }
  nextTick(() => {
    DrawTipTagsSpace();
  })
}

const getList = async (user_id, month, keyword = '') => {
  // 日历最大显示42个日期格子
  let sort = {
    knocked: true,
    start: moment(month).add(-42, 'days').valueOf(),
    end: moment(month).add(42, 'days').valueOf(),
  };
  const paramsAll = {
    sid: '',
    size: 9999,
    notices_matters: {
      user_id,
      ...sort,
      title: keyword,
      not_done: null
    }
  }
  let resNoticesMatters = null
  let noticesList = [];
  if (zhixingStore.checkNet == 'good') {//网络状态良好
    resNoticesMatters = await getNoticesMatters(flattenObject(paramsAll));//获取当前数据
    noticesList = resNoticesMatters?.data.data?.notices || [];
    let list = saveList(noticesList);//获取当月数据
    let res = await insertRemind({ content: JSON.stringify(list), current: moment().startOf('month').valueOf() });//将数据保存到本地
  } else {//无网络
    let res = await queryRemind()//获取数据
    console.log(res.data,'右侧获取提醒的缓存数据-----------------------');
    noticesList = res?.data || [];
  }
  // 由于owner字段是后端返回的，所以需要重新赋值
  noticesList = noticesList.map(v => { return { ...v, owner: user_id } });
  noticesList.forEach(item => {
    item.notices_matters_type = 'remind';
    // 对别人共享过来的提醒数据进行标记
    if(user_id !== myOpenid.value) {
      item.sharePersenId = user_id;
    }
  });
  return noticesList
};

const _transData = (arr) => {
  let groupData = new Map();
  // todo 目前数据没有结束时间，默认提醒时间+30s
  arr?.forEach(item => {
    let eventsType = '';
    let itemTemp = {
      ...item,
      notices_matters_status: 'DETAIL',
      id: item.id || item.openid,
      title: item.title,
      start: moment(Number(item.knockAt)).valueOf(),
      end: moment(Number(item.knockAt)).add(30, 'seconds').valueOf(),
      className: 'eventsText'
    };
    if (item.notices_matters_type === 'manifest' || item.notices_matters_type === 'remind') {
      let finishAt = item?.mentionUsers?.some(user => {
        if(item.sharePersenId){
          return user.finishAt!== '0' && user.openid === item.sharePersenId;
        }
        return user.finishAt !== '0' && user.openid === myOpenid.value;
      });
      let titleTag = finishAt ? '[已结束]' : '';
      itemTemp.title = `${titleTag}${item.title}`;
      itemTemp.className = titleTag ? 'eventsText eventsTextTitleTag' : 'eventsText';
    } else if (item.notices_matters_type === 'activity') {
      itemTemp.title = `[${item.done}]${item.title}`;
      itemTemp.start = item.start;
      itemTemp.end = item.end;
      itemTemp.className = ['canceled', 'ended'].includes(formatStatus(item, 'activityTypeClass')) ? 'eventsText eventsTextTitleTag' : 'eventsText';
    }
    if (item?.owner === myOpenid.value) {
      eventsType = item.notices_matters_type;
      if (item.notices_matters_type === 'important') {
        let titleTag = item.done < 0 ? '[已过期]' : '';
        itemTemp.title = `${titleTag}${item.title}`;
        itemTemp.className = titleTag ? 'eventsText eventsTextTitleTag' : 'eventsText';
        eventsType = 'important_' + item.groupId;
      }
    } else {
      if (item.notices_matters_type === 'remind' && item?.owner === getOpenid()) {
        eventsType = item.notices_matters_type;
      } else {
        let notices_matters_status = props.allConfig?.['other']?.find(it => it.cardId === item.owner)?.notices_matters_status;
        eventsType = item.owner;
        itemTemp.notices_matters_status = notices_matters_status?.[item.notices_matters_type];
        if (['STATUS'].includes(itemTemp.notices_matters_status)) itemTemp.title = '******';
      }

    }
    if (!groupData.has(eventsType)) {
      groupData.set(eventsType, [itemTemp])
    } else {
      groupData.get(eventsType).push(itemTemp)
    }
  })
  // console.log(groupData,'groupData------------------------')
  const keys = [];
  const values = [];
  for (const [key, value] of groupData) {
    let events = {
      events: value,
      className: ''
    }
    let isShow = true
    if (key === 'manifest') {
      events.className = props.allConfig?.[key]?.className
      isShow = props.allConfig?.[key]?.mark
    } else if (key === 'remind') {
      events.className = props.allConfig?.[key]?.className
      isShow = props.allConfig?.[key]?.mark
    } else if (key === 'activity') {
      events.className = props.allConfig?.[key]?.className
      isShow = props.allConfig?.[key]?.mark
    } else if (key === 'important') {
      events.className = props.allConfig?.[key]?.find(it => it.cardId === key)?.className
      isShow = props.allConfig?.[key]?.find(it => it.cardId === key)?.mark
    } else {
      events.className = props.allConfig?.['other']?.find(it => it.cardId === key)?.className
      isShow = props.allConfig?.['other']?.find(it => it.cardId === key)?.mark
      if (key.indexOf('important_') !== -1) {
        events.className = props.allConfig?.['important']?.find(it => it.cardId === key.split('_')[1])?.className
        isShow = props.allConfig?.['important']?.find(it => it.cardId === key.split('_')[1])?.mark
      }

    }
    keys.push(key);
    if (isShow) {
      values.push(events);
    }
  }
  return values
}

// 跳转视图位置
const viewScrollToTime = (time = '08:00') => {
  nextTick(() => {
    fullCalendar.value?.calendar.scrollToTime(time)
  })
}

const firstViewScrollToTime = (arg) => {
  // 以当前时间为基准滚动视图位置
  let time = moment().format('HH:mm');
  if (moment().hour() > 4) {
    time = moment().add(-3, 'hour').format('HH:mm');
  }
  viewScrollToTime(time);
}

const changeClassDom = (val) => {
  let currentViewType = fullCalendar.value?.calendar.currentData.currentViewType;
  if (currentViewType === "timeGridWeek") {
    document.querySelectorAll(`.fullCalendar-box .dayGridWeekHeader[data-date]`)?.forEach(item => {
      item.classList.remove('dayGridMonth_activeDay');
    });
    document.querySelectorAll(`.fullCalendar-box .dayGridWeekCell[data-date]`)?.forEach(item => {
      item.classList.remove('dayGridMonth_activeDay');
    });
    const elH = document.querySelector(`.fullCalendar-box .dayGridWeekHeader[data-date="${val}"]`);
    const elC = document.querySelector(`.fullCalendar-box .dayGridWeekCell[data-date="${val}"]`);
    elH && elH.classList.add('dayGridMonth_activeDay');
    elC && elC.classList.add('dayGridMonth_activeDay');
  } else if (currentViewType === "dayGridMonth") {
    document.querySelectorAll(`.fullCalendar-box .dayGridMonthCell[data-date]`)?.forEach(item => {
      item.classList.remove('dayGridMonth_activeDay');
    });
    const el = document.querySelector(`.fullCalendar-box .dayGridMonthCell[data-date="${val}"]`);
    el && el.classList.add('dayGridMonth_activeDay');
  }
}

// 刷新列表数据
const refreshListData = () => {
  fullCalendarListMonth.value?.getAllListData();
}

defineExpose({
  getAllListData,
  refreshListData
})

onMounted(() => {
  nextTick(() => {
    document.getElementsByClassName('fc-my_timeGridDay-button')[0].classList.add('myActiveCustomButton');
    innerTextDom();
    calendarOptions.customButtons.my_listWeek.click();
  })
})

watch(() => props.activeDate, async (val) => {
  if (val) {
    currentYearAndMonth.value = val;
    await getAllListData();
    isLeftActiveDateChange.value = true;
    isRightActiveDate.value = false;
    fullCalendar.value?.calendar.gotoDate(val);
    leftActiveDate.value = val;
    fullCalendarListMonth.value?.changeLeftActiveDate(leftActiveDate.value);
    changeClassDom(val);
    // 下一个滚动
    nextTick(()=>{
      // fullCalendarListMonth.value?.positionScroll(val);
    })
  }
})

watch(() => props.promiseList, (val) => {
  if (val) {
    getAllListData()
  }
})

watch(() => props.allConfig, (val) => {
  if (val) {
    getAllListData()
  }
})

watch(() => props.firstDayOfWeek, (val) => {
  calendarOptions.firstDay = Number(val)
})


</script>

<style lang="less" scoped>
::-webkit-scrollbar {
  width: 0;
}

.fullCalendar-box,
.fullCalendar {
  height: 100%;
  overflow: auto;
}

:deep(.fc-header-toolbar) {
  padding: 16px;

  .fc-toolbar-chunk {
    display: flex;
    align-items: center;
  }

  .fc-toolbar-chunk:nth-child(1) {

    .fc-my_today-button,
    .fc-today-button {
      display: flex;
      height: 36px;
      padding: 0px 12px;
      align-items: center;
      gap: 8px;
      border-radius: 4px;
      border: 1px solid var(--border-kyy-color-border-default, #D5DBE4);
      color: var(--text-kyy-color-text-2, #516082);
      background-color: #FFFFFF;
      box-shadow: none;

      /* kyy_fontSize_2/regular */
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      /* 157.143% */
    }

    .fc-button-group {
      display: flex;
      height: 36px;
      padding: 0px 12px;
      justify-content: center;
      align-items: center;
      border-radius: 4px;
      background-color: #FFFFFF;

      .fc-prev-button,
      .fc-next-button {
        height: 100%;
        padding: 0.4em 0.85em;
        box-shadow: none;
        background-color: #FFFFFF;
        border: 1px solid var(--border-kyy-color-border-default, #D5DBE4);

        .fc-icon {
          color: #828DA5;
          font-size: 10px;
          font-weight: 600;
          line-height: 7px;
        }
      }

      .fc-prev-button {
        border-right: none;
      }

      .fc-prev-button:after {
        content: '';
        width: 2px;
        height: 12px;
        background: var(--divider-kyy-color-divider-deep, #D5DBE4);
        position: absolute;
        right: 0;
        top: 0;
        bottom: 0;
        margin: auto;
      }

      .fc-next-button {
        border-left: none;
      }

    }

    .fc-toolbar-title {
      color: var(--text-kyy-color-text-1, #1A2139);

      /* kyy_fontSize_2/bold */
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: 22px;
      /* 157.143% */
    }
  }

  .fc-toolbar-chunk:nth-child(3) {
    .fc-button-group {
      display: inline-flex;
      height: 36px;
      min-height: 36px;
      max-height: 36px;
      padding: 4px;
      align-items: center;
      gap: 2px;
      flex-shrink: 0;
      border-radius: var(--radius-kyy-radius-button-s, 4px);
      border: 1px solid var(--color-button-tab-kyy-color-button-tab-border, #D5DBE4);
      background: var(--color-button-tab-kyy-color-button-tab-bg, #FFF);

      .fc-button {
        display: flex;
        height: 28px;
        min-width: 56px;
        min-height: 28px;
        max-height: 28px;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 10px;

        color: var(--color-button-tab-kyy-color-button-tab-item-text-default, #516082);
        font-feature-settings: 'clig' off, 'liga' off;
        font-family: PingFang SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;

        background-color: #FFFFFF;
        border-color: #FFFFFF;
        box-shadow: none;
      }

      .fc-button-active {
        border-radius: var(--radius-kyy-radius-button-xs, 2px);
        background: var(--color-button-tab-kyy-color-button-tab-item-bg-active, #DBDFFF);
        color: var(--color-button-tab-kyy-color-button-tab-item-text-active, #4D5EFF);
        font-feature-settings: 'clig' off, 'liga' off;
        font-family: PingFang SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
      }

      .myActiveCustomButton {
        border-radius: var(--radius-kyy-radius-button-xs, 2px);
        background: var(--color-button-tab-kyy-color-button-tab-item-bg-active, #DBDFFF);
        color: var(--color-button-tab-kyy-color-button-tab-item-text-active, #4D5EFF);
        font-feature-settings: 'clig' off, 'liga' off;
        font-family: PingFang SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
      }

    }
  }
}

:deep(.fullCalendar-box) {

  .fc-dayGridMonth-view .fc-daygrid-day-events {
    height: 65px;

    .fc-event-title {
      font-size: 14px;
      font-weight: 700;
      white-space: nowrap; /* 不换行 */
      overflow: hidden; /* 超出部分隐藏 */
      text-overflow: ellipsis; /* 溢出部分显示省略号 */
    }
  }

  .fc-timegrid-axis-frame {
    justify-content: flex-start;
  }

  .fc-direction-ltr .fc-timegrid-slot-label-frame {
    text-align: center;

    .fc-timegrid-slot-label-cushion {
      color: var(--text-kyy-color-text-5, #ACB3C0);
      text-align: right;

      /* kyy_fontSize_1/regular */
      font-family: PingFang SC;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      /* 166.667% */
    }
  }

  .fc .fc-timegrid-slot {
    height: 30px;
    transform: translateY(-14px);
  }

  .eventsTextTitleTag {
    opacity: 0.75;
  }

  .important {
    background-color: rgb(110, 190, 94);
    border-left: 4px solid #4D5EFF;
    background: var(--tag-bg-kyy-color-tag-bg-brand, #EAECFF)
  }

  .fc-timegrid-now-indicator-arrow.nowIndicatorClassNames {
    margin-right: 15px;
    display: inline-flex;
    height: 20px;
    padding: var(--checkbox-kyy-radius-checkbox, 2px) 4px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    flex-shrink: 0;
    border-radius: 100px;
    background: var(--tag-bg-kyy-color-tag-bg-magenta, #FFE3F1);
    color: var(--magenta-kyy-color-magenta-default, #FF4AA1);
    text-align: right;
    font-feature-settings: 'clig' off, 'liga' off;
    font-family: PingFang SC;
    font-size: 12px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
    border-color: transparent;
    border-width: 0;
    left: auto;
    right: 2px;
  }

  .fc-timegrid-now-indicator-line.nowIndicatorClassNames {
    border-top: 2px solid var(--magenta-kyy-color-magenta-disabled, #FFACD4);
    color: transparent;
    //.nowIndicatorClassNamesSon{
    //  width: 12px;
    //  height: 12px;
    //  background-color: var(--magenta-kyy-color-magenta-default, #FF4AA1);
    //  border-radius: 50%;
    //  position: absolute;
    //  right: 0;
    //  left: 0;
    //  top: -24px;
    //  bottom: 0;
    //  margin: auto;
    //}
  }

  //.fc-timeGridWeek-view .fc-timegrid-now-indicator-container{
  //  position: static;
  //  .fc-timegrid-now-indicator-line.nowIndicatorClassNames{
  //    border-top: 2px solid var(--magenta-kyy-color-magenta-disabled, #FFACD4);
  //    color: transparent;
  //    width: calc(100% * 7 + 30px);
  //    .nowIndicatorClassNamesSon{
  //      width: 12px;
  //      height: 12px;
  //      background-color: var(--magenta-kyy-color-magenta-default, #FF4AA1);
  //      border-radius: 50%;
  //      position: absolute;
  //      right: 0;
  //      left: 0;
  //      top: -24px;
  //      bottom: 0;
  //      margin: auto;
  //    }
  //  }
  //}
  .moreLinkClassNames {
    bottom: auto !important;
    color: var(--text-kyy-color-text-2, #516082);
    font-family: PingFang SC;
    font-style: normal;
    font-weight: 600;
    line-height: 22px;
  }

  .dayGridMonth_activeDay {
    background-color: #f0f8ff;
  }

  .fc-more-popover {
    padding: 20px;
    border-radius: 16px;
    background: var(--bg-kyy-color-bg-default, #FFF);
    box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.12);

    .fc-popover-header {
      padding-bottom: 15px;
      background: transparent;

      .fc-popover-title {
        color: var(--text-kyy-color-text-1, #1A2139);
        font-feature-settings: 'clig' off, 'liga' off;
        font-family: PingFang SC;
        font-size: 20px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
      }
    }

    .fc-popover-body {
      overflow: auto;
      max-height: 200px;
      padding: 0;

      .fc-more-popover-misc {
        display: none;
      }
    }
  }

  .fc-view {
    margin: 0 10px 10px 16px;
  }

  .fc-timeGridDay-view {
    .fc-timegrid-axis {
      vertical-align: middle;
    }

    .fc-day-today {
      background: #FFFFFF;
    }

    .fc-col-header-cell {
      text-align: left;

      .fc-col-header-cell-cushion {
        color: var(--text-kyy-color-text-1, #1A2139);

        /* kyy_fontSize_2/bold */
        font-family: PingFang SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 22px;
        /* 157.143% */
      }
    }

    .timeGridDayHeader {}

    .timeGridDayCell {}

    .timeGridDay_box {
      text-align: left;
      margin-left: 12px;
    }

    .timeGridDay_week {
      color: var(--text-kyy-color-text-1, #1A2139);

      /* kyy_fontSize_2/bold */
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: 22px;
      /* 157.143% */
    }

    .timeGridDay_day {
      margin-right: 15px;
      margin-left: 15px;
      display: inline-block;
      color: var(--text-kyy-color-text-2, #516082);

      /* kyy_fontSize_1/bold */
      font-family: PingFang SC;
      font-size: 12px;
      font-style: normal;
      font-weight: 500;
      line-height: 20px;
      /* 166.667% */
    }

    .timeGridDay_today {
      display: inline-block;
      border-radius: 50%;
      padding: 0 4px;
      border: 1px solid var(--brand-kyy-color-brand-default, #4D5EFF);
      color: var(--brand-kyy-color-brand-default, #4D5EFF);
      text-align: center;

      /* kyy_fontSize_2/bold */
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: 22px;
      /* 157.143% */
    }
  }

  .fc-timeGridWeek-view {
    .fc-day-past .timeGridWeek_box {
      opacity: 0.6;
    }

    .fc-timegrid-axis {
      vertical-align: middle;
    }

    .fc-day-today {
      background: var(--bg-kyy-color-bg-list-hover, #F3F6FA);
    }

    .fc-col-header-cell {
      text-align: left;

      .fc-col-header-cell-cushion {
        color: var(--text-kyy-color-text-2, #516082);
        text-align: center;

        /* kyy_fontSize_4/regular */
        font-family: PingFang SC;
        font-size: 18px;
        font-style: normal;
        font-weight: 400;
        line-height: 26px;
        /* 144.444% */
      }
    }

    .dayGridWeekHeader {
      word-break: keep-all;
    }

    .dayGridWeekCell {}

    .timeGridWeek_box {
      text-align: left;
    }

    .timeGridWeek_week {
      color: var(--text-kyy-color-text-2, #516082);
      //text-align: center;

      /* kyy_fontSize_4/regular */
      font-family: PingFang SC;
      font-size: 18px;
      font-style: normal;
      font-weight: 400;
      line-height: 26px;
      /* 144.444% */
    }

    .timeGridWeek_day {
      margin-right: 15px;
      display: inline-block;
      color: var(--text-kyy-color-text-2, #516082);
      text-align: center;

      /* kyy_fontSize_2/regular */
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      /* 157.143% */
    }

    .timeGridWeek_today {
      margin-right: 15px;
      display: inline-block;
      border-radius: 50%;
      padding: 0 4px;
      border: 1px solid var(--brand-kyy-color-brand-default, #4D5EFF);
      color: var(--brand-kyy-color-brand-default, #4D5EFF);
      text-align: center;

      /* kyy_fontSize_2/bold */
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: 22px;
      /* 157.143% */
    }
  }

  .fc-dayGridMonth-view {
    .fc-day-other .fc-daygrid-day-top {
      opacity: 1;

      .dayGridMonth_day:nth-child(1) {
        opacity: 0.3;
      }
    }

    .fc-day-today {
      background: var(--bg-kyy-color-bg-list-hover, #F3F6FA);
    }

    .fc-col-header-cell {
      text-align: left;

      .fc-col-header-cell-cushion {
        color: var(--text-kyy-color-text-1, #1A2139);
        text-align: center;

        /* kyy_fontSize_2/regular */
        font-family: PingFang SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        /* 157.143% */
      }
    }

    .dayGridMonthHeader {}

    .dayGridMonthCell {
      .fc-daygrid-day-top {
        flex-direction: column;
      }
    }

    .dayGridMonth_box {
      text-align: left;
    }

    .dayGridMonth_day {
      margin-right: 15px;
      display: inline-block;
      color: var(--text-kyy-color-text-1, #1A2139);
      text-align: center;

      /* kyy_fontSize_2/bold */
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: 22px;
      /* 157.143% */
    }

    .dayGridMonth_day:nth-child(2) {
      color: var(--text-kyy-color-text-5, #ACB3C0);

      /* kyy_fontSize_2/bold */
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: 22px;
      /* 157.143% */
    }

    .dayGridMonth_today {
      margin-right: 15px;
      display: inline-block;
      border-radius: 50%;
      padding: 0 4px;
      border: 1px solid var(--brand-kyy-color-brand-default, #4D5EFF);
      color: var(--brand-kyy-color-brand-default, #4D5EFF);
      text-align: center;

      /* kyy_fontSize_2/bold */
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: 22px;
      /* 157.143% */
    }

    td,
    th {
      border-left: none;
      border-right: none;
    }
  }

  .fc-theme-standard td,
  .fc-theme-standard th {
    //border: 1px solid var(--divider-kyy-color-divider-light, #ECEFF5);
    border-color: var(--divider-kyy-color-divider-light, #ECEFF5);
  }

  .fc .fc-scrollgrid {
    border: none;
  }

  .fc .fc-timegrid-slot-label {
    border: none;
  }

  .fc .fc-timegrid-slot-minor {
    border-top-style: none;
  }

  .eventSources_1 {
    border: none;
    border-left: 4px solid var(--blue-kyy-color-blue-default, #4093E0);
    background: var(--tag-bg-kyy-color-tag-bg-blue, #E8F0FB);

    .fc-event-main {
      padding: 3px 12px;
      color: var(--blue-kyy-color-blue-default, #4093E0);
    }
  }

  .eventSources_2 {
    border: none;
    border-left: 4px solid var(--warning-kyy-color-warning-default, #FC7C14);
    background: var(--tag-bg-kyy-color-tag-bg-warning, #FFE5D1);

    .fc-event-main {
      padding: 3px 12px;
      color: var(--warning-kyy-color-warning-default, #FC7C14);
    }
  }
}

:deep(.fc-timegrid-col-events) {
  z-index: 10;

  .fc-event-title {
    font-size: 14px;
    font-weight: 700;
  }
}

:deep(.fc-daygrid-dot-event):hover {
  border-radius: 4px;
  // border-left: 4px solid var(--brand-kyy_color_brand_default, #4D5EFF);
  // background: var(--tagBG-kyy_color_tagBg_brand, #EAECFF);
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.12) !important;
}

/deep/ .fc-daygrid-dot-event:hover {
  background: unset;
}

/deep/ .fc-theme-standard .fc-popover {
  border: none;
}
</style>
