<template>
  <div class="waterfall-box" v-if="noticeListData?.length > 0">
    <div class="top topNotice">
      <div class="title">
        <img src="@/assets/fengcai/elegance.svg" />
        <span>{{ t("banch.ptfc") }}</span>
        <!-- <img src="@/assets/bench/icon_arrow_right.svg" /> -->
      </div>
      <!-- v-if="authority?.manage_auth" -->
      <div v-show="noticeListData && noticeListData?.length > 0" class="more cursor" @click="goTabNew">
        {{ $t("member.winter_column.statusOptions_1")
        }}<iconpark-icon name="iconarrowright" class="icon"></iconpark-icon>
      </div>
    </div>
    <div class="notice-item-body" v-if="noticeListData && noticeListData?.length > 0">
      <div class="notice-item-list">
        <div class="notice-item" v-for="(item, index) in noticeListData" :key="index"
          @click.stop="jumpColumn(item, false)">
          <div>
            <div style="display: flex; margin-bottom: 4px">
              <img :src="item.cover"
                style="margin-right: 12px; width: 96px; height: 72px; border-radius: 8px; object-fit: cover" />
              <div style="display: flex; flex-direction: column; justify-content: space-between">
                <div class="title-text">{{ item.title }}</div>
                <div style="display: flex; align-items: center; justify-content: space-between">
                  <div class="tag-item">
                    <div class="jumplink" @click.stop="jumpColumn(item, true)">#{{ item.column.column_title }}</div>
                    <div class="lin"></div>
                    <div class="mumber">{{ item.author }}</div>
                  </div>
                  <div class="times-fengcai">{{ dayjs(item.show_at * 1000).format("YYYY-MM-DD HH:mm") }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="notice-item-body" v-else>
      <div class="default">
        <!-- <img src="@/assets/bench/Rectangle.svg" /> -->
        <!-- <div>{{t('activity.announcement.noData')}}</div> -->
        <template v-if="proxy.$i18n.locale === 'zh-cn'">
          <img src="@/assets/member/icon/notice_cn.jpg" />
        </template>
        <template v-else>
          <img src="@/assets/member/icon/notice_hk.jpg" />
        </template>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
  import { ref, onMounted, computed, watch, nextTick, onUnmounted, toRaw, getCurrentInstance } from "vue";
  import { AddIcon } from "tdesign-icons-vue-next";
  import { useRoute, useRouter } from "vue-router";
  import { useI18n } from "vue-i18n";
  import { onMountedOrActivated } from "@renderer/hooks/onMountedOrActivated";
  import ggao from "@/assets/svg/ggaopt.svg";
  import fengcaisvg from "@/assets/fengcai/elegance.svg";
  import { platformViewList, platformColumnsList } from "@/api/fengcai/manage";
  import dayjs from "dayjs";
  import { MessagePlugin } from "tdesign-vue-next";

  import useRouterHelper from "@renderer/views/square/hooks/routerHelper";
  import { useDigitalPlatformStore } from "@renderer/views/digital-platform/store/digital-platform-store";
  import { getSrcThumbnail, getSrcLogo } from "@renderer/views/message/service/msgUtils";

  const digitalPlatformStore = useDigitalPlatformStore();
  const digitalRouter = useRouterHelper("digitalPlatformIndex");
  const { proxy } = getCurrentInstance() as any;
  const { t } = useI18n();
  const router = useRouter();
  const is100W = ref(false);
  const props = defineProps({
    DataType: {
      type: Number,
      default: 3,
    },
    authority: {
      type: Object,
    },
    activationGroupItem: {
      type: Object,
    },
    allLayoutList: {
      type: Array,
    },
  });
  const emits = defineEmits(["setWorkBenchTabItem"]);
  const noticeListData = ref(null);
  // watch(
  //   () => props.activationGroupItem.teamId,
  //   (newValue) => {
  //     getList();
  //   },
  // );
  let jumpflag = false;
  const jumpColumn = async (column, flag) => {
    console.log(column, 'columncolumncolumn');
    console.log(flag, 'asdasdasdasd');
    console.log(jumpflag, 'asdasdasdasdjumpflag');

    if (jumpflag) {
      return;
    }
    let query = {
      id: column.id,
      isClose: true,
      notTab: !flag,
      team_id: props.activationGroupItem?.teamId,
    };
    let path_url = "fengcai_adDetails";
    let route_url = `/digitalPlatformIndex/fengcai_adDetails?id=${column.id}&team_id=${props.activationGroupItem?.teamId}`;
    if (flag) {
      jumpflag = true;
      query.column_id = column?.column.column_id;
      route_url = `/digitalPlatformIndex/digital_platform_fengcai?id=${column.id}&team_id=${props.activationGroupItem?.teamId}`;
      path_url = "digital_platform_fengcai";
      try {
        let teamid = ''
        if (route.path.includes("/square-fengcai")) {
          teamid = props.activationGroupItem?.teamId
        } else if (route.path.includes("/workBenchIndex")) {
          teamid = localStorage.getItem('workBenchTeamid')
        } else {
          teamid = localStorage.getItem('digital_platform_teamid')
        }
        const res = await platformColumnsList(teamid, 2);
        jumpflag = false;
        console.log(res.data.data.list, 'asdasdasdasd333');
        console.log(column.id, 'asdasdasdasd333');
        let arr = res.data.data.list.map(e => e.id)
        console.log(arr, 'arrrr');
        console.log(column?.column?.column_id, 'arrrrcolumn?.column?.column_id');
        //  [37, 42, 57].includes(37)
        if (!arr.includes(column?.column?.column_id)) {
          console.log('走进来了,asdasdas');
          // "当前专栏已删除,请刷新"
          MessagePlugin.error(t("banch.dqzlysc"));
          return;
        }
      } catch (error) {
        return;

        jumpflag = false;
      }
    }
    jumpflag = false
    console.log(column, "columncolumncolumn");
    router.push({
      path: path_url,
      query: query,
    });
    digitalPlatformStore.addTab(
      toRaw({
        path: path_url,
        fullPath: route_url,
        name: "noticeDetailRead",
        title: flag ? t("banch.ptfc") : column.title,
        icon: "fengcai",
        isClose: true,

        query: {
          id: column.id,
          title: column.title,
          notTab: true,
          isClose: true,

          team_id: props.activationGroupItem?.teamId,
        },
      }),
      path_url === "fengcai_adDetails",
    );
    // path_url === "fengcai_adDetails"
  };
  const getTipText = (row) => {
    let text = "";
    try {
      if (row.content) {
        const content = JSON.parse(row.content);
        for (const item of content) {
          if (item.insert !== "\n" && typeof item.insert !== "object") {
            text += item.insert;
          }
          if (typeof item.insert === "object" && item.insert.image) {
            text += `[图片]`;
          }
        }
      } else {
        text = row.title;
      }
    } catch (err) {
      text = row.title;
    }
    return text;
  };
  const setWorkBenchTabItem = (val) => {
    emits("setWorkBenchTabItem", val);
  };
  const goTabNew = () => {
    // router.push({
    //   path: `/workBenchIndex/noticeDetailRead?id=${item.receive_id}&teamId=${props.activationGroupItem.teamId}`,
    //   query: {
    //     noticeId: item.receive_id,
    //     id: item.receive_id,
    //     title: item.title,
    //     teamId: props.activationGroupItem.teamId,
    //   },
    // });

    const tabs = {
      label: t("banch.ptfc"),
      icon: fengcaisvg,
      value: "digital_platform_fengcai",
      page: "digital_platform_fengcai",
      uuid: "digital_platform_fengcai",
    };
    const searchMenu = digitalRouter.routeList.find((v) => v.name.includes(tabs.page));
    router.push({ path: searchMenu.fullPath, query: {} });
    digitalPlatformStore.addTab(toRaw(searchMenu));
  };

  const getList = () => {
    console.log(props.DataType, "ressssssssss");
    platformViewList(
      {
        channel: 2,
        page: 1,
        no_read: 1,
        pageSize: 4,
        team_id: props.activationGroupItem?.teamId,
      },
      props.activationGroupItem?.teamId,
    ).then((res) => {
      noticeListData.value = res.data.data.list?.map((item) => {
        if (item.cover) {
          item.cover = getSrcThumbnail(item.cover)
        }
        return item;
      });
      console.log(res, "ressssssssss");
    });
  };
  onMountedOrActivated(() => {
    getList();
  });
  const toSetUp = () => {
    router.push(`/workBenchIndex/noticeCreate`);
  };
</script>
<style lang="less" scoped>
  .times-fengcai {
    color: var(--text-kyy_color_text_5, #ACB3C0);
    text-align: right;

    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    /* 166.667% */
  }

  // lss 加个滚动条样式
  @import "@renderer/views/member/member_number/panel/member-panel/less/home-panel.less";

  .topNotice {
    background: linear-gradient(111deg, #fcdbf5 0.63%, rgba(252, 219, 245, 0.3) 28.93%) !important;
  }

  .default {
    width: 560px;
    height: 172.949px;

    img {
      width: inherit;
      // height: 172.949px;
      border-radius: 8px;
    }
  }

  .notice-item:first-child {
    /* padding-top: 0 !important; */
  }

  .head-item-icon:hover {
    color: #707eff;
  }

  .head-item-icon {
    cursor: pointer;
    display: flex;
    align-items: center;
    color: #516082;
    text-align: center;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;

    /* 157.143% */
    .icon-img {
      font-size: 20px;
      width: 20px;
      height: 20px;
      margin-right: 4px;
    }
  }

  .user-name {
    display: flex;
    align-items: center;
    justify-content: start;
    margin-top: 4px;
    color: var(--text-kyy_color_text_5, #acb3c0);
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    gap: 12px;
    line-height: 20px;

    /* 166.667% */
    .time {
      width: 106px;
      flex: none;
    }

    .line {
      width: 1px;
      height: 12px;

      background: var(--divider-kyy_color_divider_light, #eceff5);
    }
  }

  .dian {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    width: 100%;
    word-break: break-all;
  }

  .notice-content {
    color: var(--text-kyy_color_text_3, #828da5);
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    /* 157.143% */
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    width: 100%;
    word-break: break-all;
  }

  .notice-item::after {
    content: "";
    height: 1px;
    width: 100%;
    background: #eceff5;
    position: absolute;
    bottom: -5px;
    left: 0;
  }

  .nodata {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--text-kyy_color_text_2, #516082);
    text-align: center;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;

    line-height: 26px;

    /* 152.941% */
    img {
      width: 200px;
      height: 200px;
    }
  }

  .notice-item:hover {
    cursor: pointer;
    background: var(--bg-kyy_color_bg_list_hover, #f3f6fa);
    transition: all 0.25s linear;
  }

  .notice-item-text {
    color: var(--text-kyy_color_text_1, #1a2139);
    font-size: 16px;
    font-style: normal;
    padding-left: 8px;
    font-weight: 600;
    line-height: 24px;
    /* 150% */
    display: flex;
    height: 40px;
    width: 104px;
    gap: 8px;
    align-items: center;

    img {
      width: 20px;
      height: 20px;
    }
  }

  .notice-item-text:hover {
    border-radius: 8px;
    background: var(--bg-kyy_color_bg_list_hover, #f3f6fa);
    cursor: pointer;
  }

  .head-item-text {
    color: #1a2139;
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
    /* 150% */
    position: relative;
    margin-left: 8px;
    margin-bottom: 12px;
  }

  .notice-box {
    // background-color: #fff;
    // border-radius: 8px;
    // padding: 8px;
    height: 432px;
    /* height: fit-content; */
    position: relative;
  }

  .notice-item:last-child {
    margin-bottom: 0 !important;
  }

  .notice-item:last-child::after {
    display: none;
  }

  .notice-item:first-child::after {
    display: inline-block;
  }

  .notice-item-body {
    padding: 8px;
  }

  .notice-item-list {
    .notice-item {
      margin-bottom: 9px;
      padding: 8px;
      border-radius: 8px;
      padding: 8px;
      cursor: pointer;
      position: relative;
      border-radius: 8px;
      transition: all 0.25s linear;

      .notice-title {
        display: flex;

        span {
          color: var(--text-kyy_color_text_1, #1a2139);
          font-size: 14px;
          font-style: normal;
          font-weight: 600;
          line-height: 22px;
          /* 157.143% */
        }
      }

      .status {
        display: flex;
        height: 20px;
        min-height: 20px;
        max-height: 20px;
        padding: 1px 8px;
        justify-content: center;
        align-items: center;
        margin-right: 4px;
        color: var(--kyy_color_tag_text_warning, #fc7c14);
        text-align: right;
        font-size: 12px;
        font-weight: 400;
        border-radius: var(--kyy_radius_tag_full, 999px);
        background: var(--kyy_color_tag_bg_warning, #ffe5d1);
        line-height: 20px;
        /* 166.667% */
      }

      .tag {
        display: flex;
        height: 20px;
        min-height: 20px;
        margin-right: 4px;
        max-height: 20px;
        padding: 0px 4px;
        justify-content: center;
        align-items: center;
        color: var(--kyy_color_tag_text_kyyBlue, #21acfa);
        text-align: center;
        border-radius: var(--kyy_radius_tag_s, 4px);
        background: var(--kyy_color_tag_bg_kyyBlue, #e4f5fe);
        font-size: 12px;
        font-weight: 400;
        line-height: 20px;
        /* 166.667% */
      }
    }
  }

  .title-text {
    color: var(--text-kyy_color_text_1, #1a2139);
    text-overflow: ellipsis;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    width: 435px;
    height: 44px;
  }

  .tag-item {
    display: flex;
    align-items: center;
  }

  .lin {
    width: 1px;
    height: 12px;
    margin: 0 12px;
    background: var(--divider-kyy_color_divider_light, #eceff5);
  }

  .mumber {
    color: var(--text-kyy_color_text_5, #acb3c0);
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    /* 166.667% */
  }

  .jumplink {
    color: var(--brand-kyy_color_brand_default, #4d5eff);
    font-size: 12px;
    font-style: normal;
    cursor: pointer;
    font-weight: 400;
    line-height: 20px;
    /* 166.667% */
  }
</style>