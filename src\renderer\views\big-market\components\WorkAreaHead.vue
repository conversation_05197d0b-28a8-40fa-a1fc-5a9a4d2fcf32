<template>
  <div class="head-box">
    <icon v-if="!hidGroup" class="icon-specials" :url="iconUrl" name="iconSpecial-graphics" />
    <div class="tabs-list">
      <div v-for="(item, index) in tabList" :key="index" :class="isactiveTabClass(item)" @click.stop="goRoute(item)">
        <div class="flex-a">
          <img class="tab-icon" :src="tapImage(item.type)" />
          <span class="line-1 mw100">{{ item.title }} </span>
        </div>
        <div v-if="index !== 0" @click.stop="deltabItem(index)">
          <icon class="tap-close-item" name="iconerror" :url="iconUrl" />
        </div>
      </div>
    </div>

    <div class="flex-a">
      <icon class="org-img-window" name="iconrefresh" :url="iconUrl" @click="openRefresh" />
      <icon
        v-if="newWinFlag && !hidGroup"
        class="org-img-window"
        name="iconwindow"
        :url="iconUrl"
        @click="openStandAloneWin"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ClientSide } from "@renderer/types/enumer.js";
import { useRoute, useRouter } from "vue-router";
import { onMounted, onActivated, ref, watch, nextTick, computed } from "vue";
import avatar from "@renderer/components/kyy-avatar/index.vue";
import { useI18n } from "vue-i18n";
import { iconUrl } from "@renderer/plugins/KyyComponents";
import { Icon } from "tdesign-icons-vue-next";
import { businessCount } from "@renderer/api/business";
import { tapImage, setgztdid } from "@renderer/utils/myUtils.ts";
import { useHonorStore } from "@renderer/views/workBench/teamSting/honorStore";
import { useTabStore } from "@renderer/views/workBench/teamSting/honorStore/tabStore";
import temaavatar from "@/assets/svg/clouddisk/temaavatar.svg";
import { approveForeignCount } from "@/api/approve";
import LynkerSDK from "@renderer/_jssdk";
const { ipcRenderer } = LynkerSDK;

const { t } = useI18n();
// 公共store
const honorStore = useHonorStore();
const tabStore = useTabStore();
const newWinFlag = ref(true);
const props = defineProps({
  currentAppPath: {
    type: String,
    default: "",
  },
  iconIconWindow: {
    type: Boolean,
    default: true,
  },
  notRed: {
    type: Boolean,
    default: false,
  },
  groupList: {
    type: Array,
    default: () => [],
  },
  tabList: {
    type: Array,
    default: () => [],
  },
  activationGroupItem: {
    type: Object,
    default: () => {},
  },
  activeIndex: {
    type: Number,
    default: 0,
  },
  tabIndex: {
    type: Number,
    default: 0,
  },
  redType: {
    type: Number,
    default: 1,
  },
  hidGroup: {
    type: Boolean,
    default: false,
  },
});
onActivated(() => {});
onMounted(() => {});
const router = useRouter();
const route = useRoute();
// 'openStandAloneWin',
const emits = defineEmits([
  "openRefresh",
  "getGroupListApi",
  "deltabItem",
  "setCloudDiskType",
  "changGroupApproval",
  "changGroupCustomer",
  "changGroupSupplier",
  "changGroupDevice",
  "chang-group-business",
  "changGroupPartner",
  "setActiveIndex",
  "restUdpListRefresh",
  "setActiveIndexAndName",
  "changGroupCustomerService",
  "changGroupActivity",
]);
const openRefresh = () => {
  honorStore.shershFlag = true;
  emits("openRefresh");
};
const onVisibleChangeFn = (val) => {
  console.log(val, props.activationGroupItem);
  if (val) {
    emits("getGroupListApi", props.activationGroupItem.teamId);
  }
};
const openStandAloneWin = () => {
  console.log(props.tabList, "routerprops.tabListouteroute");
  newWinFlag.value = false;
  ipcRenderer.invoke("click-standalone-window", {
    url: props.currentAppPath,
    flag: route.fullPath.split("/")[1],
  });
};
// const goRoute = (item) => {
//   emits("getGroupListApi", props.activationGroupItem.teamId);
//   router.push({
//     path: item.path,
//     query: { ...route.query, ...item.query },
//   });
//   honorStore.setdetailHaved(false)
// };
const goRoute = (item) => {
  const querys = { ...item.query };
  router.push({
    path: item.path,
    query: { ...querys },
  });
};

ipcRenderer.on("update-approve", (val, i) => {
  if (props.activationGroupItem?.teamId) {
    emits("getGroupListApi", props.activationGroupItem.teamId);
  }
});

const isactiveTabClass = (item) => {
  if (item?.addNew) {
    return route.path === item.path && JSON.stringify(route?.query) === JSON.stringify(item.query)
      ? "bgc-fff tabs-item"
      : "tabs-item";
  }
  return route.path === item.path ? "bgc-fff tabs-item" : "tabs-item";
};
const deltabItem = (index) => {
  // if (data.addNew) {
  //   const index = props.tabList.findIndex(
  //     (e) => e.path === data?.path && JSON.stringify(route?.query) === JSON.stringify(data.query),
  //   );
  //   emits("deltabItem", index, true);
  // } else {
  //   const index = props.tabList.findIndex((e) => e.path === data?.path);
  //   emits("deltabItem", index, true);
  // }
  emits("deltabItem", index, true);
};
// 点击存草稿按钮
const saveDraftFlag = ref(false);
const saveDraft = () => {
  saveDraftFlag.value = true;
};

watch(
  () => route.path,
  () => {
    if (route.query.closeTab) {
      deltabItem({ path: route.query.closeTab });
    }
  },
);
</script>

<style lang="less" scoped>
@import "@/style/workAreaHead.less";

.operate-kr {
  max-height: 320px;
  overflow-y: auto;
  overflow-x: hidden;
}

.operate-kr::-webkit-scrollbar {
  width: 6px;
  height: 6px;
  // background-color: red;
}

/*定义滚动条轨道 内阴影+圆角*/
.operate-kr::-webkit-scrollbar-track {
  // box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  // -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  // border-radius: 10px;
  background-color: #fff;
}

/*定义滑块 内阴影+圆角*/
.operate-kr::-webkit-scrollbar-thumb {
  border-radius: 10px;
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
  background-color: #d5dbe4;
}

.mw100 {
  max-width: 100px;
}
</style>
