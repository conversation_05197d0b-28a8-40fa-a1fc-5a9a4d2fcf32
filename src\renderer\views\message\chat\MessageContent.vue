<script lang="tsx">
  import { PropType, h } from "vue";
  import { Radio, Icon, MessagePlugin, DialogPlugin, Button } from "tdesign-vue-next";
  import { useContactsStore } from "@renderer/store/modules/contacts";
  import to from "await-to-js";
  import { AxiosError, AxiosResponse } from "axios";
  import { jumpH5WithLink } from "@renderer/views/contacts/utils";
  import moment from "moment";
  import PlayIcon from "@renderer/assets/album/icon_play.svg";
  import { isPreviewableType, previewFileWithErrorTips } from "@renderer/utils/wps";
  import { MsgShareType } from "@/utils/share";
  import MergedMessage from "./MergedMessage.vue";
  import ChatAvatar from "../components/ChatAvatar.vue";
  import VcardMsg from "./msgTypeContent/VcardMsg.vue";
  import UsageTips from "./msgTypeContent/usage-tips/Home.vue";
  import MsgWrapper from "../service/message";
  import { useMessageStore } from "../service/store";
  import { useChatExtendStore } from "../service/extend";
  import { useChatActionStore, useChatVoiceStore } from "../service/actionStore";
  import { parseUrl, parseSquareShareUrl } from "@/api/square/square";
  import {
    getAppShareApprovalStatus,
    reqShareApproveReject,
    reqShareApproveAgree,
    getActivityInfo,
  } from "../service/extend/statusUtils";
  import { getBaseUrl } from "@renderer/utils/apiRequest";
  import { goToDigitalPlatform_member } from "@renderer/views/member/utils/auth";

  import {
    getParsedTextMessage,
    getParsedServerTextMsg,
    MsgTextType,

    getZxListRemindTimeText,
    getEndSubstring,
    getReferMessagePreview,
    msgJumpToCloudDisk,
    getMsgSenderName,
    getImageStyle,
    getMsgImageSrc,
    getMeetingStartText,
    getMeetingEndText,
    getMeetingTimeText,
    ClientMsg,
    getRemindTimeText,
  } from "../service/msgUtils";
  import { formatTime, jumpZhixing } from "@/views/zhixing/util";
  import { fileImage } from "@renderer/utils/myUtils";

  import SvgIcon from "@/components/SvgIcon.vue";
  import {
    AppCard,
    AppCardHeader,
    AppCardBody,
    AppCardFooter,
    AppCardBtn,
    AppCardText,

  } from "./MessageAppCard";
  import {
    imgArrayType,
    videoArrayType,
  } from "@/views/clouddisk/clouddiskhome/fileType";

  import MsgMoment from "../cards/msg-moment.vue";
  import MsgImage from "../cards/msg-image.vue";
  import MsgVoice from "../cards/msg-voice.vue";
  import MsgReply from "../cards/msg-reply.vue";
  import MsgAlbum from "../cards/msg-album.vue";
  import MsgWeather from "../cards/msg-weather.vue";
  import MsgBirthday from "../cards/msg-birthday.vue";

  import MsgPartnerInvite from "../cards/msg-partnerInvite.vue";
  import MsgAppSquare from "../cards/msg-app-square.vue";
  import MsgAppKnow from "../cards/msg-app-know.vue";
  import MsgGroupNotice from "../cards/msg-group-notice.vue";
  import MsgImportant from "../cards/msg-important.vue";
  import MsgSportStepsEnable from "../cards/msg-sportStepsEnable.vue";
  import MsgHoliday from "../cards/msg-holiday.vue";
  // 运动排行
  import MsgRank from "../cards/msg-rank.vue";

  import CloudeDiskIcon from "@/assets/<EMAIL>";

  import SquareIcon from "@/assets/im/square_icon.png";
  import SquareLink from "@/assets/im/square_link.png";

  import { checkPostInvisible, getPostPublishVerifyPreview, getPost } from "@/api/square/post";
  import { ErrorResponseReason } from "@/views/square/enums";
  import { errReasonMap } from "@/views/square/constant";
  import AppServe from "./msgTypeContent/AppServe";
  import AppPartner from "./msgTypeContent/AppPartner";
  import VcardAssistant from "./msgTypeContent/VcardAssistant.vue"
  import AppWorks from "./msgTypeContent/AppWorks";
  import AppSecretary from "./msgTypeContent/AppSecretary/index";
  import HelpCenter from "./msgTypeContent/HelpCenter";
  import ServiceContent from "./msgTypeContent/ServiceContent";
  import AppActivityEle from "./msgTypeContent/AppActivityEle";
  import AppNicheEle from "./msgTypeContent/AppNicheEle";
  import ApproveAssistantEle from "./msgTypeContent/ApproveAssistantEle";
  import ContractAssistantEle from "./msgTypeContent/ContractAssistantEle";
  import AppTeams5005 from "./msgTypeContent/AppTeams5005";
  import AppBussinesEle from "./msgTypeContent/AppBussinesEle";
  import FileEle from "./msgTypeContent/FileEle.vue";
  import Consult from "./msgTypeContent/Consult/consult.vue";
  // import Exclusive from "./msgTypeContent/Consult/Exclusive.vue";
  import Payorder from "./msgTypeContent/Consult/payorder.vue";
  import AboutPbFcSquare from "./msgTypeContent/AboutPbFc/share.vue";
  import Forum from "./msgTypeContent/Forum/index.vue";
  import Order from "./msgTypeContent/order/index.vue";
  import MeetingMsg from "./msgTypeContent/MeetingMsg.vue";
  import MapImageLoader from './MapImageLoader.vue';
  import SHOP from "./msgTypeContent/shop/index.vue";
  import AdAssistant from "./msgTypeContent/AdAssistant/index.vue";

  import {LkIMEditorPlayground} from "@rk/editor";

  import { i18nt } from "@/i18n";
  import MyVCardType from "./msgTypeContent/MyVCardType.vue";
  import LynkerSDK from "@renderer/_jssdk";
  const { ipcRenderer, shell } = LynkerSDK;

  const actionStore = useChatActionStore();

  let ctxProps: any;
  export default {
    props: {
      onShowPostDetailDialogId: { type: Function, default: (id: string) => console.log(id) },
      message: { type: Object as PropType<MsgWrapper> },
      /**
       * 由于群存在多身份，需要区别是否当前身份
       */
      isMySelf: { type: Boolean, default: false },
      from: { type: String, default: '' }
    },
    setup(props, ctx) {
      ctxProps = props;
      const msgStore = useMessageStore();
      return () => {
        if (props.message.isMerged) {
          // @ts-ignore
          return <MergedMessage message={props.message} />;
        }

        const msg = props.message.msg;
        const messageType = msg.messageType;
        // console.log('extra?',msg.messageType, msg.contentExtra?.contentType, msg.contentExtra?.scene );
        if (messageType === "text") {
          const extra = msg.contentExtra;
          let msgs = _.cloneDeep(msg);
          if (msgs.contentExtra && msgs.contentExtra.data) {
            if (typeof msgs.contentExtra.data === 'object' && msgs.contentExtra.data !== null) {
              msgs.contentExtra.data.receiver = msgs.contentExtra.data.receiver
                ? msgs.contentExtra.data.receiver
                : msgs.contentExtra.data.creator;
            } else {
              console.error('预期 data 是一个对象，但实际得到的是:', msgs.contentExtra.data);
            }
            // msgs.contentExtra.data.receiver = msgs.contentExtra?.data?.receiver
            //   ? msgs.contentExtra?.data?.receiver
            //   : msgs.contentExtra?.data?.creator;
          }

          // console.log('extra?.contentType',extra);
          switch (extra?.contentType) {
            // 基本类型
            case "digital_card":
              if (props.isMySelf) {
                useMessageStore().isMySendVcard = true;
              }
              return props.from === 'history' ? `[${i18nt('vcard.numberVcard')}]` : <VcardMsg msg={msg} onClick={() => {
                useChatExtendStore().showChatDialog("vcard-chat-dialog", msg?.contentExtra?.data);
              }} />;
            case "text":
              return getTextEle(msg);
            case "richText":
              return getRichTextEle(msg);
            case "image":
              return h(MsgImage, { msg: props.message, isSender: props.isMySelf });
            case "emoji_image":
              return h(MsgImage, { msg: props.message, isSender: props.isMySelf });
            case "emoji_coco_image":
              return getTextEle(msg);
            case "voice":
              return h(MsgVoice, { msg: props.message, isSender: props.isMySelf });
            case "video":
              return getVideoEle(msg);
            case "location":
              return getLocationEle(extra.data, props.isMySelf);
            case "file":
              return <FileEle msg={msg} />;
            case "merge_forward":
              return getMergeForwardEle(msg, props.isMySelf);
            case "meeting_start":
              return getMeetingStartEle(msg);
            case "meeting_end":
              return getMeetingEndEle(msg, msgStore.chatingSession.myCardId);
            case "meeting":
              return getMeetingEndEle(msg, msgStore.chatingSession.myCardId);
            case "group_notice":
              return <MsgGroupNotice msg={props.message} />;

            case "server_video_call_finish":
              return getMeetingEndTimeEle(msg);
            case "server_message_middle":
              return getSeverMsgEle(msg, msgStore.chatingSession);
            // @ts-ignore
            case "server_message_middle_weather":
              return <MsgWeather msg={props.message} />;
            // @ts-ignore
            case "server_message_middle_birthday":
              return <MsgBirthday msg={props.message} />;

            // 分享卡片
            case "square":
              return h(MsgMoment, { msg: props.message, maxWidth: props.from === 'history' ? "326px" : "360px" });
            // return <AboutPbFcSquare msg={props.message} />;
            case "square_idcard":
              return getSquareIdcardEle(msg);
            case "square_preview":
              return getSquarePreviewEle(msg);
            case "card_id_card":
              return getCardEle(msg);
            case "cloud_disk":
              return getCloudeDiskFile(msg);
            case MsgShareType.about_us_organize:
            case MsgShareType.about_us_experience:
            case MsgShareType.about_us_honor:
            case MsgShareType.platform_charm:
            case MsgShareType.party_building:
              return <AboutPbFcSquare msg={props.message} from={props.from} />;
            case "zx_remind_detail":
              return getZxDetailEle(msg);
            case "zx_list_detail":
              return getZxListEle(msg);
            case "zx_schedule":
              return getZxScheduleEle(msg);
            case "zx_notes_detail":
              return getZxNoteDetailEle(msg);
            case "approve":
              return getApproveShareEle(msg);
            case "approve_to_rel_people":
              return getApproveCommentForward(msg);
            case "biz_opportunity":
              return getBizOpportunityEle(msg);
            /**
             * deprecated
             */
            case "activity":
              return getActivityCardEle(msg, ctxProps);
            case "activity_card":
              return h(AppActivityEle, { data: msg, ...ctx.attrs, activityContentType: "activity_card" });
            case "sharedPartner":
              return h(MsgPartnerInvite, { msg: props.message, ...ctx.attrs });
            case "square_light_album":
              return h(MsgAlbum, { msg: props.message, ...ctx.attrs });
            // 帮助中心分享
            case "help_center":
              return h(HelpCenter, { data: msg, ...ctx.attrs });
            // 服务内容分享
            case "service_content":
              return h(ServiceContent, { data: msg, ...ctx.attrs });

            // 处于对方的黑名单中
            case ClientMsg.blacklist:
              return getSeverMsgEle(msg, msgStore.chatingSession);
            // 助手卡片
            case "APP_CLOUD_DISK":
              return getCloudDiskAssistantEle(msg);
            case "APP_KNOW":
              return h(MsgAppKnow, { msg: props.message, ...ctx.attrs });
            // 广场
            case "APP_SQUARE":
              return h(MsgAppSquare, { msg: props.message, ...ctx.attrs, maxWidth: props.from === 'history' ? "326px" : "360px" });
            // 审批助手
            case "APP_APPROVAL":
              return h(ApproveAssistantEle, { data: msg, ...ctx.attrs });
            // 增加联系人助手
            case "APP_ADDRESS_BOOK":
              return h(ContractAssistantEle, { data: msg, ...ctx.attrs });
            // 名片助手
            case "APP_DIGITAL_BUSINESS":
              return <VcardAssistant msg={props.message} />;
            case "APP_TEAMS":
              return getTeamsAssistantEle(msg);
            case "APP_BUSINESS":
              return h(AppBussinesEle, { data: msg, ...ctx.attrs });
            // 小秘书
            case "APP_SECRETARY":
              return <AppSecretary data={msg} />;
            // 论坛助手
            case "APP_BBS":
              return <Forum msg={msg} />;
            // 订单助手
            case "APP_ORDER_ASSISTANT":
              return <Order msg={msg} />;
            case "APP_WORKS":
              // 工作通知
              return h(AppWorks, { data: msg, ...ctx.attrs });
            // 商机助手
            case "APP_NICHE":
              return h(AppNicheEle, { data: msg, ...ctx.attrs });
            // 活动助手.
            case "APP_ACTIVITY":
              return h(AppActivityEle, { data: msg, ...ctx.attrs, activityContentType: "APP_ACTIVITY" });
            // 联盟助手;
            case "APP_PARTNER":
              return h(AppPartner, { data: msg, ...ctx.attrs });
            // 服务助手
            case "APP_SERVE":
              return h(AppServe, { data: msg, ...ctx.attrs });
            // 运动排行卡片
            case "SportPlanCreated":
              return h(MsgRank, { msg: msg, ...ctx.attrs });
            // 运动要求全部达标
            case "SportPlanAllUpToPar":
              return h(MsgRank, { msg: msg, ...ctx.attrs });
            // 每日运动排行
            case "SportStepsRanking":
              return h(MsgRank, { msg: msg, ...ctx.attrs });
            // 运动排行启用
            case "SportStepsEnabled":
              return h(MsgSportStepsEnable, { msg: props.message, ...ctx.attrs });
            // 重要日子
            case "DayReminder":
              return h(MsgImportant, { msg: props.message, ...ctx.attrs });
            // 活动卡片消息
            case "ActivityInvitation":
              return h(AppActivityEle, { data: msgs, ...ctx.attrs, activityContentType: "ActivityInvitation" });
            // 节假日
            case "PredefinedDayReminder":
              return h(MsgHoliday, { msg: props.message, ...ctx.attrs });
            // 咨询单 获取专属名称咨询单
            case MsgShareType.consult:
              return <Consult msg={msg} />;
            // 获取专属名称咨询单
            // case MsgShareType.exclusive_name:
            //   return <Exclusive msg={msg} />;
            // 咨询转订单
            case MsgShareType.consult_order:
              return <Payorder msg={msg} />;
            // 使用技巧
            case MsgShareType.usage_tips:
              return <UsageTips msg={msg} />;
            // 城市代理
            case "APP_CITY_PROXY":
              return h("div", { class: "proxy-box", }, [
                h("div", { class: "proxy-header" }, extra.data.content.title),
                h("div", { class: "proxy-content", }, extra.data.content.subtitle),
                h("div", {
                  class: "proxy-btn", onClick: () => {
                    MessagePlugin.warning(i18nt('im.msg.appTip'));
                  }
                }, i18nt("im.msg.enterNow")),
              ]);
            case 'APP_MEETING_MSG':
              return h(MeetingMsg, { msg: msg, isSender: props.isMySelf });
            case 'APP_PENDING':
              return h("div", { class: "chat-text" }, [h("span", `${msg.contentExtra?.data?.content}`)]);
            case 'APP_STORE_ASSISTANT':
               // 店铺助手
              return h(SHOP, { msg: msg })
            case 'APP_AD_ASSISTANT':
               // 广告助手
              return h(AdAssistant, { msg: msg })
          }
        } else if (msg.messageType === 114) {
          return h(MsgReply, { msg: props.message, isSender: props.isMySelf });
        } else if (msg.messageType === 2101 || msg.messageType === 111) {
          return isSenderByCardId(msg) ? getMyRecallMsgEle(msg) : getOtherRecallMsgEle(msg);
        }

        const msgTypeText = process.env.NODE_ENV !== "production" ? msg.contentExtra?.contentType : "";
        return h("div", { class: "chat-text" }, [h("span", `[${i18nt("im.public.unsupport")}]${msgTypeText}`)]);
      };
    },
  };
  // 判断发送者。通过cardId和当前聊天我的cardId，消息类型不是中间通知展示类消息
  function isSenderByCardId(msg: MessageToSave) {
    return (
      useMessageStore().chatingSession?.myCardId === msg.contentExtra?.senderId &&
      msg.contentExtra?.contentType !== "server_message_middle"
    );
  }

  function getMyRecallMsgEle(msg: MessageToSave) {
    const btns =
      msg.contentExtra?.contentType === "text"
        ? h(
          "a",
          { class: "text-link", onClick: () => useChatActionStore().onReEditMessage(msg) },
          i18nt("im.public.editAgain"),
        )
        : null;
    return h(
      "div",
      { style: "width:100%; text-align:center;font-size:14px;color:#828DA5;font-weight: 400;line-height: 22px;" },
      [h("span", i18nt("im.public.recall_self")), btns],
    );
  }

  function getOtherRecallMsgEle(msg: MessageToSave) {
    const name = getMsgSenderName(msg);
    return h(
      "div",
      { style: "width:100%; text-align:center;font-size:14px;color:#828DA5;font-weight: 400;line-height: 22px;" },
      [h("a", { class: "text-link" }, name), i18nt("im.public.recall_peer")],
    );
  }

  function getTextEle(msg: MessageToSave) {
    const splits = getParsedTextMessage(msg);
    if (splits.length === 1 && splits[0].type === MsgTextType.Emoji) {
      const emoji = splits[0];
      return h(
        "div",
        {
          class: "chat-text",
          style: `${emoji?.height > 100 ? "background-color: transparent;border: none;padding:0;" : ""}`,
        },
        h("img", {
          draggable: false,
          src: emoji.emojiSrc,
          alt: emoji.str,
          'data-alt': emoji.str,
          'data-emoji': emoji.str,
          style: `height:${emoji?.height || 44}px;width:${emoji?.width || 44}px;`,
          onCopy: handleMouseSelect
        }),
      );
    }

    return h(
      "div",
      { class: "chat-text", style: 'whiteSpace: pre-wrap' },
      splits.map((item) => {
        switch (item.type) {
          case MsgTextType.Text:
            return h("span", item.str);
          case MsgTextType.Emoji:
            return h("span", { onCopy: handleMouseSelect }, h("img", { draggable: false, src: item.emojiSrc, alt: item.str, 'data-alt': item.str, 'data-emoji': item.str, style: "height:32px;width:32px;" }));
          case MsgTextType.At:
            return h("span", { 'data-at': JSON.stringify({
              value: item?.str?.replace('@', '')?.replace('~', ''),
              cardId: msg?.contentExtra?.data?.atInfo?.[item?.atIndex || 0]?.cardId,
              openId: msg?.contentExtra?.data?.atInfo?.[item?.atIndex || 0]?.openId,
              sessionId: msg?.targetId,
            }), class: "text-link", onClick: () => openIdentityCard(msg, item) }, item.str);
          case MsgTextType.Email:
            return h("span", { class: "text-link" }, item.str);
          case MsgTextType.File:
            return h("span", { class: "text-link" }, item.str);
          case MsgTextType.Url:
            return h("span", { class: "text-link", onClick: () => openUrl(msg, item) }, item.str);
          default:
            return item.str;
        }
      }),
    );
  }

  function getRichTextEle(msg: MessageToSave) {
    // const splits = getParsedTextMessage(msg);
    // if (splits.length === 1 && splits[0].type === MsgTextType.Emoji) {
    //   const emoji = splits[0];
    //   return h(
    //     "div",
    //     {
    //       class: "chat-text",
    //       style: `${emoji?.height > 100 ? "background-color: transparent;border: none;padding:0;" : ""}`,
    //     },
    //     h("img", {
    //       draggable: false,
    //       src: emoji.emojiSrc,
    //       alt: emoji.str,
    //       style: `height:${emoji?.height || 44}px;width:${emoji?.width || 44}px;`,
    //       onCopy: handleMouseSelect
    //     }),
    //   );
    // }
    let data = msg?.contentExtra?.data?.text
    try {
      data = JSON.parse(data)
    } catch (error) {
      console.error('richText', error)
    }

    // 富文本 LkIMEditorPlayground组件渲染
    return h(
      "div",
      { class: "chat-text", style: 'whiteSpace: pre-wrap' },
      // 富文本 LkIMEditorPlayground组件渲染
      h(LkIMEditorPlayground, {
        content: data,
        emojiObject: EmojiObject,
        onOnMentionClick: (data: any) => {
          console.log('mentionElement', data, msg.contentExtra?.data?.atInfo)
          const cardId = data.cardId;
          const myId = useMessageStore().chatingSession?.myCardId;
          openIdentityCardById(myId, cardId);
        },
        onOnLinkClick: (data: string) => {
          openUrl(msg, { str: data })
        }
      }),
    );
  }

  /**
   * 视频文件消息
   * @param msg
   {
    "duration": 3,
    "height": 1280,
    "recallFileId": null,
    "size": 5022489,
    "videoImageUrl": "https://img.kuaiyouyi.com/chat/d30bfbaea776b436556035f8fc8342b0/20230809/2a389e96aa144de43a4a63b6c4e77407a.mp4?x-oss-process=video/snapshot,t_0,w_0,f_png",
    "videoName": "",
    "videoUrl": "https://img.kuaiyouyi.com/chat/d30bfbaea776b436556035f8fc8342b0/20230809/2a389e96aa144de43a4a63b6c4e77407a.mp4",
    "width": 720
   }
   */
  function getVideoEle(msg: MessageToSave) {
    const data = msg.contentExtra?.data;
    let size = getImageStyle(data?.width, data?.height, { max: 280, min: 120 });
    let styleString = size.width ? `width: ${size.width}; height: ${size.height};` : "";
    return h("div", { style: "position: relative; border: 1px solid #eceff5;" }, [
      h("img", { class: "chat-image chat-video-img", src: data?.videoImageUrl, style: size, draggable: false }),
      h(
        "div",
        {
          style: `${styleString}position: absolute; left:0; right:0; top:0; bottom:0;display:flex;justify-content:center; align-items:center;background-color: rgba(0, 0, 0, 0.1)`,
        },
        [h(Icon, { name: "play-circle-filled", size: "40px", color: "#fff" })],
      ),
    ]);
  }

  function getMergeForwardEle(msg: MessageToSave, isSender: boolean) {
    const data = msg.contentExtra?.data as IMessageMergedExtra;
    return h("div", { class: "chat-merged", "data-sender": isSender, onclick: () => previewMergedFile(msg) }, [
      h("div", { class: "merged-title" }, i18nt("im.public.chat_record")),
      h("div", { class: "merged-divider" }),
      data?.data?.map((c) =>
        h(
          "div",
          { class: "merged-data line-1" },
          `${c?.name ?? ""}:${getReferMessagePreview({ data: c.data, contentType: c.type }) ?? ""}`,
        ),
      ),
    ]);
  }

  /**
  {
      "adCode": "",
      "adName": "双流区",
      "address": "锦龙街117号(广都地铁站C口步行200米)",
      "businessArea": "",
      "cityCode": "",
      "cityName": "成都市",
      "distance": 3,
      "latLng": {
          "latitude": 30.512691,
          "longitude": 104.076473
      },
      "poiId": "B0IA3ZUNKO",
      "provinceCode": "",
      "provinceName": "四川省",
      "tel": "***********",
      "title": "夜鸡杂Chattering Together(天目中心店)"
      }
   */
  function getLocationEle(data: any, isSender: boolean) {
    const location = `${data.latLng.longitude},${data.latLng.latitude}`;
    // https://lbs.amap.com/api/webservice/guide/api/staticmaps/
    // TODO: 缓存静态地图
    // const staticmaps = `https://restapi.amap.com/v3/staticmap?location=${location}&zoom=14&size=240*96&markers=mid,,A:${location}&key=${mapKey}`;
    // https://lbsyun.baidu.com/faq/api?title=static/heightStatic
    const staticmaps = `https://api.map.baidu.com/staticimage/v2?ak=${BAIDU_AK}&center=${location}&markers=${location}&width=240&height=96&zoom=14&scaler=2`;
    return h("div", { class: "chat-location", "data-sender": isSender }, [
      h("div", { class: "address" }, [
        h("div", { class: "address-title" }, [
          h("svg", { class: "svg-size20", style: "color: #FC7C14" }, [h("use", { href: "#iconorientation" })]),
          h("span", { class: "line-1" }, data?.title),
        ]),
        h("div", { class: "line-1 address-detail" }, data?.address),
      ]),
      <MapImageLoader location={data.latLng} />
      // h("img", {
      //   src: staticmaps,
      //   style: { display: "block", width: "100%", borderWidth: "1px", height: "96px", objectFit: "cover" },
      // }),
    ]);
  }

  /**
   * "avatar": "真帅",
   * "cardId": "$45",
   * "departmentId": null,
   * "departmentName": null,
   * "jobId": null,
   * "jobName": "产研中心",
   * "name": "真帅",
   * "openId": "009hc0000hkaa07njq",
   * "teamId": null,
   * "teamName": "成都测试组织1",
   * "type": "internal"
   */
  function getCardEle(msg: MessageToSave) {
    const data = msg.contentExtra?.data;
    return h("div", { class: "chat-idcard" }, [
      h("div", { style: "display:flex;flex-direction:row;justify-content:space-between;gap:8px;" }, [
        h("div", { style: "width:44px;height:44px;overflow:hidden;border-radius:50%;" }, [
          h("img", {
            src: data?.avatar,
            style: "width:100%;height:100%;display:block;",
            alt: getEndSubstring(data?.name),
          }),
        ]),
        h("div", { style: "flex: 1;" }, [
          h("div", { class: "user-name" }, data?.name),
          h(
            "div",
            { class: "team-name" },
            data?.ringkolId ? `${i18nt("identity.lkID")}: ${data?.ringkolId}` : data?.teamName,
          ),
        ]),
      ]),
      h("div", { class: "divider" }),
      h("div", { class: "card-name" }, i18nt("im.public.card1")),
    ]);
  }

  function getCloudeDiskFile(msg: MessageToSave) {
    const data = msg.contentExtra?.data;

    const onClick = async () => {
      // 预览文件，图片，视频 走预览文件组件弹窗
      if (
        (data.officeId && isPreviewableType(data.type)) ||
        imgArrayType.includes(data.type.toLowerCase()) ||
        videoArrayType.includes(data.type.toLowerCase())
      ) {
        try {
          await DiskFileList(data.diskId);
          await fileDetail(data?.isFolder ? data?.pid : data?.id);
          // openPreWin(data.officeId, data.type);
          actionStore.previewFileAction(data);
        } catch (error) {
          MessagePlugin.error(error?.response?.data?.message || "网络异常");
        }
      } else {
        msgJumpToCloudDisk({
          disk_id: data?.diskId,
          file_id: data?.type === "folder" ? data?.id : data?.pid,
        });
      }
    };

    return h("div", { class: "im-app-card chat-cloud-disk", onClick }, [
      h("div", { class: "im-card-title" }, [
        h("img", { src: CloudeDiskIcon, style: "width: 20px;height:20px;" }),
        i18nt("im.public.disk"),
      ]),
      h("div", { class: "im-card-content cloud-file cursor-pointer" }, [
        h("img", { src: fileImage(data?.type), style: "width: 20px;height:20px;" }),
        h("div", { style: "flex:1;" }, data?.title),
      ]),
      h(
        "div",
        {
          class: "im-card-btn1",
          onClick: (e) => {
            console.log(data, "dataaaaaaaaaa");
            e.stopPropagation();
            msgJumpToCloudDisk({
              disk_id: data?.diskId,
              file_id: data?.type === "folder" ? data?.pid : data?.id,
            });
          },
        },
        i18nt("im.public.goCheck"),
      ),
    ]);
  }

  /**
   {
      "content": {
          "body": [],
          "title": "issac005 已将你在【测试2】中的权限修改为可上传下载"
      },
      "extend": {
          "disk_id": 58,
          "file_id": 1168
      },
      "header": {
          "team": {
              "logo": "",
              "name": "issac测试1018公司"
          }
      },
      "scene": 3012,
      "template": "normal",
      "type": "APP_CLOUD_DISK"
  }
    云盘场景值
      3001 分配云盘使用权限
      3002 移除云盘权限
      3003 申请云盘容量
      3011 增加权限
      3012 修改权限
      3013 删除权限
      3014 变更为所有者
   */
  function getCloudDiskAssistantEle(msg: MessageToSave) {
    const data = msg.contentExtra?.data;
    const onClickDetail = () =>
      msgJumpToCloudDisk({
        disk_id: data?.extend?.disk_id,
        file_id: data?.extend?.file_id,
        cloudDiskMB: [3003].includes(data?.scene),
      });
    const showJumpBtn = [3002, 3013].includes(data?.scene);
    return (
      <AppCard style="width:320px;">
        <AppCardHeader>
          {data?.header?.team?.logo ? (
            <img
              src={data?.header?.team?.logo}
              style="display:block;vertical-align: middle; width: 20px;height:20px; border-radius: 4px;margin-right:12px;overflow:hidden;"
            />
          ) : (
            <SvgIcon
              name="im_avatar_team"
              style="width:20px;height:20px; color:#488BF0; border-radius: 4px; margin-right:12px; overflow:hidden;"
            />
          )}
          {data?.header?.team?.name ?? ""}
        </AppCardHeader>
        <AppCardBody>{data?.content?.title ?? ""}</AppCardBody>
        <div class="app-card-divider"></div>
        {showJumpBtn ? null : (
          <AppCardFooter>
            <div class="app-card-btn" onClick={onClickDetail}>
              {i18nt("im.public.goCheck")}
            </div>
          </AppCardFooter>
        )}
      </AppCard>
    );
  }

  /**
   {
      "openid": "00ry9mrfbtwch",
      "title": "产品梳理技术做不到的需求：\n1、消息免打扰状态下，设备推送无法控制\n2、红点双端无法同步\n3、头像、昵称、备注、名称无法实时更新\n4、基于第三方插件开发的功能项：如摄像无法聚焦、无法横屏、目录英文无法转换\n5、卸载重装登录后，消息已读过的状态仍会存在未读",
      "desc": "123",
      "noticeTyp": "ONCE",
      "repeat_typ": [
          "NONE"
      ],
      "knockAt": 1695140220000,
      "owner": "02ghs0000y8vxnrbuc",
      "create_at": 1695139941,
      "update_at": 0,
      "delete_at": 0,
      "parent": "00ry9mrfbtwcg"
  }
   */
  function getZxTypeEle(msg: MessageToSave) {
    // scene 0提醒 1清单
    if (msg.contentExtra?.scene === 1) {
      return getZxListEle(msg);
    } else if (msg.contentExtra?.scene === 19) {
      return h("div", { class: "chat-text" }, `[${i18nt("im.public.unsupport")}]`);
    }
  }

  function getZxDetailEle(msg: MessageToSave) {
    const data = msg.contentExtra?.data;
    if(typeof data.knockAt === 'string'){
      data.knockAt = Number(data.knockAt);
      data.knock_at = data.knockAt;
    }
    return h("div", { class: "im-app-card chat-cloud-disk" }, [
      h("div", { class: "im-card-title" }, data?.title),
      h("div", { class: "im-card-content" }, [
        // h('div')
        h("div", { style: "flex:1; display:flex;flex-direction:column;gap:4px;" }, [
          h(
            "div",
            { style: "text-overflow: ellipsis;white-space: nowrap;overflow: hidden;" },
            `${i18nt("im.msg.remind_frequency")}：${checkFrequency(data)}`,
          ),
          h("div", `${i18nt("im.msg.remind_time")}：${getRemindTimeText(data?.knock_at)}`),
        ]),
      ]),
      h(
        "div",
        {
          class: "im-card-btn1",
          onClick: () => {
            // useChatActionStore().onOpenRemindDetail(data);
            console.log("--->", "提醒");
            useChatExtendStore().showChatDialog("zhixing-remind", msg);
          },
        },
        i18nt("im.public.detail"),
      ),
    ]);
  }

  const frequencyOptions = [
    { label: i18nt("zx.remind.ONCE"), value: "ONCE", formDataLabel: "date" },
    { label: i18nt("zx.remind.EVERY_DAY"), value: "EVERY_DAY", formDataLabel: "time" },
    { label: i18nt("zx.remind.EVERY_WEEK"), value: "EVERY_WEEK", formDataLabel: "week" },
    { label: i18nt("zx.remind.EVERY_MONTH"), value: "EVERY_MONTH", formDataLabel: "day" },
    { label: i18nt("zx.remind.EVERY_YEAR"), value: "EVERY_YEAR", formDataLabel: "year" },
    { label: i18nt("zx.remind.EVERY_WORKDAY"), value: "EVERY_WORKDAY", formDataLabel: "time" },
    { label: i18nt("zx.remind.EVERY_MON_SAT"), value: "EVERY_MON_SAT", formDataLabel: "time" },
    { label: i18nt("zx.remind.EVERY_SUN_FRI"), value: "EVERY_SUN_FRI", formDataLabel: "time" },
  ];

  // 将数组转为周一周二等
  const frequencyOptionsName = (chooseWeekdays: any) => {
    if (!chooseWeekdays) return;
    const weekdays = ["周一", "周二", "周三", "周四", "周五", "周六", "周日"];
    return chooseWeekdays.map((v) => weekdays[v - 1]).join("、");
  };

  // 判断选择提醒频次
  const checkFrequency = (data: any) => {
    const noticeTyp = data?.notice_typ || data?.noticeTyp || "";
    console.log(noticeTyp)
    if (!noticeTyp) return;
    const title = frequencyOptions.find((v) => v.value === noticeTyp)?.label || "";
    const knockAt = data?.knock_at;
    if (noticeTyp === "EVERY_WEEK") {
      return `${title}-${frequencyOptionsName(data.chooseWeekdays)}`;
    } else if (noticeTyp === "EVERY_MONTH") {
      return `${title}-${moment(knockAt).format("DD")}日`;
    } else if (noticeTyp === "EVERY_YEAR") {
      return `${title}-${moment(knockAt).format("MM")}月/${moment(knockAt).format("DD")}日`;
    }
    return title;
  };

  function getZxListEle(msg: MessageToSave) {
    const data = msg.contentExtra?.data;
    // getZxNoticeTypeText(data?.noticeTyp)
    return h("div", { class: "im-app-card chat-cloud-disk" }, [
      h("div", { style: "padding:0px 12px;margin-top:12px" }, [h(Radio, {}), data.title]),
      h("div", { class: "im-card-content" }, [
        h("div", { style: "flex:1; display:flex;flex-direction:column;gap:4px;" }, [
          h("div", `${i18nt("im.msg.task_time")}：${formatTime(data?.mission_at)}`),
          h("div", `${i18nt("im.msg.remind_time")}：${getZxListRemindTimeText(data?.knockAt, data?.mission_at)}`),
        ]),
      ]),
      h(
        "div",
        {
          class: "im-card-btn1",
          onClick: () => {
            useChatExtendStore().showChatDialog("zhixing-manifest", msg);
          },
        },
        i18nt("im.public.detail"),
      ),
    ]);
  }

  function getZxScheduleEle(msg: MessageToSave) {
    const data = msg.contentExtra?.data;
    return h("div", { class: "im-app-card chat-cloud-disk" }, [
      h("div", { class: "im-card-title" }, i18nt("im.msg.scheduleShare")),
      h("div", { class: "im-card-content" }, i18nt("im.msg.scheduleShareTip")),
      h(
        "div",
        {
          class: "im-card-btn1",
          onClick: (e) => {
            e.stopPropagation();
            jumpZhixing({
              to: "schedule",
              atId: data.at_id,
              openId: data.openid,
              create: false,
            });
          },
        },
        i18nt("im.msg.checkSchedule"),
      ),
    ]);
  }

  /**
   * @param msg {
      "openid": "00pqplhc2nim8",
      "user_id": "1wwzk0000wz4x71j0v",
      "title": "欢迎来到全新的另可笔记",
      "content": {
          "attachments": [],
          "delta": "[{\"insert\":{\"image\":\"https://img.kuaiyouyi.com/zhiXing/f03648ed61bb6572be67bd46f8832acb/20230626/11090d791b11b9a1ef50777f2ba00ebd3.jpg\"}},{\"insert\":\"\\n\\n使用另可笔记，管理你的笔记文档与清单规划。随时随地整理、分享笔记，用这些方式来记录你的灵感，同时让你的工作更高效、生活更有序、学习更便捷。\\n\\n\"},{\"insert\":\"新建笔记 高效记录\",\"attributes\":{\"bold\":true}},{\"insert\":\"\\n\",\"attributes\":{\"header\":2}},{\"insert\":\"随时随地记录信息和灵感，提升你的工作、学习、生活效率\\n\\n\"},{\"insert\":\"生成笔记 即时分享\",\"attributes\":{\"bold\":true}},{\"insert\":\"\\n\",\"attributes\":{\"header\":2}},{\"insert\":\"将个人动态生成笔记，让分享更优质\\n\\n\"},{\"insert\":\"新建笔记本 分类清晰\",\"attributes\":{\"bold\":true}},{\"insert\":\"\\n\",\"attributes\":{\"header\":2}},{\"insert\":\"方便笔记归纳分类，设置隐私锁，打造安全专属笔记本\\n\\n\"},{\"insert\":\"新建标签 个性标记\",\"attributes\":{\"bold\":true}},{\"insert\":\"\\n\",\"attributes\":{\"header\":2}},{\"insert\":\"自定义标签，文案及颜色任由发挥\\n\\n\"},{\"insert\":\"新建清单 定时提醒\",\"attributes\":{\"bold\":true}},{\"insert\":\"\\n\",\"attributes\":{\"header\":2}},{\"insert\":\"提前规划个人安排，实现有序进行；设置自定义提醒，避免出现遗漏。\\n\\n\"}]",
          "description": "￼使用另可笔记，管理你的笔记文档与清单规划。随时随地整理、分享笔记，用这些方式来记录你的灵感，同时让你的工作更高效、生活更有序、学习更便捷。新建笔记 高效记录随时随地记录信息和灵感，提升你的工作、学习、生活效率生成笔记 即时分享将个人动态生成笔记，让分享更优质新建笔记本 分类清晰方便笔记归纳分类，设置隐私锁，打造安全专属笔记本新建标签 个性标记自定义标签，文案及颜色任由发挥新建清单 定时提醒提前规划个人安排，实现有序进行；设置自定义提醒，避免出现遗漏。",
          "imageUrl": "https://img.kuaiyouyi.com/zhiXing/f03648ed61bb6572be67bd46f8832acb/20230626/11090d791b11b9a1ef50777f2ba00ebd3.jpg",
          "images": [
              {
                  "name": "5d7f43ed3cc0c7468d2b252a916e8244_exif.jpg",
                  "size": 73921,
                  "type": "jpg",
                  "url": "https://img.kuaiyouyi.com/zhiXing/f03648ed61bb6572be67bd46f8832acb/20230626/11090d791b11b9a1ef50777f2ba00ebd3.jpg"
              }
          ]
      },
      "tags": [],
      "mark": false,
      "draft": false,
      "at_id": "00pqplh8jaozk",
      "create_at": 1693213577,
      "update_at": 1694172790,
      "delete_at": 0,
      "annex_size": 73921
  }
   */
  function getZxNoteDetailEle(msg: MessageToSave) {
    const data = msg.contentExtra?.data;
    return (
      <AppCard
        max-width= '320px'
        onClick={() => {
          useChatExtendStore().showChatDialog("zhixing-note", msg);
        }}
      >
        <AppCardHeader>
          <div class="line-2">{_.isEmpty(data?.title) ? i18nt('zx.note.notitle') : data.title}</div>
        </AppCardHeader>
        <AppCardBody style="overflow:hidden;">
          {data?.content?.imageUrl ? (
            <img src={data?.content?.imageUrl} style="float:right;object-fit:cover;width:64px;height:64px;"></img>
          ) : null}
          <div class="zx-note-detail">{data?.content?.description}</div>
        </AppCardBody>
        <div class="msg-divider"></div>
        <AppCardFooter>
          <AppCardText type="info" font={12}>
            {i18nt("im.public.note")}
          </AppCardText>
        </AppCardFooter>
      </AppCard>
    );
  }

  /**
   * {
      "approveId": 707,
      "staffId": 282,
      "teamLogo": "",
      "approveTitle": "苏小二提交的审批意见必填",
      "teamName": "珠海刘阳测试有限公司",
      "projectName": "珠海刘阳测试有限公司",
      "formValue": [
          {
              "name": "数字",
              "type": "NumberInput",
              "value": "121"
          }
      ],
      "approving_log": [
          {
              "staff_id": 21,
              "log_id": 773
          },
          {
              "staff_id": 35,
              "log_id": 774
          },
          {
              "staff_id": 282,
              "log_id": 776
          }
      ],
      "mustOpinion": 0
  }
   */
  function getApproveShareEle(msg: MessageToSave) {
    const data = msg.contentExtra?.data;
    const formValue = data?.formValue ?? [];

    getAppShareApprovalStatus(msg);
    const approveStatus = data?.apiData?.status;

    const onApproveShareAgree = (e: MouseEvent) => {
      e.stopPropagation();
      if (!data?.apiData?.must_opinion) {
        reqShareApproveAgree(msg);
      } else {
        useChatExtendStore().showApproveDialog("approve-helper", msg, "agree");
      }
    };

    const onApproveShareReject = (e: MouseEvent) => {
      e.stopPropagation();
      if (!data?.apiData?.must_opinion) {
        reqShareApproveReject(msg);
      } else {
        useChatExtendStore().showApproveDialog("approve-helper", msg, "reject");
      }
    };

    /* -1：不可审批，0：待审批，1：已同意，2：已被他人处理，3：已拒绝，4：已转交，5：已被转交，6：已退回，7：已加签，8：发起人已撤销 */
    return (
      <AppCard style="width:320px;" onClick={() => useChatExtendStore().showApproveDialog("approve-helper", msg)}>
        <div style="display:flex;flex-direction:row;justify-content:space-between; gap:8px;margin:12px 0 0;padding:9px 16px 0;">
          {data?.teamLogo ? (
            <img src={data?.teamLogo} width="40" height="40" style="border-radius:20px;" />
          ) : (
            <SvgIcon name="im_avatar_team" style="width:40px;height:40px; color:#488BF0;border-radius:20px;" />
          )}
          <div style="flex:1;">
            <AppCardText style="font-weight:bold;">{data?.teamName}</AppCardText>
            <AppCardText type="info" style="font-size:12px;">
              {data?.projectName}
            </AppCardText>
          </div>
        </div>
        <AppCardBody>
          {formValue.map((item) => (
            <div>
              <span class="im-font2">{item.name}：</span>
              <span>{item.value}</span>
            </div>
          ))}
        </AppCardBody>

        <AppCardFooter>
          {approveStatus === -1 ? <AppCardBtn style="flex:1;" text={i18nt("im.public.detail")} /> : null}
          {approveStatus === 0 ? (
            <AppCardBtn style="flex:1;" text={i18nt("im.msg.refuse")} theme="danger" onClick={onApproveShareReject} />
          ) : null}
          {approveStatus === 0 ? (
            <AppCardBtn style="flex:1;" text={i18nt("im.msg.agree")} onClick={onApproveShareAgree} />
          ) : null}
          {approveStatus === 1 ? <AppCardBtn text={i18nt("im.msg.agreed")} theme="text" style="flex:1;" /> : null}
          {approveStatus === 2 ? <AppCardBtn text={i18nt("im.msg.handled")} theme="text" style="flex:1;" /> : null}
          {approveStatus === 3 ? <AppCardBtn text={i18nt("im.msg.refused")} theme="text" style="flex:1;" /> : null}
          {approveStatus === 4 ? <AppCardBtn text={i18nt("im.msg.transfer")} theme="text" style="flex:1;" /> : null}
          {approveStatus === 5 ? <AppCardBtn text={i18nt("im.msg.transferred")} theme="text" style="flex:1;" /> : null}
          {approveStatus === 6 ? <AppCardBtn text={i18nt("im.msg.back")} theme="text" style="flex:1;" /> : null}
          {approveStatus === 7 ? <AppCardBtn text={i18nt("im.msg.countersign")} theme="text" style="flex:1;" /> : null}
          {approveStatus === 8 ? <AppCardBtn text={i18nt("im.msg.revoke")} theme="text" style="flex:1;" /> : null}
        </AppCardFooter>
      </AppCard>
    );
  }

  //  {
  //     "approveId": 1325,
  //     "approveTitle": "苏小二提交的审批意见必填",
  //     "commentText": "8",
  //     "teamId": "553636462972424192",
  //     "projectId": 0,
  //     "approvedIds": [
  //         "$861"
  //     ],
  //     "initiatorId": "$793",
  //     "commentatorId": "$792",
  //     "commentatorName": "苏一"
  // }
  function getApproveCommentForward(msg: MessageToSave) {
    const data = msg.contentExtra.data;
    const referClass = `chat-refer ${isSenderByCardId(msg) ? "refer-sender" : "refer-receiver"}`;

    const onClickRefer = () => {
      useChatExtendStore().showApproveDialog("approve-comment", msg);
    };

    return h("div", { class: referClass }, [
      h("div", { class: "refer-msg", onClick: onClickRefer }, [
        h("div", { class: "refer-name" }, `${data?.commentatorName} ${i18nt("im.msg.comment")}`),
        h("a", { class: "refer-content" }, data?.approveTitle || ""),
      ]),
      h("div", { class: "refer-divider" }),
      h("div", { class: "refer-reply" }, data?.commentText),
    ]);
  }

  function getTeamsAssistantEle(msg: MessageToSave) {
    switch (msg.contentExtra?.scene) {
      case 5001:
        return getAppTeams_5001_Ele(msg);
      case 5002:
        return getAppTeams_5001_Ele(msg);

      case 5017:
        return getAppTeams_5001_Ele(msg);
      case 5018:
        return getAppTeams_5001_Ele(msg);

      case 5003:
        return getAppTeams_5003_Ele(msg);
      case 5004:
        return getAppTeams_5003_Ele(msg);
      case 5005:
        return <AppTeams5005 data={msg} />;

      case 5006:
        return getAppTeamsTextEle(msg);

      // 入会申请提交
      case 5007:
        return getAppTeamsBusssinessEle(msg);
      // 激活申请提交
      case 5008:
        return getAppTeamsBusssinessEle(msg);
      // 会员过期提醒
      case 5009:
        return getAppTeamsBusssinessEle(msg);
      // 入会申请驳回
      case 5010:
        return getAppTeamsBusssinessEle(msg);
      // 激活申请驳回
      case 5011:
        return getAppTeamsBusssinessEle(msg);
      // 入会、激活申请同意
      case 5012:
        return getAppTeamsBusssinessEle(msg);
      // 会员添加联系人
      case 5013:
        return getAppTeamsBusssinessEle(msg);
      // 会员删除联系人
      case 5014:
        return getAppTeamsBusssinessEle(msg);

      case 5015:
        return getAppTeamsTextEle(msg);
      case 5016:
        return getAppTeamsTextEle(msg);
      case 5020:
        return getAppTeamsTextEle(msg);
      case 5021:
        return getAppTeamsTextEle(msg);
      case 5022:
        return getAppTeamsTextEle(msg);
      // 用户提交会员申请给管理员发送工作通知
      case 5023:
        return getAppTeamsTextEle(msg);
      // 用户提交激活申请给管理员发送工作通知
      case 5024:
        return getAppTeamsTextEle(msg);
      // 成员加入，后端有重复，会发到 APP_TEAMS 和 APP_SECRETARY 中
      case 5025:
        return getAppTeamsTextEle(msg);

      default:
        return getAppTeamsTextEle(msg);
    }
  }

  function getAppTeamsTextEle(msg: MessageToSave) {
    const data = msg.contentExtra?.data;
    return (
      <AppCard style={{ width: "320px", position: "relative" }}>
        <AppCardBody style="word-break:break-all;">{data?.content?.title}</AppCardBody>
      </AppCard>
    );
  }

  // 商协会用户消息场景值：5007、5008、5009、5010、5011、5012、5013、5014
  function getAppTeamsBusssinessEle(msg: MessageToSave) {
    const data = msg.contentExtra?.data;
    const logo = data?.header?.team?.logo;
    const team = data?.header?.team?.name;
    const link = data?.extend?.link || data.scene === 5012;
    const jumpH5 = () => {
      if (data.scene === 5012) {
        goToDigitalPlatform_member(data?.extend?.teamId);
      } else {
        // const links = link.replace('https://operation-manage-square-dev.ringkol.com/','http://192.168.31.21:5173/');
        console.log("789", link);
        jumpH5WithLink(link, data?.extend?.teamId);
      }
    };

    return (
      <AppCard>
        <AppCardHeader style="display:flex;flex-direction:row;justify-content:space-between;">
          {i18nt("im.public.biz")}
        </AppCardHeader>
        <AppCardBody>
          {data?.scene !== 5009 ? null : (
            <div style="display:flex;flex-direction:row;justify-content:space-between;margin-bottom: 8px;">
              {logo ? (
                <img width="24" height="24" src={logo} />
              ) : (
                <SvgIcon name="im_avatar_team" style="width:24px;height:24px; color:#488BF0" />
              )}
              <div style="flex:1;margin-left:4px;width:0;overflow:hidden;text-overflow: ellipsis;white-space:nowrap;">
                {team}
              </div>
            </div>
          )}
          {data?.content?.title}
        </AppCardBody>
        {link ? (
          <AppCardFooter>
            <Button class="w-full fw-600"  variant="outline" onClick={jumpH5}>
              {i18nt("im.public.detail")}
            </Button>
          </AppCardFooter>
        ) : null}
      </AppCard>
    );
  }

  /**
   * @param msg 场景值 5001、5002认证过期 的消息 5017认证同意 5018认证拒绝
   */
  function getAppTeams_5001_Ele(msg: MessageToSave) {
    const data = msg.contentExtra?.data;
    const goAuth = i18nt("im.msg.goAuth");
    const checkAuth = i18nt("im.msg.checkAuth");
    const btnText = { 5001: checkAuth, 5002: checkAuth, 5017: checkAuth, 5018: checkAuth }[data?.scene];
    const { region, type, teamId } = data?.extend;
    // 类型 0：其他， 1：企业， 2:商协会，3：个体户
    const typeMap = { 0: "OTHER", 1: "ENTERPRISE", 2: "BUSINESS_ASSOCIATION", 3: "INDIVIDUAL", 4: "GOVERNMENT" };
    return (
      <AppCard style={{ width: "320px", position: "relative" }}>
        <AppCardBody>{data?.content?.title}</AppCardBody>
        <AppCardFooter style="margin-top:12px;">
          <AppCardBtn
            text={btnText}
            style="flex:1;"
            onClick={() => {
              useMessageStore().changeOrgAuthDialog(true, { teamId, orgType: typeMap[type], region });
            }}
          />
        </AppCardFooter>
      </AppCard>
    );
  }

  function getAppTeams_5003_Ele(msg: MessageToSave) {
    const data = msg.contentExtra?.data;
    return (
      <AppCard style={{ width: "320px", position: "relative" }}>
        <AppCardBody>{data?.content?.title}</AppCardBody>
        {data.scene !== 5004 ? (
          <AppCardFooter onClick={() => openAddressbookTeam(data?.extend?.teamId)}>
            <AppCardBtn text={i18nt("im.public.detail")} theme="text" style="flex:1;" />
          </AppCardFooter>
        ) : null}
      </AppCard>
    );
  }

  const getMeetingStartEle = (msg: MessageToSave) => {
    const text = getMeetingStartText(msg);
    return h("div", { class: "meeting-tip" }, text);
  };

  const getMeetingEndEle = (msg: MessageToSave, myCard?) => {
    const text = getMeetingEndText(msg, myCard);
    return h("div", { class: "meeting-tip" }, text);
  };

  function getMeetingEndTimeEle(msg: MessageToSave) {
    const data = msg.contentExtra?.data;
    const text = getMeetingTimeText(data.call_time);
    return h("div", { class: "meeting-tip" }, text);
  }

  function getSeverMsgEle(msg: MessageToSave, conversation: ConversationToSave) {
    const data = msg.contentExtra?.data;
    const onClick = (item: ReturnType<typeof getParsedServerTextMsg>[number]) => {
      if (item.index !== undefined) {
        const myCardId = useMessageStore().chatingSession.myCardId;
        const cardId = data?.card_ids?.[item?.index];
        ipcRenderer.invoke("identity-card", { cardId, myId: myCardId });
      }
    };

    const displayInfo = getParsedServerTextMsg(msg);
    // 是否是刚成为联系人, 临时前端处理
    const isFastReply = /已成为联系人|已成為聯繫人/gi.test(data?.text);
    return h(
      "div",
      { style: "width:100%; text-align:center;font-size: 14px;font-weight: 400;line-height: 22px;color:#828DA5;" },
      <div>
        <div>
          {displayInfo.map((item, index) => {
            if (item.isVar) {
              const cardVar = data?.card_ids?.[item.index];
              const text = conversation.myCardId === cardVar ? i18nt("im.public.you") : item.str;
              return h("a", { class: "text-link", onClick: () => onClick(item) }, text);
            }
            return item.str;
          })}
        </div>
        {isFastReply ? (
          <div class="mt-[20px] mr-[-46px]">
            {/* @ts-ignore */}
            <MyVCardType />
          </div>
        ) : null}
      </div>,
    );
  }

  const getSquarePreviewEle = (msg: MessageToSave) => {
    // publishId: string,
    // squareName: string
    const data = msg.contentExtra?.data;
    const onClick = async () => {
      const postId = data?.publishId;

      const isOpened = await checkSquareIsOpened();
      if (!isOpened) return;

      // 预检测动态是否可见
      const [err] = await to < AxiosResponse, AxiosError< { reason: string } >> (getPostPublishVerifyPreview(postId));
      if (err) {
        const errReason = err.response.data.reason;
        if (
          [ErrorResponseReason.SquareDeleted, ErrorResponseReason.PostDeleted].includes(errReason as ErrorResponseReason)
        ) {
          await MessagePlugin.warning({ offset: [0, 24], content: i18nt("im.msg.deleteMoment") });
          return;
        }
        if (errReasonMap[errReason]) {
          await MessagePlugin.warning({ offset: [0, 24], content: errReasonMap[errReason] });
        }
        return;
      }
      useChatExtendStore().showChatDialog("square-preview", msg);
    };
    return (
      <AppCard style="padding-bottom:12px;width:320px;" onClick={onClick}>
        <AppCardHeader theme="secondary" style="font-size:16px;">
          <img src={SquareIcon} width="24" style="margin-right:4px;" />
          {i18nt("im.public.square")}
        </AppCardHeader>
        <AppCardBody style="display: flex;flex-direction: row;justify-content: space-between;">
          <AppCardText>{`【${i18nt("im.public.preview")}】`}</AppCardText>
          <div style="position:relative;width: 60px;height:60px;background:#f6f6f6;border-radius:8px; display: flex;align-items: center;justify-content: center;">
            <svg class="svg-size24">
              <use href="#icon-logo"></use>
            </svg>
            <div style="position: absolute;top: 0; right: 0; width:24px;height: 24px;border-radius: 0px 10px 0px 10px;background-color: rgba(0, 0, 0, 0.5);">
              <img style="margin: 1px 0 0 2px;" src={SquareLink} width="20" />
            </div>
          </div>
        </AppCardBody>
        <AppCardText type="info" font={12} style="margin: 0 12px 12px;">
          {i18nt("im.msg.momentFrom", [data?.squareName])}
        </AppCardText>
      </AppCard>
    );
  };

  import { isCloudDiskUrl } from "@renderer/utils/myUtils";
  import { getOpenid, setAccountAuthRouters } from "@renderer/utils/auth";
  import { getIDCardSquareInfo } from "@/api/square/common";
  import { DiskFileList, fileDetail } from "@renderer/api/cloud";
  import { goSquareModule } from "@/views/square/utils/business";
  import _ from "lodash";
  import { BAIDU_AK } from "@renderer/components/common/map/utils";
import { EmojiObject } from "@renderer/assets/im/emoji";
  // 检测自己是否开通广场号
  const checkSquareIsOpened = async (squareId?: string) => {
    const [myErr, myRes] = await to(getIDCardSquareInfo({ open_id: getOpenid() }));
    if (
      (myErr && myErr?.response?.data?.reason === ErrorResponseReason.SquareNotOpen) ||
      (myRes && !myRes.data?.opened)
    ) {
      const squareConfirmDia = DialogPlugin.confirm({
        header: i18nt("account.tip"),
        body: i18nt("identity.enableSquareTip"),
        confirmBtn: i18nt("identity.confirm"),
        cancelBtn: i18nt("identity.cancel"),
        closeBtn: null,
        closeOnOverlayClick: true,
        theme: "info",
        onConfirm: async () => {
          squareConfirmDia.destroy();
          const query = { redirect: '', id: '', from: "outer", needShowDialog: true };
          if (squareId) {
            query.redirect = '/square/info';
            query.id = squareId;
          }
          await goSquareModule(query);
        },
        onClose: () => {
          squareConfirmDia.hide();
        },
      });
      return false;
    }
    return true;
  };

  /**
   * 跳转广场号主页
   * @param data {squareId:string}
   */
  const SquareOnClick = async (data) => {
    console.log(data);
    const isOpened = await checkSquareIsOpened(data.squareId);
    if (!isOpened) return;

    const query = { redirect: "/square/info", id: data.squareId as string, from: "outer", needShowDialog: true };
    await goSquareModule(query);
  };
  const getSquareIdcardEle = (msg: MessageToSave) => {
    const data = msg.contentExtra?.data;
    const sOnclick = () => {
      SquareOnClick(data);
    };
    const img = 'https://kuaiyouyi.oss-cn-shenzhen.aliyuncs.com/square/172238828994927074432.png';
    const avatar = (data?.avatar || data.certStatus) ? data?.avatar : img;
    return (
      <AppCard style="min-width:220px; max-width:320px;" class="chat-square" onClick={sOnclick}>
        <AppCardBody>
          <div style="display:flex;flex-direction:row;gap: 8px;">
            <div style="position: relative;">
              <ChatAvatar src={avatar || img} alt={data?.name}></ChatAvatar>
            </div>
            <div>
              <AppCardText>{data?.name}</AppCardText>
              <AppCardText type="info" font={12}>
                {i18nt("im.msg.momentId", [data?.pid])}
              </AppCardText>
            </div>
          </div>
          <div style="display:block;height:1px;background-color:#e3e6eb;margin:8px 0;" />
          <AppCardText type="info" font={12}>
            {i18nt("im.public.square_id", data?.pid)}
          </AppCardText>
        </AppCardBody>
      </AppCard>
    );
  };

  const getBizOpportunityEle = (msg: MessageToSave) => {
    const data = msg.contentExtra?.data;
    // const pic = data?.goods_image?.[0];
    const pic = data?.images?.[0]?.file_name;
    const onClick = () => {
      // useChatExtendStore().showChatDialog("biz-opportunity", msg);
      // 脱离im直接打开独立窗口详情
      ipcRenderer.invoke(
        "niche-read",
        JSON.stringify({
          uuid: data?.uuid || data?.extend?.uuid,
          teamId: data?.extend?.team_id,
          from: "im",
        }),
      );
    };
    return (
      <AppCard style="width:320px;" class="chat-square" onClick={onClick}>
        <AppCardHeader>
          <svg class="svg-size20" style="margin-right:12px;">
            <use href="#business"></use>
          </svg>
          {i18nt("im.msg.biz")}
        </AppCardHeader>
        <AppCardBody>
          <AppCardText>{data?.title}</AppCardText>
          {pic ? (
            <div style="margin-top:12px;">
              <img style="max-width:296px;max-height: 320px;" src={pic}></img>
            </div>
          ) : null}
        </AppCardBody>
        <AppCardFooter>
          <AppCardText type="info">
            {i18nt("im.msg.bizFrom", [data?.square_data?.name ?? data?.square_name ?? ""])}
          </AppCardText>
        </AppCardFooter>
      </AppCard>
    );
  };

  const getActivityCardEle = (msg: MessageToSave, ctxProps) => {
    const data = msg.contentExtra?.data;

    const isDeleted = data?.apiData?.isDeleted;

    // 分享没有操作按钮，直接查看,
    const onClickDetail = async () => {
      if (isDeleted) {
        return MessagePlugin.warning({
          content: i18nt("activity.activity.hasDelete"),
          placement: "center",
        });
      }
      let row = data;
      let msgStore = useMessageStore();
      let item = msgStore.allMembers.get(msgStore.chatingSession.localSessionId).get(msgStore.chatingSession.myCardId);
      console.log(item);
      console.log(data);
      ipcRenderer.invoke("create-dialog", {
        url: `layoutActivity/activityDetailLayout/${row.id}?title=${encodeURIComponent(
          row.subject,
        )}&teamId=${encodeURIComponent(item.teamId)}&myCardId=${encodeURIComponent(item.cardId)}`,
        opts: {
          x: 50,
          y: 50,
          width: 1296,
          minWidth: 1296,
          height: 720,
        },
      });
    };

    return (
      <AppCard style="position:relative;width:360px;" maxWidth={props.from === 'history' ? "326px" : '360px'}>
        <AppCardHeader style="font-size: 16px;font-weight: 400;line-height: 24px;color: var(--brand-kyy_color_brand_default, #4D5EFF);">
          {data.categoryTitle || i18nt("im.tools.activity")}
        </AppCardHeader>
        <AppCardBody onClick={onClickDetail}>
          <img src={data.assetUrl} width="328" style="margin-right:4px;height: 185px;" />
          <AppCardText type="main" style="margin: 8px 0;width:100%;display:flex;gap:8px;">
            <div
              className={"line-2"}
              style="font-size: 14px;font-weight: 600;color: var(--text-kyy-color-text-1, #1A2139);"
            >
              {data.subject || "--"}
            </div>
          </AppCardText>
          <AppCardText type="info" style="margin: 8px 0;width:100%;font-size: 14px;line-height: 22px;">
            <span style="margin-right: 16px;color: var(--text-kyy-color-text-3, #828DA5);">
              {i18nt("im.msg.activityTime")}
            </span>
            <span style="color: var(--text-kyy-color-text-1, #1A2139);">
              {data?.duration?.startTime
                ? `${moment.unix(data.duration.startTime).format("MM-DD HH:mm")} ~ ${moment
                  .unix(data.duration.endTime)
                  .format("MM-DD HH:mm")}`
                : "--"}
            </span>
          </AppCardText>
          <AppCardText type="info" style="margin: 8px 0;width:100%;font-size: 14px;line-height: 22px;">
            <span style="margin-right: 16px;color: var(--text-kyy-color-text-3, #828DA5);">
              {i18nt("im.msg.activityAddr")}
            </span>
            <span style="color: var(--text-kyy-color-text-1, #1A2139);">
              {data?.location?.title || data?.location?.address || "--"}
            </span>
          </AppCardText>
        </AppCardBody>
        <AppCardFooter style="padding:16px 0 0 0;margin: 0 16px 16px 16px;border-top: 1px solid #ECEFF5">
          <AppCardBtn
            text={i18nt("im.public.detail")}
            style="color: var(--color-button_border-kyy_color_buttonBorder_text_default, #516082);font-size: 14px;font-weight: 600;line-height: 22px;border-radius: var(--radius-kyy_radius_button_s, 4px);border: 1px solid var(--color-button_border-kyy_color_buttonBorder_border_dedault, #D5DBE4);padding:5px 16px;"
            onClick={() => onClickDetail()}
          />
        </AppCardFooter>
      </AppCard>
    );
  };

  // 帮助中心
  const getHelpCenter = (msg: MessageToSave) => {
    const data = msg.contentExtra?.data;
    const onClick = () => { };

    return (
      <AppCard style="position:relative;width:495px;" maxWidth="495px" onClick={onClick}>
        <AppCardHeader>{data.document_name}</AppCardHeader>
        <AppCardBody>
          <AppCardText type="main" style="margin: 8px 0;width:100%;display:flex;gap:8px;">
            <div
              style={`
              overflow: hidden;
              display: -webkit-box;
              text-overflow: ellipsis;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              font-size: 14px;
              font-weight: 600;
              color: var(--text-kyy-color-text-1, #1A2139);
            `}
            >
              {data.tags_list[0].text_content}
            </div>
          </AppCardText>
        </AppCardBody>
        <AppCardFooter>
          <img src={data.cover} style={"width: 100%;height: 250px;display: block;"} />
        </AppCardFooter>
        <div style={`color:#828DA5;padding:0 12px 12px 12px;`}>{i18nt("im.msg.help")}</div>
      </AppCard>
    );
  };

  // ---------------------------------------------------
  //                       utils
  // ---------------------------------------------------

  async function openUrl(msg, item) {
    if (isCloudDiskUrl(item.str)) {
      useChatExtendStore().showDiskLink(msg, item.str);
      console.log(item);
    } else if (/\.com\/square\/square-detail/gi.test(item.str)) {
      // 广场号链接跳转
      const { data } = await parseUrl({ url: item?.str, urlType: "SQUARE_SHARING" });
      const squareId = data?.squareSharing?.decodeToken?.squareId;
      squareId && SquareOnClick({ squareId });
    } else if (/\.com\/square\/post-detail/gi.test(item.str)) {
      const isOpened = await checkSquareIsOpened();
      if (!isOpened) return;

      try {
        const url = item.str;
        const urlParams = new URLSearchParams(url.split("?")[1]);
        const postShareToken = urlParams.get("post_share_token");
        const [err, data] = await to(
          parseSquareShareUrl({
            share_token: postShareToken,
          }),
        );
        if (err) {
          throw err;
        } else {
          const post_id = data?.data?.post?.post?.id;
          const invisible = data?.data?.post?.invisible;
          const deleted = data?.data?.post?.deleted;
          if (deleted) {
            MessagePlugin.warning({ offset: [0, 24], content: i18nt("im.msg.deleteMoment") });
            return;
          }
          if (invisible) {
            MessagePlugin.warning({ offset: [0, 24], content: i18nt("square.post.invisible") });
            return;
          }
          if (!post_id) {
            // 这条动态已删除
            MessagePlugin.warning({ offset: [0, 24], content: i18nt("im.msg.deleteMoment") });
          } else {
            ctxProps?.onShowPostDetailDialogId?.(post_id);
          }
        }
      } catch (err) {
        // openUrlByBrowser(item?.str);
        console.error("parseSquareShareUrl", err);
      }
      // 6644
    } else {
      openUrlByBrowser(item?.str);
    }
  }

  function openUrlByBrowser(url: string) {
    // 新增数美链接检查，跳转h5检测页/safeCheck
    const h5Getway = "h5";
    const h5Path = "safeCheck";
    const openid = getOpenid();
    const h5Link = `${getBaseUrl(h5Getway)}/${h5Path}?link=${encodeURIComponent(url)}&openid=${encodeURIComponent(
      openid,
    )}`;
    try {
      shell.openExternal(h5Link);
    } catch (error) { }
  }

  function openAddressbookTeam(teamId: string) {
    useContactsStore().setOrgId(teamId);
    const query = { from: "mainCard", teamId, departmentId: "-1" };
    ipcRenderer.send("update-nume-index", { value: "address_book", query });
  }

  async function openIdentityCard(msg, item) {
    const cardId = msg.contentExtra?.data?.atInfo?.[item?.atIndex || 0]?.cardId;
    const myId = useMessageStore().chatingSession?.myCardId;
    openIdentityCardById(myId, cardId);
  }

  function openIdentityCardById(mycard: string, targetCard: string) {
    if (mycard && targetCard) {
      ipcRenderer.invoke("identity-card", { cardId: targetCard, myId: mycard });
    }
  }

  function openPreWin(fileId: string, fileExtention: string) {
    previewFileWithErrorTips(fileId, fileExtention);
  }
  function handleMouseSelect(event) {
    event.preventDefault();

    const selection = window.getSelection();
    let selectedText = '';
    let selectedHtml = '';

    for (let i = 0; i < selection.rangeCount; i++) {
      const range = selection.getRangeAt(i);
      const rangeText = range.toString();

      // 处理图片 alt
      range.cloneContents().querySelectorAll('img').forEach((img) => {
        selectedText += `${img.alt} `;
      });
      selectedText += rangeText;

      // 获取 HTML
      const div = document.createElement('div');
      div.appendChild(range.cloneContents());
      selectedHtml += div.innerHTML;
    }

    selectedText = selectedText.trim();
    selectedHtml = selectedHtml.trim();

    // 写入剪贴板
    navigator.clipboard.write([
      new window.ClipboardItem({
        'text/plain': new Blob([selectedText], { type: 'text/plain' }),
        'text/html': new Blob([selectedHtml], { type: 'text/html' })
      })
    ]).then(() => {
      console.log('Copied to clipboard:', selectedText, selectedHtml);
    }).catch((err) => {
      console.error('Failed to copy to clipboard:', err);
    });
  }
  function getReferDisplayInfo(msg: MessageToSave) {
    const msgStore = useMessageStore();

    const { referMsgUserId = null, referMsg = null } = msg.content;
    let extra: MessageToSave["contentExtra"] = null;
    try {
      extra = JSON.parse(referMsg.extra);
    } catch (error) {
      console.info("引用消息错误: ", msg);
      console.error(error);
    }

    let image = null;
    if (extra?.contentType === "image" || extra?.contentType === "emoji_image") {
      const style = getImageStyle(extra?.data?.width, extra?.data?.height, { max: 160, min: 140 });
      const { thumbnail } = getMsgImageSrc(extra?.data);
      image = h("img", { src: thumbnail, class: "refer-img", style });
    } else if (extra?.contentType === "video") {
      const style = getImageStyle(extra?.data?.width, extra?.data?.height, { max: 160, min: 140 });
      image = h("div", { style: "position: relative;width: max-content;" }, [
        h("img", { src: extra?.data?.videoImageUrl, class: "refer-img", style }),
        h("img", { src: PlayIcon, style: "position:absolute;left:50%; top:50%;transform:translate(-50%, -50%);" }),
      ]);
    }

    let text: string = image ? null : getReferMessagePreview(extra);
    const conversationId = msg.conversationType === 1 ? msg.localSessionId : msg.targetId;
    const members = msgStore.allMembers.get(conversationId);
    const member = members?.get(extra?.senderId);

    if (member) {
      return { text, image, name: member.staffName || member.nickname, type: extra?.contentType };
    }

    members.forEach((value) => {
      if (referMsgUserId === value.openId) {
        return { text, image, name: value.staffName || value.nickname, type: extra?.contentType };
      }
    });

    return { text, image, type: extra?.contentType };
  }

  async function previewMergedFile(msg: MessageToSave) {
    const extra = msg.contentExtra.data as IMessageMergedExtra;
    ipcRenderer.invoke("merged-message", { fileUrl: extra.fileUrl });
  }
</script>

<style lang="less">
  .proxy-box {
    max-width: 360px;
    border-radius: 8px;
    border: 1px solid #ECEFF5;
    background: #FFF;
  }

  .proxy-header {
    background: #E0F2E5;
    padding: 9px 16px;
    color: #499D60;
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
  }

  .proxy-content {
    padding: 16px;
    color: #1A2139;
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
  }

  .proxy-btn {
    height: 32px;
    border-radius: 4px;
    border: 1px solid #D5DBE4;
    background: #FFF;
    margin: 0 16px 16px;
    color: #516082;
    text-align: center;
    font-size: 14px;
    font-weight: 400;
    line-height: 32px;
    cursor: pointer;
  }

  .proxy-btn:hover {
    border: 1px solid var(--lingke-brand-hover, #707EFF) !important;
    background: var(--lingke-brand-12, rgba(76, 94, 255, 0.12)) !important;
    color: var(--color-button-border-kyy-color-button-border-text-hover, #707EFF) !important;
  }

  .chat-text {
    * {
      position: relative;
    }
    padding: 8px 12px;
    border-radius: 0px 8px 8px 8px;
    border: 1px solid #eceff5;
    background-color: @kyy_white;
    color: #516082;
    font-size: 14px;
    line-height: 22px;
    word-break: break-all;
    word-wrap: break-word;
    white-space: pre-line;
    flex-grow: 0;
    flex-shrink: 1;
    flex-basis: auto;
    transition: all 0.3s;
    max-width: 320px;
    & img {
      margin: 0;
      padding: 0;
      border: 0;
      width: auto;
      height: 28px;
      box-sizing: border-box;
      vertical-align: middle;
    }
  }

  .chat-content[data-tool="false"] .chat-text {
    max-width: 600px;
  }

  .message-content[data-sender="true"] {

    .chat-text,
    .chat-file {
      border-radius: 8px 0 8px 8px;
    }

    .chat-text,
    .meeting-msg {
      background-color: #e1eaff;
      border: 1px solid var(--divider-kyy_color_divider_light, #eceff5);
    }
  }

  .meeting-tip {
    width: 100%;
    text-align: center;
    font-size: 14px;
    color: var(--text-kyy_color_text_3, #828da5);
  }

  .text-link {
    color: #4d5eff;
    cursor: pointer;
    // margin: 4px;

    &:hover {
      //   color: @kyy_brand_7;
    }
  }

  .chat-image {
    min-width: 40px;
    max-width: 280px;
    min-height: 40px;
    max-height: 280px;
    overflow: hidden;
  }

  .chat-image-ele {
    min-width: 40px;
    min-height: 40px;
    max-width: 280px;
    max-height: 280px;
    overflow: hidden;
    position: relative;
    border-radius: 8px;

    & img {
      width: 100%;
      object-fit: contain;
    }
  }

  .image-long-tag {
    color: var(--kyy_color_tag_text_gray, #516082);
    background: var(--kyy_color_tag_bg_gray, #eceff5);
    border-radius: 4px;
    text-align: center;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    position: absolute;
    top: 4px;
    padding: 0px 4px;
    z-index: 1;
  }

  .chat-video-img {
    object-fit: cover;
    display: block;
  }

  .chat-voice {
    padding: 8px 12px;
    border-radius: 4px;
    border: 1px solid @kyy_gray_3;
    background-color: @kyy_white;
    color: @kyy_font_1;
    font-size: 14px;
    line-height: 22px;
    word-break: break-all;
    word-wrap: break-word;
    white-space: pre-wrap;
    cursor: pointer;

    &-player {
      display: flex;
      flex-direction: row;
      align-items: center;
      min-width: 130px;
      max-width: 300px;
    }

    .t-slider__track {
      background-color: #2069e3;
    }

    .t-slider__track,
    .t-slider__rail {
      height: 2px;
    }

    & .t-slider__button {
      border: none;
      background-color: #c7e3ff;
    }
  }

  .chat-location {
    border-radius: 8px;
    max-width: 240px;
    overflow: hidden;
    cursor: pointer;
    // border: 1px solid var(--divider-kyy_color_divider_light, #ECEFF5);

    .address {
      background-color: @kyy_white;
      padding: 12px 8px;
      vertical-align: middle;
      font-size: 14px;
      font-weight: 400;
      line-height: 22px;
      border-radius: 8px 8px 0 0;
      border-left: 1px solid var(--divider-kyy-color-divider-light, #eceff5);
      border-top: 1px solid var(--divider-kyy-color-divider-light, #eceff5);
      border-right: 1px solid var(--divider-kyy-color-divider-light, #eceff5);
    }

    .address-title {
      color: var(--text-kyy_color_text_2, #516082);
      display: flex;
    }

    .address-detail {
      color: var(--text-kyy_color_text_3, #828da5);
    }

    &[data-sender="true"] {
      border-radius: 8px 0 8px 8px;

      .address {
        border-radius: 8px 0 0 0;
      }
    }
  }

  .right .chat-text {
    border-radius: 8px 0 8px 8px;
    border: 1px solid var(--divider-kyy-color-divider-light, #eceff5);
    background: var(--bg-kyy-color-bg-list-foucs, #e1eaff);
  }

  .chat-refer {
    padding: 8px 12px;
    border-radius: 4px;
    border: 1px solid @kyy_gray_3;
    width: 320px;

    .refer-msg {
      padding-left: 8px;
      border-left: 2px solid @kyy_brand_5;
      color: @kyy_font_2;
    }

    .refer-name {
      font-size: 12px;
      line-height: 20px;
    }

    .refer-content {
      font-size: 14px;
      line-height: 22px;
      word-break: break-all;
    }

    .refer-divider {
      margin: 8px 0;
      width: 100%;
      height: 1px;
      background-color: @kyy_gray_3;
    }

    .refer-reply {
      font-size: 14px;
      line-height: 22px;
      color: @kyy_font_1;
      word-break: break-all;
      word-wrap: break-word;
      white-space: pre-wrap;
    }

    .refer-img {
      min-width: 40px;
      min-height: 40px;
      max-height: 240px;
      max-width: 210px;
    }
  }

  .refer-sender {
    background-color: @kyy_brand_1;
  }

  .refer-receiver {
    background-color: @kyy_white;
  }

  .chat-merged {
    font-size: 14px;
    line-height: 22px;
    min-width: 264px;
    max-width: 360px;
    padding: 8px 12px;
    border-radius: 0 8px 8px 8px;
    border: 1px solid var(--divider-kyy_color_divider_light, #eceff5);
    background: var(--bg-kyy_color_bg_light, #fff);
    overflow: hidden;

    .merged-title {
      vertical-align: center;
      color: var(--text-kyy_color_text_2, #516082);
      font-size: 14px;
      font-weight: 400;
      line-height: 22px;
      display: flex;
      align-items: center;
      gap: 4px;

      &::before {
        content: "";
        display: block;
        border-radius: 1px;
        width: 2px;
        height: 14px;

        background: var(--success-kyy-color-success-default, #62bf7c);
      }
    }

    .merged-divider {
      margin: 8px 0;
      width: 100%;
      height: 1px;
      background: var(--divider-kyy_color_divider_light, #eceff5);
    }

    .merged-data {
      color: var(--text-kyy_color_text_3, #828da5);
      font-size: 14px;
      font-weight: 400;
      line-height: 22px;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    &[data-sender="true"] {
      border-radius: 8px 0 8px 8px;
      background: var(--bg-kyy_color_bg_list_foucs, #e1eaff);

      .merged-divider {
        background: var(--divider-kyy_color_divider_deep, #d5dbe4);
      }
    }
  }

  .chat-idcard {
    width: 320px;
    background-color: @kyy_white;
    border-radius: 4px;
    border: 1px solid @kyy_gray_3;
    overflow: hidden;
    font-size: 14px;
    line-height: 22px;
    padding: 16px 16px 12px 16px;

    img::before {
      content: attr(alt);
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 14px;
      line-height: 44px;
      background-color: @kyy_cyan_6;
      color: @kyy_white;
    }

    & .divider {
      background-color: @kyy_gray_3;
      height: 1px;
      margin: 12px 0;
    }

    .user-name {
      color: @kyy_font_1;
      margin-bottom: 2px;
    }

    .team-name {
      color: @kyy_font_3;
    }

    .card-name {
      color: @kyy_font_3;
    }
  }

  .im-app-card {
    background-color: @kyy_white;
    border-radius: 4px;
    border: 1px solid @kyy_gray_3;
    overflow: hidden;
    font-size: 14px;
    line-height: 22px;
    width: 320px;
    color: @kyy_font_1;

    .im-card-title {
      box-sizing: border-box;
      width: 319px;
      padding: 8px 12px 6px 12px;
      background-color: @kyy_brand_1;
      color: @kyy_brand_6;
      display: flex;
      line-height: 35px;
      flex-direction: row;
      justify-content: flex-start;
      align-items: center;
      gap: 8px;
      user-select: none;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .im-card-content {
      padding: 16px 12px;
    }

    .im-card-btn1 {
      margin: 0px 12px 16px;
      border-radius: 4px;
      line-height: 32px;
      display: block;
      border: 1px solid @kyy_brand_6;
      color: @kyy_brand_6;
      text-align: center;
      cursor: pointer;
      user-select: none;
    }

    .im-card-btn1:hover {
      opacity: 0.8;
    }

    .im-font2 {
      color: @kyy_font_2;
    }

    .im-card-action {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      margin: 0px 12px 16px;
      gap: 8px;
    }

    .im-card-btn {
      flex: 1;
      border-radius: 4px;
      line-height: 32px;
      display: block;
      border: 1px solid @kyy_brand_6;
      color: @kyy_brand_6;
      text-align: center;
      cursor: pointer;
      user-select: none;
    }
  }

  .chat-cloud-disk {
    .cloud-file {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      gap: 6px;

      div {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }

  .chat-approve {
    .msg-approve-title {
      font-size: 16px;
      line-height: 24px;
      color: @kyy_font_1;
      padding: 8px 12px;
    }

    .msg-approve-title-text {
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    .msg-approve-title[data-approve-status="0"] {
      background-color: @kyy_brand_1;
      color: @kyy_brand_6;
    }

    .msg-approve-title[data-approve-status="1"] {
      background-color: @kyy_green_1;
      color: @kyy_green_6;
    }

    .msg-approve-title[data-approve-status="2"] {
      background-color: @kyy_red_1;
      color: @kyy_red_6;
    }

    .msg-approve-title[data-approve-status="3"] {
      background-color: @kyy_brand_1;
      color: @kyy_brand_6;
    }

    .msg-approve-btn1 {
      margin: 0px 12px 16px;
      border-radius: 4px;
      line-height: 32px;
      display: block;
      border: 1px solid @kyy_gray_3;
      color: @kyy_font_1;
      text-align: center;
      cursor: pointer;
      user-select: none;
    }

    .msg-approve-btn2 {
      border: 1px solid @kyy_red_6;
      color: @kyy_red_6;
    }

    .msg-approve-btn3 {
      background-color: @kyy_brand_6;
      color: @kyy_white;
    }

    .msg-approve-btn2,
    .msg-approve-btn3 {
      flex: 1;
      text-align: center;
      line-height: 32px;
      border-radius: 4px;
      cursor: pointer;
    }

    .msg-approve-btn1:hover,
    .msg-approve-btn2:hover,
    .msg-approve-btn3:hover {
      opacity: 0.8;
    }
  }

  .chat-square {
    .square-publish-header {
      color: @kyy_orange_6;
      background-color: @kyy_orange_1;
    }

    .square-btn1 {
      color: @kyy_font_1;
      border: 1px solid @kyy_gray_3;
    }

    .square-btn2 {
      color: @kyy_font_4;
      border: 1px solid @kyy_gray_3;
    }

    .square-cer {
      position: absolute;
      width: 20px;
      height: 20px;
      bottom: 0;
      right: 0;
    }
  }

  .zx-note-detail {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    overflow: hidden;
  }

  .msg-divider {
    height: 1px;
    margin: 12px;
    background-color: @kyy_gray_3;
  }

  :deep(.t-progress__info) {
    display: inline-block;
    text-align: right;
    width: 30px;
  }

  .app-card-btn {
    min-width: 88px;
    min-height: 32px;
    max-height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 16px;
    border-radius: var(--radius-kyy_radius_button_s, 4px);
    color: #4d5eff;
    border: 1px solid currentColor;
    background-color: #eaecff;

    &:hover {
      color: #707eff;
      background-color: #dbdfff;
    }

    &:active {
      color: #3e4cd1;
      background-color: #dbdfff;
    }
  }

  .app-card-btn-nd {
    min-width: 88px;
    min-height: 32px;
    max-height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 16px;
    border-radius: var(--radius-kyy_radius_button_s, 4px);
    border: 1px solid var(--color-button_border-kyy_color_buttonBorder_border_dedault, #D5DBE4);
    color: var(--color-button_border-kyy_color_buttonBorder_text_default, #516082);
    text-align: center;
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px;

    /* 157.143% */
    &:hover {
      color: #707eff;
      background-color: #dbdfff;
    }

    &:active {
      color: #3e4cd1;
      background-color: #dbdfff;
    }
  }

  .app-card-btn-waring {
    color: #d54941;
    background: #fdf5f6;

    &:hover {
      color: #d54941;
      background: #f7d5db;
    }

    &:active {
      color: #ae264e;
      background: #f7d5db;
    }
  }

  .app-card-divider {
    display: block;
    padding: 0 16px 16px;

    &::before {
      content: "";
      display: block;
      height: 1px;
      background-color: #eceff5;
    }
  }
</style>
