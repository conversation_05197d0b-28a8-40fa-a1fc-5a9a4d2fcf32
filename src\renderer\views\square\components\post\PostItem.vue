<template>
  <div class="trends-item">
    <img
      v-if="postItem.stickOnTop && !hideTop"
      src="@/assets/square/top-banner.png"
      alt=""
      class="top-banner"
    >

    <div class="right-content" :class="{ 'mt-16': data.stickOnTop }">
      <template v-if="goDetail">
        <div class="flex flex-center gap-16">
          <SquareAvatar
            v-if="goDetail"
            class="avatar"
            :square="square"
            size="40px"
            @click.stop="clickHead"
          />

          <div class="flex-1">
            <div class="top">
              <div class="flex-y-center flex-1 w-0">
                <SquareCertIcon :square="square" class="mr-4" />
                <div class="name" @click.stop="clickHead">{{ square?.remark || square?.name }}</div>
              </div>
              <div v-if="!hideMoreNav" class="nav" @click.stop="emit('click-more', postItem)">
                <template v-if="square?.timelineNewPosts">
                  <div v-if="data.viewed" class="count-wrap viewed">
                    <div class="count-inner">
                      <span class="count">{{ square?.timelineNewPosts }}</span>
                      <iconpark-icon name="iconarrowright" class="icon" />
                    </div>
                  </div>
                  <div v-else class="count-wrap">
                    <span class="count">{{ square?.timelineNewPosts }}</span>
                    <iconpark-icon name="iconarrowright" class="icon" />
                  </div>
                </template>
                <div v-else class="count-wrap">
                  <iconpark-icon name="iconarrowright" class="icon" />
                </div>
              </div>
              <div v-if="showFollow" class="nav">
                <FollowButton :square="square" />
              </div>
            </div>
            <div class="time">
              {{ postedAtFormat }}

              <!--动态可见性-->
              <PartVisibility
                v-if="store.isSelfSquare(square.squareId) && !isFromOuter"
                :post="postItem.post"
                :violation-id="postItem.violationId"
                :stick-on-top="postItem.stickOnTop"
                :promoting="postItem.promoting"
                class="ml-8"
                @click.stop
                @refresh-item="settingChange('PartVisibility')"
              />

              <t-tag
                v-if="showViolationTag"
                theme="danger"
                size="small"
                shape="round"
                variant="light"
                class="tag-violation"
                @click="violationVisible = true; violationId = postItem.violationId"
              >
                动态违规
              </t-tag>

              <t-tag
                v-if="postItem.promoting"
                size="small"
                shape="round"
                class="tag-promoting ml-8"
              >
                推广中
              </t-tag>
            </div>
          </div>

          <div v-if="isSelfPage" class="setting" @click.stop>
            <PostSetting
              :key="settingKey"
              :hide-top="hideTop"
              :default-value="postItem"
              :has-top-post="hasTopPost"
              :post-id="postItem.post.id"
              :private-hide-share="false"
              :include-visibility-options="[]"
              @removed="onRemoved"
              @change="settingChange"
              @toggle-top="onTop"
              @promoting="emit('promoting', $event, postItem)"
            />
          </div>
        </div>
      </template>

      <template v-else>
        <t-tag
          v-if="showViolationTag"
          theme="danger"
          size="small"
          shape="round"
          variant="light"
          class="cursor-pointer"
          @click="violationVisible = true; violationId = postItem.violationId"
        >
          动态违规
        </t-tag>
      </template>

      <PostItemContent
        v-bind="$attrs"
        :data="postItem"
        :preview="preview"
        in-list
        :show-part-visibility="!goDetail"
        @click-content="(e) => emit('click-content', e)"
        @article-card-click="emit('article-card-click')"
        @card-click="emit('card-click', $event)"
        @toolbar-change="emit('toolbar-change', $event)"
        @preview-share="emit('preview-share')"
      />

      <ViolationInfoDrawer v-if="violationVisible" :id="violationId" v-model="violationVisible" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, inject, ref, watch } from 'vue';
import to from 'await-to-js';
import { getPost } from '@renderer/api/square/post';
import { useRoute } from 'vue-router';
import { useEventBus } from '@vueuse/core';
import { timeAgo } from '@/views/square/utils/time';
import PostItemContent from '@/views/square/components/post/PostItemContent.vue';
import SquareAvatar from '@/views/square/components/SquareAvatar.vue';
import { PostStatus } from '@/api/square/models/post';
import ViolationInfoDrawer from '@/views/square/components/ViolationInfoDrawer.vue';
import PartVisibility from '@/views/square/components/post/PartVisibility.vue';
import { useSquareStore } from '@/views/square/store/square';
import { POST_IS_FROM_OUTER, Visibility, VisibilityMap } from '@/views/square/constant';
import PostSetting from '@/views/square/components/post/PostSetting.vue';
import SquareCertIcon from '@/views/square/components/SquareCertIcon.vue';
import FollowButton from '@/views/square/components/FollowButton.vue';
import { postItemRefreshKey } from '@/views/square/utils/eventBusKey';

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
  goDetail: Boolean,
  prevTime: String,
  inList: Boolean,
  preview: Boolean,
  // 是否显示可见性标签
  showVisibility: Boolean,
  // 是否隐藏更多按钮
  hideMoreNav: Boolean,
  hideTop: Boolean,
  // 是否有置顶动态
  hasTopPost: {
    type: Boolean,
    default: false,
  },
  showFollow: Boolean,
});
const emit = defineEmits([
  'click-card',
  'click-head',
  'click-more',
  'click-content',
  'toolbar-change',
  'refresh-item',
  'preview-share',
  'article-card-click',
  'card-click',
  'removed',
  'toggle-top',
  'promoting',
]);

// 在其它模块（如IM）内调用
const isFromOuter = inject(POST_IS_FROM_OUTER, false);

const store = useSquareStore();
const data = ref<any>({
  ...props.data,
  square: props.data.square || store.squareInfo.square,
});

// 数据更新
watch(() => props.data, () => {
  data.value = {
    ...props.data,
    square: props.data.square || store.squareInfo.square,
  };
}, { deep: true });

const postItem = computed<Record<string, any>>({
  get() {
    return data.value;
  },
  set(val) {
    data.value = val;
  },
});
const route = useRoute();
const square = computed(() => postItem.value.square || store.squareInfo.square);
const postedAtFormat = computed(() => timeAgo(props.data?.post?.postedAt));
// const showViolationTag = computed(() => postItem.value.post?.status === PostStatus.Block && postItem.value.violationId && [Visibility.PUBLIC, Visibility.FANS_ONLY].includes(postItem.value.post.visibility));
const showViolationTag = computed(() => postItem.value.post?.status === PostStatus.Violation && postItem.value.violationId);
const settingKey = ref(1);
const isSelfPage = computed(() => {
  if (route.path === '/square/homepage') {
    return true;
  }
  if (route.path === '/square/friend-detail') {
    const id = route.query.id;
    return store.squareId === id;
  }
  return false;
});

const clickHead = () => emit('click-head', postItem.value);
const getInfo = async (post_id: string, type: string) => {
  // const [err, res] = await to(props.publishVerify ? getPostPublishVerifyPreview(post_id) : getPost({ post_id }));
  const [err, res] = await to(getPost({ post_id }));
  if (err) return;

  const { post } = res.data;
  // 非公开的显示
  if (post.post.visibility !== Visibility.PUBLIC) {
    post.post.visibilityText = VisibilityMap[post.post.visibility];
  }
  // 好友圈列表自己已置顶的动态，变更私密性时不变更是否置顶属性
  if (type === 'PartVisibility' && !postItem.value.stickOnTop) {
    post.stickOnTop = false;
  }
  postItem.value = post;
};

const onRemoved = () => {
  emit('removed');
};

const onTop = (event) => {
  emit('toggle-top', event);
};

const bus = useEventBus(postItemRefreshKey);
const settingChange = async (type: string) => {
  const id = postItem.value.post.id;
  await getInfo(id, type);
  emit('refresh-item', id);
  setTimeout(() => {
    settingKey.value++;
  }, 500);

  // 通过好友圈列表更新。TODO 最好限制仅二级列表第一条数据更新时才触发（第一条对应好友圈列表对应的动态）
  bus.emit({ name: 'post-item-refresh' }, postItem.value);
};
const violationVisible = ref(false);
const violationId = ref('');
</script>

<style lang="less" scoped>
.trends-item {
  position: relative;
  display: flex;
  padding: 24px;
  width: 100%;
  border-radius: 8px;
  border: 1px solid var(--divider-kyy-color-divider-light, #ECEFF5);
  background: var(--bg-kyy-color-bg-light, #FFF);
  overflow-x: hidden;

  .top-banner {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    width: 100%;
    z-index: 1;
  }

  .avatar {
    width: 40px;
    height: 40px;
    border-radius: 6px;
    cursor: pointer;
  }

  .right-content {
    flex: 1;
    max-width: 100%;
    z-index: 2;
    // overflow-x: hidden;
    .top {
      display: flex;
      align-items: center;
      .name {
        margin-right: 8px;
        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        text-align: left;
        color: var(--text-kyy-color-text-1, #1A2139);
        line-height: 24px;
        display: inline-block;
        max-width: 100%;
        cursor: pointer;
        .ellipsis();
        &:hover {
          color: var(--brand-kyy_color_brand_default, #4D5EFF);
        }
      }
      .nav {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        margin-top: -24px;
        margin-bottom: -24px;
        margin-right: -24px;
        padding: 14px 24px;
        cursor: pointer;
        .follow-btn {
          height: 26px;
          border-radius: 20px;
          background-color: rgb(31, 109, 250);
          margin-right: 0;
          padding: 2px 12px;
        }

        .count-wrap {
          height: 48px;
          border-radius: 4px;
          display: flex;
          padding: 4px 0px;
          align-items: center;
          gap: 4px;
          align-self: stretch;
          &:hover {
            background: var(--bg-kyy_color_bg_list_hover, #F3F6FA);
          }
          &.viewed {
            &:hover {
              color: var(--brand-kyy_color_brand_default, #4D5EFF);
              background: var(--bg-kyy_color_bg_list_hover, #F3F6FA);
            }
            .count {
              color: var(--brand-kyy_color_brand_default, #4D5EFF);
              background: var(--bg-kyy_color_bgBrand_hover, #EAECFF);
            }
            .icon {
              color: var(--brand-kyy_color_brand_default, #4D5EFF);
            }
            .count-inner {
              background: var(--bg-kyy_color_bgBrand_hover, #EAECFF);
              .count {
                padding-right: 0;
                background-color: transparent;
              }
            }
          }
        }

        .count-inner {
          display: flex;
          border-radius: 99px;
          .count {
            background: var(--kyy_color_badge_bg, #FF4AA1);
          }
        }

        .count {
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          font-weight: 500;
          padding: 0 6px;
          min-width: 20px;
          height: 20px;
          line-height: 20px;
          color: #fff;
          border-radius: var(--kyy_radius_badge_full, 999px);
          background: var(--kyy_color_badge_bg, #FF4AA1);
          cursor: pointer;
        }
        .icon {
          color: #a1a2a4;
          font-size: 20px;
          border-radius: 999px;
        }
      }
    }
    .time {
      display: flex;
      font-size: 14px;
      color: var(--text-kyy_color_text_3, #828DA5);
    }
  }
}

.time-wrap {
  width: 36px;
  margin-right: 18px;
  text-align: center;
  z-index: 9;
  .month {
    font-weight: bold;
    font-size: 16px;
    color: #13161b;
  }
  .day {
    font-size: 12px;
    color: #717376;
  }
}

.setting {
  // position: absolute;
  // right: 24px;
  // top: 12px;
  margin-left: 16px;
}

.tag-violation {
  margin-left: 12px;
  cursor: pointer;
  color: var(--lingke-wrong, #D92F4D) !important;
  background: #FBDDE3 !important;
  padding: 0 8px !important;
  &:hover {
    background: var(--error-kyy_color_error_disabled, #F2B6C1) !important;
  }
}
</style>
