import { defineStore } from "pinia";
import { RouteItem } from "./types";
import { setMemberTeamID } from "../utils/auth";

export const useMemberFormDesignStore = defineStore("member_form_design", {
  state: () => ({
    personal_form: [], // 个人入会表单设置，前端自行定义结构，字段名看表单字段说明
    team_form: [] // 单位入会表单设置，前端自行定义结构，字段名看表单字段说明
  }),

  getters: {
    // isPersonal() {
    //   if (!this.activeAccount) return true;
    //   return this.activeAccount.type === 1;
    // }
  },

  actions: {
    // setActiveAccount(item) {
    //   this.activeAccount = item;
    //   // localStorage.setItem('project_teamid', item.teamId);
    //   setMemberTeamID(item.teamId);
    // },
    // 设置个人表单
    setPersonForm(arr) {
      this.personal_form = arr;
    },
    setTeamForm(arr) {
      this.team_form = arr;
    }
  },
  persist: {
    key: "member_form_design",
    storage: localStorage
  }
});
