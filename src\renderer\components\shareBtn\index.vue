<template>
  <!-- trigger="click"  trigger="click"-->
  <t-popup
    class="share-popup"
    v-model="visible"
    attach="body"
    overlayClassName="share-popup-class"
    placement="bottom-left"
    destroy-on-close
  >
    <template v-if="$slots.default">
      <slot @click="visible=false"/>
    </template>
    <template v-else>
      <div v-if="btnType === 'icon'" class="share-icon">
        <iconpark-icon class="iconshare" name="iconshare"></iconpark-icon>
      </div>
      <div v-else class="share-btn">
        <iconpark-icon class="iconshare" name="iconshare"></iconpark-icon>
        <span>分享</span>
      </div>
    </template>
    <template #content>
      <div class="share-menu">
        <div @click.stop="rlink(),visible=false" v-if="menuList.includes('contacts')" class="share-menu-item">{{t('square.square.ringkolFriend')}}</div>
        <div @click.stop="squareForwardVisible = true,visible=false" v-if="menuList.includes('forward')" class="share-menu-item">
          {{t('banch.gczf')}}
        </div>
        <div @click.stop="copyLink(),visible=false" v-if="menuList.includes('copy')" class="share-menu-item"> {{ t('clouddisk.copyLink') }}</div>
      </div>
    </template>
  </t-popup>
  <!-- 廣場轉發 -->
  <PostPublishDialog
    v-if="menuList.includes('forward')"
    v-model="squareForwardVisible"
    :type="props.type"
    :extra-data="extraData"
    :only-text="true"
    :show-map="true"
    :draft="false"
    :z-index="2601"
  />
  <!-- :default-value="postData" -->
  <!-- 另可好友 -->
  <selectMember
    v-if="menuList.includes('contacts')"
    v-model:visible="selectMemberVisible"
    :disable-list="[cardId]"
     attach="body"
    change-menus
     show-my-group-menu
    :change-menus="true"
    @confirm="selectMemberConfirm"
  />
</template>
<script setup lang="ts">
import { ref, onMounted, onActivated } from "vue";
import selectMember from '@renderer/components/rk-business-component/select-member/common-add-members.vue';
import { MessagePlugin } from "tdesign-vue-next";
import { useI18n } from "vue-i18n";
import { MsgShareType, sendApplicationMsg } from "@renderer/utils/share";
import useClipboard from "vue-clipboard3";
import PostPublishDialog from "@/views/square/components/post/PostPublishDialog.vue";
import { PostType } from "@/views/square/constant";

const { toClipboard } = useClipboard();

const { t } = useI18n();

const cardId = ref("");
const visible=ref(false)
const selectMemberVisible = ref(false);
const squareForwardVisible = ref(false);
const props = defineProps({
  teamId: {
    type: String,
    default: "",
  },
  menuList: {
    //分享功能列表默認三個都有
    type: Array,
    default: () => ["contacts", "forward", "copy"],
  },
  //默認是icon圖標展示
  btnType: {
    type: String,
    default: "icon",
  },
  extraData: {
    // 广场号=>卡片内容
    type: Object,
  },
  type: {
    // 广场号=>指定上传类型
    type: String,
  },
  // 广场号=>相册节点id, 相册节点时必传
  nodeId: {
    type: String,
    default: "",
  },
  //复制链接=>複製鏈接的url
  copyUrl: {
    type: String,
    default: "",
  },
  //另可好友=>分享類型
  msgShareType: {
    type: String,
  },
  //另可好友=>分享內容
  shareData: {
    type: Object,
  },
});

const teamId = ref(props.teamId || window.localStorage.getItem('workBenchTeamid'));

const logsa = () => {};
const copyLink = async () => {
  try {
    console.log(props.copyUrl,'copyUrlcopyUrlextraDataextraData');
    console.log(props.extraData,'copyUrlcopyUrlextraDataextraData');
    console.log(props.shareData,'copyUrlcopyUrlshareDatashareData');

    await toClipboard(props.copyUrl);
    MessagePlugin.success(t("banch.fzljcg"));
  } catch (e) {
    console.error(e);
  }
};
const rlink = () => {
  let staff = window.localStorage.getItem("staff");
  const teamId = props.teamId || window.localStorage.getItem(`workBenchTeamid`);
  if (staff) {
    staff = JSON.parse(staff);
    const item = staff.find((e) => e.teamId === teamId);
    if (item?.uuid) {
      cardId.value = item.uuid;
    }
  }
  selectMemberVisible.value = true;
};
const selectMemberConfirm = (members) => {
  console.error(props.msgShareType, { data: props.shareData }, members);

  sendApplicationMsg(props.msgShareType, props.shareData, members);
  MessagePlugin.success("分享成功！");
};
</script>
<style lang="less" scoped>
.share-icon {
  cursor: pointer;
  display: flex;
  width: 32px;
  height: 32px;
  justify-content: center;
  align-items: center;
  gap: 24px;
  flex-shrink: 0;
  border-radius: 40px;
  border: 1px solid var(--icon-kyy_color_icon_white, #fff);
  background: rgba(255, 255, 255, 0.48);
  backdrop-filter: blur(50px);
  .iconshare {
    font-size: 20px;
    color: #828da5;
  }
}
.share-btn {
  height: 24px;
  border-radius: 4px;
  color: var(--text-kyy_color_text_2, #516082);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  padding-right: 4px;
  padding-left: 3px;
  width: fit-content;
  display: flex;
  align-items: center;
  gap: 4px;
  .iconshare {
    color: #828da5;
    font-size: 20px;
  }
}
.share-btn:hover {
  cursor: pointer;
  color: #707eff !important;
  background: var(--bg-kyy_color_bgBrand_hover, #EAECFF) !important;
  .iconshare {
    color: #707eff !important;
  }
}

.iconshare:hover {
  color: #707eff !important;
}
.share-menu-item {
  display: flex;
  height: 32px;
  min-width: 136px;
  min-height: 32px;
  max-height: 32px;
  padding: 0px 8px;
  align-items: center;
  gap: 12px;
  align-self: stretch;
  color: var(--kyy_color_dropdown_text_default, #1a2139);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  cursor: pointer;
  border-radius: var(--kyy_radius_dropdown_s, 4px);
}
.share-menu-item:hover {
  background: var(--kyy_color_dropdown_bg_active, #e1eaff);
  color: var(--kyy_color_dropdown_text_active, #4d5eff);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
}

.share-menu {
}
</style>
<style>
  .share-popup-class-left {
  z-index:2500;
  left: -110px !important;
  .t-popup__content {
    margin-top: 5px !important;
    padding: 4px !important;
  }
}
.share-popup-class {
  z-index:2500;
  /* left: -80px !important; */
  .t-popup__content {
    margin-top: 5px !important;
    padding: 4px !important;
    box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.12) !important;
  }
}
</style>
