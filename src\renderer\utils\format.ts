/**
 * 格式化银行卡
 * @param cardNumber 银行卡号
 * @returns 格式化后的银行卡号
 */
export const formatBankCard = (cardNumber: string) => formatNumberGroup(cardNumber);

/**
 * 格式化数字分组
 * @param value 需要格式化的字符串
 * @param groupSize 分组大小
 * @param separator 分隔符
 * @returns 格式化后的字符串
 */
export const formatNumberGroup = (value: string, groupSize: number = 4, separator: string = ' ') => {
  if (!value) return '';
  return value.replace(/\s/g, '').replace(new RegExp(`(\\d{${groupSize}})(?=\\d)`, 'g'), `$1${separator}`);
};

/**
 * 数字缩写格式化
 * @param value 数字
 * @returns 缩写后的字符串，如1.2k、3.4w
 */
export const formatAbbreviatedNumber = (value: number): string => {
  // 1w
  if (value >= 10000) {
    const v = Math.floor(value / 100) / 100; // 保留一位小数但不四舍五入
    const str = v.toString();
    const dotIdx = str.indexOf('.');
    return dotIdx > -1 ? str.slice(0, dotIdx + 2) + 'w' : str + '.0w';
  }

  // 1k
  if (value >= 1000) {
    const v = Math.floor(value / 100) / 10;
    const str = v.toString();
    const dotIdx = str.indexOf('.');
    return dotIdx > -1 ? str.slice(0, dotIdx + 2) + 'k' : str + '.0k';
  }

  return value.toString();
};

/**
 * 距离格式化
 * @param distance 距离（单位：米）
 * @returns 格式化后的距离字符串
 */
export const formatDistance = (distance: number): string => {
  if (distance < 1000) {
    return `${Math.round(distance)} m`;
  }

  if (distance <= 999900) {
    const km = Math.floor(distance / 100) / 10;
    const str = km.toString();
    const dotIdx = str.indexOf('.');
    return dotIdx > -1 ? str.slice(0, dotIdx + 2) + ' km' : str + '.0 km';
  }

  return '>999.9km';
};
