<template>
  <div v-lkloading="{ show: lkloadingFlag }" style="height: calc(100% - 40px)">
    <work-area-head
      :tab-list="tabList"
      :active-index="activeIndex"
      :group-list="groupList"
      :activation-group-item="activationGroupItem"
      :not-red="true"
      @open-refresh="openRefresh"
      @deltab-item="deltabItem"
      @upt-work-bench-tab-item="uptWorkBenchTabItem"
      @set-work-bench-tab-item="setWorkBenchTabItem"
      @get-group-list-api="getGroupListApi"
      @setactivation-group-item="setactivationGroupItem"
      @set-active-index="setActiveIndex"
    />
    <router-view
      v-slot="{ Component }"
      :tab-list="tabList"
      :activation-group-item="activationGroupItem"
      :group-list="groupList"
      :tab-index="tabIndex"
      class="work-bench-index"
      :cloud-disk-type="activationGroupItem"
      @open-refresh="openRefresh"
      @get-group-list-api="getGroupListApi"
      @deltab-item="deltabItem"
      @add="add"
      @set-active-index="setActiveIndex"
      @update-title="updateTitle"
      @upt-work-bench-tab-item="uptWorkBenchTabItem"
      @set-work-bench-tab-item="setWorkBenchTabItem"
    >
      <keep-alive :key="pageKey" :exclude="data.exc">
        <component
          :is="Component"
          v-if="data.showCompoent"
          :key="Component.key"
          ref="routerViewRef"
          :exclude="data.exc"
        />
      </keep-alive>
    </router-view>
  </div>
</template>

<script setup lang="jsx">
import { ref, onMounted } from "vue";
import { useI18n } from "vue-i18n";
import { isEmpty } from "lodash";
import { useRoute, useRouter } from "vue-router";
import WorkAreaHead from "./components/WorkAreaHead.vue";
import { onMountedOrActivated } from "@/hooks/onMountedOrActivated";
import LynkerSDK from "@renderer/_jssdk";
const { ipcRenderer } = LynkerSDK;

const pageKey = ref(1);

const { t } = useI18n();
const lkloadingFlag = ref(false);
const router = useRouter();
const route = useRoute();
const routerViewRef = ref(null);
const tabList = ref([]);
const groupList = ref([]);
const activationGroupItem = ref({});
const tabIndex = ref(0);
const data = ref({
  showCompoent: true,
  exc: "",
});
const emits = defineEmits(["add"]);

const add = () => {
  emits("add");
};

const activeIndex = ref(0);
const deltabItem = (index, flag, notTip) => {
  // 根据审批模块需求变更为数组循环
  let nameArr = tabList.value[index].name.split(",");
  console.log(nameArr, "nameArr");
  nameArr.forEach((e) => {
    setTimeout(() => {
      data.value.exc = e;
      data.value.showCompoent = false;
      setTimeout(() => {
        data.value.showCompoent = true;
        data.value.exc = "";
      });
    });
  });

  tabList.value.splice(index, 1);
  console.log(tabList.value, "啊塞擦声");
  if (flag) {
    const tabItemNext = tabList.value[tabList.value.length - 1];
    router.push({
      path: tabItemNext.path,
      query: isEmpty(tabItemNext.query) ? route.query : tabItemNext.query,
    });
  }
};

onMountedOrActivated(() => {});
const openRefresh = () => {
  // pageKey.value++;
  const currentRoute = router.currentRoute.value;
  console.log(currentRoute, "当前路由aseawdaw");
  currentRoute.matched.forEach((r) => {
    // fullPath替换成了path
    if (r.path === currentRoute.path) {
      // 获取到当前页面的name
      const comName = r.components.default.name;
      console.log(comName, "拿到的name");
      if (comName !== undefined) {
        data.value.exc = comName;
        data.value.showCompoent = false;
      }
    }
  });
  setTimeout(() => {
    data.value.showCompoent = true;
    data.value.exc = "";
  });
};

const setActiveIndex = (index) => {
  activeIndex.value = index;
};
ipcRenderer.on("set-big-market-tab-item", (e, val) => {
  console.log(val, "vaset-work-bench-tab-iteml");
  setWorkBenchTabItem(val);
  if (val.gopath) {
    router.push(val.path);
  }
});

// 更新tab
const uptWorkBenchTabItem = (item) => {
  if (item.updateKey) {
    const index = tabList.value.findIndex((v) => v?.updateKey === item.updateKey);
    tabList.value[index] = item;
  }
};
const updateTitle = (item) => {
  // if (item.title) {
  //   const index = tabList.value.findIndex((v) => v?.name === item.name);
  //   tabList.value[index].title = item.title;
  // } 7.24交互说放弃这个更新页签标题功能
};

const setWorkBenchTabItem = (item) => {
  if (item.addNew) {
    const index = tabList.value.findIndex(
      (v) => v?.path === item.path && JSON.stringify(v?.query) === JSON.stringify(item.query),
    );
    console.log("index", tabList.value);
    console.log("item", item);
    console.log("index", index);
    if (index === -1) {
      tabList.value.push(item);
    } else {
      tabList.value[index] = item;
    }
  } else {
    const index = tabList.value.findIndex((e) => e.path === item.path);
    const hasDuplicate = index !== -1;
    if (hasDuplicate) {
      tabList.value[index] = item;
    } else {
      tabList.value.push(item);
    }
  }
};
onMounted(() => {
  LynkerSDK.ipc.handleRenderer('big-market-is-inited', async () => {
    console.log('big-market-is-inited');
    return true;
  });
});
</script>
<style>
.work-bench-index {
  background: #fff;
  overflow: auto;
  height: calc(100%) !important;
}
::-webkit-scrollbar {
  width: 4px;
  background-color: #f5f5f5;
}
/*定义滚动条轨道 内阴影+圆角*/
::-webkit-scrollbar-track {
  background-color: #e3e6eb;
}
/*定义滑块 内阴影+圆角*/
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
  background-color: #c8c8c8;
}
</style>
<style lang="less" scoped>
:deep(.appAuthTipDialog) {
  .t-dialog__body__icon {
    padding: 16px 0 24px;
  }
  .t-button--theme-default {
    font-weight: 600;
  }
}
</style>
