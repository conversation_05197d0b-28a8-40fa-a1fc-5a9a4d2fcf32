<template>
  <div class="page" @click="pageClick">
    <shop-header ref="shopHeaderRef" :keyword="params.keyword" :fixdHeaderShow="fixdHeaderShow" :searchPage="true"
      @edit-address-data="editAddressData" @editIsMapPageFlag="editIsMapPageFlag" @edit-address-text="editAddressText" @change-type="changeType"
      @homeinit="toindex" @toSaerch="toSeach" :type="params.type" :addressText="addressText"  />

    <div v-infinite-scroll="handleInfiniteOnLoad" class="cont-box" :class="{ embox: !supplyData.length && !loadingStatus}" :infinite-scroll-immediate-check="false"
      :infinite-scroll-distance="20" @scroll="onScroll">
      <div class="conetnt">
        <!-- <shop-card /> -->
        <div v-if="supplyData.length && !loadingStatus" class="list-box">
          <template v-if="params.type === 1">
              <goods-card v-for="item in supplyData" :key="item.spuId" :card-data="item"  />
          </template>
          <template v-else>
             <shop-card v-for="item in supplyData" :key="item.id" :card-data="item"  />
          </template>
        </div>
        <!-- <nores v-if="!supplyData.length && !loadingStatus" :type="notype" style="margin-top: 130px" /> -->
        <noData v-if="!supplyData.length && !loadingStatus" name="no-result" tip="搜索无结果" />
        <t-divider v-if="supplyData.length && supplyData.length > 10 && scrollDisabled && !loadingStatus">到底部了</t-divider>
      </div>
      <loading v-if="loadingStatus" />
    </div>
    <totop container=".cont-box" />
  </div>
</template>

<script setup lang="ts">
import { computed, onActivated, onMounted, ref, toRaw } from "vue";
import { useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
import { useNicheStore } from "@renderer/store/modules/niche";
import useRouterHelper from "@renderer/views/square/hooks/routerHelper";
import locationWidget from "@/views/big-market/components/location-widget.vue";
import menuWidget from "@/views/digital-platform/marketplace/components/menu-widget.vue";
import shopHeader from "@/views/digital-platform/marketplace/components/shop-header.vue";
import shopCard from "@/views/digital-platform/marketplace/components/shop-card.vue";
import goodsCard from "@/views/digital-platform/marketplace/components/goods-card.vue";
import noData from '@renderer/components/common/Empty.vue';

import cardWidget from "@/views/big-market/components/card-widget.vue";
import nores from "@/views/big-market/components/nores.vue";
import {  searchDigitalPlatformProducts, searchDigitalPlatformStores } from "@/views/digital-platform/marketplace/apis";
import loading from "@/views/big-market/components/loading.vue";
import { useDigitalPlatformStore } from "../../store/digital-platform-store";
import { getNowDigitalType } from "../utils";
import { platform } from "../../utils/constant";
import mockSelect from "@/views/big-market/components/mock-select.vue";
import totop from "@/views/big-market/components/totop.vue";
import { getMemberTeamID } from "@renderer/views/member/utils/auth";
import { useRoute } from "vue-router";
const { t, locale } = useI18n();
const route = useRoute();
const isSquare = route?.query?.platform === 'square';

const props = defineProps({
  typeKey: {
    type: String,
    default: "desired",
  },
  home: {
    type: Boolean,
    default: false,
  },
  position: {
    type: Array,
    default: () => [],
  },
});

const digitalPlatformStore = useDigitalPlatformStore();
const teamId = isSquare ? route?.query?.team_id : digitalPlatformStore.activeAccount.teamId;

const params = ref({
  'page.number': 1,
  'page.size': 20,
  'latLng.longitude': props.position[0],
  'latLng.latitude': props.position[1],
  'page.nextPageToken': '',
  'page.prevPageToken': '',
  keyword: '',
  type: 1,
  teamId,
});

const nicheStore = useNicheStore();
const supplyData = ref([]);
const total = ref(0);
const nicheClassId = ref(0);
const loadingStatus = ref(false);
const reqOk = ref(false);

const addressData: any = ref(null);
const typeKeyVal: any = ref(props.typeKey);
const addressText = ref(null);
const editAddressData = (data, name) => {
  console.log(`editAddressData shopSearch`, data, name);
  addressText.value = name;
  addressData.value = data;
  supplyData.value = [];
  homeDataReq();
};

const editIsMapPageFlag = (val, name?) => {
  console.log(`editIsMapPageFlag`, val, name);
  emit('editIsMapPageFlag', val)
  if (val) {
    console.log(val, name);
    addressText.value = name;
    addressData.value = [val.location.lng, val.location.lat];
    params.value['latLng.longitude'] = val.location.lng;
    params.value['latLng.latitude'] = val.location.lat;
    searchRun(true);
  }
};
const editAddressText = (text) => {
  addressText.value = text;
};
const searchRun = (AddressRef?) => {
  if (!params.value.keyword && !AddressRef) {
    return;
  }
  if (!AddressRef) {
    typeKeyVal.value = null;
  }
  params.value["page.number"] = 1;
  nicheClassId.value = 0;
  supplyData.value = [];
  homeDataReq();
};
const changeType = (type) => {
  params.value.type = type;
  params.value["page.number"] = 1;
  supplyData.value = [];
  homeDataReq();
};
const homeDataReq = async (dispense?, notReClass?) => {
  if (!dispense) {
    loadingStatus.value = true;
  }
  console.log(params.value, "params.value");
  const res = params.value.type === 1 ?  await searchDigitalPlatformProducts(params.value, teamId)
  : await searchDigitalPlatformStores(params.value, teamId);
  reqOk.value = true;
  console.log(res, "reshomeDataReq");
  if (res.data.code === 0) {
    const dats = params.value.type === 1 ? res.data.data.products : res.data.data.stores;
    supplyData.value = supplyData.value.concat(dats);
    total.value = res.data.data.total;
  }
  loadingStatus.value = false;
};

const scrollDisabled = computed(() => supplyData.value.length >= total.value);
const handleInfiniteOnLoad = () => {
  console.log("触发了");
  if (!scrollDisabled.value) {
    params.value["page.number"] += 1;
    homeDataReq(true);
  }
};
const router = useRouter();
onActivated(() => {
});
onMounted(() => {
  // if (!props.home) {
  //   init();
  // }
});

const viewDetail = (row) => {
  onOpenRich(row);
};
const digitalRouter = useRouterHelper("digitalPlatformIndex");
const onOpenRich = (row) => {
  const type = getNowDigitalType();
  const pageKey = `digital_platform_${type}_rich_detail`;
  const searchMenu = digitalRouter.routeList.find((v) => v.name.includes(pageKey));
  searchMenu.query = { uuid: row.uuid, platform: platform.digitalPlatform, from: type };
  router.push({ path: searchMenu.fullPath, query: { ...searchMenu.query } });
  searchMenu.title = row.title;
  digitalPlatformStore.addTab(toRaw({ ...searchMenu }), true);
};

const moreTagShow = ref(false);

const shopHeaderRef = ref(null);
const pageClick = () => {
  if (moreTagShow.value) {
    moreTagShow.value = false;
  }
  shopHeaderRef.value.latClose();
};

const emit = defineEmits(["homeinit", "editIsMapPageFlag"]);
const pathClick = (key) => {
  if (!props.home) {
    if (key !== "index") {
      typeKeyVal.value = props.typeKey;
      params.value.keyword = "";
      changeType(props.typeKey === "desired" ? 2 : 1);
    }
  } else if (key === "index") {
    emit("homeinit");
  }
};

const toSeach = (val) => {
  params.value.keyword = val.keyword;
  params.value.type = val.type;
  searchRun();
};

const homeSearch = (homeParams, addressTextc) => {
  console.log(homeParams, addressTextc, 'homeParams');
  params.value.keyword = homeParams.keyword;
  params.value.type = homeParams.type;
  addressData.value = homeParams.position;
  addressText.value = addressTextc;
  searchRun();
};
const toindex = () => {
  if (props.home) {
    emit("homeinit");
  } else {
    const type = getNowDigitalType();
    digitalPlatformStore.richPagekey += 1;
    const searchMenu = digitalRouter.routeList.find((v) => v.name.includes(`digital_platform_${type}_rich`));
    searchMenu.title = "市场";
    digitalPlatformStore.addTab(toRaw(searchMenu), false);
    router.push({ path: searchMenu.fullPath, query: searchMenu.query });
  }
};
defineExpose({
  homeSearch,
});


// const some = () => {
//   console.log("方法有用，请勿删除");
// };

const fixdHeaderShow = ref(false);
const onScroll = (e) => {
  if (e.target.scrollTop >= 30) {
    fixdHeaderShow.value = true;
  } else {
    fixdHeaderShow.value = false;
  }
  shopHeaderRef.value.latClose();
};

</script>

<style lang="less" scoped>
.page {
  display: flex;
  width: 100%;
  flex-direction: column;
  align-items: center;
  height: 100vh !important;
  background-image: url(@/assets/big-market/bg.png);
  overflow-y: auto;
  background-position: center;
  background-size: 100% 100%;
  background-repeat: no-repeat;

  .fixd-header-box {
    width: 100%;
    margin: 0 auto;
    background: #fff;
    z-index: 1000;

    .fixd-header {
      display: flex;
      width: 1216px;
      gap: 16px;
      background: #fff;
      padding: 16px 24px;
      align-items: center;
      margin: 0 auto;

      .logo-box {
        display: flex;
        align-items: center;
        gap: 12px;
        min-width: 117px;
        cursor: pointer;

        .logo {
          width: 32px;
          height: 32px;
        }

        .font_cn {
          width: 60px;
          height: 40px;
        }

        .name {
          color: var(--text-kyy_color_text_2, #516082);
          font-family: YouSheBiaoTiHei;
          font-size: 32px;
          font-style: normal;
          font-weight: 400;
          line-height: 40px;
          /* 125% */
        }
      }

      .line {
        display: flex;
        height: 24px;
        min-width: 1px;
        max-width: 1px;
        align-items: flex-start;
        gap: 4px;
        background: var(--divider-kyy_color_divider_deep, #d5dbe4);
      }

      .search-bar {
        display: flex;
        width: 424px;
        height: 32px;
        padding: 0px 2px 0px 7px;
        align-items: center;
        gap: 12px;
        flex-shrink: 0;
        border-radius: 4px;
        border: 2px solid var(--brand-kyy_color_brand_hover, #707eff);
        background: var(--input-kyy_color_input_bg_default, #fff);

        .search-btn {
          width: 48px;
          height: 24px;
          flex-shrink: 0;
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;
          border-radius: var(--radius-kyy_radius_button_s, 4px);
          background: var(--brand-kyy_color_brand_hover, #707eff);

          .iconsearch {
            font-size: 20px;
            color: #fff;
          }
        }
      }
    }
  }

  .cont-box::-webkit-scrollbar {
    width: 0px;
  }

  .cont-box {
    width: 100%;
    height: calc(100% - 48px);
    overflow-y: auto;
    border-radius: 16px 16px 0px 0px;
    background: #F5f8fe;
    // background: linear-gradient(180deg, #fff 5.63%, #f5f8fe 18.66%);
    padding: 0 24px;
    .conetnt {
      width: 100%;
      margin-top: 16px;

      .types {
        display: flex;
        height: 32px;
        align-items: center;
        gap: 16px;
        margin-bottom: 12px;
        flex-wrap: wrap;

        .type-item-clay {
          display: flex;
          padding: 4px 16px;
          justify-content: center;
          align-items: center;
          gap: 10px;
          cursor: pointer;
          border-radius: 16px;
          background: var(--bg-kyy_color_bg_light, #fff);
          color: var(--text-kyy_color_text_1, #1a2139);
          text-align: center;
          font-family: "PingFang SC";
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px;
          /* 157.143% */
        }

        .type-item-clay:hover {
          border-radius: 16px;
          color: var(--icon-kyy_color_icon_hover, #707eff);
          background: var(--bg-kyy_color_bgBrand_hover, #eaecff);
        }

        .typeItemAct {
          border-radius: 16px;
          background: var(--bg-kyy_color_bgBrand_foucs, #dbdfff);
          color: var(--icon-kyy_color_icon_active, #4d5eff);
          text-align: center;
          font-family: "PingFang SC";
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px;
          /* 157.143% */
        }

        .more-b {
          display: flex;
          padding: 4px 8px 4px 12px;
          justify-content: center;
          align-items: center;
          gap: 4px;
          color: var(--text-kyy_color_text_2, #516082);
          text-align: center;
          border-radius: 16px;
          background: #fff;
          font-family: "PingFang SC";
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px;
          /* 157.143% */
          cursor: pointer;
          position: relative;

          .iconarrowdwon {
            font-size: 20px;
          }

          .more-tag-box::-webkit-scrollbar {
            width: 4px;
          }

          .more-tag-box {
            display: inline-flex;
            padding: 4px;
            flex-direction: column;
            align-items: center;
            gap: 2px;
            position: absolute;
            right: 0px;
            top: 34px;
            border-radius: var(--kyy_radius_dropdown_m, 8px);
            background: var(--kyy_color_dropdown_bg_default, #fff);
            box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.12);
            z-index: 100;
            max-height: 278px;
            overflow-y: auto;

            .tag-li {
              display: flex;
              height: 32px;
              min-width: 136px;
              min-height: 32px;
              max-height: 32px;
              padding: 0px 8px;
              align-items: center;
              gap: 12px;
              align-self: stretch;
              color: var(--kyy_color_dropdown_text_default, #1a2139);
              font-family: "PingFang SC";
              font-size: 14px;
              font-style: normal;
              font-weight: 400;
              line-height: 22px;
              /* 157.143% */
            }

            .tag-li:hover {
              border-radius: var(--kyy_radius_dropdown_s, 4px);
              background: var(--kyy_color_dropdown_bg_hover, #f3f6fa);
            }

            .tagLA {
              border-radius: var(--kyy_radius_dropdown_s, 4px);
              background: var(--kyy_color_dropdown_bg_active, #e1eaff);
              color: var(--kyy_color_dropdown_text_active, #4d5eff);
              font-family: "PingFang SC";
              font-size: 14px;
              font-style: normal;
              font-weight: 400;
              line-height: 22px;
              /* 157.143% */
            }
          }
        }

        .more-b:hover {
          .iconarrowdwon {
            font-size: 20px;
            color: var(--icon-kyy_color_icon_hover, #707eff);
          }

          color: var(--icon-kyy_color_icon_hover, #707eff);
          border-radius: 16px;
          background: var(--bg-kyy_color_bgBrand_foucs, #eaecff);
        }

        .moreBA {
          .iconarrowdwon {
            font-size: 20px;
            color: var(--icon-kyy_color_icon_hover, #707eff);
          }

          color: var(--icon-kyy_color_icon_hover, #707eff);
          border-radius: 16px;
          background: var(--bg-kyy_color_bgBrand_foucs, #eaecff);
        }

        .moreBC {
          .iconarrowdwon {
            font-size: 20px;
            color: var(--icon-kyy_color_icon_hover, #4d5eff);
          }

          color: var(--icon-kyy_color_icon_hover, #4d5eff);
          border-radius: 16px;
          background: var(--bg-kyy_color_bgBrand_foucs, #dbdfff);
        }
      }

      .list-box {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        gap: 12px;
        column-gap: 12px;
      }
    }
  }
}

.fontWeight6 {
  font-weight: 600 !important;
}

:deep(.t-back-top) {
  background: var(--bg-kyy_color_bg_mask, rgba(0, 0, 0, 0.5));
  border: none;
  box-shadow: none;
}

.headerboxshow {
  box-shadow: 0px 3px 8px 0px rgba(0, 0, 0, 0.12);
}

.fixd-header-box2 {
  width: 100%;
  margin: 0 auto;
  position: fixed;
  top: 40px;
  background: #fff;
  box-shadow: 0px 3px 8px 0px rgba(0, 0, 0, 0.12);
  z-index: 1000;
}
:deep(.t-divider__inner-text){
  color: #828da5;
}
.embox{
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
