<template>
  <div class="waterfall-box">
    <div class="top topDynamics">
      <!-- @click="gonotice" -->
      <div class="title" >
        <img src="@renderer/assets/member/svg/square.svg">
        <span>{{$t('member.eb.n')}}</span>
        <!-- <img src="@/assets/bench/icon_arrow_right.svg" /> -->
      </div>
      <!-- v-if="authority?.manage_auth" -->
      <div v-show="getFrontDynamicsListAxiosData && getFrontDynamicsListAxiosData.length > 0" class="more cursor" @click="goTabNew">
        全部<iconpark-icon name="iconarrowright" class="icon"></iconpark-icon>
      </div>
    </div>
    <div class="dyBox  min-h-200px" v-if="getFrontDynamicsListAxiosData && getFrontDynamicsListAxiosData.length > 0">
      <div class="dynamic cursor" v-for="(row, index) in getFrontDynamicsListAxiosData.slice(0,5)" :key="index" @click="rowClick(row)">
          <!-- src\renderer\assets\member\svg\vplay.svg -->
      <div style="width: 550px" v-if="row.push_type !== 'FORWARD' && !row.relay" class="dynamic cursor">
        <template v-if="row.push_type === SquareContentType.ARTICLE">
          <div class="logo" v-show="row.img">
            <t-image :src="row.img" class="simg">
              <template #loading>
                  <img src="@renderer/assets/member/svg/dynamic.svg" />
              </template>
              <template #error>
                  <img src="@renderer/assets/member/svg/dynamic.svg" />
              </template>
            </t-image>
            <!-- <span class="iconimg">
              <iconpark-icon name="icon16" class="icon"></iconpark-icon>
            </span> -->

          </div>
          <div class="infok" >
            <span class="content line-1">
              <span class="top" v-show="row.top_date">
                <!-- src\renderer\assets\member\svg\top_zh.svg
                src\renderer\assets\member\svg\top_hk.svg -->
                <img v-if="proxy.$i18n.locale === 'zh-cn'" src="@renderer/assets/member/svg/top_zh.svg"/>
                <img v-else src="@renderer/assets/member/svg/top_hk.svg"/>
              </span>
              {{ row.content || default_text }}
            </span>
            <span class="title line-1">{{ row.title }}</span>
            <span class="team">
              <span class="team-left">{{ row.created_at }}</span>
              <span class="line"></span>
              <span class="team-right">{{ row.member_team_name }}</span>
            </span>
          </div>
        </template>
        <template v-else-if="row.push_type === SquareContentType.PICTURE">
          <div class="logo" v-show="row.img">
            <img :src="row.img" />
            <!-- <span class="iconimg">
              <iconpark-icon name="iconimg" class="icon"></iconpark-icon>
            </span> -->
            <!-- v-show="row.img_count" -->
            <!-- <span class="iconcount" v-show="row.img_count">
              {{ row.img_count }}
            </span> -->
          </div>
          <div class="infok" >
            <span class="content line-2">
              <span class="top" v-show="row.top_date">
                  <!-- src\renderer\assets\member\svg\top_zh.svg
                src\renderer\assets\member\svg\top_hk.svg -->
                <img v-if="proxy.$i18n.locale === 'zh-cn'" src="@renderer/assets/member/svg/top_zh.svg"/>
                <img v-else src="@renderer/assets/member/svg/top_hk.svg"/>
              </span>
              <p>{{ row.content || default_text}}</p>
            </span>
            <span class="team">
              <span class="team-left">{{ row.created_at }}</span>
              <span class="line"></span>
              <span class="team-right">{{ row.member_team_name }}</span>
            </span>
          </div>
        </template>
        <template v-else-if="[
                  SquareContentType.FENGCAI,
                  SquareContentType.TEAM_INTRO,
                  SquareContentType.TEAM_HONOR_ROLL,
                  SquareContentType.TEAM_HISTORY,
                  SquareContentType.PARTY_BUILDING
          ].includes(row.push_type)"
        >
          <div class="logo">
            <!-- <img :src="row.img" /> -->
            <t-image v-if="row.img" class="timage" :src="row.img" fit="cover">
              <template #loading>
                <img src="@renderer/assets/member/icon/default.png">
              </template>
              <template #error>
                <img src="@renderer/assets/member/icon/default.png">
              </template>
            </t-image>
            <template v-else-if="SquareContentType.TEAM_INTRO === row.push_type">
              <img src="@renderer/assets/business/info_def.png">
            </template>
            <!-- <span class="iconimg">
              <iconpark-icon name="iconimg" class="icon"></iconpark-icon>
            </span> -->
            <!-- v-show="row.img_count" -->
            <!-- <span class="iconcount" v-show="row.img_count">
              {{ row.img_count }}
            </span> -->
          </div>
          <div class="infok" >
            <span class="content line-1"> <span class="top" v-show="row.top_date">
              <img v-if="proxy.$i18n.locale === 'zh-cn'" src="@renderer/assets/member/svg/top_zh.svg"/>
              <img v-else src="@renderer/assets/member/svg/top_hk.svg"/>
            </span>{{ row.content || default_text}}</span>
            <span class="title line-1">{{row.title}}</span>
            <span class="team">
              <span class="team-left">{{ row.push_flag }}</span>
              <span class="line"></span>
              <span class="team-right">{{ row.member_team_name }}</span>
            </span>
          </div>
        </template>

        <template v-else-if="row.push_type === SquareContentType.ALBUM_NODE">
          <div class="logo album-logo" v-show="row.img">
            <img :src="row.img" />
            <iconpark-icon v-if="row.nodeType !== 1" name="zhiboanniu" class="live-icon" />
            <!-- <span class="iconimg">
              <iconpark-icon name="iconimg" class="icon"></iconpark-icon>
            </span> -->
            <!-- v-show="row.img_count" -->
            <!-- <span class="iconcount" v-show="row.img_count">
              {{ row.img_count }}
            </span> -->
          </div>
          <div class="infok" >
            <span class="content line-1"> <span class="top" v-show="row.top_date">
              <img v-if="proxy.$i18n.locale === 'zh-cn'" src="@renderer/assets/member/svg/top_zh.svg"/>
              <img v-else src="@renderer/assets/member/svg/top_hk.svg"/>
            </span>{{ row.content || default_text}}</span>
            <span class="title line-1">{{row.time_node}}</span>
            <span class="team">
              <span class="team-left" v-show="row.push_flag">{{ row.push_flag }}</span>
              <span class="line" v-show="row.push_flag"></span>
              <span class="team-right">{{ row.member_team_name }}</span>
            </span>

          </div>
        </template>
        <template v-else-if="row.push_type === SquareContentType.TEXT">
          <div class="logo">
            <img src="@renderer/assets/member/svg/dynamic.svg" />
          </div>
          <div class="infok" >
            <span class="content line-2">
              <span class="top" v-show="row.top_date">
                <img v-if="proxy.$i18n.locale === 'zh-cn'" src="@renderer/assets/member/svg/top_zh.svg"/>
                <img v-else src="@renderer/assets/member/svg/top_hk.svg"/>
              </span>
              {{ row.content || default_text}}
            </span>
            <span class="team">
              <span class="team-left">{{ row.created_at }}</span>
              <span class="line"></span>
              <span class="team-right">{{ row.member_team_name }}</span>
            </span>
          </div>
        </template>
        <template v-else-if="row.push_type === SquareContentType.VIDEO">
          <div class="logo" v-show="row.img">
            <img :src="row.img" />
            <img src="@renderer/assets/member/svg/vplay.svg" class="vplay">
          </div>
          <div class="infok" >
            <span class="content line-2">
              <span class="top" v-show="row.top_date">
                <img v-if="proxy.$i18n.locale === 'zh-cn'" src="@renderer/assets/member/svg/top_zh.svg"/>
                <img v-else src="@renderer/assets/member/svg/top_hk.svg"/>
              </span>
              {{ row.content || default_text }}
            </span>
            <span class="team">
              <span class="team-left">{{ row.created_at }}</span>
              <span class="line"></span>
              <span class="team-right">{{ row.member_team_name }}</span>
            </span>
          </div>
        </template>
      </div>
      <!-- <div v-else class="forward-dynamics-box cursor">
        <div v-show="row.top_date" class="forward-dynamics-header-box">
          <img v-if="row.top_date && proxy.$i18n.locale === 'zh-cn'" src="@renderer/assets/member/svg/top_zh.svg" />
          <img v-if="row.top_date && proxy.$i18n.locale !== 'zh-cn'" src="@renderer/assets/member/svg/top_hk.svg" />
          <div class="forward-dynamics-header-text">
            {{ row?.content ? row?.content : t("notice.zfldt") }}
          </div>
        </div>
        <div class="forward-dynamics-content">
          <img class="avt" :src="row?.relay?.square_avatar" />
          <div class="forward-dynamics-content-flex-box">
            <div class="forward-dynamics-content-flex-box-name">{{ row?.relay?.square_name }}</div>
            <div class="forward-dynamics-content-flex-box-dis">
              {{ row?.title ? row?.title : t("notice.zfldt") }}
            </div>
            <div style="display: flex; align-items: center; gap: 25px">
              <span class="forward-dynamics-content-flex-box-time">{{ row?.created_at }}</span>
              <span class="forward-dynamics-content-flex-box-zz">{{ row?.member_team_name }}</span>
            </div>
          </div>
        </div>
      </div> -->

      <SmallDynamicList :row="row" v-else ></SmallDynamicList>
      </div>
    </div>
    <div class="dyBox"  v-else>
      <div class="default">
        <template  v-if="proxy.$i18n.locale === 'zh-cn'">
          <img src="@/assets/member/icon/dynamic_cn.jpg">
        </template>
        <template  v-else>
          <img src="@/assets/member/icon/dynamic_hk.jpg">
        </template>
      </div>
    </div>
  </div>

	<PostDetail
    v-if="detailVisible"
    :id="detailId"
    v-model="detailVisible"
    :scroll-to-comment="defaultToolbar === 'comment'"
    :default-toolbar="defaultToolbar"
    only-like-list
    private-hide-share
    from-outer
    team-id="-1"
    hide-setting
    @removed="detailVisible = false;"
  />
</template>
<script setup lang="ts">
import { ref, onMounted,toRaw, computed, watch, nextTick, getCurrentInstance, onUnmounted } from "vue";
import { AddIcon } from "tdesign-icons-vue-next";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
import { onMountedOrActivated } from "@renderer/hooks/onMountedOrActivated";
import {SquareContentType, ApprovalStatusList, ApprovalStatus, DYNAMICImageDefault} from '@renderer/views/digital-platform/utils/constant'
import PostDetail from '@/views/square/components/post/PostDetail.vue';

import { getFrontDynamicsListAxios } from "@renderer/api/politics/api/businessApi";
import SmallDynamicList from "@renderer/views/digital-platform/components/SmallDynamicList.vue";

import useRouterHelper from "@renderer/views/square/hooks/routerHelper";
import { useDigitalPlatformStore } from "@renderer/views/digital-platform/store/digital-platform-store";
import { SNAPSHOT } from "@/views/square/constant";
import { getSrcThumbnail, getSrcLogo } from "@renderer/views/message/service/msgUtils";
const digitalRouter = useRouterHelper("digitalPlatformIndex");

const digitalPlatformStore = useDigitalPlatformStore();

const default_text = ref('发布了动态')
const { proxy } = getCurrentInstance() as any;
const { t } = useI18n();
const router = useRouter();
const defaultToolbar = ref('');
const is100W = ref(false);
const props = defineProps({
  DataType: {
    type: Number,
    default: 2,
  },
  authority: {
    type: Object,
  },
  activationGroupItem: {
    type: Object,
  },
  allLayoutList: {
    type: Array,
  },
});
const emits = defineEmits(["setWorkBenchTabItem"]);
const getFrontDynamicsListAxiosData = ref(null);
// watch(
//   () => props.activationGroupItem.teamId,
//   (newValue) => {
//     getList();
//   },
// );
const getTipText = (row) => {
  let text = '';
  try {
    if (row.content) {
      const content = JSON.parse(row.content);
      for (const item of content) {
        if (item.insert !== '\n' && typeof row.insert !== 'object') {
          row += row.insert;
        }
        if (typeof row.insert === 'object' && row.insert.image) {
          row += `[图片]`;
        }
      }
    } else {
      text = item.title;
    }
  } catch (err) {
    text = item.title;
  }
  return text;
};
const setWorkBenchTabItem = (val) => {
  emits("setWorkBenchTabItem", val);
};

const postTeamId = ref("-1");

const detailVisible = ref(false);
const detailId = ref('');
const rowClick = (row ) => {
  console.log(row)
  postTeamId.value= row.member_type===1?props.activationGroupItem.teamId:"-1"

  detailId.value = row.origin_id;
  detailVisible.value = true;
};

const goTabNew = () => {
  // router.push('/digitalPlatformIndex/post_list');
  const val = {
    label: t("member.eb.n"),
    // icon: document,
    // value: 'document',
    page: 'post_list'
  };
  const searchMenu = digitalRouter.routeList.find((v) => v.name.includes(val.page));
  // store.addTab(routeList.filter((v) => v.affix).filter(roleFilter));
  searchMenu.query = { type: 'government' };
  router.push({ path: searchMenu.fullPath, query: searchMenu.query });
  console.log(digitalPlatformStore)
  digitalPlatformStore.addTab(toRaw(searchMenu), true);

}

const jumpNotice = (item) => {
  router.push({
    path: `/workBenchIndex/noticeDetailRead?id=${item.receive_id}&teamId=${props.activationGroupItem.teamId}`,
    query: {
      noticeId: item.receive_id,
      id: item.receive_id,
      title: item.title,
      teamId: props.activationGroupItem.teamId,
    },
  });
};
const gonotice = () => {
  router.push("/workBenchIndex/notice");

  emits("setWorkBenchTabItem", {
    path: "/workBenchIndex/notice",
    path_uuid: "notice",
    title: "公告",
    iswork: true,
    name: "notice",
    type: 24,
  });
};
const getList = () => {
  console.log(props.DataType, "ressssssssss");
  getFrontDynamicsListAxios({
    publish_type: props.DataType,
    page: 1,
    page_size: 4,
    confirm_status: props.DataType === 2 ? 0 : 1,
  }, props.activationGroupItem?.teamId).then((res) => {
    getFrontDynamicsListAxiosData.value = res?.data?.data?.list ?
    res.data.data.list?.map((v) => {
      v.popMoreconfirmVisible = false;
      if( v.push_type === SquareContentType.VIDEO) {
        v.img = v.img ? `${v.img}${SNAPSHOT}` : ''
      } else {
        v.img = v.img ? getSrcLogo(v.img) : '';
      }
      return v;
    })
    : [];
    console.log(getFrontDynamicsListAxiosData.value, "ressssssssss");
  });
};
onMountedOrActivated(()=> {
  getList();
})
const toSetUp = () => {
  router.push(`/workBenchIndex/noticeCreate`);
};

const goMorePost = () => {
  router.push('/digitalPlatformIndex/post_list');
};
</script>
<style lang="less" scoped>
// lss 加个滚动条样式
@import "@renderer/views/member/member_number/panel/member-panel/less/home-panel.less";
.album-logo{
  position: relative;
}
.live-icon{
  font-size: 36px;
  position: absolute;
  top: 50%;
  left: 50%;
  margin-left: -18px;
  margin-top: -18px;
}
</style>
