import LynkerSDK from "@renderer/_jssdk";
const { ipcRenderer } = LynkerSDK;
const memberTeamKey = "uin_teamid";

export function getUniTeamID() {
  return window.localStorage.getItem(memberTeamKey);
}

export function setUniTeamID(id) {
  return window.localStorage.setItem(memberTeamKey, id);
}

export function removUniTeamID() {
  return window.localStorage.removeItem(memberTeamKey);
}

export function removeUni() {
  window.localStorage.removeItem("Uni_form_design");
  window.localStorage.removeItem("Uni");

  removeUniTeamID();
  return window.localStorage.removeItem("Uni");
}



export const goToAdmin = async (teamId, paramsQuery?) => {
  // if (!url) return;
  const params = {
      from: "digital_platform",
      // redirect: url,
      // t: +new Date(),
      uuid: "uni",
      jumpPath: '/workBenchIndex/uni_home',
      activationGroupItemTeamId: teamId,
      toAdmin:true,
      teamId: teamId,
      origin: '',
  };

  // ipcRenderer.invoke("delect-memberWinBV");
  ipcRenderer
      .invoke("click-menu-item", {
      url: "/workBenchIndex/workBenchHome",
      ...params,
      query: {...params, ...paramsQuery},
  })
  .then((res) => {
    if (res) {
      ipcRenderer.send("update-nume-index", 'workBench');
    }
  });
};


// 跳转到数字城市-组织端
export const goToDigitalPlatform_member = async (teamId, paramsQuery?) => {
  // if (!url) return;
  const params = {
    from: "digital_platform",
    // redirect: url,
    // t: +new Date(),
    uuid: "government",
    name: "数字高校",
    jumpPath: "/associationIndex/association_manage",
    teamId, // 用于刷新跳转
  };

  // ipcRenderer.invoke("delect-memberWinBV");
  ipcRenderer
    .invoke("click-menu-item", {
      url: "/digitalPlatformIndex/digital_platform_home",
      // ...params,
      teamId,
      query: { ...params, ...paramsQuery },
  })
  .then((res) => {
    if (res) {
      ipcRenderer.send("update-nume-index", 'digital_platform');
    }
  });
};
export const goToDigital_platform_politics_my = async (teamId) => {
  // if (!url) return;
  const params = {
    from: "digital_platform",
    // redirect: url,
    // t: +new Date(),
    uuid: "government",
    name: "数字高校",
    jumpPath: "/digitalPlatformIndex/digital_platform_association_my",
    teamId, // 用于刷新跳转
  };

  // ipcRenderer.invoke("delect-memberWinBV");
  ipcRenderer
    .invoke("click-menu-item", {
      url: "/digitalPlatformIndex/digital_platform_home",
      // ...params,
      reload: true,
      teamId,
      query: params,
  })
  .then((res) => {
    if (res) {
      ipcRenderer.send("update-nume-index", 'digital_platform');
    }
  });
};
