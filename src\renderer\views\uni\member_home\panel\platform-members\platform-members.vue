<template>
  <div class="container">
    <div class="su-header">
      <div class="more-search">
        <div class="in-box">
          <t-input v-model="formData.name" :placeholder="t('ebook.vssxm')" :maxlength="50" clearable
            style="width: 304px" @change="onSearch">

            <template #prefixIcon>
              <iconpark-icon name="iconsearch" class="iconsearch"></iconpark-icon>
            </template>
          </t-input>
        </div>
        <div v-if="paramsSuper" class="af-icon" @click="showFilter">
          <img src="@renderer/assets/approval/icons/fbutton.svg" style="width: 32px; height: 32px" alt="" />
        </div>
        <div v-else class="f-icon" @click="showFilter">
          <img src="@renderer/assets/approval/icons/icon_screen.svg" style="width: 20px; height: 20px" alt="" />
        </div>
      </div>
      <div class="opt">
        <t-badge :count="appCount" style="z-index: 99">
          <t-button id="applyList" theme="default" class="bold" variant="outline" @click="tovis">
            {{ t('ebook.vfk') }}</t-button>
        </t-badge>
        <t-button theme="primary" variant="base" @click="onAddRecord" v-if="allow_visitor">
          <template #icon>
            <iconpark-icon name="iconadd" class="iconadd"></iconpark-icon>
          </template>
          {{ t('ebook.vyq') }}
        </t-button>
      </div>
    </div>
    <div class="body">
      <div v-if="paramsSuper" class="filter-res">
        <div class="tit">{{ t("approval.approval_data.sures") }}</div>
        <div v-if="formData.status || formData.status === 0" class="ov-time te">
          <span>{{ t('ebook.vst') }} {{ formData.status_text }}</span>
          <span class="close2" @click="clearStatus">
            <img src="@renderer/assets/approval/icons/close2.svg" alt="" />
          </span>
        </div>
        <div v-if="formData.official_type" class="ov-time te">
          <span>{{ t('ebook.vcy') }} {{ formData.official_type_text }}</span>
          <span class="close2" @click="clearType">
            <img src="@renderer/assets/approval/icons/close2.svg" alt="" />
          </span>
        </div>

        <div v-if="formData.phone" class="ov-time te">
          <span>{{ t('ebook.vph') }}
            {{ formData.phone }}</span>
          <span class="close2" @click="clearphone">
            <img src="@renderer/assets/approval/icons/close2.svg" alt="" />
          </span>
        </div>

        <div class="icon" @click="clearFilters">
          <img src="@renderer/assets/approval/icons/del8.svg" alt="" />
          <a>{{ t("approval.approval_data.clearFilters") }}</a>
        </div>
      </div>
      <div class="body-content" v-if="loaded">
        <div class="table">
          <t-table row-key="id" :columns="memberColumns" :pagination="pagination.total > 10 ? pagination : null"
            :data="memberData">
            <template #name="{ row }">
              <div class="main-box">
                <kyy-avatar round-radius avatar-size="32px" :image-url="row.avatar" :user-name="row.name"
                  style="margin-right: 12px" />
                <span>{{ row.name }}</span>
              </div>
            </template>
            <template #status="{ row }">
              <div :class="`status-tag${row.status}`">
                {{ showTextStatus(row.status) }}
              </div>
            </template>
            <template #official_type="{ row }">
              <div :class="`type-tag${row.official_type}`">{{ showTextType(row.official_type) }}</div>
            </template>

            <template #phone="{ row }">
              +{{ row.telCode }} {{ row.telephone }}
            </template>

            <template #time="{ row }">
              {{ row.official_date || '--' }}
            </template>
            <template #operate="{ row }">
              <span class="operates">
                <t-link theme="primary" hover="color" class="operates-item" @click="onLookDetail(row)">
                  {{ t('ebook.vxq') }}
                </t-link>
              </span>
            </template>

            <template #empty>
              <div class="empty">
                <Empty :tip="paramsSuper || formData.name ? t('ebook.nos') : t('ebook.nod')"
                  :name="paramsSuper || formData.name ? 'no-result' : 'no-data-new'" />
              </div>
            </template>
          </t-table>
        </div>
      </div>
    </div>
  </div>

  <t-drawer v-model:visible="filterVisible" class="filterDrawer filterDrawerdp" @close="footerCla" :close-btn="true"
    size="472px" :header="t('approval.approval_data.sur')">
    <div class="form-boxxx">
      <div class="fitem">
        <div class="title">{{ t('ebook.vst2') }}</div>
        <div class="ctl">
          <t-select v-model="paramsTemp.status" :options="statusOptions" clearable style="width: 422px"
            @change="statusChange"><template #suffixIcon>
              <img src="@/assets/svg/icon_arrow_down.svg" />
            </template>
          </t-select>
        </div>
      </div>
      <div class="fitem">
        <div class="title">{{ t('ebook.vcy2') }}</div>
        <div class="ctl">
          <t-select v-model="paramsTemp.official_type" :options="merberType" clearable style="width: 422px"
            @change="typeChange"><template #suffixIcon>
              <img src="@/assets/svg/icon_arrow_down.svg" />
            </template>
          </t-select>
        </div>
      </div>
      <div class="fitem">
        <div class="title">{{ t('ebook.vph2') }}</div>
        <div class="ctl">
          <t-input v-model="paramsTemp.phone" style="width: 422px;" :placeholder="t('ebook.vipu')" :maxlength="20"
            clearable />
        </div>
      </div>

    </div>
    <template #footer>
      <div class="foot">
        <div class="btn1" @click="footerCla">
          {{ t("approval.approval_data.cl") }}
        </div>
        <div v-if="paramsSuperFoot" class="btn3" @click="getDataRunDr">
          {{ t("approval.approval_data.cm") }}
        </div>
        <div v-else class="btn2">
          {{ t("approval.approval_data.cm") }}
        </div>
      </div>
    </template>
  </t-drawer>

  <memberDetail ref="lookFeeModalRef" :platform="formData.type" :team-id="currentTeamId" @refdata="onSearch" />
  <InviteVisitorModal :from="'uni'" ref="inviteVisitorModalRef" />
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onActivated, onMounted } from "vue";
import { useI18n } from "vue-i18n";
import {
  getVisitorPlatformList,
  getPlatformDetail,
  getMemberApplyLinkAxios,
  getStatistics,
  getMemberSettingAxios,
} from "@renderer/api/uni/api/businessApi";
import { getResponseResult, } from "@renderer/utils/myUtils";
import { MessagePlugin, } from "tdesign-vue-next";
import { useMemberStore } from "@renderer/views/member/store/member";
import Empty from "@renderer/components/common/Empty.vue";
import { platform } from "@renderer/views/digital-platform/utils/constant";
import { useDigitalPlatformStore } from "@renderer/views/digital-platform/store/digital-platform-store";
import { useRoute } from "vue-router";
import { getMemberTeamID } from "@renderer/views/member/utils/auth";
import memberDetail from "./member-detail.vue";
import kyyAvatar from "@renderer/components/kyy-avatar/index.vue";
import InviteVisitorModal from "@renderer/views/digital-platform/modal/invite-visitor-modal.vue";
import { IMRefreshType } from "@renderer/views/message/common/constant";
import LynkerSDK from "@renderer/_jssdk";
const { ipcRenderer } = LynkerSDK;

const digitalPlatformStore = useDigitalPlatformStore();
const route = useRoute();

const store = useMemberStore();
const { t } = useI18n();
const appCount = ref(0);
const emits = defineEmits(["change-type", "getRedNum"]);
const tovis = () => {
  emits('change-type', 2);
}
const statusOptions = [
  { label: t('ebook.vzc'), value: 1 },
  { label: t('ebook.vjr'), value: 0 },
];

const merberType = [
  { label: t('ebook.vzs'), value: 1 },
  { label: t('ebook.vfk2'), value: 2 },
];

const formData = reactive({
  name: "", // 缴费人/单位
  phone: undefined,
  type: 'uni',
  status: undefined, // 会员级别
  official_type: undefined, // 会费类型，1：会员入会，2：会员续期，3：其他
  status_text: undefined,
  official_type_text: undefined, // 会费类型，1：会员入会，2：会员续期，3：其他
});
const lookFeeModalRef = ref(null);
const memberColumns = ref([
  { colKey: "name", title: t('ebook.vname'), width: "200px" },
  { colKey: "status", title: t('ebook.vst2'), width: "136px", ellipsis: true },
  { colKey: "official_type", title: t('ebook.vcy2'), width: "136px", ellipsis: true },
  { colKey: "phone", title: t('ebook.vph2'), width: "192px", ellipsis: false },
  { colKey: "time", title: t('ebook.vat'), width: "160px", ellipsis: true },
  { colKey: "operate", title: t('ebook.voperate'), width: "120px", ellipsis: true },
]);
const memberData = ref([]);
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
  onChange: (pageInfo) => {
    console.log("pagination.onChange", pageInfo);
    pagination.current = pageInfo.current;
    pagination.pageSize = pageInfo.pageSize;
    onSearch(1);
  },
});

const props = defineProps({
  platform: {
    type: String,
    default: '',
  },
})
// 平台类型 目前只有digital-platform
const platformCpt = computed(() => {
  return props.platform || route.query?.platform
})

const activeAccount = computed(() => {
  if (platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount
  } else if (platformCpt.value === platform.digitalWorkbench) {
    return route.query;
  } else {
    return store.activeAccount
  }
})


const currentTeamId = computed(() => {
  if (platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount?.teamId
  } else if (platformCpt.value === platform.digitalWorkbench) {
    return route.query?.teamId || 0;
  } else {
    return getMemberTeamID()
  }
})

const showTextStatus = (val) => {
  const option = statusOptions.find((v: any) => v.value === val);
  if (option) {
    return option.label;
  }
  return "--";
};
// 会费类型
const showTextType = (val) => {
  const option = merberType.find((v: any) => v.value === val);
  if (option) {
    return option.label;
  }
  return "--";
};


const onSearch = (nocurrent?) => {
  if (nocurrent !== 1) {
    pagination.current = 1;
  }
  const params = {
    ...formData,
    telephone: formData.phone,
  };
  getMemberList(params);
  getStatisticsReq();
};

const getStatisticsReq = () => {
  getStatistics(currentTeamId.value).then((res: any) => {
    if (res) {
      appCount.value = res.data?.data?.visit_apply_count;
    }
  });
}


ipcRenderer.on('IM-refresh', (e, data) => {
  const { type, data: { extend } } = data || {};
  if (type === IMRefreshType.IMRefreshVisitor) {
    if (activeAccount.value) {
      getStatisticsReq();
    }
  }
});


// 获取邀请链接
const getInviteLinkAxios = () => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await getMemberApplyLinkAxios({}, currentTeamId.value);
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      resolve(result.data);
    } catch (error) {
      reject();
      const errMsg = error instanceof Error ? error.message : error;
      if (errMsg === 'Network Error') {

      } else {
        MessagePlugin.error(errMsg);
      }
    }
  });
};

const inviteVisitorModalRef = ref(null);

const onAddRecord = () => {
  getInviteLinkAxios().then((val) => {
    inviteVisitorModalRef.value.onOpen(val);
  }).catch(() => {

  });
};


const loaded = ref(false);
// 获取入会申请列表
const getMemberList = async (params) => {
  // teamId
  loaded.value = false;
  params.page = pagination.current;
  params.page_size = pagination.pageSize;
  params.type = 'uni';
  try {
    let result = await getVisitorPlatformList(params, currentTeamId.value);
    console.log(result);
    result = getResponseResult(result);
    if (!result) return;
    memberData.value = result.data.list;
    pagination.total = result.data.total;
    console.log(memberData.value);
    loaded.value = true;
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    MessagePlugin.error(errMsg);
    loaded.value = true;
  }
};

const initData = () => {
  getMemberList({});
  getStatisticsReq();
  onGetMemberSetting();
};
onMounted(() => {
  initData();
})
onActivated(() => {
  getStatisticsReq();
  onGetMemberSetting();
});

const onLookDetail = (row: any) => {
  getPlatformDetail(row.id, currentTeamId.value).then((res: any) => {
    lookFeeModalRef.value.open(res.data?.data);
  });
};

const filterVisible = ref(false);
const paramsSuper = computed(
  () =>
    formData.status ||
    formData.status === 0 ||
    formData.phone ||
    formData.official_type
);
const paramsSuperFoot = computed(
  () =>
    paramsTemp.value.phone ||
    paramsTemp.value.official_type ||
    paramsTemp.value.status ||
    paramsTemp.value.status === 0
);
const showFilter = () => {
  filterVisible.value = true;
};
const paramsTemp = ref({
  phone: undefined,
  official_type: undefined,
  official_type_text: undefined,
  status: undefined,
  status_text: undefined,
});
const typeChange = (e, ctx) => {
  console.log(e, ctx);
  paramsTemp.value.official_type_text = ctx.option.label;
};
const statusChange = (e, ctx) => {
  paramsTemp.value.status_text = ctx.option.label;
};

const clearFilters = () => {
  formData.phone = undefined;
  formData.official_type = undefined;
  formData.status = undefined;
  formData.name = undefined;
  paramsTemp.value.official_type = undefined;
  paramsTemp.value.status = undefined;
  paramsTemp.value.phone = undefined;
  onSearch();
};

const clearType = () => {
  formData.official_type = undefined;
  paramsTemp.value.official_type = undefined;
  onSearch();
};
const clearphone = () => {
  formData.phone = undefined;
  paramsTemp.value.phone = undefined;
  onSearch();
};
const clearStatus = () => {
  formData.status = undefined;
  paramsTemp.value.status = undefined;
  onSearch();
};

const getDataRunDr = () => {
  filterVisible.value = false;
  formData.phone = paramsTemp.value.phone?.trim();
  formData.official_type = paramsTemp.value.official_type;
  formData.official_type_text = paramsTemp.value.official_type_text;
  formData.status = paramsTemp.value.status;
  formData.status_text = paramsTemp.value.status_text;
  onSearch();
};
const footerCla = () => {
  filterVisible.value = false;
  paramsTemp.value.phone = undefined;
  paramsTemp.value.official_type = undefined;
  paramsTemp.value.status = undefined;
};

const allow_visitor = ref(false);
const onGetMemberSetting = async () => {
  let result = null;
  try {
    result = await getMemberSettingAxios({}, currentTeamId.value);
    result = getResponseResult(result);
    allow_visitor.value = !!result.data?.allow_visitor;
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    MessagePlugin.error(errMsg);
  }
};
</script>

<style lang="less" scoped>
@import "@renderer/views/member/member_home/panel/public.less";


.iconadd {
  color: #ffffff;
  font-size: 24px;
}

.main_body {
  display: flex;
  flex-direction: column;
}

.main-box {
  display: flex;
  align-items: center;
}

.status-tag1 {
  display: flex;
  height: 20px;
  min-height: 20px;
  max-height: 20px;
  padding: 1px 8px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: var(--kyy_radius_tag_full, 999px);
  background: var(--kyy_color_tag_bg_success, #E0F2E5);
  color: var(--kyy_color_tag_text_success, #499D60);
  text-align: right;
  font-family: "PingFang SC";
  width: max-content;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  color: var(--kyy_color_tag_text_success, #499D60);
  text-align: right;

  /* kyy_fontSize_2/bold */
  font-family: "PingFang SC";
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 22px;
  /* 157.143% */
  /* 166.667% */
}

.status-tag0 {
  display: flex;
  height: 24px;
  min-height: 24px;
  max-height: 24px;
  padding: 1px 8px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: var(--kyy_radius_tag_full, 999px);
  background: var(--kyy_color_tag_bg_warning, #FFE5D1);
  color: var(--kyy_color_tag_text_warning, #FC7C14);
  text-align: right;
  font-family: "PingFang SC";
  width: max-content;
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 22px;
  /* 157.143% */
}

.type-tag1 {
  display: flex;
  height: 24px;
  padding: 0px var(--kyy_radius_toopltip, 6px);
  justify-content: center;
  align-items: center;
  gap: 4px;
  border-radius: var(--kyy_radius_tag_s, 4px);
  background: var(--kyy_color_tag_bg_brand, #EAECFF);
  color: var(--kyy_color_tag_text_brand, #4D5EFF);
  text-align: center;
  width: max-content;
  /* kyy_fontSize_2/bold */
  font-family: "PingFang SC";
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 22px;
  /* 157.143% */
}

.type-tag2 {
  display: flex;
  height: 24px;
  min-height: 24px;
  max-height: 24px;
  padding: 0px 8px;
  justify-content: center;
  align-items: center;
  gap: 4px;
  border-radius: var(--kyy_radius_tag_s, 4px);
  background: var(--kyy_color_tag_bg_magenta, #FFE3F1);
  color: var(--kyy_color_tag_text_magenta, #FF4AA1);
  text-align: center;
  font-family: "PingFang SC";
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 22px;
  /* 157.143% */
  width: max-content;
}

:deep(.t-table__content) {
  overflow-x: hidden;
}

.operates-item {
  display: flex;
  padding: 4px;
  justify-content: center;
  align-items: center;
  gap: 10px;
}

.operates-item:hover {
  border-radius: 4px;
  background: var(--bg-kyy_color_bgBrand_hover, #eaecff);
}

.empty {
  min-height: 60vh;
}

.form-boxxx {
  padding: 0 8px;
}

.iconsearch {
  font-size: 20px;
}

:deep(.t-badge--circle) {

  color: var(--kyy_color_tag_text_magenta, #ff4aa1);

  background: var(--kyy_color_badge_bg, #FF4AA1) !important;
}
</style>
