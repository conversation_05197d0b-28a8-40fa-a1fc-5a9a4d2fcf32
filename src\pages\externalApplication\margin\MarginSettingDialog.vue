<template>
	<t-dialog v-model:visible="visible" :close-btn="false" :header="true" :footer="true" width="480">
		<template #header>
			<div style="display: flex; align-items: center; width: 100%; justify-content: space-between">
				<div>保证金设置</div>
				<img style="width: 16px; cursor: pointer; height: 16px" src="@/assets/<EMAIL>" @click="handleClose" />
			</div>
		</template>
		<div class="margin-setting-form">
			<t-form label-width="120px">
				<t-form-item label="是否需要保证金">
					<t-switch v-model="formData.need" :custom-value="[1, 0]" />
				</t-form-item>

				<template v-if="formData.need === 1">
					<t-form-item label="保证金金额" label-align="top" :rules="[{ required: true, message: '请输入保证金金额' }]">
						<!-- TODO :decimalPlaces="0" 	:min="1" -->
						<t-input-number
							v-model="formData.bond_amount"
							placeholder="请输入保证金金额"
							theme="normal"
							:max="999999"
							:suffix="formData.currency === 'CNY' ? '¥' : 'MOP'"
						/>
					</t-form-item>
				</template>
			</t-form>
		</div>
		<template #footer>
			<div style="display: flex; align-items: center; width: 100%; justify-content: flex-end">
				<t-button theme="default" variant="outline" @click="handleClose">取消</t-button>
				<t-button @click="handleSave">确定</t-button>
			</div>
		</template>
	</t-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { setBond } from '@/api/advertisement';
import to from 'await-to-js';

interface MarginConfig {
	bond_id: number;
	bond_amount: number;
	currency: 'CNY' | 'MOP';
	need: number;
	region: 'CN' | 'MO';
}

interface Props {
	modelValue: boolean;
	marginConfig: MarginConfig;
}

interface Emits {
	(e: 'update:modelValue', value: boolean): void;
	(e: 'save', config: MarginConfig): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const visible = ref(false);
const formData = ref<MarginConfig>({
	bond_id: 0,
	bond_amount: 0,
	currency: 'CNY',
	need: 0,
	region: 'CN',
});

// 监听弹窗显示状态
watch(
	() => props.modelValue,
	(newVal) => {
		visible.value = newVal;
		if (newVal) {
			// 打开弹窗时重置表单数据
			formData.value = { ...props.marginConfig };
		}
	},
	{ immediate: true },
);

// 监听内部visible变化
watch(visible, (newVal) => {
	emit('update:modelValue', newVal);
});

// 关闭弹窗
const handleClose = () => {
	visible.value = false;
};

// 保存设置
const handleSave = async () => {
	const { need, bond_amount } = formData.value;

	if (need === 1 && (!bond_amount || bond_amount <= 0)) {
		MessagePlugin.error('保证金金额必须大于0');
		return;
	}

	if (need === 1 && bond_amount > 999999) {
		MessagePlugin.error('保证金金额不能超过6位数');
		return;
	}

	const [err] = await to(
		setBond({
			need,
			bond_amount: need === 1 ? bond_amount : 0,
		}),
	);
	if (err) {
		MessagePlugin.error(err.message || '操作失败');
		return;
	}
	emit('save', { ...formData.value });
	visible.value = false;
	MessagePlugin.success('操作成功');
};
</script>

<style lang="less" scoped>
:deep(.t-input-number) {
	width: 100%;
}
</style>
