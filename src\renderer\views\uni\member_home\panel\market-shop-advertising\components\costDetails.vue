<template>
  <t-dialog 
  v-model:visible="visible" 
  attach="body" :destroy-on-close="true" 
  class="dialogSet20240801 dialogSetHeader dialogSetFooter_top_0 dialogNoDefault"
  @close="close()" :footer="true" :close-btn="true" :header="true" width="480">
    <template #closeBtn>
      <!-- <iconpark-icon name="close" size="16"></iconpark-icon> -->
      <!-- <svg class="iconpark-icon" style="width: 16px; height: 16px">
        <use href="#close" />
      </svg> -->
      <iconpark-icon name="iconerror" style="font-size: 24px"></iconpark-icon>
    </template>
    <template #header>
      <div style="display: flex; align-items: center; width: 100%; justify-content: space-between">
        <div>{{ t("ad.fymx") }}</div>
        <!-- <img style="width: 24px; cursor: pointer; height: 24px" src="@/assets/<EMAIL>" @click="close()" /> -->
      </div>
    </template>
    <div class="body">
      <div class="content-box pagi">
        <t-table
          row-key="index"
          :data="tableList"
          :columns="columns"
          :hover="hover"
          :pagination="
            pagination.total > 7
              ? { total: pagination.total, page: pagination.defaultCurrent, pageSize: pagination.defaultPageSize }
              : null
          "
          height="400px"
          @PageChange="onPageChanges"
          cell-empty-content="-"
        >
          <template #pay="{ row }">
            {{ row.symbol === "¥" ? "¥" : "MOP" }}
            {{ row.price_text }}
          </template>
        </t-table>
      </div>
    </div>
    <template #footer>
      <div class="footer"></div>
    </template>
  </t-dialog>
</template>
<script setup lang="ts">
import { ref, onMounted } from "vue";
import { DialogPlugin, MessagePlugin } from "tdesign-vue-next";
import { useI18n } from "vue-i18n";
import { adpricelist, adfrontpricelist } from "@renderer/api/member/api/ebookApi";
const emits = defineEmits(["closeDrawer"])
const pagination = ref({
  defaultCurrent: 1,
  defaultPageSize: 7,
  total: 0,
});
const tableList = ref([]);
const { t } = useI18n();
const cropperRef = ref(null);
const visible = ref(false);
const columns = [
  {
    title: "日期",
    colKey: "date_at",
    align: 'left'
  },
  {
    title: t("ad.fy"),
    colKey: "pay",
    align: 'right'
  },
];
const close = () => {
  pagination.value.defaultCurrent = 1;
  visible.value = false;
  pagination.value.defaultPageSize = 7;
  console.log(pagination,'出发');
  emits('closeDrawer')

};
const onPageChanges = (e) => {
  pagination.value.defaultCurrent = e.current;
  pagination.value.defaultPageSize = e.pageSize;
  getlist();
};
let ids = null;
let teamIds = null;
const value2 = ref(null);

const getlist = () => {
  let api = adfrontpricelist;
  if (value2.value) {
    api = adpricelist;
  }
  api(
    {
      page: pagination.value.defaultCurrent,
      page_size: pagination.value.defaultPageSize,
      ad_id: ids,
    },
    teamIds,
  ).then((res) => {
    visible.value = true;
    tableList.value = res.data.data.list;
    pagination.value.total = res.data.data.count;
    console.log(res, "费用明细");
  });
};
const openWin = (id, teamId, val, val1, val2) => {
  console.log(id, teamId, val, val1, val2, "啊啊啊啊");
  ids = id;
  teamIds = teamId;
  pagination.value.defaultCurrent = 1;
  pagination.value.defaultPageSize = 7;

  value2.value = val2;
  getlist();
};
defineExpose({
  openWin,
});
</script>

<style lang="less" scoped>
:deep(.t-pagination__select) {
  display: none;
}
.body {
  padding: 0 24px;
  padding-bottom: 24px;
}

.pagi{
  :deep(.t-table__body) {
    td {
      padding: 12px  !important;
    }
  }
}
</style>
