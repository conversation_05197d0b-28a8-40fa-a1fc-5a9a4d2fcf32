import axios, { AxiosError, AxiosInterceptorManager, InternalAxiosRequestConfig } from 'axios';
import _ from 'lodash';
import CryptoJS from 'crypto-js';
import { HEADER_FORUM_KEY } from '@renderer/constants/localKey';
import { MessagePlugin } from 'tdesign-vue-next';
import {
  getServerLang,
  setAccesstoken,
  getAccesstoken,
  getRefreshCode,
  setRefreshCode,
  getOpenid,
  emptyAndQuit,
  getSMDeviceId,
  getCurrentAccount,
} from '@renderer/utils/auth';
import { getProjectTeamID } from '@renderer/views/engineer/utils/auth';
import { getServiceTeamID } from '@renderer/views/service/utils/auth';
import { onHandleError } from '@renderer/constants/engineer';
import { getMemberTeamID } from '@renderer/views/member/utils/auth';
import {
  handleRequestErrorToast,
  handleRequestErrorToastPzy,
  handleRequestErrorToastPzy1,
} from '@renderer/api/approval/api/approvalAdmin';
import { logHandler } from '@renderer/log';
import { configInfo } from '@renderer/views/setting/util';
import { isMacOS, isWindows } from '@renderer/views/square/utils/index';
import LynkerSDK from '@renderer/_jssdk';
import { i18n } from '@/i18n';
import { getOrgSquare, handleRequestError } from '@/views/square/utils/business';
import { handleRingkolError } from './apiResponse';
import emitter from '@/utils/MittBus';

const Env = LynkerSDK.config.env;

const agentTypeMap = {
  isWindows: 'windows',
  isMacOS: 'mac',
};

// 通过布尔值匹配对应的 agentType
const agentType = isWindows ? agentTypeMap.isWindows : isMacOS ? agentTypeMap.isMacOS : 'web';

type ApiModule =
  | 'iam-srv'
  | 'im-sync'
  | 'client-organize'
  | 'organize'
  | 'square'
  | 'know-srv'
  | 'business'
  | 'order'
  | 'square-operation-manage'
  | 'portal'
  | 'tools'
  | 'global'
  | 'h5'
  | 'org-web'
  | 'activities'
  | 'activitiesV2'
  | 'website'
  | 'families'
  | 'WS_URL'
  | 'API_URL';

type ApiConfig = {
  [key in ApiModule]: string;
};

type RequestIntercept = Parameters<AxiosInterceptorManager<InternalAxiosRequestConfig>['use']>[0];

// 设置返回解析未json
axios.defaults.headers['Content-Type'] = 'application/json';
// 跨域允许 cookie
axios.defaults.withCredentials = false;

function getApiConfig(env: string = Env): ApiConfig {
  const config = {
    dev: {
      organize: 'https://dev.ringkol.com/organize/api',
      'client-organize': 'https://dev.ringkol.com/client/api',
      'im-sync': 'https://dev.ringkol.com/im/api',
      'iam-srv': 'https://dev.ringkol.com/iam/api',

      // "iam-srv": "http://192.168.31.160:8000",
      square: 'https://dev.ringkol.com/square/api',
      portal: 'https://dev.ringkol.com/portal/api',
      'square-operation-manage': 'https://dev.ringkol.com/square-operation-manage/api',
      'know-srv': 'https://dev.ringkol.com/know/api',
      business: 'https://dev.ringkol.com/business/api',
      order: 'https://dev.ringkol.com/order/api',
      tools: 'https://dev.ringkol.com/tools/api',
      global: 'https://dev.ringkol.com/global/api',
      h5: 'https://h5-dev.ringkol.com',
      'org-web': 'https://organize-manage-dev.ringkol.com',
      activities: 'https://dev.ringkol.com/activities/api',
      activitiesV2: 'https://dev.ringkol.com/ringkol/api/campaign/v2',
      website: 'https://dev.ringkol.com',
      // 排行api
      families: 'https://dev.ringkol.com/families/api',

      WS_URL: 'wss://im-dev.ringkol.com/msg_gateway',
      API_URL: 'https://im-dev.ringkol.com/api',
      // WS_URL: "wss://web.rentsoft.cn/msg_gateway_enterprise",
      // API_URL: "https://web.rentsoft.cn/api_enterprise",

      ringkol: 'https://dev.ringkol.com/ringkol/api',
    },
    test: {
      organize: 'https://qa.ringkol.com/organize/api',
      'client-organize': 'https://qa.ringkol.com/client/api',
      'im-sync': 'https://qa.ringkol.com/im/api',
      'iam-srv': 'https://qa.ringkol.com/iam/api',

      // "iam-srv": "http://192.168.31.160:8000",
      square: 'https://qa.ringkol.com/square/api',
      portal: 'https://qa.ringkol.com/portal/api',
      'square-operation-manage': 'https://qa.ringkol.com/square-operation-manage/api',
      'know-srv': 'https://qa.ringkol.com/know/api',
      business: 'https://qa.ringkol.com/business/api',
      order: 'https://qa.ringkol.com/order/api',
      tools: 'https://qa.ringkol.com/tools/api',
      global: 'https://qa.ringkol.com/global/api',
      h5: 'https://h5-qa.ringkol.com',
      'org-web': 'https://organize-manage-qa.ringkol.com',
      activities: 'https://qa.ringkol.com/activities/api',
      activitiesV2: 'https://qa.ringkol.com/ringkol/api/campaign/v2',
      website: 'https://qa.ringkol.com',
      // 排行api
      families: 'https://qa.ringkol.com/families/api',

      WS_URL: 'wss://im-qa.ringkol.com/msg_gateway',
      API_URL: 'https://im-qa.ringkol.com/api',

      ringkol: 'https://qa.ringkol.com/ringkol/api',
    },
    pre: {
      organize: 'https://pre.ringkol.com/organize/api',
      'client-organize': 'https://pre.ringkol.com/client/api',
      'im-sync': 'https://pre.ringkol.com/im/api',
      'iam-srv': 'https://pre.ringkol.com/iam/api',
      square: 'https://pre.ringkol.com/square/api',
      portal: 'https://pre.ringkol.com/portal/api',
      'square-operation-manage': 'https://pre.ringkol.com/square-operation-manage/api',
      'know-srv': 'https://pre.ringkol.com/know/api',
      business: 'https://pre.ringkol.com/business/api',
      order: 'https://pre.ringkol.com/order/api',
      tools: 'https://pre.ringkol.com/tools/api',
      global: 'https://pre.ringkol.com/global/api',
      h5: 'https://h5-pre.ringkol.com',
      'org-web': 'https://organize-manage-pre.ringkol.com',
      activities: 'https://pre.ringkol.com/activities/api',
      activitiesV2: 'https://pre.ringkol.com/ringkol/api/campaign/v2',
      website: 'https://pre.ringkol.com',
      // 排行api
      families: 'https://pre.ringkol.com/families/api',

      WS_URL: 'wss://im-pre.ringkol.com/msg_gateway',
      API_URL: 'https://im-pre.ringkol.com/api',

      ringkol: 'https://pre.ringkol.com/ringkol/api',
    },
    prod: {
      organize: 'https://ringkol.com/organize/api',
      'client-organize': 'https://ringkol.com/client/api',
      'im-sync': 'https://ringkol.com/im/api',
      'iam-srv': 'https://ringkol.com/iam/api',
      square: 'https://ringkol.com/square/api',
      portal: 'https://ringkol.com/portal/api',
      'square-operation-manage': 'https://ringkol.com/square-operation-manage/api',
      'know-srv': 'https://ringkol.com/know/api',
      business: 'https://ringkol.com/business/api',
      order: 'https://ringkol.com/order/api',
      tools: 'https://ringkol.com/tools/api',
      global: 'https://ringkol.com/global/api',
      h5: 'https://h5.ringkol.com',
      'org-web': 'https://organize-manage.ringkol.com',
      activities: 'https://ringkol.com/activities/api',
      activitiesV2: 'https://ringkol.com/ringkol/api/campaign/v2',
      website: 'https://ringkol.com',
      // 排行api
      families: 'https://ringkol.com/families/api',

      WS_URL: 'wss://im.ringkol.com/msg_gateway',
      API_URL: 'https://im.ringkol.com/api',

      ringkol: 'https://ringkol.com/ringkol/api',
    },
  };
  if (env === 'PRE') {
    // 先用用正式环境的后面预发环境没问题再切过来
    return config.pre;
  }
  if (env === 'PROD') {
    return config.prod;
  }
  if (env === 'TEST') {
    return config.test;
  }
  // 默认开发环境
  return config.dev;
}

// 刷新token方法
export function refreshTokens(refer?) {
  const params = refer ? { ...getRefreshCode(), refer } : getRefreshCode();
  return iam_srvRequest.post('/v1/passport', params).then((res) => res.data);
}

export const quit = _.throttle((emptyToken?) => {
  MessagePlugin.error(i18n.global.t('im.public.auth_expired'));
  _.delay(() => {
    // removeAccesstoken();
    emptyAndQuit(emptyToken);
  }, 2000);
}, 2000);

export const getErrorStatusMsg = (error) => {
  console.log(error, '提示语言了');
  const status = error?.response?.status;
  // console.log('资源没找到提示语',error);

  const statusMsgMap = {
    301: '资源已被移除',
    303: '重定向',
    304: '资源没有被修改',
    404: error?.response?.message || error?.response?.data.message || '资源，服务未找到',
    405: '不允许的http方法',
    409: error?.response?.message || '资源冲突，或者资源被锁定',
    415: '不支持的数据（媒体）类型',
    418: error?.response?.message || '你输入的信息包含敏感内容，请修改后重试',
    429: '请求过多被限制',
    500: '系统内部错误',
    502: '网络异常',
  };
  return statusMsgMap[status] || '';
};

/**
 * 创建 axios 实例
 * @param baseUrl 默认 url
 * @param reqInterceptor 公共请求参数注入处理， 默认不需要传。如果模块需要自定义，可以传入自定义的函数
 * @param onRejected 响应错误处理
 * @returns
 */
export function createApiInstance(
  baseUrl: string,
  reqInterceptor?: RequestIntercept,
  onRejected?: (error: AxiosError, commonErrMsg?: string) => any,
) {
  // 创建一个axios实例
  const instance = axios.create({
    baseURL: baseUrl,
    timeout: 30000, // 与产品（@邹邹）确认统一30秒，提示请求超时
  });

  // 请求结果拦截器
  instance.interceptors.response.use(
    (response) => response,
    (error) => {
      // 接口错误日志上报
      console.log('====>error', error);
      const currentAccountTemp = getCurrentAccount();
      logHandler({
        name: '接口错误',
        info: `${baseUrl}, url:${error.config?.url}, method:${error.config?.method}; error:${JSON.stringify(error)}`,
        desc: `name:${currentAccountTemp?.name}; openid:${currentAccountTemp?.openid}；`,
      });
      if (error.code === 'ECONNABORTED' && error.message.indexOf('timeout') !== -1) {
        // 超时
        // @ts-ignore
        MessagePlugin.closeAll(); // 审批助手调用多次,大量超时报错

        // 添加 noError 参数判断
        if (!error.config?.noError) {
            MessagePlugin.error(i18n.global.t('network.timeout'));
        }
        return Promise.reject(error);
      }
      if (error.code === 'ERR_NETWORK') {
        // 出错
        MessagePlugin.closeAll();
        // @ts-ignore
        MessagePlugin.error(i18n.global.t('network.error'));
        return Promise.reject(error);
      }

      const msg = getErrorStatusMsg(error);
      switch (error?.response?.status) {
        case 401:
        case 403:
          const currentAccountTemp = getCurrentAccount();
          logHandler({
            name: '账号登录失效403',
            info: `error:${JSON.stringify(error)}, token: ${getAccesstoken()}`,
            desc: `${baseUrl}, name:${currentAccountTemp?.name}; openid:${currentAccountTemp?.openid}`,
          });
          if (error.config?.url?.indexOf('/v1/profiles/me') !== -1) {
            // /v1/profiles/me 在onKickedOffline中验证token失效处理
            console.log('====>/v1/profiles/me', 'error.config?.url');
          } else {
            quit(true);
          }
          break;
        default:
          break;
      }

      if (onRejected) return onRejected(error, msg);
      if (msg) {
        MessagePlugin.error({
          content: msg,
          duration: 3000,
          zIndex: ********,
          offset: ['0', '30'],
        });
      }

      return Promise.reject(error);
    },
  );

  if (reqInterceptor) {
    instance.interceptors.request.use(reqInterceptor);
  }

  instance.interceptors.request.use(
    (config) => {
      // 是否需要设置 token
      config.headers.Authorization = `Bearer ${getAccesstoken()}`;
      config.headers['Accept-Language'] = getServerLang();
      config.headers['Content-Type'] = config.headers['Content-Type']
        ? config.headers['Content-Type']
        : 'application/json';
      config.headers['Sm-Device-Id'] = getSMDeviceId() || '';
      config.headers['Phone-Type'] = agentType;

      config.headers['App-Version'] = configInfo.version;
      // config.headers["App-BuildNumber"] = configInfo.buildNumber;

      // 签名相关2323112222212
      // step1 获取URLI
      const url = config?.params ? getSignUrl(config.url, config.params) : config.url;
      // 生成一个8位数的随机数
      const random = +String(Math.random()).slice(2, 10);
      // teamid,没有必须给空字符串
      const teamId = config.headers?.teamId || config.headers?.teamid || '';
      // timestamp
      const timestamp = Date.now();
      // body
      const body = config?.data ? JSON.stringify(config.data) : '';
      // openid 没有必须给空字符串
      const openid = getOpenid() || '';
      // console.log(
      //   `${url}${random}${teamId}${timestamp}${body}${openid}`,
      //   "aaaaa"
      // );
      // 生成签名
      const hmac = CryptoJS.HmacSHA256(`${url}${random}${teamId}${timestamp}${body}${openid}`, openid);
      const sign = CryptoJS.enc.Base64.stringify(hmac);
      config.headers.Nonce = random;
      config.headers.TeamId = teamId;
      config.headers.Timestamp = timestamp;
      config.headers.Signature = sign;
      return config;
    },
    (error) => {
      Promise.reject(error);
    },
  );

  return instance;
}

const getSquareId = () => {
  const hash = window.location.hash;
  if (hash.includes('digital_platform_album') || hash.includes('work_bench_album')) {
    return localStorage.getItem('personSquareId');
  }
  if (hash.includes('activity/manage')) {
    const match = hash.match(/#\/activity\/manage\/(\d+)/);
    const id = match[1];
    return localStorage.getItem(`activitySquareId_${id}`);
  }
  if (hash.includes('activityParticipantDetail') || hash.includes('activity/ablum-view')) {
    return localStorage.getItem('activityLoginSquareId');
  }
  const squareCache = JSON.parse(localStorage.square);
  return squareCache.squareInfo?.square?.squareId;
};

const useProjectId: RequestIntercept = (config) => {
  if (!config.headers.projectId) {
    config.headers.projectId = 0;
  }

  const hash = window.location.hash;
  if (hash.includes('#/workBenchIndex/') && !config.headers.teamId) {
    const teamId = localStorage.getItem('honorteamid');
    teamId && (config.headers.teamId = teamId);
  }

  return config;
};
const useOrganizeErrorHandler = (error, commonErrMsg?: string) => {
  const { data } = error.response;
  console.log('useOrganizeErrorHandler', data, error);
  if (data.code === 0) {
    return Promise.resolve();
  }
  if (data.code === 10020) {
    MessagePlugin.error(data.message);
  }

  return Promise.reject(error);
};

// const useServiceId: RequestIntercept = (config) => {
//   if (!config.headers.projectId) {
//     config.headers.projectId = 0;
//   }
//   return config;
// };

// 获取 base url
export const getBaseUrl = (env: ApiModule) => getApiConfig(Env)[env];

export const iam_srvRequest = createApiInstance(getApiConfig(Env)['iam-srv']);

export const im_syncRequest = createApiInstance(getApiConfig(Env)['im-sync']);

export const organizeRequest = createApiInstance(getApiConfig(Env)['organize']);

export const familiesRequest = createApiInstance(getApiConfig(Env)['families']);

export const client_orgRequest = createApiInstance(getApiConfig(Env)['client-organize'], useProjectId);
export const client_orgRequest_disk = createApiInstance(getApiConfig(Env)['client-organize'], (config) => {
  config.headers.teamId = config.headers.teamId || localStorage.getItem('workBenchTeamid');

  return config;
});
export const simple_orgRequest = createApiInstance(getApiConfig(Env)['client-organize']);

const HEADER_X_MD_SQUARE_ID = 'x-md-square-id';
const HEADER_TEAM_ID = 'teamId';

export const squareRequest = createApiInstance(
  getApiConfig(Env)['square'],
  (config) => {
    // 指定路径强制以个人身份访问
    const hash = window.location.hash;
    const inDigitalPlatform = hash.includes('#/digitalPlatformIndex/post_list') || hash.includes('digital_platform_album');
    const inActivityCr = hash.includes('#/activity/manage');
    const inActivityInvolved = hash.includes('activityParticipantDetail') || hash.includes('ablum-view');
    const pathExclude = inDigitalPlatform || inActivityInvolved;

    const square = getOrgSquare();
    const teamId = (config as any).teamId || square?.teamId;
    const isPersonal = (config as any)._onlyPersonal || pathExclude || !teamId || +teamId === -1;

    if (inActivityCr && (config[HEADER_X_MD_SQUARE_ID] || teamId)) {
      config.headers[HEADER_X_MD_SQUARE_ID] = config[HEADER_X_MD_SQUARE_ID];
      config.headers[HEADER_TEAM_ID] = teamId;
      return config;
    }

    // 组织身份处理
    if (!isPersonal) {
      if (teamId) {
        config.headers[HEADER_TEAM_ID] = teamId;
      } else if (square?.squareId && !inActivityCr) {
        config.headers[HEADER_X_MD_SQUARE_ID] = square.squareId;
        console.warn('Deprecated: x-md-square-id 请求头即将废弃，用 teamId 替换');
      }

      // 确保至少有一个组织标识头
      if (!config.headers[HEADER_TEAM_ID] && !config.headers[HEADER_X_MD_SQUARE_ID]) {
        console.error('组织用户请求缺少请求头');
      }
    } else {
      // 个人身份清除组织头
      delete config.headers[HEADER_X_MD_SQUARE_ID];
      delete config.headers[HEADER_TEAM_ID];
    }
    return config;
  },
  handleRequestError,
);

export const portalRequest = createApiInstance(getApiConfig(Env)['portal']);

export const square_manageRequest = createApiInstance(getApiConfig(Env)['square-operation-manage']);

export const know_srvRequest = createApiInstance(getApiConfig(Env)['know-srv']);

export const ringkolRequestForExport = createApiInstance(getApiConfig(Env)['ringkol'], 
  (config) => {
    // config.headers.teamId = getMemberTeamID();

    if (window.localStorage.getItem('profile')) {
      config.headers.Zone = JSON.parse(window.localStorage.getItem('profile')).area;
    }

    return config;
  }
);

export const businessRequest = createApiInstance(getApiConfig(Env)['business']);

export const orderRequest = createApiInstance(getApiConfig(Env)['order']);

export const toolsRequest = createApiInstance(getApiConfig(Env)['tools']);

export const globalRequest = createApiInstance(getApiConfig(Env)['global']);

export const activitiesRequest = createApiInstance(
  getApiConfig(Env)['activities'],
  (config) => {
    config.headers.teamId = localStorage.getItem('activityTeamId');
    return config;
  },
  handleRequestError,
);
export const activitiesRequestV2 = createApiInstance(
  getApiConfig(Env)['activitiesV2'],
  (config) => {
    config.headers.teamId = config.headers.teamId;
    return config;
  },
  handleRequestError,
);

// 工作台
export const workBenchRequest = createApiInstance(getApiConfig(Env)['organize'], (config) => {
  config.headers.teamId = config.headers.teamId || localStorage.getItem('workBenchTeamid');

  return config;
});
// 审批相关
export const approvalRequest = createApiInstance(
  getApiConfig(Env)['organize'],
  (config) => {
    config.headers.teamId = localStorage.getItem('approvalteamid');
    return config;
  },
  handleRequestErrorToast,
);
// 添加公共自定义 header
export const workbench_apps = createApiInstance(
  getApiConfig(Env)['client-organize'],
  (config) => {
    config.headers.teamId = config.headers.teamId || localStorage.getItem('workBenchTeamid');
    return config;
  },
  handleRequestErrorToastPzy,
);
// 添加公共自定义 header
export const approve_clientRequest = createApiInstance(
  getApiConfig(Env)['client-organize'],
  (config) => {
    const getKey = () => {
      if (window.location.href.includes('main/message')) {
        return 'approvalteamid-im';
      }
      if (window.location.href.includes('zhixing')) {
        return 'approvalteamid-zx';
      }
      return 'approvalteamid';
    };
    config.headers.teamId = config.headers.teamId || localStorage.getItem(getKey());
    console.log('请求头teamId', config.headers.teamId);
    return config;
  },
  handleRequestErrorToastPzy,
);

// 添加公共自定义 header
export const approve_clientRequest_pzy = createApiInstance(
  getApiConfig(Env)['client-organize'],
  (config) => {
    const getKey = () => {
      if (window.location.href.includes('main/message')) {
        return 'approvalteamid-im';
      }
      if (window.location.href.includes('zhixing')) {
        return 'approvalteamid-zx';
      }
      return 'approvalteamid';
    };
    config.headers.teamId = config.headers.teamId || localStorage.getItem(getKey());
    return config;
  },
  handleRequestErrorToastPzy1,
);

export const albumRequest = createApiInstance(
  getApiConfig(Env)['client-organize'],
  (config) => {
    config.headers.teamId = config.headers.teamId || localStorage.getItem('albumTeamId');
    config.headers.loginSquareId = getSquareId();
    console.log(config, 'config.headersconfig.headers');

    return useProjectId(config);
  },
  // handleRequestErrorToast
);

export const customerRequest = createApiInstance(
  getApiConfig(Env)['client-organize'],
  (config) => {
    config.headers.teamId = config.headers.teamId || localStorage.getItem('customerTeamId');
    return useProjectId(config);
  },
  handleRequestErrorToast,
);

export const deviceRequest = createApiInstance(
  getApiConfig(Env)['client-organize'],
  (config) => {
    config.headers.teamId = config.headers.teamId || localStorage.getItem('deviceTeamId');
    return useProjectId(config);
  },
  handleRequestErrorToast,
);

// lss 2023-10-8 服务中心
export const lssClientOrganizeServiceRequest = createApiInstance(
  getApiConfig(Env)['client-organize'],
  (config) => {
    if (!config.headers.teamId) {
      config.headers.teamId = getServiceTeamID();
    }
    // return useProjectId(config);
    return config;
  },
  onHandleError,
);

// lss 2023-11-8 数字商协会
export const lssClientOrganizeMemRequest = createApiInstance(
  getApiConfig(Env)['client-organize'],
  (config) => {
    if (!config.headers.teamId) {
      config.headers.teamId = getMemberTeamID();
    }
    // return useProjectId(config);
    return config;
  },
  onHandleError,
);
// lss 2023-12-25 数字商协会 调广场数据
export const lssSquareMemRequest = createApiInstance(
  getApiConfig(Env)['square'],
  (config) => {
    if (!config.headers.teamId) {
      config.headers.teamId = getMemberTeamID();
    }
    // return useProjectId(config);
    return config;
  },
  onHandleError,
);

// lss 2023-9-6 工程管理
export const lssClientOrganizeProjectRequest = createApiInstance(
  getApiConfig(Env)['client-organize'],
  (config) => {
    config.headers.teamId = getProjectTeamID();
    return useProjectId(config);
  },
  onHandleError,
);
// 组织抬头
export const groupHeadList = createApiInstance(
  getApiConfig(Env)['organize'],
  (config) => {
    console.log(config, 'configconfigconfig');
    if (config.params?.teamId) {
      config.headers.Teamid = config.params?.teamId;
    }
    if (config.params?.TeamId) {
      config.headers.Teamid = config.params?.TeamId;
    }
    return useProjectId(config);
  },
  onHandleError,
);
// lss 2023-9-6 工程管理
export const lssOrganizeProjectRequest = createApiInstance(
  getApiConfig(Env)['organize'],
  (config) => {
    config.headers.teamId = getProjectTeamID();
    return useProjectId(config);
  },
  onHandleError,
);

// lss 2023-9-6  帮助中心
export const lssClientOrganizeMemberRequest = createApiInstance(
  getApiConfig(Env)['client-organize'],
  (config) => {
    config.headers.teamId = getMemberTeamID();

    if (window.localStorage.getItem('profile')) {
      config.headers.Zone = JSON.parse(window.localStorage.getItem('profile')).area;
    }

    return useProjectId(config);
  },
  onHandleError,
);

export const partnersRequest = createApiInstance(
  getApiConfig(Env)['client-organize'],
  (config) => {
    config.headers.teamId = config.headers.teamId || localStorage.getItem('partnerTeamId');
    return useProjectId(config);
  },
  handleRequestErrorToast,
);

export const businessOpportunityRequest = createApiInstance(
  getApiConfig(Env)['client-organize'],
  (config) => {
    config.headers.teamId = config.headers.teamId || localStorage.getItem('businessTeamId');
    return useProjectId(config);
  },
  handleRequestErrorToast,
);

export const supplierRequest = createApiInstance(
  getApiConfig(Env)['client-organize'],
  (config) => {
    config.headers.teamId = config.headers.teamId || localStorage.getItem('supplierTeamId');
    return useProjectId(config);
  },
  handleRequestErrorToast,
);

// 创建基于 client-organize 的服务工厂函数。用于重构上述 16 个 axios 实例
/** @deprecated */
export const createClientApiFactory = (type: 'digitalPlatform') => {
  const teamIdKey = {
    digitalPlatform: 'digital_platform_teamid',
  }[type] || '';

  const noop = () => undefined;
  const onRejected = {
    digitalPlatform: handleRequestErrorToast,
  }[type] || noop;

  return createApiInstance(
    getApiConfig(Env)['client-organize'],
    (config) => {
      config.headers.teamId = config.headers.teamId || localStorage.getItem(teamIdKey);
      return useProjectId(config);
    },
    onRejected,
  );
};

// FIXME 论坛 router beforeEnter 用此方法会导致无法执行（报：未在使用前引用）
// export const digitalPlatformRequest = createClientApiFactory('digitalPlatform');
export const digitalPlatformRequest = createApiInstance(
  getApiConfig(Env)['client-organize'],
  (config) => {
    config.headers.teamId = config.headers.teamId || localStorage.getItem('digital_platform_teamid');
    return useProjectId(config);
  },
  (error, commonErrMsg?: string) => {
    const { data, config } = error.response;
    const msg = getErrorStatusMsg(error) || (data as any).message || commonErrMsg;
    if (!config.params?.hideMsg && msg) {
      MessagePlugin.error(msg);
    }
    return Promise.reject(error);
  },
);

export const ringkolRequest = (() => {
  const instance = createApiInstance(getApiConfig(Env)['ringkol'], (config) => {
    config.headers.teamId = config.headers.teamId || localStorage.getItem('digital_platform_teamid');
    const ownerId = config.headers['X-Auth-Id'] || (config as any)?.ownerId || localStorage.getItem(HEADER_FORUM_KEY);

    if (ownerId) {
      config.headers['X-Auth-Id'] = ownerId;
    }

    const profileStr = window.localStorage.getItem('profile');
    if (profileStr) {
      try {
        const { area } = JSON.parse(profileStr);
        if (area) config.headers.Zone = area;
      } catch (error) {}
    }

    return config;
  });

  instance.interceptors.response.use((res) => {
    const { code } = res.data;
    try {
      const _data = res.config.params || JSON.parse(res.config?.data || '{}');
      if (!_data?.notError && code !== undefined && code !== 0) return handleRingkolError(res);
    } catch (e) {
      console.log('e', e);
    }
    return res;
  });

  return instance;
})();

// 避免和论坛发布侧冲突，单独封装论坛管理侧方法
export const ringkolRequestForForumAdmin = (() => {
  const instance = createApiInstance(getApiConfig(Env)['ringkol'], (config) => config);

  instance.interceptors.response.use((res) => {
    const { code } = res.data;
    if (code === 2001) {
      emitter.emit('noForumAdminPermission');
    }
    if (code !== undefined && code !== 0) return Promise.reject(res.data);
    return res;
  });

  return instance;
})();

function getSignUrl(url, query?) {
  if (query) {
    const keyArr = Object.keys(query);
    let str = '';
    keyArr.forEach((key, index) => {
      if (index === 0) {
        str += `${key}=${query[key]}`;
      } else {
        str += `&${key}=${query[key]}`;
      }
    });
    return `${url}?${str}`;
  }
  return url;
}
