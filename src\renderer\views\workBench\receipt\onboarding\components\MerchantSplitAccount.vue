<script setup lang='ts'>
import { storeToRefs } from 'pinia';
import { useMerchantStore } from '@renderer/store/modules/merchant';
import { onMountedOrActivated } from '@renderer/hooks/onMountedOrActivated';
import { ref, computed } from 'vue';
import to from 'await-to-js';
import { applySeparateAccountsReceiver } from '@renderer/api/workBench/commission';
import LynkerSDK from '@renderer/_jssdk';
import { useComponentComm } from '../hooks/useComponentComm';
import UploadAgreement from './UploadAgreement.vue';

const props = defineProps<{
  /** 是否为分佣入网 */
  isCommission?: boolean;
}>();

const emit = defineEmits(['prev-step', 'submit', 'success']);

const merchantStore = useMerchantStore();
const { info: merchantInfo } = storeToRefs(merchantStore);

// 使用统一组件通信机制
const { createComponentInterface } = useComponentComm({
  step: computed(() => (props.isCommission ? 1 : 2)), // 根据是否分佣调整步骤
  flowControl: computed(() => ({})),
  isCommission: computed(() => props.isCommission || false),
  setFlowStatus: () => { /* 空实现 */ },
  updateFlowStatus: () => { /* 空实现 */ },
});

// 创建组件接口
const componentInterface = createComponentInterface('MerchantSplitAccount');

onMountedOrActivated(() => {
  merchantStore.getDetail();
});

// 上传协议
const uploadAgreementVisible = ref(false);
const loading = ref(false);

// 申请
const handleApplyClick = async () => {
  if (!props.isCommission) {
    uploadAgreementVisible.value = true;
    return;
  }

  loading.value = true;
  const [err] = await to(applySeparateAccountsReceiver(merchantStore.teamId));

  loading.value = false;
  if (err) {
    const errorMessage = (err as any).response?.data?.message || '申请失败';

    // 使用统一通信机制报告错误
    componentInterface.reportError({
      code: 'SPLIT_ACCOUNT_APPLY_ERROR',
      message: errorMessage,
      context: { error: err, isCommission: props.isCommission },
      level: 'error',
    });

    return;
  }

  // 使用统一通信机制发送成功事件
  componentInterface.emitEvent('merchant:split', {
    type: 'commission-success',
    message: '分佣申请成功',
    isCommission: props.isCommission,
  });

  // 分佣通过
  emit('success');
  LynkerSDK.workBench.reload();
};
</script>

<template>
  <div class="split-container">
    <div class="content-wrapper">
      <t-alert
        theme="info"
        :message="isCommission ? '申请开通分佣账户后，可与其他企业进行关联，并获取交易佣金' : '申请开通分账业务时所上传的协议盖章需为对应商户入网的营业执照主体一致'"
        class="mb-16!"
      />

      <div class="info-content">
        <div class="info-content__title">申请开通分佣业务</div>
        <div class="info-content-item">
          <div class="info-content-item__label">收款账户类型：</div>
          <div class="info-content-item__value">对公</div>
        </div>
        <div class="info-content-item">
          <div class="info-content-item__label">收款账户开户名称：</div>
          <div class="info-content-item__value">{{ merchantInfo?.pay_merchant_legal_person_bank_card_bank_name }}</div>
        </div>
        <div class="info-content-item">
          <div class="info-content-item__label">收款账户开户行号：</div>
          <div class="info-content-item__value">{{ merchantInfo?.pay_merchant_legal_person_bank_card_open_bank_number }}</div>
        </div>
        <div class="info-content-item">
          <div class="info-content-item__label">收款账户清算行行号：</div>
          <div class="info-content-item__value">{{ merchantInfo?.clear_bank_code }}</div>
        </div>
      </div>

      <t-divider class="my-24!" />

      <div class="tip-wrap">
        <div class="tip-title">温馨提示：</div>
        <p v-if="isCommission">1、商户分佣：用于后续交易完成后，订单金额获取对应比例的分佣</p>
        <template v-else>
          <p>1、商户分账：用于后续交易完成后，订单金额分账</p>
          <p>2、开通商户分账业务需要进行人工审核，预计需要1-3工作工作日</p>
        </template>
      </div>
    </div>
  </div>

  <UploadAgreement v-model:visible="uploadAgreementVisible" :is-commission="isCommission" @upload-success="$emit('submit');" />

  <!-- 底部操作栏 -->
  <div class="action-bar">
    <t-button variant="outline" class="min-w-80! font-600!" @click="emit('prev-step')">上一步</t-button>
    <t-button
      theme="primary"
      class="min-w-80"
      :loading="loading"
      :disabled="loading"
      @click="handleApplyClick"
    >
      申请
    </t-button>
  </div>
</template>

<style lang='less' scoped>
.split-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  flex: 1;
  border-bottom-right-radius: 8px;
  border-bottom-left-radius: 8px;
  position: relative;
  overflow: hidden;
  margin-bottom: 102px;
}

.content-wrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  padding: 24px;
  padding-top: 0;
}

.info-content {
  width: 100%;
  background-color: #fff;
}

.info-content__title {
  color: var(--text-kyy_color_text_1, #1A2139);
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
  padding-left: 11px;
  position: relative;
  margin-bottom: 8px;
  &:before {
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    content: '';
    display: inline-block;
    width: 3px;
    height: 16px;
    border-radius: 8px;
    background: var(--brand-kyy_color_brand_default, #4D5EFF);
  }
}

.info-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-content-item {
  display: flex;
  align-items: center;
  gap: 4px;
  border-radius: 8px;
  background: var(--bg-kyy_color_bg_light, #FFF);
  .info-content-item__label {
    color: var(--text-kyy_color_text_3, #828DA5);
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
  }
  .info-content-item__value {
    color: var(--text-kyy_color_text_1, #1A2139);
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
  }
}

.tip-wrap {
  border-radius: 8px;
  color: var(--text-kyy_color_text_3, #828DA5);
  display: flex;
  flex-direction: column;
  gap: 8px;
  .tip-title {
    color: var(--text-kyy_color_text_1, #1A2139);
    font-size: 14px;
    line-height: 22px;
  }
}

.action-bar {
  position: fixed;
  bottom: 16px;
  left: 50%;
  right: 0;
  transform: translateX(-50%);
  z-index: 9;
  display: flex;
  width: 872px;
  height: 64px;
  padding: 16px 24px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  border-radius: 8px;
  border-top: 1px solid var(--bg-kyy_color_bg_list_hover, #f3f6fa);
  background: var(--bg-kyy_color_bg_light, #fff);
}
</style>
