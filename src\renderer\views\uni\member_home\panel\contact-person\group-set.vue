<template>
  <div class="box">
    <t-dialog
      v-model:visible="visible"
      :header="t('ebook.grs')"
      :on-close="onCancel"
      :destroy-on-close="true"
      width="536px"
      :confirmLoading="confirmLoading"
      :confirm-btn="'确定'"
      :closeOnOverlayClick="false"
      @confirm="onConfirm"
    >
      <div class="se-box">
        <div class="label">{{ t("ebook.grs") }}</div>
        <t-select
          v-model="groupSelected"
          multiple
          :popupProps="{
            overlayInnerStyle: {
              width: '488px',
              maxHeight: '212px',
            },
            overlayClassName: 'gset-select-option',
          }"
          :placeholder="'请选择分组'"
        >
          <template v-for="item in groupList" :key="item.id">
            <t-option :label="item.group_name" :value="item.id" />
          </template>
        </t-select>
      </div>
    </t-dialog>
  </div>
</template>
<script setup lang="ts">
import { getGovernmentlist, setcontactsGroup } from "@renderer/api/uni/api/businessApi";
import to from "await-to-js";
import { MessagePlugin } from "tdesign-vue-next";
import { ref } from "vue";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const props = defineProps({
  teamId: {
    type: String,
    default: "",
  },
  type: {
    type: Number,
    default: 1,
  },
});
const visible = ref(false);
const confirmLoading = ref(false);
const eid = ref("");
const groupList = ref([]);
const groupSelected = ref([]);

const emit = defineEmits(["change"]);
const onConfirm = () => {
  // if (groupSelected.value.length === 0) {
  //   return MessagePlugin.error(t("ebook.sgt"));
  // }
  confirmLoading.value = true;
  if (props.type === 1) {
    emit("change", groupSelected.value);
    visible.value = false;
  confirmLoading.value = false;
} else {
    setReq();
  }
};

const setReq = () => {
  const params = {
    group_ids: groupSelected.value || [],
    id: eid.value,
  };
  setcontactsGroup(params, props.teamId)
    .then((res) => {
      console.log(res);
      confirmLoading.value = false;

      if (res.data?.code === 0) {
        MessagePlugin.success(t("ebook.grsu2"));
        visible.value = false;
        emit("change", groupSelected.value);
      } else {
        MessagePlugin.error(res.data?.msg);
      }
    })
    .catch((err) => {
      MessagePlugin.error(err.message || t("ebook.seterr"));
      confirmLoading.value = false;
    });
};

const onCancel = () => {
  visible.value = false;
};

const getGroupList = async () => {
  try {
    const [err, res] = await to(getGovernmentlist(props.teamId));
    if (err) {
      return;
    }
    const { data }: any = res;
    groupList.value = data?.data?.list || [];
  } catch (error) {}
};

// 现在有一个div，宽度是276px,用来显示多个名字字符串，是通过数组tostring()方法转换的，现在需要实现的功能是当名字超出一行时显示名字加...等x人，要如何实现
const setOpen = (selected, id) => {
  console.log(selected, "selected");

  eid.value = id;
  visible.value = true;
  groupSelected.value = selected || [];
  getGroupList();
};

const onClose = () => {
  visible.value = false;
};

defineExpose({
  setOpen,
  onClose,
});
</script>

<style lang="less" scoped>
.box {
  width: 100%;
}
.se-box {
  margin-top: 24px;
  margin-bottom: 16px;
  .label {
    color: var(--text-kyy_color_text_3, #828da5);
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
    margin-bottom: 8px;
  }
}
.name-box {
}
:deep(.t-select-input .t-input__suffix) {
  top: 4px;
  height: 20px;
}
:deep(.t-select-input .t-input) {
  max-height: 90px;
  overflow-y: auto;
}

:deep(.t-select-input .t-tag--text) {
  max-width: 420px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
:deep(.t-select__list .t-select-option) {
  height: fit-content !important;
}
:deep(.t-select__list .t-checkbox__label) {
  text-overflow: unset;
  white-space: normal;
}
</style>
