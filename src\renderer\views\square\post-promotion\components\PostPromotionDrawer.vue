<script setup lang="ts">
import { computed, onMounted, reactive, ref } from 'vue';
import KyyTabs from '@renderer/views/square/components/Tabs.vue';
import { addPostPromote, getPromotableDigitalPlatformList } from '@renderer/api/square/post';
import to from 'await-to-js';
import { DigitalPlatformItem, PostPromoteChannelType } from '@renderer/api/square/models/post';
import { digitalConnectApi, digitalConnectGet } from '@renderer/views/niche/apis/create';
import Empty from '@renderer/components/common/Empty.vue';
import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next';
import { useI18n } from 'vue-i18n';
import { AxiosResponse, AxiosError } from 'axios';
import SquareAvatar from '@/views/square/components/SquareAvatar.vue';
import { SquareType } from '@/api/square/enums';
import { useSquareStore } from '../../store/square';
import { usePostPromotion } from '../hooks';

const props = withDefaults(defineProps<{
  modelValue: boolean,
  id: string,
}>(), {
  modelValue: false,
});
const emit = defineEmits(['update:modelValue', 'upgrade', 'buy', 'refresh']);

const visible = computed({
  get() {
    return props.modelValue;
  },
  set(value) {
    emit('update:modelValue', value);
  },
});

const { t } = useI18n();
const store = useSquareStore();
const btnLoading = ref({});
// Mac(应用市场)的包
const isMas = __APP_ENV__.VITE_APP_MAS;

const tabs = reactive([
  { label: '数字商协', value: 1 },
  { label: '数字城市', value: 2 },
  { label: '数字CBD', value: 3 },
  { label: '数字社群', value: 4 },
  { label: '数字高校', value: 5 },
]);
const listDataMap = ref({ 0: [], 1: [], 2: [], 3: [], 4: [] });
const activeTab = ref(0);
const currentTab = computed(() => tabs[activeTab.value].value);
const currentList = computed(() => listDataMap.value[activeTab.value] || []);

const tabChange = (_, index: number) => {
  activeTab.value = index;
  getList(true);
};

// 是否是体验套餐
const isTrial = computed(() => store.annualFeeInfo?.annualFeeDetail?.trial ?? false);
// 年费套餐是否过期
const isAnnualFeeExpired = computed(() => new Date(store.annualFeeInfo?.annualFeeExpiredAt).getTime() < new Date().getTime());

// 连接信息
const connectInfo = ref({ total: -1, consume: 0, usable: -1 });
const getConnectInfo = async () => {
  if (!store.teamId) return;

  const [err, res] = await to(digitalConnectGet(store.teamId));
  if (err) return;
  connectInfo.value = res.data.data;
};

// 本组织
const isSelf = (item: DigitalPlatformItem) => store.teamId === item.digitalPlatform.teamId;
const { handleRequestErr } = usePostPromotion();

// 推广列表
const loading = ref(false);
const getList = async (tabClick = false) => {
  if (!props.id) return;
  // 有数据则不重新请求
  if (tabClick && currentList.value.length) return;

  loading.value = true;
  const [err, res] = await to(getPromotableDigitalPlatformList({
    post_id: props.id,
    digital_platform_type: currentTab.value,
  }));

  loading.value = false;
  if (err) return;

  const list = res.data.items;
  listDataMap.value[activeTab.value] = list;

  // 默认选中有数据的 Tab
  if (!tabClick && !list.length && activeTab.value < tabs.length - 1) {
    activeTab.value++;
    getList();
  }
};

// 添加推广
const addPromote = async (item: DigitalPlatformItem) => {
  const channelId = item.digitalPlatform.teamId;
  btnLoading.value[channelId] = true;
  const [err] = await to<AxiosResponse, AxiosError<{ message: string }>>(addPostPromote({
    postId: props.id,
    channelId,
    channelType: PostPromoteChannelType.DigitalPlatform,
    digitalPlatformType: currentTab.value,
  }));
  btnLoading.value[channelId] = false;

  if (err) {
    await handleRequestErr(err, {
      onConfirm: () => {
        visible.value = false;
      },
      onRefresh: () => {
        emit('refresh');
      },
    });
    return;
  }

  emit('refresh');
  MessagePlugin.success(isSelf(item) ? '添加推广成功' : '申请成功，请等待管理员处理');
  getList();
};

// 连接
const connect = async (item: DigitalPlatformItem) => {
  const { usable } = connectInfo.value;

  let header = isMas ? '提示' : '连接数不足';
  let body = isMas ? '组织连接数不足' : '组织连接数不足，可前往升级获取更高权益';
  let cancelBtn = isMas ? null : t('square.action.cancel');
  let confirmBtn = isMas ? '我知道了' : '立即升级';
  let upgrade = true;

  // 体验套餐
  if (isTrial.value) {
    header = '连接数不足';
    body = '体验套餐连接数不足，请先购买套餐';
    confirmBtn = '立即购买';
    upgrade = false;
    // 体验套餐过期
    if (isAnnualFeeExpired.value) {
      body = '体验套餐已过期，连接数不足，请先购买套餐';
    }
  } else if (isAnnualFeeExpired.value) {
    // 未续费新套餐
    body = '当前套餐已过期，连接数不足，请先续费套餐';
    confirmBtn = '立即续费';
    upgrade = false;
  }

  // 无连接数
  if (usable === 0) {
    const confirm = DialogPlugin.confirm({
      header,
      body,
      closeBtn: null,
      theme: 'info',
      cancelBtn,
      confirmBtn,
      zIndex: 2502,
      onConfirm: async () => {
        if (upgrade) {
          !isMas && emit('upgrade');
        } else {
          emit('buy');
        }

        confirm.hide();
      },
      onClose: () => {
        confirm.hide();
      },
    });
    return;
  }

  if (usable > 0 || usable === -1) {
    const confirm = await new Promise((resolve) => {
      const instance = DialogPlugin.confirm({
        header: '连接数字平台',
        body: '连接将消耗连接数，连接后可添加推广',
        closeBtn: null,
        theme: 'info',
        confirmBtn: t('square.action.confirm'),
        zIndex: 2502,
        onConfirm: async () => {
          resolve(true);
          instance.hide();
        },
        onClose: () => {
          resolve(false);
          instance.hide();
        },
      });
    });

    if (!confirm) return;
  }

  // 建立连接
  const channelId = item.digitalPlatform.teamId;
  btnLoading.value[channelId] = true;
  const [err] = await to(digitalConnectApi({
    consume_team_id: store.teamId,
    belong_team_id: channelId,
  }));
  btnLoading.value[channelId] = false;
  if (err) return;

  getConnectInfo();
  MessagePlugin.success('连接成功');
  getList();
};

const init = () => {
  getConnectInfo();
  getList();
};

onMounted(() => {
  init();
});
</script>

<template>
  <t-drawer
    v-model:visible="visible"
    size="472"
    attach="body"
    header="添加数字平台推广"
    prevent-scroll-through
    class="square-d-post-promotion"
    :close-btn="true"
    :footer="null"
    :z-index="2501"
    v-bind="$attrs"
  >
    <template #closeBtn>
      <iconpark-icon name="iconerror" class="text-24 cursor-pointer" />
    </template>

    <div v-if="store.teamId" class="stats-alert">
      <div>当前连接数权益 ：</div>
      <div class="primary">{{ connectInfo.total === -1 ? $t("niche.wuxx") : `${connectInfo.total}个` }}</div>
      <div class="danger mx-8">{{ $t("niche.ysyong") }} <span class="font-600">{{ connectInfo.consume }}个</span></div>
      <div class="success">可用 <span class="font-600">{{ connectInfo.usable === -1 ? $t("niche.wuxx") : `${connectInfo.usable}个` }}</span></div>
    </div>

    <KyyTabs
      :default-value="activeTab"
      :list="tabs"
      align="flex-start"
      class="tab-wrap"
      height="36px"
      @change="tabChange"
    />

    <div v-loading="loading" class="list-wrap">
      <div v-for="item in currentList" :key="item.digitalPlatform.teamId" class="list-item">
        <SquareAvatar
          :square="{ avatar: item.digitalPlatform.teamLogo, name: item.digitalPlatform.teamName, squareType: SquareType.Organization }"
          size="64px"
          class="avatar"
          shape="round"
        />

        <div class="right-content">
          <div v-if="isSelf(item)" class="tag tag-blue">自</div>
          <div v-if="store.teamId && !item.connected" class="tag tag-red">未连接</div>
          <div class="title">{{ item.digitalPlatform.teamName }}</div>
        </div>

        <t-button
          v-if="item.added"
          theme="primary"
          disabled
          class="btn"
        >
          已添加
        </t-button>
        <t-button
          v-else-if="store.teamId && !item.connected && !isSelf(item)"
          theme="primary"
          class="btn"
          :loading="btnLoading[item.digitalPlatform.teamId]"
          @click="connect(item)"
        >
          连接
        </t-button>
        <t-button
          v-else-if="!item.added"
          theme="primary"
          class="btn"
          :loading="btnLoading[item.digitalPlatform.teamId]"
          @click="addPromote(item)"
        >
          添加
        </t-button>
      </div>

      <Empty
        v-if="!loading && !currentList.length"
        name="no-friend-list"
        center
        tip="暂无数据"
      />
    </div>
  </t-drawer>
</template>

<style lang="less">
.square-d-post-promotion {
  .t-drawer__body {
    display: flex;
    flex-direction: column;
    padding: 0;
    color: var(--text-kyy_color_text_1, #1A2139);
  }

  .stats-alert {
    display: flex;
    padding: 8px 12px;
    align-items: center;
    align-self: stretch;
    border-radius: 8px;
    margin: 0 16px 4px;
    background: linear-gradient(90deg, #F1F8EB 1.3%, rgba(216, 236, 255, 0.63) 52.27%, rgba(220, 230, 255, 0.63) 99.49%);
    color: var(--text-kyy_color_text_1, #1A2139);
    font-size: 14px;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
    .primary {
      color: var(--brand-kyy_color_brand_default, #4D5EFF);
      font-weight: 600;
    }
    .danger {
      color: var(--error-kyy_color_error_default, #D54941);
    }
    .success {
      color: var(--success-kyy_color_success_active, #499D60);
    }
  }

  .tab-wrap {
    flex-shrink: 0;
    border-bottom: 1px solid var(--divider-kyy_color_divider_deep, #D5DBE4);
  }

  .list-wrap {
    display: flex;
    padding: 12px 16px;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    flex: 1 0 0;
    align-self: stretch;
    background: var(--bg-kyy_color_bg_deep, #F5F8FE);
    overflow-y: auto;

    .list-item {
      display: flex;
      width: 440px;
      height: 96px;
      padding: 16px;
      align-items: center;
      gap: 12px;
      border-radius: 8px;
      background: var(--bg-kyy_color_bg_light, #FFF);
      .avatar {
        width: 64px;
        height: 64px;
        flex-shrink: 0;
        border-radius: 8px;
      }

      .right-content {
        display: flex;
        flex: 1;
        gap: 4px;
        .title {
          color: var(--text-kyy_color_text_1, #1A2139);
          .multi-ellipsis(2);
        }
      }

      .tag {
        display: inline-block;
        height: 20px;
        line-height: 20px;
        min-height: 20px;
        max-height: 20px;
        padding: 0px 4px;
        gap: 4px;
        flex-shrink: 0;
        border-radius: var(--kyy_radius_tag_s, 4px);
        &-blue {
          color: var(--kyy_color_tag_text_kyyBlue, #21ACFA);
          background: var(--kyy_color_tag_bg_kyyBlue, #E4F5FE);
        }
        &-red {
          color: var(--kyy_color_tag_text_error, #D54941);
          background: var(--kyy_color_tag_bg_error, #F7D5DB);
        }
      }

      .btn {
        height: 28px;
        min-width: 72px;
        padding: 0 16px;
      }
    }
  }
}
</style>
