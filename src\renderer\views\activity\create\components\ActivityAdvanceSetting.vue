<template>
  <div>
    <div class="text-[#1A2139] text-16 leading-24 font-600">
      <span v-if="!hideSerialNumber">5.</span>
      {{ t('activity.activity.advanceSetting') }}
    </div>

    <t-form
      ref="formRef"
      class="activity-advance-setting-form mt-16 w-608"
      :data="activityFormData.advanced"
      :rules="rules"
      label-align="top"
      scroll-to-first-error="smooth"
      :disabled="disabled"
    >
      <!--报名管理-->
      <t-form-item class="!mb-16" name="openRegist">
        <div>
          <div class="flex items-center">
            <div class="w-2 h-14 rounded-1 bg-[#4D5EFF]" />
            <div class="ml-8 text-[#1A2139] font-600">报名管理</div>
            <t-tooltip content="参与人员范围为公开报名时，可开启报名管理" placement="top">
              <img class="flex-shrink-0 ml-4" src="@renderer/assets/activity/icon_help.svg" alt="">
            </t-tooltip>
            <!--非公开报名时禁用报名管理开关-->
            <t-switch
              v-model="activityFormData.advanced.openRegist"
              class="!ml-24"
              :disabled="disabled || activityFormData.basic.actorScope !== 'Publish'"
            />
          </div>

          <!--没有开启报名时，显示下方提示文字-->
          <template v-if="!activityFormData.advanced.openRegist">
            <!--根据活动是否公开显示对应的文案-->
            <div v-if="activityFormData.basic.actorScope === 'Publish'" class="mt-8 text-[#ACB3C0]">
              开启后可设置报名时间、报名人数、报名审核、报名表单
            </div>
            <div v-else class="text-[#ACB3C0]">当前活动范围为非公开，报名管理不可设置</div>
          </template>
        </div>
      </t-form-item>

      <!--开启报名时的表单项-->
      <template v-if="activityFormData.advanced.openRegist">
        <!--报名时间-->
        <t-form-item class="activity-register-time-form-item" label="报名时间" name="registerTime">
          <t-form-item class="!mr-0" name="registerTime.startTime" :required-mark="false">
            <t-date-picker
              v-model="registerStartTime"
              class="w-300"
              enable-time-picker
              clearable
              format="YYYY-MM-DD HH:mm"
              placeholder="开始时间"
              @change="activityFormData.advanced.registerTime.endTime = null"
            />
          </t-form-item>
          <t-form-item
            class="activity-register-time-end-time-form-item"
            name="registerTime.endTime"
            :required-mark="false"
          >
            <t-tooltip v-if="!registerStartTime" content="请先选择开始时间">
              <t-date-picker
                v-model="registerEndTime"
                class="w-300 !ml-8"
                enable-time-picker
                clearable
                format="YYYY-MM-DD HH:mm"
                disabled
                placeholder="结束时间"
                :disable-date="{
                  before: moment(activityFormData.advanced.registerTime.startTime)
                    .add(-1, 'day')
                    .format('YYYY-MM-DD HH:mm:ss'),
                }"
              />
            </t-tooltip>
            <t-date-picker
              v-else
              v-model="registerEndTime"
              class="w-300 !ml-8"
              enable-time-picker
              clearable
              format="YYYY-MM-DD HH:mm"
              placeholder="结束时间"
              :disable-date="{
                before: moment(activityFormData.advanced.registerTime.startTime)
                  .add(-1, 'day')
                  .format('YYYY-MM-DD HH:mm:ss'),
              }"
            />
          </t-form-item>
        </t-form-item>

        <!--报名人数-->
        <t-form-item label="报名人数" name="quota">
          <t-input-number
            v-model="activityFormData.advanced.quota"
            class="!w-full"
            theme="normal"
            :decimal-places="0"
            placeholder="请填写最大限制人数，不填写则不限制"
            :min="1"
            :max="9999"
            :allow-input-over-limit="true"
            @blur="onQuotaInputBlur"
          />
        </t-form-item>

        <!--报名审核-->
        <t-form-item name="registApprove" label-align="left">
          <template #label>
            <div class="flex items-center">
              <span class="text-[#828DA5]">报名审核</span>
              <t-tooltip content="若开启报名，用户报名后需要主办方审核">
                <img class="flex-shrink-0 ml-4" src="@renderer/assets/activity/icon_help.svg" alt="">
              </t-tooltip>
            </div>
          </template>

          <t-switch v-model="activityFormData.advanced.registApprove" />
        </t-form-item>

        <!--报名表单设置-->
        <t-form-item class="!mb-16" name="registerForms">
          <template #label>
            <div class="inline-flex items-center">
              <span class="text-[#828DA5]">报名表单设置</span>
              <!--              <t-tooltip content="勾选后，用户报名时需要填写表单">-->
              <!--                <img class="flex-shrink-0 ml-4" src="@renderer/assets/activity/icon_help.svg" alt="">-->
              <!--              </t-tooltip>-->

              <t-button
                v-if="!disabled && !registerFormDisabled"
                class="!ml-8 register-form-add-button"
                theme="default"
                variant="outline"
                @click="addCustomRegisterFormItem"
              >
                <iconpark-icon class="text-20 text-[#4D5EFF]" name="iconadd" />
                <span class="ml-4 text-[#4D5EFF]">自定义名称</span>
              </t-button>
            </div>
          </template>

          <t-table row-key="type" :columns="registerFormsTableColumns" :data="activityFormData.advanced.registerForms">
            <template #name="{ row, rowIndex }">
              <span v-if="row.type !== 'TypeCustom' || disabled || registerFormDisabled">{{ row.name }}</span>

              <t-form-item v-else :name="`registerForms[${rowIndex}].name`">
                <t-input
                  v-model="row.name"
                  class="register-form-custom-input"
                  placeholder="最多输入10个字"
                  :maxlength="10"
                  @change="formRef?.clearValidate(`registerForms[${rowIndex}].name`)"
                />
              </t-form-item>
            </template>

            <template #enable="{ row }">
              <t-checkbox
                v-model="row.enable"
                :disabled="row.disabled || disabled || registerFormDisabled"
                @change="(val) => (row.isRequired = val ? row.isRequired : false)"
              >
                启用
              </t-checkbox>
            </template>

            <template #isRequired="{ row }">
              <t-checkbox v-model="row.isRequired" :disabled="!row.enable || row.disabled || disabled || registerFormDisabled">必填</t-checkbox>
            </template>

            <template #operate="{ row, rowIndex }">
              <div class="flex justify-center items-center">
                <img
                  v-if="row.type === 'TypeCustom' && !disabled && !registerFormDisabled"
                  class="cursor-pointer"
                  src="@renderer/assets/activity/icon_clean.svg"
                  alt=""
                  @click="removeCustomRegisterFormItem(rowIndex)"
                >
              </div>
            </template>
          </t-table>
        </t-form-item>
      </template>

      <!--报名费设置-->
      <t-form-item v-if="!isPersonal" class="!mb-16" name="registFeeEnable">
        <div>
          <div class="flex items-center">
            <div class="w-2 h-14 rounded-1 bg-[#4D5EFF]" />
            <div class="ml-8 text-[#1A2139] font-600">报名费设置</div>
            <t-switch
              :value="activityFormData.advanced.registFeeEnable"
              class="!ml-24"
              @click="registFeeClick"
            />
          </div>

          <div v-if="!activityFormData.advanced.registFeeEnable" class="mt-8 text-[#ACB3C0]">开启后可设置需要收取的报名费金额</div>
        </div>
      </t-form-item>

      <!--报名费金额（开启报名费设置时显示）-->
      <t-form-item
        v-if="activityFormData.advanced.registFeeEnable"
        class="!mb-16"
        label="报名费金额"
        name="registFee.value"
      >
        <div class="flex items-center gap-8">
          <t-input-number
            v-model="activityFormData.advanced.registFee.value"
            theme="normal"
            class="!w-294"
            placeholder="请填写参与人报名时需缴纳费用"
            :decimal-places="2"
            :min="0"
            :max="9999999.99"
            @blur="onRegistFeeInputBlur"
          />
          <span>元</span>
        </div>
      </t-form-item>

      <div class="h-1 bg-[#ECEFF5]" />
      <div class="my-16 flex items-center">
        <div class="w-2 h-14 rounded-1 bg-[#4D5EFF]" />
        <div class="ml-8 text-[#1A2139] font-600">其他设置</div>
      </div>

      <!--发布渠道（非个人身份创建活动且有可显示的发布渠道时显示）-->
      <t-form-item v-if="!isPersonal && allPublishChannels.some((channel) => channel.visible)" name="publishChannels">
        <template #label>
          <div class="flex items-center">
            <span class="text-[#828DA5]">发布渠道</span>
            <t-tooltip content="勾选后可在对应的渠道下看到此活动">
              <img class="flex-shrink-0 ml-4" src="@renderer/assets/activity/icon_help.svg" alt="">
            </t-tooltip>
          </div>
        </template>

        <t-checkbox-group v-model="activityFormData.advanced.publishChannels" name="publishChannels">
          <div
            v-for="channel in allPublishChannels.filter((channel) => channel.visible)"
            :key="channel.value"
            class="flex items-center"
          >
            <t-checkbox :value="channel.value" :disabled="channel.disabled || disabled">
              <span>{{ channel.label }}</span>
            </t-checkbox>
            <t-tooltip v-if="channel.value === 'SQUARE'" content="参与范围公开时，可以发布至广场号">
              <img class="flex-shrink-0 ml-4" src="@renderer/assets/activity/icon_help.svg" alt="">
            </t-tooltip>
          </div>
        </t-checkbox-group>
      </t-form-item>

      <!--是否开启评论-->
      <t-form-item name="enableComment" label-align="left">
        <template #label>
          <div class="flex items-center">
            <span class="text-[#828DA5]">开启评论</span>
            <t-tooltip content="开启后已参与活动的人员可以进行评论">
              <img class="flex-shrink-0 ml-4" src="@renderer/assets/activity/icon_help.svg" alt="">
            </t-tooltip>
          </div>
        </template>

        <t-switch v-model="activityFormData.advanced.enableComment" :disabled="isCommentDisabled" @change="onEnableCommentChange" />
      </t-form-item>

      <!--现场图-->
      <t-form-item label="现场图" name="sceneDrawing">
        <div class="flex gap-16 img-box">
          <div
            class="w-78 h-78 border-1 border-solid border-[#D5DBE4] rounded-8 flex-col justify-center items-center gap-4 cursor-pointer"
            :class="{ '!border-none': activityFormData.advanced.sceneDrawing }"
            @click="sceneDrawingClick()"
          >
            <template v-if="!activityFormData.advanced.sceneDrawing">
              <img class="w-32 h-32" src="@renderer/assets/activity/upload.svg" alt="">
              <div class="text-[#516082] text-12 leading-20">{{ t('activity.activity.clickUpload') }}</div>
            </template>

            <img
              v-else
              class="w-full h-full object-cover rounded-8"
              :src="activityFormData.advanced.sceneDrawing"
              alt=""
            >
          </div>
          <iconpark-icon
            v-if="!disabled && activityFormData.advanced.sceneDrawing"
            name="iconclean"
            fill="rgba(26, 33, 57, 0.36)"
            class="del-icon text-20 text-[#fff]"
            @click="delSceneDrawing()"
          />
          <div class="text-[#ACB3C0] text-12 leading-20">
            <div class="mt-18">
              <div>{{ t('activity.activity.uploadTip7') }}</div>
              <div>1、图片大小10M内</div>
              <div>2、支持格式： jpg/jpeg/png/bmp/webp</div>
            </div>
          </div>
        </div>
      </t-form-item>

      <!--活动提醒（最多9条）-->
      <t-form-item label="活动提醒" name="reminders">
        <activity-reminder-selector v-model:reminders="activityFormData.advanced.reminders" :disabled="disabled" />
      </t-form-item>
    </t-form>

    <!--图片裁剪上传控件-->
    <UploadAvatar
      ref="uploadAssetRef"
      :size-limit="10"
      :show-size-limit="false"
      :format-restrict="true"
      :no-cropper="true"
      title="裁剪头像"
      @confirm="(url) => (activityFormData.advanced.sceneDrawing = url)"
    />
  </div>
</template>

<script setup lang="ts">
import { inject, computed, ref, watch, onActivated, onDeactivated, nextTick } from 'vue';
import { useI18n } from 'vue-i18n';
import moment from 'moment';
import LynkerSDK from '@renderer/_jssdk';
import { getWhoRole } from '@/api/activity';
import UploadAvatar from '@/components/common/UploadAvatar.vue';
import { cardIdType } from '@/views/identitycard/data';
import emitter from '@/utils/MittBus';
import ActivityReminderSelector from '@/views/activity/create/components/ActivityReminderSelector.vue';
import { checkMerchantStatus } from '@/api/activity/product';
import { useCheckAuth } from '@/views/activity/hooks/useCheckAuth';

const { ipcRenderer } = LynkerSDK;

const { t } = useI18n();

const props = defineProps({
  disabled: {
    type: Boolean,
    default: false,
  },
  registerFormDisabled: {
    type: Boolean,
    default: false,
  },
  // 隐藏序号
  hideSerialNumber: {
    type: Boolean,
    default: false,
  },
  hasEditPermission: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['onEnableCommentChange']);

const formRef = ref(null);

const uploadAssetRef = ref(null);

const isManage = inject('isManage');
const isAllowEdit = inject('isAllowEdit');
// 活动表单数据
const activityFormData = inject('activityFormData');
const selectedTeam = inject('selectedTeam');
const isPersonal = inject('isPersonal');

// 报名开始时间（特殊处理字段）
const registerStartTime = computed({
  get() {
    return activityFormData.advanced.registerTime.startTime
      ? moment.unix(activityFormData.advanced.registerTime.startTime).format('YYYY-MM-DD HH:mm')
      : null;
  },
  set(val) {
    activityFormData.advanced.registerTime.startTime = moment(val).unix();
  },
});

// 报名结束时间（特殊处理字段）
const registerEndTime = computed({
  get() {
    return activityFormData.advanced.registerTime.endTime
      ? moment.unix(activityFormData.advanced.registerTime.endTime).format('YYYY-MM-DD HH:mm')
      : null;
  },
  set(val) {
    activityFormData.advanced.registerTime.endTime = moment(val).unix();
  },
});

// 表单验证规则
const rules = computed(() => ({
  'registerTime.startTime': [
    {
      validator: () => {
        if (!registerStartTime.value && registerEndTime.value) {
          return Promise.resolve({
            result: false,
            message: '请选择报名开始时间',
            type: 'error',
          });
        }
        return Promise.resolve({ result: true });
      },
    },
  ],
  'registerTime.endTime': [
    {
      validator: () => {
        if (registerStartTime.value && !registerEndTime.value) {
          return Promise.resolve({ result: false, message: '请选择报名结束时间', type: 'error' });
        }
        if (registerEndTime.value && !moment(registerEndTime.value).isAfter(moment(registerStartTime.value))) {
          return Promise.resolve({ result: false, message: '报名结束时间不能早于或等于报名开始时间', type: 'error' });
        }
        if (
          registerEndTime.value
          && activityFormData.basic.duration.startTime
          && activityFormData.basic.duration.startTime < activityFormData.advanced.registerTime.endTime
        ) {
          return Promise.resolve({ result: false, message: '报名结束时间不能晚于活动开始时间', type: 'error' });
        }
        return Promise.resolve({ result: true });
      },
    },
  ],
  // 自定义报名表单项添加校验规则
  ...activityFormData.advanced.registerForms.reduce((acc, item, index) => {
    if (item.type === 'TypeCustom') {
      acc[`registerForms[${index}].name`] = [
        {
          validator: () => {
            if (!item.name) {
              return Promise.resolve({
                result: false,
                message: '请输入选项内容',
                type: 'error',
              });
            }
            return Promise.resolve({ result: true });
          },
          trigger: 'submit',
        },
      ];
    }
    return acc;
  }, {}),
  'registFee.value': [
    {
      required: true,
      message: '请填写不小于 0 且精确到小数点后两位的数',
    },
  ],
}));

// 是否禁用评论按钮
const isCommentDisabled = computed(() => {
  if (!isManage) {
    // 如果不是管理侧，不禁用
    return false;
  }
  if (!props.hasEditPermission) {
    // 管理侧没有权限，始终保持禁用
    return true;
  }
  if (isAllowEdit.value) {
    // 当活动处于可修改的状态时，根据是否启用修改模式判断是否禁用
    return props.disabled;
  }
  // 管理侧有权限，且活动处于不可修改的状态时，始终不禁用
  return false;
});

// 报名表单设置表格列
const registerFormsTableColumns = [
  {
    title: '表单项名称',
    colKey: 'name',
    width: 240,
  },
  {
    title: '是否启用',
    colKey: 'enable',
    width: 184,
  },
  {
    title: '是否必填',
    colKey: 'isRequired',
    width: 128,
  },
  {
    title: '',
    colKey: 'operate',
    width: 56,
  },
];

// 所有发布渠道，如果组织已开通的渠道中不包括该渠道，则禁用
const allPublishChannels = computed(() => [
  { label: '数智工场', value: 'WORKSHOP', visible: selectedTeamChannels.value.includes('WORKSHOP'), disabled: false },
  { label: '数字平台', value: 'PLATFORM', visible: selectedTeamChannels.value.includes('PLATFORM'), disabled: false },
  // 仅在公开活动时可选发布广场号
  {
    label: '广场号',
    value: 'SQUARE',
    visible: selectedTeamChannels.value.includes('SQUARE'),
    disabled: activityFormData.basic.actorScope !== 'Publish',
  },
]);

// 当前组织已开通的渠道
const selectedTeamChannels = ref([]);

// 当前组织是否已经绑定组织收款
const isSelectedTeamMerchantBind = ref(false);

// 获取当前选择的组织已开通的渠道
const getSelectedTeamChannels = async () => {
  const res = await getWhoRole(activityFormData.basic.teamId, selectedTeam.value.uuid);
  selectedTeamChannels.value = res.data.data.stages;
};

// 获取当前选择的组织是否开通组织收款
const getSelectedTeamMerchantStatus = async () => {
  const res = await checkMerchantStatus({
    teamId: selectedTeam.value.teamId,
  });
  isSelectedTeamMerchantBind.value = res.data.data.result;

  if (!isSelectedTeamMerchantBind.value) {
    // 如果没有开通组织收款，则清空报名费设置相关内容
    activityFormData.advanced.registFeeEnable = false;
    activityFormData.advanced.registFee.value = null;
  }
};

// 报名人数输入框失焦事件
const onQuotaInputBlur = () => {
  // 自动修正最大最小值
  if (activityFormData.advanced.quota <= 0) {
    activityFormData.advanced.quota = 1;
  } else if (activityFormData.advanced.quota > 9999) {
    activityFormData.advanced.quota = 9999;
  }
};

// 报名费输入框失焦事件
const onRegistFeeInputBlur = () => {
  // 自动修正最大最小值
  if (activityFormData.advanced.registFee.value <= 0) {
    activityFormData.advanced.registFee.value = null;
  } else if (activityFormData.advanced.registFee.value >= 10000000) {
    activityFormData.advanced.registFee.value = 9999999.99;
  }
};

// 添加自定义报名表单项
const addCustomRegisterFormItem = async () => {
  activityFormData.advanced.registerForms.push({
    type: 'TypeCustom',
    name: null,
    enable: true,
    isRequired: false,
    disabled: false,
  });

  await nextTick();

  // 聚焦到最后一个自定义报名表单项的输入框
  const customInputs = document.querySelectorAll('.register-form-custom-input');
  const lastInput = customInputs[customInputs.length - 1]?.querySelector('input');
  if (lastInput) {
    lastInput.focus();
  }
};

// 移除自定义报名表单项
const removeCustomRegisterFormItem = (index) => {
  activityFormData.advanced.registerForms.splice(index, 1);
};

const validate = (params) => formRef.value?.validate(params);

const sceneDrawingClick = () => {
  if (activityFormData.advanced.sceneDrawing) {
    preview();
    return;
  }
  if (!props.disabled) {
    uploadAssetRef.value.open();
  }
};

// 切换开启评论开关，抛出事件
const onEnableCommentChange = (value) => {
  emit('onEnableCommentChange', value);
};

const preview = () => {
  // 图片预览
  ipcRenderer.invoke(
    'preview-file',
    JSON.stringify({
      url: activityFormData.advanced.sceneDrawing,
      type: 'png',
    }),
  );
};
const delSceneDrawing = () => {
  activityFormData.advanced.sceneDrawing = '';
};

// 切换非个人活动归属时查询渠道和组织商户状态（这里用cardId进行监听，是为了兼容场景活动初始化时有teamId无cardId的问题）
watch(
  () => activityFormData.basic.cardId,
  () => {
    if (cardIdType(activityFormData.basic.cardId) !== 'personal') {
      getSelectedTeamChannels();
      getSelectedTeamMerchantStatus();
    }
  },
  {
    immediate: true,
  },
);

// 检查用户权限
const { handleRegistFeeClick } = useCheckAuth({
  teamId: computed(() => selectedTeam.value.teamId),
  onRegistFeeEnable: () => {
    activityFormData.advanced.registFeeEnable = !activityFormData.advanced.registFeeEnable;
  },
});

const registFeeClick = () => {
  if (props.disabled) {
    return;
  }
  handleRegistFeeClick();
};

onActivated(() => {
  emitter.on('validate-form-fields', validate);
});

onDeactivated(() => {
  emitter.off('validate-form-fields', validate);
});

defineExpose({
  validate,
});
</script>

<style lang="less" scoped>
:deep(.activity-advance-setting-form) {
  .t-form__label {
    line-height: 22px;
    min-height: 22px;
    padding: 0;

    &.t-form__label--top {
      margin-bottom: 8px;
    }
  }

  .t-form__controls {
    min-height: 22px;
    .t-form__controls-content {
      min-height: 22px;
    }
  }

  .t-form__controls-content {
    .t-input {
      padding-left: 12px;

      &.t-is-disabled {
        border-color: #eceff5 !important;
        background: #fff !important;
        .t-input__inner {
          color: #acb3c0 !important;
        }
      }

      .t-input__limit-number {
        color: #acb3c0;
        font-size: 12px;
      }
    }
  }

  .t-button--theme-default.t-is-disabled {
    border-color: #d5dbe4 !important;
    background: #fff !important;
    color: #acb3c0 !important;
  }

  .activity-register-time-form-item {
    .t-form__controls-content {
      align-items: flex-start;
    }
    .t-is-error .t-input__extra {
      position: relative !important;
      bottom: 0;
      left: 8px;
      color: var(--td-error-color) !important;
      margin-top: 0 !important;
    }
  }

  .t-form-item__registerTime.endTime {
    .t-input__extra {
      position: relative !important;
    }
  }
}

.img-box {
  position: relative;
}

.del-icon {
  display: inline;
  position: absolute;
  top: -1px;
  left: 56px;
  cursor: pointer;
}

.register-form-add-button{
  width: 108px;
  height: 28px;
  border-color: #4D5EFF !important;
  padding-left: 8px;
}

:deep(.t-table__th-isRequired){
  border-right-color: #e2e6f5;
}
</style>
