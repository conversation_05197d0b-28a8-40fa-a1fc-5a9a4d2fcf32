<template>
  <div class="notice-box" >
    <div class="item-head">
      <div class="notice-item-text">
        <img :src="module?.logo" />
        <span>{{ module?.name}}</span>
        <!-- <img src="@/assets/bench/icon_arrow_right.svg" /> -->
      </div>
      <!-- <div class="head-item-icon" @click="toSetUp"    v-show="checkIsAdminData?.isAdmin || checkIsAdminData?.super || checkIsAdminData?.superAdmin">
        <iconpark-icon class="icon-img" name="iconsetUp"></iconpark-icon>
        <span>{{ t("banch.set") }}</span>
      </div> -->
      <div v-show="richArrEbook.length > 3" class="more cursor" @click="goTabEbook">全部<iconpark-icon name="iconarrowright" class="icon"></iconpark-icon></div>
    </div>
    <div v-if="richArrEbook.length > 0" class="waterfall-box">
      <!-- <div class="top">
        <div class="title">

          {{ $t('member.squarek.a') }}
        </div>
        <div v-show="richArrEbook.length > 3" class="more cursor" @click="goTabNew({page: 'member_ebook'})">查看更多<iconpark-icon name="iconarrowright" class="icon"></iconpark-icon></div>
      </div> -->
      <div class="listsEbook">
        <div
          v-for="(rich, richIndex) in richArrEbook.slice(0,3)"
          :key="richIndex"
          class="listsEbook-item cursor"
          @click="onOpenEbook(rich)"
        >
          <div class="cover">
            <img :src="rich.cover" alt="">
          </div>
          <div class="name">
            {{ rich.name }}
          </div>
        </div>
      </div>
    </div>
    <!-- <CultureSettingDrawer ref="cultureSettingDrawerRef" :activationGroupItem="props.activationGroupItem" @onSuccess="onSuccess"/> -->

    <div class="waterfall-box" v-else>
      <Empty :tip="$t('ebook.nodata')" :name="'no-friend-list'">
                    </Empty>
    </div>
  </div>

  <!-- <CultureSettingDrawer ref="cultureSettingDrawerRef" :activationGroupItem="props.activationGroupItem" @onSuccess="onSuccess"/> -->
</template>
<script setup lang="ts">
import { ref, onMounted, computed, watch, nextTick, onUnmounted, toRaw } from "vue";
import Empty from '@renderer/components/common/Empty.vue';
import { useI18n } from "vue-i18n";
import { MessagePlugin } from "tdesign-vue-next";
import { getEbookShareList } from "@renderer/api/member/api/ebookApi";
import to from 'await-to-js';
import { getSrcThumbnail, getSrcLogo } from "@renderer/views/message/service/msgUtils";
import { getBaseUrl } from "@renderer/utils/apiRequest";
import { ClientSide } from "@renderer/types/enumer";
import { useRouter } from "vue-router";
import LynkerSDK from '@renderer/_jssdk';

const { ipcRenderer, shell } = LynkerSDK;
const { t } = useI18n();
const is100W = ref(false);
const router = useRouter();
const props = defineProps({
  DataType: {
    type: Number,
    default: 2,
  },
  activationGroupItem: {
    type: Object,
  },
  checkIsAdminData: {
    type: Object,
  },
  module: {
    type: Object,
    default: () =>  null,
  },

  allLayoutList: {
    type: Array,
  },
});



watch(
  () => props.activationGroupItem.teamId,
  (newValue) => {
    getList();
  },
);





const onCancelUpload = () => {}





// 预览图片
const preview = (i) => {
  // const temp = noticeListData.value?.map((item) => ({
  //   url: item.file_name,
  //   imgIndex: i,
  // }));
  // ipcRenderer.invoke("view-img", JSON.stringify(temp));
};

const isLoading = ref(false);
const pagination = {
  pageSize: 10,
  page: 1,
  total: 0,
};
const richArrEbook = ref([]);
const getList = async (isCover= false) => {
  // let chanelType = '';
  // 渠道类型（数字平台：digital_platform，数智工场：digital_work，广场号：square）
  // switch (props.module?.uuid) {
  //   case 'government':
  //     chanelType = '';
  //     break;
  //   case 'member':
  //     comp = EbookWall;
  //     break;
  //   case 'cbd':
  //     comp = EbookWall;
  //     break;
  //   default:
  //     comp = null;
  // }

  const params = {
    team_id: props.activationGroupItem.teamId,
    // name: keyword.value,
    pageSize: 5,
    channel_type: 'digital_work',
    digital_type: props.module.uuid,
    // page: pagination.page
  };
  // 缓存信息存储
  // const caches = store.getStorageDatas;
  // const cache = caches.find((v) => v.teamId === getMemberTeamID());
  isLoading.value = true;
  const [err, res] = await to(getEbookShareList(params, params.team_id));
  isLoading.value = false;
  // if (err) {
  //   console.log(err, 'rich-err');
  //   if (err?.message === 'Network Error' && cache) {
  //     isNetworkError.value = true;
  //     richArr.value = cache.memberRich?.items;
  //     pagination.total = cache.memberRich?.total;
  //   }
  //   return;
  // }

  let { data } = res;
  data = data.data;
  console.log(data);
  const richs = data.total > 0 ? data.list?.map((v) => {v.cover = getSrcThumbnail(v.cover);return v;}) : [];
  pagination.total = data.total;
  if (isCover) {
    richArrEbook.value = richs;
  } else {
    richArrEbook.value = richArrEbook.value.concat(richs);
  }

};


const onOpenEbook = (data: any) => {
  let url = `${getBaseUrl("h5")}/ebook/browse?id=${data.uuid}`;
  if (data.type === 2) {
    url = data?.file?.url || '';
  }
  openUrlByBrowser(url);
};
const openUrlByBrowser = (url: string) => {
  shell.openExternal(url);
};

const goTabEbook = () => {
  if(!props.activationGroupItem)  return;



  const querySearch =  {
      path: "/workBenchIndex/member_ebook",
      query: { platform: "digital_workbench", ...props.activationGroupItem },
      routeName: "bench_member_ebook",
      type: ClientSide.MEMBER,
  };
  switch (props.module.uuid) {
    case 'member':

      break;
    case 'government':
      querySearch.path = '/workBenchIndex/politics_ebook';
      querySearch.routeName = 'bench_politics_ebook';
      querySearch.type = ClientSide.POLITICS;
      break;
    case 'association':
      querySearch.path = '/workBenchIndex/association_ebook';
      querySearch.routeName = 'bench_association_ebook';
      querySearch.type = ClientSide.ASSOCIATION;
      break;

    case 'cbd':
      querySearch.path = '/workBenchIndex/cbd_ebook';
      querySearch.routeName = 'bench_cbd_ebook';
      querySearch.type = ClientSide.CBD;
      break;
  case 'uni':
      querySearch.path = '/workBenchIndex/uni_ebook';
      querySearch.routeName = 'bench_uni_ebook';
      querySearch.type = ClientSide.UNI;
      break;
    default:
      break;
  }
  const { path, type, query, routeName } = querySearch;
  if (path) {
    const ipcObj = {
      type,
      path,
      query: query || {},
      name: routeName,
      // path_uuid: item.uuid,
      title: '会刊',
    }

    // if (setActiveAccount) {
    //   setActiveAccount(team);
    // }
    if (ipcObj.query?.user_ids) {
      ipcObj.query.user_ids = JSON.stringify(ipcObj.query?.user_ids);
    }
    console.log(ipcObj, "ipcObjjjjj");
    ipcRenderer.invoke("set-work-bench-tab-item", ipcObj);
    router.push(ipcObj);
  }
};


onMounted(()=> {
  getList();
})


</script>
<style lang="less" scoped>
.swiper-img {
  width: 100%;
  height: 368px;
  border-radius: 8px;
  object-fit: fill;
}
.notice-item:first-child{
  padding-top: 0 !important;
}
.head-item-icon:hover {
  color: #707eff;
}
.head-item-icon {
  cursor: pointer;
  display: flex;
  align-items: center;
  color: #516082;
  text-align: center;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
  .icon-img {
    font-size: 20px;
    width: 20px;
    height: 20px;
    margin-right: 4px;
  }
}
.user-name {
  display: flex;
  align-items: center;
  justify-content: start;
  margin-top: 4px;
  color: var(--text-kyy_color_text_5, #acb3c0);
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  gap: 12px;
  line-height: 20px; /* 166.667% */
}
.notice-content {
  color: var(--text-kyy_color_text_3, #828da5);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  word-break: break-all;
}
.notice-item::after {
  content: "";
  height: 1px;
  width: 100%;
  background: #eceff5;
  position: absolute;
  bottom: -5px;
  left: 0;
}
.nodata {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--text-kyy_color_text_2, #516082);
  text-align: center;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;

  line-height: 26px; /* 152.941% */
  img {
    width: 200px;
    height: 200px;
  }
}
.notice-item:hover {
  background: var(--bg-kyy_color_bg_list_hover, #f3f6fa);
}
.item-head {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-radius: 8px;
  background: linear-gradient(104deg, #D0F1FF 9.81%, #E5F6FF 29.31%);
  height: 48px;
  padding: 8px;
}
.notice-item-text {
  color: var(--text-kyy_color_text_1, #1a2139);
  font-size: 16px;
  font-style: normal;
  // padding-left: 8px;
  font-weight: 600;
  line-height: 24px; /* 150% */
  display: flex;
  height: 40px;
  // width: 104px;
  gap: 8px;
  align-items: center;
  img {
    width: 20px;
    height: 20px;
  }
}
// .notice-item-text:hover{

//   border-radius: 8px;
// background: var(--bg-kyy_color_bg_list_hover, #F3F6FA);
// cursor: pointer;
// }
.head-item-text {
  color: #1a2139;
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: 24px; /* 150% */
  position: relative;
  margin-left: 8px;
  margin-bottom: 12px;
}
.notice-box {
  background-color: #fff;
  border-radius: 8px;

  height: fit-content;
  position: relative;
}


.waterfall {
  // padding-bottom: 8px;
  &-box {
    break-inside: avoid;
    // width: calc((100% - 16px)/2)
    background: #fff;
    // margin-bottom: 10px;
    border-radius: 8px;
    background: var(--bg-kyy_color_bg_light, #FFF);
    padding: 8px;
    .top {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .title {
        position: relative;
        padding: 0 11px;
        color: var(--text-kyy_color_text_1, #1A2139);
        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        line-height: 24px; /* 150% */
        &::before {
          position: absolute;
          left: 0;
          content: " ";
          border-radius: 8px;
          background: var(--brand-kyy_color_brand_default, #4D5EFF);
          width: 3px;
          height: 16px;
          top: 0;
          bottom: 0;
          margin: auto;
        }

      }

    }

  }
  .entry {
    width: inherit;
  }
}
.more:hover{
  border-radius: 12px;
background: var(--bg-kyy_color_bgBrand_hover, #EAECFF);
color: #707EFF !important;
.icon{
color: #707EFF !important;

}
}
.more {
  display: flex;
  color:#516082;
  align-items: center;
  height: 24px;
  padding: 0 4px 0 8px;
  /* color: var(--color-button_text_brand-kyy_color_button_text_brand_font_default, #4D5EFF); */
  text-align: center;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
  .icon {
    font-size: 20px;
    color: #516082;
  }
}
// 会刊
.listsEbook {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  width: 100%;
  &-item {
    padding: 12px;
    background: var(--bg-kyy_color_bg_deep, #F5F8FE);
    border-radius: 8px;
    // background: var(--bg-kyy_color_bg_deep, #F5F8FE);
    // width: 182px;
    width: calc((100% - 24px) / 3);
    height: 262px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    transition: all 0.15s linear;

    .cover{
      display: flex;
      justify-content: center;
      padding: 0 5px;
      img{
        width: 156px;
        height: 208px;
        object-fit: cover;

      }
    }
    .name{
      padding: 0 5px;
      overflow: hidden;
      color: var(--text-kyy_color_text_1, #1A2139);
      text-overflow: ellipsis;
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: 22px; /* 157.143% */
      display: -webkit-box;
      width: 156px;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;
    }


    &:hover {
      border-radius: 8px;
      background: var(--bg-kyy_color_bg_light, #FFF);
      /* kyy_shadow_m */
      box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.12);
      transition: all 0.15s linear;
    }
    .left {
        width: 72px;
        height: 72px;
        position: relative;
        .lo {
            width: 72px;
            height: 72px;
            border-radius: 3.6px;
        }
        .ri {
            height: 20px;
            position: absolute;
            top: 0;
            left: 0;

        }
    }
    .right {
        display: flex;
        flex-direction: column;
        justify-content: center;
        gap: 8px;
        .top {
            .name {
              color: var(--text-kyy_color_text_1, #1A2139);
              /* kyy_fontSize_2/bold */
              font-family: "PingFang SC";
              font-size: 14px;
              font-style: normal;
              font-weight: 600;
              line-height: 22px; /* 157.143% */
                .tip {
                    color: var(--kyy_color_tag_text_brand, #4D5EFF);
                    text-align: center;
                    font-size: 12px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 20px; /* 166.667% */
                    padding: 0 4px;
                    border-radius: var(--kyy_radius_tag_s, 4px);
                    background: var(--kyy_color_tag_bg_brand, #EAECFF);
                }
                .yellow {
                    color: var(--kyy_color_tag_text_warning, #FC7C14);
                    background: var(--kyy_color_tag_bg_warning, #FFE5D1);
                }
                .blue{
                  color: #4D5EFF;
                  background: #EAECFF;
                  margin-right: 4px;
                }
            }
        }
        .bottom {
            display: flex;
            align-items: center;

            .company {
              flex: 1 1 auto;
              display: flex;
              align-items: center;
              gap: 4px;
              height: 20px;

              .av {
                flex: none;
              }
              .text {
                flex: 1;
                max-width: 140px;
                color: var(--text-kyy_color_text_3, #828DA5);
                /* kyy_fontSize_1/regular */
                font-family: "PingFang SC";
                font-size: 12px;
                font-style: normal;
                font-weight: 400;
                line-height: 20px; /* 166.667% */
              }
            }
            .area {
              flex: none;
            //  width: 72px;
              display: flex;
              align-items: center;
              .icon {
                color: #21ACFA;
                font-size:20px;
              }
              .text {
                width: 60px;
              }
            }
        }

    }
  }
}
</style>
