import { i18nt } from '@/i18n';

/**
 * 公告场景类型枚举
 */
export enum SceneType {
  /**
   * 平台成员新建待审核广告
   */
  NOTICE_APPLY = 22001,
  /**
   * 平台成员取消广告
   */
  NOTICE_PASS = 22002,
  /**
   * 广告审核超时提醒
   */
  NOTICE_REJECT = 22003,
}

export const SceneTypeArr = Object.values(SceneType).filter((value) => typeof value === 'number');

// export const typeText = ["", "通知", "通告", "通报", "公告", "公示"];
export const typeText = ['', '通知', '通告', i18nt('banch.tb'), '公告', '告示'];

/**  1商协 2政企 3CBD */
export const getTitle = (platform_type) => {
  let title = '';
  switch (platform_type) {
    case 1:
      title = i18nt('im.public.biz');
      break;
    case 2:
      title = i18nt('im.public.government');
      break;
    case 3:
      title = i18nt('member.digital.c');
      break;
    case 4:
      title = i18nt('niche.szsq');
      break;
    case 5:
      title = i18nt('member.digital.e1');
      break;
    default:
      break;
  }
  return title;
};
export const getBtnTxt = (status) => {
  const titleMap = {
    7: i18nt('ebook.vrefuse'), // 已拒绝
    1: i18nt('ebook.vtype3'), // 已通过
    2: i18nt('ebook.vtype3'), // 已通过
    8: i18nt('ebook.vtype6'), // 已失效
    5: i18nt('approval.approval_data.repeal'), // 已撤销
  };
  return titleMap[status] || null;
};
