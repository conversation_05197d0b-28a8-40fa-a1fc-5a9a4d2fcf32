
export const enum PlatType {
  /** 相册 */
  ALBUM_NODE = 'ALBUM_NODE',
  /** 党建 */
  PARTY_BUILDING = 'PARTY_BUILDING',
  /** 组织介绍 */
  TEAM_INTRO = 'TEAM_INTRO',
  /** 组织荣誉榜 */
  TEAM_HONOR_ROLL = 'TEAM_HONOR_ROLL',
  /** 组织历程 */
  TEAM_HISTORY = 'TEAM_HISTORY',
  /** 平台风采 */
  FENGCAI = 'FENGCAI'
}
export const enum TheTypeOfAssistant {
  /** 小秘书 */
  APP_SECRETARY = 'APP_SECRETARY',
}

export const isHasSelf = (type: PlatType | PlatType[]): boolean => {
  const types = Array.isArray(type) ? type : [type];
  const enumValues = [
    PlatType.ALBUM_NODE,
    PlatType.PARTY_BUILDING,
    PlatType.TEAM_INTRO,
    PlatType.TEAM_HONOR_ROLL,
    PlatType.TEAM_HISTORY,
    PlatType.FENGCAI,
  ];
  return types.some((v) => enumValues.includes(v));
};

export const enum IMRefreshType {
  /** 广告申请同步 */
  IMRefreshAd = 'IM-refresh-ad',
  /** 数字平台申请同步 */
  IMRefreshDigital = 'IM-refresh-digital',
  /** 访客申请同步 */
  IMRefreshVisitor = 'IM-refresh-visitor',
  /** 平台动态申请同步 */
  IMRefreshDynamic = 'IM-refresh-dynamic',
  /** 代表人审核联系人申请同步 */
  IMRefreshAgent = 'IM-refresh-agent',
}

export const IMRefreshMap = {
  5067: IMRefreshType.IMRefreshDigital,
  14037: IMRefreshType.IMRefreshDigital,
  19037: IMRefreshType.IMRefreshDigital,
  21037: IMRefreshType.IMRefreshDigital,
  16037: IMRefreshType.IMRefreshDigital,

  5068: IMRefreshType.IMRefreshVisitor,
  19038: IMRefreshType.IMRefreshVisitor,
  51038: IMRefreshType.IMRefreshVisitor,
  16038: IMRefreshType.IMRefreshVisitor,
  14038: IMRefreshType.IMRefreshVisitor,

  22007: IMRefreshType.IMRefreshAd,

  5069: IMRefreshType.IMRefreshDynamic,

  5084: IMRefreshType.IMRefreshAgent,
  14044: IMRefreshType.IMRefreshAgent,
  16044: IMRefreshType.IMRefreshAgent,
  19044: IMRefreshType.IMRefreshAgent,
  51044: IMRefreshType.IMRefreshAgent,
};
  // 20042 同步签到列表
  // 20044 同步文件 未使用
  // 20045 同步参与人数据

export const enum ActivityRefreshType {
  /** 同步参与人数据 */
  ActivityRefreshSyncActors = 'activity-refresh-syncActors',
  /** 同步签到列表 */
  ActivityRefreshSyncChecklist = 'activity-refresh-syncChecklist',
  /** 同步文件 */
  ActivityRefreshSyncFiles = 'activity-refresh-syncFiles',
}

export const ActivityRefreshMap = {
  20045: ActivityRefreshType.ActivityRefreshSyncChecklist,
  20046: ActivityRefreshType.ActivityRefreshSyncActors,
};
