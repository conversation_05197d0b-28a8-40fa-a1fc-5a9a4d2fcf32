<template>
  <t-dialog
    v-model:visible="visible"
    :z-index="2501"
    attach="body"
    :close-on-overlay-click="false"
    class="dialog"
    :close-btn="false"
    :header="'添加入会流程'"
    width="952px"
  >
    <!-- <template #header>
      <div class="header">
        <span class="title">添加入会流程 </span>
      </div>

    </template> -->
    <template #body>
      <div class="toBody mt-24px">
        <img v-if="proxy.$i18n.locale === 'zh-cn'" src="@renderer/assets/member/icon/add_in_member.png">
        <img v-else src="@renderer/assets/member/icon/add_in_member_flow.png">
      </div>
    </template>

    <template #closeBtn>

      <iconpark-icon name="iconerror" style="font-size: 24px"></iconpark-icon>
    </template>
    <template #footer>
      <div class="operates">


        <t-button
          theme="primary"
          class="operates-item"
          @click="onClose"
        >知道了</t-button>
      </div>
    </template>
  </t-dialog>


</template>

  <script lang="ts" setup>

  /**
   * @description 选择组织
   * <AUTHOR>
   */
  import { debounce } from "lodash";
  import { Ref, reactive, ref, getCurrentInstance } from "vue";
  import { MessagePlugin } from "tdesign-vue-next";
  const { proxy } = getCurrentInstance() as any;


  const visible = ref(false);

  const onOpen = () => {

    visible.value = true;
  };
  const onClose = () => {
    visible.value = false;
  };

  defineExpose({
    onOpen,
    onClose
  });
  </script>

  <style lang="less" scoped>
  @import "@renderer/views/engineer/less/common.less";


  .header {

  }

  .toBody {
     img {
       height: 178px;
     }
  }


  :deep(.t-form__item) {
    margin-bottom: 10px;
  }

  .t-dialog__ctx .t-dialog__position {
    padding: 0;
  }


  </style>
