import { RouteRecordName } from "vue-router";

export type RouteItem = {
  // 路由名
  name: RouteRecordName;
  // 路径
  path: string;
  // 图标
  icon: string;
  // 路由标题
  title: string;
  // 路由完整路径
  fullPath: string;
  // 是否固定在顶部 tab 栏，无法移除（首页）
  affix?: boolean;
  // 是否在菜单中不显示
  hidden?: boolean;
  // 角色，personal：个人, office: 企业
  role?: string;
  // 排序，小的排前面
  sort?: number;

  query?: Record<string, string>,
}
