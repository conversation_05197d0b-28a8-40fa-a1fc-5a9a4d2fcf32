<template>
	<!-- 合伙人结算institutionalSettlementDetail -->
	<div class="view-file-box">
		<div class="header">
			<div
				style="
					display: flex;
					align-items: center;
					font-size: 16px;
					font-weight: 700;
					width: 100%;
					margin-bottom: 20px;
					justify-content: start;
				"
			>
				<img
					style="width: 24px; margin-right: 8px; margin-left: -8px; cursor: pointer"
					src="@/assets/img/<EMAIL>"
					@click="goRouter"
				/>
				<div>应用管理详情</div>
			</div>
		</div>
		<div class="bodycs">
			<div style="margin-bottom: 50px">
				<div class="lin head-item-lab">应用信息</div>
				<div class="content-box">
					<div class="form-box" style="gap: 12px">
						<div class="form-flex" style="width: 45%">
							<div class="laber-item">应用ID:</div>
							<div class="value-item">
								{{ detailData?.uuid || '--' }}
							</div>
						</div>
						<div class="form-flex" style="width: 45%">
							<div class="laber-item">发布组织:</div>
							<div class="value-item" style="display: flex; align-items: center">
								<img
									v-if="detailData?.team_logo"
									:src="detailData?.team_logo"
									alt=""
									style="width: 50px; height: 50px; margin-right: 8px"
								/>
								<img v-else src="../../assets/<EMAIL>" alt="" />
								{{ detailData?.team_name || '--' }}
							</div>
						</div>
						<div class="form-flex" style="width: 45%">
							<div class="laber-item">创建时间:</div>
							<div class="value-item">
								{{ detailData?.created_at || '--' }}
							</div>
						</div>
						<div class="form-flex" style="width: 45%">
							<div class="laber-item">展示渠道:</div>
							<div v-if="detailData?.channels.length !== 0" class="value-item">
								<span v-for="(item, key) in detailData?.channels" :key="key">
									{{ type_val(item.channel_type) }}
									<span v-if="key !== detailData?.channels.length - 1">,</span>
								</span>
							</div>
							<div v-else>--</div>
						</div>
						<div class="form-flex" style="width: 45%">
							<div class="laber-item">应用图片:</div>
							<div class="value-item">
								<img
									v-if="detailData?.picture_linking"
									:src="detailData?.picture_linking"
									alt=""
									style="width: 50px; height: 50px"
								/>
								<img v-else src="../../assets/yingyong.png" alt="" />
							</div>
						</div>
						<div class="form-flex" style="width: 45%">
							<div class="laber-item">应用名称:</div>
							<div class="value-item">
								{{ detailData?.name || '--' }}
							</div>
						</div>

						<div class="form-flex" style="width: 45%">
							<div class="laber-item">保证金:</div>
							<div class="value-item">
								{{ margin_status_val(detailData?.bond_status) }}
							</div>
						</div>
						<div class="form-flex" style="width: 45%">
							<div class="laber-item">状态:</div>
							<div class="value-item">
								{{ app_status_val(detailData?.deleted_status) }}
							</div>
						</div>
						<div
							v-if="detailData?.bond_status === 2 || detailData?.bond_status === 3"
							class="form-flex"
							style="width: 45%"
						>
							<div class="laber-item">保证金编号:</div>
							<div class="value-item">
								{{ detailData?.bond_sn || '--' }}
							</div>
						</div>

						<div class="form-flex" style="width: 45%">
							<div class="laber-item">应用类型:</div>
							<div class="value-item">
								{{ status_val(detailData?.type) }}
							</div>
						</div>
						<div class="form-flex" style="width: 45%">
							<div v-if="detailData?.type === 'app'" style="display: flex">
								<div class="laber-item" style="display: inline-block">分享链接:</div>
								<div
									style="
										color: #2176ff;
										width: 500px;
										overflow-wrap: break-word;
										display: inline-block;
										cursor: pointer;
									"
									@click="openWindow(detailData.share_link)"
								>
									{{ detailData?.share_link }}
								</div>
							</div>
							<div v-else-if="detailData?.type === 'mini_program'">
								<div style="display: flex">
									<div class="laber-item">小程序原始ID:</div>
									<div class="value-item">
										{{ detailData?.mini_program_original_id || '--' }}
									</div>
								</div>
								<div style="display: flex">
									<div class="laber-item">小程序路径:</div>
									<div class="value-item">
										{{ detailData?.mini_program_path || '--' }}
									</div>
								</div>
							</div>
							<div v-else-if="detailData?.type === 'h5'">
								<div style="display: flex">
									<div class="laber-item" style="display: inline-block">网页H5链接:</div>
									<div
										style="
											color: #2176ff;
											width: 500px;
											overflow-wrap: break-word;
											display: inline-block;
											cursor: pointer;
										"
										@click="openWindow(detailData?.h5_link)"
									>
										{{ detailData?.h5_link }}
									</div>
								</div>
								<div style="display: flex">
									<div class="laber-item" style="display: inline-block">桌面端链接:</div>
									<div
										style="
											color: #2176ff;
											width: 500px;
											overflow-wrap: break-word;
											display: inline-block;
											cursor: pointer;
										"
										@click="openWindow(detailData?.desktop_link)"
									>
										{{ detailData?.desktop_link }}
									</div>
								</div>
							</div>
							<div v-else-if="detailData?.type === 'wechat_official'" style="display: flex">
								<div class="laber-item" style="display: inline-block">文章链接:</div>
								<div
									style="
										color: #2176ff;
										width: 500px;
										overflow-wrap: break-word;
										display: inline-block;
										cursor: pointer;
									"
									@click="openWindow(detailData?.article_link)"
								>
									{{ detailData?.article_link }}
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { externalAppDetail } from '@/api/advertisement/index';

const router = useRouter();
const route = useRoute();
const detailData = ref(null);
const idVal = ref(null);

const status_val = (status) => {
	switch (status) {
		case 'app':
			return 'App跳转';
		case 'h5':
			return '网页H5跳转';
		case 'mini_program':
			return '微信小程序';
		case 'wechat_official':
			return '微信公众号';
		default:
			return '--';
	}
};

const type_val = (status) => {
	switch (status) {
		case 'workshop':
			return '数智工场';
		case 'square':
			return '广场号';
		case 'digital':
			return '数字平台';
		default:
			return '--';
	}
};

// 保证金状态转换
const margin_status_val = (status) => {
	switch (status) {
		case 0:
			return '无需支付';
		case 1:
			return '待支付';
		case 2:
			return '已支付';
		case 3:
			return '已退款';
		default:
			return '--';
	}
};

// 应用状态转换
const app_status_val = (status) => {
	switch (status) {
		case 1:
			return '正常';
		case 2:
			return '已删除';
		default:
			return '--';
	}
};

const openWindow = (val) => {
	window.open(val);
};

onMounted(() => {
	idVal.value = route.query.id;
	console.log(idVal.value, 'idVal');
	getList(idVal.value);
});

const getList = (val) => {
	externalAppDetail(val).then((res) => {
		if (res.data.code === 0) {
			detailData.value = res.data.data;
		}
		console.log(res);
	});
};

const goRouter = () => {
	router.go(-1);
};
</script>
<style lang="less" scoped>
.head-item-lab {
	height: 22px;
	font-size: 14px;
	font-family: Microsoft YaHei, Microsoft YaHei-Bold;
	font-weight: 700;
	color: #13161b;
	display: flex;
	padding-left: 8px;
	margin-bottom: 12px;
}

.foot-lab {
	width: 70px;
	height: 22px;
	font-size: 14px;
	font-family: Microsoft YaHei, Microsoft YaHei-Regular;
	font-weight: 400;
	color: #13161b;
	line-height: 22px;
	text-align: right;
}

.foot-val {
	min-width: 120px;
	height: 22px;
	font-size: 14px;
	font-family: Microsoft YaHei, Microsoft YaHei-Regular;
	font-weight: 400;
	text-align: right;
	color: #13161b;
	line-height: 22px;
}

.foot-btn-box {
	margin-top: 36px;
	text-align: end;
}

.value-item {
	font-size: 14px;
	font-weight: 400;
	color: #13161b !important;
	flex: 1;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.form-box {
	display: flex;
	flex-wrap: wrap;

	.form-flex {
		display: flex;
		align-items: center;
	}

	.laber-item {
		width: 90px;
		font-size: 14px;
		font-family: Microsoft YaHei, Microsoft YaHei-Regular;
		font-weight: 400;
		text-align: left;
		color: #717376;
	}
}

.foot-box {
	width: 100%;
	background: #ffffff;

	border-top: none;
	border-radius: 0px 0px 4px 4px;
	text-align: center;
	padding: 12px;
	line-height: 64px;
}

:deep(.t-drawer__footer) {
	// display: none;
}

.table-thing-value {
	font-size: 14px;
	font-family: Microsoft YaHei, Microsoft YaHei-Regular;
	font-weight: 400;
	color: #13161b;
}

.my-order-box {
	display: flex;
	flex-wrap: nowrap;
	flex-direction: column;
}

.flex-a-js-w140 {
	display: flex;
	align-items: center;
	justify-content: space-between;
	width: 140px;
}

.w70text-r {
	width: 75px;
	text-align: right;
}

.table-foot-box {
	font-size: 14px;
	font-family: Microsoft YaHei, Microsoft YaHei-Regular;
	font-weight: 400;
	text-align: left;
	color: #717376;
	line-height: 44px;

	.foot-item {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}
}

.flex-a-end {
	display: flex;
	align-items: center;
	justify-content: end;
}

.table-thing-laber {
	font-size: 14px;
	font-family: Microsoft YaHei, Microsoft YaHei-Regular;
	font-weight: 400;
	color: #717376;
}

.table-thing {
	display: flex;

	img {
		width: 48px;
		height: 48px;
		margin-right: 12px;
	}
}

.content-box {
	// height: 122px;
	border: 1px solid #e3e6eb;
	padding: 16px;
	margin-bottom: 24px;
}

.lin {
	position: relative;
}

.btn {
	height: 22px;
	font-size: 14px;
	font-family: Microsoft YaHei, Microsoft YaHei-Regular;
	font-weight: 400;
	text-align: left;
	cursor: pointer;
	color: #2069e3 !important;
	line-height: 22px;
}

.lable-box {
	margin-bottom: 4px;
	margin-top: 4px;
	color: var(--text-kyy_color_text_3, #828da5);
	font-size: 14px;
	font-style: normal;
	font-weight: 400;
	line-height: 22px;
	/* 157.143% */
}

.lin::after {
	content: '';
	width: 2px;
	height: 14px;
	background: #2069e3;
	position: absolute;
	top: 4px;
	border-radius: 2px;
	left: 0;
}

.head-search-box {
	padding-top: 72px;
	height: 185px;
	display: flex;
	flex-wrap: wrap;
	padding-bottom: 24px;
	padding-left: 24px;
}

.form-items {
	display: flex;
	align-items: center;
	padding-right: 32px;

	.labels {
		height: 22px;
		padding-right: 8px;
		font-size: 14px;
		font-family: Microsoft YaHei, Microsoft YaHei-Regular;
		font-weight: 400;
		text-align: left;
		color: #13161b;
		line-height: 22px;
	}
}

.btn-box {
	font-size: 14px;
	font-family: Microsoft YaHei, Microsoft YaHei-Regular;
	font-weight: 400;
	color: #2069e3;
	cursor: pointer;
	line-height: 22px;
}

.transaction-status {
	width: 58px;
	height: 24px;
	border-radius: 4px;
	font-size: 14px;
	font-family: Microsoft YaHei, Microsoft YaHei-Regular;
	font-weight: 400;
	text-align: center;
	line-height: 24px;
}

.flex-a {
	display: flex;
	align-items: center;
}

.view-file-box {
	background: #fff;
	padding: 20px 24px;
}

.upditem:hover {
	background: #e2e7fd;

	img {
		margin-left: 12px;
		display: block;
	}
}

.upditem {
	height: 30px;
	margin-top: 0 !important;
	line-height: 30px;
	cursor: pointer;
	width: 400px;
	display: flex;
	border-radius: 4px;
	justify-content: space-between;
	align-items: center;
}

.img-wrap {
	display: flex;
	align-items: center;
	justify-content: center;
	flex-shrink: 0;
	width: 60px;
	height: 60px;
	margin-right: 8px;
	border: 1px solid #e3e6eb;
	border-radius: 4px;
	position: relative;

	&:hover .btn-close {
		opacity: 1;
	}

	.img {
		width: 100%;
		height: 100%;
		border-radius: 4px;
	}

	.btn-close {
		opacity: 0;
		position: absolute;
		top: -8px;
		right: -8px;
		font-size: 16px;
		cursor: pointer;
		color: #97989a;
		z-index: 1;
	}

	.play-icon {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		color: #fff;
		z-index: 1;
	}
}
:deep(.form-flex .t-upload__trigger) {
	display: none;
}
:deep(.form-flex .t-upload__single-file) {
	display: flex;
	flex-wrap: wrap;
}
:deep(.form-flex .file-item-list) {
	width: 30%;
}
</style>
