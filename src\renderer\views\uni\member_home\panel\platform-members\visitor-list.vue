<template>
  <div class="container">
    <div class="back-box">
      <div @click="back" class="backt">
        <iconpark-icon name="iconarrowlift" class="iconarrowleft"></iconpark-icon>
        {{ t('ebook.visback') }}
      </div>
      <div class="dr"></div>
      <div class="numtitle"> {{ t('ebook.vist') }}（{{ appCount }}）</div>
    </div>
    <div class="su-header">
      <div class="more-search">
        <div class="in-box">
          <t-select v-model="formData.status" :options="merberType" :placeholder="t('ebook.visp')" style="width: 240px"
            @change="typeChange"><template #suffixIcon>
              <img src="@/assets/svg/icon_arrow_down.svg" />
            </template>
          </t-select>
        </div>
      </div>

    </div>
    <div class="body">
      <div class="body-content">
        <div class="table" v-if="loaded">
          <t-table row-key="id" :columns="memberColumns" :pagination="pagination.total > 10 ? pagination : null"
            :max-height="pagination.total > 10 ? 430 : 500" :data="memberData">
            <template #name="{ row }">
              <div class="main-box">
                <kyy-avatar round-radius avatar-size="32px" :image-url="row.avatar" :user-name="row.name"
                  style="margin-right: 12px" />
                <span>{{ row.name }}</span>
              </div>
            </template>

            <template #status="{ row }">
              <div :class="showClassStatus(row.status)">
                {{ showTextStatus(row.status) }}
              </div>
            </template>
            <template #type="{ row }">
              <div :class="`type-tag${row.type}`">{{ showTextType(row.type) }}</div>
            </template>
            <template #operatorer="{ row }">
              <div>{{ row.reference || '--' }}</div>
            </template>
            <template #phone="{ row }">
              +{{ row.tel_code }} {{ row.telephone }}
            </template>

            <template #time="{ row }">
              {{ row.apply_time }}
            </template>
            <template #operate="{ row }">
              <template v-if="row.status === 1">
                <span class="operates">
                  <t-link theme="primary" hover="color" class="operates-item" @click="passRun(row)">
                    {{ t('ebook.vispass') }}
                  </t-link>
                  <t-link theme="primary" hover="color" class="operates-item" @click="refuseRun(row)">
                    {{ t('ebook.visrefu') }}
                  </t-link>
                </span>
              </template>
              <template v-if="row.status === 2 || row.status === 99">
                <div style="display: flex;align-items: center;gap: 4px;">
                  <t-tooltip :content="row.operator">
                    <div style="max-width: 130px;overflow: hidden;text-overflow: ellipsis;">
                      {{ row.operator }}
                    </div>
                  </t-tooltip>
                  <div class="pass">
                    {{ t('ebook.viyt') }}
                  </div>
                </div>
              </template>
              <template v-if="row.status === 3">
                <div style="display: flex;align-items: center;gap: 4px;">
                  <t-tooltip :content="row.operator">
                    <div style="max-width: 130px;overflow: hidden;text-overflow: ellipsis;">
                      {{ row.operator }}
                    </div>
                  </t-tooltip>
                  <div class="refuse">
                    {{ t('ebook.vrefuse') }}
                  </div>
                </div>
              </template>
              <template v-if="row.status === 5">
                <div class="lose">
                  {{ t('ebook.sx') }}
                </div>
              </template>

            </template>

            <template #empty>
              <div class="empty">
                <Empty :tip="formData.status ? t('ebook.nos') : t('ebook.nod')"
                  :name="formData.status ? 'no-result' : 'no-data-new'" />
              </div>
            </template>
          </t-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
// import noData from "@renderer/components/approvalSatellite/approvalData/noData.vue";
import { ref, reactive, watch, computed, onActivated, onMounted } from "vue";
import { useI18n } from "vue-i18n";
import {
  getMemberOrderDetailAxios,
  applyVisitList,
  applyAgree,
  applyReject,
  getStatistics,
} from "@renderer/api/uni/api/businessApi";
import { getResponseResult, priceDivisorShow } from "@renderer/utils/myUtils";
import { MessagePlugin, FormRule } from "tdesign-vue-next";
import { useMemberStore } from "@renderer/views/member/store/member";
import Empty from "@renderer/components/common/Empty.vue";
import { platform } from "@renderer/views/digital-platform/utils/constant";
import { useDigitalPlatformStore } from "@renderer/views/digital-platform/store/digital-platform-store";
import { useRoute } from "vue-router";
import { getMemberTeamID } from "@renderer/views/member/utils/auth";
import kyyAvatar from "@renderer/components/kyy-avatar/index.vue";
import { message } from "@renderer/api/login";


const digitalPlatformStore = useDigitalPlatformStore();
const route = useRoute();
const emits = defineEmits(["change-type", "getRedNum"]);
const back = () => {
  emits('change-type', 1);
}

const store = useMemberStore();
const { t } = useI18n();
const appCount = ref(0);

const statusOptions = [
  { label: t('ebook.vzc'), value: 1 },
  { label: t('ebook.vjr'), value: 2 },
];

const optionsFeeType = [

];
const merberType = [
  { label: t('ebook.vtype1'), value: 0 },
  { label: t('ebook.vtype2'), value: 1 },
  { label: t('ebook.vtype3'), value: 2 },
  { label: t('ebook.vtype4'), value: 3 },
  { label: t('ebook.vtype5'), value: 99 },
  { label: t('ebook.vtype6'), value: 5 },
];

const formData = reactive({
  status: 0,
});
const feeModalRef = ref(null);
const lookFeeModalRef = ref(null);
const form = ref(null);
const isMoreSearch = ref(false);
const memberColumns = ref([
  { colKey: "name", title: t('ebook.vname'), width: "200px" },
  { colKey: "phone", title: t('ebook.vphone'), width: "192px", ellipsis: false },
  { colKey: "operatorer", title: t('ebook.voperatorer'), width: "160px", ellipsis: true },
  { colKey: "time", title: t('ebook.vtime'), width: "192px", ellipsis: true },
  { colKey: "operate", title: t('ebook.voperate'), width: "200px", ellipsis: true },
]);
const memberData = ref([]);
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
  onChange: (pageInfo) => {
    console.log("pagination.onChange", pageInfo);
    pagination.current = pageInfo.current;
    pagination.pageSize = pageInfo.pageSize;
    onSearch(1);
  },
});


const props = defineProps({
  platform: {
    type: String,
    default: '',
  },
})
// 平台类型 目前只有digital-platform
const platformCpt = computed(() => {
  return props.platform || route.query?.platform
})

const activeAccount = computed(() => {
  if (platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount
  } else if (platformCpt.value === platform.digitalWorkbench) {
    return route.query;
  } else {
    return store.activeAccount
  }
})


const currentTeamId = computed(() => {
  if (platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount?.teamId
  } else if (platformCpt.value === platform.digitalWorkbench) {
    return route.query?.teamId || 0;
  } else {
    return getMemberTeamID()
  }
})



const showClassStatus = (val) => {
  // 申请状态，1：待审核，2：已入会，3：已驳回
  let result = {};
  if (val === 1) {
    result = { wait: true };
  } else if (val === 2) {
    result = { success: true };
  } else if (val === 3) {
    result = { reject: true };
  }
  return result;
};
const showTextStatus = (val) => {
  const option = statusOptions.find((v: any) => v.value === val);
  if (option) {
    return option.label;
  }
  return "--";
};
// 会费类型
const showTextType = (val) => {
  const option = optionsFeeType.find((v: any) => v.value === val);
  if (option) {
    return option.label;
  }
  return "--";
};


const onSearch = (nocurrent?) => {
  if (nocurrent !== 1) {
    pagination.current = 1;
  }
  const params = {
    ...formData,
  };
  getMemberList(params);
};



// 获取入会申请详情
const onGetMemberFeeDetailAxios = (res) => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await getMemberOrderDetailAxios(res.id, {}, currentTeamId.value);
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      resolve(result.data);
    } catch (error) {
      reject();
      const errMsg = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
    }
  });
};
// getMemberApplyDetailAxios
const loaded = ref(false);
// 获取入会申请列表
const getMemberList = async (params) => {
  // teamId
  loaded.value = false;
  params.page = pagination.current;
  params.page_size = pagination.pageSize;
  try {
    let result = await applyVisitList(params, currentTeamId.value);
    console.log(result);
    result = getResponseResult(result);
    if (!result) return;
    memberData.value = result.data.list;
    pagination.total = result.data.total;
    console.log(memberData.value.length);
    loaded.value = true;
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    MessagePlugin.error(errMsg);
    loaded.value = true;
  }
};

const initData = () => {
  getMemberList({});
  getStatisticsReq();
};
onMounted(() => {
  initData();
})
onActivated(() => {
  initData();
});

const onLookDetail = (row: any) => {
  onGetMemberFeeDetailAxios(row).then((res: any) => {
    lookFeeModalRef.value.onOpen(res);
  });
};

const typeChange = (e, ctx) => {
  onSearch();
};

const passRun = (row) => {
  applyAgree(row.id, currentTeamId.value).then((res) => {
    console.log(res, 'res');
    if (res.data?.code === 0) {
      MessagePlugin.success(t('ebook.vsc'));
      emits('getRedNum');
    } else {
      MessagePlugin.error(t('ebook.ver'));
    }
    initData();
  }).catch((error) => {
    const errMsg = error instanceof Error ? error.message : error;
    MessagePlugin.error(errMsg);
    initData();
  });
}
const refuseRun = (row) => {
  applyReject(row.id, currentTeamId.value).then((res) => {
    if (res.data?.code === 0) {
      MessagePlugin.success(t('ebook.vsc'));
      emits('getRedNum');
    } else {
      MessagePlugin.error(t('ebook.ver'));
    }
    initData();
  }).catch((error) => {
    const errMsg = error instanceof Error ? error.message : error;
    MessagePlugin.error(errMsg);
    initData();
  });
}

const getStatisticsReq = () => {
  getStatistics(currentTeamId.value).then((res: any) => {
    if (res) {
      console.log(res.data.data);

      appCount.value = res.data?.data?.visit_apply_count;
    }
  });
}
</script>

<style lang="less" scoped>
@import "@renderer/views/member/member_home/panel/public.less";


.iconadd {
  color: #ffffff;
  font-size: 24px;
}

.main_body {
  display: flex;
  flex-direction: column;
}

.main-box {
  display: flex;
  align-items: center;
}

.type-tag1 {
  display: flex;
  height: 24px;
  padding: 0px var(--kyy_radius_toopltip, 6px);
  justify-content: center;
  align-items: center;
  gap: 4px;
  border-radius: var(--kyy_radius_tag_s, 4px);
  background: var(--kyy_color_tag_bg_brand, #EAECFF);
  color: var(--kyy_color_tag_text_brand, #4D5EFF);
  text-align: center;

  /* kyy_fontSize_2/bold */
  font-family: "PingFang SC";
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 22px;
  /* 157.143% */
}

.type-tag2 {
  display: flex;
  height: 24px;
  min-height: 24px;
  max-height: 24px;
  padding: 0px 8px;
  justify-content: center;
  align-items: center;
  gap: 4px;
  border-radius: var(--kyy_radius_tag_s, 4px);
  background: var(--kyy_color_tag_bg_magenta, #FFE3F1);
  color: var(--kyy_color_tag_text_magenta, #FF4AA1);
  text-align: center;
  font-family: "PingFang SC";
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 22px;
  /* 157.143% */
}

.back-box {
  display: flex;
  height: 56px;
  padding: 0px 16px;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
  align-self: stretch;
  border-bottom: 1px solid var(--divider-kyy_color_divider_light, #ECEFF5);
  background: var(--bg-kyy_color_bg_default, #FFF);

  .backt {
    cursor: pointer;

    .iconarrowleft {
      font-size: 20px;
      color: #516082;
      margin-right: 8px;
    }

    display: flex;
    align-items: center;
    color: var(--text-kyy_color_text_1, #1A2139);

    /* kyy_fontSize_2/regular */
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    /* 157.143% */
  }

  .dr {
    display: flex;
    height: 16px;
    min-width: 1px;
    max-width: 1px;
    align-items: flex-start;
    gap: 4px;
    background: #D5DBE4;
  }

  .numtitle {
    color: var(--text-kyy_color_text_1, #1A2139);

    /* kyy_fontSize_3/bold */
    font-family: "PingFang SC";
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
    /* 150% */
  }
}

.lose {
  display: flex;
  height: 20px;
  min-height: 20px;
  max-height: 20px;
  padding: 0px 4px;
  justify-content: center;
  align-items: center;
  gap: 4px;
  border-radius: var(--kyy_radius_tag_s, 4px);
  background: var(--kyy_color_tag_bg_gray, #ECEFF5);
  color: var(--kyy_color_tag_text_gray, #516082);
  text-align: center;
  font-family: "PingFang SC";
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  max-width: max-content;
}

.pass {
  display: flex;
  height: 20px;
  min-height: 20px;
  max-height: 20px;
  padding: var(--checkbox-kyy_radius_checkbox, 2px) 4px;
  justify-content: center;
  align-items: center;
  gap: 4px;
  border-radius: var(--kyy_radius_tag_s, 4px);
  background: var(--kyy_color_tag_bg_success, #E0F2E5);
  color: var(--kyy_color_tag_text_success, #499D60);
  text-align: center;
  font-family: "PingFang SC";
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  max-width: max-content;
}

.refuse {
  display: flex;
  height: 20px;
  min-height: 20px;
  max-height: 20px;
  padding: 0px 4px;
  justify-content: center;
  align-items: center;
  gap: 4px;
  border-radius: var(--kyy_radius_tag_s, 4px);
  background: var(--kyy_color_tag_bg_error, #F7D5DB);
  color: var(--kyy_color_tag_text_error, #D54941);
  text-align: center;

  max-width: max-content;
  font-family: "PingFang SC";
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  /* 166.667% */
}

.operates-item {
  display: flex;
  padding: 4px;
  justify-content: center;
  align-items: center;
  gap: 10px;
}

.operates-item:hover {
  border-radius: 4px;
  background: var(--bg-kyy_color_bgBrand_hover, #eaecff);
}

.empty {
  min-height: 60vh;
}

:deep(.t-table__content) {
  overflow-x: hidden;
}
</style>
