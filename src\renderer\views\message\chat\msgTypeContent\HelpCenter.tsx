// 帮助中心
import { defineComponent } from 'vue';
import Empty from "@/components/common/Empty.vue";
import { AppCard, AppCardHeader, AppCardBody, AppCardFooter, AppCardText } from '../MessageAppCard';
import { i18nt } from "@/i18n";

import LynkerSD<PERSON> from "@renderer/_jssdk";
const { ipcRenderer } = LynkerSDK;

export default defineComponent({
    props: {
        data: { type: Object, default: null },
    },
    setup(props, ctx) {
        return () => {
            const data = props.data.contentExtra?.data;
            const click = () => {
                console.log('data===', props.data);
                 // ipcRenderer.invoke("my-help", { id: 66, module_id: 27, directory_id: 67 });
                // ipcRenderer.invoke("my-help", { id: data.id, module_id: data.module_id, directory_id: data.directory_id });
                LynkerSDK.openMyHelpWindow({ id: data.id, module_id: data.module_id, directory_id: data.directory_id });
            };
            return (
                <AppCard onClick={click}>
                    <AppCardHeader>
                        { data.title }
                    </AppCardHeader>

                    {!data ? <Empty text={i18nt('im.msg.noData')} /> : (
                        <>
                            <AppCardBody>
                                <AppCardText type='main' style='margin: 8px 0;width:100%;display:flex;gap:8px;' >
                                <div style={`
                                    overflow: hidden;
                                    display: -webkit-box;
                                    text-overflow: ellipsis;
                                    -webkit-line-clamp: 2;
                                    -webkit-box-orient: vertical;
                                    font-size: 14px;
                                    font-weight: 600;
                                    color: var(--text-kyy-color-text-1, #1A2139);
                                `}>
                                    {data.content}
                                </div>
                                </AppCardText>
                            </AppCardBody>
                            {data.cover && (
                                <AppCardFooter>
                                    <img src={data.cover} style={'width: 100%;height: 250px;display: block;'}/>
                                </AppCardFooter>
                            )}
                        </>
                    )}
                    <div style={`color:#828DA5;padding:0 12px 12px 12px;`}>{i18nt('im.msg.help')}</div>
                </AppCard>
            );
        };
    }
});

