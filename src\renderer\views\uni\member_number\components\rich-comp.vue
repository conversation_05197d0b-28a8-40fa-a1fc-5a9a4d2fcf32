<template>
  <t-loading
    size="medium"
    class="memberLoading"
    :loading="isLoading"
    show-overlay
    text="加载中..."
  >
    <div class="page">
      <div class="rich">
        <div class="between">
          <div class="rich-tabs">
            <!-- v-show="!(currentTab.key === 'all' && richArr.length < 1)" -->
            <span
              v-for="(tem, temIndex) in tabs"

              :key="temIndex"
              class="tem cursor"
              :class="{active: currentTab.key === tem.key}"
              @click="onClickTab(tem)"
            >{{ tem.label }}</span>
          </div>
          <t-input
            v-model="keyword"
            :placeholder="$t('member.second.k')"
            :maxlength="50"
            clearable
            style="width: 304px"
            @change="onSearch"
          >
            <template #prefix-icon>
              <iconpark-icon name="iconsearch-a961a3le" class="iconsearch"></iconpark-icon>
            </template>
          </t-input>
        </div>

        <div class="lists mt-24px">

          <div
            v-for="(rich, richIndex) in richArr"

            :key="richIndex"
            class="lists-item cursor"
            @click="onOpenRich(rich)"
          >
            <div class="left">

              <img v-lazy="rich.goods_image" class="lo">

            </div>
            <div class="right">
              <div class="top">

                <span class="name line-2">
                  <span v-show="rich.is_top === 1" class="tip blue">置顶</span>
                  <span v-show="rich.type === 2" class="tip yellow">{{ rich.type_text }}</span>
                  <span v-show="rich.type === 1" class="tip">{{ rich.type_text }}</span>
                  {{ rich.title }}
                </span>
              </div>
              <div class="bottom">
                <span class="company">
                  <kyy-avatar
                    class="av"
                    :avatar-size="'20px'"
                    :image-url="rich.avatar || ORG_DEFAULT_AVATAR"
                    :user-name="rich.name"
                    :shape="'circle'"
                  />
                  <span class="text  line-1">
                    {{ rich.name }}
                  </span>
                </span>
                <!-- <span class="area ">
                  <iconpark-icon name="iconorientation" class="icon"></iconpark-icon>
                  <span class="text line-1">
                    {{ rich.area }}
                  </span>
                </span> -->
              </div>
            </div>
          </div>
        </div>
        <div v-show="richArr.length > 0" class="example-more mt-24px">
          <span v-if=" pagination.total > richArr.length" class="more cursor" @click="onMore"> {{ isLoading ? '加载中...': '加载更多' }} </span>
          <span v-else-if="richArr.length > pagination.pageSize" class="noempty">
            <span class="line"></span><span class="toText">{{ $t('member.second.l') }}</span>  <span class="line"></span>
          </span>
        </div>

        <template v-if="!isNetworkError">
          <div v-show="!isLoading && richArr.length < 1" class="noEmpty">
            <Empty :tip="$t('member.bing.j')" />
          </div>
        </template>
        <template v-else>

          <div v-show="!isLoading && richArr.length < 1" class="noEmpty">
            <Empty name="offline">
              <template #tip>
                <div class="tipEmpty">
                  <span class="text">网络链接失败，请检查网络后重试</span>
                  <t-button theme="primary" class="btn" @click="onSearch">点击重试</t-button>
                </div>
              </template>
            </Empty>
          </div>
        </template>
      </div>
    </div>
  </t-loading>
  <detailsReadonly
    ref="detailsReadonlyRef"
    :new-jupm="true"
    @close="onCloseRich"
  />
</template>

<script setup lang="ts">
import { computed, onActivated, reactive, ref, toRaw, watch } from "vue";
import { getMemberNichAxios } from "@renderer/api/uni/api/businessApi";
import { getUniTeamID } from "@renderer/views/uni/utils/auth";
import to from 'await-to-js';
// import detailsReadonly from "@renderer/components/business/manage/detailsReadonly.vue";
import detailsReadonly from "@renderer/views/square/niche/components/detail.vue";
import { useI18n } from 'vue-i18n';
import Empty from "@renderer/components/common/Empty.vue";
import { useUniStore } from "@renderer/views/uni/store/uni";
import { platform } from '@renderer/views/digital-platform/utils/constant';
import { useRoute, useRouter } from "vue-router";
import { useDigitalPlatformStore } from "@renderer/views/digital-platform/store/digital-platform-store";
import useRouterHelper from "@renderer/views/square/hooks/routerHelper";
import { getSrcLogo } from "@renderer/views/message/service/msgUtils";
import KyyAvatar from "@/components/kyy-avatar/index.vue";
import { ORG_DEFAULT_AVATAR } from "@/views/square/constant";
const digitalPlatformStore = useDigitalPlatformStore();
const route = useRoute();


const { t } = useI18n();


const store = useUniStore();
const isLoading = ref(false);
const keyword = ref('');
const isNetworkError = ref(false);


const props = defineProps({
  platform: {
    type: String,
    default: '',
  },
});
// 平台类型 目前只有digital-platform
const platformCpt = computed(() => props.platform || route.query?.platform);


const currentTeamId = computed(() => {
  if (platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount?.teamId;
  }
    return getUniTeamID();

});



const tabs = ref([
   {
    label: t('member.winter_column.statusOptions_1'),
    key: 'all'
   },
   {
    label: t('member.bing.k'),
    key: '2'
   },
   {
    label: t('member.bing.l'),
    key: '1'
   },
   {
    label: t('member.bing.n'),
    key: '3'
   },
]);
const currentTab = ref(tabs.value[0]);

const pagination = {
  pageSize: 24,
  page: 1,
  total: 0,
};

const richArr = ref([]);

const useType = (type) => {

  let msgType = '';
  if (type === 1) {
    msgType = '供应';
  } else if (type === 2) {
    msgType = '需求';
  }
  return msgType;
};
const detailsReadonlyRef = ref(null);
const router = useRouter();
const digitalRouter = useRouterHelper("digitalPlatformIndex");
const { routeList } = useRouterHelper("uniIndex");
const onOpenRich = (row) => {
  const quParams = {
    uuid: row.uuid,
    from: 'digital'
  };
  const pageKey = 'uni_rich_detail';
  if (platformCpt.value === platform.digitalPlatform) {
      const searchMenu = digitalRouter.routeList.find((v) => v.name.includes(pageKey));
      // store.addTab(routeList.filter((v) => v.affix).filter(roleFilter));
      searchMenu.query = { ...quParams, platform: platform.digitalPlatform, from: 'uni' };
      router.push({ path: searchMenu.fullPath, query: searchMenu.query });
      searchMenu.title = row.title;
      digitalPlatformStore.addTab(toRaw(searchMenu), true);
    } else {
      const searchMenu = routeList.find((v) => v.name === pageKey);
      // store.addTab(routeList.filter((v) => v.affix).filter(roleFilter));
      router.push({ path: searchMenu.fullPath, query: quParams });
      searchMenu.title = row.title;
      store.addTab(toRaw(searchMenu));
    }
};
const onCloseRich = () => {
  // detailsReadonlyRef.value.
};
const onClickTab = (tab) => {
  currentTab.value = tab;
  pagination.page = 1;
  onSetArr(true);
};
const page = ref(null);
const onSetArr = async (isCover= false) => {
  // const obj = {
  //     tip: '供应',
  //     name: '一次性丁腈乳胶弹pvc手套餐饮厨一次性丁腈乳胶弹pvc手套餐饮厨一次性丁腈乳胶弹pvc手套餐饮厨',
  //     company: '就看见四点零分考虑到实际发',
  //     area: '中国相关',
  // };

  // for (let i = 0; i< 200; i++) {
  //     richArr.value.push(obj);
  // }
  const params = {
    related_type: 4,
    promotion_type: 2,
    related_id: currentTeamId.value,
    promotion_related: currentTeamId.value,
    is_top: '',
    title: keyword.value,
    ...pagination
  };
  if (['1', '2'].includes(currentTab.value.key)) {
    params.type = Number(currentTab.value.key);
    params.is_top = 0;
  } else if (currentTab.value.key === '3') {
    params.is_top = 1;
  } else {
    params.is_top = '';
  }

  // 缓存信息存储
  const caches = store.getStorageDatas;
  const cache = caches.find((v) => v.teamId === currentTeamId.value);
  if (!cache) {
    isLoading.value = true;
  }
  const [err, res] = await to(getMemberNichAxios(params));
  isLoading.value = false;
  if (err) {
    console.log(err, 'rich-err');
    if (err?.message === 'Network Error') {
      isNetworkError.value = true;
      // if(cache) {
      //   richArr.value = cache.memberRich?.items || [];
      //   pagination.total = cache.memberRich?.total || 0;
      // }

    }
    return;
  }
  isNetworkError.value = false;
  let { data } = res;
  data = data.data;
  console.log(data);
  const richs = data.total > 0 ? data.list.map((v) => (
    { ...v.apply_data, uuid: v.uuid, goods_image: (v.images.length > 0 ? getSrcLogo(v.images[0].file_name): ''), is_top: v.is_top, type: v.type, title: v.title, type_text: useType(v.type) }
    )): [];
  pagination.total = data.total;
  if (isCover) {
    richArr.value = richs;
  } else {
    richArr.value = richArr.value.concat(richs);
  }
  const memberRich = {
    items: toRaw(richArr.value),
    total: data.total
  };
  if (cache) {
    cache.memberRich = memberRich;
  } else {
    caches.push({ teamId: currentTeamId.value, memberRich });
  }

};
// onSetArr();

const onLoadCache = () => {
  // 缓存信息存储
  const caches = store.getStorageDatas;
  const cache = caches.find((v) => v.teamId === currentTeamId.value);
  if (cache) {
    richArr.value = cache.memberRich?.items || [];
    // pagination.total = cache.memberRich?.total || 0;
  }
  pagination.total = 0;
  onSetArr(true);
};

onActivated(() => {
  onLoadCache(); // 会员名录列表
});

const onMore = () => {
  pagination.page += 1;
  onSetArr();
};

const onSearch = () => {
  console.log('onSearch');
  pagination.page = 1;
  // richArr.value = [];
  onSetArr(true);
};


onActivated(() => {
  // onSearch();
  onSearch();
});

// watch(
//   () => store.activeAccount,
//   (val) => {
//     if (val) {
//       // updateAllCount();
//       onSearch();
//     }
//   },
//   {
//     deep: true
//     // immediate: true
//   }
// );

</script>

<style lang="less" scoped>
// lss 加个滚动条样式
@import "@renderer/views/engineer/less/common.less";
.memberLoading {
  height: calc(100% - 40px);
}
.between {
  display: flex;
  justify-content: space-between;
}
.page {
  display: flex;
  justify-content: center;
  width: 100%;
  height: inherit;
  padding: 16px;
  background: var(--bg-kyy_color_bg_deep, #F5F8FE);
  height: 100%;

}
.rich {
   padding: 16px;

   max-width: 1184px;
   min-width: 1088px;
  //  margin: 0 16px;
   width: 100%;
   background-color: #fff;
   border-radius: 8px;
   height: inherit;
   overflow-y: overlay;
   &-tabs {
      display: flex;
      gap: 8px;
      .tem {
        padding: 4px 16px;
        border-radius: var(--radius-kyy_radius_button_s, 4px);
        border: 1px solid var(--color-button_border-kyy_color_buttonBorder_border_dedault, #D5DBE4);
        background: var(--color-button_border-kyy_color_buttonBorder_bg_default, #FFF);
        color: var(--color-button_border-kyy_color_buttonBorder_text_default, #516082);
        text-align: center;

        min-width: 80px;
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 22px; /* 157.143% */
      }
      .active {
        background: var(--color-button_primary-kyy_color_button_primary_bg_default, #4D5EFF);
        color: var(--lingke-White-100, #FFF);
        border: 1px solid  var(--color-button_primary-kyy_color_button_primary_bg_default, #4D5EFF);
      }
   }

   .lists {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
      width: 100%;
      &-item {

        padding: 12px;
        border-radius: 8px;
        background: var(--bg-kyy_color_bg_deep, #F5F8FE);
        width: calc((100% - 32px) / 3);

        display: flex;
        gap: 12px;
        transition: all 0.15s linear;
        &:hover {
          border-radius: 8px;
          background: var(--bg-kyy_color_bg_light, #FFF);
          /* kyy_shadow_m */
          box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.12);
          transition: all 0.15s linear;
        }
        .left {
            width: 72px;
            height: 72px;
            position: relative;
            .lo {
                width: 72px;
                height: 72px;
                border-radius: 3.6px;
           }
           .ri {
                height: 20px;
                position: absolute;
                top: 0;
                left: 0;

           }
        }
        .right {
            display: flex;
            flex-direction: column;
            justify-content: center;
            gap: 8px;
           .top {
                .name {
                  color: var(--text-kyy_color_text_1, #1A2139);
                  /* kyy_fontSize_2/bold */
                  font-family: "PingFang SC";
                  font-size: 14px;
                  font-style: normal;
                  font-weight: 600;
                  line-height: 22px; /* 157.143% */
                    .tip {
                      color: #11BDB2;
                    text-align: center;
                    font-size: 12px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 20px; /* 166.667% */
                    padding: 0 4px;
                    border-radius: var(--kyy_radius_tag_s, 4px);
background: var(--kyy_color_tag_bg_cyan, #E6F9F8);
                    }
                    .yellow {
                        color: var(--kyy_color_tag_text_warning, #FC7C14);
                        background: var(--kyy_color_tag_bg_warning, #FFE5D1);
                    }
                    .blue{
                  color: #4D5EFF;
                  background: #EAECFF;
                  margin-right: 4px;
                }
                }
           }
           .bottom {
               display: flex;
               align-items: center;

               .company {
                 flex: 1 1 auto;
                 display: flex;
                 align-items: center;
                 gap: 4px;
                 height: 20px;

                 .av {
                    flex: none;
                 }
                 .text {
                    flex: 1;
                    max-width: 140px;
                    color: var(--text-kyy_color_text_3, #828DA5);
                    /* kyy_fontSize_1/regular */
                    font-family: "PingFang SC";
                    font-size: 12px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 20px; /* 166.667% */
                 }
               }
               .area {
                 flex: none;
                //  width: 72px;
                 display: flex;
                 align-items: center;
                 .icon {
                    color: #21ACFA;
                    font-size:20px;
                 }
                 .text {
                    width: 60px;
                 }
               }
           }

        }
      }
   }
   .example-more {
    display: flex;
    align-items: center;
    justify-content: center;

    .more {
      border-radius: var(--radius-kyy_radius_button_s, 4px);
      border: 1px solid var(--color-button_border-kyy_color_buttonBorder_border_dedault, #D5DBE4);
      background: var(--color-button_border-kyy_color_buttonBorder_bg_default, #FFF);
      color: var(--color-button_border-kyy_color_buttonBorder_text_default, #516082);
      text-align: center;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
      padding: 4px 16px;
    }
    .noempty {
      color: var(--text-kyy_color_text_2, #516082);
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
      display: flex;
      align-items: center;
      width: 100%;
      gap: 12px;
      padding-bottom: 16px;
      .toText {
        flex: none;
      }
      .line {
        height: 1px;
        background: var(--divider-kyy_color_divider_deep, #D5DBE4);
        width: 100%;
      }
    }
  }
}
</style>
