import { ipc<PERSON>ain, BrowserWindow, shell, webContents } from "electron";
import { getSDK, getRandomUUID } from '@lynker-desktop/electron-sdk/main';
import fs from "fs-extra";
import lodash from "lodash";
import { IsUseSysTitle } from "../config/const";
import { otherWindowConfig } from "../config/windowsConfig";
import { imgArrayType, videoArrayType } from "../../renderer/views/clouddisk/clouddiskhome/fileType";
import { db } from "../message/dbManager";
import { GlobalStatus, ipcMainHandle } from "@main/utils";
import { attachViewWithRoute } from './bvManager';

export const TB_DOWNLOADRECORD = "linker_download_record_table";
export type FileDowRecordData = {
  url: string;
  download_path: string;
  file_name: string;
  file_type: string;
  file_size: number;
};

// Window storage
export const viewWindows = {
  viewFileWin: null,
  viewImgWin: null,
  viewVideoWin: null
};
export const viewFileWin = viewWindows.viewFileWin;
export const viewImgWin = viewWindows.viewImgWin;
export const viewVideoWin = viewWindows.viewVideoWin;

export const restViewWin = () => {
  Object.keys(viewWindows).forEach(key => {
    viewWindows[key] = null;
  });
};

/**
 * Close all preview windows
 */
export const closePreviwew = () => {
  Object.values(viewWindows).forEach(win => {
    if (win) {
      try {
        if (!win.isDestroyed()) {
          win.close();
        }
      } catch (error) {
        console.error('Close window error:', error);
      }
    }
  });

  // Ensure all window references are cleared
  restViewWin();
};


/**
 * Create a preview window
 */
const createPreviewWindow = async (type: string, options?: any) => {
  const bwOptions = {
    titleBarStyle: IsUseSysTitle ? "default" : "hidden",
    ...otherWindowConfig(),
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      backgroundThrottling: false,
    },
    width: 1296,
    transparent: false,
    show: true,
    fullscreen: false,
    minWidth: 960,
    minHeight: 480,
    height: 720,
  }
    let url = '';
    switch(type) {
      case 'viewVideo':
        url = '/viewVideo';
        break;
      case 'viewImg':
        url = '/viewImg';
        break;
      case 'viewFile':
        url = `/viewFile?${new URLSearchParams(options).toString()}`;
        break;
    }
  try {
    const win = getSDK().windowManager.create({
      name: `preview-method-window-${getRandomUUID()}`,
      url: url,
      browserWindow: bwOptions
    });

    // Use BrowserView to load content


    await attachViewWithRoute(win, {
      type: 'default',
      url,
      viewOptions: {
        bounds: {
          x: 0,
          y: 0,
          width: bwOptions.width,
          height: bwOptions.height
        },
        webPreferences: bwOptions.webPreferences,
      }
    });
    win.on('close', (event) => {
      console.log('preview window close');
      event.preventDefault();
      // Immediately clear window reference to prevent using destroyed objects
      const winKey = `${type}Win`;
      viewWindows[winKey] = null;

      // Handle window cleanup
      try {
        let view = win.getBrowserView();
        if (view) {
          try {
            // win?.removeBrowserView(view);
            getSDK().windowManager.close(view?._name);
            if (!view.webContents?.isDestroyed()) {
              win.setBrowserView(null);  // 先移除
              view.webContents?.destroy();       // 释放资源
              view = null;
            }
          } catch (error) {
            console.error('Remove browser view error:', error);
          }
        }
      } catch (error) {
        console.error('Get browser view error:', error);
      }

      // Handle window focus
      if (type === 'viewImg' && GlobalStatus.latestViewImgWebContentId) {
        try {
          const webContent = webContents.fromId(GlobalStatus.latestViewImgWebContentId);
          if (webContent && !webContent.isDestroyed()) {
            const parentWin = BrowserWindow.fromWebContents(webContent);
            if (parentWin && !parentWin.isDestroyed()) {
              parentWin.focus();
              parentWin.getBrowserViews()?.forEach((item) => {
                if (item && !item.webContents.isDestroyed()) {
                  item.webContents.focus();
                }
              });
            }
          }
        } catch (error) {
          console.error('Focus parent window error:', error);
        } finally {
          GlobalStatus.latestViewImgWebContentId = undefined;
        }
      }

      // Destroy window
      setTimeout(() => {
        try {
          if (win && !win.isDestroyed()) {
            getSDK().windowManager.close(win?._name);
            win.destroy();
          }
        } catch (error) {
          console.error('Destroy window error:', error);
        }
      }, 100);
    });

    return win;
  } catch (error) {
    console.error('Create preview window error:', error);
    return null;
  }
};

// File download record functions
const addDowRecord = (record: FileDowRecordData) => {
  try {
    db?.prepare(
      `INSERT OR REPLACE INTO ${TB_DOWNLOADRECORD} (url, download_path, file_name, file_type, file_size)
      VALUES (:url, :download_path, :file_name, :file_type, :file_size)`
    ).run(record);
  } catch (error) {
    console.error('Add download record error:', error);
  }
};

const getDowRecordByUrl = (url: String) => {
  try {
    const query = db?.prepare(`SELECT * FROM ${TB_DOWNLOADRECORD} WHERE url = ?`);
    const result = query.get(url);
    return result || null;
  } catch (error) {
    console.error('Get download record error:', error);
    return null;
  }
};

const delDowRecord = (fileUrl: string) => {
  try {
    db?.prepare(`DELETE FROM ${TB_DOWNLOADRECORD} WHERE url =?`).run(fileUrl);
  } catch (error) {
    console.error('Delete download record error:', error);
  }
};

// Initialize preview functionality
export const initViewWin = () => {
  previewFnInit();
};

// 定义预览类型枚举
enum PreviewType {
  Video = 'viewVideo',
  Image = 'viewImg',
  File = 'viewFile'
}

// 定义预览数据接口
interface PreviewData {
  type?: string;
  [key: string]: any;
}

// 定义消息映射
const messageTypeMap = {
  [PreviewType.Video]: { event: 'arrayData', clearEvent: 'clear-video' },
  [PreviewType.Image]: { event: 'arrayData' },
  [PreviewType.File]: { event: 'upd-url' }
};

// 处理预览窗口的通用函数
const handlePreview = async (e, val, previewType: PreviewType, needSetLatestId = false) => {
  try {
    // 解析数据
    let fileData = JSON.parse(val);
    if (!Array.isArray(fileData)) {
      fileData = [fileData];
    }

    // 设置最后一次打开预览窗口ID（如果需要）
    if (needSetLatestId) {
      GlobalStatus.latestViewImgWebContentId = e.sender.id;
    }

    const winKey = `${previewType}Win`;
    let win = viewWindows[winKey];

    // 检查窗口是否存在且有效
    if (win && !win.isDestroyed()) {
      // 窗口存在且有效，获取视图并发送消息
      try {
        const view = win.getBrowserView();
        if (view && view.webContents && !view.webContents.isDestroyed()) {
          // 发送适当的消息
          const messageConfig = messageTypeMap[previewType];
          if (messageConfig.clearEvent) {
            view.webContents.send(messageConfig.clearEvent);
          }
          view.webContents.send(
            messageConfig.event,
            previewType === PreviewType.File ? fileData[0] : fileData
          );
          win.show();
          return;
        }
      } catch (error) {
        console.error(`Get browser view error for ${previewType}:`, error);
      }
    }

    // 窗口不存在、无效或获取视图失败，创建新窗口
    viewWindows[winKey] = null; // 清除任何无效引用
    win = await createPreviewWindow(previewType, fileData[0]);
    if (!win) {
      throw new Error(`Failed to create ${previewType} window`);
    }
    viewWindows[winKey] = win;

    // 获取新创建窗口的视图并发送消息
    try {
      const view = win.getBrowserView();
      if (view && view.webContents && !view.webContents.isDestroyed()) {
        const messageConfig = messageTypeMap[previewType];
        if (messageConfig.clearEvent) {
          view.webContents.send(messageConfig.clearEvent);
        }
        view.webContents.send(
          messageConfig.event,
          previewType === PreviewType.File ? fileData[0] : fileData
        );
      } else {
        throw new Error(`Browser view is invalid for ${previewType}`);
      }
    } catch (error) {
      console.error(`Send message to ${previewType} error:`, error);
    }
  } catch (error) {
    console.error(`${previewType} preview error:`, error);
  }
};

// 根据文件类型确定预览类型
const getPreviewTypeByFileType = (fileType: string): PreviewType => {
  if (videoArrayType.includes(fileType.toLowerCase())) {
    return PreviewType.Video;
  } else if (imgArrayType.includes(fileType.toLowerCase())) {
    return PreviewType.Image;
  } else {
    return PreviewType.File;
  }
};

// Register IPC handlers for preview functionality
const previewFnInit = () => {
  // 统一文件预览处理器
  ipcMainHandle("preview-file", lodash.debounce(async (e, val) => {
    try {
      let fileType = "";
      let fileData = JSON.parse(val);

      if (!Array.isArray(fileData)) {
        fileType = fileData.type;
        fileData = [fileData];
      } else {
        fileType = fileData[0].type;
      }

      GlobalStatus.latestViewImgWebContentId = e.sender.id;
      const previewType = getPreviewTypeByFileType(fileType);
      await handlePreview(e, val, previewType);
    } catch (error) {
      console.error('preview-file error: ', error);
    }
  }, 300));

  // 视频预览
  ipcMainHandle("view-video", lodash.debounce(async (e, val) => {
    await handlePreview(e, val, PreviewType.Video);
  }, 300));

  // 图片预览
  ipcMainHandle("view-img", lodash.debounce(async (e, val) => {
    await handlePreview(e, val, PreviewType.Image, true);
  }, 300));

  // 文件预览
  ipcMainHandle("view-file", lodash.debounce(async (e, val) => {
    await handlePreview(e, val, PreviewType.File);
  }, 300));

  // 其他IPC处理器保持不变
  ipcMainHandle("open-folder", async (event, path) => {
    try {
      const exist = fs.existsSync(path);
      if (exist) {
        shell.showItemInFolder(path);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Open folder error:', error);
      return false;
    }
  });

  // 下载记录相关IPC处理器
  ipcMainHandle("get-dow-record-by-url", async (e, url) => {
    try {
      return getDowRecordByUrl(url);
    } catch (error) {
      console.error('Get download record error:', error);
      return null;
    }
  });

  ipcMainHandle("del-dow-record", async (e, url) => {
    try {
      delDowRecord(url);
    } catch (error) {
      console.error('Delete download record error:', error);
    }
  });

  ipcMainHandle("add-dow-record", async (e, val) => {
    try {
      addDowRecord({
        url: val.url,
        download_path: val.download_path,
        file_name: val.file_name,
        file_type: val.file_type,
        file_size: val.file_size,
      });
    } catch (error) {
      console.error('Add download record error:', error);
    }
  });
};
