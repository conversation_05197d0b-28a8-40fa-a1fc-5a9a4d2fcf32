// 角色信息
export interface Role {
	roleId: number; // 角色ID标识
	roleName: string; // 角色名
}

// 部门/岗位信息
export interface Position {
	departmentId: string;
	departmentName: string;
	jobId: number;
	jobName: string;
}

// 获取组织成员详情类型
export interface OrganizeMemberInfo {
	openId: string; // 用户唯一标识
	name: string; // 员工姓名
	avatar: string; // 员工头像
	staffId: number; // 员工ID标识
	teamId: string; // 组织标识
	roles: Role; // 角色信息
	leader: string; // 直接上级标识
	leaderName: string; // 直接上级姓名
	telephone: string; // 联系电话
	no: string; // 员工号
	position: Array<Position>; // 部门/岗位信息
}

export interface SetStateExternalJobsOptions {
	/**
	 * 岗位ID
	 */
	id: number;
	/**
	 * 状态，0 禁用 1 启用
	 */
	state: number;
}
