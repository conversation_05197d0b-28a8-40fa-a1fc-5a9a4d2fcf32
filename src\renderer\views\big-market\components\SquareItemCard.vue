<template>
  <div class="square-item-card" @click="emit('card-click', data)">
    <!-- 广告标识 -->
    <div v-if="data.isAd" class="ad-badge">
      广告
    </div>

    <!-- 左侧区域：头像和关注按钮 -->
    <div class="card-left">
      <img :src="data.avatar || defaultOrgLogo" :alt="data.name" class="avatar" />

      <div
        class="follow-button"
        :class="data.followed ? 'follow-button--active' : 'follow-button--inactive'"
        @click.stop="emit('follow-click', data)"
      >
        <!-- 加载状态 -->
        <t-loading v-if="isFollowLoading" size="small" />

        <template v-else>
          <!-- 已关注状态的图标 -->
          <svg
            v-if="data.followed"
            xmlns="http://www.w3.org/2000/svg"
            width="14"
            height="14"
            viewBox="0 0 14 14"
            fill="none"
            class="follow-icon"
          >
            <path
              d="M7.03848 1.17139C7.04632 1.17658 7.06215 1.18909 7.07559 1.22314L8.36563 4.4917C8.53289 4.91528 8.92271 5.22146 9.38809 5.25928L12.7592 5.53369C12.7797 5.53544 12.7899 5.5411 12.7982 5.54834C12.8094 5.55804 12.8236 5.57672 12.8324 5.60498C12.8413 5.63341 12.8419 5.66242 12.8363 5.68506C12.8315 5.70467 12.8219 5.72434 12.8002 5.74365L10.227 8.0376C9.88503 8.34244 9.74369 8.812 9.84512 9.25244L10.6371 12.6909C10.6457 12.7282 10.6419 12.754 10.6352 12.772C10.6274 12.7924 10.6129 12.811 10.5961 12.8237C10.5796 12.8362 10.5655 12.8398 10.558 12.8403C10.5541 12.8406 10.5426 12.8406 10.5209 12.8267L7.64005 10.9761C7.24859 10.7247 6.7512 10.7246 6.35977 10.9761L3.47891 12.8267C3.45718 12.8406 3.44575 12.8406 3.4418 12.8403C3.43426 12.8398 3.42017 12.8361 3.40372 12.8237C3.38692 12.811 3.37337 12.7924 3.36563 12.772C3.35881 12.754 3.35407 12.7284 3.3627 12.6909L4.15469 9.25244C4.25612 8.81212 4.11554 8.34246 3.77383 8.0376L1.20059 5.74365C1.17892 5.72433 1.16834 5.70469 1.16348 5.68506C1.15795 5.66242 1.15856 5.63341 1.16739 5.60498C1.17624 5.57672 1.19038 5.55806 1.20157 5.54834C1.21002 5.54101 1.22074 5.53539 1.24161 5.53369L4.6127 5.25928C5.0779 5.22132 5.46698 4.91516 5.63419 4.4917L6.9252 1.22314C6.93863 1.18914 6.95349 1.17659 6.96133 1.17139C6.97144 1.16469 6.98507 1.15967 7.0004 1.15967C7.0155 1.15974 7.02847 1.16478 7.03848 1.17139Z"
              fill="#4D5EFF"
              stroke="#4D5EFF"
              stroke-width="1.12"
            />
          </svg>

          <!-- 未关注状态的图标 -->
          <svg
            v-else
            xmlns="http://www.w3.org/2000/svg"
            width="14"
            height="14"
            viewBox="0 0 14 14"
            fill="none"
            class="follow-icon"
          >
            <path
              d="M7.03848 1.17139C7.04631 1.17658 7.06214 1.18909 7.07558 1.22314L8.36562 4.4917C8.53288 4.91528 8.92271 5.22146 9.38808 5.25928L12.7592 5.53369C12.7796 5.53544 12.7899 5.5411 12.7982 5.54834C12.8094 5.55804 12.8236 5.57672 12.8324 5.60498C12.8413 5.63341 12.8419 5.66242 12.8363 5.68506C12.8315 5.70467 12.8219 5.72434 12.8002 5.74365L10.227 8.0376C9.88502 8.34244 9.74368 8.812 9.84512 9.25244L10.6371 12.6909C10.6457 12.7282 10.6419 12.754 10.6352 12.772C10.6274 12.7924 10.6129 12.811 10.5961 12.8237C10.5796 12.8362 10.5655 12.8398 10.558 12.8403C10.5541 12.8406 10.5426 12.8406 10.5209 12.8267L7.64004 10.9761C7.24858 10.7247 6.75119 10.7246 6.35976 10.9761L3.4789 12.8267C3.45717 12.8406 3.44574 12.8406 3.4418 12.8403C3.43425 12.8398 3.42016 12.8361 3.40371 12.8237C3.38691 12.811 3.37336 12.7924 3.36562 12.772C3.3588 12.754 3.35406 12.7284 3.36269 12.6909L4.15469 9.25244C4.25611 8.81212 4.11553 8.34246 3.77383 8.0376L1.20058 5.74365C1.17891 5.72433 1.16833 5.70469 1.16348 5.68506C1.15794 5.66242 1.15855 5.63341 1.16738 5.60498C1.17623 5.57672 1.19037 5.55806 1.20156 5.54834C1.21001 5.54101 1.22074 5.53539 1.2416 5.53369L4.61269 5.25928C5.0779 5.22132 5.46697 4.91516 5.63418 4.4917L6.92519 1.22314C6.93862 1.18914 6.95348 1.17659 6.96133 1.17139C6.97144 1.16469 6.98507 1.15967 7.00039 1.15967C7.0155 1.15974 7.02846 1.16478 7.03848 1.17139Z"
              stroke="white"
              stroke-width="1.12"
            />
          </svg>
        </template>

        <div class="follow-text">
          {{ data.followed ? '已关' : '关注' }}
        </div>
      </div>
    </div>

    <!-- 右侧区域：详细信息 -->
    <div class="card-right">
      <!-- 名称和组织类型 -->
      <div class="name-section">
        <iconpark-icon
          v-if="organizationIconName"
          :name="organizationIconName"
          class="org-icon"
        />
        <span class="name">{{ data.name }}</span>
      </div>

      <div class="info-item">
        广场号ID：{{ data.publicId }}
      </div>

      <div class="info-item">
        {{ data.industry || '--' }}
      </div>

      <div class="stats-section">
        <span>粉丝数：{{ fansCountText }}</span>
        <span>{{ data.distance ? distanceText : '' }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import type { RingkolMarketMarketSquare } from '../types';
import { formatAbbreviatedNumber, formatDistance } from '@renderer/utils/format';

interface Props {
  /** 广场号数据 */
  data: RingkolMarketMarketSquare;
  /** 是否正在加载关注状态 */
  isFollowLoading?: boolean;
}

interface Emits {
  (e: 'card-click', data: RingkolMarketMarketSquare): void;
  (e: 'follow-click', data: RingkolMarketMarketSquare): void;
}

const { data, isFollowLoading = false } = defineProps<Props>();

const emit = defineEmits<Emits>();

const fansCountText = computed(() => formatAbbreviatedNumber(data.fansCount));
const distanceText = computed(() => formatDistance(data.distance));

const organizationIconMap: Record<string, string> = {
  ENTERPRISE: 'iconenterprise',
  BUSINESS_ASSOCIATION: 'iconbusiness',
  INDIVIDUAL_BUSINESS: 'iconindividual',
  GOVERNMENT: 'icongov',
};

const organizationIconName = computed(() => organizationIconMap[data.organizationType] || '');
const defaultOrgLogo = 'http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/bigmarket/defaultOrgLogo.svg';
</script>

<style lang="less" scoped>
.square-item-card {
  display: flex;
  width: 279px;
  padding: 8px;
  justify-content: center;
  align-items: flex-start;
  gap: 8px;
  border-radius: 12px;
  border: 1px solid #BEE2FF;
  background: linear-gradient(180deg, rgba(227, 249, 254, 0.50) 0%, rgba(245, 250, 253, 0.50) 100%), #FFF;
  cursor: pointer;
  position: relative;
  transition: all 0.2s ease;

  &:hover {
    border: 1px solid var(--border-kyy_color_border_hover, #707EFF);
    box-shadow: 0px 4px 8px 0px rgba(0, 7, 66, 0.16);
  }

  .ad-badge {
    display: flex;
    padding: 2px 4px;
    justify-content: center;
    align-items: center;
    gap: 4px;
    position: absolute;
    top: 0px;
    left: 2px;
    border-radius: 8px 0px;
    background: var(--bg-kyy_color_bg_deep, #F5F8FE);
    color: var(--text-kyy_color_text_5, #ACB3C0);
    font-family: "PingFang SC";
    font-size: 10px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
  }

  .card-left {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 8px;
    align-self: stretch;

    .avatar {
      width: 46px;
      height: 46px;
      border-radius: 50%;
      object-fit: cover;
    }

    .follow-button {
      display: flex;
      padding: 2px 8px 2px 4px;
      align-items: center;
      justify-content: center;
      gap: 2px;
      border-radius: 99px;
      cursor: pointer;
      transition: all 0.2s ease;
      font-family: "PingFang SC";
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      text-align: center;
      font-feature-settings: 'liga' off, 'clig' off;
      min-width: 56px;
      max-width: 120px;
      white-space: nowrap;
      box-sizing: border-box;

      &.follow-button--active {
        background: var(--tagBG-kyy_color_tagBg_brand, #EAECFF);
        box-shadow: 0px 0.4px 1px 0px rgba(1, 48, 167, 0.50);
        color: var(--brand-kyy_color_brand_default, #4D5EFF);
      }
       &.follow-button--active:hover {
        border-radius: 99px;
        background: var(--bg-kyy_color_bgBrand_foucs, #DBDFFF);
        box-shadow: 0px 0.4px 1px 0px rgba(1, 48, 167, 0.50);
        color: var(--brand-kyy_color_brand_default, #4D5EFF);
      }
        &.follow-button--active:active {
        border-radius: 99px;
        background: var(--brand-kyy_color_brand_disabled, #C9CFFF);
        box-shadow: 0px 0.4px 1px 0px rgba(1, 48, 167, 0.50) inset;
        color: var(--brand-kyy_color_brand_default, #4D5EFF);
      }
      &.follow-button--inactive {
        background: linear-gradient(100deg, #A7C9FD 1.84%, #4D5EFF 100%);
        box-shadow: 0px 0.5px 1px 0px rgba(1, 48, 167, 0.50);
        color: var(--text-kyy_color_text_white, #FFF);
      }
      &.follow-button--inactive:hover{
        border-radius: 99px;
        background: linear-gradient(100deg, #A7C9FD 1.84%, #A7C9FD 100%);
        box-shadow: 0px 0.5px 1px 0px rgba(1, 48, 167, 0.50);
        color: var(--text-kyy_color_text_white, #FFF);
      }
        &.follow-button--inactive:active{
        border-radius: 99px;
background: linear-gradient(100deg, #4D5EFF 1.84%, #3E4CD1 100%);
box-shadow: 0px 0.5px 1px 0px rgba(1, 48, 167, 0.50);
        color: var(--text-kyy_color_text_white, #FFF);
      }
    }
  }

  .card-right {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    gap: 4px;
    flex: 1;
    min-width: 0;

    .name-section {
      display: flex;
      align-items: center;
      gap: 2px;
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: 22px;
      flex: 1;
      min-width: 0;

      .org-icon {
        font-size: 24px;
        flex-shrink: 0;
      }

      .name {
        display: block;
        align-items: center;
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 22px;
        color: var(--text-kyy_color_text_1, #1A2139);
        flex: 1;
        min-width: 0;
        max-width: 165px;
        .ellipsis();
      }
    }

    .info-item {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;
      align-self: stretch;
      overflow: hidden;
      color: var(--text-kyy_color_text_2, #516082);
      text-overflow: ellipsis;
      font-family: "PingFang SC";
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
    }

    .stats-section {
      display: flex;
      justify-content: space-between;
      align-self: stretch;
      color: var(--text-kyy_color_text_2, #516082);
      font-family: "PingFang SC";
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
    }
  }
}
</style>
