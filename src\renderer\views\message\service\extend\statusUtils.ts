import { createMessage, ignoreFriendApply } from "@renderer/api/contacts/api/common";
import { useContactsStore } from "@renderer/store/modules/contacts";
import {
  getApproveStatusApi,
  agreeApproveApi,
  rejectApproveApi,
  getApproveDetail<PERSON>pi,
  getStaffAppply<PERSON>pi,
  reviewApply<PERSON>pi,
  getPeerApplyInfoApi,
  getFriendAppliesApi,
  getSquareStatusApi,
  getBoothStatusApi,
  applyBoothStatusApi,
  removePrivateChat<PERSON>pi,
  rejectRemoveBussinessPeerApi,
  getBussinessPeerRemoveInfoApi,
  getNicheStatusApi,
} from "@renderer/api/im/api";
import { openRemind, openActivityRemind, checkAppUpdate, getImCardIds, getOpenid, removeActivityRemind, getCards, getStaff, getPlatform, getCurrentAccount, loadProfile } from '@renderer/utils/auth';
import { showDialog } from '@renderer/utils/DialogBV';
import { ActivityRefreshMap, IMRefreshMap } from "@renderer/views/message/common/constant";
import { combMsgInfo, cardData } from '@/views/identitycard/data';
import { getGroupInfo, getGroupSessionInfo } from '../request'
import { updatePrivateRelation, updateGroupRelation, onDeleteConversation, onRemoveGroupMembers, onAddGroupMembers, onGroupMemberApply, onGroupDisband, onQuitGroupSync, onGroupMembersUpdate, onGroupEnter, updateGroupNotice, updateAssistantsInfo } from '../relationUpdate';
import { loadAcountCards } from "../accountUtils";
import { activityRes, getActivityAboutByImV2, registers, unRegisters, getActivityInfoByIm } from "@/api/activity";
import { useMessageStore } from "../store";
import { useSessionSetting } from "../../tool/service/chatSetting";
import { msgEmit } from "../msgEmit";
import { useChatExtendStore } from "@renderer/views/message/service/extend";
import { MessagePlugin } from "tdesign-vue-next";
import dayjs from "dayjs";
import { logHandler } from "@renderer/log";
import { debounce } from "lodash";

import LynkerSDK from '@renderer/_jssdk';

const { ipcRenderer } = LynkerSDK;

/**
 * 请求组织新成员加入消息状态
 * @param msg
 * @returns
 * {
        "id": 291,
        "name": "foster",
        "from": "code",
        "teamId": "4c460c6e2007",
        "team": "1004测试组织",
        "logo": "",
        "telCode": 86,
        "phone": "***********",
        "staffName": "",
        // 状态 0待审核 1已同意 2已拒绝 3已撤销
        "status": 1
    }
 */
export const getAppTeamStatus = async (msg: MessageToSave) => {
  const res = await getStaffAppplyApi(msg.contentExtra.data.extend.apply_id);
  if (res.data?.code === 0) {
    msg.contentExtra.data.apiData = res.data.data;

    return res;
  }
  throw "load data error";
};

export const reviewAppTeamsApply = async (msg: MessageToSave, agree: boolean) => {
  try {
    const res = await reviewApplyApi({ ids: msg.contentExtra.data.extend.apply_id, agree: agree ? 1 : 0 });
    if (res.data?.code === 0) {
      getAppTeamStatus(msg);
    }
  } catch (error) {
    // FIXED:【IM】【回归】通知里操作出现错误提示后，没有弹出信息出来 https://www.tapd.cn/69781318/bugtrace/bugs/view?bug_id=1169781318001034242
    if (error.response?.data?.message) {
      MessagePlugin.error(error.response?.data?.message);
    }
  }
};

// --------------------------- 新联系人 ---------------------------

export const getAddressbookStatus = async (msg: MessageToSave) => {
  const data = msg.contentExtra.data;
  const myid = data.extend.applyCardId;
  const cardid = data.extend.cardId;
  const rela = myid?.startsWith("#") || cardid?.startsWith("#") ? "BUSINESS" : "FRIEND";

  if (!data.apiData) {
    const info = await combMsgInfo(cardid, myid, rela);
    commonUpdateMsgStatus(msg, info);
  }
  const applyId = data.extend.apply_id;
  const statusRes = await getPeerApplyInfoApi(applyId);
  /**
   * statusRes.data.data?.[applyId] < 4
   * FIXED: 【ID1024938】【PC消息 】新联系人通知：解除关系对该通知无影响
   */
  if (statusRes?.data?.code === 0) {
    if (data.imApprove !== statusRes.data.data?.[applyId]) {
      // key 申请id //0待通过;1通过;2忽略;3过期; 4已解除
      msg.contentExtra.data.imApprove = statusRes.data.data?.[applyId];
    }
    return statusRes;
  }
  throw "load data error";
};
// 新联系人 6101 新接口
export const getAppliesDetail = async (msg: MessageToSave) => {
  const data = msg.contentExtra.data;

  if (!data.apiData) {
    const myid = data.extend?.applyCardId || data.cardIdApply;
    const cardid = data.extend?.cardId || data.cardIdSelf;
    const rela = myid?.startsWith("#") || cardid?.startsWith("#") ? "BUSINESS" : "FRIEND";
    const info = await combMsgInfo(cardid, myid, rela);
    commonUpdateMsgStatus(msg, info);
  }
  const applyId = data.extend?.apply_id || data.applyID;
  const statusRes = await getFriendAppliesApi({ids: [applyId]});
  console.log('===>statusRes', statusRes);
  
  /**
   * statusRes.data.data?.[applyId] < 4
   * FIXED: 【ID1024938】【PC消息 】新联系人通知：解除关系对该通知无影响
   */
  if (statusRes?.data?.code === 0) {
    const applies = statusRes.data.data?.applies[0]
    if(!applies) return;
    if (data.imApprove !== applies.status) {
      msg.contentExtra.data = applies
      msg.contentExtra.data.imApprove = applies.status
      const peerInfo = data.apiData.attachment?.member?.find((item) => item.cardId === applies.cardIdApply) as ConversationMemberToSave;
      msg.contentExtra.data.peerInfo = peerInfo;
    }
    return statusRes;
  }
  throw "load data error";
};
export const agreeNewFrindApply = async (msg: MessageToSave) => {
  const data = msg.contentExtra.data;
  const applyCard =  data.cardIdApply || data.extend?.applyCardId;
  const myCard =  data.cardIdSelf || data.extend?.cardId;
  const rela = data.pairType || ((applyCard?.startsWith("#") || myCard?.startsWith("#")) ? "BUSINESS" : "FRIEND");

  const relationInfo = await combMsgInfo(applyCard, myCard, rela);
  createMessage(relationInfo).then((res) => {
    console.log('====>res', res);
    if (res.status === 200) {
      msg.contentExtra.data.imApprove = 1;
      msg.contentExtra.data.status = 'FRIEND_APPLY_STATUS_ACCEPTED';
      useContactsStore().clearNotice();
    }
  }).catch((err) => {
    MessagePlugin.error(
      err.response.data?.message
    );
  });

};

export const ignoreNewFrindApply = async (msg: MessageToSave) => {
  const data = msg.contentExtra.data;
    const applyId = data.id || data?.extend?.apply_id

  const res = await ignoreFriendApply(applyId);
  if (res?.data?.code === 0) {
    msg.contentExtra.data.imApprove = 2;
    msg.contentExtra.data.status = 'FRIEND_APPLY_STATUS_IGNORED';
    useContactsStore().clearNotice();
  }
};

// --------------------------- 解除商务关系 ---------------------------

// 获取解除商务关系状态
export const getRelationBusinessRemoveStatus = async (msg: MessageToSave) => {
  const data = msg.contentExtra.data;
  // 申请人身份卡
  const applyCard = data.extend?.applicant_card_id;
  // 要移除的人身份卡
  const toRemoveCard = data.extend?.friend_card_id;

  if (!data.apiData) {
    const info = await combMsgInfo(toRemoveCard, applyCard, "BUSINESS");
    commonUpdateMsgStatus(msg, info);
  }
  const applyId = data.extend?.apply_id || data.applyId;
  const statusRes = await getBussinessPeerRemoveInfoApi(applyId);
  // !data.imApprove !== statusRes.data.data?.[applyId]
  if (statusRes?.data?.code === 0) {
    if (!data.imApprove) {
      // key  1 待审批、2 已同意、3 已拒绝
      msg.contentExtra.data.imApprove = statusRes.data.data?.[applyId];
    }
    return statusRes;
  }
  throw "load data error";
};

export const agreeBusinessRemove = async (msg: MessageToSave) => {
  const data = msg.contentExtra.data;
  // 申请人身份卡
  const applyCard = data.extend.applicant_card_id;
  // 要移除的人身份卡
  const toRemoveCard = data.extend.friend_card_id;

  const res = await removePrivateChatApi(applyCard, toRemoveCard, data.extend.is_clear === 2);
  if (res.status === 200) {
    msg.contentExtra.data.imApprove = 2;
  }
};

export const rejectBusinessRemove = async (msg: MessageToSave) => {
  const applyId = msg.contentExtra.data?.extend?.apply_id;
  const res = await rejectRemoveBussinessPeerApi(applyId);
  if (res.data?.code === 0) {
    msg.contentExtra.data.imApprove = 3;
  }
};

// 商务关系解除申请被拒，小秘书消息
export const getSecretary_6004_status = async (msg: MessageToSave) => {
  const data = msg.contentExtra.data;
  const operator = msg.contentExtra.data?.extend?.operator_card_id;
  if (!data.apiData) {
    const info = await cardData(operator);
    commonUpdateMsgStatus(msg, info);
  }
};

// 广场消息更新
export const getAppSquareStatus = async (msg: MessageToSave) => {
  if (!msg.contentExtra.data.extend.violation_id) {
    return;
  }

  // 申诉过期 或者 禁言结束，无法申诉
  if (msg.contentExtra.data.extend.silenceEnd || msg.contentExtra.data.extend.expired) {
    return;
  }

  const res = await getSquareStatusApi(msg.contentExtra.data.extend.violation_id);
  if (res.status === 200) {
    msg.contentExtra.data.extend.appealed = res.data?.appealed;
    msg.contentExtra.data.extend.expired = res.data?.expired;
    msg.contentExtra.data.extend.silenceEnd = res.data?.silenceEnd;
    msg.contentExtra.data.extend.appealable = res.data?.appealable;

    return res;
  }
  throw "load data error";
};

export const getAppRingkolCycleStatus = async (msg: MessageToSave) => {
  const data = msg.contentExtra.data;
  const res = await getBoothStatusApi(data?.extend?.booth_id);
  if (res.status === 200) {
    if (res.data?.status !== data.apiData?.status) {
      msg.contentExtra.data.apiData = res.data;
    }
    return res;
  }
  throw "load data error";
};

export const applyRingkolCycleStatus = async (msg: MessageToSave, status: "PASS" | "REJECT") => {
  const data = msg.contentExtra.data;
  const res = await getBoothStatusApi(data?.extend?.booth_id);
  if (res.data?.status === "WAIT") {
    const applyRes = await applyBoothStatusApi(data?.extend?.booth_id, status);
    if (applyRes.status === 200) {
      msg.contentExtra.data.apiData = { status, boothId: data?.extend?.booth_id };
    }
  } else if (res.status === 200) {
    msg.contentExtra.data.apiData = res.data;
  }
};

export const getAppCloudDiskStatus = (msg: MessageToSave) => {};

// --------------------------- 审批 ---------------------------

const isValidateApproveMsg = (msg: MessageToSave) =>
  msg.contentExtra?.contentType === "APP_APPROVAL" &&
  msg.contentExtra?.data?.extend &&
  Object.keys(msg.contentExtra?.data?.extend).length > 1;

// 请求审批消息的审批状态
export const getAppApprovalStatus = async (msg: MessageToSave, changeDetail = false) => {
  if (!isValidateApproveMsg(msg)) {
    console.log(false, "审批消息内容为空", msg);
    return;
  }

  const extend = msg.contentExtra?.data?.extend;
  // -1：不可审批，0：待审批，1：已同意，2：已被他人处理，3：已拒绝，4：已转交，5：已被转交，6：已退回，7：已加签，8：发起人已撤销
  const res = await getApproveStatusApi(extend);
  if (res.data?.code === 0) {
    if (res.data?.code === 0 && res.data?.data?.status !== msg.contentExtra.data.apiData?.status) {
      updateMsgApproveStatus(msg, res.data.data, changeDetail);
    }
    return res;
  }
  throw "load data error";
};
// 请求通过审批
export const reqApproveAgree = async (msg: MessageToSave) => {
  if (!isValidateApproveMsg(msg)) {
    console.log(false, "审批消息内容为空", msg);
    return;
  }

  const extend = msg.contentExtra?.data?.extend;
  const statusRes = await getApproveStatusApi(extend);
  if (statusRes.data?.code === 0 && statusRes.data?.data?.status !== 0) {
    updateMsgApproveStatus(msg, statusRes.data.data, true);
    return;
  }

  const res = await agreeApproveApi(extend, {
    approve_id: extend.approval_id,
    free_form: [],
    form_content_modify: false,
    handle_value: null,
  });

  if (res.data?.code === 0) {
    ipcRenderer.invoke("set-unread-post-approval");
    updateMsgApproveStatus(msg, { status: 1, must_opinion: 0 }, true);
  }
};
// 请求拒绝审批
export const reqApproveReject = async (msg: MessageToSave) => {
  if (!isValidateApproveMsg(msg)) {
    console.log(false, "审批消息内容为空", msg);
    return;
  }

  const extend = msg.contentExtra?.data?.extend;
  const statusRes = await getApproveStatusApi(extend);
  if (statusRes.data?.code === 0 && statusRes.data?.data?.status !== 0) {
    updateMsgApproveStatus(msg, statusRes.data.data, true);
    return;
  }

  const res = await rejectApproveApi(extend, {
    approve_id: extend.approval_id,
    free_form: [],
    form_content_modify: false,
    handle_value: null,
  });

  if (res.data?.code === 0) {
    ipcRenderer.invoke("set-unread-post-approval");
    updateMsgApproveStatus(msg, { status: 3, must_opinion: 0 }, true);
  }
};

// 请求审批分享消息的审批状态
export const getAppShareApprovalStatus = async (msg: MessageToSave) => {
  const data = msg.contentExtra?.data;
  // !详情接口可以不用传 team_id 和 project_id
  const statusRes = await getApproveStatusApi({
    approval_id: data?.approveId,
    team_id: data?.teamId,
    project_id: data?.projectId,
    log_id: getApproveShareMsgLogId(msg),
  });

  if (statusRes.data?.code === 0 && statusRes.data?.data?.status !== msg.contentExtra.data.apiData?.status) {
    updateMsgApproveStatus(msg, statusRes.data.data, true);
  }
};

// 请求通过审批
export const reqShareApproveAgree = async (msg: MessageToSave) => {
  const data = msg.contentExtra?.data;
  // !详情接口可以不用传 team_id 和 project_id
  const info = {
    approval_id: data?.approveId,
    team_id: data?.teamId,
    project_id: data?.projectId,
    log_id: getApproveShareMsgLogId(msg),
  };
  const statusRes = await getApproveStatusApi(info);

  // !先检测是否可以同意
  if (statusRes.data?.data?.status === 0) {
    const res = await agreeApproveApi(info, {
      approve_id: data?.approveId,
      free_form: [],
      form_content_modify: false,
      handle_value: null,
    });
    res.data?.code === 0 && getAppShareApprovalStatus(msg);
  } else if (statusRes.data?.code === 0) {
    updateMsgApproveStatus(msg, statusRes.data.data, true);
  }
};
// 请求拒绝审批
export const reqShareApproveReject = async (msg: MessageToSave) => {
  const data = msg.contentExtra?.data;
  // !详情接口可以不用传 team_id 和 project_id
  const info = {
    approval_id: data?.approveId,
    team_id: data?.teamId,
    project_id: data?.projectId,
    log_id: getApproveShareMsgLogId(msg),
  };
  // !先检测是否可以同意
  const statusRes = await getApproveStatusApi(info);
  if (statusRes.data?.data?.status === 0) {
    const res = await rejectApproveApi(info, {
      approve_id: data?.approveId,
      free_form: [],
      form_content_modify: false,
      handle_value: null,
    });
    res.data?.code === 0 && getAppShareApprovalStatus(msg);
  } else if (statusRes.data?.code === 0) {
    updateMsgApproveStatus(msg, statusRes.data.data, true);
  }
};

export const isShowHelperApproveDetail = async (msg: MessageToSave) => {
  const extend = msg.contentExtra?.data?.extend;
  const res = await getApproveDetailApi(extend);
  return res.status === 200 && res.data?.code !== -1;
};

export const isShowShareApproveDetail = async (msg: MessageToSave) => {
  const extend = {
    approval_id: msg.contentExtra?.data?.approveId,
    approvalId: msg.contentExtra?.data?.approveId,
    teamId: msg.contentExtra?.data?.teamId,
    projectId: msg.contentExtra?.data?.projectId || 0,
  };
  const res = await getApproveDetailApi(extend);
  return res.status === 200 && res.data?.code !== -1;
};

const getApproveShareMsgLogId = (msg: MessageToSave) => {
  const data = msg.contentExtra?.data;
  let logId = data?.nodeId ?? 0;
  const myCards = getImCardIds();
  const item = data?.approving_log?.find((item) => myCards.includes(`$${item?.staff_id}`));
  if (item) {
    logId = item?.log_id || 0;
  }
  return logId;
};

// -------------------------- 商机助手消息状态 ----------------------------------
export const getNicheStatus = async (msg: MessageToSave) => {
  const data = msg.contentExtra?.data;
  const res = await getNicheStatusApi(data?.extend);
  // process_state 审核状态（0：待审核，1：已通过，2：已拒绝）
  if (res.data?.code === 0) {
    if (data?.apiData?.process_state !== res.data?.data?.process_state) {
      commonUpdateMsgStatus(msg, res.data.data);
    }
    return res;
  }
  MessagePlugin.error(res?.message || "数据异常");
  throw "load data error";
};

// 更新消息状态
const updateMsgApproveStatus = (
  msg: MessageToSave,
  data: { status: number; must_opinion: number },
  changeDetail = false,
) => {
  if (changeDetail) useChatExtendStore().approvalRefreshKey += 1;
  commonUpdateMsgStatus(msg, data);
};

// -------------------------- 活动助手消息状态 ----------------------------------
export const getActivityInfo = async (msg: MessageToSave) => {
  const data = msg.contentExtra?.data;
  const activityId = data?.id;
  try {
    const res = await getActivityInfoByIm(activityId);

    if (res.status === 200) {
      return { reason: "RIGHT", res };
    }
  } catch (error) {
    // console.log(error?.response?.data)
    if (error?.response?.data?.metadata?.deletedAt) {
      commonUpdateMsgStatus(msg, { ...msg.contentExtra?.data?.apiData, isDeleted: true });
      return { reason: "NOT_FOUND", res: error };
    } else {
      return { reason: "ERROR", res: error };
    }
  }
};
export const getActivityStatus = async (msg: MessageToSave, contentType?: string) => {
  const data = msg.contentExtra?.data;
  const activityId = data?.id;
  const params = {};
  if (data?.receiver?.card_id) {
    params["rep.cardId"] = data.receiver?.card_id;
    params["rep.teamId"] = data.receiver?.team_id;
  } else {
    params["rep.openid"] = data.receiver?.open_id || data.receiver?.openid;
  }
  if ('activity_card' === contentType) {
    const item = useMessageStore().chatingSessionMembers?.get(useMessageStore().chatingSession.myCardId);
    console.error('item', item);
    if (item?.teamId) {
      params["rep.cardId"] = item?.cardId
      params["rep.teamId"] = item?.teamId;
    } else {
      params["rep.openid"] = item?.openId
    }
  }
  //  删除的活动标记状态后续不在发起请求更新状态
  if (msg.contentExtra?.data?.apiData?.isDeleted) return;
  try {
    const res = await getActivityAboutByImV2({
      activityId,
      ...params,
    });

    const items = res.data.items?.find(
      (item) => (item.rep.cardId || item.rep.openid) === (data?.receiver?.card_id || data?.receiver?.openid),
    ) ?? {
      role: "",
      reply: "",
    };
    if (res.status === 200) {
      _tranActivityInfo(msg, res?.data?.data?.activity, contentType);
      if (data?.apiData?.reply !== items?.reply) {
        const _data = { ...items, activity: res?.data?.data?.activity };
        commonUpdateMsgStatus(msg, _data);
      } else {
        const _data = { ...items, activity: res?.data?.data?.activity };
        commonUpdateMsgStatus(msg, _data);
      }
      return res;
    }
  } catch (error) {
    // if (error?.response?.data?.metadata?.deletedAt) {
      if (error?.data?.code === 5010) {
      commonUpdateMsgStatus(msg, { ...msg.contentExtra?.data?.apiData, isDeleted: true });
    }
    throw error;
  }
};
const _tranActivityInfo = (msg, info, contentType?: string) => {
  if (["activity", "activity_card"].includes(contentType)) {
    msg.contentExtra.data = { ...msg?.contentExtra?.data, ...info };
  } else {
    const _tranInfo = {
      category_id: info.categoryId,
      category_title: info.categoryTitle,
      cover: info.assetUrl,
      location: info.location,
      title: info.subject,
      duration: {
        start_time: info?.duration?.startTime,
        end_time: info?.duration?.endTime,
      },
      register: info?.register?.startTime
        ? {
            start_time: info?.register?.startTime,
            end_time: info?.register?.endTime,
          }
        : {},
    };
    msg.contentExtra.data = { ...msg?.contentExtra?.data, ..._tranInfo };
  }
};
export const activityResponse = async (msg: MessageToSave, replyCode, replyValue) => {
  const data = msg.contentExtra?.data;
  console.log(msg);
  const activityId = data?.id;
  const params = {
    activityId,
    reply: replyCode,
  };
  if (data.receiver.card_id) {
    params.cardId = data.receiver.card_id;
    params.teamId = data.receiver.team_id;
  }
  const res = await activityRes(activityId, params);

  if (res.status === 200 && data?.apiData?.reply !== replyValue) {
    commonUpdateMsgStatus(msg, { ...msg.contentExtra?.data?.apiData, reply: replyValue });
    return true;
  }
  return false;
};
export const activityRegisters = async (msg: MessageToSave, hasRegisters) => {
  const data = msg.contentExtra?.data;
  const id = data?.id;
  const params = {
    id,
  };
  if (data.receiver.card_id) {
    params.cardId = data.receiver.card_id;
    params.teamId = data.receiver.team_id;
  }
  const res = hasRegisters ? await unRegisters(id, params) : await registers(params);

  const role = hasRegisters ? "None" : "Registered";

  if (res.status === 200 && data?.apiData?.role !== role) {
    commonUpdateMsgStatus(msg, { ...msg.contentExtra?.data?.apiData, role });
  }
};

export const commonUpdateMsgStatus = <T>(msg: MessageToSave, data: T, key = "apiData") => {
  msg.contentExtra.data[key] = data;
};

// ----- 助手消息
// APP_NOTIFY // 全部提醒

// APP_KNOW // 知行提醒 场景: 0:提醒 1:清单
// APP_SQUARE // 广场提醒
// APP_CLOUD_DISK // 云盘提醒
// APP_APPROVAL // 审批
// APP_ADDRESS_BOOK // 通讯录
// APP_TEAMS // 组织
// APP_SECRETARY // 小秘书
// APP_WORK // 工作通知
// APP_BUSINESS 商务关系 删除联系人申请

// ----- 系统通知
// APP_GROUPS // 群组 场景: 0:新增 1:更新 2:解散 5:群主变更 6:入群申请
// APP_MEMBERS // 群成员 场景: 0:新增 1:更新 2:删除 3:批量删 4:批量加 7:自己退群
// APP_PAIRS // 单聊 场景: 0:新增 1:更新 2:删除 3:批量删
// APP_ACCOUNT // 账号 2:删除 8:用户注销下线 9:移除身份卡 10:添加身份卡 11:修改密码下线 12: 抢登下线 15:账号违禁下线
// APP_CONFIGS // 用户配置: 0:新增 1:更新
// APP_REFRESH // 添加联系人同步
// APP_ADDRESS_BOOK // 通讯录(计数通知) 1:更新
// APP_ORDER // 订单：8016：订单状态更新 8017：咨询单状态更新
// APP_PUSH_NOTIFY // 30：反诈推送同步
// APP_PAIR_GROUP // 1 消息分组变更通知
export const onNotifyMessages = (msg: MessageToSave) => {
  console.log("onNotifyMessages", msg);
  switch (msg.contentExtra?.contentType) {
    case "APP_ADDRESS_BOOK":
      onNotifyAddressBook(msg);
      break;
    case "APP_CARD":
      onAppCardChange(msg);
      break;
    case "APP_GROUPS":
      onGroupUpdate(msg);
      break;
    case "APP_MEMBERS":
      onGroupMemberUpdate(msg);
      break;
    case "APP_PAIRS":
      onPrivateSessionUpdate(msg);
      break;
    case "APP_ASSISTANTS":
      onAssistantsSessionUpdate(msg);
      break;
    case "APP_ACCOUNT":
      onAccountUpdate(msg);
      break;
    case "APP_CONFIGS":
      onLoadConfigsUpdate(msg);
      break;
    case "APP_NOTIFY":
      onCommonNotify(msg);
      break;
    case "APP_SQUARE":
      onSquareUpdate(msg);
      break;
    case "APP_KNOW":
      onAppKnowNotify(msg);
      break;
    case "APP_ACTIVITY":
      onAppActivityNotify(msg);
      break;
    case "APP_ACTIVITY_NOTICE":
      onAppActivityNoticeNotify(msg);
      break;
    case "APP_APPROVAL":
      onAppArpprovalNotify(msg);
      break;
    case "APP_TEAMS":
      onAppTeams(msg);
      break;
    case "APP_REFRESH":
      onAppRefreshNotify(msg);
      break;
    case "APP_ORDER":
      onAppOrderNotify(msg);
      break;
    case "APP_BBS_NOTICE":
      onAppForumNotify(msg);
      break;
    case "APP_PUSH_NOTIFY":
      onAppPushNotify(msg);
      break;
    case "APP_PAIR_GROUP":
      onAppPairGroup(msg);
      break;
    case 'server_group_card_update': onGroupMemberCardUpdate(msg); break;
    case 'APP_STORE_ASSISTANT_NOTIFY': useMessageStore().onShopMsgUpdate(msg); break;
    default: '';
  }
};

const onLoadConfigsUpdate = (msg: MessageToSave) => {
  loadAcountCards();
  // const data = msg.contentExtra.data;
  // if (data?.typ === 'APP_CHAT_SCENE') {
  //     useSessionSetting().getSceneTools();
  // } else {
  //     loadAcountCards();
  // }
};

const onPrivateSessionUpdate = (msg: MessageToSave) => {
  console.log("=====>onPrivateSessionUpdate", msg);
  const scene = msg.contentExtra.scene;
  const ids = [];
  getStaff()?.forEach((val) => ids.push(val.uuid));
  getCards()?.forEach((val) => ids.push(val.uuid));
  getPlatform()?.forEach((val) => ids.push(val.uuid));

  ids.push(getOpenid());
  // "data": { "main": "操作者", "peer": "对方" }
  const data = msg.contentExtra.data;
  let main = "",
    peer = "";
  if (ids.includes(data?.main)) {
    // 发现我通过添加好友后 main是对方，peer是自己？？？
    main = data?.main;
    peer = data?.peer;
  } else {
    main = data?.peer;
    peer = data?.main;
  }
  if (scene === 0) {
    updatePrivateRelation(main, peer);
  } else if (scene === 1) {
    // 置顶、免打扰更新
    updatePrivateRelation(main, peer);
  } else if (scene === 2) {
    if (ids.includes(data?.main) && !data?.history) {
      onDeleteConversation({ main, peer, deleHistory: true });
      // 清除本地数据
    } else {
      updatePrivateRelation(main, peer);
    }
  } else if (scene === 3) {
    loadAcountCards();
  }
};

// 单聊身份卡备注变更
const onAppCardChange = (msg) => {
  const scene = msg.contentExtra.scene;
  if (scene === 6301) {
    const main = msg.contentExtra.data.id
    const peer = msg.contentExtra.data.card_ids[0]
    const sessions = useMessageStore().sessionList.filter((item) => item.targetCardId === peer)
    const ids = sessions.map((item) => item.myCardId)
    ids.forEach(id => {
      updatePrivateRelation(id,peer)
    });
  }

}
const onAssistantsSessionUpdate = (msg: MessageToSave) => {
  console.log("=====>onAssistantsSessionUpdate", msg);
  const scene = msg.contentExtra.scene;
  if (scene === 1) {
    // 置顶、免打扰更新
    updateAssistantsInfo(msg.contentExtra.data?.id)
  }
}
const onNotifyAddressBook = (msg: MessageToSave) => {
  if (msg.contentExtra.scene === 1) {
    useContactsStore().setNewContactsNum([]);
  } else if ([6101,6001].includes(msg.contentExtra.scene)) {
    console.log("通讯录更新", msg);
    useContactsStore().setNewContactsNum([msg.contentExtra.seq]);
  }
};

const onAccountUpdate = (msg: MessageToSave) => {
  const data = msg.contentExtra?.data;
  const scene = msg.contentExtra?.scene;
  // APP_ACCOUNT // 账号 2:删除 8:用户注销下线 9:移除身份卡 10:添加身份卡 11:修改密码下线 12: 抢登下线
  if (!msg.isOffLineMessage && msg.contentExtra.scene === 8) {
    const currentAccountTemp = getCurrentAccount();
    console.error("====>im-onAccountUpdate", msg);
    logHandler({
      name: "账号退出登录scene8, dialogLogout",
      info: `${JSON.stringify(msg)},Account:${JSON.stringify(currentAccountTemp)}`,
      desc: "onAccountUpdate->dialogLogout",
    });
    showDialog("dialogLogout");
  } else if (!msg.isOffLineMessage && scene === 11) {
    const currentAccountTemp = getCurrentAccount();
    logHandler({
      name: "账号退出密码修改重新登录scene11",
      info: `${JSON.stringify(msg)},Account:${JSON.stringify(currentAccountTemp)}`,
      desc: "onAccountUpdate->dialogModifyPw",
    });
    if (!data?.platform) {
      showDialog("dialogModifyPw");
    } else if (data?.platform !== "PC") {
      // 同端不退出
      showDialog("dialogModifyPw");
    }
  } else if (!msg.isOffLineMessage && msg.contentExtra.scene === 12 && msg.contentExtra?.data?.platform !== "PC") {
    const currentAccountTemp = getCurrentAccount();
    logHandler({
      name: "账号退出踢出scene12",
      info: `${JSON.stringify(msg)},Account:${JSON.stringify(currentAccountTemp)}`,
      desc: "onAccountUpdate->dialogKickOut",
    });
    showDialog("dialogKickOut");
  } else if (!msg.isOffLineMessage && msg.contentExtra.scene === 15) {
    showDialog("dialogViolationOut");
  } else if (!msg.isOffLineMessage && msg.contentExtra.scene === 2) {
    loadAcountCards();
  } else if (!msg.isOffLineMessage && msg.contentExtra.scene === 9) {
    loadAcountCards();
  } else if (!msg.isOffLineMessage && msg.contentExtra.scene === 10) {
    loadAcountCards();
  } else if (msg.contentExtra.scene === 13) {
    loadAcountCards();
  }
};

const onGroupMemberCardUpdate = (msg: MessageToSave) => {
  const contentExtra = msg.contentExtra;
  if (contentExtra?.senderId === getOpenid()) {
    // 自己其他端发来的更新
    changeProfiles();
  }
};

const changeProfiles = debounce(() => {
  loadProfile()
},5*1000)

const onAppTeams = (msg: MessageToSave) => {
  if (!msg.isOffLineMessage && msg.contentExtra.scene === 5006) {
    loadAcountCards();
  }
};

const onGroupUpdate = async (msg: MessageToSave) => {
  console.log('====>msg', msg);
  // 更新会话的场景工具红点，sceneRedDot 26红点提示，27取消红点
  // 机制修改为一天群第一个人点击触发红点，只推送一次，后面其他用户点击可能不会触发27取消红点了，在切换tool天气时调用去除红点
  if (msg.contentExtra?.scene === 26 || msg.contentExtra?.scene === 27) {
      msg.targetId = msg.contentExtra?.data?.group;
      useMessageStore().onConversationSceneChange(msg, 'weather', msg.contentExtra?.scene === 26);
      return;
  }
  const groupId = msg.contentExtra?.data?.id;
  if (!groupId) return;
  useSessionSetting().reloadByAppMembersMsg(msg);

  if (msg.contentExtra?.scene === 0) { // 新增群
      // 新增群通过newMessage来触发。因为有多个创建会话入口，新消息没有会话会创建会话。先后顺序不一致会导致摘要和红点不准确。注释掉这个新增群。
      // await loadAcountCards();
      // onGroupEnter(groupId);

  } else if (msg.contentExtra?.scene === 1) { // 更新群
      updateGroupRelation(groupId);
      msgEmit.emit('group-change', { groupId });

  } else if (msg.contentExtra?.scene === 2) { // 群解散
      onGroupDisband(groupId, msg.senderUserId === getOpenid());

  } else if (msg.contentExtra?.scene === 6) { // 入群申请
      onGroupMemberApply(groupId);
  } else if (msg.contentExtra?.scene === 18) { // 群公告
      updateGroupNotice(groupId);
  }
};

// 群成员更新通知
// APP_MEMBERS 群成员 场景: 0:新增 1:更新 2:删除 3:批量删 4:批量加 7:自己退群
const onGroupMemberUpdate = async (msg: MessageToSave) => {
  const { data, scene } = msg.contentExtra;
  if (scene === 0) {
      // 我扫码进入
      await loadAcountCards();
      onAddGroupMembers({...data, senderUserId: msg.senderUserId});

  } else if (scene === 4) {
      // 我拉入进去只需要更新
      // if (msg.sendID === getOpenid()) {
      //     console.log('====>data.id', data.id);
      //     await onGroupMembersUpdate(data.id);
      //     getGroupInfo(data.id);
      //     return;
      // }
      // 别人拉人
      await loadAcountCards();
      onAddGroupMembers({...data, senderUserId: msg.senderUserId});

  } else if (scene === 1) {
      // 群成员设置修改， 包含置顶
      await getGroupSessionInfo(data.id);
      useMessageStore().sortSessions('onGroupMemberUpdate');

  } else if (scene === 2) {
      // 多个身份在同一个群的时候，某个身份退群后，会收到该消息，同时会收到 scene 7 的消息。
      // 此退群操作由客户端在7中进行处理，2则不做处理。

      // 当前群聊有人退群更新群成员
      if (useMessageStore().chatingSession?.targetId === data.id) {
        await getGroupSessionInfo(data.id);
      }
  } else if (scene === 3) {
      onRemoveGroupMembers(data);

  } else if (scene === 7) {
      onQuitGroupSync(data);
  }
  // 更新设置弹窗的内容
   useSessionSetting().reloadByAppMembersMsg(msg);
};

const onAppOrderNotify = (msg: MessageToSave) => {
  const chatingMessages = useMessageStore().chatingMessages;
  const {
    scene,
    data: { consult_order_sn, order_sn },
  } = msg.contentExtra;
  let item;
  console.log('onAppOrderNotify', scene)
  if (scene === 8016) {
    item = chatingMessages.find((v) => v?.msg?.contentExtra?.data?.data?.info?.sn === order_sn);
  }
  if (scene === 8017) {
    item = chatingMessages.find((v) => {
      console.log('onAppOrderNotify type', v?.msg?.contentExtra?.data?.type, v?.msg?.contentExtra?.data?.data)
      if (v?.msg?.contentExtra?.data?.type === "square") {
        return v?.msg?.contentExtra?.data?.data?.consultOrderDetail?.sn === consult_order_sn;
      }
      if (v?.msg?.contentExtra?.data?.type === "album") {
        return v?.msg?.contentExtra?.data?.data?.consult_data?.sn === consult_order_sn;
      }
      if (v?.msg?.contentExtra?.data?.type === "exclusive_name") {
        return v?.msg?.contentExtra?.data?.data?.consult?.sn === consult_order_sn;
      }
    });
  }
  if (item) {
    useMessageStore().refreshMessageByNeedUpdateMessageUId({
      messageUId: item?.msg?.messageUId,
      scene,
    });
  }
};
const onAppRefreshNotify = (msg: MessageToSave) => {
  const {
    scene,
    data: {
      extend: { uuid },
    },
  } = msg.contentExtra;
  if ([19036,51036, 5066, 16036, 14036].includes(scene)) {
    const chatingMessages = useMessageStore().chatingMessages;
    const item = chatingMessages.find((v) => v?.msg?.contentExtra?.data?.extend?.uuid === uuid);
    if (item) {
      useMessageStore().refreshMessageByNeedUpdateMessageUId({
        messageUId: item?.msg?.messageUId,
        scene,
      });
    }
  }

  if (scene === 10013) {
    ipcRenderer.invoke("update-niche-reddot");
  }

  if (IMRefreshMap[scene]) {
    console.log('kakaxi', IMRefreshMap[scene],  msg.contentExtra?.data)
    ipcRenderer.invoke('IM-refresh', {
      type: IMRefreshMap[scene],
      data: msg.contentExtra?.data
    });
  }
};
const onAppForumNotify = (msg: MessageToSave) => {
  // useForumStore().getUnreadStats();
  const { scene } = msg.contentExtra;
  if (scene === 23001) {
    ipcRenderer.invoke("forum-unread-post");
  }
};

const onAppPushNotify = (msg: MessageToSave) => {
  const chatingMessages = useMessageStore().chatingMessages;
  const {
    scene,
    data: { pushID },
  } = msg.contentExtra;
  if (scene === 31) {
    const item = chatingMessages.find((v) => v?.msg?.contentExtra?.data?.content?.ex?.id === pushID);
    if (item) {
      useMessageStore().refreshMessageByNeedUpdateMessageUId({
        messageUId: item?.msg?.messageUId,
        scene,
      });
    }
  }
};
const onAppPairGroup = (msg: MessageToSave) => {
  const { scene, data } = msg.contentExtra;
  if (scene === 1) {
    useMessageStore().getPairGroup();
  }
};
const onSquareUpdate = async (msg: MessageToSave) => {
  const { data, scene } = msg.contentExtra;
  switch (scene) {
    case 7301:
      // 好友圈新动态红点
      await ipcRenderer.invoke("set-unread-post", true);
      break;

    case 7302:
      // 新消息通知（评论/点赞/关注）
      await ipcRenderer.invoke("square-notify", data?.extend?.news_stats);
      break;

    case 7303:
      // 动态审核状态变更推送
      await ipcRenderer.invoke("square-post-status", data?.extend);
      break;

    default:
      break;
  }
};

const onAppKnowNotify = (msg: MessageToSave) => {
  const { data, scene } = msg.contentExtra;
  const sentTime = msg?.sentTime;
  console.log("onAppKnowNotify通知：", msg);
  if (scene === 0) {
    // 0 提醒
    const isSentTime = dayjs().isBefore(dayjs(sentTime + 1000 * 30).valueOf());
    if (!isSentTime) return;
    openRemind(data, scene);
    ipcRenderer.invoke("update-zhixing-count");
  } else if (scene === 1) {
    // 1 清单
    openRemind(data, scene);
  } else if (scene === 17) {
    // 17 数据变更通知，提醒弹窗的状态变更，待处理，等有缘人
    //   由于提醒需要展示多个, 所以在此通知时不要关闭窗口,否则多个提醒就不会展示完了
    //   ipcRenderer.invoke('close-remind-dialog');
  } else if (scene === 19) {
    // 日程变更通知
    ipcRenderer.invoke("update-zhixing-count");
    ipcRenderer.invoke("send-refresh-schedule");
  } else if (scene === 20) {
    // 隐私锁变更-修改密码时操作
    ipcRenderer.invoke("send-refresh-schedule");
  } else if (scene === 21) {
    // 稍后处理变更
    // ipcRenderer.invoke('send-refresh-schedule');
    ipcRenderer.invoke("update-zhixing-internal-count");
    ipcRenderer.invoke("update-zhixing-count");
  } else if (scene === 22) {
    // 笔记变更消息
    // ipcRenderer.invoke('send-refresh-schedule');
    ["POCKET"].includes(data?.platform) && ipcRenderer.invoke("note-update-message");
  } else if (scene === 23) {
    // 笔记本变更消息
    ipcRenderer.invoke("send-refresh-schedule");
  } else if (scene === 24) {
    // 清单变更消息
  } else if (scene === 25) {
    // 笔记本标签变更消息
    ipcRenderer.invoke("send-notebook-label-change-message");
  } else if (scene === 28) {
    // 笔记本标签变更消息
    removeActivityRemind({...data, openid: data.notice_id}, scene);
  }
};

// 20001/20021会同时触发新增消息，其他场景走20022, 20023, 20024, 20025, 20000, 20006, 20007, 20008
const refreshActivityAssistant = (msg: MessageToSave) => {
  const { data, scene } = msg.contentExtra;
  const messageStore = useMessageStore();
  // 更新消息会话
  messageStore.chatingMessages?.forEach((v) => {
    if (
      ["activity", "activity_card", "APP_ACTIVITY"].includes(v?.msg?.contentExtra?.contentType) &&
      data?.id === v?.msg?.contentExtra?.data?.id
    ) {
      getActivityStatus(v.msg, v?.msg?.contentExtra?.contentType);
    }
  });
  // 更新消息列表
  messageStore.sessionList?.forEach((s) => {
    const v = s.latestMessage;
    if (
      ["activity", "activity_card", "APP_ACTIVITY"].includes(v?.contentExtra?.contentType) &&
      data?.id === v?.contentExtra?.data?.id
    ) {
      getActivityStatus(v, v?.contentExtra?.contentType);
    }
  });
};

const onAppActivityNotify = (msg: MessageToSave) => {
    const { data, scene } = msg.contentExtra;
    console.log(data,scene,'活动消息--------------------------------------活动')
    if (!msg.isOffLineMessage) {
        if ([20000, 20006, 20007, 20008,20025].includes(scene)) {
            // if ([20021, 20022, 20023, 20024, 20025].includes(scene)) return;
            refreshActivityAssistant(msg);
            // 关闭对应弹窗 活动和提醒公用同一窗口，同步设置唯一标识openid
            if ([20007].includes(scene)) {
                removeActivityRemind({...data, openid: data.id}, scene);
            }
            //删除活动的消息通知20006
            if ([20006].includes(scene)) {
                console.log('删除活动的消息通知20006------删除活动的消息通知20006-----删除活动的消息通知20006----删除活动的消息通知20006')
                // 向主进程发送消息
                ipcRenderer.invoke('delete-activity-item');
                ipcRenderer.invoke('refresh-scence-activity');
            }
            //单独设置活动提醒不勾选应用弹窗(活动取消提前延期弹框)
            // 如果报名要钱高度要加 56
            if ([20022,20023,20024,20025,20026].includes(scene)) {
              const remindDetail = {
                ...data,
                openid: data.id,
              }
              openActivityRemind(remindDetail, scene);
            }
        } else {
          if (![20021, 20022, 20023, 20024, 20025, 20026].includes(scene)) return;
          // 如果报名要钱高度要加 56
          const remindDetail = {
            ...data,
            openid: data.id,
          }
          openActivityRemind(remindDetail, scene);
        }
    }
};

const onAppActivityNoticeNotify = (msg: MessageToSave) => {
  const { data, scene } = msg.contentExtra;

  // 活动应用详情更新
  if([20041].includes(scene)){
    console.log('活动应用详情更新', data, scene);
    ipcRenderer.invoke('refresh-activity-detail', data);
  }

  // 活动应用两端同步关闭弹窗
  if([20043].includes(scene)){
    removeActivityRemind({...data, openid: data.activity_id}, data.inner_scene);
  }
  console.error('活动通知1', scene)
  if (ActivityRefreshMap[scene]) {
    console.error('活动通知2', ActivityRefreshMap[scene],  msg.contentExtra?.data)
    ipcRenderer.invoke('activity-refresh', {
      type: ActivityRefreshMap[scene],
      data: msg.contentExtra?.data
    });
  }

  // 活动通知未读统计
  if ([20051].includes(scene)) {
    ipcRenderer.invoke("refresh-activity-unread-stats");
  }
}

const onAppArpprovalNotify = (msg: MessageToSave) => {
  const { data, scene } = msg.contentExtra;
  console.log("====>onAppArpprovalNotify", msg.contentExtra);
  if (msg.conversationType !== 4) {
    // 审批未读红点
    ipcRenderer.invoke("set-unread-post-approval");
    // 更新知行内部红点-返回状态, 比如4001-4002
    ipcRenderer.invoke("update-zhixing-internal-count");
    return;
  }

  // 审批数据变更通知
  if (scene === 4024) {
    ipcRenderer.invoke("update-approve");
    // 待办新增时-通知刷新
    ipcRenderer.invoke("send-refresh-schedule");
  } else {
    // 更新知行内部红点-返回状态, 比如4001-4002
    ipcRenderer.invoke("update-zhixing-internal-count");
  }
};

const onCommonNotify = (msg: MessageToSave) => {
  const { data, scene } = msg.contentExtra;
  // 下载更新
  console.log('====>onCommonNotify', msg);
  if (scene === 9001 && !msg.isOffline) {
    checkAppUpdate(data?.extend);
  }
};
