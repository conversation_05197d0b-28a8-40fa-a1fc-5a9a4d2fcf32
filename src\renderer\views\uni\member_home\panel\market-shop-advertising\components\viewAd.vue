<template>
  <t-dialog v-model:visible="visible" :close-btn="false" :header="true" :footer="false" width="872">
    <template #header>
      <div style="display: flex; align-items: center; width: 100%; justify-content: space-between">
        <div>
          {{ imgItem?.name }}
        </div>
        <img
          style="width: 24px; cursor: pointer; height: 24px"
          src="@/assets/<EMAIL>"
          @click="visible = false"
        />
      </div>
    </template>
    <div class="content-box">
      <img
        :style="{
          width:filterWH()?.bigWidth,
          height: filterWH()?.bigHeight,
        }"
        :class="tabIndex===1&&titleIndex === 3 ? 'content-bgc' : ''"
        class="content-box-bgc"
        :src="filterWH()?.bgImg"
      />
      <img
        :class="filterWH()?.samllClass"
        :src="imgUrl"
      />
    </div>
  </t-dialog>
</template>
<script setup lang="ts">
import { ref, onMounted } from "vue";
import { DialogPlugin, MessagePlugin } from "tdesign-vue-next";
import { useI18n } from "vue-i18n";

// import view1 from "@/assets/member/zwtpc.svg";
import view1 from "@/assets/member/pcdzwt.svg";
import view2 from "@/assets/member/adappviews.svg";
import view3 from "@/assets/member/view1.svg";
import view4 from "@/assets/member/view2.svg";
const imgUrl = ref("");
const tableList = ref([]);
const { t } = useI18n();
const visible = ref(false);
const titleIndex = ref(0);
const tabIndex = ref(1);

const filterWH = () => {
  let objs = {
    samllClass: "",
    bigWidth: "",
    bigHeight: "",
    bigClass: "",
    bgImg: "",
  };
  console.log(tabIndex.value,'tabIndextabIndex');
  console.log(titleIndex.value,'titleIndextitleIndex');
  if (tabIndex.value!==2) {
    if (imgItem.value?.name === 'APP_市场_主轮播图'||imgItem.value?.name === 'APP_市场店铺_主轮播图') {
      objs.bigWidth = "375px";
      objs.bigHeight = "464px";
      objs.bigClass = "content-bgc";
      objs.bgImg = view4;
      objs.samllClass = "samimg4";
      return objs;
    }
    if (imgItem.value?.name === 'PC_市场_主轮播图'||imgItem.value?.name === 'PC_市场店铺_主轮播图') {
      objs.samllClass = "samimg1";
    }
    if (imgItem.value?.name === 'PC_市场_右上'||imgItem.value?.name === 'PC_市场店铺_右上') {
      objs.samllClass = "samimg2";
    } else if (imgItem.value?.name === 'PC_市场_右下'||imgItem.value?.name === 'PC_市场店铺_右下') {
      objs.samllClass = "samimg3";
    }
    objs.bigWidth = "808px";
    objs.bigHeight = "440px";
    objs.bigClass = "";
    objs.bgImg = view3;
    return objs
  } else {
    if (imgItem.value?.name === 'APP_平台_主轮播图') {
      objs.bigWidth = "375px";
      objs.bigHeight = "464px";
      objs.bigClass = "";
      objs.bgImg = view2;
      objs.samllClass = "img4";
      return objs;
    }
    if (imgItem.value?.name === 'PC_平台_主轮播图') {
      objs.samllClass = "img1";
    }
    if (imgItem.value?.name === 'PC_平台_右上') {
      objs.samllClass = "img2";
    } else if (imgItem.value?.name === 'PC_平台_右下') {
      objs.samllClass = "img3";
    }
    objs.bigWidth = "808px";
    objs.bigHeight = "440px";
    objs.bigClass = "";
    objs.bgImg = view1;
    return objs

  }
};
const imgItem=ref(null)
const openWin = (val) => {
  // viewAdRef.value.openWin({
  //   url: viewImgUrl.value,
  //   imgIndex: imgIndex.value,
  //   tabFlag: tabFlag.value,
  // });
  console.log(val, "v啊离开来来来");
  imgUrl.value = val.url;
  imgItem.value = val.imgItem;

  tabIndex.value = val.tabFlag;
  titleIndex.value = val.imgIndex;
  visible.value = true;
};
defineExpose({
  openWin,
});
</script>

<style lang="less" scoped>
.content-bgc {
  width: 375px;
  height: 550px;
  border-radius: 17px 17px 0 0;
  /* border: 1px solid var(--border-kyy_color_border_default, #d5dbe4); */
  border-bottom: none;
}


.samimg1 {
  border-radius: 5px;
  position: absolute;
  top: 88px;
  left: 31px;
  width: 544px;
  height: 200px;
}
.samimg2 {
  position: absolute;
  top: 89px;
  border-radius: 5px;
  right: 31px;
  width: 208px;
  height: 96px;
}
.samimg3 {
  position: absolute;
  top: 192px;
  right: 31px;
  border-radius: 5px;
  width: 208px;
  height: 96px;
}
.samimg4 {
  position: absolute;
    top: 135px;
    left: 241px;
    width: 343px;
    height: 172px;
    border-radius: 8px;
}







.img1 {
  position: absolute;
  top: 56px;
  border-radius: 5.2px;
  right: 375px;
  width: 417px;
  height: 200px;
}
.img2 {
  position: absolute;
  top: 56px;
  border-radius: 5.2px;
  right: 220px;
  width: 147px;
  height: 96px;
}
.img3 {
  position: absolute;
  top: 160px;
  border-radius: 5.2px;
  right: 220px;
  width: 147px;
  height: 96px;
}
.img4 {
  position: absolute;
    top: 135px;
    left: 240px;
    width: 343px;
    height: 172px;
    border-radius: 8px;
}
.content-box-bgc {
  width: 808px;
}
.content-box {
  position: relative;
  display: flex;
  margin-top: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  /* border-radius: 11.139px; */
}
</style>
