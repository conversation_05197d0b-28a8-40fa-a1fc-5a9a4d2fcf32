<template>
  <div class="container">
    <div class="head">
      <t-button theme="default" style="font-weight: 600" @click="groupSet"> 分组查看设置 </t-button>

      <t-button theme="primary" variant="base" @click="onAdd">
        <template #icon>
          <iconpark-icon name="iconadd" class="iconadd"></iconpark-icon>
        </template>
        {{ t("ebook.gai") }}
      </t-button>

      <!-- <calling /> -->
    </div>

    <div class="body">
      <div class="body-content">
        <div class="table">
          <t-table
            row-key="id"
            :columns="memberColumns"
            :data="memberData"
            style="width: 100%"
            :hover="memberData.length"
            drag-sort="row-handler"
            @drag-sort="onDragSort"
          >
            <template #drag>
              <iconpark-icon name="icondrag" style="font-size: 20px; color: #828da5; margin-top: 5px"></iconpark-icon>
            </template>
            <template #Contact="{ row }">
              <div class="group-member-name">
                <template v-if="updateNames(row.group_contact).esp">
                  <div class="group-member-name-esp">
                    {{ updateNames(row.group_contact).names }}
                  </div>
                  <div>
                    {{ updateNames(row.group_contact).esp }}
                  </div>
                </template>

                <div v-else>
                  {{ updateNames(row.group_contact).names }}
                </div>
              </div>
            </template>

            <template #size="{ row }">
              <a @click="jumpOrganization(row)" v-if="row.group_team_member">
                {{ row.group_team_member || 0 }}
              </a>
              <span v-else>0</span>
            </template>

            <template #operate="{ row }">
              <div class="links">
                <t-link
                  theme="primary"
                  style="margin-left: -4px"
                  hover="color"
                  class="operates-item"
                  @click="editItem(row)"
                >
                  编辑
                </t-link>

                <t-link hover="color" class="operates-item" style="color: #d54941" @click="delItem(row)"> 删除 </t-link>
              </div>
            </template>
            <!-- <template #empty>
              <div class="empty">
                <noData :tip="t('ebook.zug')" :name="'no-friend-list'"> </noData>
              </div>
            </template> -->
          </t-table>
          <div class="empty" v-if="loaded && !memberData.length">
            <noData :tip="t('ebook.zug')" :name="'no-friend-list'"> </noData>
          </div>

          <div class="empty" v-if="!loaded && loading">
            <t-loading />
          </div>
        </div>
      </div>
    </div>
  </div>
  <group-set-modal :team-id="currentTeamId" ref="groupSetModalRef" @delist-succ="getMemberList" />

  <group-edit-modal :team-id="currentTeamId" @edit-ok="getMemberList" :etype="etype" ref="groupEditModalRef" />
</template>

<script lang="ts" setup>
import noData from "@renderer/components/common/Empty.vue";
import { getResponseResult } from "@renderer/utils/myUtils";
import { MessagePlugin, DialogPlugin } from "tdesign-vue-next";
import { ref, computed } from "vue";
import { useI18n } from "vue-i18n";
import { useDigitalPlatformStore } from "@renderer/views/digital-platform/store/digital-platform-store";
import { useRoute } from "vue-router";
import { platform } from "@renderer/views/digital-platform/utils/constant";
import { onMountedOrActivated } from "@renderer/hooks/onMountedOrActivated";
import { governmentGroupList, getGovernmentdelete, governmentsort } from "@renderer/api/uni/api/businessApi";
import groupSetModal from "./group-set-modal.vue";
import groupEditModal from "./group-edit-modal.vue";
import { useAssociationPanelStore } from "@renderer/views/uni/store/uni_panel";

const loading = ref(false);
const loaded = ref(false);
const groupEditModalRef = ref(null);

const digitalPlatformStore = useDigitalPlatformStore();
const associationPanelStore = useAssociationPanelStore();
const route = useRoute();

const { t } = useI18n();

const memberColumns = ref([
  {
    colKey: "drag", // 列拖拽排序必要参数
    title: "排序",
    width: "62px",
  },
  { colKey: "group_name", title: t("ebook.grn"), width: "300px" },
  { colKey: "Contact", title: t("ebook.llr"), width: "300px" },
  { colKey: "size", title: t("ebook.cys"), width: "100px" },
  { colKey: "operate", title: t("member.impm.drag_7"), width: "182px" },
]);
const memberData = ref([]);
const emits = defineEmits(["onPage"]);
const props = defineProps({
  platform: {
    type: String,
    default: "",
  },
});
// 平台类型 目前只有digital-platform
const platformCpt: any = computed(() => props.platform || route.query?.platform);


const currentTeamId = computed(() => {
  if (platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount?.teamId;
  }
  if (platformCpt.value === platform.digitalWorkbench) {
    return route.query?.teamId || 0;
  }
  return null;
});

// 获取会员职务列表
const getMemberList = async () => {
  console.log("获取会员职务列表");
  loaded.value = false;
  loading.value = true;

  try {
    let result = await governmentGroupList(currentTeamId.value);
    console.log(result);
    result = getResponseResult(result);
    if (!result) {
      return;
    }
    memberData.value = result.data.list;
    console.log(memberData.value);
    loaded.value = true;
    loading.value = false;
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    MessagePlugin.error(errMsg);
    loaded.value = true;
    loading.value = false;
  }
};

onMountedOrActivated(() => {
  getMemberList();
});

const onDragSort = (params) => {
  console.log(params.e.newIndex);
  let data = {
    the_previous_id: 0,
    move_id: params.newData[params.e.newIndex].id,
  };
  if (params.e.newIndex === 0) {
    data.the_previous_id = 0;
  } else {
    data.the_previous_id = params.newData[params.e.newIndex - 1].id;
  }
  onSetSortAxios(data).then(() => {
    memberData.value = params.newData;
  });
};

// 编辑
const editItem = async (row) => {
  etype.value = 2;
  setTimeout(() => {
    groupEditModalRef.value.editOpen(row.id);
  }, 1);
};

const delItem = (row) => {
  if (row.group_team_member) {
    dontdel();
    return;
  }
  const confirmDia = DialogPlugin({
    header: "删除",
    theme: "info",
    body: t("ebook.de1"),
    closeBtn: null,
    confirmBtn: t("ebook.qd"),
    className: "delmode",
    onConfirm: async () => {
      let res = null;
      try {
        res = await getGovernmentdelete(row.id, currentTeamId.value);
        res = getResponseResult(res);
        MessagePlugin.success(t("ebook.delsucc"));
        if (!res) return;
        getMemberList({});
      } catch (error) {
        MessagePlugin.error(error.message);
      }
      confirmDia.hide();
    },
    onClose: () => {
      confirmDia.hide();
    },
  });
};

const dontdel = () => {
  const confirmDia = DialogPlugin({
    header: "删除",
    theme: "info",
    body: t("ebook.de2"),
    closeBtn: null,
    confirmBtn: "知道了",
    cancelBtn: null,
    className: "delmode",
    onConfirm: async () => {
      confirmDia.hide();
    },
  });
};

// 排序
const onSetSortAxios = (data) => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await governmentsort(data, currentTeamId.value);
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      resolve("success");
    } catch (error) {
      reject();
      const errMsg = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
    }
  });
};
const etype = ref(1);
const onAdd = () => {
  etype.value = 1;
  groupEditModalRef.value.editOpen();
};

const groupSetModalRef = ref(null);
const groupSet = () => {
  groupSetModalRef.value.setOpen();
};

const jumpOrganization = (row) => {
  console.log(`跳转组织管理`, row);
  if (!row.group_team_member) {
    return;
  }
  associationPanelStore.setParamsKey("group_id", row.id);
  emits("onPage", "PRegularMemberPanel");
};

const updateNames = (group_contact) => {
  try {
    if (!group_contact || group_contact.length === 0) {
      return {
        names: "--",
        esp: null,
      };
    }
    const names = group_contact.map((item) => item.name);
    const newDiv = document.createElement("div");
    newDiv.innerText = names.join("、");
    document.body.appendChild(newDiv);
    newDiv.style.width = "fit-content";
    const width = newDiv.getBoundingClientRect().width;
    document.body.removeChild(newDiv);
    if (width > 230) {
      return {
        names: names.join("、"),
        esp: `等${names.length}人`,
      };
    }
    return {
      names: names.join("、"),
      esp: null,
    };
  } catch (error) {
    return {
      names: "--",
      esp: null,
    };
  }
};
</script>

<style lang="less" scoped>
.operates {
  &-item {
    // min-width: 64px;
    padding: 4px;
    border-radius: 4px;
    &:hover {
      background: var(--bg-kyy_color_bg_list_hover, #f3f6fa);
    }
  }
}

.statusT {
  display: flex;
  gap: 8px;
  .iconunusual {
    font-size: 20px;
    color: #828da5;
  }
}
.pop {
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 144px;
  &-item {
    padding: 8px;

    border-radius: 4px;
    cursor: pointer;
    user-select: none;
    transition: all 0.2s linear;

    display: flex;
    align-items: center;
    gap: 12px;
    img {
      margin-right: 8px;
    }

    &:hover {
      background: var(--kyy_color_dropdown_bg_hover, #f3f6fa);
    }
  }
}
.more-box {
  display: flex;
  width: 28px;
  height: 28px;
  padding: 0px 4px;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  color: #828da5;
  cursor: pointer;
  font-size: 18px;
  transition: all 0.15s linear;
  .iconmore {
    font-size: 20px;
  }
}
.more-box:hover {
  border-radius: 4px;
  background: var(--bg-kyy-color-bg-brand-foucs, #dbdfff);
  color: #3e4cd1;
  transition: all 0.15s linear;
}

.icondepartment1 {
  font-size: 20px;
  width: 20px;
  height: 20px;
  color: #828da5;
}

.container-bar {
  padding: 0 16px;
  margin-top: 16px;
  margin-bottom: 24px;
  display: flex;
  gap: 16px;
  .sBox {
    border-radius: 8px;
    background: var(--bg-kyy_color_bg_deep, #f5f8fe);
    padding: 12px 16px;
    min-width: 304px;
    width: fit-content;
    .item {
      height: 50px;
      display: flex;
      // align-items: center;

      .left {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .icon {
          font-size: 24px;
          color: #828da5;
        }
        .bText {
          color: var(--text-kyy_color_text_2, #516082);

          /* kyy_fontSize_2/bold */
          font-family: "PingFang SC";
          font-size: 14px;
          font-style: normal;
          font-weight: 600;
          line-height: 22px; /* 157.143% */
        }
      }
      .line {
        width: 1px;
        background: var(--divider-kyy_color_divider_deep, #d5dbe4);
        height: 16px;
        margin: auto 16px;
      }

      .right {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .count {
          color: var(--text-kyy_color_text_1, #1a2139);
          font-size: 18px;
          font-style: normal;
          font-weight: 600;
          line-height: 26px; /* 144.444% */
        }
        .text {
          color: var(--text-kyy_color_text_2, #516082);
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px; /* 157.143% */
        }
      }
    }
  }
}

.container {
  height: calc(100vh - 104px);
  width: 100%;
  max-width: none;
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: @kyy_color_bg_light;
    padding: 0 16px;
    height: 64px;
    &-title {
      font-size: 16px;

      font-weight: 700;
      text-align: left;
      color: #13161b;
      user-select: none;
    }
    &-buttons {
      display: flex;
      gap: 8px;
    }
  }
  .body {
    // min-height:100%;

    // width: 100%;
    // padding: 12px;
    overflow-y: auto;
    height: calc(100vh - 128px);
    &-content {
      // height: max-content;
      background-color: @kyy_color_bg_light;
      height: fit-content;
      min-height: 100%;
    }
  }
  .body::-webkit-scrollbar {
    width: 4px;
  }
  .body::-webkit-scrollbar-thumb {
    background-color: #ccc;
  }
}
.table {
  padding: 0 16px;
  display: block;
}
:deep(.empty) {
  min-height: 70vh;
}
.iconadd {
  font-size: 20px;
}
.table {
  :deep(.t-table table) {
    width: 100%;
    // min-width: 100%;
  }
  :deep(.t-table--layout-fixed) {
    table-layout: auto !important;
  }
}

// :deep(.t-dialog--default) {
//   padding: 0 !important;
// }

.links {
  display: flex;
  gap: 8px;
}

.head {
  background-color: #ffffff;
  padding: 0 16px;
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 24px;
  gap: 8px;
  .tip {
    color: var(--text-kyy_color_text_3, #828da5);
    text-align: center;

    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
    display: flex;
    align-items: center;
    gap: 4px;
    .icon {
      color: #828da5;
      font-size: 20px;
    }
  }
}
.body-content {
  min-height: min-content !important;
}

.flex-column-center {
  display: flex;
  flex-direction: column;
}
.tag {
  display: flex;
  height: 24px;
  min-height: 24px;
  max-height: 24px;
  padding: 1px 8px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  text-align: right;
  font-family: "PingFang SC";
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 22px; /* 157.143% */
}
.status-tag0:extend(.tag) {
  width: 86px;
  color: var(--kyy_color_tag_text_warning, #fc7c14);
  border-radius: var(--kyy_radius_tag_full, 999px);
  background: var(--kyy_color_tag_bg_warning, #ffe5d1);
}
.status-tag1:extend(.tag) {
  width: 58px;
  border-radius: var(--kyy_radius_tag_full, 999px);
  background: var(--kyy_color_tag_bg_success, #e0f2e5);
  color: var(--kyy_color_tag_text_success, #499d60);
}
.status-tag2:extend(.tag) {
  width: 72px;
  color: var(--lingke-wrong, #d92f4d);
  border-radius: var(--kyy_radius_tag_full, 999px);
  background: #fbdde3;
}
.group-member-name {
  display: flex;
  .group-member-name-esp {
    width: 230px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
:deep(.table .t-table__empty-row) {
  display: none;
}
.operates-item {
  display: flex;
  padding: 4px;
  justify-content: center;
  align-items: center;
  gap: 10px;
}
.operates-item:hover {
  border-radius: 4px;
  background: var(--bg-kyy_color_bgBrand_hover, #eaecff);
}
</style>
