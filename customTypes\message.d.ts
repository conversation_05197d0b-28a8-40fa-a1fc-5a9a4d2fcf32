import { KyyAvatar } from '@/components/kyy-avatar/index.vue';

/**
 * UI通过主进程调用消息方法的数据结构
 */
interface IMBridgeInvokeData {
    bridgeId?: number
    action: string
    data: any
}

/**
 * 消息进程返回调用结果给主进程，主进程再返回给UI
 */
interface IMBridgeReplyData {
    bridgeId: number
    /**
     * 调用结果
     */
    data: any
}

// export declare enum MessageContentType {
//   'text' = 101,  //文本消息
//   'quote' = 114,  //引用消息
//   'revoke' = 2101, //撤回消息

// }

// IM回调事件及参数
interface IMBridgeCallbackData {
    eventName: string
    eventArgs: any[]
}

// 回复消息'RC:ReferenceMsg': 114, 撤回引用111 自定义文本'RC:TxtMsg': 'text', 撤回消息'RC:RCNtf': 2101
type messageType = 114 | 111 | 'RC:ReferenceMsg' | 2101 | 'text' | 'RC:TxtMsg' | 'RC:RCNtf' | 'APP_MEETING_MSG'
interface MessageToSave {
    // 消息自增id
    id?: number
    // 消息ID
    messageUId:string
    /**
     * 消息类型
     */
    messageType: messageType
    /**
     * 消息内容
     */
    content: any
    ex?:any
    /**
     * 消息发送者的用户 id
     */
    senderUserId: string;
    /**
     * 会话 id
     */
    targetId: string;
    // openIM消息ID
    clientMsgID?:string
    // 暂时id，用于渲染进程调用主进程存储消息的时候对应消息ß
    tempId?: string
    // 消息内容，融云原始为对象，存储的时候需要转为 string
    payload?: string
    // 消息的 extra 存储，仅保存的时候使用
    payloadExtra?: string
    // 本地会话id，个人会话的情况下，适配了身份卡
    localSessionId?: string
    //openIM会话ID
    conversationID?: string
    conversationType: ConversationType
    seq?:number
    /**
     * @
     */
    attachedInfoElem?: Object
    /**
     * 文本
     */
    textElem?:any
    // 消息extra内容，展示的时候使用
    contentExtra?: {
        senderId?: string
        receiverId?: string
        data?: any
        type?: any
        clientId?: string
        source?: string
        contentType?: string
        errorInfo?:string // 发送失败类型，展示tips
        /**
         * 场景，通知消息和业务消息可能具有不同的场景，即需要进行不同的后续处理
        APP_KNOW // 知行提醒 场景: 0:提醒 1:清单 16:通知创建（助手） 17:通知变更 19:日程变更 20:隐私锁变更 21:稍后处理变更 22: 笔记变更消息 23:笔记本变更消息 24:清单变更消息 25:笔记本标签变更消息
        APP_SQUARE // 广场提醒
        APP_NOTIFY // 全部提醒
        APP_CLOUD_DISK // 云盘提醒
        APP_APPROVAL // 审批
        APP_ADDRESS_BOOK // 通讯录
        APP_TEAMS // 组织
        APP_SECRETARY // 小秘书
        APP_WORKS // 工作通知
        APP_NICHE // 商机助手
        APP_PARTNER // 联盟助手
        APP_ACTIVITY // 活动助手
        APP_SERVE // 服务助手
        // =============================
        // =============================
        APP_GROUPS // 群组 场景: 0:新增 1:更新 2:删除 5:群主变更 6:入群申请 14:面对面建群变更 18:群公告变更 26:群天气信息更新
        APP_MEMBERS // 群成员 场景: 0:新增 1:更新 2:删除 3:批量删 4:批量加 7:自己退群
        APP_PAIRS // 单聊 场景: 0:新增 1:更新 2:删除 3:批量删
        APP_ACCOUNT // 账号 2:删除 8:用户注销下线 9:移除身份卡 10:添加身份卡 11:修改密码下线 12: 抢登下线 13:组织踢出 15:违规封禁下线
        APP_CONFIGS // 用户配置: 0:新增 1:更新
        APP_ADDRESS_BOOK // 通讯录(计数通知) 1:更新
        APP_MEETING_INVITE // 会议邀请: 1:更新
        APP_CARD // 身份卡 1:更新(单聊)
        */
        scene?: number,

        seq?: number,
        extand?: any
    }

    replied?: string
    receipts?: {hasReadUserIDList?:string[],unReadUserIDList?:string[]}
    receiptTime?: number
    isRead?:boolean
    isOffline?: boolean
    readTime?: number
    recallTime?: number
    deleteTime?: number
    source?: string
    isCountUnread?: boolean
    unreadMentionedCount?: number
    openImId?:string
    // 消息在本地创建时间， 不存库，仅在发送的时候，用来保持消息顺序
    createTime?: number
    sendTime?:number
    // 同sendTime，历史原因
    sentTime?:number
    // 进度条，文件类的消息发送会有这个
    progress?: number
    // 是否重试发送状态
    isResend?: boolean,
    // 场景红点
    sceneRedDot?:string
    // openIM 插入消息的消息状态
    status?:number  // 1:发送中,2:发送成功,3:发送失败
    // 同sendStatus，历史原因
    sentStatus?:number  // 1:发送中,2:发送成功,3:发送失败
    sendID?: string  // 发送者openIMId
    recvID?: string  // 接收者openIMId
}
/**
 * 引用消息
 * @category Interface
 */
interface IReferenceMessageBody extends MessageToSave {
    /**
     * 被引用消息的发送用户 Id
     */
    referMsgUserId: String;
    /**
     * 引用消息对象
     */
    referMsg: any;
    /**
     * 引用消息UID
     */
    referMsgUid: string;
    /**
     * 输入的文本消息内容
     */
    content: string;
    /**
     * 发送的引用消息类型
     */
    objName: string;
}
/**
 * 消息接收批量处理数据结构
 */
interface ConversationMsgReceiveData {
    /**
     * 会话类型
     */
    conversationType: number,
    /**
     * 会话id
     */
    localSessionId: string,
    /**
     * 会话目标id
     */
    targetId: string,
    /**
     * 会话消息
     */
    msgList: MessageToSave[],
    /**
     * 会话清零消息
     */
    cleans: MessageToSave[]
}

interface ConversationChangeData {
    latestMsg?: MessageToSave
    unread: number
    mention:number
}

/**
 * 发送状态
 * @category Enum
 */
declare enum SentStatus {
    /**
     * 发送中。
     */
    SENDING = 10,
    /**
     * 发送失败。
     */
    FAILED = 20,
    /**
     * 已发送。
     */
    SENT = 30,
    /**
     * 对方已接收。
     */
    RECEIVED = 40,
    /**
     * 对方已读。
     */
    READ = 50,
    /**
     * 对方已销毁。
     */
    DESTROYED = 60
}
/**
 * 会话类型
 * @category Enum
 */
declare enum ConversationType {
    /**
     * 无类型
     */
    NONE = 0,
    /**
     * 单聊
     */
    PRIVATE = 1,
    /**
     * 讨论组
     */
    DISCUSSION = 2,
    /**
     * 群组聊天
     */
    GROUP = 3,
    /**
     * 聊天室会话
     */
    CHATROOM = 4,
    /**
     * 客服会话
     */
    CUSTOMER_SERVICE = 5,
    /**
     * 助手
     */
    ASSISTANT = 6
}

/**
     * 单聊表示关系类型，群聊表示群类型
     * 个人
     * FRIEND // 好友
     * BUSINESS // 商务
     * CO_WORKER // 同事
     * TEMPORARY // 临时
     * CONSULT // 咨询
     * PLATFORM_FRIEND // 平台关系
*/
type PairType = string

/**
     * 类型，根据业务来定义
     *
     * NORMAL = 0; // 普通群
     * DEPARTMENT = 1; // 部门群
     * COMPANY = 2; // 公司群
     * INNER = 3; // 内部群
     * KNOW = 4; // 知行助手
     * CLOUD_DISK = 5; // 云盘助手
     * APPROVAL = 6; // 审批助手
     * SQUARE = 7; // 广场助手
     * APPLY_PAIR = 8; // 单聊申请
     * SECRETARY = 9; // 小秘书
     * OUT = 10; // 外部群
     * APPLY_BUSINESS_CANCEL = 11; // 商务关系解除申请
     * APPLY_TEAM = 12; // 新成员申请
     * WORK_NOTIFY = 13; // 工作通知
     * FILE_HELPER = 14; // 文件助手
     * FAMILY = 15; // 家庭群
     * NICHE = 16; // 商机助手
     * PARTNER = 17; // 联盟助手
     * ACTIVITY = 18; // 活动助手
     * SERVE = 19; // 服务助手
     * PLATFORM = 20; // 平台群
     * PLATFORM = 22; // 平台全员群`
     * PLATFORM = 23； 数字平台分组群
*/
type GroupType = number
interface ConversationToSave {
    avatar?: string
    name?: string
    localSessionId: string
    conversationID?: string // openIm会话ID
    conversationType: ConversationType
    targetId: string
    myCardId?: string
    targetCardId?: string
    myOpenImId:string //我的openIMUserID
    ownerOpenImId?:string //群聊群主openIMUserID
    targetOpenImId:string //单聊对方openIMUserID，群聊群组id
    createTime: number
    updateTime: number
    startChatTime?: number  // 其他入口打开会话时间，需要排序但是不更新会话时间,收到消息或updateTime更新的时候需要置null，重新启用updateTime排序
    onlyName?: string
    /**
     * 是否免打扰
     */
    isMute?: boolean
    /**
     * 是否置顶
     */
    isTop?: boolean
    msgId?: string
    latestMessage: MessageToSave | null
    draft?: string
    unreadCount:number //消息未读数量
    unreadMentionedCount:number //@未读数量
    joined?: number // 我加入的时间
    viewHistory?: boolean // 可查看历史
    /**
     * 本地表示会话是否移除
     */
    removeSession?: boolean
    /**
     * 表示关系被移除
     * 单聊：已经解除关系
     * 群聊：表示退群、被踢出群聊、群已解散
     */
    inSession?: boolean

    relation: PairType | GroupType
    // 表示用户是否后注销
    unregistered?: boolean;
    // 是否在对方黑名单
    blacklist?: number;
    /**
     * 是否特殊关注
     */
    isFollow?: boolean
    /**
     * 场景红点,保存数据库 string
     */
    sceneRedDot?:string
    /**
     * 场景红点obj
     */
    sceneRedDotObj?:{[key:string]: boolean}
    // 详情类型,进消息对话是否展示对应的详情，临时存储，进程结束则消失
    detailObj?: {
      detailType?: MsgDetailDrawerType | null,
      // 消息自增id
      id?: number | null,
      link?: string | null,
      serverDetailId?: number | null,
      msgDetailOptionZIndex?: number | null,
      sceneOptionZIndex?: number | null,
    }
}

interface ConversationMemberToSave {
    id?: number,
    sessionId: string,
    avatar?: string;
    cardId?: string;
    openImId?:string,
    comment?: string;
    describe?: string;
    nickname?: string;
    openId?: string;
    staffName?: string;
    departmentId?: string;
    departmentName?: string;
    jobId?: string;
    jobName?: string;
    teamId?: string;
    teamName?: string;
    roles?: string;
    stayOn?: number;
    noDisturb?: number;
    // 群成员是否允许查看历史记录，单聊无此字段
    viewHistory?: boolean;
    // 群身份id
    label_id?: string;
    // 加入时间，仅群聊有效
    joined?: number;
    // 是否常用聊天
    group_type?: 0 | 1;
}

interface ConversationMemberLabel {
    /**
     * 数据库id
     */
    id?: number,

    /**
     * 创建时间
     */
    create_at: number;
    /**
     * 删除时间
     */
    delete_at: number;
    /**
     * 群ID
     */
    group_id: string;
    /**
     * 身份标签名称
     */
    label: string;
    /**
     * 群身份标签ID
     */
    openid: string;
    /**
     * 更新时间
     */
    update_at: number;
    /**
     * 创建用户ID
     */
    user_id: string;
}

// 比较符号，is 为 null， not 为 not null
type WhereCompare = '=' | '>' | '<' | 'null' | 'not null' | '!=' | '>=' | '<=';

type WhereCondition = {
    relation?: "or" | "and",
    columns: {
        name: string,
        compare: WhereCompare
        value?: string | number
    }[]
}
interface DbTableUpdateItem {
    whereColumns?: WhereCondition[],
    whereSql?: string,
    columns: string[],
    value: any | any[]
}

interface GroupToSave {
    /**
    * 附加数据
    */
    attachment: {
        // "issac001|issac|issac005|https://img.kuaiyouyi.com/517303767528484864.jpg|issac"
        avatar: string;
        // 是否开启群员验证
        hasInviteMember?: boolean;
        // 是否开启新成员查看历史记录
        hasLookForHistory?: boolean;
        // 开启云盘文件同步
        openCloudFile?: boolean;
        // 开启云盘图片视频同步
        openPictureFile?: boolean;
        // 企业组织名字
        teamName?: string
    };
     /**
     * 创建时间
     */
     created: number;
     /**
      * 创建人 openid
      */
     creator: string;
     /**
      * TODO: 创建者身份卡，添加到数据库
      */
     creator_card: string;
     /**
      * 开放标识
      */
     group: string;
     /**
      * 组织归属地
      */
     host?: string;
     /**
      * 群名称
      */
     name: string;
     /**
      * 所有者标识
      */
     owner: string;
     /**
      * 组织内层级路径定义，JSON 一维数组
      */
     path?: string;
     /**
      * 群聊是否解散
      */
     removed?: number;
     /**
      * 0:非限定 1:会话级 2:关系链
      */
     scene: number;
     /**
      * 所属组织，为空表示跨组织
      */
     team?: string;
    /**
     * 群类型
     */
     type: GroupType;
     /**
      * 更新时间
      */
     updated: number;
     /**
      * 群成员数
      */
     total: number

    /**
     * 是否同步到云盘
     */
     sync_disk?: boolean;
     /**
      * 云盘文件夹id
      */
     disk_folder?: number;
    /**
     * 生日消息卡片样式，生日消息卡片样式，家庭群 ONE 样式1 TWO 样式2 THREE 样式3
     * @type {string}
     * @memberof GroupToSave
     */
    birthday_style?: string;
    /**
     * 生日提醒开关
     * @type {boolean}
     * @memberof GroupToSave
     */
    birthday_notify?: boolean;
    owner_open_im_id?:string;
    conversation_id?:string;
}



interface IChatCardUpdateNotificationItem {
    cardId: string,
    openId: string,
    avatar: string,
    staffName: string,
    comment: string,
    describe: string,
}


/**
 * 会话对象
 */
interface IConversationEntity {
    /**
     * 会话id，即融云id，群聊通过服务器接口生成
     * 数据库字段：[session_id]
     */
    id: string;
    /**
     * 本地会话id，由客户端生成，为了支持多身份id，而融云不支持多身份id
     * 规则如下：:对方身份卡:己方身份卡
     * 数据库字段: [client_session_id]
     */
    localId: string;
    /**
     * 当前会话，用户的身份id
     * 个人无身份卡，在某个组织下具有身份卡
     * 数据库字段: [my_card_id]
     */
    card?: string;
    /**
     * 会话类型： 个人、群等等
     * 数据库字段: [session_id_type]
     */
    type: string;
    /**
     * 会话创建时间戳
     * 数据库字段: [create_time]
     */
    create_time: number;
    /**
     * 会话标识，用与展示内部，部门，外部等
     * 数据库字段: [only_name]
     */
    tags?: string;
    /**
     * 是否置顶
     * 数据库字段: [stay_on]
     */
    isTop?: boolean;
    /**
     * 逻辑删除字段，会话是否被删除
     * 数据库字段: [remove_session]
     */
    removeSession?: boolean;
    /**
     * 是否被移除会话，即对方删除、拉黑、踢出群聊等
     * 数据库字段: [in_session]
     */
    inSession?: boolean;
    /**
     * 是否免打扰
     * 数据库字段: [not_disturb]
     */
    mute?: boolean;

    /**
     * 未读消息数
     * 数据库字段: [no_read]
     */
    unreadCount?: number;
    /**
     * 未读消息中 `@` 我的消息数
     * 数据库字段: [mention_count]
     */
    mentionCount?: number;
    /**
     * TODO: 对应消息什么id？
     * 最新消息id
     * 数据库字段: [msg_id]
     */
    lastMsgId?: string;
    /**
     * 最新消息的发送时间，服务器时间
     * 数据库字段: [update_time]
     */
    updateTime?: number;
    /**
     * 消息草稿内容
     * 数据库字段: [draft]
     */
    draft?: string;
}

/**
 * 消息对象
 */
interface IMessageEntity {
    /**
     * 消息服务器唯一 id
     * 数据库字段: [msg_id]
     */
    id?: string;
    /**
     * 本地时间戳id
     * 数据库字段: [client_msg_id]
     */
    localId?: number;
    /**
     * 融云会话id
     * 数据库字段: [target_id]
     */
    targetId: string;
    /**
     * 会话id，适配身份卡
     * 数据库字段: [session_id]
     */
    conversationId: string;
    /**
     * 消息发送者id，可用于判断是否自己
     * 数据库字段: [sender]
     */
    senderId: string;
    /**
     * 消息内容
     * 数据库字段: [payload]
     */
    content: string;
    /**
     * 消息类型
     * 数据库字段: [content_type_name]
     */
    category: string;
    /**
     * 消息创建时间， 服务器消息发送时间，已发送之前设置为创建时间，发送之后改为服务器时间
     * 数据库字段: [create_at_time]
     */
    createTime?: number;
    /**
     *
     * 数据库字段: [timestamp]
     */
    sentTime?: number;
    /**
     * 消息发送状态，针对发送者
     * 数据库字段: [msg_result_name]
     */
    sentStatus?: string;

    /**
     * 自己发送的消息，对方已读时间
     * 数据库字段: [receipt_time]
     */
    receiptTime?: number;
    /**
     * 自己接受其他人消息，已读时间
     * 数据库字段: [read_time]
     */
    readTime?: number;
    /**
     * 撤回的时间
     * 数据库字段: [recall_time]
     */
    recallTime?: number;
    /**
     * 删除的时间
     * 数据库字段: [delete_time]
     */
    deleteTime?: number;
    /**
     * 群中，自己发的消息，被别人已读的列表
     * 数据库字段: [receipts]
     */
    receipts?: string;
    /**
     * 消息被回复列表，`@`多人或全部的情况下
     *  数据库字段: [replied]
     */
    replied?: string;
    /**
     * 消息发送设备来源
     */
    source?: string;
}
/**
 * 发送消息会话属性
 * @category Interface
 */
interface SendConversationOption {
    /**
     * 会话类型
     */
    conversationType: ConversationType;
    /**
     * 会话 Id
     */
    targetId: string;
    /**
     * 频道 Id 字符串类型，不能包含 _ , 长度不能超过 20
     */
    channelId?: string;
}
interface IMessageContentBase {
    senderId: string
    receiverId: string
    data?: any
}

interface IMessageTextExtra {
    text: string
}
type OfflinePush = {
    title: string;
    desc: string;
    ex: string;
    iOSPushSound: string;
    iOSBadgeCount: boolean;
};
/**
 * 发送消息option
 * @category Interface
 */
interface ISendMessageOptions {

    /**
     * 是否发送静默消息
     * @description
     * 当值为 `true` 时，服务器将不会发送 Push 信息，移动端也不会弹出本地通知提醒
     */
    disableNotification?: boolean;
    /**
     * 离线Push 信息
     */
    offlinePushInfo: OfflinePush
    /**
     * 消息拓展内容数据
     */
    expansion?: {
        [key: string]: string;
    };
}
/**
 * 图片消息体
 */
interface IMessageImageExtra {
    // 图片地址
    imgUrl: string
    // 缩略图地址，暂定为 ?x-oss-process=style/size-400
    thumbnail: string
    // 文件宽度，像素
    width: number
    // 文件高度，像素
    height: number
    // 图片文件大小， byte
    size: number
    // 文件后缀
    type: string
    // 撤回ID， 可选
    recallFileId?: string
}

/**
 * 视频消息体
 */
interface IMessageVideoExtra {
    // 视频名称，可以传空字符
    videoName: string
    // 视频预览图片
    videoImageUrl: string
    // 视频地址
    videoUrl: string
    // 视频宽度，像素
    width: number
    // 视频高度，像素
    height: number
    // 视频文件大小， byte
    size: number
    // 视频时长，秒
    duration: number
    // 撤回ID， 可选
    recallFileId?: string
}

interface IMessageFileExtra {
    fileUrl: string
    fileName: string
    size: number
    type: string
}

interface IMessageVoiceExtra {
    duration: number,
    size: number,
    audioUrl: string,
    audioId: string,
    audioText?: string,

    // 是否展示文本
    showText?: boolean,
    // 转换错误
    convertError?: 'error'
}

interface IMessageMergedExtra {
    data: {
        name?: string,
        avatar?: string,
        cardId?: string,
        openId?: string,
        data?: any,
        type?: string,
        sendTime?: number,
        content?: string,
        imgUrl?:string,

    }[],
    fileUrl?: string
}

type SearchContactRowModel = Pick<ConversationMemberToSave, 'avatar' | 'staffName' | 'nickname' | 'teamName' | 'departmentId' | 'departmentName' | 'jobId' | 'jobName'> & {
    cardId: string;
    main?: string;
    peer?: string;

    recent?: boolean;
    /**
     * 唯一id去重，main+peer
     */
    itemID?:string;
    teamID?:string;
 }

 type MsgConstructorData = {msgType: string, msgFields: {[key:string]: any}};


// 文件上传信息
type FileUploadInfo = {
    name: string;
    path: string;
    size: number;
    type: string;
}

type CustomMessage = {
  contentType: string | number,
  type?: number,
  content?: {},
  extra: string
}

/**
 * @category Interface
 */
interface IAsyncRes<T = void> {
  /**
   * Promise 执行结果
   */
  code: number;
  /**
   * 结果数据
   */
  data?: T;
  /**
   * 错误消息
   */
  msg?: string;
}
