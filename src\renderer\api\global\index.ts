import { globalRequest } from "@renderer/utils/apiRequest";
import { AxiosResponse } from "axios";
import { BaiduMapSuggestionParams } from './models/baiduMap';

const BAIDU_REDIRECT = '/baidumap/';
const Api = {
  baiduMap: `${BAIDU_REDIRECT}place/v2/suggestion`,
  ipLocation: `${BAIDU_REDIRECT}location/ip`,
};

// 地点输入提示
// https://lbsyun.baidu.com/faq/api?title=webapi/place-suggestion-api
export const baiduMapSuggestion = (params: BaiduMapSuggestionParams): Promise<AxiosResponse> => globalRequest.get(Api.baiduMap, {
  params: { ...params, output: 'json' }
});

// 普通IP定位
export const baiduMapIPLocation = (params?: { ip?: string }): Promise<AxiosResponse> => globalRequest.get(Api.ipLocation, { params });
