<template>
  <t-dialog
    v-model:visible="visible"
    :z-index="2500"
    attach="body"
    width="472px"
  >
    <template #header>
      <div class="header">
        <!-- <svg class="iconpark-icon header-svg"><use href="#attention-6ebn71gl"></use></svg> -->
        <div class="header-title"> {{ $t("engineer.move_super_admin") }}</div>
      </div>
    </template>
    <template #body>
      <div class="toBody">
        <div class="space">
          <div class="space-label">{{ $t("member.sadim.s_1") }}</div>
          <div class="space-value">
            <div
              v-show="!formData.idStaff"
              class="addIcon cursor"
              @click="onSelectPerson"
            >
              <iconpark-icon
                name="iconadd"
                style="font-size: 24px"
              ></iconpark-icon>
            </div>
            <div
              v-show="formData.idStaff"
              class="showPerson cursor"
              @click="onSelectPerson"
            >
              <kyyAvatar
                data-id="isclick"
                :image-url="formData.staffAvatar"
                avatar-size="32px"
                :user-name="formData.staffName"
                :shape="'circle'"
              />
              <span class="name-text line-1">{{ formData.staffName }}</span>
            </div>
          </div>
        </div>
        <div class="space">
          <div class="space-label">{{ $t("member.sadim.s_2") }}</div>
          <div class="space-value">
            <t-radio-group v-model="formData.action">
              <t-radio :value="1"> {{ $t("member.sadim.s_3") }} </t-radio>
              <t-radio :value="2"> 从管理员列表中移除 </t-radio>
            </t-radio-group>
          </div>
        </div>
      </div>
    </template>
    <template #closeBtn>
      <!-- <iconpark-icon name="close" size="16"></iconpark-icon> -->
      <!-- <svg class="iconpark-icon" style="width: 16px; height: 16px">
        <use href="#close" />
      </svg> -->
      <iconpark-icon name="iconerror" style="font-size: 24px"></iconpark-icon>
    </template>
    <template #footer>
      <div class="footer">
        <t-button
          theme="default"
          variant="outline"
          style="margin-right: 10px"
          @click="onClose"
        >
          {{ $t("member.impm.input_8") }}
        </t-button>
        <t-button theme="primary" @click="onSave">
          {{ $t("member.impm.input_9") }}
        </t-button>
      </div>
    </template>
  </t-dialog>

  <SelectMemberModal
    ref="selectMemberModalRef"
    :options="optionsMembers"
    :header="$t('member.sadim.s_5')"
    :is-only="true"
    @sub-form="onListenMembers"
  />

  <!-- <organize-select-modal
    ref="organizeSelectModalRef"
    :header="'添加成员'"
    :is-only="true"
    :team-id="'484044634096406528'"
    @on-select-item="onSelectItem"
  /> -->
  <!-- <approval-organize-select-modal
    ref="organizeSelectModalRef"
    :header="'添加成员'"
    :radio-flag="false"
    :is-only="true"
    :team-id="teamId"
    :close-on-overlay-click="false"
    :show-modal-list-index-array="[1, 6]"
    :is-filter="true"
    :is-department="false"
    :only-project="true"
    @on-select-item="onSelectItem"
  /> -->
</template>

<script lang="ts" setup>
// import approvalOrganizeSelectModal from "@renderer/views/approve/approve_home/components/approvalOrganizeSelectModal.vue";
import { ref, reactive, Ref, computed } from "vue";
// 选择直接上级组件 或添加成员
// import OrganizeSelectModal from '@renderer/components/engineer/modal/OrganizeSelectModal.vue';
import kyyAvatar from "@renderer/components/kyy-avatar/index.vue";
import { MessagePlugin } from "tdesign-vue-next";
import { getResponseResult } from "@renderer/utils/myUtils";
import { ExchangeProjectAdminAxios } from "@renderer/api/engineer";
import { getProjectTeamID } from "@renderer/views/engineer/utils/auth";
import SelectMemberModal from "@renderer/views/uni/member_home/panel/membership-setting-panel/modal/select-member-modal.vue";
import {
  changeAdminAxios,
  getAppStaffAxios,
} from "@renderer/api/uni/api/businessApi";
import { useRoute } from "vue-router";
import { platform } from "@renderer/views/digital-platform/utils/constant";
import { useDigitalPlatformStore } from "@renderer/views/digital-platform/store/digital-platform-store";
import { useUniStore } from "@renderer/views/uni/store/uni";
import { getUniTeamID } from "@renderer/views/uni/utils/auth";
const props = defineProps({
  platform: {
    type: String,
    default: '',
  },
})
const digitalPlatformStore = useDigitalPlatformStore();
const route = useRoute();
// 平台类型 目前只有digital-platform
const platformCpt = computed(() => {
  return props.platform || route.query?.platform
})
const store = useUniStore();
const activeAccount = computed(() => {
  if(platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount
  } else if(platformCpt.value === platform.digitalWorkbench)  {
    return route.query;
  } else {
    return store.activeAccount
  }
})


const currentTeamId = computed(() => {
  if(platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount?.teamId
  } else if(platformCpt.value === platform.digitalWorkbench){
    return route.query?.teamId || 0;
  } else {
    return  getUniTeamID()
  }
})
const emits = defineEmits(["reload"]);
const teamId = ref("");
teamId.value = getProjectTeamID();
const optionsMembers = ref([]);
const formData = reactive({
  id: 0, // 需要转移的管理员
  staffName: "",
  staffAvatar: "",
  idStaff: 0,
  action: 1,
});
const data = ref(null);
// const organizeSelectModalRef = ref(null);
const selectMemberModalRef = ref(null);

const onSelectPerson = () => {
  // teamId.value = getProjectTeamID();

  // organizeSelectModalRef.value.onOpen();
  getAppMemberList().then(() => {
    selectMemberModalRef.value.onOpen();
    if (formData.idStaff) {
      selectMemberModalRef.value.onSetSelectedValue([formData.idStaff]);
    }
  });
};

// const onSelectItem = (item) => {
//   // selectedPerson.value = item;
//   console.log(item);
//   // if (organizeSelectType.value === 1) {
//   // 	formData.leader = item.idStaff;
//   // 	formData.leader_name = item.name;
//   // } else if (organizeSelectType.value === 2) {
//   // 	formData.contact = item.idStaff;
//   // 	formData.contact_name = item.name;
//   // }
//   formData.staffAvatar = item.avatar;
//   formData.idStaff = item.idStaff;
//   formData.staffName = item.name;
// };

const getAppMemberList = () => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await getAppStaffAxios({}, currentTeamId.value);
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      optionsMembers.value = result.data.map((v) => {
        if (v.idStaff === data.value?.idStaff) {
          v.is_admin = 1;
        } else {
          v.is_admin = 0;
        }
        return v;
      });
      resolve("success");
    } catch (error) {
      reject();
      const errMsg = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
    }
  });
};

// 添加成员
const onListenMembers = async (arr) => {
  console.log(arr);
  if (arr && arr.length < 1) return;
  // const formData = reactive({
  //   id: 0, // 需要转移的管理员
  //   staffName: "",
  //   staffAvatar: "",
  //   idStaff: 0,
  //   action: 1
  // });
  const option = optionsMembers.value.find((v) => v.idStaff === arr[0]);
  if (option) {
    formData.idStaff = option.idStaff;
    formData.staffName = option.name;
    formData.staffAvatar = option.avatar;
  }
  selectMemberModalRef.value.onClose();
};

const onSave = async () => {
  if (!formData.idStaff) {
    MessagePlugin.error("请选择新的超级管理员");
    return;
  }
  let res = null;
  try {
    const params = {
      id: data.value ? data.value.id : undefined,
      idStaff: formData.idStaff,
      action: formData.action,
    };
    res = await changeAdminAxios(params, currentTeamId.value);
    res = getResponseResult(res);
    if (!res) return;
    emits("reload");
    onClose();
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    MessagePlugin.error(errMsg);
  }
};

// 从公司组织架构导入
// const onImportFromCompany = () => {
// 	emits('onImportFromCompany');
// };

// // 从工程组织架构中导入
// const onImportFromProject = () => {
// 	emits('onImportFromProject');
// };
// // 手工建立组织架构
// const onHandCreate = () => {
// 	emits('onHandCreate');
// };

const visible = ref(false);
const initData = (_data) => {
  if (_data) {
    formData.id = _data.id; // 需要转移的管理员
    formData.staffName = "";
    formData.staffAvatar = "";
    formData.idStaff = 0;
    formData.action = 1;
  } else {
    formData.id = 0; // 需要转移的管理员
    formData.staffName = "";
    formData.staffAvatar = "";
    formData.idStaff = 0;
    formData.action = 1;
  }
};
const onOpen = (_data?: any) => {
  data.value = _data;

  initData(_data);
  visible.value = true;
};

const onClose = () => {
  visible.value = false;
};

defineExpose({
  onOpen,
  onClose,
});
</script>

<style lang="less" scoped>
.t-button + .t-button {
  margin-left: 0;
}
.showPerson {
  display: flex;
  flex-direction: column;
  // align-content: flex-start;
  align-items: center;
  justify-content: center;
  width: 48px;
  .name-text {
    color: var(--text-kyy_color_text_2, #516082);
    text-align: center;
    width: inherit;

    /* kyy_fontSize_1/regular */
    font-family: "PingFang SC";
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px; /* 166.667% */
  }
}
.header {
  display: flex;
  &-svg {
    width: 22px;
    height: 22px;
    color: #2069e3;
    margin-right: 8px;
  }
  &-title {
    font-size: 16px;

    font-weight: 700;
    color: var(--radio-bueeon-kyy-color-radio-button-text-active, #1a2139);
  }
}
.toBody {
  .msg {
    margin-bottom: 16px;
    margin-left: 30px;
  }
  .btns {
    display: flex;
    align-items: center;
    flex-direction: column;
    .btn {
      width: 320px;
      margin-bottom: 16px;
    }
  }
}
// :deep(.t-dialog--default) {
// 	padding-bottom: 0;
// }

.t-dialog__body {
}

.space {
  margin-bottom: 24px;
  &-label {
    font-size: 14px;

    font-weight: 400;
    text-align: left;
    color: var(--radio-bueeon-kyy-color-radio-button-text-active, #1a2139);
  }
  &-value {
    // padding-left: 24px;
    padding-top: 12px;
  }
}
.addIcon {
  width: 32px;
  height: 32px;
  border: 1px solid #e3e6eb;
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  &-icon {
    width: 16px;
    height: 16px;
  }
}
.name-text {
  color: var(--text-kyy-color-text-2, #516082);
  text-align: center;

  /* kyy_fontSize_1/regular */
  font-family: PingFang SC;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px; /* 166.667% */
}
:deep(.space-value .t-radio-group) {
  display: flex;
  flex-direction: column;
  align-items: start;
  row-gap: 12px;
}
</style>
