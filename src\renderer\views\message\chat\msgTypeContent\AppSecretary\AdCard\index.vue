<template>
  <AppCard
    :is-loading="isLoading"
    :is-load-error="isLoadError"
    :refresh-cb="refreshCb"
    v-bind="$attrs"
  >
    <AppCardHeader v-if="title" style="font-size: 16px" :theme="getHeaderTheme()">
      {{ title }}
    </AppCardHeader>

    <AppCardBody>
      <div class="flex items-center gap-4 mb-12">
        <img class="w-24 h-24 rounded-full" :src="data?.header?.team?.logo || BizUnion">
        <div class="flex-1 overflow-hidden ellipsis-1">{{ data?.header?.team?.name || "" }}</div>
      </div>
      <div class="mb-12">{{ data?.content?.title }}</div>
      <template v-if="[22008].includes(scene)">
        <template v-for="item in data?.content?.body" :key="item.key">
          <div class="text-#828DA5 mb-4">{{ item.key }}</div>
          <div class="flex mb-12">
            {{ item.value }}
          </div>
        </template>
      </template>
      <div v-if="[5037, 22008].includes(scene)" class="divider" />
    </AppCardBody>

    <AppCardFooter>
      <t-button class="w-full" variant="outline" @click="onclick">{{ t("im.public.detail") }}</t-button>
    </AppCardFooter>
  </AppCard>
</template>

<script setup lang="ts">
// api 文档 https://app.apifox.com/project/2360511

// 需求地址： https://app.mockplus.cn/app/share-371f1ff252873c6e88707b971d8ceb1fshare-4pstxjE3T/comment/R0Br6N31p/q5L1O7p-yF6
// 22001: 平台成员新建待审核广告
// 22002: 平台成员取消广告
// 22003: 广告审核超时提醒
import { PropType, computed, ref } from 'vue';
import { AppCard, AppCardBody, AppCardHeader, AppCardFooter } from '@renderer/views/message/chat/MessageAppCard';
import { useI18n } from 'vue-i18n';
import { goWorkerToPlatform } from '@renderer/utils/auth';
import { MessageToSave } from 'customTypes/message';
import BizUnion from '@/assets/im/biz_union.png';

const { t } = useI18n();

const isLoading = ref(false);
const isLoadError = ref(false);

const props = defineProps({
  msg: { type: Object as PropType<MessageToSave>, required: true },
});
/** 标题 */
const title = computed(() => {
  /**
   * 1商协 2政企 3CBD
   */
  const platform_type = props.msg.contentExtra?.data.extend?.platform_type;
  let title = '';
  switch (platform_type) {
    case 1:
      title = t('im.public.biz');
      break;
    case 2:
      title = t('im.public.government');
      break;
    case 3:
      title = t('member.digital.c');
      break;
    case 4:
      title = t('niche.szsq');
      break;
    case 5:
      title = t('niche.szgx');
      break;
    default:
      break;
  }
  return title;
});

const scene = computed(() => props.msg.contentExtra?.scene);

const data = computed(() => props.msg.contentExtra?.data);

const extend = computed(() => props.msg.contentExtra?.data?.extend as {
    /** 广告id */
    ad_id: number;
    /** 组织标识 */
    team_id: string;
    /** 1商协 2政企 3CBD */
    platform_type: 1 | 2 | 3;
    /** 1未开始 2投放中 3结束 4终止 5取消 6待审核 7拒绝 8超时未审核 */
    status: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8;
  });

const getHeaderTheme = () => {
  // 22004: 广告审核不通过
  // 22005: 广告审核通过
  // 22006: 广告超时未审核
  const theme = { 22004: 'primary', 22005: 'primary', 22006: 'primary' }[scene.value] || 'primary';
  return theme as any;
};

const goToadAdmin = async (teamId, paramsQuery?) => {
  // 数智工场跳转的数字平台管理端的单页详情
  //   goWorkerToPlatform(teamId, "/workBenchIndex/AdDetails", {
  //     id: paramsQuery.id,
  //     title: "广告详情",
  //     type: 23,
  //     uuid:'cbd'
  //   },'workBench');
  /**
   * munber 21 politics 20  cbd 23  association 28
   * 1商协 2政企 3CBD
   */
  const platform_type = props.msg.contentExtra?.data.extend?.platform_type;
  let type = null;
  let uuid = null;

  switch (platform_type) {
    case 1:
      type = 21;
      uuid = 'member';
      break;
    case 2:
      type = 20;
      uuid = 'politics';
      break;
    case 3:
      type = 23;
      uuid = 'cbd';
      break;
    case 4:
      type = 28;
      uuid = 'association';
      break;
    default:
      break;
  }
  goWorkerToPlatform(
    teamId,
    `/digitalPlatformIndex/${uuid}_adDetails`,
    {
      id: paramsQuery.id,
      title: t('ad.ggxq'),
      type,
      uuid,
    },
    'digital_platform',
  ); // 已经正常

  // goWorkerToPlatform(teamId, "/workBenchIndex/AdDetails", {
  //   id: paramsQuery.id,
  //   title: "广告详情",
  //   type: type,
  //   uuid:'cbd'
  // },'workBench');//已经正常
};

const onclick = () => {
  const ad_id = extend.value.ad_id;
  const team_id = extend.value.team_id;
  console.log('msg-app-ad', ad_id, team_id, extend.value);
  goToadAdmin(team_id, {
    id: ad_id,
  });

  // /**
  //  * 1商协 2政企 3CBD
  //  */
  //  const platform_type =  data.value.extend?.platform_type;

  // if (platform_type === 1) {
  //     goToAdminMember(team_id, {
  //         origin: 'message',
  //         redirect: url,
  //     })
  // }
  // if (platform_type === 2) {
  //     goToAdminPolitics(team_id, {
  //         origin: 'message',
  //         redirect: url,
  //     })
  // }
  // if (platform_type === 3) {
  //     goToAdminCBD(team_id, {
  //         origin: 'message',
  //         redirect: url,
  //     })
  // }
};
</script>

<style lang="less" scoped>
.divider {
  margin-top: 8px;
  display: block;
  height: 1px;
  background: var(--divider-kyy_color_divider_light, #eceff5);
}

.square-link {
  color: #4d5eff;
  cursor: pointer;
}
.popularize-company-logo {
  display: block;
  width: 24px;
  height: 24px;
  border-radius: 100%;
  overflow: hidden;
}
.popularize-company-name {
  font-family: PingFang SC;
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  color: #1a2139;
}
.popularize-dynamic-title {
  font-family: PingFang SC;
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  color: #828da5;
}
.popularize-dynamic-info {
  font-size: 14px;
  font-weight: 400;
  line-height: 26px;
  color: #1a2139;
}
.popularize-msg-wrap {
  display: flex;
  flex-direction: row;
  padding: 12px 16px;
  background-color: #f5f8fe;
  border-radius: 8px;
  justify-content: center;
  align-items: center;
  gap: 15px;
  cursor: pointer;
}
.popularize-msg-img {
  display: block;
  width: 100%;
  height: 100%;
}
.popularize-msg-text-wrap {
  flex-grow: 2;
  display: flex;
  align-items: center;
  flex-direction: column;
  width: 0;
  gap: 4px;
}
.popularize-msg-text {
  width: 100%;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  font-family: PingFang SC;
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  color: #1a2139;
}
.popularize-msg-text-line-1 {
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  & + & {
    color: #828da5;
  }
}

.popularize-msg-img-wrap {
  position: relative;
  width: 72px;
  height: 72px;
  border-radius: 6px;
  flex-shrink: 0;
  overflow: hidden;
}
.popularize-msg-img-num {
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  z-index: 2;
  bottom: 0;
  right: 0;
  height: 20px;
  min-width: 20px;
  padding: 0 5px;
  border-radius: 4px 0 0 0;
  font-family: PingFang SC;
  font-size: 14px;
  font-weight: 600;
  line-height: 19.6px;
  color: #fff;
  background-color: rgba(0, 0, 0, 0.3);
}
.popularize-msg-img-play {
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  z-index: 2;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  margin: auto;
  width: 36px;
  height: 36px;
  border-radius: 100%;
  background-color: rgba(0, 0, 0, 0.2);
}
.play-icon,
.books-icon {
  width: 16px;
  height: 16px;
}
.popularize-msg-img-books {
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  z-index: 2;
  top: 0;
  bottom: 0;
  right: 0;
  width: 20px;
  height: 20px;
  left: 52px;
  border-radius: 0px 8px 0px 8px;
  background-color: rgba(0, 0, 0, 0.2);
}
</style>
