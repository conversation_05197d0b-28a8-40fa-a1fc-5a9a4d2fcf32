<template>
  <t-dialog
    class="post-preview-dialog"
    :visible="visible"
    :header="false"
    :footer="false"
    :close-btn="false"
    @close="closeModal"
    width="872px"
  >
    <div v-if="ready" slot="body">
      <div class="preview-container">
        <div class="preview-header">
          {{ t("forum.postDetailPreview") }}
          <img class="close-btn" src="@/assets/<EMAIL>" alt="" @click="closeModal" />
        </div>

        <div class="preview-scroll" @scroll="onPreviewContainerScroll">
          <div class="preview-body">
            <div class="published-user-box">
              <Avatar
                avatar-size="44px"
                :image-url="source.owner.avatar ?? ''"
                :user-name="source.owner.name"
                round-radius
              />
              <div>
                <div class="published-username">{{ source.owner.name }}</div>
                <div v-if="source.post.postedAt" class="published-time">
                  {{ dayjs(source.post.postedAt).format("YYYY-MM-DD HH:mm") }}
                </div>
              </div>
            </div>

            <div class="post-content">
              <article class="post-content-text">
                {{ source.post.text }}
              </article>

              <div v-if="source.post.content?.objects?.length" class="post-content-pictures">
                <VideoDisplay
                  v-if="source.post.postType === 'VIDEO'"
                  :video="source.post.content.objects[0].url"
                  :post="source.post"
                  forum
                />
                <ImageGrid v-else :list="imageUrlList" :post="source.post" forum />
              </div>

              <div class="post-content-topic" v-if="source.topics.length">
                <img class="close-btn" src="@/assets/digital/icon/topic-gray.png" alt="" />
                <span>{{ source.topics[0].name }}</span>
              </div>
            </div>
          </div>

          <div class="preview-footer">
            <div class="comment-sort-row">
              <div class="comment-sort-box">
                <div
                  class="sort-toggle-button"
                  :class="!commentQueryParams.newCommentFirst ? 'active' : ''"
                  @click="toggleNewCommentFirst(false)"
                >
                  {{ t("forum.default") }}
                </div>
                <div class="comment-sort-divider"></div>
                <div
                  class="sort-toggle-button"
                  :class="commentQueryParams.newCommentFirst ? 'active' : ''"
                  @click="toggleNewCommentFirst(true)"
                >
                  {{ t("forum.new") }}
                </div>
              </div>
            </div>

            <t-loading :text="t('forum.loading')" size="small" :loading="commentLoading" showOverlay>
              <ul v-if="commentList.length" class="comment-list">
                <li v-for="comment in commentList" :key="comment.comment.comment.id" class="comment-item">
                  <Avatar
                    avatar-size="32px"
                    :image-url="comment.comment.owner.avatar ?? ''"
                    :user-name="comment.comment.owner.name"
                    round-radius
                  />
                  <div>
                    <span class="comment-username">{{ comment.comment.owner.name }}</span>
                    <p class="comment-content">
                      {{ comment.comment.comment.content }}
                    </p>
                    <div class="comment-publish-box">
                      <span v-if="comment.comment.comment.createdAt">{{
                          dayjs(comment.comment.comment.createdAt).format("YYYY-MM-DD HH:mm")
                        }}</span>
                      <span v-if="comment.comment.comment.ipRegion"
                      >{{ t("forum.from") }}{{ comment.comment.comment.ipRegion }}</span
                      >
                    </div>

                    <ul class="comment-list">
                      <li
                        v-for="childrenComment in comment.comment.children"
                        :key="childrenComment.comment.id"
                        class="comment-item children-item"
                      >
                        <Avatar
                          avatar-size="32px"
                          :image-url="childrenComment.owner.avatar ?? ''"
                          :user-name="childrenComment.owner.name"
                          round-radius
                        />
                        <div>
                          <span class="comment-username">{{ childrenComment.owner.name }}</span>
                          <p class="comment-content">
                            {{ childrenComment.comment.content }}
                          </p>

                          <div v-if="childrenComment.replyComment" class="comment-reply-box">
                            <div class="symbol" />
                            <div v-if="childrenComment.replyComment.deleted">{{ t("square.post.commentDeleted") }}</div>
                            <div v-else>
                              {{ childrenComment.replyComment.owner.name }}：{{
                                childrenComment.replyComment.comment.content
                              }}
                            </div>
                          </div>

                          <div class="comment-publish-box">
                            <span v-if="childrenComment.comment.createdAt">{{
                                dayjs(childrenComment.comment.createdAt).format("YYYY-MM-DD HH:mm")
                              }}</span>
                            <span v-if="childrenComment.comment.ipRegion"
                            >{{ t("forum.from") }}{{ childrenComment.comment.ipRegion }}</span
                            >
                          </div>
                        </div>
                      </li>
                    </ul>

                    <template v-if="comment.comment.comment.replies > comment.comment.children.length">
                      <t-loading
                        v-if="comment.comment.replyLoading"
                        :text="$t('square.loading1')"
                        size="small"
                        class="ml-44"
                      />

                      <div v-else class="load-more" @click="loadChildrenComments(comment)">
                        {{ t("square.action.unfold") }}
                        {{ comment.comment.comment.replies - comment.comment.children.length }} {{ $t("square.piece1")
                        }}{{ $t("square.post.reply") }}
                        <iconpark-icon name="iconarrowdwon" class="icon" />
                      </div>
                    </template>
                  </div>
                </li>
              </ul>

              <Empty v-else-if="!commentLoading" class="mb-24" :tip="t('square.post.noCommentTip')" />
            </t-loading>
          </div>
        </div>
      </div>
    </div>

    <t-skeleton v-else />
  </t-dialog>
</template>

<script setup lang="ts">
import { reactive, ref, computed } from "vue";
import { useI18n } from "vue-i18n";
import dayjs from "dayjs";
import { getManagerPostCommentList, getManagerPostDetail } from "@/api/uni/api/forumAdminApi";
import Empty from "@/views/digital-platform/forum/components/Empty.vue";
import ImageGrid from "@/views/square/components/ImageGrid.vue";
import VideoDisplay from "@/views/square/components/VideoDisplay.vue";
import Avatar from "@/components/kyy-avatar/index.vue";

const { t } = useI18n();

const visible = ref(false);

const ready = ref(false);

const source = ref({
  post: {},
  owner: {},
  topics: {},
});

// 帖子图片资源列表
const imageUrlList = computed(() => source.value.post.content?.objects.map((image) => image.url) ?? []);

// 评论加载状态
const commentLoading = ref(false);

// 评论查询参数
const commentQueryParams = reactive({
  nextPageToken: null,
  pageSize: 20,
  newCommentFirst: false,
});

// 评论列表
const commentList = ref([]);

const openModal = async (row) => {
  ready.value = false;
  visible.value = true;

  const resDetail = await getManagerPostDetail(row.post.id);
  source.value = resDetail.data.data.post;

  commentList.value = [];
  commentQueryParams.nextPageToken = null;

  ready.value = true;

  commentLoading.value = true;
  loadComments();
};

// 加载评论列表
const loadComments = async () => {
  const resComments = await getManagerPostCommentList({
    resource_id: source.value.post.id,
    new_comment_first: commentQueryParams.newCommentFirst,
    "page.next_page_token": commentQueryParams.nextPageToken,
    "page.size": commentQueryParams.pageSize,
  });

  const {
    data: { page, comments },
  } = resComments.data;

  // 加入二级评论
  comments.forEach(({ comment, firstReply }) => {
    comment.children = firstReply ? [firstReply] : [];
    comment.replyLoading = false;
    comment.childrenNextPageToken = null;
  });

  commentQueryParams.nextPageToken = page.nextPageToken || null;

  commentList.value = [...commentList.value, ...comments];

  if(commentLoading.value){
    commentLoading.value = false;
  }
};

// 加载子评论
const loadChildrenComments = async ({ comment }) => {
  comment.replyLoading = true;

  const resChildrenComments = await getManagerPostCommentList({
    resource_id: source.value.post.id,
    new_comment_first: commentQueryParams.newCommentFirst,
    "page.next_page_token": comment.childrenNextPageToken,
    "page.size": commentQueryParams.pageSize,
    parent_comment: comment.comment.id,
  });

  comment.replyLoading = false;

  const {
    data: { page, comments: childrenComments },
  } = resChildrenComments.data;

  // 过滤已存在数据并插入
  const ids = comment.children.map((childrenComment) => childrenComment.comment.id);
  comment.children = [
    ...comment.children,
    ...childrenComments
      .map(({ comment }) => comment)
      .filter((childrenComment) => !ids.includes(childrenComment.comment.id)),
  ];
  comment.childrenNextPageToken = page.nextPageToken || null;
};

// 切换默认/最新状态
const toggleNewCommentFirst = async (newCommentFirst) => {
  commentList.value = [];
  commentQueryParams.nextPageToken = null;
  commentQueryParams.newCommentFirst = newCommentFirst;
  commentLoading.value = true;
  await loadComments();
};

// 容器滚动事件
const onPreviewContainerScroll = async (e) => {
  if (!commentQueryParams.nextPageToken) {
    return;
  }
  const { scrollTop, clientHeight, scrollHeight } = e.target;
  if (scrollTop + clientHeight === scrollHeight) {
    // 滚动到底加载下一页
    await loadComments();
  }
};

const closeModal = () => {
  visible.value = false;
};

defineExpose({
  openModal,
});
</script>

<style lang="less">
.post-preview-dialog {
  .t-dialog {
    padding: 16px 0 0 0;
  }

  .t-dialog__body {
    margin-right: 6px;
  }
}
</style>

<style lang="less" scoped>
.preview-container {
  height: calc(100vh - 148px);
  display: flex;
  flex-direction: column;

  .preview-scroll {
    margin-top: 16px;
    padding: 0 24px;
    flex: 1;
    overflow: auto;

    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      border: none;
      background-clip: content-box;
      background-color: var(--td-scrollbar-color);
      border-radius: 11px;
    }

    &::-webkit-scrollbar-track {
      background-color: transparent;
    }
  }

  .preview-header {
    text-align: center;
    color: var(--kyy_color_modal_title, #1a2139);
    font-size: 16px;
    font-weight: 600;
    line-height: 24px;
    position: relative;

    .close-btn {
      cursor: pointer;
      position: absolute;
      right: 18px;
      top: 50%;
      transform: translateY(-50%);
      width: 24px;
      height: 24px;
    }
  }

  .preview-body {
    padding-bottom: 12px;
    border-bottom: 1px solid #eceff5;

    .published-user-box {
      display: flex;
      align-items: center;
      gap: 12px;

      .published-username {
        color: var(--text-kyy_color_text_1, #1a2139);
        font-size: 16px;
        font-weight: 600;
        line-height: 24px;
      }

      .published-time {
        color: var(--text-kyy_color_text_3, #828da5);
        line-height: 22px;
      }
    }

    .post-content {
      margin-top: 12px;

      .post-content-text {
        color: #44494f;
        line-height: 22px;
      }

      .post-content-pictures {
        margin-top: 12px;
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
      }

      .post-content-topic {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        border-radius: 4px;
        background: var(--bg-kyy_color_bg_deep, #f5f8fe);
        margin-top: 8px;
        padding: 4px 8px;
        color: var(--text-kyy_color_text_3, #828da5);
        line-height: 22px;

        img {
          width: 20px;
          height: 20px;
        }
      }
    }
  }

  .preview-footer {
    margin-top: 12px;

    .comment-sort-row {
      display: flex;
      justify-content: flex-end;

      .comment-sort-box {
        display: flex;
        align-items: center;

        .sort-toggle-button {
          cursor: pointer;
          color: var(--color-button_text_secondray-kyy_color_button_text_secondray_font_default, #516082);

          &.active {
            color: var(--color-button_text_brand-kyy_color_button_text_brand_font_default, #4d5eff);
            cursor: default;
          }
        }

        .comment-sort-divider {
          margin: 0 8px;
          height: 16px;
          width: 1px;
          background: #eceff5;
        }
      }
    }

    .comment-loading-box {
      height: 300px;
    }

    .comment-list {
      margin-top: 12px;

      .comment-item {
        display: flex;
        gap: 12px;
        padding: 8px;

        & + .comment-item {
          border-top: 1px solid #eceff5;
        }

        &.children-item {
          padding-left: 0;
          border-top: none;
        }

        .comment-user-avatar {
          width: 32px;
          height: 32px;
          border-radius: 50%;
        }

        .comment-username {
          color: var(--text-kyy_color_text_3, #828da5);
        }

        .comment-content {
          margin-top: 4px;
          color: var(--text-kyy_color_text_1, #1a2139);
        }

        .comment-reply-box {
          display: flex;
          gap: 8px;
          align-items: center;
          margin-top: 4px;
          color: var(--text-kyy_color_text_3, #828da5);

          .symbol {
            width: 2px;
            height: 14px;
            background: #828da5;
          }
        }

        .comment-publish-box {
          margin-top: 8px;
          color: var(--text-kyy_color_text_3, #828da5);
          display: flex;
          gap: 12px;
        }

        .load-more {
          margin-left: 44px;
          display: flex;
          height: 32px;
          min-height: 32px;
          max-height: 32px;
          padding: 0 4px 0 8px;
          justify-content: center;
          align-items: center;
          gap: 4px;
          border-radius: var(--radius-kyy_radius_button_s, 4px);
          color: var(--text-kyy_color_text_2, #516082);
          background: var(--bg-kyy_color_bg_deep, #f5f8fe);
          cursor: pointer;
          .icon {
            color: var(--text-kyy_color_text_2, #516082);
            font-size: 20px;
          }
        }
      }
    }
  }
}
</style>
