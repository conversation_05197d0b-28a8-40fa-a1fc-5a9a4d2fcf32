<template>
	<t-space direction="vertical" class="page-container">
		<div class="head-title">广告白名单</div>
		<t-button theme="primary" @click="addPlatformWays">添加数字平台</t-button>
		<t-form
			ref="form"
			:data="formData"
			layout="inline"
			label-align="right"
			reset-type="initial"
			@reset="onReset"
			@submit="onSearch"
		>
			<t-form-item label="组织名称" name="team_name">
				<t-input v-model="formData.team_name" theme="column" placeholder="请输入" class="w-260!" />
			</t-form-item>
			<t-form-item label="组织ID" name="team_id">
				<t-input v-model="formData.team_id" theme="column" placeholder="请输入" class="w-260!" />
			</t-form-item>
			<t-form-item label="平台类型" name="digital_uuid">
				<t-select v-model="formData.digital_uuid" class="w-260">
					<t-option v-for="(item, key) in statusList" :key="key" :label="item.label" :value="item.value" />
				</t-select>
			</t-form-item>
			<t-form-item label-width="0">
				<t-space size="small">
					<t-button theme="primary" type="submit">查询</t-button>
					<t-button variant="outline" type="reset">重置</t-button>
				</t-space>
			</t-form-item>
		</t-form>
		<t-table
			row-key="id"
			:loading="loading"
			:data="tableData"
			:columns="columns"
			:pagination="pagination.total > 10 ? pagination : false"
			cell-empty-content="--"
			class="person-table"
			@page-change="onPageChange"
		>
			<template #team_name="{ row }">
				<div style="display: flex; align-items: center">
					<img v-if="row.team_logo" :src="row.team_logo" alt="" style="width: 50px; height: 50px" />
					<img v-else src="../../../assets/<EMAIL>" alt="" />
					{{ row.team_name }}
				</div>
			</template>
			<template #digital_uuid="{ row }">
				{{ status_val(row.digital_uuid) }}
			</template>
			<template #actions="{ row }">
				<a href="javascript:;" @click="deleteWays(row)">移除</a>
			</template>
		</t-table>
	</t-space>
	<addPlatform ref="addPlatformRef" @update="getList" />
</template>
<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';
import { whiteList, whiteDel } from '@/api/advertisement/index.ts';
import addPlatform from '@/pages/advertisement/components/addPlatform.vue';

const loading = ref(false);
const tableData = ref([]);
const addPlatformRef = ref(null);
const statusList = ref([
	{
		label: '数字商协',
		value: 'member',
	},
	{
		label: '数字政企',
		value: 'government',
	},
	{
		label: '数字社群',
		value: 'association',
	},
	{
		label: '数字CBD',
		value: 'cbd',
	},
   {
		label: '数字高校',
		value: 'uni',
	},
]);
const status_val = (status) => {
	switch (status) {
		case 'member':
			return '数字商协';
		case 'government':
			return '数字政企';
		case 'association':
			return '数字社群';
		case 'cbd':
			return '数字CBD';
      		case 'uni':
			return '数字高校';
		default:
			return '--';
	}
};
const columns = [
	{ title: '组织名称', colKey: 'team_name', width: 150, align: 'left' },
	{ title: '组织ID', colKey: 'team_id', width: 150, align: 'left' },
	{ title: '平台状态', colKey: 'digital_uuid', width: 150, align: 'left' },
	{ title: '平台专属名称', colKey: 'exclusive_name', width: 150, align: 'left' },
	{ title: '操作', colKey: 'actions', width: 150, align: 'left' },
];
const pagination = {
	current: 1,
	pageSize: 10,
	total: 0,
	showJumper: true,
	onChange: (pageInfo) => {
		console.log('pagination.onChange', pageInfo);
		pagination.current = pageInfo.current;
		pagination.pageSize = pageInfo.pageSize;

		getList();
	},
};
const formData = ref({
	team_name: undefined,
	team_id: undefined,
	digital_uuid: undefined,
});

onMounted(() => {
	getList();
});

const onReset = () => {
	formData.value = {
		team_name: undefined,
		team_id: undefined,
		digital_uuid: undefined,
	};
	pagination.current = 1;
	getList();
};
const onSearch = () => {
	pagination.current = 1;
	getList();
};

const onPageChange = (pageInfo: any) => {
	console.log('onPageChange', pageInfo);
	pagination.current = pageInfo.current;
	pagination.pageSize = pageInfo.pageSize;
};

const getList = () => {
	loading.value = true;
	const params = {
		page_size: pagination.pageSize,
		page: pagination.current,
		...formData.value,
	};
	whiteList(params).then((res) => {
		console.log(res);
		if (res.data.code === 0) {
			loading.value = false;
			tableData.value = res.data.data.list;
			pagination.total = res.data.data.count;
		} else {
			MessagePlugin.error(res.data.message);
		}
	});
};

const deleteWays = (val) => {
	const myDialog = DialogPlugin({
		header: '确定移除白名单？',
		theme: 'info',
		body: '数字平台移除白名单后，将会展示另可广告素材，是否移除？',
		className: 't-dialog-new-class1 t-dialog-new-class2',
		style: 'color: rgba(0, 0, 0, 0.6)',
		onConfirm: () => {
			whiteDel({
				team_id: val.team_id,
			}).then((res) => {
				if (res.data.code === 0) {
					MessagePlugin.success('移除成功');
					getList();
				} else {
					MessagePlugin.error(res.data.message);
				}
			});
			myDialog.hide();
		},
		onCancel: () => {
			myDialog.hide();
		},
	});
};

const addPlatformWays = () => {
	const ids = tableData.value.map((item) => item.team_id);
	addPlatformRef.value.open(ids);
};
</script>
<style lang="less" scoped>
.page-container {
	background-color: #fff;
	padding: 16px 24px;
	display: flex;
	flex-direction: column;
	height: calc(100% - 30px);
}
.head-title {
	height: 24px;
	font-size: 16px;
	font-weight: 700;
	color: #13161b;
	line-height: 24px;
}
.twelve-name {
	width: 180px;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}
</style>
