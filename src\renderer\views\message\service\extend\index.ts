import { computed, ref } from 'vue';
import { defineStore } from 'pinia';

import { getNicheStatus, commonUpdateMsgStatus } from './statusUtils';
import { useImToolStore } from '../../tool/service/tool';
import { ImToolContainer } from '../../tool/service/type';
import { useMessageStore } from '../store';

export const useChatExtendStore = defineStore('chat-extend', () => {
  const approvalRefreshKey = ref(0);
  const dialogInfo = ref<{ type: MsgDetailDrawerType, msg?: MessageToSave, link?: string }>(null);

  // 云盘链接
  const squareHelperVisible = computed(() => dialogInfo.value && dialogInfo.value.type === 'square-helper');

  const approvAutoAction = ref<'' | 'agree' | 'reject'>('');
  const approveData = computed(() => {
    const extra = dialogInfo.value?.msg?.contentExtra;
    if (!extra || !extra.data) {
      return null;
    }

    if (extra.contentType === 'APP_APPROVAL') {
      return {
        approval_id: extra.data?.extend?.approval_id,
        approvalId: extra.data?.extend?.approval_id,
        teamId: extra.data?.extend?.team_id,
      };

    } if (extra.contentType === 'approve' || extra.contentType === 'approve_to_rel_people') {
      return {
        approval_id: extra.data?.approveId,
        approvalId: extra.data?.approveId,
        teamId: extra.data?.teamId,
      };
    }
    return null;
  });

  const msgOrderDetailVisible = computed(() => dialogInfo.value && dialogInfo.value.type === 'order');
  const msgVcardDetailVisible = computed(() => dialogInfo.value && dialogInfo.value.type === 'vcard-chat-dialog');
  const msgSendVcardDetailVisible = computed(() => dialogInfo.value && dialogInfo.value.type === 'send-vcard-chat-dialog');

  const msgInvoiceVisible = computed(() => dialogInfo.value && dialogInfo.value.type === 'invoice');
  const msgInvoiceRefuseVisible = computed(() => dialogInfo.value && dialogInfo.value.type === 'invoice-refuse');
  const pbDetailVisible = computed(() => dialogInfo.value && dialogInfo.value.type === 'pb-detail');
  const fcDetailVisible = computed(() => dialogInfo.value && dialogInfo.value.type === 'fc-detail');

  const squareInviteVisible = computed(() => dialogInfo.value && dialogInfo.value.type === 'square-invite');
  const squareInviteCode = computed(() => {
    const data = dialogInfo.value?.msg?.contentExtra?.data;
    return data?.inviteCode;
  });

  const albumExpireVisible = computed(() => dialogInfo.value && dialogInfo.value.type === 'album-expire');

  const comboVisible = computed(() => dialogInfo.value && dialogInfo.value.type === 'combo');
  const comboSquareId = computed(() => {
    const data = dialogInfo.value?.msg?.contentExtra as SquareHelperMsgData;
    return data?.data?.extend?.square_id;
  });

  const comboGroupId = computed(() => {
    const data = dialogInfo.value?.msg?.contentExtra as SquareHelperMsgData;
    return data?.data?.extend?.group_id;
  });

  const bizOpportunityDetailVisible = computed(() => dialogInfo.value && dialogInfo.value.type === 'biz-opportunity');

  // 商机分享id
  const bizOpportunityId = computed(() => {
    const data = dialogInfo.value?.msg?.contentExtra?.data;
    return data?.uuid;
  });

  const bizOpportunityHelperDetailVisible = computed(() => dialogInfo.value && dialogInfo.value.type === 'biz-opportunity-helper');

  // 商机助手消息 id
  const bizOpportunityHelperId = computed(() => {
    const data = dialogInfo.value?.msg?.contentExtra?.data;
    return data?.extend;
  });

  const updateBizOpportunityHelperMsg = () => {
    getNicheStatus(dialogInfo.value?.msg);
  };

  const zhixingRemindVisible = computed(() => dialogInfo.value && dialogInfo.value.type === 'zhixing-remind');

  // 一些弹框详情改窄
  const isWidth376 = computed(() => ['vcard-chat-dialog', 'send-vcard-chat-dialog', 'zhixing-remind', 'consult-order-info',
    'consult-info', 'ad-info', 'digital-apply-info', 'cloud-disk-link', 'merged-msg-detail', 'activity-join-info',
    'naas-result', 'square-preview','shop-category-apply','shop-apply', 'culture-tourism', 'culture-apply'
  ].includes(dialogInfo?.value?.type));
  const zhixingRemindId = computed(() => {
    const scene = dialogInfo.value?.msg?.contentExtra?.scene;
    const data = dialogInfo.value?.msg?.contentExtra?.data;
    const msgType = dialogInfo.value?.msg?.contentExtra?.contentType;
    if (msgType === 'zx_remind_detail' || scene === 0) {
      return data?.openid;
    }
    const extend = data?.extend;
    try {
      const notice = JSON.parse(extend?.notice);
      return notice?.openid;

    } catch (error) {

    }

    return extend?.openid;

  });

  const zhixingNoteVisible = computed(() => dialogInfo.value && dialogInfo.value.type === 'zhixing-note');

  const zhixingNoteData = computed(() => ((dialogInfo.value && dialogInfo.value.type === 'zhixing-note') ? dialogInfo.value.msg.contentExtra?.data : {}));

  const zhixingManifestVisible = computed(() => dialogInfo.value && dialogInfo.value.type === 'zhixing-manifest');

  // 服务内容
  const serviceContentDetailPanel = computed(() => {
    if (dialogInfo.value?.type === 'serviceContentDetailPanel') {
      return dialogInfo.value?.info;
    }
    return '';
  });
  // 详情里不需要切换显示的抽屉
  const notNeedShowInIM = ['activity-join-info'];
  /**
     * @param drawer.history 历史消息
     * @param drawer.order 订单详情
     * @param drawer.invoice 发票详情
     * @param drawer.invoice-refuse 发票拒绝
     * @param drawer.combo 另可圈续费
     * @param msg 消息
     * @param scene 场景化
     */
  const showChatDialog = (type: MsgDetailDrawerType, msg?: MessageToSave, extend?: object) => {
    if (!notNeedShowInIM.includes(type)) {
      useMessageStore().updateMsgDetailById(msg, type);
    }
    useImToolStore().toggleTool(ImToolContainer.MsgDetail, { msg, extend });
    const chatDialogData = { type, msg, extend };
    dialogInfo.value = chatDialogData;
    console.log('====>showChatDialog', chatDialogData);

  };

  /**
   * 云盘链接
   * @param msg 原消息
   * @param link 链接
   */
  const showDiskLink = (msg: MessageToSave, link: string, extend?: object) => {
    useMessageStore().updateMsgDetailById(msg, 'cloud-disk-link', link);
    dialogInfo.value = { type: 'cloud-disk-link', msg, link };
    useImToolStore().toggleTool(ImToolContainer.MsgDetail, { msg, extend });
  };

  /**
   * 审批详情
   * @param type
   * @param msg
   * @param action
   */
  const showApproveDialog = (type: Extract<MsgDetailDrawerType, 'approve' | 'approve-helper' | 'approve-comment'>, msg: MessageToSave, action?: 'agree' | 'reject') => {
    approvAutoAction.value = action;
    approvalRefreshKey.value++;
    showChatDialog(type, msg);
  };

  const hideChatDialog = () => {
    hideDrawer();
  };

  const hideDrawer = () => {
    useImToolStore().closeTool(ImToolContainer.MsgDetail);
    dialogInfo.value = { type: 'cloud-disk-link', msg: null };
  };
  const showSendVcard = () => {
    useImToolStore().closeTool(ImToolContainer.MsgDetail);

    dialogInfo.value = { type: 'send-vcard-chat-dialog', msg: null };
    console.log('chfazheli', dialogInfo);

  };

  return {
    approvalRefreshKey,
    hideDrawer,
    showSendVcard,
    squareHelperVisible,

    dialogInfo,
    showChatDialog,
    hideChatDialog,
    showApproveDialog,

    approvAutoAction,
    approveData,
    msgVcardDetailVisible,
    msgSendVcardDetailVisible,
    msgInvoiceVisible,
    msgOrderDetailVisible,
    msgInvoiceRefuseVisible,

    bizOpportunityId,
    bizOpportunityDetailVisible,

    bizOpportunityHelperId,
    bizOpportunityHelperDetailVisible,
    updateBizOpportunityHelperMsg,

    squareInviteVisible,
    squareInviteCode,

    albumExpireVisible,

    comboVisible,
    comboSquareId,
    comboGroupId,

    zhixingRemindVisible,
    isWidth376,
    zhixingRemindId,

    zhixingNoteVisible,
    zhixingNoteData,
    zhixingManifestVisible,

    serviceContentDetailPanel,

    commonUpdateMsgStatus,
    showDiskLink,

  };
});
