<template>
  <t-dialog
    v-model:visible="visible"
    :z-index="2500"
    attach="body"
    class="dialogSet"
    width="560px"
  >
    <template #header>
      <div class="header">
        <!-- <svg class="iconpark-icon header-svg"><use href="#attention-6ebn71gl"></use></svg>
				<div class="header-title">提示</div> -->
        组织退出
      </div>
    </template>
    <template #body>
      <div class="toBody">
        <t-form
          ref="form"
          label-align="top"
          layout="inline"
          :rules="FORM_RULES"
          :data="formData"
          :colon="true"
          class="form"
        >
          <t-form-item :label="'退出时间'" name="exit_time" class="form-item">
            <t-date-picker
              v-model="formData.exit_time"
              :placeholder="$t('member.impm.input_12')"
              :maxlength="500"
              :format="'YYYY-MM-DD'"
              style="width: 100%"
              clearable
            />
            <!-- @change="changeDate" -->
            <!-- @blur="changeDate" -->
          </t-form-item>
          <t-form-item :label="'退出原因'" name="content">
            <t-textarea
              v-model="formData.content"
              class="form-item"
              :autosize="{ minRows: 4 }"
              clearable
              :maxlength="200"
              :placeholder="$t('member.impm.input_12')"
            />
          </t-form-item>
        </t-form>
      </div>
    </template>
    <template #closeBtn>
      <!-- <iconpark-icon name="close" size="16"></iconpark-icon> -->
      <!-- <svg class="iconpark-icon" style="width: 16px; height: 16px">
        <use href="#close" />
      </svg> -->
      <iconpark-icon name="iconerror" style="font-size: 24px"></iconpark-icon>
    </template>
    <template #footer>
      <div class="footer">
        <!-- <span class="check">
          <t-checkbox v-model="isremovePolitics">将会员从组织成员中移除</t-checkbox>
        </span> -->
        <span class="tips">
          <!-- {{ $t("member.manager.outVip") }} -->
          历史已退出组织可在组织管理筛选“已退出”后查看
        </span>
        <span />
        <span class="btns">
          <t-button
            theme="default"
            variant="outline"
            class="w-80"
            @click="onClose"
          >{{$t('member.impm.input_8')}}</t-button>
          <t-button theme="primary" class="ml-8 w-80" @click="onSave">{{$t('member.impm.input_9')}}</t-button>
        </span>
      </div>
    </template>
  </t-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, Ref, toRaw } from "vue";
import { MessagePlugin } from "tdesign-vue-next";

const isremovePolitics = ref(true);
const emits = defineEmits(["onSend"]);

// 从公司组织架构导入
// const onImportFromCompany = () => {
// 	emits('onImportFromCompany');
// };
// // 手工建立组织架构
// const onHandCreate = () => {
// 	emits('onHandCreate');
// };
const form = ref(null);
const formData = reactive({
  id: 0,
  content: "", // 退会原因
  exit_time: "" // 退会时间
});
const FORM_RULES = {
  content: [{ required: true, message: "请输入退会原因" }],
  exit_time: [{ required: true, message: "请选择退会时间" }]
};
const onChange = () => {};

const visible = ref(false);
const onOpen = (data?: any) => {
  form.value.reset();
  if (data) {
    formData.id = data.id;
  }
  visible.value = true;
};
const onClose = () => {
  visible.value = false;
};
// 确定发送消息
const onSave = () => {
  // if (!select.area) {
  //   MessagePlugin.error("请输入驳回原因");
  //   return;
  // }
  form.value
    .validate({ showErrorMessage: true })
    .then(async (validateResult) => {
      if (validateResult && Object.keys(validateResult).length) {
        console.log(formData);
      } else {
        emits("onSend", {
          ...toRaw(formData),
          isremovePolitics: toRaw(isremovePolitics.value)
        });
      }
    });
};

defineExpose({
  onOpen,
  onClose
});
</script>

<style lang="less" scoped>
.t-button + .t-button {
  margin-left: 0;
}
.header {
  display: flex;
  &-svg {
    width: 22px;
    height: 22px;
    color: #2069e3;
    margin-right: 8px;
  }
  &-title {
    font-size: 16px;

    font-weight: 700;
    color: #13161b;
  }
}
.toBody {
  .msg {
    margin-bottom: 16px;
    margin-left: 30px;
  }
  .btns {
    display: flex;
    margin-left: 30px;
    .btn {
      width: 320px;
      margin-bottom: 16px;
    }
  }
  .area {
    // margin-left: 30px;
    width: auto !important;
    margin-top: 10px;
  }
}
// :deep(.t-dialog--default) {
// 	padding-bottom: 0;
// }

.footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 5px;
  // border-top: 1px solid #e3e6eb;
  .btns {
    display: flex;
    gap: 8px;
  }
}

.t-dialog__body {
}

.form {
  &-item {
    width: 100%;
  }
}

:deep(.t-form-inline) .t-form__item {
  width: 100%;
  margin-right: 0;
}

.tips {
  color: var(--text-kyy_color_text_3, #828DA5);

  /* kyy_fontSize_2/regular */
  font-family: PingFang SC;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
  text-align: left;
}
</style>
