<template>
  <div>
    <div class="inline-flex items-center gap-4 cursor-pointer" @click="goBack">
      <iconpark-icon class="text-20 text-[#828DA5]" name="iconarrowlift"></iconpark-icon>
      <span class="text-[#516082]">{{ t('account.back') }}</span>
    </div>
    <div class="info">
      <div class="text-20 text-[#1A2139] font-bold leading-28 text-center">{{ t('account.pwSettingNew') }}</div>
      <t-form
        class="set-password-form"
        ref="pwForm"
        label-align="top"
        :data="pw"
        :rules="pwRules"
        :requiredMark="false"
        resetType="initial"
      >
        <t-form-item name="password">
          <t-input
            v-model="pw.password"
            :spellcheck="false"
            type="password"
            :placeholder="t('account.inputPw')"
            :maxlength="16"
          />
        </t-form-item>
        <t-form-item class="mt-12" name="passwordConfirm" :help="t('account.pwCheckRule')">
          <t-input
            v-model="pw.passwordConfirm"
            :spellcheck="false"
            type="password"
            :placeholder="t('account.pwSettingNewConfirm')"
            :maxlength="16"
          />
        </t-form-item>
      </t-form>
      <t-button
        v-loading="loginLoading"
        class="login-btn"
        :disabled="loginBtnDisabled"
        block
        theme="primary"
        variant="base"
        @click="confirm"
        >{{ props.btnConfirm }}</t-button
      >
      <div v-if="props.showSkip" class="mt-12 py-3 text-center">
        <span class="cursor-pointer text-[#516082]" @click="skip">{{ t('account.skip') }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue';
import LynkerSDK from '@renderer/_jssdk';
import { useI18n } from 'vue-i18n';
import { encrypt } from '@/utils/myUtils';
import { debounce } from 'lodash';
import { setRegister } from "@/utils/auth";
const { ipcRenderer } = LynkerSDK;

const { t } = useI18n();
const props = defineProps({
  btnConfirm: {
    type: String,
    default: '',
  },
  showSkip: {
    type: Boolean,
    default: false,
  },
});
const loginLoading = ref(false);
const loginBtnDisabled = ref(true);
const pw = ref({
  password: '',
  passwordConfirm: '',
});
const pwForm = ref(null);

const rePassword = (val) => {
  return new Promise((resolve) => {
    const timer = setTimeout(() => {
      resolve(pw.value.password === pw.value.passwordConfirm);
      clearTimeout(timer);
    });
  });
};
const pwRules = ref({
  passwordConfirm: [
    {
      validator: () =>
        new Promise((resolve) => {
          const timer = setTimeout(() => {
            resolve(/^(?=.*[0-9])(?=.*[a-zA-Z])[0-9a-zA-Z!@#$%^&*()-_+=]{8,}$/.test(pw.value.password));
            clearTimeout(timer);
          });
        }),
      message: t('account.pwCheckRule'),
      trigger: 'submit'
    },
    { validator: rePassword, message: '密码不一致，请重新输入', type: 'error', trigger: 'submit' },
    {
      pattern: /^(?=.*[0-9])(?=.*[a-zA-Z])[0-9a-zA-Z!@#$%^&*()-_+=]{8,}$/,
      message: t('account.pwCheckRule'),
      type: 'error',
      trigger: 'submit',
    },
  ],
});
const emits = defineEmits(['back', 'confirm', 'skip']);
const confirm = debounce(() => {
  pwForm.value.validate().then((res) => {
    if (res === true) {
      emits('confirm', {
        password: encrypt(pw.value.password),
        passwordConfirm: encrypt(pw.value.passwordConfirm),
      });
    }
  });
}, 500);
const skip = () => {
  goBack();
  setRegister('false');
  emits('skip');
};
const goBack = () => {
  clear();
  document.removeEventListener('keydown', keyDown);
  emits('back');
};
onMounted(() => {
  document.addEventListener('keydown', keyDown);
});
const keyDown = (e) => {
  if (e.keyCode === 13 && !loginBtnDisabled.value) {
    confirm();
  }
};
const clear = () => {
  pw.value = {
    password: '',
    passwordConfirm: '',
  };
  loginBtnDisabled.value = true;
};
watch(
  pw,
  (newValue) => {
    if (newValue.password.length >= 6 && newValue.passwordConfirm.length >= 6) {
      loginBtnDisabled.value = false;
    } else {
      loginBtnDisabled.value = true;
    }
  },
  {
    deep: true,
  },
);
defineExpose({});
</script>

<style lang="less" scoped>
.pointer {
  cursor: pointer;
}
.back {
  margin-left: 48px;
  margin-top: 41px;
  position: relative;
  color: var(--text-kyy-color-text-3, #828da5);

  /* kyy_fontSize_2/regular */
  font-family: PingFang SC;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
  &:before {
    content: '';
    width: 5px;
    height: 5px;
    border-top: 2px solid #a1a2a4;
    border-left: 2px solid #a1a2a4;
    position: absolute;
    left: -15px;
    top: 50%;
    transform: translateY(-50%) rotate(-45deg);
  }
}
.info {
  margin-top: 16px;

  .login-btn {
    margin-top: 24px;
    height: 40px;
    font-size: 16px;

    color: #ffffff;
    line-height: 24px;
  }
}

:deep(.set-password-form) {
  margin-top: 24px;

  .t-input {
    height: 40px;
    border-color: #d5dbe4 !important;
    padding: 0 12px;

    .t-input__clear{
      .t-icon {
        color: #828da5 !important;
      }
    }
  }

  .t-form-item__passwordConfirm {
    .t-input__help {
      color: #828da5;
      margin-top: 4px;
      line-height: 20px;
    }

    .t-is-error {
      .t-input__help {
        display: none;
      }
    }
  }
}

.inline-flex {
  display: inline-flex;
}

.items-center {
  align-items: center;
}

.gap-4 {
  gap: 4px;
}

.cursor-pointer {
  cursor: pointer;
}

.text-20 {
  font-size: 20px;
}

.text-\[\#828DA5\] {
  color: #828da5;
}

.text-\[\#516082\] {
  color: #516082;
}

.info {
  /* 该类可能需要具体的布局样式，未提供 */
}

.font-bold {
  font-weight: bold;
}

.leading-28 {
  line-height: 28px;
}

.text-center {
  text-align: center;
}

.mt-12 {
  margin-top: 12px !important; /* 处理有!important修饰符的类 */
}

.login-btn {
  width: 100%;
}

.block {
  display: block;
}

.v-loading {
  /* 处理加载动画样式 */
}

.mt-12 {
  margin-top: 12px;
}

.py-3 {
  padding-top: 3px;
  padding-bottom: 3px;
}

.cursor-pointer {
  cursor: pointer;
}

.text-\[\#516082\] {
  color: #516082;
}

.text-\[\#1A2139\] {
  color: #1a2139;
}
</style>
