<template>
  <div>
    <t-dialog v-model:visible="visible" header="添加数字平台" width="900" @close="
				visible = false;
				init();
			">
      <t-form ref="form" :data="formData" layout="inline" label-align="right" reset-type="initial" @reset="onReset"
        @submit="onSearch">
        <t-form-item label="组织名称" name="fullname">
          <t-input v-model="formData.fullname" theme="column" placeholder="请输入" class="w-260!" />
        </t-form-item>
        <t-form-item label="平台类型" name="digital_uuid">
          <t-select v-model="formData.digital_uuid" class="w-260">
            <t-option v-for="(item, key) in statusList" :key="key" :label="item.label" :value="item.value" />
          </t-select>
        </t-form-item>
        <t-form-item label-width="0">
          <t-space size="small">
            <t-button theme="primary" type="submit">查询</t-button>
            <t-button variant="outline" type="reset">重置</t-button>
          </t-space>
        </t-form-item>
      </t-form>
      <t-table row-key="team_id" :loading="loading" :data="tableData" :columns="columns"
        :pagination="pagination.total > 10 ? pagination : false" cell-empty-content="--" class="person-table"
        :selected-row-keys="selectedRowKeys" @select-change="rehandleSelectChange" @page-change="onPageChange">
        <template #team_name="{ row }">
          <div style="display: flex; align-items: center">
            <img v-if="row.team_logo" :src="row.team_logo" alt="" style="width: 50px; height: 50px" />
            <img v-else src="../../../assets/<EMAIL>" alt="" />
            {{ row.team_name }}
          </div>
        </template>
        <template #digital_uuid="{ row }">
          {{ status_val(row.digital_uuid) }}
        </template>
        <template #actions="{ row }">
          <a href="javascript:;" @click="deleteWays(row)">移除</a>
        </template>
      </t-table>
      <template #footer>
        <div style="display: flex; justify-content: space-between">
          <div>已选： {{ selectedRowKeys.length }}个</div>
          <div>
            <t-button theme="default" @click="
								visible = false;
								init();
							">取消</t-button>
            <t-button theme="primary" @click="save">确定</t-button>
          </div>
        </div>
      </template>
    </t-dialog>
  </div>
</template>
<script lang="ts" setup>
  import { ref } from 'vue';
  import { MessagePlugin } from 'tdesign-vue-next';
  import { platformListAxios } from '@/api/businessOpportunity/niche-class.ts';
  import { whiteAdd } from '@/api/advertisement/index.ts';

  const visible = ref(false);
  const loading = ref(false);
  const tableData = ref([]);
  const selectedRowKeys = ref([]);
  const statusList = ref([
    {
      label: '数字商协',
      value: 'member',
    },
    {
      label: '数字政企',
      value: 'government',
    },
    {
      label: '数字社群',
      value: 'association',
    },
    {
      label: '数字CBD',
      value: 'cbd',
    },
    {
      label: '数字高校',
      value: 'uni',
    },
  ]);
  const status_val = (status) => {
    switch (status) {
      case 'member':
        return '数字商协';
      case 'government':
        return '数字政企';
      case 'association':
        return '数字社群';
      case 'cbd':
        return '数字CBD';
      case 'uni':
        return '数字高校';
      default:
        return '--';
    }
  };
  const columns = [
    {
      colKey: 'row-select',
      type: 'multiple',
      checkProps: ({ row, rowIndex }) => ({
        disabled: whiteList.value.includes(row.team_id),
      }),
      width: 50,
    },
    { title: '组织名称', colKey: 'team_name', width: 150, align: 'left' },
    { title: '组织ID', colKey: 'team_id', width: 150, align: 'left' },
    { title: '平台状态', colKey: 'digital_uuid', width: 150, align: 'left' },
  ];
  const pagination = {
    current: 1,
    pageSize: 10,
    total: 0,
    showJumper: true,
    onChange: (pageInfo) => {
      console.log('pagination.onChange', pageInfo);
      pagination.current = pageInfo.current;
      pagination.pageSize = pageInfo.pageSize;

      getList();
    },
  };
  const formData = ref({
    fullname: undefined,
    digital_uuid: undefined,
  });

  const whiteList = ref([]);
  const open = (arr) => {
    getList();
    visible.value = true;
    whiteList.value = JSON.parse(JSON.stringify(arr));
  };

  const onReset = () => {
    formData.value = {
      fullname: undefined,
      digital_uuid: undefined,
    };
    pagination.current = 1;
    getList();
  };
  const onSearch = () => {
    pagination.current = 1;
    getList();
  };

  const onPageChange = (pageInfo: any) => {
    console.log('onPageChange', pageInfo);
    pagination.current = pageInfo.current;
    pagination.pageSize = pageInfo.pageSize;
  };

  const getList = () => {
    loading.value = true;
    const params = {
      pageSize: pagination.pageSize,
      page: pagination.current,
      ...formData.value,
    };
    platformListAxios(params).then((res) => {
      console.log(res);
      if (res.data.code === 0) {
        loading.value = false;
        tableData.value = res.data.data.list;
        pagination.total = res.data.data.total;
        // tableData.value.forEach((item) => {
        // 	item.checked = whiteList.value.includes(item.team_id);
        // });
      } else {
        MessagePlugin.error(res.data.message);
      }
    });
  };

  const rehandleSelectChange = (keys: string[]) => {
    selectedRowKeys.value = keys;
    console.log(selectedRowKeys.value, 'selectedRowKeys.value');
  };

  const emit = defineEmits(['update']);
  const save = () => {
    if (selectedRowKeys.value.length === 0) {
      MessagePlugin.error('请选择组织');
      return;
    }
    const params = { team_arr: selectedRowKeys.value };
    whiteAdd(params).then((res) => {
      if (res.data.code === 0) {
        MessagePlugin.success('添加成功');
        visible.value = false;
        emit('update', true);
        init();
      }
    });
  };

  const init = () => {
    selectedRowKeys.value = [];
  };

  defineExpose({
    open,
  });
</script>
