<template>
  <t-drawer
    v-model:visible="visible"
    class-name="drawerSet"
    :header="$t('member.impm.set_8')"
    :z-index="1500"
    :on-confirm="onClickConfirm"
    :close-btn="true"
    :size="'472px'"
    :footer="null"
  >
    <template #closeBtn>
      <!-- <svg class="iconpark-icon" style="width: 16px; height: 16px">
        <use href="#close" />
      </svg> -->
      <iconpark-icon name="iconerror" style="font-size: 24px"></iconpark-icon>
    </template>

    <div v-if="memberData && visible" class="drawerSet-body">
      <t-table
        row-key="id"
        :columns="memberColumns"
        :pagination="pagination.total > 10 ? pagination : null"
        :data="memberData"
      >
        <template #main="{ row }">
          <div class="main_body">
            <kyy-avatar
              class="rd-10"
              :avatar-size="'46px'"
              :image-url="row?.logo"
              :user-name="row?.team_name"
              :shape="'circle'"
            />
            <t-tooltip :content="row.team_name">
              <div class="main_body-item">
                {{ row.team_name || "--" }}
              </div>
            </t-tooltip>
          </div>
        </template>
        <template #level_name="{ row }">
          <div>{{ $filters.isPeriodEmpty(row.level_name) }}</div>
        </template>
        <template #status="{ row }">
          <!-- <div :class="showClassStatus(row.status)">
            {{ showTextStatus(row.status) }}
          </div> -->
          <div :class="'status-box' + row.status">
            {{ showTextStatus(row.status) }}
          </div>
        </template>
        <!-- <template #type="{ row }">
              <div class="status">{{ showTextType(row.type) }}</div>
            </template> -->
        <!-- <template #pay_status="{ row }">
              <div class="status">{{ showTextFee(row.status) }}</div>
            </template> -->

        <template #empty>
          <div class="empty">
            <noData :text="$t('engineer.no_data')" />
          </div>
        </template>
      </t-table>
    </div>
  </t-drawer>
</template>

<script setup lang="ts">
import { ref, Ref, reactive, watch } from "vue";
import { MessagePlugin } from "tdesign-vue-next";
import {
  getMemberApplyListAxios,
  getMemberApplyRecordsAxios
} from "@renderer/api/uni/api/businessApi";
import { getResponseResult } from "@renderer/utils/myUtils";
import { useI18n } from "vue-i18n";
import noData from "@renderer/components/common/Empty.vue";
import KyyAvatar from "@/components/kyy-avatar/index.vue";
const { t } = useI18n();
const memberColumns = ref([
  { colKey: "main", title: "商协会名称", width: "296px", ellipsis: true },
  // { colKey: "type", title: "入会类型", width: "10%", ellipsis: true },
  // { colKey: "level_name", title: "会员级别", width: "10%", ellipsis: true },
  // { colKey: "apply_time", title: "申请时间", width: "13%", ellipsis: true },
  { colKey: "status", title: "申请状态", width: "128px", ellipsis: true }
  // { colKey: "pay_status", title: "会费状态", width: "10%", ellipsis: true },
  // { colKey: "operate", title: "操作", width: "10%", ellipsis: true }
]);
const statusOptions = [
  // 申请状态
  //   { label: "全部", value: 0 },
  { label: "待审核", value: 1 },
  { label: "已入会", value: 2 },
  { label: "已驳回", value: 3 }
];
const showClassStatus = (val) => {
  // 申请状态，1：待审核，2：已入会，3：已驳回
  let result = {};
  if (val === 1) {
    result = { wait: true };
  } else if (val === 2) {
    result = { success: true };
  } else if (val === 3) {
    result = { reject: true };
  }
  return result;
};
const showTextStatus = (val) => {
  const option = statusOptions.find((v: any) => v.value === val);
  if (option) {
    return option.label;
  }
  return "--";
};

const visible = ref(false);

const data = ref(null);
const emits = defineEmits(["reload"]);

watch(
  () => visible.value,
  (cur) => {}
);
const memberData = ref([]);
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
  onChange: (pageInfo) => {
    console.log("pagination.onChange", pageInfo);
    pagination.current = pageInfo.current;
    pagination.pageSize = pageInfo.pageSize;

    // getMemberList({});
    onSearch();
  }
});

const onSearch = () => {
  getMemberList({});
  // ipcRenderer.send("update-nume-index", 10);
};

// 获取入会申请列表
const getMemberList = async (params) => {
  // teamId
  params.page = pagination.current;
  params.pageSize = pagination.pageSize;
  try {
    let result = await getMemberApplyRecordsAxios(params);
    console.log(result);
    result = getResponseResult(result);
    if (!result) return;
    memberData.value = result.data.list;
    pagination.total = result.data.total;
    console.log(memberData.value);
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    MessagePlugin.error(errMsg);
  }
};

const onClickConfirm = async () => {};

const onOpen = (item?: any) => {
  onSearch();
  visible.value = true;
};
const onClose = () => {
  visible.value = false;
};

defineExpose({
  onOpen,
  onClose
});
</script>
<style lang="less" scoped>
@import "@renderer/views/uni/member_home/panel/public.less";
.empty {
  height: 76vh;
}
:deep(.t-steps--vertical).t-steps--dot-anchor
  .t-steps-item--finish
  .t-steps-item__icon {
  border-color: #c7c7c8;
  background: #c7c7c8;
}

:deep(.t-steps-item--finish) {
  &::before {
    border-right-color: #c7c7c8 !important;
    color: #c7c7c8 !important;
    left: 2.5px;
    top: 24px;
  }
}

.sports {
  &-title {
    font-size: 14px;

    font-weight: 400;

    color: #717376;
  }
}
.toTitle {
  font-size: 14px;

  font-weight: 400;

  // color: #13161b;
}
.toContent {
  font-size: 14px;

  font-weight: 400;
}
.operates {
  display: flex;
  justify-content: flex-end;
}
.drawerSet {
  // width: 720px;
  &-body {
  }
  //   .t-drawer__content-wrapper {
  //     width: 720px !important;
  //   }
}
.main_body {
  display: flex;
  align-items: center;
  .main_body-item {
    margin-left: 12px;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
:deep(.t-drawer__header) {
  border-bottom: 0;
  color: red;
}
:deep(.t-drawer__body) {
  padding-top: 10px !important;
}

.status-box2 {
  display: flex;
  height: 24px;
  width: 58px;
  max-height: 24px;
  padding: 1px 8px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  color: var(--kyy_color_tag_text_success, #499d60);
  text-align: right;
  border-radius: var(--kyy_radius_tag_full, 999px);
  background: var(--kyy_color_tag_bg_success, #e0f2e5);
  /* kyy_fontSize_2/bold */
  font-family: PingFang SC;
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 22px; /* 157.143% */
}
.status-box1 {
  display: flex;
  height: 24px;
  width: 58px;
  max-height: 24px;
  padding: 1px 8px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: var(--kyy_radius_tag_full, 999px);
  // background: var(--kyy_color_tag_bg_warning, #ffe5d1);
  // color: var(--kyy_color_tag_text_warning, #fc7c14);
  background: var(--kyy_color_tag_bg_brand, #eaecff);
  color: var(--kyy_color_tag_text_brand, #4d5eff);
  text-align: right;

  /* kyy_fontSize_2/bold */
  font-family: PingFang SC;
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 22px; /* 157.143% */
}
.status-box {
  color: var(--kyy_color_tag_text_gray, #516082);
  text-align: right;

  /* kyy_fontSize_2/bold */
  font-family: PingFang SC;
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 22px; /* 157.143% */
  display: flex;
  height: 24px;
  width: 58px;
  max-height: 24px;
  padding: 1px 8px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: var(--kyy_radius_tag_full, 999px);
  background: var(--kyy_color_tag_bg_gray, #eceff5);
}
.status-box3 {
  display: flex;
  height: 24px;
  width: 58px;
  max-height: 24px;
  padding: 1px 8px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: var(--kyy_radius_tag_full, 999px);
  background: #fbdde3;
  color: var(--lingke-wrong, #d92f4d);
  text-align: right;

  /* kyy_fontSize_2/bold */
  font-family: PingFang SC;
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 22px; /* 157.143% */
}
</style>
