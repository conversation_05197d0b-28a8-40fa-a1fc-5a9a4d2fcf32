<template>
  <t-dialog
    v-model:visible="visible"
    :close-on-overlay-click="false"
    :z-index="2500"
    attach="body"
    class="dialogSet dialogSetLimitHeight560 dialogSetNP dialogSetHeaderInvite dialogSet20240801 dialogSetRadius"
    width="480px"
    :footer="currentTabValue === 'square'? null: true"
  >
    <template #header>
      <div class="header">
        <span class="title">{{ props.headerText }} </span>
        <!-- {{ type ? '了解添加入会流程':'了解邀请入会流程' }} -->
        <!-- <span class="tip cursor" @click="onShowMemberFlow"><iconpark-icon name="iconhelp" class="iconhelp"></iconpark-icon>

          {{ type ? $t('member.winter_column.know_add_flow_1'):$t('member.winter_column.know_add_flow_2') }}
        </span> -->

      </div>
    </template>
    <template #body>
      <div class="toBody">
        <!-- <div v-if="!type" class="desc mt-4px">可通过以下任意方式邀请入会，会员申请后需管理员审核。链接永久有效</div>
        <div v-else-if="type==='active'" class="desc mt-4px">可通过以下任意方式邀请激活，会员申请后需管理员审核。链接永久有效</div> -->
        <div v-if="!type" class="desc mt-8px">
          可通过以下任意方式邀请加入，组织申请后需管理员审核。链接永久有效
        </div>
        <div v-else-if="type==='active'" class="desc mt-8px">
          可通过以下任意方式邀请激活，组织申请后需管理员审核。链接永久有效
        </div>

        <div class="tab">
          <span class="tab-item cursor" @click="onTab(tab.value)" :class="{'active': currentTabValue === tab.value}" v-for="tab in tabs" :key="tab.value">{{ tab.label }}</span>
        </div>

        <div class="row mt-16px">
          <div class="rowBox" :style="{height: currentTabValue === 'square'? '420px': '340px'  }">
            <div v-if="groupOptions && groupOptions.length > 0" class="fitem mb-16px" v-show="['link', 'qrcode', 'weixin'].includes(currentTabValue)">
              <div class="title">选择分组</div>
              <div class="ctl">
                <t-select
                  class="select"
                  v-model="groupModel"
                  :options="groupOptions"
                  clearable
                  :popupProps="{
                    overlayInnerStyle: {
                      width: '365px',
                    },
                  }"
                  :keys="{label: 'group_name', value: 'id'}"
                  @change="groupOptionsChange"
                >  <template #suffixIcon>
                    <img src="@/assets/svg/icon_arrow_down.svg" />
                  </template>
              </t-select>
              </div>
            </div>
            <div class="column" v-show="currentTabValue === 'link'">
              <div v-if="!type" class="name">方式一：组织访问链接申请加入</div>
              <div v-else-if="type==='active'" class="name">
                <!-- {{ $t('member.winter_column.know_add_flow_6') }} -->
                方式一：组织访问链接申请激活
              </div>
              <div class="box">
                <span v-if="!type" class="box-desc line-6">
                  {{ activeAccount?.staffName }}{{ $t('member.winter_column.know_add_flow_7') }}

                  {{ groupObjCpt ? (activeAccount?.exclusive_name ||activeAccount?.teamFullName) + '-'+ groupObjCpt?.group_name : (activeAccount?.exclusive_name || activeAccount?.teamFullName) }}

                  ，点击链接填写加入申请表
                </span>
                <span v-else-if="type==='active'" class="box-desc">
                  {{ activeAccount?.exclusive_name || activeAccount?.teamFullName }}&nbsp;{{ $t('member.winter_column.invite') }}&nbsp;【{{ currentRow?.team_name || currentRow?.name }}】&nbsp;激活组织，点击链接确认成员资料
                </span>
                <span class="box-link">
                  {{ link }}
                </span>
              </div>




              <!-- <t-button
                class="mt-16 second-btn savebtn"
                theme="primary"
                variant="outline"

                @click="onCopyLink"
              >
                {{ props.copyLinkText }}</t-button> -->

            </div>
            <div class="column"  v-show="currentTabValue === 'qrcode'">
              <div v-if="type === 'active'" class="name">
                <!-- {{ $t('member.winter_column.invite_two_1') }} -->
                方式二：组织扫描二维码申请激活
              </div>

              <div v-else class="name"> 方式二：组织扫描二维码申请加入</div>
              <div ref="shareImg" class="share">
                <span class="staff">
                  <span v-if="type === 'active'" class="staff-name">{{ activeAccount?.exclusive_name || activeAccount?.teamFullName }}</span>
                  <span v-else class="staff-name">{{ activeAccount?.staffName }}</span>
                  <!-- <span v-if="type === 'active'" class="staff-tip">
                    {{ $t('member.winter_column.invite') }}【{{ currentRow?.team_name || currentRow?.name }}】激活组织</span>
                  <span v-else class="staff-tip">{{ $t('member.winter_column.know_add_flow_7') }}</span> -->
                  {{ $t('member.winter_column.know_add_flow_7') }}
                </span>
                <span v-if="!type" class="team  line-2 max-w-200px">
                  <!-- {{ activeAccount?.teamFullName }} -->
                  {{ groupObjCpt ? (activeAccount?.exclusive_name || activeAccount?.teamFullName) + '-'+ groupObjCpt?.group_name : (activeAccount?.exclusive_name ||activeAccount?.teamFullName) }}
                </span>
                <span id="code-img" class="qrcode">
                  <qrcode-vue :value="link" :size="qrCodeSetting.size" level="Q" style="transform: scale(0.4);"/>
                  <span class="qr">
                    <img class="logo" src="@/assets/member/logo.svg" />
                  </span>
                </span>
                <div class="qrtip mt-8px">{{ props.downLoadText }}</div>

                <img class="qrlogo mt-16px" src="@renderer/assets/member/icon/linker.png">

              </div>

              <!-- <t-button
                class="mt-16 second-btn savebtn"
                theme="primary"
                variant="outline"
                :loading="loading"
                @click="onDownloadQRcode"
              >
                {{ $t('member.winter_column.save_qrcode') }}</t-button> -->


            </div>
            <div class="column" v-show="currentTabValue === 'square'">
              <div class="name">方式三：广场号开启申请加入</div>

              <img src="@renderer/assets/member/qrcodeInfo_gov.png" style="width: 256px; height: 328px;">
              <!-- <t-button
                class="mt-24 second-btn savebtn"
                theme="primary"
                variant="outline"
                :loading="loading"
                @click="onDownloadQRcode"
              >
                {{ $t('member.winter_column.save_qrcode') }}</t-button> -->
              <div class="switchBtn">
                <t-switch :value="openSquareVal" class="switch" :label="['开启', '关闭']" @change="onSwitchChange"></t-switch>
              </div>

            </div>
            <div class="column"  v-show="currentTabValue === 'weixin'">
              <div v-if="type === 'active'" class="name">
                <!-- {{ $t('member.winter_column.invite_two_1') }} -->
                方式四：微信扫小程序码申请激活
              </div>

              <div v-else class="name"> 方式四：微信扫小程序码申请加入</div>
              <div ref="shareImgWeixin" class="share">
                <span class="staff">
                  <span v-if="type === 'active'" class="staff-name">{{ activeAccount?.exclusive_name || activeAccount?.teamFullName }}</span>
                  <span v-else class="staff-name">{{ activeAccount?.staffName }}</span>
                  <!-- <span v-if="type === 'active'" class="staff-tip">
                    {{ $t('member.winter_column.invite') }}【{{ currentRow?.team_name || currentRow?.name }}】激活组织</span>
                  <span v-else class="staff-tip">{{ $t('member.winter_column.know_add_flow_7') }}</span> -->
                  {{ $t('member.winter_column.know_add_flow_7') }}
                </span>
                <span v-if="!type" class="team  line-2 max-w-200px">
                  <!-- {{ activeAccount?.teamFullName }} -->
                  {{ groupObjCpt ? (activeAccount?.exclusive_name || activeAccount?.teamFullName) + '-'+ groupObjCpt?.group_name : (activeAccount?.exclusive_name ||activeAccount?.teamFullName) }}
                </span>
                <span id="code-img-weixin" class="qrcode">
                  <!-- <qrcode-vue :value="link" :size="qrCodeSetting.size" level="Q" style="transform: scale(0.4);"/>
                  <span class="qr">
                    <img class="logo" src="@/assets/member/logo.svg" />
                  </span> -->
                  <img class="w-124 h-124" :src="image" alt="" />
                </span>
                <div class="qrtip mt-8px">{{ props.downLoadTextWeixin }}</div>

                <img class="qrlogo mt-16px" src="@renderer/assets/member/icon/linker.png">

              </div>
            </div>
          </div>
        </div>
      </div></template>
    <template #closeBtn>
      <!-- <iconpark-icon name="close" size="16"></iconpark-icon> -->
      <!-- <svg class="iconpark-icon" style="width: 16px; height: 16px">
        <use href="#close" />
      </svg> -->
      <iconpark-icon name="iconerror" style="font-size: 24px"></iconpark-icon>
    </template>
    <template #footer>
      <div class="footer">
        <!-- <t-button
          theme="default"
          class="min-w-80px"
          variant="outline"
          @click="onClose"
        >{{ $t('member.impm.sys_4') }}</t-button>
        <t-button theme="primary" class="min-w-80px" @click="onSave">保存</t-button> -->
        <t-button
          class="second-btn savebtn"
          theme="primary"
          v-show="currentTabValue === 'link'"
          variant="outline"

          @click="onCopyLink"
        >
          {{ props.copyLinkText }}</t-button>
        <template v-if="currentTabValue === 'qrcode'">
          <t-button
              class="second-btn savebtn"
              theme="primary"
              variant="outline"
              :loading="loading"
              @click="onDownloadQRcode"
            >
              {{ $t('member.winter_column.save_qrcode') }}
          </t-button>
          <t-button  theme="primary" class="min-w-80px" @click="onCopyQr">复制二维码</t-button>
        </template>
        <template v-else-if="currentTabValue === 'weixin'">
          <t-button variant="outline" theme="primary" @click="onDownloadWeixin">保存小程序码</t-button>
          <t-button  theme="primary" @click="onCopyWeixin">复制小程序码</t-button>
        </template>
      </div>
    </template>
  </t-dialog>
</template>
<script lang="ts">
import { i18n } from '@/i18n';
import { useRoute } from "vue-router";
import { setSquareApplyAxios } from "@renderer/api/digital-platform/api/businessApi";
import to from "await-to-js";
// @ts-ignore
const { t } = i18n.global;

</script>

<script lang="ts" setup>
/**
 * @description 批量调整角色弹层
 * <AUTHOR>
 */
import { debounce } from "lodash";
import { computed, reactive, ref, toRaw } from "vue";
import QrcodeVue from "qrcode.vue";
// import html2canvas from "html2canvas";
import useClipboard from "vue-clipboard3";
import { useUniStore } from "@renderer/views/uni/store/uni";

import { inviteUrl } from "@renderer/utils/baseUrl";
import { MessagePlugin } from "tdesign-vue-next";
import { getBaseUrl, refreshTokens } from "@renderer/utils/apiRequest";
import Qs from "qs";
// import { getWebsiteUrl } from "@renderer/constants/website";
import { getUniTeamID } from "@renderer/views/uni/utils/auth";
import dayjs from "dayjs";
import * as htmlToImage from "html-to-image";
import { useDigitalPlatformStore } from "@renderer/views/digital-platform/store/digital-platform-store";
import { platform as platformCon} from "@renderer/views/digital-platform/utils/constant";

import { getGroupOnlyListAxios } from "@renderer/api/uni/api/businessApi";
import { getActivityWxQrCode } from '@/api/activity';
import { onDeliverCreateAxios } from "@renderer/views/digital-platform/utils/auth";
import LynkerSDK from "@renderer/_jssdk";
const { ipcRenderer } = LynkerSDK;

// import { useI18n } from "vue-i18n";
const store = useUniStore();
// const { t } = useI18n();


const { toClipboard } = useClipboard();
const openSquareVal = ref(false);

const props = defineProps({
  type: {
    default: '',
    type: String,
  },
  headerText: {
    default: t('member.digital.o'),
    type: String
  },
  tipText: {
    default: t('member.winter_column.huiyi_2'),
    type: String
  },
  downLoadText: {
    default: '扫一扫上面的二维码，申请加入',
    type: String
  },
  downLoadTextWeixin: {
    default: '微信扫一扫，申请加入',
    type: String
  },
  copyLinkText: {
    default: t('member.winter_column.huiyi_4'),
    type: String
  }
});
const shareImg = ref(null);
const shareImgWeixin = ref(null)
const link = ref("");
// 二维码生成 size: 110,
const qrCodeSetting = reactive({
  size: 275,
  level: "H"
});




const tabs = [
  {
    label: '链接',
    value: 'link'
  },
  {
    label: '二维码',
    value: 'qrcode'
  },
  {
    label: '广场号',
    value: 'square'
  },
  {
    label: '微信小程序',
    value: 'weixin'
  }
]

const currentTabValue = ref(tabs[0].value);

const route = useRoute();
// 平台类型 目前只有digital-platform
const platformCpt = computed(() => {
  return props.platform || route.query?.platform
})
const digitalPlatformStore = useDigitalPlatformStore();

const activeAccount = computed(() => {
  if(platformCpt.value === platformCon.digitalPlatform) {
    return digitalPlatformStore.activeAccount
  } else if(platformCpt.value === platformCon.digitalWorkbench)  {
    return route.query;
  } else {
    return store.activeAccount
  }
})


const currentTeamId = computed(() => {
  if(platformCpt.value === platformCon.digitalPlatform) {
    return digitalPlatformStore.activeAccount?.teamId
  } else if(platformCpt.value === platformCon.digitalWorkbench){
    return route.query?.teamId || 0;
  } else {
    return  getUniTeamID()
  }
})

const image = ref(null);
const getCode = async () => {
  // 分享的详情码不确定是谁扫，不用传actorid，因为这是创建者的
  onDeliverCreateAxios({
    content: link.value
  }).then(async(result: any)=> {
    const res = await getActivityWxQrCode({
      scene: `deliver_key=${result?.deliver_key}`,
      width: 64,
      page: 'pages/webview/index',
    });
    const blob = new Blob([res.data], { type: 'image/jpeg' });
    image.value  = URL.createObjectURL(blob);
  })
}


const visible = ref(false);

const emits = defineEmits(["reload", "onShowMemberFlow"]);
const onShowMemberFlow = () => {
  emits("onShowMemberFlow");
};
const onSave = debounce(() => {}, 500);

// 点击下载二维码
let imgUrl = "";
const loading = ref(false);
const onDownloadQRcode = debounce((down = true) => {
  loading.value = true;
  console.log(dayjs());
  htmlToImage
    .toPng(shareImg.value, {
      quality: 1, // 设置图像质量为最高
      pixelRatio: 4
    })
    .then(async (dataUrl) => {
      if (down) {
        ipcRenderer.invoke("save-base64", dataUrl).then((res) => {
          console.log(res);
          if (res) {
            MessagePlugin.success("保存成功");
          }
        });
      } else {
        const data = await fetch(dataUrl);
        const blob = await data.blob();
        await navigator.clipboard.write([
          new ClipboardItem({
            [blob.type]: blob
          })
        ]);
        MessagePlugin.success(t("zx.contacts.copySuc"));
      }
      loading.value = false;
    })
    .finally(() => {
      // saveStatus.value = false;
      loading.value = false;
    });
  setTimeout(() => {
    loading.value = false;
  }, 5000);
}, 500);

const onDownloadWeixin = debounce((down = true) => {
  loading.value = true;
  console.log(dayjs());
  htmlToImage
    .toPng(shareImgWeixin.value, {
      quality: 1, // 设置图像质量为最高
      pixelRatio: 4
    })
    .then(async (dataUrl) => {
      if (down) {
        ipcRenderer.invoke("save-base64", dataUrl).then((res) => {
          console.log(res);
          if (res) {
            MessagePlugin.success("保存成功");
          }
        });
      } else {
        const data = await fetch(dataUrl);
        const blob = await data.blob();
        await navigator.clipboard.write([
          new ClipboardItem({
            [blob.type]: blob
          })
        ]);
        MessagePlugin.success(t("zx.contacts.copySuc"));
      }
      loading.value = false;
    })
    .finally(() => {
      // saveStatus.value = false;
      loading.value = false;
    });
  setTimeout(() => {
    loading.value = false;
  }, 5000);
}, 500);

const onCopyWeixin = ()=> {
  htmlToImage
    .toPng(shareImgWeixin.value, {
      quality: 1, // 设置图像质量为最高
      pixelRatio: 2 })
    .then(async (dataUrl) => {
      const data = await fetch(dataUrl);
      const blob = await data.blob();
      await navigator.clipboard.write([
        new ClipboardItem({
          [blob.type]: blob
        })
      ]);
      MessagePlugin.success(t("zx.contacts.copySuc"));
    })
    .finally(() => {
      // saveStatus.value = false;
    });
}


const onCopyQr = ()=> {
  htmlToImage
    .toPng(shareImg.value,  {
      quality: 1, // 设置图像质量为最高
      pixelRatio: 4
    })
    .then(async (dataUrl) => {
      const data = await fetch(dataUrl);
      const blob = await data.blob();
      await navigator.clipboard.write([
        new ClipboardItem({
          [blob.type]: blob
        })
      ]);
      MessagePlugin.success(t("zx.contacts.copySuc"));
    })
    .finally(() => {
      // saveStatus.value = false;
    });
}


// const onDownloadQRcode = () => {

//   loading.value = true;
//   console.log(dayjs());
//   setTimeout(()=> {
//     html2canvas(document.getElementById("code-img"), {
//       useCORS: true, // 【重要】开启跨域配置
//       scale: window.devicePixelRatio < 3 ? window.devicePixelRatio : 2,
//       allowTaint: true // 允许跨域图片
//     }).then(async (canvas) => {
//       imgUrl = canvas.toDataURL();
//       // console.log(imgUrl);
//       // const data = await fetch(imgUrl);
//       // const blob = await data.blob();
//       // // const myUrl = URL.createObjectURL(blob);
//       // console.log(blob);
//       // // downImg()
//       // const link = document.createElement("a");
//       // link.href = imgUrl;
//       // link.download = "linker.png";
//       // link.click();
//       // ipcRenderer.invoke("download-file", {
//       //   title: "linker.png",
//       //   url: imgUrl
//       // });
//       console.log(dayjs());
//       loading.value = false;

//       const dres = await ipcRenderer.invoke("download-file", {
//         title: "linker.png",
//         url: imgUrl
//       });
//       if (dres) {
//         MessagePlugin.success(`保存成功`);
//       }
//     });
//   }, 1000)
//   setTimeout(() => {
//     loading.value = false;
//   }, 5000);
// };


const getGroupList =  () => {
  return new Promise(async(resolve, reject) => {
    const [err, res] = await to(getGroupOnlyListAxios({},currentTeamId.value));

    if (err) {
      reject(err);
      return;
    }
    const { data }:any = res;
    resolve( data?.data?.group_list || []);
  });
};


const onCopyLink = async () => {
  try {
    if (props.type === 'active') {
      await toClipboard(`${activeAccount?.exclusive_name || activeAccount.value?.teamFullName}邀请【${currentRow.value?.team_name || currentRow.value?.name}】激活会员，点击链接确认会员资料：${link.value}`);

    } else {
      await toClipboard(`${activeAccount.value?.staffName}邀请你加入${activeAccount.value?.exclusive_name || activeAccount.value?.teamFullName}，点击链接填写加入申请表：${link.value}`);
    }
    // console.log('Copied to clipboard');
    MessagePlugin.success("已复制到剪贴板");
  } catch (e) {
    console.error(e);
  }
};

const onGoLink = () => {
  // previewAppPage({ url: toRaw(link.value) });
  // http://operation-manage-square-dev.ringkol.com
  // ipcRenderer.invoke(
  //   "create-h5-preview",
  //   `{}/member/invite?link=d9844ff11d04752dbbd318c075dee4bf`
  // );

  refreshTokens("WEB").then((res) => {
    console.log(res);
    // getWebsiteUrl("organizeManage")
    const domain = "http://localhost:3002";
    const path = "/#/setting/setting-info/setting-info-detail";
    const params = {
      teamId: currentTeamId.value,
      ...res,
      refresh_token: Qs.stringify(res.refresh_token)
    };
    // link.value = `${inviteUrl}${path}?${Qs.stringify(params)}`;
    // visible.value = true;
    console.log(params);
    console.log(`${domain + path}?${Qs.stringify(params)}`);
  });
};


/**
 * 获取组织信息
 */
const onSetOpenSquare = (params)=> {
  return new Promise(async (resolve, reject) => {
    const [err, res] =  await to(setSquareApplyAxios(params, currentTeamId.value));
    console.log(res);
    if(err) {
      return reject(err?.message);
    }
    console.log(res)
    const { data } = res;
    resolve(data);
  });

}


/**
 *
 * @param data 值不为空说明为编辑状态
 */
//  path = "/member/invite"
const currentRow = ref(null);


const onSwitchChange = (val) => {
  const params = {
    square_apply: val ? 1 : 0
  }
  onSetOpenSquare(params).then((res)=> {
    openSquareVal.value = val
  }).catch((err) => {
    console.log(err);
    if(err) {
      MessagePlugin.error(err)
    }
  })

}
const groupModel = ref(null);
const groupOptions = ref([]);

const groupObjCpt = computed(() => {
  console.log(groupOptions.value)
  return groupOptions.value.find((item) => item.id === groupModel.value)
})
const groupOptionsChange = (e) => {
  console.log(groupModel.value)
  const params = {
    link: valData.value?.link,
    group_id: e
  };
  link.value = `${inviteUrl}${valData.value?.path}&${Qs.stringify(params, {encode: false})}`;
  getCode();
}
const valData = ref(null);
// eslint-disable-next-line default-param-last
const onOpen = (val?: any, path = "/account/jump?to=uniInvite", row?: any) => {

  currentTabValue.value = tabs[0].value;
  groupModel.value = null;
  getGroupList().then((arr)=> {
    console.log(arr)
    groupOptions.value = arr;
    console.log(groupOptions.value)
  });


  console.log();
  currentRow.value = row || null;
  link.value = "";
  valData.value = {...val, path};
  if (val) {

    let params = {
      link: val?.link
    };
    openSquareVal.value = Boolean(val?.square_apply)
    // const inviteUrl = getBaseUrl("square-operation-manage");
    link.value = `${inviteUrl}${path}&${Qs.stringify(params, {encode: false})}`;
    visible.value = true;
    // refreshTokens("WEB").then((res) => {
    //   console.log(res);
    //   params = { ...params, ...res };
    //   // link.value = `${inviteUrl}${path}?${Qs.stringify(params)}`;
    //   // visible.value = true;
    //   console.log(params);
    //   console.log(Qs.stringify(params));
    // });
    getCode();
  }
};
const onClose = () => {
  visible.value = false;
};


const onTab = (val) => {
  currentTabValue.value = val;
}

defineExpose({
  onOpen,
  onClose
});
</script>

<style lang="less" scoped>
@import "@renderer/views/engineer/less/common.less";
@import "@renderer/views/member/member_number/less/inviteqrcode.less";
.footer {
  padding:24px;
  padding-top: 16px;
}
.tab {
  margin: 0 24px;
  margin-top: 24px;
  padding: 0 8px;
  height: 28px;

  display: flex;
  gap: 24px;
  border-bottom: 1px solid var(--divider-kyy_color_divider_light, #ECEFF5);
  &-item {
    color: var(--kyy_color_tabbar_item_text, #1A2139);
    text-align: center;

    /* kyy_fontSize_2/regular */
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
  .active {
    color: var(--brand-kyy_color_brand_default, #4D5EFF);

    /* kyy_fontSize_2/bold */
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px; /* 157.143% */
    position: relative;
    &::after {
      position: absolute;
      content: "";
      width: 28px;
      height: 3px;
      border-radius: 1.5px;
      background: var(--brand-kyy_color_brand_default, #4D5EFF);
      bottom: 0;
      left: 0;
      right: 0;
      margin: 0 auto;
    }
  }
}
.switchBtn {
  display: flex;
  height: 32px;
  display: flex;
  align-items: center;
  margin-top: 16px;

}

:deep(.switch) {

  width: 62px;
  height: 24px;
  .t-switch__content {
    color: var(--kyy_color_switch__text_active, #828DA5);

    /* kyy_fontSize_1/regular */
    font-family: "PingFang SC";
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px; /* 166.667% */
    padding-left: calc(62px - 34px);

  }

}

:deep(.t-is-checked) {
  .t-switch__content {
    // color: var(--kyy_color_switch__text_active, #828DA5);
    // padding-right: calc(62px - 60px) !important;
    color: #fff;
    padding-left: 10px !important;
  }
}


.t-alert--info {
  padding: 8px 16px;
}
.form {
  margin-top: 14px;
}

// :deep(.t-dialog__body) {
//   overflow: hidden !important;
//   padding-bottom: 0;
// }

.inputFlex {
  display: flex;
  flex-direction: column;
  width: 100%;
  .tips {
    font-size: 14px;

    font-weight: 400;
    color: #717376;
  }
}

.header {
  display: flex;
  align-items: center;
  gap: 16px;
  .title {
    color: var(--kyy_color_modal_title, #1A2139);
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px; /* 150% */
  }
  .tip {
    color: var(--brand-kyy-color-brand-default, #4D5EFF);

    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */

    display: flex;
    align-items: center;
    .iconhelp {
      font-size: 20px;
    }
  }
}

.toBody {
  //   max-height: 70vh;
  //   overflow: auto;
  // padding: 0 24px;
  display: flex;
  flex-direction: column;
  // align-items: center;
  .w {
    width: 100%;
  }
  .desc {
    color: var(--text-kyy-color-text-2, #516082);
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
    padding: 0 24px;
  }

  .row {
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 0 2px;
    border-bottom-right-radius: 16px;
    border-bottom-left-radius: 16px;


    .rowBox {
      width: 100%;
      height: 340px;
      overflow-y: overlay;
      border-bottom-right-radius: 16px;
      border-bottom-left-radius: 16px;
      padding: 0 22px;
      padding-bottom: 16px;
    }
    .rowBox::-webkit-scrollbar-thumb {
      border-radius: 16px;

    }

    /* 伪元素的滚动条轨道 */
    .rowBox::-webkit-scrollbar-track {
      background: #fff !important;
      opacity: 0 !important;
      // background: red;
      height: 0; /* 设置滚动条的高度为0 */
      width: 0; /* 设置滚动条的宽度为0 */
      border-radius: 16px;
    }

    .fitem {
      display: flex;
      align-items: center;
      gap: 8px;
      .title {
        flex: none;
        color: var(--text-kyy_color_text_3, #828DA5);

        /* kyy_fontSize_2/regular */
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
      }
      .ctl {
        flex: 1;
        .select {
          width: 100%;
        }
      }
    }

    .column {
      display: flex;
      align-items: center;
      flex-direction: column;
      padding: 16px 32px;
      width: 100%;
      // width: 320px;
      // height: 420px;

      border-radius: 8px;
      background: var(--bg-kyy-color-bg-deep, #F5F8FE);
      .name {
        color: var(--text-kyy-color-text-1, #1A2139);

        /* kyy_fontSize_2/bold */
        font-family: PingFang SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 22px; /* 157.143% */
        margin-bottom: 16px;
      }
      .box {
        border-radius: 8px;
        border: 1px solid var(--border-kyy-color-border-default, #D5DBE4);
        // background: url('@renderer/assets/member/icon/invite_pic.png'), lightgray 50% / cover no-repeat;
        background: url('@renderer/assets/member/icon/invite_pic.png');
        background-size: cover;
        width: 256px;
        height: 328px;
        // height: 100%;
        padding: 24px;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        &-desc {
          color: var(--text-kyy-color-text-1, #1A2139);
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px; /* 157.143% */
        }
        &-link {
          color: var(--brand-kyy-color-brand-default, #4D5EFF);
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px;
          word-break: break-all;
        }
      }

      .share {
        width: 256px;
        height: 100%;
        background: #fff;
        display: flex;
        // justify-content: center;
        align-items: center;
        flex-direction: column;
        padding: 24px;

        border-radius: 8px;
        border: 1px solid var(--border-kyy-color-border-default, #D5DBE4);


        // background: url('@renderer/assets/member/icon/invite_pic.png'), lightgray 50% / cover no-repeat;
        background: url('@renderer/assets/member/icon/invite_pic.png');
        background-size: cover !important;
        .qrtip {
          color: var(--text-kyy-color-text-3, #828DA5);
          text-align: center;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px; /* 157.143% */
        }
        .qrlogo {

          width: 118px;
          height: 24px;

        }
        .staff {
          min-width: 180px;
          display: flex;
          gap: 4px;
          align-items: center;
          justify-content: center;
          flex-wrap: wrap;
          color: var(--text-kyy-color-text-1, #1A2139) !important;

          // flex-direction:column;
          &-name {
            // color: var(--brand-kyy-color-brand-default, #4D5EFF);
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
            text-align: center;
            color: var(--brand-kyy_color_brand_default, #4D5EFF);

          }
          &-tip {

            /* kyy_fontSize_2/regular */
            font-family: PingFang SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
          }

        }
        .team {
          color: var(--text-kyy-color-text-1, #1A2139) ;
          text-align: center;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px; /* 157.143% */
        }
      }
    }
  }
}

.savebtn {
  font-size: 14px !important;
  font-style: normal;
  font-weight: 400 !important;
  line-height: 22px !important; /* 157.143% */
  padding: 4px 16px !important;
  height: 32px;
  :deep(.t-button) {
    overflow: none;
  }
  :deep(.t-button__text) {
    font-weight: 400 !important;

    line-height: 22px !important; /* 157.143% */
  }
}

:deep(.t-form__item) {
  margin-bottom: 10px;
}

.t-dialog__ctx .t-dialog__position {
  padding: 0;
}

// :deep(.t-dialog--default) {
//   padding: 0 !important;
// }

.qrcode {
  margin-top: 16px;
  position: relative;
  border: 1px solid #e3e6eb;
  border-radius: 8px;
  padding-bottom: 0;
  width: 124px;
  height: 124px;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;

  .qr {
    // width: 40px;
    // height: 40px;
    position: absolute;
    display: block;
    left: calc(50% - 30px/2);
    right: 0;
    // margin: auto;
    bottom: 0;
    top: calc(50% - 30px/2);
    display: flex;
    align-items: center;
    justify-content: center;

    border-radius: 4px;
    background-color: #fff;
    width: 30px;
    height: 30px;

    .logo {
      width: 24px;
      height: 24px;
    }
  }
}

.copyBtn {
  display: flex;
  align-items: center;
  font-size: 14px;

  font-weight: 400;
  color: #2069e3;
}
.iconcopy {
  font-size: 20px;
}
</style>
