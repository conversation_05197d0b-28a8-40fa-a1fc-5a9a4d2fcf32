<template>
  <div class="bus">
    <leftMenu />
    <div class="right-box">
      <div class="right-content">
        <div class="data-box-header">
          <div class="top">
            <div class="tab">
              <div class="item" :class="{ itemAct: params.promotion_type === 1 }" @click="switchType(1)">
                {{ t('niche.gch') }}
                <div class="tag0"></div>
              </div>
              <div class="item" :class="{ itemAct: params.promotion_type === 2 }" @click="switchType(2)">
                {{ t('niche.szpt') }}
                <div class="tag"></div>
              </div>
            </div>

            <div class="approval-time">
              <div @keyup.enter="getDataRunDr">
                <t-input
                  v-model="params.title"
                  style="width: 304px"
                  :placeholder="t('niche.sbar')"
                  clearable
                >
                  <template #prefix-icon>
                    <iconpark-icon name="iconsearch" class="name-icon" />
                  </template>
                </t-input>
              </div>
              <div v-if="paramsSuper" class="af-icon" @click="showFilter">
                <img src="@renderer/assets/approval/icons/fbutton.svg" style="width: 32px; height: 32px" alt="" />
              </div>
              <t-button
                v-else
                style="width: 32px; margin-left: 8px; height: 32px"
                type="button"
                theme="default"
                variant="outline"
                @click="showFilter"
              >
                <iconpark-icon name="iconscreen" class="icon iconscreen" />
              </t-button>

              <t-divider layout="vertical"></t-divider>
          <t-checkbox v-model="showDay">显示距离时间</t-checkbox>

            </div>
          </div>
          <div class="f-btns">
            <t-button
              class="f-btn"
              :theme="
                !params.process_state &&
                  params.process_state !== 0 &&
                  !params.release_state &&
                  params.release_state !== 0
                  ? 'primary'
                  : 'default'
              "
              @click="clearState()"
            >
              {{ t('niche.all') }}
            </t-button>

            <t-button :theme="params.process_state === 0 ? 'primary' : 'default'" @click="setProcessState(0)">
              {{ t('niche.status_pending') }} ({{ params.promotion_type === 2 ? examineCount.digital : examineCount.square }})
            </t-button>
            <t-button
              class="f-btn"
              :theme="params.process_state === 2 ? 'primary' : 'default'"
              @click="setProcessState(2)"
            >
              {{ t('niche.status_rejected') }}
            </t-button>

            <t-button
              class="f-btn"
              :theme="params.release_state === 1 ? 'primary' : 'default'"
              @click="setReleaseState(1)"
            >
              {{ t('niche.status_in_progress') }}
            </t-button>
            <t-button
              class="f-btn"
              :theme="params.release_state === 0 ? 'primary' : 'default'"
              @click="setReleaseState(0)"
            >
              {{ t('niche.status_not_active') }}
            </t-button>
            <t-button
              class="f-btn"
              :theme="params.release_state === 2 ? 'primary' : 'default'"
              @click="setReleaseState(2)"
            >
              {{ t('niche.status_off_shelf') }}
            </t-button>
            <t-button
              class="f-btn"
              :theme="params.release_state === 3 ? 'primary' : 'default'"
              @click="setReleaseState(3)"
            >
              {{ t('niche.status_expired') }}
            </t-button>
            <t-button
              class="f-btn"
              :theme="params.release_state === 4 ? 'primary' : 'default'"
              @click="setReleaseState(4)"
            >
              {{ t('niche.status_deleted') }}
            </t-button>
          </div>
        </div>
        <div class="data-box-con">
          <div v-if="paramsSuper" class="filter-res" style="margin: 12px 0px 24px 0px;">
            <div class="tit">{{ t("approval.approval_data.sures") }}</div>
            <div v-if="params.apply_team_name" class="kword te">
              <span>{{ t('niche.sqzz') }}{{ params.apply_team_name }}</span>
              <span class="close2" @click="clearFilterKeyword">
                <iconpark-icon name="iconerror" style="font-size: 20px" />
              </span>
            </div>
            <div v-if="params.created_at_begin || params.created_at_end" class="ov-time te">
              <span>{{ t('niche.sqsj') }} {{ params.created_at_begin }} ~ {{ params.created_at_end }}</span>
              <span class="close2" @click="clearFilterFinish">
                <iconpark-icon name="iconerror" style="font-size: 20px" />
              </span>
            </div>
            <div v-if="params.type" class="stat te">
              <span>{{ t("niche.res_type") }}{{ params.type === 1 ? t("niche.gy") : t("niche.xq") }}</span>
              <span class="close2" @click="clearFilterStatus">
                <iconpark-icon name="iconerror" style="font-size: 20px" />
              </span>
            </div>
            <div v-if="params.release_channel" class="stat te">
              <span>{{ t("niche.res_ch_a") }}{{ optionsLabel() }}</span>
              <span class="close2" @click="clearFilterChannel">
                <iconpark-icon name="iconerror" style="font-size: 20px" />
              </span>
            </div>

            <div v-if="params.classify_id" class="kword te">
              <span>{{ t("niche.hyfl") }} {{ classify_name }}</span>
              <span class="close2" @click="clearClassifytree">
                <iconpark-icon name="iconerror" style="font-size: 20px" />
              </span>
            </div>

            <div class="icon" @click="clearFilters">
              <img src="@renderer/assets/approval/icons/del8.svg" alt="" />
              <a>{{ t("approval.approval_data.clearFilters") }}</a>
            </div>
          </div>
          <t-table
            :row-key="'id'"
            :data="listData"
            cell-empty-content="--"
            :columns="columns"
          >
            <template #team_name="{ row }">
              <div class="g-box" @click="onRowClick(row)">
                <div class="main-img">
                  <img
                    v-if="row.images"
                    :src="row.images[0].file_name + '?x-oss-process=image/resize,m_fill,w_200,quality,q_60'"
                    alt=""
                    @click.stop="preview(row.images)"
                  />
                  <img v-else src="/assets/business/Rectangle.png" alt="" />
                </div>
                <div class="good-info">
                  <div class="good-title">
                    <t-tooltip :content="row.title">
                      <div class="tc" :class="`tct${row.is_top}`">
                        <div v-if="row.is_top === 1" class="tag">{{ t('niche.top') }}</div>
                        <div v-if="row.type === 1" class="tag1">
                          {{ t("niche.gy") }}
                        </div>
                        <div v-else class="tag2">{{ t("niche.xq") }}</div>
                        {{ row.title }}
                      </div>
                    </t-tooltip>
                  </div>
                  <div class="good-time"><iconpark-icon name="icondate" class="name-icon" /> {{ row.created_at }}</div>
                  <!-- <div class="good-time">
                    <iconpark-icon name="iconplatformmember" class="name-icon" />
                    {{ row.staff_name }}
                  </div> -->
                </div>
              </div>
            </template>
            <template #approval_name="{ row }">
              <div v-if="row.effective_unlimited" @click="onRowClick(row)">
                长期
              </div>
              <div v-else @click="onRowClick(row)">
                <p>{{ row.effective_begin }} ～ {{ row.effective_end }}</p>
                <template v-if="showDay">
                <p v-if="row.effective_state === 0" style="color: #499d60; margin-top: 2px">
                  {{ t('niche.jsxiao') }} {{ row.effective_day }} 天
                </p>
                <p v-if="row.effective_state === 1" style="color: #d54941; margin-top: 2px">
                  {{ t('niche.jsx') }} {{ row.effective_day }} 天
                </p>
                </template>
              </div>
            </template>
            <template #channels="{ row }">
              <span @click="onRowClick(row)">{{ row.apply_data?.name }}</span>
              <br />
              <span @click="onRowClick(row)">{{ row.apply_data?.id }}</span>
            </template>

            <template #status="{ row }">
              <div
                style="display: flex;
    justify-content: center;"
                @click="onRowClick(row)"
              >
                <template v-if="row.process_state === 0">
                  <div :class="`st-tag0 exsty`">{{ t('niche.status_pending') }}</div>
                </template>
                <template v-if="row.process_state === 1">
                  <div :class="`st-tag${row.release_state} exsty`">{{ releaseStateText[row.release_state] }}</div>
                </template>
                <template v-if="row.process_state === 2">
                  <div :class="`st-tag2 exsty`">{{ t('niche.status_rejected') }}</div>
                </template>
              </div>
            </template>
            <template #actions="{ row }">
              <div class="actions">
                <template v-if="row.process_state === 0 && row.can_examine">
                  <div class="mbtna">
                    <a @click="examineRun(row)">审核</a>
                  </div>
                </template>
                <template v-if="row.process_state === 1">
                  <template v-if="row.release_state === 0">
                    <div class="mbtna">
                      <a @click="editHotRun(row)">{{ row.is_top ? t('niche.topcal'):t('niche.top') }}</a>
                    </div>
                  </template>
                  <template v-if="row.release_state === 1">
                    <div class="mbtna">
                      <a @click="delistOne(row, 3)">{{ t('niche.xj') }}</a>
                    </div>
                    <div class="mbtna">
                      <a @click="editHotRun(row)">{{ row.is_top ? t('niche.topcal'):t('niche.top') }}</a>
                    </div>
                  </template>
                  <template v-if="row.release_state === 2">
                    <div class="mbtna">
                      <a @click="publishOne(row)">{{ t('niche.sj') }}</a>
                    </div>
                    <div class="mbtna">
                      <a @click="reonOpen(row.off_reason)">{{ t('niche.reson') }}</a>
                    </div>
                    <div class="mbtna">
                      <a @click="editHotRun(row)">{{ row.is_top ? t('niche.topcal'):t('niche.top') }}</a>
                    </div>
                  </template>
                </template>
                <template v-if="row.process_state === 2">
                  <div class="mbtna">
                    <a @click="reonOpen(row.refuse_reason)">{{ t('niche.reson') }}</a>
                  </div>
                </template>
              </div>
            </template>
          </t-table>
          <noData
            v-if="!listData.length"
            style="margin-top: 88px"
            :show-step="false"
            :text="t('approval.no_data')"
          />
          <div v-if="total && total > 10" class="pagination">
            <t-pagination
              :total="total"
              :total-content="false"
              show-previous-and-next-btn
              :show-page-size="false"
              :current="params.page"
              @change="pageChange"
            />
          </div>
        </div>
      </div>
    </div>

    <t-drawer
      v-model:visible="filterVisible"
      :close-btn="true"
      size="472px"
      :header="t('approval.approval_data.sur')"
      class="filterDrawer"
    >
      <div class="form-boxxx">
        <div class="fitem">
          <div class="title">{{ t("niche.re_type") }}</div>
          <div class="ctl">
            <t-select
              v-model="drawerForm.type"

              :options="optionsType"
              clearable
              :placeholder="t('approval.operation.select')"
            > <template #suffixIcon>
              <img src="@/assets/svg/icon_arrow_down.svg" />
            </template>
          </t-select>
          </div>
        </div>
        <div class="fitem">
          <div class="title">{{ t('niche.sqzz') }}</div>
          <div class="ctl">
            <t-input v-model="drawerForm.apply_team_name" :maxlength="20" :placeholder="'请输入申请组织'" />
          </div>
        </div>
        <div class="fitem">
          <div class="title">{{ t('niche.sqsj2') }}</div>
          <div class="ctl">
            <t-date-range-picker
              v-model="drawerForm.finish"
              style="width: 100%"
              :placeholder="[t('approval.approval_data.start_time'), t('approval.approval_data.end_time')]"
              clearable
            >
              <template #suffixIcon>
                <iconpark-icon name="icondate" class="iconorientation" />
              </template>
            </t-date-range-picker>
          </div>
        </div>

        <div class="fitem">
          <div class="title">{{ t('niche.hyfl2') }}</div>
          <div class="ctl">
            <t-cascader
              v-model="drawerForm.classifytree"
              :options="classifytree"
              :keys="{ label: 'name', value: 'id', children: 'children' }"
              clearable
              :placeholder="t('niche.cla_tip')"
              @change="classifytreeChange"
            />
          </div>
        </div>
      </div>
      <template #footer>
        <div class="foot">
          <t-button
            type="button"
            style="width: 80px"
            theme="default"
            @click="initF"
          >
            {{ t("niche.rest") }}
          </t-button>

          <t-button style="width: 80px" type="button" @click="getDataRunDr">
            {{ t("niche.ss") }}
          </t-button>
        </div>
      </template>
    </t-drawer>

    <t-image-viewer v-model:visible="viewer" :images="imageFiles" />

    <delist ref="delistRef" @delist-succ="getDataRunDr" />

    <guide ref="guideRef" />
  </div>

  <t-dialog
    v-model:visible="reVisible"
    :header="t('niche.reson')"
    :on-cancel="reonCancel"
    :on-close="reonCancel"
    width="384px"
    :footer="null"
  >
    <div class="re-con">
      {{ reConValue }}
    </div>
  </t-dialog>

  <RenewalDialog
    v-if="renewalDialogVisible"
    v-model="renewalDialogVisible"
    :square-id="squareData?.square?.squareId"
    :upgrade="upgrade"
    :team-id="teamId"
    @success="renewalSuccess"
  />
  <OpenSquare v-if="openSquareVisible" v-model="openSquareVisible" @success="renewalSuccess" />
</template>

<script setup lang="ts" name="nicheExtend">
import { computed, onActivated, onMounted, reactive, ref } from "vue";
import leftMenu from "@renderer/views/niche/components/leftMenu.vue";
import delist from "@renderer/views/niche/components/nicheHome/delist.vue";
import noData from "@renderer/views/niche/components/nicheHome/noData.vue";
import guide from "@renderer/views/niche/components/nicheHome/guide.vue";
import { DialogPlugin, MessagePlugin } from "tdesign-vue-next";
import noPermissions from "@renderer/views/device-management/noPermissions.vue";
import {
  businessEXamineCount,
  businessReleaseCopy,
  businessReleaseDel,
  examineList,
  exhotSet,
  hotSet,
  examineOn,
  marketClassifytree,
  releaseRelease,
  releaseSelf,
} from "@renderer/views/niche/apis/index";
import { AprListResponse } from "@renderer/api/approval/model/approvalData";
import { AxiosResponse } from "axios";
import { useI18n } from "vue-i18n";
import { getTeamAnnualFee } from "@renderer/api/business/manage";
import { ClientSide } from "@renderer/types/enumer";
import { useRoute, useRouter } from "vue-router";
import { getCommonAppAuthAxios } from "@renderer/api/digital-platform/api/businessApi";
import to from "await-to-js";
import OpenSquare from "@/views/square/components/OpenSquare.vue";
import RenewalDialog from "@/views/square/components/annual-fee/AnnualFeeDialog.vue";
import { goToDigitalPlatform_member } from "../member/utils/auth";
import LynkerSDK from '@renderer/_jssdk';

const { ipcRenderer } = LynkerSDK;
const { t } = useI18n();
const optionsType = computed(() => [
  { label: t("niche.gy"), value: 1 },
  { label: t("niche.xq"), value: 2 },
]);
const options = computed(() => [
  { label: t("niche.sq1"), value: "1" },
  { label: t("niche.sq2"), value: "2" },
  { label: t("niche.sq3"), value: "3" },
  { label: t("niche.sq4"), value: "4" },
  { label: t("niche.sq5"), value: "5" },
  { label: t("niche.sq6"), value: "6" },
  { label: t("niche.sq7"), value: "7" },
  { label: t("niche.sq8"), value: "8" },
]);
const releaseStateText = ref([t('niche.status_not_active'), t('niche.status_in_progress'), t('niche.status_off_shelf'), t('niche.status_expired'), t('niche.status_deleted')]);

const optionsLabel = () => {
  const opt = options.value.filter((item) => item.value === params.release_channel);
  return opt[0].label;
};
const approve_status_text = [
  "",
  t("approval.approval_data.under_approval"),
  t("approval.approval_data.pass"),
  t("approval.approval_data.refuse"),
  t("approval.approval_data.repeal"),
];

const emits = defineEmits(["uptWorkBenchTabItem", "setActiveIndexAndName", "settabItem", "setActiveIndex"]);

const allPermissions = ref({
  allow_niche: 0,
  allow_admin: 0,
});

onActivated(() => {
  init();
});
const props = defineProps({
  groupList: {
    type: Array,
    default: () => [],
  },
  tabList: {
    type: Array,
    default: () => [],
  },
  tabIndex: {
    type: Number,
    default: 0,
  },
  activationGroupItem: {
    type: Object,
    default: null,
  },
  cloudDiskType: {
    type: Object,
    default: null,
  },
});
const route = useRoute();
const settabItem = () => {
  // ipcRenderer.invoke("set-work-bench-tab-item", {
  //   path: `/workBenchIndex/nicheExtend`,
  //   path_uuid: "niche",
  //   title: "推广审核",
  //   name: 'nicheExtend',
  //   type: ClientSide.NICHE,
  // });
  emits("uptWorkBenchTabItem", {
    path: `/workBenchIndex/nicheExtend`,
    path_uuid: "niche",
    title: "推广审核",
    name: "nicheHome,nicheExtend,nicheDraftList,nicheAdmin",
    type: ClientSide.NICHE,
    updateKey: 'nicheHome',
  });
};

const init = () => {
  settabItem();
  getDataRun();
  getMarketClassifytree();
};

onMounted(() => {
  init();
  // nicheAuth(teamId.value).then((res) => {
  //   if (res.data) {
  //     allPermissions.value = res.data.data;
  //     if (allPermissions.value?.allow_niche) {

  //     }
  //   }
  // });
});

const showDay = ref(true);
const total = ref(0);
const params = reactive({
  title: "",
  release_channel: "",
  type: undefined,
  process_state: undefined,
  release_state: undefined,
  apply_team_name: "",
  classify_name: null,
  created_at_begin: "",
  created_at_end: "",
  classify_id: null,
  page: 1,
  promotion_type: 1,
  operation_state: 0,
  pageSize: 10,
});

const drawerForm = reactive({
  apply_team_name: "",
  release_channel: "",
  classifytree: null,
  finish: [],
  type: "",
});

const selectedRowKeys = ref([]);
const columns = [
  {
    colKey: "team_name",
    title: t("niche.ni_info"),
    width: "304",
  },
  {
    colKey: "channels",
    title: t('niche.sqqd'),
    width: "160",
    ellipsis: true,
  },
  {
    colKey: "approval_name",
    title: t('niche.yxsj'),
    width: "216",
  },
  {
    colKey: "status",
    title: t('niche.ztai'),
    width: "88",
  },
  {
    colKey: "actions",
    title: t('niche.opt'),
    width: "176",
  },
];

const clearState = () => {
  params.process_state = undefined;
  params.release_state = undefined;
  getDataRun();
};
const setProcessState = (key) => {
  params.process_state = key;
  params.release_state = undefined;
  getDataRun();
};
const setReleaseState = (key) => {
  params.release_state = key;
  params.process_state = undefined;
  getDataRun();
};

const listData = ref([]);
const examineCount = ref({
  square: 0,
  digital: 0,
});

const getData = () => {
  examineList(params).then((res: AxiosResponse<AprListResponse>) => {
    console.log(res);
    if (res.data) {
      listData.value = res.data.data.list;
      total.value = res.data.data.total;
      // listDataHandle(listData.value);
    }
  });
  businessEXamineCount().then((res: any) => {
    console.log(res);
    if (res.data) {
      examineCount.value = res.data.data;
    }
  });
};
const listDataHandle = (data) => {
  for (const item of data) {
    item.func = getFeature(item);
    // getChannelFeature(item.channel_type);
  }
};

const viewer = ref(false);
const imageFiles = ref([]);
// const preview = (imgs) => {
//   console.log(imgs);
//   imageFiles.value = imgs;
//   viewer.value = true;
// };


const preview = (imgs) => {
  const temp = imgs.map((item) => ({ url: item.file_name }));
  console.log("preview", temp);
  ipcRenderer.invoke("view-img", JSON.stringify(temp));
};

const pageChange = (e) => {
  params.page = e.current;
  params.pageSize = e.pageSize;
  getData();
};

const reqParamsHandle = () => {
  params.created_at_begin = drawerForm.finish[0];
  params.created_at_end = drawerForm.finish[1];
  params.apply_team_name = drawerForm.apply_team_name;
  params.release_channel = drawerForm.release_channel;
  params.type = drawerForm.type;
  params.classify_id = drawerForm.classifytree;
};

const getDataRun = () => {
  // getTeamAnnualFeeRun();
  reqParamsHandle();
  getData();
};
const switchType = (type) => {
  params.promotion_type = type;
  params.page = 1;
  params.pageSize = 10;
  getDataRun();
};
const filterVisible = ref(false);
const showFilter = () => {
  filterVisible.value = true;
};
const getDataRunDr = () => {
  filterVisible.value = false;
  params.page = 1;
  params.pageSize = 10;
  getDataRun();
};
const clearFilters = () => {
  params.created_at_begin = null;
  params.created_at_end = null;
  params.apply_team_name = "";
  params.release_channel = null;
  params.type = null;
  params.classify_id = null;
  drawerForm.type = null;
  drawerForm.release_channel = null;
  drawerForm.classifytree = null;
  drawerForm.apply_team_name = "";
  drawerForm.finish = [];
  params.page = 1;
  params.pageSize = 10;
  getDataRun();
};
const clearFilterStatus = () => {
  params.type = undefined;
  drawerForm.type = undefined;
  getDataRun();
};
const clearFilterChannel = () => {
  drawerForm.release_channel = null;
  params.release_channel = null;
  getDataRun();
};
const clearFilterKeyword = () => {
  drawerForm.apply_team_name = null;
  params.apply_team_name = null;
  getDataRun();
};
const clearFilterFinish = () => {
  drawerForm.finish = [];
  getDataRun();
};
const clearClassifytree = () => {
  params.classify_id = null;
  drawerForm.classifytree = null;
  getDataRun();
};
const paramsSuper = computed(
  () =>
    params.type ||
    params.release_channel ||
    params.apply_team_name ||
    params.classify_id ||
    params.created_at_end ||
    params.created_at_begin,
);

const squareData = ref(null);
const pageShow = ref(true);
const tipsData = ref({
  con: "",
  btn: "",
});
const renewalDialogVisible = ref(false);
const upgrade = ref(false);
const teamId = ref(localStorage.getItem("businessTeamId") || "");
const openSquareVisible = ref(false);
const squareShow = ref(false);
const checkExpiration = (expiredAt) => {
  const now = new Date();
  const expirationDate = new Date(expiredAt);
  return now > expirationDate;
};
const getTeamAnnualFeeRun = async () => {
  const res = await getTeamAnnualFee(teamId.value);
  if (res.data) {
    squareData.value = res.data;
    if (squareData.value.opened) {
      pageShow.value = false;
      squareShow.value = false;
      if (checkExpiration(squareData.value.annualFeeExpiredAt)) {
        pageShow.value = true;
        tipsData.value.con = t("niche.vip_open_tip1");
        tipsData.value.btn = t("niche.vip_open_btn1");
      } else {
        pageShow.value = false;
        if (squareData.value?.annualFeeDetail?.package) {
          const item = squareData.value.annualFeeDetail?.package?.items.find((item) => item.itemType === "NICHE");
          if (!item) {
            tipsData.value.con = t("niche.vip_open_tip2");
            tipsData.value.btn = t("niche.vip_open_btn2");
            upgrade.value = true;
            pageShow.value = true;
          } else {
            pageShow.value = false;
          }
        } else {
          tipsData.value.con = t("niche.vip_open_tip2");
          tipsData.value.btn = t("niche.vip_open_btn2");
          upgrade.value = true;
          pageShow.value = true;
        }
      }
    } else {
      tipsData.value.con = t("niche.vip_open_tip3");
      tipsData.value.btn = t("niche.vip_open_btn3");
      pageShow.value = true;
      squareShow.value = true;
    }
  }
};

const renewalSuccess = (e) => {
  renewalDialogVisible.value = false;
  openSquareVisible.value = false;
  getTeamAnnualFeeRun();
};

const initF = () => {
  filterVisible.value = false;
  clearFilters();
};

const classifytree = ref([]);
const getMarketClassifytree = () => {
  const area = props.activationGroupItem.teamRegion;
  marketClassifytree(area).then((res) => {
    console.log(res);
    if (res.data) {
      classifytree.value = res.data.data;
    }
  });
};

const classify_name = ref("");
const classifytreeChange = (e, ctx) => {
  console.log(e);
  let str = "";
  const item = ctx.node["__tdesign_tree-node__"];
  if (item.parent) {
    str = `${item.parent.label}/${item.label}`;
  } else {
    str = item.label;
  }
  classify_name.value = str;
};

const copyBusinessReq = (cid, myDialog) => {
  businessReleaseCopy({ id: cid }).then((res: any) => {
    console.log(res);
    if (res.data.code === 0) {
      // delSucc();
      MessagePlugin.success(t("niche.opsucc"));
      myDialog.hide();
    } else {
      // MessagePlugin.error(res.data.message);
    }
  });
};

const router = useRouter();
const toExtend = () => {
  router.push(`/workBenchIndex/nicheExtendManage`);
};

const getFeature = (rowData) => {
  // 发布状态（0：未生效，1：进行中，2：已下架，3：已失效，4：已删除）
  const funcs = [
    {
      title: t('niche.tg'),
      id: "extend",
      icon: "icontotopcancel",
      show: false,
      keys: [0, 1],
    },
    {
      title: t('niche.sj'),
      id: "listing",
      icon: "iconListing",
      show: false,
      keys: [2],
    },
    {
      title: t('niche.xj'),
      id: "delist",
      icon: "iconcommodity-reduce",
      show: false,
      keys: [1],
    },
    {
      title: t('niche.edit'),
      id: "edit",
      icon: "iconedit",
      show: false,
      keys: [0, 2, 3],
    },
    {
      title: t('niche.top'),
      id: "pinned",
      icon: "icontotop",
      show: false,
      keys: [0, 1, 2],
    },
    {
      title: t('niche.topcal'),
      id: "topCancel",
      icon: "icontotopcancel",
      show: false,
      keys: [0, 1, 2],
    },
    {
      title: t('niche.copy'),
      id: "copy",
      icon: "iconcopy",
      show: true,
      keys: [0, 1, 2, 3, 4],
    },
    {
      title: t('niche.del'),
      id: "delete",
      icon: "icondelete",
      show: false,
      keys: [0, 1, 2, 3],
    },
  ];
  const topKey = rowData.is_top ? "pinned" : "topCancel";
  for (const item of rowData.channel_data) {
    for (const func of funcs) {
      if (func.keys.includes(item.release_state)) {
        func.show = true;
      }
    }
  }
  return funcs.filter((item) => item.show).filter((item) => item.id !== topKey);
};

const extendRun = (row) => {
  console.log("推广");
  const isOpen = row.channel_data[0].id !== 0;
  if (!isOpen) {
    const myDialog = DialogPlugin({
      header: t("niche.tip"),
      theme: "info",
      body: t('niche.tgskss'),
      className: "dialog-classp32",
      cancelBtn: null,
      onConfirm: () => {
        myDialog.hide();
      },
    });
  } else {
    router.push(`/workBenchIndex/nicheDetail?to=2&id=${row.id}&title=${row.title}`);
  }
};

const editBusRun = (rowData) => {
  console.log(t('niche.edit'));
  console.log(rowData);
  if (rowData.channel_data[0].release_state === 1 || rowData.channel_data[1].release_state === 1) {
    const myDialog = DialogPlugin({
      header: t("niche.tip"),
      theme: "info",
      body: t('niche.edxiaj'),
      className: "dialog-classp32",
      confirmBtn: t('niche.xj'),
      onConfirm: () => {
        myDialog.hide();
        delistRun(rowData, 0);
      },
      onCancel: () => {
        myDialog.hide();
      },
    });
  } else {
    router.push(`/workBenchIndex/nicheEdit?form=list&id=${rowData.id}`);
  }
};
const listingRunReq = (id, myDialog) => {
  releaseRelease(id).then((res: any) => {
    console.log(res);
    if (res.data.code === 0) {
      delSucc(t('niche.sjcg'));
      myDialog.hide();
    } else {
      // MessagePlugin.error(res.data.message);
    }
  });
};

const plazaRenew = () => {
  const myDialog = DialogPlugin({
    header: t("niche.tip"),
    theme: "info",
    body: tipsData.value.con,
    className: "dialog-classp32",
    confirmBtn: tipsData.value.btn,
    onConfirm: () => {
      if (squareShow.value) {
        openSquareVisible.value = true;
      } else {
        renewalDialogVisible.value = true;
      }
      myDialog.hide();
    },
    onCancel: () => {
      myDialog.hide();
    },
  });
};
const figureRenew = () => {
  const myDialog = DialogPlugin({
    header: t("niche.tip"),
    theme: "info",
    body: t('niche.szpttip'),
    className: "dialog-classp32",
    onConfirm: () => {
      console.log("figureRenew");
    },
    confirmBtn: t('niche.toopen'),
    onCancel: () => {
      myDialog.hide();
    },
  });
};
const listingMain = (rowData) => {
  const myDialog = DialogPlugin({
    header: t("niche.tip"),
    theme: "info",
    body: t('niche.sjhhc'),
    className: "dialog-classp32",
    onConfirm: () => {
      listingRunReq(rowData.id, myDialog);
    },
    onCancel: () => {
      myDialog.hide();
    },
  });
};
const listingRun = async (rowData) => {
  console.log(t('niche.sj'));
  if (pageShow.value) {
    plazaRenew();
  } else {
    listingMain(rowData);
  }
  // else if (!figureAuth.value) {
  //   figureAuthTip();
  // }
};
const copyBusRun = (data) => {
  console.log(t('niche.copy'));
  const myDialog = DialogPlugin({
    header: t("niche.tip"),
    theme: "info",
    body: `${t("niche.askcopy")} “${data.title}” 到草稿箱中？`,
    className: "dialog-classp32",
    onConfirm: () => {
      copyBusinessReq(data.id, myDialog);
    },
    onCancel: () => {
      myDialog.hide();
    },
  });
};
const businessDelRun = (row) => {
  console.log(t('niche.del'));
  const myDialog = DialogPlugin({
    header: t("niche.tip"),
    theme: "info",
    body: t("niche.re_del_tip"),
    className: "dialog-classp32",
    onConfirm: () => {
      businessDelReq(row.id, myDialog);
    },
    onCancel: () => {
      myDialog.hide();
    },
  });
};
const delSucc = (msg) => {
  MessagePlugin.success(msg);
  getData();
};
const businessDelReq = (id, myDialog) => {
  businessReleaseDel(id).then((res: any) => {
    console.log(res);
    if (res.data.code === 0) {
      delSucc(t('niche.delsucc'));
      myDialog.hide();
    } else {
      // MessagePlugin.error(res.data.message);
    }
  });
};

const editHotReq = (hot, row, myDialog) => {
  exhotSet({ is_top: hot }, row.id).then((res: any) => {
    console.log(res);
    if (res.data?.code === 0) {
      MessagePlugin.success(hot === 0 ? t('niche.topcal') : t('niche.topsucc'));
      myDialog.hide();
      getDataRun();
    }
  });
};

const editHotRun = (rowData) => {
  console.log(t('niche.top'));
  const val = rowData.is_top ? 0 : 1;
  const tipText = [t('niche.topcl'), t('niche.topop')];
  const myDialog = DialogPlugin({
    header: t("niche.tip"),
    theme: "info",
    body: tipText[val],
    className: "dialog-classp32",
    onConfirm: () => {
      editHotReq(val, rowData, myDialog);
    },
    onCancel: () => {
      myDialog.hide();
    },
  });
};
const delistRef = ref(null);
const delistRun = (rowData, origin) => {
  console.log(t('niche.xj'));
  delistRef.value.deOpen(rowData, origin);
};
const featureRun = (eventId, rowData) => {
  console.log(eventId, rowData);
  switch (eventId) {
    case "extend":
      extendRun(rowData);
      break;
    case "listing":
      listingRun(rowData);
      break;
    case "delist":
      delistRun(rowData, 0);
      break;
    case "edit":
      editBusRun(rowData);
      break;
    case "pinned":
      editHotRun(rowData);
      break;
    case "topCancel":
      editHotRun(rowData);
      break;
    case "copy":
      copyBusRun(rowData);
      break;
    case "delete":
      businessDelRun(rowData);
      break;
    default:
      console.log("推广");
      break;
  }
};

const delistOne = (data, type) => {
  delistRef.value.deOpen(data, type, true);
};
const publishOneReq = (data, type, myDialog) => {
  examineOn({ id: data.id, type }).then((res: any) => {
    console.log(res);
    if (res.data.code === 0) {
      delSucc(t('niche.sjcg'));
      myDialog.hide();
    } else {
      // MessagePlugin.error(res.data.message);
    }
  });
};
const publishOne = (data) => {
  const myDialog = DialogPlugin({
    header: t("niche.tip"),
    theme: "info",
    body: t('niche.jzszs'),
    className: "dialog-classp32",
    confirmBtn: t('niche.sj'),
    onConfirm: () => {
      publishOneReq(data, 1, myDialog);
    },
    onCancel: () => {
      myDialog.hide();
    },
  });
};

const guideRef = ref(null);

const setOperationState = (key) => {
  params.operation_state = key;
  getDataRunDr();
};
const onRowClick = (row) => {
  router.push(`/workBenchIndex/extendDetail?id=${row.id}`);
};
const examineRun = (row) => {
  // router.push(`/workBenchIndex/nicheDetail?to=1&id=${row.id}&title=${row.title}`);
  router.push(`/workBenchIndex/extendDetail?id=${row.id}`);
};

const figureAuth = ref(false);
const getAppAuth = async () => {
  const [err, res] = await to(getCommonAppAuthAxios({ teamId: teamId.value }, teamId.value));
  if (err) {
    return;
  }
  const { data } = res;
  if (data.data.find((v) => v.uuid === "government")?.auth) {
    console.log("该组织已开启【数字城市】");
    figureAuth.value = true;
    return;
  }
  if (data.data.find((v) => v.uuid === "member")?.auth) {
    console.log("该组织已开启【数字商协】");
    figureAuth.value = true;
    return;
  }
  figureAuth.value = false;
};

const figureAuthTip = () => {
  const myDialog = DialogPlugin({
    header: t("niche.tip"),
    theme: "info",
    body: t('niche.szpttip'),
    className: "dialog-classp32",
    onConfirm: () => {
      myDialog.hide();
      jumpFigureRun();
    },
    onCancel: () => {
      myDialog.hide();
    },
  });
};
const jumpFigureRun = () => {
  // ipcRenderer
  //   .invoke("click-menu-item", {
  //     url: "/digitalPlatformIndex/digital_platform_home",
  //     query: null,
  //     selected_path_uuid: "digital_platform",
  //     click_path_uuid: "digital_platform",
  //   })
  //   .then((res) => {
  //     console.log("goPath res: ", res);
  //     res && ipcRenderer.send("update-nume-index", 4);
  //     // if (res) {
  //     //   configData.$state.numeIndex = 5;
  //     //   configData.$state.path_uuid = "digital_platform";
  //     // }
  //   })
  //   .catch((err) => {
  //     console.log("goPath error: ", err);
  //   });
  goToDigitalPlatform_member(teamId.value);
};

const channelResRun = (rowData, type) => {
  const typeText = type === 1 ? t('niche.gch') : t('niche.szpt');
  const myDialog = DialogPlugin({
    header: t("niche.tip"),
    theme: "info",
    body: `是否将“${rowData.title}”发布到自身的“${typeText}”渠道中？`,
    className: "dialog-classp32",
    onConfirm: () => {
      myDialog.hide();
      releaseSelfRun(rowData.id, type);
    },
    onCancel: () => {
      myDialog.hide();
    },
  });
};

const releaseSelfRun = (rid, type) => {
  releaseSelf(rid, type).then((res: any) => {
    console.log(res);
    if (res.data.code === 0) {
      delSucc(t('niche.fbcg'));
    } else {
      // MessagePlugin.error(res.data.message);
    }
  });
};

const channelRelease = (row, item) => {
  // 广场
  if (item.promotion_type === 1) {
    if (pageShow.value) {
      plazaRenew();
    } else {
      channelResRun(row, 1);
    }
  } else if (figureAuth.value) {
    channelResRun(row, 2);
  } else {
    figureAuthTip();
  }
};

const reConValue = ref(null);
const reVisible = ref(null);
const reonCancel = () => {
  reVisible.value = false;
};
const reonOpen = (reason) => {
  reConValue.value = reason;
  reVisible.value = true;
};
const spString = (title) => {
  if (title?.length > 28) {
    return `${title?.slice(0, 28)}...`;
  }
  return title;
};
</script>

<style lang="less" scoped>
@import "./styles/common.less";
.bus {
  height: 100%;
  display: flex;
}
.fill {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #fff;
  padding-bottom: 202px;
}
.right-box {
  width: calc(100vw - 240px);
}
.right-content::-webkit-scrollbar {
  width: 0px;
}
.right-content {
  padding: 0 16px 16px 16px !important;
  position: relative;
  border-radius: 4px;
  background: #fff;
  height: calc(100vh - 40px);
  // overflow-y: auto;
  width: 100%;

  .pagination {
    margin-top: 15px;
  }
  .data-box-header {
    // margin-bottom: 24px;
    .f-btns {
      display: flex;
      padding: 12px 0px;
      gap: 8px;
      align-items: center;
      align-self: stretch;
      margin-bottom: 12px;
      padding-top: 16px;
      // border-bottom: 1px solid var(--divider-kyy_color_divider_light, #eceff5);
      .f-btn {
        width: 80px;
      }
    }
    .tab {
      display: flex;
      align-items: center;
      gap: 44px;

      .item {
        color: var(--text-kyy_color_text_1, #1a2139);
        font-family: "PingFang SC";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 24px; /* 150% */
        position: relative;
        cursor: pointer;
        .tag {
          width: 16px;
          height: 3px;
          flex-shrink: 0;
          border-radius: 1.5px;
          background: var(--brand-kyy_color_brand_default, #4d5eff);
          position: absolute;
          top: 37px;
          left: 25px;
          opacity: 0;
        }
        .tag0 {
          width: 16px;
          height: 3px;
          flex-shrink: 0;
          border-radius: 1.5px;
          background: var(--brand-kyy_color_brand_default, #4d5eff);
          position: absolute;
          top: 37px;
          left: 16px;
          opacity: 0;
        }
      }
      .itemAct {
        color: var(--brand-kyy_color_brand_default, #4d5eff);
        font-weight: 600;

        .tag,
        .tag0 {
          opacity: 1;
        }
      }
    }
    .top {
      display: flex;
      justify-content: space-between;
      height: 56px;
      border-bottom: 1px solid var(--divider-kyy_color_divider_light, #eceff5);
      .approval-name {
        display: flex;
        align-items: center;
        .label {
          color: var(--text-kyy-color-text-1, #1a2139);
          display: flex;
          align-items: center;
        }
        .mock-in {
          width: 304px;
          min-height: 32px;
          background: #ffffff;
          border: 1px solid #e5e5e5;
          border-radius: 4px;
          display: flex;
          cursor: pointer;
          margin-left: 12px;
          .tag-con {
            width: 1050px;
            display: flex;
            flex-wrap: wrap;
            .tag {
              height: 24px;
              background: rgba(33, 118, 255, 0.1);
              border-radius: 15px;
              font-size: 14px;
              font-family: Undefined, Undefined-Regular;
              font-weight: 400;
              text-align: left;
              color: #2176ff;
              padding: 1px 8px;
              margin: 4px 0px 4px 8px;
              line-height: 24px;
            }
            .tag-text {
              font-size: 14px;

              font-weight: 400;
              text-align: left;
              color: #13161b;
              line-height: 28px;
              margin-left: 5px;
            }
          }
          .icon {
            padding-top: 5px;
          }
        }
        .borderColor {
          border: 1px solid red;
        }
      }
      .approval-time {
        display: flex;
        align-items: center;
        .label {
          width: 76px;
          font-size: 14px;

          font-weight: 400;
          color: var(--kyy_color_tag_text_black, #1a2139);
        }
        .f-icon {
          display: flex;
          width: 32px;
          height: 32px;
          cursor: pointer;
          min-height: 32px;
          max-height: 32px;
          padding: 6px;
          justify-content: center;
          align-items: center;
          gap: 10px;
          margin-left: 8px;
          border-radius: var(--radius-kyy-radius-button-s, 4px);
          border: 1px solid var(--color-button-border-kyy-color-button-border-border-dedault, #d5dbe4);
          background: var(--color-button-border-kyy-color-button-border-bg-default, #fff);
        }
        .f-icon:hover {
          color: #4d5eff;
          border-color: #4d5eff;
        }
      }
      .approval-status {
        display: flex;
        align-items: center;
        .label {
          width: 84px;
          font-size: 14px;

          font-weight: 400;
          text-align: right;
          color: #13161b;
          margin-right: 8px;
        }
      }
      .approval-key {
        display: flex;
        align-items: center;
        margin-left: 32px;
        .label {
          width: 42px;
          font-size: 14px;

          font-weight: 400;
          text-align: right;
          color: var(--kyy_color_tag_text_black, #1a2139);
          margin-right: 8px;
        }
      }
      .approval-end {
        margin-left: 50px;
        display: flex;
        align-items: center;
        .label {
          width: 56px;
          font-size: 14px;

          font-weight: 400;
          text-align: right;
          color: var(--kyy_color_tag_text_black, #1a2139);
          margin-right: 7px;
          margin-left: 3px;
        }
      }
    }
  }

  .opblack {
    display: flex;
    justify-content: space-between;
    margin-top: 24px;
    width: 100%;
  }
}
.data-box-con::-webkit-scrollbar {
  width: 0px;
}
.data-box-con {
  color: var(--kyy-color-table-text, #1a2139);
  height: calc(100% - 120px);
  overflow-y: auto;
  margin-top: 12px;
  .status-box1 {
    width: 58px;
    height: 24px;
    border-radius: var(--kyy-radius-tag-full, 999px);
    background: var(--kyy-color-tag-bg-warning, #ffe5d1);
    padding: 0 8px;
    font-size: 14px;

    font-weight: 400;
    text-align: center;
    color: var(--kyy-color-tag-text-warning, #fc7c14);
  }
  .approval-name-ov {
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .status-box2 {
    width: 58px;
    height: 24px;
    border-radius: var(--kyy-radius-tag-full, 999px);
    background: var(--kyy-color-tag-bg-success, #e0f2e5);
    padding: 0 8px;
    font-size: 14px;

    font-weight: 400;
    text-align: center;
    color: var(--kyy-color-tag-text-success, #499d60);
  }
  .status-box3 {
    width: 58px;
    height: 24px;
    border-radius: var(--kyy-radius-tag-full, 999px);
    background: #fbdde3;
    padding: 0 8px;
    font-size: 14px;

    font-weight: 400;
    text-align: center;
    color: var(--lingke-wrong, #d92f4d);
  }
  .status-box4 {
    width: 58px;
    height: 24px;
    background: #fff6e8;
    border-radius: 4px;
    padding: 0 8px;
    font-size: 14px;

    font-weight: 400;
    text-align: center;
    color: #e66800;
  }
}
.sw-box {
  padding: 12px;
  .item {
    display: flex;
  }
  .text {
    font-size: 14px;
    font-family: Undefined, Undefined-Regular;
    font-weight: 400;
    text-align: left;
    color: #333333;
  }
  .sw {
    margin-left: 18px;
  }
}
.record {
  color: var(--text-kyy-color-text-2, #516082);
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}
:deep(.data-box-con .t-table__empty) {
  display: none;
}
.ex-btn {
  display: flex;
  height: 32px;
  min-width: 80px;
  min-height: 32px;
  max-height: 32px;
  padding: 0px 16px;
  justify-content: center;
  align-items: center;
  gap: 4px;
  border-radius: var(--radius-kyy-radius-button-s, 4px);
  border: 1px solid var(--color-button-border-kyy-color-button-border-border-dedault, #d5dbe4);
  background: var(--color-button-border-kyy-color-button-border-bg-default, #fff);
  color: var(--color-button-border-kyy-color-button-border-text-default, #516082);
  text-align: center;

  /* kyy_fontSize_2/bold */
  font-family: PingFang SC;
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 22px; /* 157.143% */
}

.g-box {
  display: flex;
  .main-img {
    width: 72px;
    height: 72px;
    border-radius: 4px;
    img {
      cursor: pointer;
      width: 72px;
      height: 72px;
      border-radius: 4px;
      object-fit: cover;
    }
  }
  .good-info {
    margin-left: 12px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 196px;
    .good-title {
      display: flex;
      .tag {
        display: flex;
        height: 20px;
        flex-shrink: 0;
        padding: 2px 4px;
        justify-content: center;
        align-items: center;
        margin-right: 4px;
        border-radius: var(--kyy_radius_tag_s, 4px);
        background: var(--kyy_color_tag_bg_brand, #eaecff);
        color: var(--kyy_color_tag_text_brand, #4d5eff);
        text-align: center;
        float: left;
        font-size: 12px;
      }
      .tag1 {
        display: flex;
        height: 20px;
        flex-shrink: 0;
        padding: 2px 4px;
        justify-content: center;
        align-items: center;
        margin-right: 4px;
        border-radius: var(--kyy_radius_tag_s, 4px);
        background: var(--kyy_color_tag_bg_cyan, #e6f9f8);
        color: var(--kyy_color_tag_text_cyan, #11bdb2);
        text-align: center;
        font-family: "PingFang SC";
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px; /* 166.667% */
        text-align: center;
        font-size: 12px;
        float: left;
      }
      .tag2 {
        display: flex;
        height: 20px;
        flex-shrink: 0;
        padding: 2px 4px;
        justify-content: center;
        align-items: center;
        margin-right: 4px;
        border-radius: var(--kyy_radius_tag_s, 4px);
        background: var(--kyy_color_tag_bg_warning, #ffe5d1);
        color: var(--kyy_color_tag_text_warning, #fc7c14);
        text-align: center;
        font-size: 12px;
        float: left;
      }
      .tc {
        color: var(--text-kyy-color-text-1, #1a2139);
        font-family: PingFang SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 22px; /* 157.143% */
        flex: 1;
        word-break: break-all;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        flex: 1 0 0;
        overflow: hidden;
        color: var(--text-kyy_color_text_1, #1A2139);
        text-overflow: ellipsis;
      }
    }
    .good-time {
      color: var(--text-kyy-color-text-3, #828da5);
      display: flex;
      gap: 2px;
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
      margin-top: 6px;
    }
  }
}
.mbtn {
  width: 28px;
  height: 28px;
  padding: 4px;
  text-align: center;
  font-size: 24px;
  line-height: 4px;
  cursor: pointer;
  // border: 1px solid #fff;
  .icon {
    font-size: 20px;
    color: #828da5;
  }
}
.mbtn:hover {
  border-radius: 4px;
  background: var(--bg-kyy-color-bg-brand-foucs, #dbdfff);
  color: #4d5eff;
  // border: 1px solid #4d5eff;
  .icon {
    font-size: 20px;
    color: #4d5eff;
  }
}
.actions{
  display: flex;
  gap: 8px;
}
.actions-boxs {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  width: 136px;
  min-height: 80px;
  .item {
    display: flex;
    height: 32px;
    min-width: 136px;
    min-height: 32px;
    max-height: 32px;
    padding: 0px 16px;
    align-items: center;
    gap: 12px;
    align-self: stretch;
    cursor: pointer;
    .icon {
      width: 20px;
      height: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      img {
        width: 16px;
        height: 16px;
      }
    }
    .text {
      color: var(--kyy_color_dropdown_text_default, #1a2139);

      /* kyy_fontSize_2/regular */
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
      // margin-left: 4px;
    }
  }
  .item:hover {
    border-radius: var(--kyy_radius_dropdown_s, 4px);
    background: var(--kyy_color_dropdown_bg_hover, #f3f6fa);
  }
  .item-view {
    border-top: 1px solid var(--divider-kyy-color-divider-light, #eceff5);
  }
}

.channel_type {
  display: flex;
  justify-content: center;
  flex-direction: column;
  .channel_item {
    display: flex;
    align-items: center;
  }
  .tagr {
    width: 8px;
    height: 8px;
    border-radius: 8px;
    margin-right: 8px;
  }
  .type-tag {
    background-color: #4d5eff;
  }
  .type-tag1 {
    background-color: #d5dbe4;
  }
}

// :deep(.have-pagination .t-table__content) {
//   max-height: calc(100vh - 180px) !important;
// }
// :deep(.no-pagination .t-table__content) {
//   max-height: calc(100vh - 140px) !important;
// }

// :deep(.have-pagination1 .t-table__content) {
//   max-height: calc(100vh - 230px) !important;
// }
// :deep(.no-pagination1 .t-table__content) {
//   max-height: calc(100vh - 168px) !important;
// }

.channel-box {
  gap: 4px;
  display: flex;
  flex-direction: column;
  .item {
    display: flex;
    padding: 4px;
    align-items: center;
    gap: 4px;
    border-radius: 2px;
    background: var(--bg-kyy_color_bg_deep, #f5f8fe);
    .name {
      display: flex;
      width: 64px;
      height: 24px;
      justify-content: center;
      padding: 0px 4px;
      align-items: center;
      color: var(--text-kyy_color_text_1, #1a2139);
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
      border-radius: 2px;
      background: var(--bg-kyy_color_bg_light, #fff);
      margin-left: 2px;
    }
    .status {
      display: flex;
      height: 24px;
      justify-content: center;
      align-items: center;
      padding: 0px 8px;
      border-radius: 2px;
      background: var(--bg-kyy_color_bg_light, #fff);
    }
    .status00 {
      color: var(--kyy_color_tag_text_gray, #516082);
      text-align: center;
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px; /* 166.667% */
    }
    .status0 {
      color: var(--brand-kyy_color_brand_default, #4d5eff);
      text-align: right;
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
    }
    .status1 {
      color: var(--warning-kyy_color_warning_default, #fc7c14);
      text-align: right;
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
    }
    .status2,
    .status3,
    .status4 {
      color: var(--error-kyy_color_error_default, #d54941);
      text-align: right;
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
    }
    .opt {
      display: flex;
      width: 58px;
      height: 24px;
      justify-content: center;
      align-items: center;
      padding: 0px 4px;
      border-radius: 2px;
      background: var(--bg-kyy_color_bg_light, #fff);
      color: var(--kyy_color_dropdown_text_default, #1a2139);
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
      cursor: pointer;
    }
  }
}

.exsty{
  height: 24px;
  min-height: 24px;
  max-height: 24px;
  border-radius: 50px;
  font-size: 14px;
  font-weight: 600;
  width: fit-content;
}

</style>
