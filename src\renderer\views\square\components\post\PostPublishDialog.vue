<template>
  <span>
    <t-dialog
      v-model:visible="visible"
      :footer="false"
      :close-on-overlay-click="false"
      width="600"
      attach="body"
      placement="center"
      prevent-scroll-through
      class="square-d-publish-trends"
      :z-index="zIndex"
      :close-btn="false"
    >
      <template #header>
        <kyy-tabs
          v-if="visible"
          :key="refreshKey"
          class="tabs"
          :default-value="activeTab"
          :list="tabs"
          :bottom-border="false"
          animation
          align="left"
          height="36px"
          size="md"
          @change="tabChange"
        />

        <div class="t-dialog__close" @click="dialogClose">
          <iconpark-icon name="iconerror" class="text-24 text-#1A2139 cursor-pointer" />
        </div>
      </template>

      <PostPublish
        v-if="visible"
        ref="postPublishRef"
        :key="refreshKey"
        :visible="visible"
        :show-article="false"
        :node-id="nodeId"
        :type="type"
        :only-text="onlyText"
        :extra-data="extraData"
        :mode="activeValue"
        v-bind="$attrs"
        @error="onError"
        @submit="submit"
        @close-dialog="visible = false"
        @certified-gov="(e) => { emit('certified-gov', e); businessGovernmentVisible = true }"
      />
    </t-dialog>

    <!--组织发布动态需扫码验证-->
    <PublishValidateDialog
      v-if="!store.isPersonal || teamId"
      ref="publishValidateRef"
      @close="publishValidateClose"
      @success="emit('submit')"
    />

    <BusinessGovernment
      v-if="!store.isPersonal && store.squareInfo.organizationProfile.type === SquareType.Government"
      v-model:visible="businessGovernmentVisible"
      :team-id="store.squareInfo?.organizationProfile?.teamId"
      :org-type="store.squareInfo?.organizationProfile?.type as unknown as OrganizationType"
      :region="store.squareInfo?.square?.regionCode as ('CN' | 'MO')"
      @success="businessGovernmentVisible = false"
    />
  </span>
</template>

<script lang="ts">
export default {
  inheritAttrs: false,
};
</script>

<script setup lang="ts">
import { computed, ref, useAttrs, watch } from 'vue';
import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next';
import to from 'await-to-js';
import { useI18n } from 'vue-i18n';
import { useRoute } from 'vue-router';
import debounce from 'lodash/debounce';
import { PostContentTemplateA, ProductType } from '@renderer/api/square/models/post';
import KyyTabs from '@renderer/views/square/components/Tabs.vue';
import PostPublish from '@/views/square/components/post/PostPublish.vue';
import { addDraft, updateDraft } from '@/api/square/post';
import PublishValidateDialog from '@/views/square/components/post/PublishValidateDialog.vue';
import { useSquareStore } from '@/views/square/store/square';
import { PostType } from '@/views/square/constant';
import { OrganizationType, SquareType } from '@/api/square/enums';
import BusinessGovernment from '@/components/orgAuth/government.vue';
import { useStateStore } from '@/views/square/store/state';

const props = defineProps<{
  modelValue: boolean,
  source?: 'article' | 'draft',
  draft?: boolean,
  // 指定上传类型（相册节点时传 PostType.AlbumNode）
  type?: PostType,
  // 相册节点id, 相册节点时必传
  nodeId?: string,
  extraData?: PostContentTemplateA,
  onlyText?: boolean,
  zIndex?: number,
  // 发布模式：图文/视频
  mode?: PostType.Picture | PostType.Video,
}>();
const emit = defineEmits(['update:modelValue', 'error', 'submit', 'close', 'updateDraft', 'certified-gov']);

const { t } = useI18n();
const visible = computed({
  get() {
    return props.modelValue;
  },
  set(value) {
    emit('update:modelValue', value);
  },
});

const store = useSquareStore();
const stateStore = useStateStore();
const attr = useAttrs();

const teamId = computed(() => attr?.['team-id'] || attr?.teamId);

const postPublishRef = ref(null);
const businessGovernmentVisible = ref(false);
const refreshKey = ref(0);
const loading = ref(false);
let formDataBak = '';

const route = useRoute();
const isInSquare = computed(() => route.path.includes('/square'));

const tabs = computed(() => {
  const baseTabs = [{ label: '发布图文', value: PostType.Picture }];
  // 仅在广场模块中显示视频发布选项;文章发布时仅显示图文
  if (isInSquare.value && props.source !== 'article') {
    baseTabs.push({ label: '发布视频', value: PostType.Video });
  }
  return baseTabs;
});
const activeTab = ref(0);
const activeValue = ref<PostType.Picture | PostType.Video>(PostType.Picture);

const tabChange = (item: { value: PostType.Picture | PostType.Video; }, index: number) => {
  activeTab.value = index;
  activeValue.value = item.value;

  // postPublishRef.value.postData.post.text = '';
  // delete postPublishRef.value.postData.post.location;
};

watch(() => visible.value, (val) => {
  if (!val) return;

  // 默认图文
  activeValue.value = props.mode || PostType.Picture;
  activeTab.value = tabs.value.findIndex((item) => item.value === activeValue.value);
  refreshKey.value++;

  // 发文章仅显示图文
  if (props.source === 'article') {
    activeValue.value = PostType.Picture;
    activeTab.value = 0;
  } else if (postPublishRef.value) {
    postPublishRef.value.postData.post.postType = props.type || PostType.Text;
  }

  setTimeout(() => {
    formDataBak = JSON.stringify(postPublishRef.value?.postData);
  }, 400);
});

const hasError = ref(false);
const onError = async () => {
  hasError.value = true;
  // visible.value = false;
  emit('error');
};

// 用于防止重复触发确认弹窗的标志位
const isConfirmDialogOpen = computed(() => stateStore.isConfirmDialogOpen);

// 关闭弹窗
const dialogClose = debounce(() => {
  const data = postPublishRef.value?.postData;

  // 如果确认弹窗已经打开，直接返回
  if (isConfirmDialogOpen.value) {
    return;
  }

  // 视频上传中，关闭弹窗时中断视频上传
  if (data.post.postType === PostType.Video && postPublishRef.value?.video?.status === 'loading') {
    postPublishRef.value?.rmVideo();
    // eslint-disable-next-line no-param-reassign
    data.post.postType = PostType.Text;
    // eslint-disable-next-line no-param-reassign
    delete data.post.product;
  }

  // 检查是否需要显示确认弹窗
  const needConfirm = postPublishRef.value?.canPublish
    && !postPublishRef.value?.draftSaved
    && !hasError.value
    && attr.draft === undefined
    && props.source !== 'article'
    && (!props.draft || formDataBak !== JSON.stringify(postPublishRef.value?.postData));

  // 如果不需要确认，直接关闭
  if (!needConfirm) {
    visible.value = false;
    return;
  }

  // 设置确认对话框状态
  stateStore.isConfirmDialogOpen = true;

  const confirmDia = DialogPlugin.confirm({
    header: '提示',
    body: t('square.post.draftTip'),
    theme: 'info',
    confirmBtn: t('square.action.save'),
    cancelBtn: t('square.action.cancel'),
    closeOnOverlayClick: false,
    zIndex: 2602,
    onConfirm: async () => {
      try {
        await saveDraft(data);
        emit('updateDraft');
      } finally {
        confirmDia.destroy();
        stateStore.isConfirmDialogOpen = false;
        visible.value = false;
      }
    },
    onClose: () => {
      confirmDia.destroy();
      visible.value = false;
      setTimeout(() => {
        stateStore.isConfirmDialogOpen = false;
      }, 200);
    },
  });
}, 1000, {
  leading: true,
});

// 保存草稿
const saveDraft = async (params) => {
  if (loading.value) return;

  let api = addDraft;
  const postId = params.post.id;
  const data = { ...params };

  // 编辑草稿
  if (props.draft && postId) {
    api = updateDraft;
    if (postId) data.post_id = postId;
  }

  if (PostType.Video !== data.post.postType) {
    delete data.post.product;
  }

  // 清除无用数据
  if (PostType.Video === data.post.postType) {
    // TODO 与 PostPublish 重复
    if (data.post.product) {
      const { type, url, niche } = data.post.product;
      if (type === ProductType.URL) {
        if (!url && data.post.product) delete data.post.product;
        if (data.post.product) delete data.post.product.niche;
      } else if (type === ProductType.Niche && !niche?.uuid) {
        delete data.post.product;
      }
    }
  }

  loading.value = true;
  const [err] = await to(api(data, teamId.value as string));
  // TODO：待优化,理论上不需要使用计时器
  loading.value = false;
  if (err) return;

  await MessagePlugin.success(t('square.saveSuccessTip'));
  postPublishRef.value?.resetForm();

  await store.getSquareStats();
  emit('submit');
};

const publishValidateRef = ref(null);
const draftTmp = ref();
const submit = ({ draft, data }) => {
  draftTmp.value = draft;

  // 传入 teamId 表示发布到指定组织
  if (data?.qrToken && (!store.isPersonal || teamId.value)) {
    publishValidateRef.value.open(data.qrToken);
  } else {
    emit('submit', draft);
    refreshKey.value++;
    visible.value = false;
  }
};

const publishValidateClose = () => {
  emit('close', draftTmp.value);
  emit('submit', draftTmp.value);
  visible.value = false;
};
</script>

<style lang="less">
.square-d-publish-trends {
  .t-dialog {
    padding: 0;
    background-image: url('@/assets/square/gradient-bg.png');
    background-size: cover;
    background-position: center;
    min-height: 334px;
  }

  .t-dialog__header {
    padding: 18px 24px;

    .tabs {
      padding: 0;
    }
  }

  .t-dialog__close {
    align-self: center;
  }

  .t-dialog__body {
    padding: 0;
    border-radius: 0 0 16px 16px;
  }

  .uploader {
    margin: 0;
  }

  .footer {
    display: flex;
    .item {
      display: flex;
      align-items: center;
      margin-right: 24px;
      font-size: 14px;
      font-weight: 400;
      text-align: left;
      color: #717376;
      cursor: pointer;
    }
    .icon {
      font-size: 20px;
      margin-right: 4px;
    }
  }
}
</style>
