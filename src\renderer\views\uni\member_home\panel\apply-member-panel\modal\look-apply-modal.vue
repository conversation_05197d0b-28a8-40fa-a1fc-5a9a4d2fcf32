<template>
  <t-drawer
    v-model:visible="visible"
    class="drawerSet drawerSetForm"
    header="详情"
    :z-index="1500"
    :on-confirm="onClickConfirm"
    :close-btn="true"
    v-bind="$attrs"
    @close="onClose"
    :close-on-overlay-click="false"
    :footer="data?.status === 1 ? true: false"
    :size="'376px'"
  >
    <template #closeBtn>
      <!-- <svg class="iconpark-icon" style="width: 16px; height: 16px">
        <use href="#close" />
      </svg> -->
      <iconpark-icon name="iconerror" style="font-size: 24px; color: #516082;"></iconpark-icon>
    </template>
    <template #footer>
      <div v-if="data" class="operates">
        <!-- <t-button
          theme="default"
          variant="outline"
          class="operates-item"
          @click="onClose"
        >取消</t-button> -->
        <t-button
          v-show="data.status === 1"
          theme="danger"
          variant="outline"
          class="operates-item"
          @click="onReject"
        >
          <iconpark-icon class="iconerror" name="iconerror"></iconpark-icon>
          {{ $t("member.bolit.g") }}
        </t-button>
        <t-button
          v-show="data.status === 1"
          theme="primary"
          variant="outline"
          class="operates-item"
          @click="onSuccess"
        >
          <iconpark-icon class="iconcorrect" name="iconcorrect"></iconpark-icon>
          {{ $t("member.bolit.h") }}
        </t-button>
      </div>
    </template>
    <div v-if="data && data.submit_data && visible" class="drawerSet-body">
      <form-detail
        ref="runtimeRef"
        :widgets="data.submit_data.free_form"
        @release="releaseRun"
      />

      <div v-if="data.record && data.record.length > 0" class="sports">
        <span class="sports-title mb-8px block">{{
          $t("member.active.trends")
        }}</span>
        <t-steps
          layout="vertical"
          theme="dot"
          :current="data.record.length"
          readonly
        >
          <t-step-item
            v-for="(record, recordIndex) in data.record"
            :key="recordIndex"
          >
            <template #title>
              <div class="toTitle">{{ record.created_at }}</div>
            </template>
            <template #content>
              <div class="toContent">{{ record.content }}</div>
              <div v-show="record.operator_name" class="toContent">
                操作人：{{ record.operator_name }}
              </div>
            </template>
          </t-step-item>
        </t-steps>
      </div>
    </div>
  </t-drawer>
  <RejectModal ref="rejectModalRef" @on-send="onSaveReject" />
  <SuccessModal ref="successModalRef" @on-send="onSaveSuccess" />
</template>

<script setup lang="ts">
import { ref, Ref, reactive, watch, computed } from "vue";
import { DialogPlugin, MessagePlugin } from "tdesign-vue-next";
import formDetail from "@renderer/components/free-from/detail/index.vue";
import {
  getMemberSettingAxios,
  postMemberApplyRejectAxios,
  postMemberApplyAgreeAxios,
  getMemberJobsDetailAxios,
} from "@renderer/api/uni/api/businessApi";
import RejectModal from "@renderer/views/uni/member_home/panel/apply-member-panel/modal/reject-modal.vue";
import SuccessModal from "@renderer/views/uni/member_home/panel/apply-member-panel/modal/success-modal.vue";
import { useI18n } from "vue-i18n";
import { getResponseResult } from "@/utils/myUtils";
import { getUniTeamID } from "@renderer/views/uni/utils/auth";
import { platform } from "@renderer/views/digital-platform/utils/constant";
import { useRoute } from "vue-router";
import { useDigitalPlatformStore } from "@renderer/views/digital-platform/store/digital-platform-store";

// import { ClientSide } from "@renderer/types/enumer";
const { t } = useI18n();
// 运行时
const controls = ref([]);
const runtimeRef = ref(null);

const organizeSelectCompRef = ref(null);

const visible = ref(false);

const data = ref(null);
const emits = defineEmits(["reload", "onClose"]);
const props = defineProps({
  platform: {
    type: String,
    default: '',
  },
});


const route = useRoute();
const digitalPlatformStore = useDigitalPlatformStore();
// 平台类型 目前只有digital-platform
const platformCpt = computed(() => {
  return props.platform || route.query?.platform
})

// const activeAccount = computed(() => {
//   if(platformCpt.value === platform.digitalPlatform) {
//     return digitalPlatformStore.activeAccount
//   } else if(platformCpt.value === platform.digitalWorkbench)  {
//     return route.query;
//   } else {
//     return memberStore.activeAccount
//   }
// })

const currentTeamId = computed(() => {
  if(platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount?.teamId
  } else if(platformCpt.value === platform.digitalWorkbench){
    return route.query?.teamId || 0;
  } else if(platformCpt.value === platform.message) {
    console.log('platform.message');
    return data.value?.teamId;
  } else {
    return  getUniTeamID()
  }
})

watch(
  () => visible.value,
  (cur) => {}
);

const releaseRun = (data) => {
  console.log(data);
};
const onInitFreeForm = (origins: any) => {
  if (origins && origins.submit_data && origins.submit_data.free_form) {
    const baseList = origins.submit_data.free_form.filter(
      (v: any) => v.type === "BaseInfoPolitics"
    );
    baseList.map((v: any) => {
      // 会员级别的设置
      let origin: any = null;
      origin = v.origin.find((or: any) => or.vModel === "memberLevel");
      if (origin) {
        const levelItem = origin.options.find(
          (v: any) => v.id === origin.value
        );
        origin.value = levelItem ? levelItem.level_name : "";
      }

      origin = v.origin.find((or: any) => or.vModel === "phone");
      if (origin) {
        origin.code_value = Number(origins.submit_data.telCode);
        // origin.value = origin_data.telephone;
      }

      // 组织名称
      origin = v.origin.find((or: any) => or.vModel === "organizeName");
      if (origin && origin.value) {
        // origin.value = origin.value.teamFullName;
      }

      // avatar头像
      origin = v.origin.find((or: any) => or.vModel === "logo");
      if (origin && origin.value && origin.value.length > 0) {
        // origin.value = origin.value[0].file_name;
      }

      // 组织logo
      //   origin = v.origin.find((or: any) => or.vModel === "organizeLogo");
      //   if (origin && origin.value && origin.value.length > 0) {
      //     origin.value = origin.value[0].file_name;
      //   }

      // 组织类型
      origin = v.origin.find((or: any) => or.vModel === "organizeType");
      if (origin) {
        const item = origin.options.find((v: any) => v.value === origin.value);
        origin.value = item ? item.label : "";
      }

      // 所在行业
      origin = v.origin.find((or: any) => or.vModel === "industryType");
      if (origin) {
        origin.value = origins.submit_data.industry_text;
      }

      // 组织规模
      origin = v.origin.find((or: any) => or.vModel === "organizeScale");
      if (origin) {
        origin.value = origins.submit_data.size_text;
      }
      // 组织地址
      origin = v.origin.find((or: any) => or.vModel === "organizeAddress");
      if (origin) {
        origin.value =
          origins.submit_data.country +
          origins.submit_data.province +
          origins.submit_data.city +
          origins.submit_data.district;

        origin.detail = origins.submit_data.address;
      }
      return v;
    });

    // controls.value = origins.submit_data.free_form;
  }
  //   else {
  //     controls.value = [];
  //   }
};

// 驳回
const rejectModalRef = ref(null);
const onReject = () => {
  rejectModalRef.value.onOpen({ id: data.value.id });
};
const onSaveReject = async (val) => {
  let result = null;
  try {
    result = await postMemberApplyRejectAxios({
      id: val.id,
      content: val.area,
    }, currentTeamId.value);
    result = getResponseResult(result);
    if (!result) return;
    MessagePlugin.success("拒绝成功");
    rejectModalRef.value.onClose();
    onClose();
    emits("reload");
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    MessagePlugin.error(errMsg);
  }
};

// 通过

const onGetMemberSetting = async () => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await getMemberSettingAxios();
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      resolve(result.data);
    } catch (error) {
      const errMsg = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
      reject();
    }
  });
};

const successModalRef = ref(null);

const onSuccess = () => {
  // console.log(data.value);
  // if (!data.value.level) {
  //   return MessagePlugin.error("缺少会员职务id");
  // }
  // onGetMemberSetting().then((res) => {
  //   successModalRef.value.onOpen(data.value, res);
  // });

  const confirmDia = DialogPlugin({
    header: '提示',
    theme: 'info',
    // body: '确定审核通过？',
    body: t('member.active.sure_to_passed'),

    closeBtn: null,
    confirmBtn: t('member.bolit.h'),
    className: 'delmode',
    onConfirm: async () => {
      onSaveSuccess({id: data.value.id})
      confirmDia.hide();
    },
    onClose: () => {
      confirmDia.hide();
    },
  });
};
const onSaveSuccess = async (val: any) => {
  let result = null;
  try {
    result = await postMemberApplyAgreeAxios(val, currentTeamId.value);
    result = getResponseResult(result);
    if (!result) return;
    MessagePlugin.success("审核成功");
    successModalRef.value.onClose();
    onClose();
    emits("reload");
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    MessagePlugin.error(errMsg);
    successModalRef.value.onClose();
    onClose();
    emits("reload");
  }
};

const onClickConfirm = async () => {};

const onOpen = (item?: any) => {
  controls.value = [];
  data.value = null;

  onInitFreeForm(item);
  data.value = item;
  visible.value = true;
  //   visible.value = true;
};
const onClose = () => {
  visible.value = false;
  emits('onClose')
};
const onNoShow = () => {
  visible.value = false;
}

defineExpose({
  onOpen,
  onClose,
  onNoShow,
});
</script>
<style lang="less" scoped>

:deep(.detail-control) {

  .value {
    padding-right: 0 !important;

    .file-box {
      .text-hide2 {
        max-width: 240px ;
      }
      &:hover {
        .text-hide2 {
          max-width: 180px ;
        }
      }
    }
    .img-view {
      width: 74px;
    }
  }
}

.iconerror {
  font-size: 20px;
  color: #D54941;
}

.iconcorrect {
  font-size: 20px;
}

.operates {
  display: flex;
  gap: 4px;
  justify-content:flex-end;
  &-item {

  }
}


:deep(.t-steps--vertical).t-steps--dot-anchor
  .t-steps-item--finish
  .t-steps-item__icon {
  border-color: #c7c7c8;
  background: #c7c7c8;
}

:deep(.t-steps-item--finish) {
  &::before {
    border-right-color: #c7c7c8 !important;
    color: #c7c7c8 !important;
    left: 2.5px;
    top: 24px;
  }
}

.sports {
  &-title {
    font-size: 14px;

    font-weight: 400;

    color: #717376;
  }
}
.toTitle {
  font-size: 14px;

  font-weight: 400;

  // color: #13161b;
}
.toContent {
  color: var(--text-kyy-color-text-1, #1a2139);

  /* kyy_fontSize_2/regular */
  font-family: PingFang SC;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
}
.operates {
  display: flex;
  justify-content: flex-end;
  &-item {
    min-width: 80px;
  }
}
.drawerSet {
  // width: 720px;
  &-body {
  }
  //   .t-drawer__content-wrapper {
  //     width: 720px !important;
  //   }
}

:deep(.t-drawer__header) {
  border-bottom: 0;
  color: red;
}
:deep(.t-drawer__body) {
  padding-top: 10px !important;
}
</style>
