import { getCommonAppAuthAxios } from '@renderer/api/digital-platform/api/businessApi';
import { PlatformType } from '@renderer/api/forum/models/forum';
import { PermRequest } from '@renderer/api/forum/models/user';
import { getTopicInfo } from '@renderer/api/forum/topic';
import { getPerms } from '@renderer/api/forum/user';
import to from 'await-to-js';
import { MessagePlugin } from 'tdesign-vue-next';

// 获取平台类型
export const getPlatformType = async (teamId: string) => {
  const [err, { data }] = await to(getCommonAppAuthAxios({ teamId }, teamId));
  if (err) {
    await MessagePlugin.error('当前组织类型异常');
    return null; 
  }

  const resourceTypes: PlatformType[] = ['member', 'government', 'cbd', 'association','uni'];
  // 检查是否存在授权
  for (const type of resourceTypes) {
    const auth = data.data.find((v) => v.uuid === type)?.auth;
    if (auth) return type;
  }

  return null;
};

// 获取权限
export const checkPerms = async (params?: PermRequest) => {
  const [err, res] = await to(getPerms(params || {}));
  if (err) return null;
  return res.data.data.perms;
};

// 校验话题是否有效
export const checkTopicValid = async (topicID: string, tip = true) => {
  const [err, res] = await to(getTopicInfo(topicID));
  if ((err || res.data.data.status === 'SHIELD')) {
    tip && MessagePlugin.error('该话题已失效');
    return false;
  }
  return true;
};
