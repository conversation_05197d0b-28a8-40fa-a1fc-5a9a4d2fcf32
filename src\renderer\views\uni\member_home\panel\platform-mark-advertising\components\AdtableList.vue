<template>
  <div class="bus">
    <div class="right-box" style="flex: 1; width: 0; margin-top: 16px">
      <div class="right-content">
        <div class="data-box-header">
          <div style="position: relative">
            <div class="tab-box">
              <div :class="['default-tab-item', tabValue === 'Unspecified' ? 'active-tab-item' : '']">
                <t-button theme="default" class="tab-btn" variant="text" @click="changeTab('Unspecified')">
                  {{ t("ad.gggl") }}
                  <span style="padding-left: 4px" v-if="marketAdNum > 0">{{ marketAdNum }}</span></t-button
                >
                <div class="tab-item-border"></div>
              </div>
              <div :class="['default-tab-item', tabValue === 'NormalActivity' ? 'active-tab-item' : '']">
                <t-button class="tab-btn" theme="default" variant="text" @click="changeTab('NormalActivity')">
                  {{ t("ad.ggwsz") }}</t-button
                >
                <div class="tab-item-border"></div>
              </div>
            </div>

            <t-button @click="add" class="addbtn" theme="primary">
              <template #icon><add-icon /></template>
              {{ t("ad.xjgg") }}
            </t-button>
          </div>
          <div v-if="tabValue === 'Unspecified'" class="top">
            <div class="tags">
              <div class="radioButtonBox">
                <div
                  @click.stop="radioChange(0)"
                  :class="[params.status === 0 ? 'activeRadioButton' : 'defaultRadioButton']"
                >
                  {{ t("ad.qb") }}
                </div>
                <div
                  @click.stop="radioChange(1)"
                  :class="[params.status === 1 ? 'activeRadioButton' : 'defaultRadioButton']"
                >
                  {{ t("ad.wks") }}
                </div>
                <div
                  @click.stop="radioChange(2)"
                  :class="[params.status === 2 ? 'activeRadioButton' : 'defaultRadioButton']"
                >
                  {{ t("ad.tfz") }}
                </div>
                <div
                  @click.stop="radioChange(6)"
                  :class="[params.status === 6 ? 'activeRadioButton' : 'defaultRadioButton']"
                >
                  {{ t("ad.dsh") }} <span style="padding-left: 4px" v-if="marketAdNum > 0">{{ marketAdNum }}</span>
                </div>
                <div
                  @click.stop="radioChange(3)"
                  :class="[params.status === 3 ? 'activeRadioButton' : 'defaultRadioButton']"
                >
                  {{ t("ad.yjs") }}
                </div>

                <t-popup placement="bottom-right">
                  <span class="cur-lan">
                    <div :class="[[4, 5, 7, 8].includes(params.status) ? 'activeRadioButton' : 'defaultRadioButton']">
                      {{ t("ad.gd") }}
                      <iconpark-icon class="icon-down" name="iconarrowdown"></iconpark-icon>
                    </div>
                  </span>
                  <template #content>
                    <div>
                      <t-list>
                        <t-list-item class="list-item" v-for="item in options" @click="radioChange(item.id)">
                          {{ item.content }}
                        </t-list-item>
                      </t-list>
                    </div>
                  </template>
                </t-popup>
              </div>
            </div>
            <div class="approval-time">
              <div @keyup.enter="getDataRun(true)">

        <!-- @compositionend="getDataRun(true)"  @change="getData" -->
                <t-input
                  v-model.trim="params.keyword"
                  style="width: 304px"
                  :placeholder="t('ad.ssggidggmc')"
                  clearable
                  @clear="getDataRun(true)"
                  @change="getDataRun(true)"
                  @blur="getDataRun(true)"  @change="getDataRun(true)"
                >
                  <template #prefixIcon
                    ><img style="width: 20px; height: 20px" src="@renderer/assets/zhixing/icon_search.svg"
                  /></template>
                </t-input>
              </div>
              <div v-if="paramsSuper" class="af-icon" @click="showFilter">
                <img src="@renderer/assets/approval/icons/fbutton.svg" style="width: 32px; height: 32px" />
              </div>
              <div v-else class="f-icon" @click="showFilter">
                <img src="@renderer/assets/approval/icons/icon_screen.svg" style="width: 20px; height: 20px" />
              </div>
            </div>
          </div>
          <div v-if="paramsSuper && tabValue === 'Unspecified'" class="filter-res">
            <div class="tit">{{ t("approval.approval_data.sures") }}</div>
            <div v-if="params['created_begin'] || params['created_end']" class="ov-time te">
              <span>{{ t("ad.cjsj") }}： {{ params["created_begin"] }} ~ {{ params["created_end"] }}</span>
              <span class="close2" @click="clearFilterCategories('duration')">
                <img src="@renderer/assets/approval/icons/close2.svg" alt="" />
              </span>
            </div>
            <div v-if="params.place_type" class="stat te">
              <span>{{ t("ad.tfwz") }}：{{ formatCategoriesText(params.place_type) }}</span>
              <span class="close2" @click="clearFilterCategories('place_type')">
                <img src="@renderer/assets/approval/icons/close2.svg" alt="" />
              </span>
            </div>
            <div v-if="params.skip_type" class="stat te">
              <span>{{ t("ad.tzlx") }}：{{ formatCategoriesText(params.skip_type, "skip_type") }}</span>
              <span class="close2" @click="clearFilterCategories('skip_type')">
                <img src="@renderer/assets/approval/icons/close2.svg" alt="" />
              </span>
            </div>
            <div v-if="params.main_body_type" class="stat te">
              <span>类型：{{ formatCategoriesText(params.main_body_type, "main_body_type") }}</span>
              <span class="close2" @click="clearFilterCategories('main_body_type')">
                <img src="@renderer/assets/approval/icons/close2.svg" alt="" />
              </span>
            </div>
            <div class="icon clearFiltersBox" @click="clearFilters">
              <img src="@renderer/assets/approval/icons/del8.svg" alt="" />
              <span class="clearFilters">{{ t("approval.approval_data.clearFilters") }}</span>
            </div>
          </div>
        </div>
        <div v-if="tabValue === 'Unspecified'" class="data-box-con" style="padding-bottom: 16px;">
          <t-table
            :row-key="'id'"
            :data="listData"
            :loading="loading"
            cell-empty-content="--"
            :columns="columns"
            :rowspan-and-colspan="rowspanAndColspan"
            :hover="false"
            :keyboardRowHover="false"
          >
            <template #title="{ row }">
              <div class="table-item" @click.stop="showDetail(row.id)">
                <div class="tab-header">
                  <div class="tab-dis">{{ row.title }}</div>
                  <div class="tab-id">{{ t("ad.ggid") }} : {{ row.uuid }}</div>
                </div>
                <div class="row-box">
                  <div class="fist-box">
                    <div class="body-box">
                      <img class="body-box-img" :src="row.image_url" alt="" />
                      <div class="info-box">
                        <div class="info-item">
                          <div class="lables">{{ t("ad.tfzt") }}：</div>
                          <div class="values">
                            <div
                              class="tags"
                              style="margin-right: 4px"
                              :class="row?.main_body_type === 1 ? 'pt' : 'ptsf'"
                            >
                              {{ row?.main_body_type === 1 ? "平台" : "平台成员" }}
                            </div>
                            <div class="value-text">{{ row.main_body_name }}</div>
                          </div>
                        </div>
                        <div class="info-item">
                          <div class="lables">{{ t("ad.tfwz") }}：</div>
                          <div class="values">
                            <div class="value-text">{{ row.place_name }}</div>
                          </div>
                        </div>
                        <div class="info-item">
                          <div class="lables">{{ t("ad.tfrq") }}：</div>
                          <div class="values">
                            <div class="value-text">{{ row?.begin_at }}～{{ row?.end_at }}</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="pay-box">{{row.symbol==='¥'?'¥':'MOP'}}{{ row.price }}</div>
                  <div class="tags-box">
                    <!-- row?.stats -->
                    <div
                      class="tags"
                      :style="{
                        color: fliterTag(row.status).color,
                        background: fliterTag(row.status).bgc,
                      }"
                    >
                      {{ fliterTag(row.status).title }}
                    </div>
                  </div>
                  <div class="option-box">
                    <span class="btn" v-if="[1, 2].includes(row.status)" @click.stop="openAddAdvertSingModleRef(row.id)">
                      {{ t("ad.bj") }}</span
                    >
                    <span class="btn" v-if="[1, 2].includes(row.status)" @click.stop="endAd(row.id)"> {{ t("ad.zz") }}</span>
                    <span v-if="[8, 7, 3, 4, 5].includes(row.status)" class="btn" @click.stop="delAd(row.id)"> 删除</span>
                    <span class="btn" v-if="row.status===6" @click.stop="examineAd(row.id)"> {{ t("ad.sh") }}</span>
                  </div>
                </div>
              </div>
            </template>

          </t-table>
          <noData v-if="!listData?.length && !loading" style="margin-top: 100px" :text="t('approval.no_data')" />

          <div v-if="total && total > 10" class="pagination">
            <t-pagination
            show-jumper
              :total="total"
              show-previous-and-next-btn
              :current="params['page']"
              @change="pageChange"
            />
          </div>
        </div>
        <advertisementsting v-if="tabValue === 'NormalActivity'" />
      </div>

      <editAdvertSingModle ref="addAdvertSingModleRef" @callBackFormData="callBackFormData"></editAdvertSingModle>
    </div>

    <t-drawer
    v-model:visible="filterVisible"
    :close-btn="true"
    size="472px"
    :header="t('approval.approval_data.sur')"
  >
    <div class="form-boxxx">
      <div class="fitem">
        <div class="title">{{ t("ad.cjsj") }}</div>
        <div class="ctl">
          <t-date-range-picker
          v-model="drawerForm.duration"
          format="YYYY-MM-DD"
          value-type="YYYY-MM-DD"
          style="width: 100%"
          :placeholder="[t('approval.approval_data.start_time'), t('approval.approval_data.end_time')]"
          clearable
        >
          <template #suffixIcon>
            <img src="@renderer/assets/identity/date_picker.svg" alt="" />
          </template>
        </t-date-range-picker>
        </div>
      </div>
      <div class="fitem">
        <div class="title">{{ t("ad.tfwz") }}</div>
        <div class="ctl">
          <t-select
            v-model="drawerForm.place_type"
            clearable
            :placeholder="t('approval.operation.select')"
          >
            <t-option
              v-for="item in activityTypeOption"
              :key="item.id"
              :value="item.id"
              :label="item.title"
            ></t-option>
            <template #suffixIcon>
              <img src="@/assets/svg/icon_arrow_down.svg" />
            </template>
          </t-select>
        </div>
      </div>
      <div class="fitem">
        <div class="title">状态</div>
        <div class="ctl">
          <t-select  v-model="drawerForm.status" clearable :placeholder="t('approval.operation.select')">
            <t-option v-for="item in statsList" :key="item.type" :value="item.type" :label="item.title"></t-option>
            <template #suffixIcon>
              <img src="@/assets/svg/icon_arrow_down.svg" />
            </template>
          </t-select>
        </div>
      </div>
      <div class="fitem">
        <div class="title">{{ t("ad.tzlx") }}</div>
        <div class="ctl">
          <t-select

            v-model="drawerForm.skip_type"
            clearable
            :placeholder="t('approval.operation.select')"
          >
            <t-option
              v-for="item in activityTypeOptiontzlx"
              :key="item.id"
              :value="item.id"
              :label="item.title"
            ></t-option>
            <template #suffixIcon>
              <img src="@/assets/svg/icon_arrow_down.svg" />
            </template>
          </t-select>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="foot">
        <div class="btn1" @click="() => (filterVisible = false)">
          {{ t("approval.approval_data.cl") }}
        </div>
        <div class="btn3" @click="getDataRunDr">
          {{ t("approval.approval_data.cm") }}
        </div>
        <!-- <div v-else class="btn2" @click="getDataRunDr">
              {{ t("approval.approval_data.cm") }}
            </div> -->
      </div>
    </template>
  </t-drawer>
    <!-- <t-image-viewer v-model:visible="viewer" :images="[imageFiles]" />

      <audio-add-members v-model:visible="shareVisible" :changeMenus="true" @confirm="confirmShare" />

      <cancelDialog v-model:visible="cancelVisible" @confirm="confirmCancel" />
      <AdvanceExtensionDialog
        v-model:visible="AdvanceVisible"
        :type="showAdvanceType"
        :data="activityItem"
        @confirm="confirmAdvance"
      /> -->
    <!-- <t-dialog v-model:visible="endFlag" :close-btn="false" :header="true" width="432">
        <template #header>
          <div style="display: flex; align-items: center; width: 100%; justify-content: space-between">
            <div>确定要提前终止这条广告内容吗?</div>
            <img
              style="width: 24px; cursor: pointer; height: 24px"
              src="@/assets/<EMAIL>"
              @click="(endFlag = false), (endValue = '')"
            />
          </div>
        </template>
        <div class="end-content-box">
          <div class="lab">终止原因</div>
          <t-textarea v-model="endValue" placeholder="请输入终止原因" :maxlength="200"></t-textarea>
        </div>
        <template #footer>
          <div class="footer">
            <t-button theme="default" variant="outline" @click="(endFlag = false), (endValue = '')"> 取消 </t-button>
            <t-button theme="primary" :disabled="!endValue" @click="onSave"> 确定 </t-button>
          </div>
        </template>
      </t-dialog> -->
    <stopAd @callBack="endCallBack" ref="stopAdRef"> </stopAd>
  </div>
</template>

<script setup lang="ts" name="activityListDraft">
import {
  adfrontadlist,
  admanageadedit,
  frontaddetail,
  adfrontadcancel,
  adfrontaddel,
} from "@renderer/api/member/api/ebookApi";
import { AddIcon, Icon } from "tdesign-icons-vue-next";
import { onMountedOrActivated } from "@/hooks/onMountedOrActivated";

import editAdvertSingModle from "@/views/member/member_home/panel/mark-advertising/editAdvertSingModle.vue";

import advertisementsting from "../advertisementsting.vue";
import moment from "moment";
import { computed, onActivated, onMounted, reactive, ref, watch, toRaw } from "vue";
import { DialogPlugin, MessagePlugin } from "tdesign-vue-next";
import { EllipsisIcon, ChevronRightIcon } from "tdesign-icons-vue-next";
import audioAddMembers from "@renderer/components/selectMember/audio-add-members.vue";
import noData from "@renderer/views/approve/approve_home/components/approvalData/noData.vue";
import AdvanceExtensionDialog from "@renderer/views/activity/components/AdvanceExtensionDialog.vue";
import cancelDialog from "@renderer/views/activity/components/cancelDialog.vue";

import { deleteActivity, extendsActivity, getActivityListDrafts } from "@renderer/api/activity";
import { useI18n } from "vue-i18n";
import { useRouter } from "vue-router";
import { useDigitalPlatformStore } from "@renderer/views/digital-platform/store/digital-platform-store";
import { getMemberTeamID } from "@renderer/views/member/utils/auth";
import { useRoute } from "vue-router";
import stopAd from "@/views/member/member_home/panel/mark-advertising/components/stopAd.vue";
import { useMemberStore } from "@renderer/views/member/store/member";
import { useActivityStore } from "@/views/activity/store";

import { platform } from "@renderer/views/digital-platform/utils/constant";
import useRouterHelper from "@renderer/views/square/hooks/routerHelper";
const { menuList, routeList, roleFilter } = useRouterHelper("digitalPlatformIndex");

const endValue = ref("");
const endFlag = ref(false);
const stopAdRef = ref(null);

const digitalPlatformStore = useDigitalPlatformStore();
const props = defineProps({
  platform: {
    type: String,
    default: "",
  },
  groupList: {
    type: Array,
    default: () => [],
  },
  tabList: {
    type: Array,
    default: () => [],
  },
  tabIndex: {
    type: Number,
    default: 0,
  },

  currentMemberCard: {
    type: Object,
    default: () => {},
  },
});
const route = useRoute();
const endAd = (val) => {
  stopAdRef.value.openWin(val, route.query.teamId || currentTeamId.value);
};
const router = useRouter();

const showDetail = (val) => {
  console.log({
      id: val.toString(),
      teamId: currentTeamId.value,
      flag: "view",
    },'123123123');
  router.push({
    path: `/digitalPlatformIndex/uni_adDetails`,
    query: {
      id: val.toString(),
      teamId: currentTeamId.value,
      flag: "view",
    },
  });
  const searchMenu = routeList.find((v) => v.name === "uni_adDetails");
  store.value.addTab(toRaw({
    ...searchMenu,
      query: {
        id: val.toString(),
        teamId: currentTeamId.value,
        flag: "view",
      },
  }), true);
};
const qxAd = (val) => {
  const confirmDia = DialogPlugin.confirm({
    header: t('ad.qxshm'),
    theme: "info",
    confirmBtn: t('ad.qxsh'),
    cancelBtn: "暂不取消",
    onConfirm: async () => {
      adfrontadcancel(
        {
          ad_id:val,
        },
        currentTeamId.value,
      )
        .then((res) => {
          if (res.data.code == 0) {
            getData();
            MessagePlugin.success("操作成功");

          }
          confirmDia.hide();
        })
        .catch((err) => {
          MessagePlugin.error(err.message);
        });
    },
    onClose: () => {
      confirmDia.hide();
    },
  });
};

const endCallBack = () => {
  getDataRun();
};
const platformCpt = computed(() => {
  return props.platform || route.query?.platform;
});
const store = computed(() => {
  // if (platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore;
  // }
  // return useMemberStore();
});
const currentTeamId = computed(() => {
  console.log(route.query.teamId || currentTeamId.value,'routerteamIdteamId');
  if (platformCpt.value === platform.digitalPlatform) {
    if (!digitalPlatformStore.activeAccount?.teamId) {
      console.log(route.query.teamId || currentTeamId.value,'routerteamIdteamId111111111');

      return route.query?.teamId || 0;
    }
    console.log(route.query.teamId || currentTeamId.value,'routerteamIdteamId2222222');

    return digitalPlatformStore.activeAccount?.teamId;
  } else if (platformCpt.value === platform.digitalWorkbench) {
    console.log(route.query.teamId || currentTeamId.value,'routerteamIdteamId333333');

    return route.query?.teamId || 0;
  } else {
    return route.query?.teamId || 0;
  }
});
const onSave = () => {};

const addAdvertSingModleRef = ref(null);
const { t } = useI18n();
const options = [
  {
    content:t('ad.shbtg'),
    id: 7,
  },
  {
    content: t('ad.cswsh'),
    id: 8,
  },
  {
    content: t('ad.yzz'),
    id: 4,
  },
  {
    content:t('ad.yqx') ,
    id: 5,
  },
];
const activityStore = useActivityStore();
const activityTypeOption = ref([
  { id: 9, title: "PC_市场店铺_主轮播图" },
  { id: 10, title: "PC_市场店铺_右上" },
  { id: 11, title: "PC_市场店铺_右下" },
  { id: 12, title: "APP_市场店铺_主轮播图" },
  { id: 1, title: "PC_市场_主轮播图" },
  { id: 2, title: "PC_市场_右上" },
  { id: 3, title: "PC_市场_右下" },
  { id: 4, title: "APP_市场_主轮播图" },
  { id: 5, title: "PC_平台_主轮播图" },
  { id: 6, title: "PC_平台_右上" },
  { id: 7, title: "PC_平台_右下" },
  { id: 8, title: "APP_平台_主轮播图" },

]);
const activityTypeOptiontzlx = ref([
  { id: 1, title: "商机" },
  { id: 2, title: "广场号" },
  { id: 3, title: "自定义链接" },
  { id: 4, title: "无跳转" },
]);
const activityTypeOptionType = ref([
  { id: 1, title: "平台" },
  { id: 2, title: "平台成员" },
]);
const statsList = [
  {
    title:t('ad.wks') ,
    type: 1,
    color: "#4093E0",
    bgc: "#E8F0FB",
  },
  {
    title:t('ad.tfz') ,
    type: 2,
    color: "#FC7C14",
    bgc: "#FFE5D1",
  },
  {
    title:t('ad.yjs') ,
    type: 3,
    color: "#499D60",
    bgc: "#E0F2E5",
  },
  {
    title: t('ad.yzz'),
    type: 4,
    color: "#D54941",
    bgc: "#F7D5DB",
  },
  {
    title: t('ad.dsh'),
    type: 6,
    color: "#4D5EFF",
    bgc: "#EAECFF",
  },
  {
    title:t('ad.shbtg') ,
    type: 7,
    color: "#D54941",
    bgc: "#F7D5DB",
  },
  {
    title: t('ad.cswsh'),
    type: 8,
    color: "#D54941",
    bgc: "#F7D5DB",
  },
  {
    title: t('ad.yqx'),
    type: 5,
    color: "#516082",
    bgc: "#ECEFF5",
  },
];
const infoData = ref(null);
const fliterTag = (val) => {
  return statsList.find((e) => val === e.type);
};
const openAddAdvertSingModleRef = (val) => {
  console.log(currentTeamId.value, "currentTeamId.valuecurrentTeamId.value");
  // addAdvertSingModleRef.value.openWin(currentTeamId.value);
  frontaddetail(val, route.query.teamId || currentTeamId.value).then((res) => {
    console.log(res, "呃呃我认为二位二位二位");
    infoData.value = res.data.data;
    let objs = {
      title: res.data.data.title,
      image_url: res.data.data.image_url,
      skip_type: res.data.data.skip_type,
      skip_param: res.data.data?.skip_param,
      remark: res.data.data.remark,
      businessItem: "",
      ad_id: val,
    };
    if (res.data.data.skip_type == "2") {
      objs.businessItem = res.data.data.square_data;
      objs.skip_param = res.data.data.square_data;
    }
    if (res.data.data.skip_type == "1") {
      objs.businessItem = res.data.data.niche_data;
      objs.skip_param = res.data.data.niche_data;
    }
    if (res.data.data.skip_type == "4") {
      objs.skip_param = null;
    }
    console.log(infoData.value, "infoData.valueinfoData.value");
    addAdvertSingModleRef.value.openWin(route.query.teamId || currentTeamId.value, true, objs, infoData.value);
  });
};
const approve_status_text = [
  "",
  t("approval.approval_data.under_approval"),
  t("approval.approval_data.pass"),
  t("approval.approval_data.refuse"),
  t("approval.approval_data.repeal"),
];

const emits = defineEmits(["setActiveIndexAndName", "setActiveIndex", "getRedNum"]);

const calcHeight = computed(() => {
  if (paramsSuper.value) {
    return total.value && total.value > 10 ? "calc(100vh - 282px)" : "calc(100vh - 242px)";
  }
  return total.value && total.value > 10 ? "calc(100vh - 242px)" : "calc(100vh - 182px)";
});

const formatCategoriesText = (val, flag) => {
  console.log(val, flag, "asdasd");
  if (flag === "skip_type") {
    return activityTypeOptiontzlx.value?.find((item) => item.id === val)?.title || "";
  }

  if (flag === "status") {
    return statsList.find((item) => item.type == val)?.title || "";
  }
  if (flag === "main_body_type") {
    return activityTypeOptionType.value?.find((item) => item.id === val)?.title || "";
  }
  return activityTypeOption.value?.find((item) => item.id === val)?.title || "";
};
const allFormatButton = [
  { text: "编辑", click: "rowEdit" },
  { text: "分享", click: "rowShare" },
  { text: "提前", click: "rowAdvance" },
  { text: "延期", click: "rowExtension" },
  { text: "取消", click: "rowCancel" },
];

const tabValue = ref("Unspecified");
const changeTab = (type) => {
  if (tabValue.value === type) return;
  tabValue.value = type;
  params.kind = type;
  getDataRun(true);
  emits("getRedNum");
};

const radioChange = (value) => {
  console.log("radioChange", value);
  params.status = value;
  pageChange({
    current: 1,
    pageSize: 10,
  });
};
onActivated(() => {
  console.log("onActivated-setActiveIndexAndName");
  if (props.currentMemberCard?.id) {
    getData();
  }
  if (activityStore.listRefreshTag) {
    getDataRun();
    activityStore.listRefreshTag = false;
  }
});

onMounted(() => {
  getDataRun();
});

const total = ref(0);
const params = reactive({
  // 1商协 2政企 3CBD
  platform_type: 5,
  keyword: "",
  status: "",
  place_type: "",
  skip_type: "",
  is_all: 0,
  teamId: "",
  created_begin: "",
  created_end: "",
  page: 1,
  page_size: 10,
});

const drawerForm = reactive({
  duration: [],
  place_type: "",
  status: "",
  skip_type: "",
  location: "",
});

const columns = [
  {
    colKey: "title",
    title:t('ad.gglb') ,
  },
  {
    colKey: "content",
    title:t('ad.fy') ,
    width: "200",
    ellipsis: true,
  },
  {
    colKey: "categoryTitle",
    title: "状态",
    width: "128",
  },
  {
    colKey: "actions",
    title: t("approval.approval_data.ops"),
    width: "112",
  },
];
watch(
  () => props.currentMemberCard,
  () => {
    getDataRun(true);
  },
);
// 切换组织刷新列表数据
watch(
  () => activityStore.activityItem,
  () => {
    getDataRun(true);
  },
);
const loading = ref(false);

const listData = ref([]);
const getData = () => {
  loading.value = true;

  adfrontadlist(
    {
      main_body_id: props.currentMemberCard?.id,
      ...params,
      teamId: currentTeamId.value,
    },
    currentTeamId.value,
  ).then((res) => {
    console.log(res);
    if (res.data) {
      listData.value = res.data.data.list;
      console.log(listData.value, "listData.valuelistData.value");
      total.value = res.data.data.count;
    }
    loading.value = false;

  });
};

const viewer = ref(false);
const imageFiles = ref([]);

const pageChange = (e) => {
  params["page"] = e.current;
  params["page_size"] = e.pageSize;
  getData();
};

const reqParamsHandle = () => {
  params["created_begin"] = drawerForm.duration?.[0] ;
  params["created_end"] = drawerForm.duration?.[1];
  params.place_type = drawerForm.place_type || "";
  // params.status = drawerForm.status || 0;

  params.skip_type = drawerForm.skip_type || "";
  params.location = drawerForm.location || "";
};
const getDataRun = (flag) => {
  reqParamsHandle();
  if (flag) {
    params.page=1
    params.page_size=10
  }
  if (props.currentMemberCard?.id) {

    getData();
  }

};

const filterVisible = ref(false);
const showFilter = () => {
  filterVisible.value = true;
};
const getDataRunDr = () => {
  console.log(drawerForm, "drawerFormdrawerFormdrawerForm");
  console.log(params, "drawerFormdrawerFormdrawerFormparamsparamsparams");
  filterVisible.value = false;

  getDataRun(true);
};
const clearFilters = () => {
  params["created_begin"] = "";
  params["created_end"] = "";
  params.location = "";
  params.skip_type = "";
  params.status = "";
  params.place_type = "";
  drawerForm.skip_type = "";
  drawerForm.status = "";
  drawerForm.place_type = "";
  drawerForm.duration = [];
  drawerForm.location = null;
  getDataRun(true);
};
const callBackFormData = (val) => {
  console.log(val, "vcallasdasdasd");
  let str = "";
  if (val.skip_type === 1) {
    str = val.skip_param.uuid;
  } else if (val.skip_type === 2) {
    str = val.skip_param.squareId;
  } else if (val.skip_type === 3) {
    str = val.skip_param;
  }else if (val.skip_type === 5) {
    str = val.skip_param?.spuId?val.skip_param?.spuId:val.skip_param;

      }
  // 编辑提交代码
  admanageadedit(
    {
      ad_id: val.ad_id,
      title: val.title,
      skip_type: val.skip_type,
      skip_param: str,
      image_url: val.image_url,
      remark: val.remark,
    },
    route.query.teamId || currentTeamId.value,
  )
    .then((res) => {
      console.log(res, "ressssss");
      if (res.data.code == 0) {
        MessagePlugin.success("编辑成功");
        getDataRun();
        addAdvertSingModleRef.value.closeWin();
      }
    })
    .catch((err) => {
      getDataRun();
      addAdvertSingModleRef.value.closeWin();
      MessagePlugin.error(err.message);
    });
};
const clearFilterCategories = (val) => {
  drawerForm[val] = "";
  params[val] = "";
  getDataRun();
};
const clearFilterLocation = () => {
  drawerForm.location = "";
  params.location = "";
  getDataRun();
};

const addAd = () => {
  // notTab: true,
  //区别配置
  router.push({
    path: `/digitalPlatformIndex/uni_addAd?platform_type=5&teamId=${currentTeamId.value}&currentMemberCard=${props.currentMemberCard?.id}`,
    query: {
      teamId: currentTeamId.value,
      platform_type: 5,
      currentMemberCard: props.currentMemberCard?.id,
    },
  });
  const searchMenu = routeList.find((v) => v.name === "uni_addAd");
  store.value.addTab(
    toRaw({
      ...searchMenu,
      query: {
        teamId: currentTeamId.value,
        platform_type: 5,
        currentMemberCard: props.currentMemberCard?.id,
      },
    }),
  );


};
const clearFilterDuration = () => {
  drawerForm.duration = [];
  params["created_begin"] = "";
  params["created_end"] = "";
  drawerForm.skip_type = "";
  drawerForm.status = "";
  drawerForm.place_type = "";
  params.skip_type = "";
  params.status = "";
  params.place_type = "";
  getDataRun(true);
};
const paramsSuper = computed(
  () =>
    params.location ||
    params.status ||
    params.skip_type ||
    params.place_type ||
    params.type ||
    params["created_begin"] ||
    params["created_end"],
);
const delAd = (val) => {
  const confirmDia = DialogPlugin.confirm({
    header:t('ad.scgg1'),
    theme: "info",
    body: t('ad.scztggm'),
    closeBtn: null,
    onConfirm: () => {
      adfrontaddel(
        {
          ad_id: val,
        },
        route.query.teamId || currentTeamId.value,
      )
        .then((res) => {
          if (res.data.code == 0) {
            getDataRun();
            MessagePlugin.success("删除成功");
          }
          confirmDia.hide();
        })
        .catch((err) => {
          MessagePlugin.error(err.message);
        });
    },
    onClose: () => {
      confirmDia.hide();
    },
  });
};
const activityItem = ref({});
const showAdvanceType = ref("Advance");
const shareVisible = ref(false);
const cancelVisible = ref(false);
const AdvanceVisible = ref(false);
const confirmShare = (val) => {
  console.log(val);
};
const confirmCancel = (val) => {
  extendsActivity(
    {
      ...val,
      id: activityItem.value.id,
    },
    activityItem.value.id,
  ).then((res) => {
    if (res?.status === 200) {
      MessagePlugin.success(t("activity.activity.success_tip"));
      getDataRun();
      cancelVisible.value = false;
    }
  });
};
const confirmAdvance = (val) => {
  extendsActivity(
    {
      ...val,
      id: activityItem.value.id,
    },
    activityItem.value.id,
  ).then((res) => {
    if (res?.status === 200) {
      MessagePlugin.success(t("activity.activity.success_tip"));
      getDataRun();
      AdvanceVisible.value = false;
    }
  });
};

const rowDelete = (row) => {
  const confirmDia = DialogPlugin.confirm({
    header: t("activity.activity.delete"),
    theme: "danger",
    body: t("activity.activity.delete_activity_tip"),
    closeBtn: null,
    confirmBtn: t("activity.activity.confirm"),
    onConfirm: async () => {
      confirmDia.hide();
      let res = await deleteActivity(row.id);
      if (res?.status === 200) {
        getData();
        MessagePlugin.success(t("activity.activity.success_tip"));
      }
    },
    onClose: () => {
      confirmDia.hide();
    },
  });
};

const rowspanAndColspan = () => {
  return {
    colspan: 4,
  };
};
const rowEdit = (row) => {
  console.log(row);
  router.push("/activity/activityEdit/" + row.id);
};
const rowShare = (row) => {
  console.log(row);
  if (!row.open) {
    return MessagePlugin.warning(t("activity.activity.shareTip"));
  }
  activityItem.value = row;
  shareVisible.value = true;
};
const rowCancel = (row) => {
  console.log(row);
  activityItem.value = row;
  cancelVisible.value = true;
};
const rowAdvance = (row) => {
  console.log(row);
  activityItem.value = row;
  showAdvanceType.value = "advance";
  AdvanceVisible.value = true;
};
const rowExtension = (row) => {
  console.log(row);
  activityItem.value = row;
  showAdvanceType.value = "extension";
  AdvanceVisible.value = true;
};
const formatStatusClickIcon = {
  rowEdit,
  rowShare,
  rowAdvance,
  rowExtension,
  rowCancel,
};
</script>

<style lang="less" scoped>
  .value-text{
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    word-break: break-all;
    flex: 1;
    max-width: 235px;
}
:deep(.activity_screen_drawer) {
  .t-drawer__header {
    padding: 0 24px;
    color: var(--text-kyy-color-text-1, #1a2139);
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px; /* 150% */
  }
  .t-drawer__close-btn {
    right: 24px;
  }
  .t-drawer__body {
    padding: 12px 24px;
  }
  input::-webkit-input-placeholder {
    color: var(--lingke-body-tips, #acb3c0);
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
  input::input-placeholder {
    color: var(--lingke-body-tips, #acb3c0);
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
}
:deep(.t-table th) {
  padding: 12px;
  border-bottom: none;
}
:deep(.t-table td) {
  padding: 12px;
}
.bus {
  height: 100%;
  display: flex;
}
.right-content {
  /* padding: 20px 16px !important; */
  position: relative;
  /* border-radius: 16px; */
  background: #fff;
  /* height: calc(100vh - 40px); */
  overflow-y: auto;

  .pagination {
    margin-top: 20px;
    // position: absolute;
    // bottom: 20px;
    // right: 10px;
  }
  .data-box-header {
    padding-bottom: 16px;position: sticky;
    top: 0px;
    z-index: 111;
    background: #fff;
    /* padding-top: 16px; */

    .approval-name {
      display: flex;
      align-items: center;
      .label {
        color: var(--text-kyy-color-text-1, #1a2139);
        display: flex;
        align-items: center;
      }
      .mock-in {
        width: 304px;
        min-height: 32px;
        background: #ffffff;
        border: 1px solid #e5e5e5;
        border-radius: 4px;
        display: flex;
        cursor: pointer;
        margin-left: 12px;
        .tag-con {
          width: 1050px;
          display: flex;
          flex-wrap: wrap;
          .tag {
            height: 24px;
            background: rgba(33, 118, 255, 0.1);
            border-radius: 15px;
            font-size: 14px;
            font-family: Undefined, Undefined-Regular;
            font-weight: 400;
            text-align: left;
            color: #2176ff;
            padding: 1px 8px;
            margin: 4px 0px 4px 8px;
            line-height: 24px;
          }
          .tag-text {
            font-size: 14px;

            font-weight: 400;
            text-align: left;
            color: #13161b;
            line-height: 28px;
            margin-left: 5px;
          }
        }
        .icon {
          padding-top: 5px;
        }
      }
      .borderColor {
        border: 1px solid red;
      }
    }
    .approval-time {
      display: flex;
      align-items: center;
      .label {
        width: 76px;
        font-size: 14px;

        font-weight: 400;
        color: var(--kyy_color_tag_text_black, #1a2139);
      }
      .f-icon {
        display: flex;
        width: 32px;
        height: 32px;
        cursor: pointer;
        min-height: 32px;
        max-height: 32px;
        padding: 6px;
        justify-content: center;
        align-items: center;
        gap: 10px;
        margin-left: 8px;
        border-radius: var(--radius-kyy-radius-button-s, 4px);
        border: 1px solid var(--color-button-border-kyy-color-button-border-border-dedault, #d5dbe4);
        background: var(--color-button-border-kyy-color-button-border-bg-default, #fff);
      }
    }
    .approval-status {
      display: flex;
      align-items: center;
      .label {
        width: 84px;
        font-size: 14px;

        font-weight: 400;
        text-align: right;
        color: #13161b;
        margin-right: 8px;
      }
    }
    .approval-key {
      display: flex;
      align-items: center;
      margin-left: 32px;
      .label {
        width: 42px;
        font-size: 14px;

        font-weight: 400;
        text-align: right;
        color: var(--kyy_color_tag_text_black, #1a2139);
        margin-right: 8px;
      }
    }
    .approval-end {
      margin-left: 50px;
      display: flex;
      align-items: center;
      .label {
        width: 56px;
        font-size: 14px;

        font-weight: 400;
        text-align: right;
        color: var(--kyy_color_tag_text_black, #1a2139);
        margin-right: 7px;
        margin-left: 3px;
      }
    }

    .tags {
      .t-radio-group {
        background-color: transparent;
        border: none;
        .t-radio-button:before {
          content: none;
        }
        .t-radio-button {
          margin-right: 8px;
          border-radius: var(--radius-kyy-radius-button-s, 4px);
          border: 1px solid var(--color-button-border-kyy-color-button-border-border-dedault, #d5dbe4);
          background: var(--color-button-border-kyy-color-button-border-bg-default, #fff);
        }
        .t-is-checked {
          border: 1px solid var(--color-button-primary-kyy-color-button-primary-bg-default, #4d5eff);
          background: var(--color-button-primary-kyy-color-button-primary-bg-default, #4d5eff);
        }
      }

      .radioButtonBox {
        display: flex;
        gap: 8px;
        .defaultRadioButton {
          display: flex;
          height: 32px;
          cursor: pointer;
          min-width: 80px;
          min-height: 32px;
          max-height: 32px;
          padding: 0px 16px;
          justify-content: center;
          align-items: center;
          gap: 4px;
          border-radius: var(--radius-kyy-radius-button-s, 4px);
          border: 1px solid var(--color-button-border-kyy-color-button-border-border-dedault, #d5dbe4);
          background: var(--color-button-border-kyy-color-button-border-bg-default, #fff);
          color: var(--color-button-border-kyy-color-button-border-text-default, #516082);
          text-align: center;

          /* kyy_fontSize_2/bold */
          font-family: PingFang SC;
          font-size: 14px;
          font-style: normal;
          font-weight: 600;
          line-height: 22px; /* 157.143% */
        }
        .activeRadioButton {
          display: flex;
          height: 32px;
          min-width: 80px;
          cursor: pointer;
          min-height: 32px;
          max-height: 32px;
          padding: 0px 16px;
          justify-content: center;
          align-items: center;
          gap: 4px;
          border-radius: var(--radius-kyy-radius-button-s, 4px);
          background: var(--color-button-primary-kyy-color-button-primary-bg-default, #4d5eff);
          color: var(--lingke-white-100, #fff);
          text-align: center;
          font-feature-settings: "clig" off, "liga" off;

          /* Body meduim */
          font-family: PingFang SC;
          font-size: 14px;
          font-style: normal;
          font-weight: 600;
          line-height: normal;
        }
      }
    }

    .tab-box {
      border-bottom: 1px solid var(--divider-kyy-color-divider-light, #eceff5);
      display: flex;
      height: 56px;
      align-items: center;
      justify-content: flex-start;
      .default-tab-item {
        margin-right: 44px;
        display: flex;
        flex-direction: column;
        align-items: center;
        .t-button {
          padding: 0;
          color: var(--kyy_color_tabbar_item_text, #1a2139);
          text-align: center;
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          line-height: 24px; /* 157.143% */
          border: none !important;
        }
        .tab-item-border {
          width: 16px;
          height: 3px;
          border-radius: 1.5px;
          background: transparent;
        }
      }
      .active-tab-item {
        .t-button {
          font-weight: 600;
          color: var(--brand-kyy-color-brand-default, #4d5eff) !important;
        }
        .tab-item-border {
          width: 16px;
          height: 3px;
          border-radius: 1.5px;
          background: var(--brand-kyy-color-brand-default, #4d5eff);
        }
      }
    }
  }

  .opblack {
    display: flex;
    justify-content: space-between;
    margin-top: 24px;
    width: 100%;
  }
}
.data-box-con {
  color: var(--kyy-color-table-text, #1a2139);
  .status-box1 {
    width: 58px;
    height: 24px;
    border-radius: var(--kyy-radius-tag-full, 999px);
    background: var(--kyy-color-tag-bg-warning, #ffe5d1);
    padding: 0 8px;
    font-size: 14px;

    font-weight: 400;
    text-align: center;
    color: var(--kyy-color-tag-text-warning, #fc7c14);
  }
  .approval-name-ov {
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .status-box2 {
    width: 58px;
    height: 24px;
    border-radius: var(--kyy-radius-tag-full, 999px);
    background: var(--kyy-color-tag-bg-success, #e0f2e5);
    padding: 0 8px;
    font-size: 14px;

    font-weight: 400;
    text-align: center;
    color: var(--kyy-color-tag-text-success, #499d60);
  }
  .status-box3 {
    width: 58px;
    height: 24px;
    border-radius: var(--kyy-radius-tag-full, 999px);
    background: #fbdde3;
    padding: 0 8px;
    font-size: 14px;

    font-weight: 400;
    text-align: center;
    color: var(--lingke-wrong, #d92f4d);
  }
  .status-box4 {
    width: 58px;
    height: 24px;
    background: #fff6e8;
    border-radius: 4px;
    padding: 0 8px;
    font-size: 14px;

    font-weight: 400;
    text-align: center;
    color: #e66800;
  }
}
.sw-box {
  padding: 12px;
  .item {
    display: flex;
  }
  .text {
    font-size: 14px;
    font-family: Undefined, Undefined-Regular;
    font-weight: 400;
    text-align: left;
    color: #333333;
  }
  .sw {
    margin-left: 18px;
  }
}
.pay-box {
  width: 200px;
  padding: 12px;
  color: #000;
  font-size: 14px;
  font-style: normal;
  text-align: right;
  font-weight: 400;
  line-height: 22px;
}
.record {
  color: var(--text-kyy-color-text-2, #516082);
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}
:deep(.data-box-con .t-table__empty) {
  display: none;
}
:deep(.data-box-con .t-table__body) {
  cursor: pointer;
}
.btn{
  font-weight: 600;

  padding: 0 4px;
}
.btn:hover {
  color: var(--color-button_text_brand-kyy_color_button_text_brand_font_default, #4d5eff);
  text-align: center;
  display: flex;
  gap: 16px;
  font-size: 14px;
  font-style: normal;
  padding: 0 4px;
  font-weight: 400;
  border-radius: 4px;
  background: var(--bg-kyy_color_bgBrand_hover, #eaecff);
  line-height: 22px;
  cursor: pointer;
}
.ex-btn {
  display: flex;
  height: 32px;
  min-width: 80px;
  min-height: 32px;
  max-height: 32px;
  padding: 0px 16px;
  justify-content: center;
  align-items: center;
  gap: 4px;
  border-radius: var(--radius-kyy-radius-button-s, 4px);
  border: 1px solid var(--color-button-border-kyy-color-button-border-border-dedault, #d5dbe4);
  background: var(--color-button-border-kyy-color-button-border-bg-default, #fff);
  color: var(--color-button-border-kyy-color-button-border-text-default, #516082);
  text-align: center;

  /* kyy_fontSize_2/bold */
  font-family: PingFang SC;
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 22px; /* 157.143% */
}

.filter-res {
  display: flex;
  margin-top: 16px;
  gap: 5px;
  flex-wrap: wrap;
  .tit {
    color: var(--text-kyy-color-text-2, #516082);

    /* kyy_fontSize_2/bold */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px; /* 157.143% */
  }
  .ov-time {
    display: flex;
    min-width: 290px;
    height: 24px;
    min-height: 24px;
    max-height: 24px;
    padding: 2px 8px;
    align-items: center;
    gap: 8px;
  }
  .close2 {
    margin-left: 8px;
    img {
      width: 10px;
      height: 10px;
    }
  }
  .clearFilters {
    color: var(--color-button-text-brand-kyy-color-button-text-brand-font-default, #4d5eff);
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
  .clearFiltersBox:hover {
    .clearFilters {
      color: var(--kyy_color_tag_text_black, #1a2139);
    }
  }
  .te {
    color: var(--kyy-color-tag-text-black, #1a2139);
    cursor: pointer;
    /* kyy_fontSize_2/regular */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
    border-radius: 4px;
    background: var(--kyy-color-tag-bg-gray, #eceff5);
    margin-right: 8px;
  }
  .stat {
    display: flex;
    height: 24px;
    min-height: 24px;
    max-height: 24px;
    padding: 2px 8px;
    align-items: center;
    gap: 8px;
  }
  .kword {
    display: flex;
    height: 24px;
    min-height: 24px;
    max-height: 24px;
    padding: 2px 8px;
    align-items: center;
    gap: 8px;
    max-width: calc(100% - 100px);
  }
  .icon {
    display: flex;
    margin-left: 4px;
    cursor: pointer;
    img {
      width: 14px;
      height: 14px;
      margin-top: 4px;
      margin-right: 4px;
    }
  }
}

.form-boxxx {
  .fitem {
    margin-bottom: 24px;
    .title {
      color: var(--text-kyy-color-text-3, #828da5);

      /* kyy_fontSize_2/regular */
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
    }
    .ctl {
      margin-top: 8px;
    }
  }
}
.foot {
  width: 100%;
  display: flex;
  justify-content: end;
  .btn1 {
    display: flex;
    cursor: pointer;
    height: 32px;
    min-width: 80px;
    min-height: 32px;
    max-height: 32px;
    padding: 0px 16px;
    justify-content: center;
    align-items: center;
    gap: 4px;
    color: var(--color-button-border-kyy-color-button-border-text-default, #516082);
    text-align: center;
    border-radius: var(--radius-kyy-radius-button-s, 4px);
    border: 1px solid var(--color-button-border-kyy-color-button-border-border-dedault, #d5dbe4);
    background: var(--color-button-border-kyy-color-button-border-bg-default, #fff);
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px; /* 157.143% */
    margin-right: 8px;
  }
  .btn2 {
    display: flex;
    cursor: pointer;
    width: 88px;
    height: 32px;
    min-height: 32px;
    max-height: 32px;
    padding: 6px 16px;
    justify-content: center;
    align-items: center;
    gap: 4px;
    border-radius: var(--radius-kyy-radius-button-s, 4px);
    background: var(--color-button-primary-kyy-color-button-primary-bg-disabled, #c9cfff);
    color: var(--color-button-primary-kyy-color-button-primary-text, #fff);
    text-align: center;

    /* kyy_fontSize_2/bold */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px; /* 157.143% */
  }
  .btn3 {
    display: flex;
    cursor: pointer;
    width: 88px;
    height: 32px;
    min-height: 32px;
    max-height: 32px;
    padding: 6px 16px;
    justify-content: center;
    align-items: center;
    gap: 4px;
    border-radius: var(--radius-kyy-radius-button-s, 4px);
    background: var(--color-button-primary-kyy-color-button-primary-bg-default, #4d5eff);

    color: var(--color-button-primary-kyy-color-button-primary-text, #fff);
    text-align: center;
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px;
  }
}
.tags-box {
  width: 128px;
  padding: 12px;
}
.option-box {
  width: 112px;
  padding: 12px;
}
.t-table {
  color: var(--kyy-color-table-text, #1a2139);
}
.af-icon {
  margin-left: 8px;
  height: 32px;
}

.main-img {
  width: 96px;
  height: 72px;
  border-radius: 4px;
  img {
    cursor: pointer;
    width: 96px;
    height: 72px;
    border-radius: 4px;
  }
  :deep(.t-image-overlay-content:before) {
    content: "";
    position: absolute;
    width: 32px;
    height: 32px;
    top: 0;
    left: 0;
    background: #4d5eff;
    clip-path: polygon(0 0, 100% 0, 0 100%);
    border-radius: 3.692px;
  }
  .t-image-overlay-title {
    transform: rotate(-45deg);
    display: inline-block;
    color: #ffffff;
    text-align: center;
    font-size: 10px;
    font-style: normal;
    font-weight: 400;
    line-height: 10px; /* 166.667% */
  }
}

:deep(tr:hover) {
  background: #fff !important;
  border-radius: 0px !important;
}
.end-content-box {
  margin-bottom: 16px;
  .lab {
    color: var(--text-kyy_color_text_3, #828da5);

    /* kyy_fontSize_2/regular */
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    margin-top: 24px;
    margin-bottom: 8px;
    margin-left: 12px;
    line-height: 22px; /* 157.143% */
    position: relative;
  }
  .lab::after {
    content: "*";
    color: var(--error-kyy_color_error_default, #d54941);
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    position: absolute;
    top: 0;
    left: -12px;
    line-height: 22px; /* 157.143% */
  }
}
.g-box {
  display: flex;
  flex-direction: column;
  .subject {
    color: var(--text-kyy-color-text-1, #1a2139);

    /* kyy_fontSize_2/bold */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px; /* 157.143% */
  }
  .duration {
    display: flex;
    align-items: center;
    color: var(--text-kyy-color-text-3, #828da5);
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
    margin: 4px 0;
    img {
      width: 20px;
      height: 20px;
      margin-right: 8px;
    }
  }
  .location {
    display: flex;
    color: var(--text-kyy-color-text-3, #828da5);
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
    img {
      width: 20px;
      height: 20px;
      margin-right: 8px;
    }
  }
  .chatId {
    display: flex;
    align-items: center;
    img {
      width: 20px;
      height: 20px;
      margin-right: 8px;
    }
    .line-1 {
      flex: 1;
      display: flex;
      align-items: center;
      color: var(--text-kyy-color-text-3, #828da5);
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
      .overHiddenName {
        max-width: calc(100% - 140px);
        padding-left: 8px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .clickViewButton {
        margin-left: 8px;
        height: 22px;
        padding: 0;
        color: var(--color-button-text-brand-kyy-color-button-text-brand-font-default, #4d5eff) !important;
        text-align: center;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
        :deep(.t-button__suffix) {
          margin-left: 0;
        }
      }
    }
  }
}

.status-box {
  .ongoing {
    display: flex;
    width: 58px;
    height: 24px;
    min-height: 24px;
    max-height: 24px;
    padding: 1px 8px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    border-radius: var(--kyy_radius_tag_full, 999px);
    background: var(--kyy_color_tag_bg_warning, #ffe5d1);
    color: var(--kyy_color_tag_text_warning, #fc7c14);
    text-align: right;

    /* kyy_fontSize_2/bold */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px; /* 157.143% */
  }
  .noStart {
    display: flex;
    width: 58px;
    height: 24px;
    min-height: 24px;
    max-height: 24px;
    padding: 1px 8px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    border-radius: var(--kyy_radius_tag_full, 999px);
    background: var(--kyy_color_tag_bg_kyyBlue, #e4f5fe);
    color: var(--kyy_color_tag_text_kyyBlue, #21acfa);
    text-align: right;

    /* kyy_fontSize_2/bold */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px; /* 157.143% */
  }
  .ended {
    display: flex;
    width: 58px;
    height: 24px;
    min-height: 24px;
    max-height: 24px;
    padding: 1px 8px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    border-radius: var(--kyy_radius_tag_full, 999px);
    background: var(--kyy_color_tag_bg_success, #e0f2e5);
    color: var(--kyy_color_tag_text_success, #499d60);
    text-align: right;

    /* kyy_fontSize_2/bold */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px; /* 157.143% */
  }
  .canceled {
    display: flex;
    width: 58px;
    height: 24px;
    min-height: 24px;
    max-height: 24px;
    padding: 1px 8px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    border-radius: var(--kyy_radius_tag_full, 999px);
    background: var(--kyy_color_tag_bg_gray, #eceff5);
    color: var(--kyy_color_tag_text_gray, #516082);
    text-align: right;

    /* kyy_fontSize_2/bold */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px; /* 157.143% */
  }
}
.addbtn {
  position: absolute;
    top: 50%;
    right: 0;
    transform: translateY(-50%);
}
.activityButton {
  padding: 0 8px;
}
// .activityButton:last-child{
//   padding: 0;
// }

.mbtn {
  width: 28px;
  height: 28px;
  padding: 4px;
  text-align: center;
  font-size: 24px;
  line-height: 4px;
  cursor: pointer;
}
.mbtn:hover {
  border-radius: 4px;
  background: var(--bg-kyy-color-bg-brand-foucs, #dbdfff);
  color: #3e4cd1;
}
.fist-box {
  flex: 1;
}
:deep(.t-table td) {
  padding: 0 !important;
}
.actions-boxs {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  width: 152px;
  min-height: 100px;
  .item {
    display: flex;
    height: 32px;
    min-width: 136px;
    min-height: 32px;
    max-height: 32px;
    padding: 0px 16px;
    align-items: center;
    gap: 12px;
    align-self: stretch;
    cursor: pointer;
    .icon {
      width: 20px;
      height: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      img {
        width: 16px;
        height: 16px;
      }
    }
    .text {
      color: var(--kyy_color_dropdown_text_default, #1a2139);

      /* kyy_fontSize_2/regular */
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
      // margin-left: 4px;
    }
  }
  .item:hover {
    border-radius: var(--kyy_radius_dropdown_s, 4px);
    background: var(--kyy_color_dropdown_bg_hover, #f3f6fa);
  }
  .item-view {
    border-top: 1px solid var(--divider-kyy-color-divider-light, #eceff5);
  }
}

.channel_type {
  display: flex;
  align-items: center;
  .tagr {
    width: 8px;
    height: 8px;
    border-radius: 8px;
    margin-right: 8px;
  }
  .type-tag {
    background-color: #4d5eff;
  }
  .type-tag1 {
    background-color: #d5dbe4;
  }
}

:deep(.data-box-con .t-table__content) {
  overflow-x: hidden;
}
:deep(input::-webkit-input-placeholder) {
  color: var(--text-kyy_color_text_5, #acb3c0) !important;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 150% */
}
:deep(input::input-placeholder) {
  color: var(--text-kyy_color_text_5, #acb3c0) !important;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 150% */
}
:deep(.t-table tr:hover) {
  background: var(--bg-kyy_color_bg_deep, #f5f8fe);
  border-radius: 4px;
  .activityButton,
  .moreBtn {
    background: var(--bg-kyy_color_bg_deep, #f5f8fe) !important;
  }
}
.list-item:hover {
  border-radius: var(--kyy_radius_dropdown_s, 4px);
  background: var(--kyy_color_dropdown_bg_hover, #f3f6fa);
}
.list-item {
  display: list-item;
  padding: 0 8px;
  height: 32px;
  line-height: 32px;
  cursor: pointer;
}
.tab-header {
  display: flex;
  align-items: center;
  padding: 8px 12px;border-radius: 8px 8px 0 0;
  width: 100%;border-radius: 8px 8px 0 0;
  gap: 16px;
  border-bottom: 1px solid var(--divider-kyy_color_divider_light, #eceff5);
  background: var(--bg-kyy_color_bg_deep, #f5f8fe);
  overflow: hidden;
  color: var(--text-kyy_color_text_1, #1a2139);
  text-overflow: ellipsis;
  font-size: 14px;
  font-style: normal;
  margin-bottom: 8px;
  font-weight: 600;
  line-height: 22px; /* 157.143% */
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  word-break: break-all;
}
:deep(.t-table__td-first-col) {
  border: none;
}
:deep(.table-item):hover {
  background: #f3f6fa;
}
.table-item {
  border-radius: 8px;
  margin-top: 12px;
  border: 1px solid var(--divider-kyy_color_divider_light, #eceff5);
}
.pt {
  color: #21acfa !important;
  background: #e4f5fe !important;
}
.ptsf {
  color: #499d60 !important;
  background: var(--kyy_color_tag_bg_success, #e0f2e5) !important;
}

.row-box {
  display: flex;
  align-items: center;
  gap: 1px;
  align-self: stretch;

  .option-box {
    color: var(--color-button_text_brand-kyy_color_button_text_brand_font_default, #4d5eff);
    text-align: center;
    display: flex;
    gap: 16px;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
  .tags {
    display: flex;
    height: 20px;
    min-height: 20px;
    max-height: 20px;
    width: fit-content;font-size: 12px;
    padding: 0px 4px;
    justify-content: center;
    align-items: center;
    gap: 4px;
    border-radius: var(--kyy_radius_tag_s, 4px);
    background: var(--kyy_color_tag_bg_blue, #e8f0fb);
  }
  .info-box {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    .info-item {
      width: 100%;
      display: flex;
      align-items: center;
      .lables {
        color: var(--text-kyy_color_text_1, #1a2139);
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        width: 70px;
        line-height: 22px; /* 157.143% */
      }
      .values {
        display: flex;
        align-items: center;
      }
    }
  }
  .body-box-img {
    width: 128px;object-fit: cover;
    height: 72px;
    border-radius: 4px;
  }
  .body-box {
    display: flex;
    align-items: center;
    padding: 0 12px 8px;
    gap: 12px;
  }

}
.tab-dis {
  position: relative;
  width: 319px;
    white-space: inherit;
    text-overflow: ellipsis;
    overflow: hidden;
    word-break: break-all;
}
.tab-btn{
  margin-bottom: 13px;
  height: 24px;
  :deep(.t-button__text){
    font-size: 16px;
  }
}
.tab-id{
  position: relative;
}
.top{
  margin-top: 16px;
}
.tab-id::after {
  content: "";
    height: 16px;
    width: 1px;
    background: #d5dbe4;
    position: absolute;
    top: 4px;
    left: -13px;
}
/* ui规定滚动条样式 */
::-webkit-scrollbar {
 width: 6px !important;
 background-color: transparent !important;

}
::-webkit-scrollbar-track {
  background-color: transparent !important;
}
::-webkit-scrollbar-thumb {
  background: var(--icon-kyy_color_icon_transparent, rgba(26, 33, 57, 0.36))!important;
  background-color: var(--icon-kyy_color_icon_transparent, rgba(26, 33, 57, 0.36))!important;
  border-radius: 4px!important;
}
</style>
