import { i18nt } from '@renderer/i18n';

/**
 * 场景类型枚举
 */
export enum SceneType {
  /**
   * 加入组织
   */
  STAFF_PLATFORM_CONTACT_MEMBER = 5065,
  /**
   * 加入组织
   */
  STAFF_PLATFORM_CONTACT_POLITICS = 14035,
  /**
   * 加入组织
   */
  STAFF_PLATFORM_CONTACT_CBD = 16035,
  /**
   * 加入组织
   */
  STAFF_PLATFORM_CONTACT_ASSOCIATION = 19035,
  /**
   * 加入组织
   */
  STAFF_PLATFORM_CONTACT = 51035
}

export const getTitle = (type) => {
  const titleMap = {
    cbd: i18nt('application.digital_cbd'),
    government: i18nt('im.public.government'),
    member: i18nt('im.public.biz'),
    association: i18nt('niche.szsq'),
    uni: i18nt('niche.szgx'),
  };
  return titleMap[type] || '';
};
export const getBtnTxt = (status) => {
  const titleMap = {
    0: {
      txt: i18nt('im.msg.agree'),
      theme: 'primary',
    },
    1: {
      txt: i18nt('im.msg.agreed'),
      theme: 'primary',
    },
    2: {
      txt: i18nt('ebook.vrefuse'),
      theme: 'danger',
    },
    4: {
      txt: i18nt('ebook.vtype6'),
      theme: 'default',
    },
  };
  return titleMap[status] || null;
};
export const SceneTypeArr = Object.values(SceneType).filter((value) => typeof value === 'number');
