<template>
  <t-drawer
    v-model:visible="visible"
    class-name="drawerSet"
    :header="$t('member.active.approved')"
    :close-btn="true"
    :z-index="2500"
    :size="'472px'"
  >
    <div v-if="settingValue" class="toBody">
      <t-alert theme="info" style="margin-bottom: 24px">
        <template #message>
          <span class="info-text">{{ $t("member.active.apply_fee_setting") }}：{{
            settingValue.pay_setting === 2
              ? t("member.active.no_fee_membership")
              : t("member.active.fee_after_membership")
          }}
          </span>
          <t-link theme="primary" hover="color" @click="goApplySettingPage">{{
            $t("member.menu.applict_setting")
          }}</t-link>
        </template>
      </t-alert>
      <t-form
        ref="formRef"
        label-align="top"
        :rules="FORM_RULES"
        :data="formData"
        :colon="true"
        class="form"
      >
        <t-form-item label="会费状态" name="name">
          <t-select v-replace-svg
            v-model="formData.pay_status"
            :options="optionsFeeStatus"
            :disabled="
              (settingValue.pay_setting === 2 &&
                settingValue.level_money === 0) ||
                settingValue.pay_setting === 2 ||
                !originData.level_money
            "
          />
        </t-form-item>
        <t-form-item
          v-if="formData.pay_status === 2"
          label="缴费时间"
          name="pay_time"
        >
          <t-date-picker
            v-model="formData.pay_time"
            :placeholder="'请选择'"
            :maxlength="500"
            :format="'YYYY-MM-DD HH:mm'"
            enable-time-picker
            style="width: 100%"
            clearable
          />
        </t-form-item>
        <t-form-item
          v-if="formData.pay_status === 2"
          :label="$t('member.active.fee_money')"
          name="money"
        >
          <t-input-number
            v-model="formData.money"
            theme="normal"
            :decimal-places="2"
            :min="0"
            style="width: 100%"
            :placeholder="$t('member.active.please_input_fee')"
          />
        </t-form-item>
        <!-- <t-form-item
          v-if="formData.pay_status === 2"
          label="币种"
          name="currency"
        >
          <t-select v-replace-svg  v-model="formData.currency" :options="optionsCurrency" />
        </t-form-item> -->
        <t-form-item :label="$t('member.active.apply_time')" name="join_time">
          <t-date-picker
            v-model="formData.join_time"
            :placeholder="'请选择'"
            :maxlength="500"
            :format="'YYYY-MM-DD'"
            style="width: 100%"
            :disable-date="onDisableJoinDate()"
            clearable
            @change="changeDate"
            @blur="changeDate"
          />
        </t-form-item>
        <t-form-item
          :label="$t('member.active.member_valid_period')"
          name="type"
          class="form-row"
        >
          <div class="setRow">
            <div class="setRow-item">
              <t-radio-group
                v-model="formData.type"
                style="margin-bottom: 16px"
                :default-value="1"
                @change="changeRadio"
              >
                <t-radio :value="1">
                  <!-- 固定期限  -->
                  {{ $t('member.active.fixed_term') }}
                </t-radio>
                <t-radio :value="2">
                  {{ $t("member.active.owner_me") }}
                </t-radio>
                <t-radio :value="3">
                   <!-- 长期有效  -->
                   {{ $t("zx.contacts.noExpired") }}
                  </t-radio>
              </t-radio-group>
            </div>
            <div class="setRow-item">
              <template v-if="formData.type === 1">
                <t-input-number

                  v-model="formData.value"
                  :min="1"
                  :decimalPlaces="0"
                  theme="column"
                  @keyup="onListenNumberChange"
                  @validate="(context)=> validateNumber(context, formData, 'value')"
                />
                <span class="pl-6px">年</span>
              </template>
              <t-date-picker
                v-else-if="formData.type === 2"
                v-model="formData.owner_value"
                :placeholder="'请选择'"
                :format="'YYYY-MM-DD'"
                :disableDate="disableDateOwner"
                @change="changeOwnDate"
                @blur="changeOwnDate"
              />
              <span
                v-show="[1, 2].includes(formData.type) && formData.join_time"
                class="setRow-item-tips ml-16px"
              >
                下次到期时间为：{{ formData.show_tips }}
              </span>
            </div>
          </div>
        </t-form-item>
      </t-form>
    </div>
    <template #footer>
      <div class="footer">
        <t-button
          theme="default"
          variant="outline"
          @click="onClose"
        >取消</t-button>
        <t-button theme="primary" class="ml-8" @click="onSave">确定</t-button>
      </div>
    </template>
  </t-drawer>

  <t-dialog
    :visible="false"
    :z-index="2500"
    attach="body"
    width="728px"
  >
    <template #header>
      <div class="header">
        <!-- <svg class="iconpark-icon header-svg">
          <use href="#attention-6ebn71gl" />
        </svg> -->
        <div class="header-title">{{ $t("member.active.approved") }}</div>
      </div>
    </template>
    <template #body>
      <div v-if="settingValue" class="toBody">
        <t-alert theme="info">
          <template #message>
            <span class="info-text">{{ $t("member.active.apply_fee_setting") }}：{{
              settingValue.pay_setting === 2
                ? t("member.active.no_fee_membership")
                : t("member.active.fee_after_membership")
            }}
            </span>
            <t-link theme="primary" hover="color" @click="goApplySettingPage">{{
              $t("member.menu.applict_setting")
            }}</t-link>
          </template>
        </t-alert>
        <t-form
          ref="formRef"
          label-align="top"
          layout="inline"
          :rules="FORM_RULES"
          :data="formData"
          :colon="true"
          class="form"
        >
          <t-form-item label="会费状态" name="name" class="form-item">
            <t-select v-replace-svg
              v-model="formData.pay_status"
              :options="optionsFeeStatus"
              :disabled="
                (settingValue.pay_setting === 2 &&
                  settingValue.level_money === 0) ||
                  settingValue.pay_setting === 2 ||
                  !originData.level_money
              "
            />
          </t-form-item>
          <t-form-item
            v-if="formData.pay_status === 2"
            label="缴费时间"
            name="pay_time"
            class="form-item"
          >
            <t-date-picker
              v-model="formData.pay_time"
              :placeholder="'请选择'"
              :maxlength="500"
              :format="'YYYY-MM-DD HH:mm'"
              enable-time-picker
              style="width: 100%"
              clearable
            />
          </t-form-item>
          <t-form-item
            v-if="formData.pay_status === 2"
            :label="$t('member.active.fee_money')"
            name="money"
            class="form-item"
          >
            <t-input-number
              v-model="formData.money"
              theme="normal"
              :decimal-places="2"
              :min="0"
              :placeholder="$t('member.active.please_input_fee')"
            />
          </t-form-item>
          <t-form-item
            v-if="formData.pay_status === 2"
            label="币种"
            name="currency"
            class="form-item"
          >
            <t-select v-replace-svg  v-model="formData.currency" :options="optionsCurrency" />
          </t-form-item>
          <t-form-item
            :label="$t('member.active.apply_time')"
            name="join_time"
            class="form-item"
          >
            <t-date-picker
              v-model="formData.join_time"
              :placeholder="'请选择'"
              :maxlength="500"
              :format="'YYYY-MM-DD'"
              style="width: 100%"
              :disable-date="onDisableJoinDate()"
              clearable
              @change="changeDate"
              @blur="changeDate"
            />
          </t-form-item>
          <t-form-item
            :label="$t('member.active.member_valid_period')"
            name="type"
            class="form-row"
          >
            <div class="setRow">
              <div class="setRow-item">
                <t-radio-group
                  v-model="formData.type"
                  style="margin-bottom: 16px"
                  :default-value="1"
                  @change="changeRadio"
                >
                  <t-radio :value="1"> 固定期限 </t-radio>
                  <t-radio :value="2">
                    {{ $t("member.active.owner_me") }}
                  </t-radio>
                  <t-radio :value="3"> 长期有效 </t-radio>
                </t-radio-group>
              </div>
              <div class="setRow-item">
                <t-input-number
                  v-if="formData.type === 1"
                  v-model="formData.value"
                  :min="0"
                  theme="column"
                  @validate="(context)=> validateNumber(context, formData, 'value')"
                />
                <t-date-picker
                  v-else-if="formData.type === 2"
                  v-model="formData.owner_value"
                  :placeholder="'请选择'"
                  :disableDate="disableDateOwner"
                  :format="'YYYY-MM-DD'"
                  @change="changeOwnDate"
                  @blur="changeOwnDate"
                />
                <span
                  v-show="[1, 2].includes(formData.type) && formData.join_time"
                  class="setRow-item-tips ml-16px"
                >
                  下次到期时间为：{{ formData.show_tips }}
                </span>
              </div>
            </div>
          </t-form-item>
        </t-form>
      </div>
    </template>
    <template #closeBtn>
      <!-- <iconpark-icon name="close" size="16"></iconpark-icon> -->
      <!-- <svg class="iconpark-icon" style="width: 16px; height: 16px">
        <use href="#close" />
      </svg> -->
      <iconpark-icon name="iconerror" style="font-size: 24px"></iconpark-icon>
    </template>
    <template #footer>
      <div class="footer">
        <t-button
          theme="default"
          variant="outline"
          @click="onClose"
        >取消</t-button>
        <t-button theme="primary" class="ml-8" @click="onSave">确定</t-button>
      </div>
    </template>
  </t-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, Ref, watch, toRaw } from "vue";
import dayjs from "dayjs";
import { useRouter } from "vue-router";
import { useUniStore } from "@renderer/views/uni/store/uni";
import { getMemberJobsDetailAxios } from "@renderer/api/uni/api/businessApi";
import { getResponseResult, priceRecovery } from "@renderer/utils/myUtils";
import { MessagePlugin } from "tdesign-vue-next";
import lodash from "lodash";
import { useI18n } from "vue-i18n";
const store = useUniStore();
const { t } = useI18n();
const router = useRouter();
const FORM_RULES = {
  // name: [{ required: true, message: "请输入角色名称" }]
  join_time: [{ required: true, message: "请输入入会时间" }],
  money: [{ min: 0, message: "金额不能小于0" }],
};
const optionsCurrency = [
  { label: "CNY", value: "CNY" },
  { label: "MOP", value: "MOP" },
  { label: "HKD", value: "HKD" },
];
const optionsFeeStatus = [
  // { label: "未缴费", value: 1 },
  { label: "已缴费", value: 2 },
  { label: "无需缴费", value: 3 },
];

interface ApplyRequest {
  /**
   * 币种
   */
  currency: string;
  /**
   * 申请ID
   */
  id: number;
  /**
   * 入会时间
   */
  join_time: string;
  /**
   * 缴费金额
   */
  money: number;
  /**
   * 会费状态，1：未缴费，2：已缴费，3：无需缴费
   */
  pay_status: number;
  /**
   * 缴费时间
   */
  pay_time: string;
  /**
   * 有效期类型，1：固定年限，2：自定义，3：长期有效
   */
  type: number;
  /**
   * 有效期填写的值
   */
  value: string;
  owner_value: string; // 自定义时间
  show_tips: string; // 显示到期时间
}

const emits = defineEmits(["onSend"]);

// 从公司组织架构导入
// const onImportFromCompany = () => {
// 	emits('onImportFromCompany');
// };
// // 手工建立组织架构
// const onHandCreate = () => {
// 	emits('onHandCreate');
// };
const formObj: ApplyRequest = {
  currency: "",
  id: 0,
  join_time: "",
  money: 0,
  pay_status: undefined,
  pay_time: dayjs(),
  type: 1,
  value: "",
  owner_value: "", // 自定义时间
  show_tips: "",
};
const formData = reactive(formObj);

watch(
  () => formData.value,
  (val) => {
    formData.show_tips = dayjs(formData.join_time)
      .add(Number(val), "year")
      .format("YYYY-MM-DD");
  }
);

const disableDateOwner = {
  before: dayjs().format('YYYY-MM-DD')
}

const visible = ref(false);
const onDisableJoinDate = () => ({
  // before: "1990-01-01",
  after: dayjs().format("YYYY-MM-DD"),
});
const changeDate = (value, context) => {
  if (context.trigger === "clear") {
  }
  console.log(value);
  if (formData.type === 1) {
    formData.show_tips = dayjs(value)
      .add(Number(formData.value), "year")
      .format("YYYY-MM-DD");
  }
};
const changeOwnDate = (value, context) => {
  if (context.trigger === "clear") {
  }
  console.log(value);
  formData.show_tips = value;
};
// 有效期选择
const changeRadio = (value) => {
  if (value === 2) {
    formData.show_tips = formData.owner_value;
  } else if (value === 1) {
    formData.show_tips = dayjs(formData.join_time)
      .add(Number(formData.value), "year")
      .format("YYYY-MM-DD");
  }
};


const validateNumber = ({ error }, origin, key) => {
  console.log(error);
  if (error) {
    origin[key] = 1;
  }

  // if(error === 'below-minimum') {
  //     MessagePlugin.error('最小值为1');
  //     formData.value = '1';

  // }
};

const onListenNumberChange = () => {
  console.log(formData.value)
  if(!formData.value) {
    formData.value = 1;
  }
}

const goApplySettingPage = () => {
  onClose();
  // router.push({
  //   path: "/memberIndex/member_home",
  //   query: {
  //     panel: "PMembershipSettingPanel"
  //   }
  // });
  console.log(store);
  store.setCurrentPanel("PMembershipSettingPanel");
};
const onGetMemberJobsDetail = async (level) => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await getMemberJobsDetailAxios(level);
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      formData.currency = result.data.currency;
      resolve(result.data);
    } catch (error) {
      const errMsg = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
      reject();
    }
  });
};
const settingValue = ref(null);
const originData = ref(null);
const onOpen = (data: any, result: any) => {
  settingValue.value = null;
  const setting = result;
  // const levelSetting = result[1];
  if (data && setting) {
    originData.value = lodash.cloneDeep(data);
    settingValue.value = setting;
    settingValue.value.level_money = data.level_money;
    // formData.pay_status = data.pay_status;
    /**
     * 表示当前会员是否缴费
     *   默认值：
     *   1、当入会会费设置为“无需缴费入会”时，默认无需缴费，且无法修改
     *   2、当入会会费设置为“缴费后入会”时
     *  （1）会员级别对应会费金额为0时，默认无需缴费，且无法修改
     *   3、若金额不为0，默认选择已缴费
     *   可选项：已缴费，无需缴费
     *  选择无需缴费时，隐藏缴费时间，会费金额和币种字段
     */
    if (setting.pay_setting === 2) {
      // 1：缴费后入会，2：无需缴费入会
      formData.pay_status = 3;
    } else if (setting.pay_setting === 1) {
      if (data.level_money) {
        formData.pay_status = 2;
      } else {
        formData.pay_status = 3;
      }
    }
    formData.id = data.id;
    formData.money = data.level_money / 100; // 会费金额
    formData.currency = undefined;
    if (data.level) {
      onGetMemberJobsDetail(data.level);
    }
  } else {
    return;
  }
  formData.join_time = dayjs().format("YYYY-MM-DD HH:mm");
  formData.type = 1;
  formData.value = "1";
  formData.owner_value = dayjs(formData.join_time)
    .add(1, "year")
    .format("YYYY-MM-DD");
  formData.show_tips = dayjs(formData.join_time)
    .add(1, "year")
    .format("YYYY-MM-DD");

  visible.value = true;
};

const onClose = () => {
  visible.value = false;
};
const formRef = ref(null);
const onSave = () => {
  formRef.value.validate().then((validateResult) => {
    if (validateResult && Object.keys(validateResult).length) {
      // const firstError = Object.values(validateResult)[0]?.[0]?.message;
      // MessagePlugin.warning(firstError);
    } else {

      if (formData.type === 2) {
        // 自定义时
        if (dayjs(formData.owner_value).diff(dayjs(formData.join_time)) < 0) {
          return MessagePlugin.error('自定义会员有效期的日期不能小于入会时间');
        }
      }


      const params: any = {
        ...toRaw(formData),
      };
      params.money = priceRecovery(params.money);
      if (params.type === 2) {
        params.value = params.owner_value;
      }
      console.log(params);

      emits("onSend", params);
    }
  });
};
defineExpose({
  onOpen,
  onClose,
});
</script>

<style lang="less" scoped>
.t-form-inline .t-form__item {
  margin-right: 24px;
  &:nth-child(2n) {
    margin-right: 0;
  }
}
.t-radio-group.t-radio-group__outline {
  column-gap: 24px;
}
.setRow {
  display: flex;
  flex-direction: column;
  &-item {
    display: flex;
    align-items: center;
    &-tips {
      font-size: 14px;

      font-weight: 400;
      color: #e66800;
    }
  }
}
.form {
  &-item {
    width: 320px;
  }
  &-row {
    width: 100%;
  }
}
.info {
  &-text {
    font-size: 14px;

    font-weight: 400;
    color: #13161b;
  }
}
.t-button + .t-button {
  margin-left: 0;
}
.header {
  display: flex;
  &-svg {
    width: 22px;
    height: 22px;
    color: #2069e3;
    margin-right: 8px;
  }
  &-title {
    font-size: 16px;

    font-weight: 700;
    color: #13161b;
  }
}
.toBody {
}
// :deep(.t-dialog--default) {
// 	padding-bottom: 0;
// }
.footer {
  display: flex;
  justify-content: flex-end;
  gap: 5px;
}
.t-dialog__body {
}
</style>
