<template>
	<t-dialog v-model:visible="visible" placement="center" :close-btn="false" :header="true" :footer="true" width="540">
		<template #header>
			<div style="display: flex; align-items: center; width: 100%; justify-content: space-between">
				<div>退款申请</div>
				<img style="width: 16px; cursor: pointer; height: 16px" src="@/assets/<EMAIL>" @click="handleClose" />
			</div>
		</template>
		<div class="refund-form-content">
			<t-form
				requiredMark
				ref="formRef"
				:data="formData"
				:rules="rules"
				label-width="100px"
				layout="vertical"
				@submit="handleSubmit"
			>
				<t-form-item label="退款方式" name="refund_method">
					<t-select v-model="formData.refund_method" disabled>
						<t-option label="线下退款" value="线下退款" />
					</t-select>
				</t-form-item>
				<t-form-item label="开户行" name="opening_bank">
					<t-input v-model="formData.opening_bank" placeholder="请输入开户行" />
				</t-form-item>
				<t-form-item label="银行账户" name="bank_account">
					<t-input v-model="formData.bank_account" placeholder="请输入银行账户" />
				</t-form-item>
				<t-form-item label="退款时间" name="refund_time">
					<t-date-picker
						v-model="formData.refund_time"
						enable-time-picker
						format="YYYY-MM-DD HH:mm"
						placeholder="请选择退款时间"
						style="width: 100%"
					/>
				</t-form-item>
				<t-form-item label="退款金额" name="refund_amount">
					<t-input-number :min="0.01" v-model="formData.refund_amount" placeholder="请输入退款金额" theme="normal" />
				</t-form-item>
				<t-form-item name="refund_voucher">
					<template #label>
						<div class="flex-inline items-center space-x-4">
							<span>退款凭证</span>
							<t-tooltip>
								<template #content>
									<div>
										<p>图片大小不超10M</p>
										<p>支持格式： jpg/jpeg/png</p>
										<p>最多上传5张图片</p>
									</div>
								</template>
								<iconpark-icon name="iconhelp" class="text-20"></iconpark-icon>
							</t-tooltip>
						</div>
					</template>
					<r-upload-image
						ref="uploadImageRef"
						v-model="formData.refund_voucher"
						accept="image/jpg,image/jpeg,image/png"
						:max-count="5"
						:auto-upload="true"
						:show-file-list="true"
						auto-sort
						sortable
						:size-limit="{
							size: 10,
							unit: 'MB',
							message: '图片大小不超过{sizeLimit}MB',
						}"
						:get-custom-oss-token="getStsToken"
						@preview="previewImage"
					/>
				</t-form-item>
			</t-form>
		</div>
		<template #footer>
			<div style="display: flex; align-items: center; width: 100%; justify-content: flex-end">
				<t-button theme="default" variant="outline" @click="handleClose">取消</t-button>
				<t-button @click="handleConfirm" :loading="isLoading">确定</t-button>
			</div>
		</template>
	</t-dialog>
</template>

<script setup lang="ts">
import { ref, watch, reactive } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import type { FormInstanceFunctions, FormRule } from 'tdesign-vue-next';
import { RUploadImage } from '@rk/unitPark';
import { getStsToken } from '@/api/clouddisk';
import to from 'await-to-js';
import { bondRefund } from '@/api/advertisement';

interface RefundFormData {
	refund_method: string;
	bank_account: string;
	opening_bank: string;
	refund_time: string;
	refund_amount: number | null;
	refund_voucher: string[];
}

interface Props {
	modelValue: boolean;
	refundId?: number | null;
}

interface Emits {
	(e: 'update:modelValue', value: boolean): void;
	(e: 'success'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const visible = ref(false);
const formRef = ref<FormInstanceFunctions>();
const isLoading = ref(false);

// 表单数据
const formData = reactive<RefundFormData>({
	refund_method: '线下退款',
	bank_account: '',
	refund_time: '',
	refund_amount: null,
	refund_voucher: [],
	opening_bank: '',
});

// 表单验证规则
const rules: Record<string, FormRule[]> = {
	opening_bank: [{ required: true, message: '请输入开户行', trigger: 'blur' }],
	bank_account: [{ required: true, message: '请输入银行账户', trigger: 'blur' }],
	refund_time: [{ required: true, message: '请选择退款时间', trigger: 'change' }],
	refund_amount: [
		{ required: true, message: '请输入退款金额', trigger: 'blur' },
		{ validator: (val) => val > 0, message: '退款金额必须大于0', trigger: 'blur' },
	],
	refund_voucher: [{ required: true, message: '请上传退款凭证', trigger: 'change' }],
};

// 重置表单
const resetForm = () => {
	formData.refund_method = '线下退款';
	formData.bank_account = '';
	formData.refund_time = '';
	formData.refund_amount = null;
	formData.refund_voucher = undefined;
	formData.opening_bank = '';
	formRef.value?.clearValidate();
};

// 监听弹窗显示状态
watch(
	() => props.modelValue,
	(newVal) => {
		visible.value = newVal;
		if (newVal) {
			// 打开弹窗时重置表单
			resetForm();
		}
	},
	{ immediate: true },
);

// 监听内部visible变化
watch(visible, (newVal) => {
	emit('update:modelValue', newVal);
});

// 关闭弹窗
const handleClose = () => {
	visible.value = false;
};

// 预览图片
const previewImage = () => {
	// window.open(file.url);
};

// 提交申请
const handleConfirm = async () => {
	if (!props.refundId) {
		MessagePlugin.error('退款数据异常');
		return;
	}

	formRef.value?.submit();
};

// 提交表单
const handleSubmit = async () => {
	isLoading.value = true;
	try {
		const result = await formRef.value?.validate();

		console.log('result', result);

		if (result === true) {
			const [err] = await to(
				bondRefund({
					app_id: props.refundId,
					bank_account: formData.bank_account,
					opening_bank: formData.opening_bank,
					refund_amount: formData.refund_amount,
					refund_method: formData.refund_method,
					refund_time: formData.refund_time,
					refund_voucher: formData.refund_voucher,
				}),
			);
			if (err) {
				MessagePlugin.error(err.message || '退款失败');
				return;
			}
			visible.value = false;

			MessagePlugin.success('退款成功');
			emit('success');
		}
	} catch (error) {
		console.log('表单验证失败:', error);
	} finally {
		isLoading.value = false;
	}
};
</script>

<style lang="less" scoped>
.refund-form-content {
	padding: 16px;
	padding-left: 0;
	padding-bottom: 0;
}
</style>
