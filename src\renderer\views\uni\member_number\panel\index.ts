import { defineAsyncComponent } from "vue";

const PHome = defineAsyncComponent(() => import("./home-panel.vue"));
const PMember = defineAsyncComponent(() => import("./member-panel.vue"));
const PLeaflets = defineAsyncComponent(() => import("./leaflets-panel.vue"));
const PPlatform = defineAsyncComponent(() => import("./platform-panel.vue"));

const PManage = defineAsyncComponent(() => import("@renderer/views/uni/member_home/index.vue"));

export const panels = {
  PHome, // 首页
  PMember, // 会员中心
  PLeaflets, // 宣传页
  PPlatform, // 选择端页面
  PManage, // 管理端
};
