<template>
  <t-dialog
    v-model:visible="visible"
    :footer="null"
    :z-index="2500"
    attach="body"
    :close-on-overlay-click="false"
    class="dialogSet"
    width="560px"
  >
    <template #header>
      <div class="header">
        <span class="title">导入成员 </span>
        <!-- <span class="tip cursor" @click="onShowMemberFlow"><iconpark-icon name="iconhelp" class="iconhelp"></iconpark-icon>{{ $t('member.winter_column.know_add_flow_1') }} </span> -->

      </div>
    </template>
    <template #body>
      <div class="toBody mt-24px">
        <!-- <t-alert theme="info" class="info" :message="$t('member.apply_flow.apply_way_30')" /> -->
        <div class="fox mt-24px">
          <div class="item">
            <div class="top">
              <h3>下载导入模板，填写成员资料</h3>
              <div class="ecex mt-28px">
                <img src="@renderer/assets/member/icon/type_icon.png">
                <span class="text mt-12px">成员导入模板.xlsx</span>
              </div>
            </div>
            <div class="bottom">
              <t-button
                theme="default"
                variant="outline"
                class="mo"
                @click="emits('onDownloadModel')"
              >{{ $t('member.apply_flow.apply_way_26') }}</t-button>
            </div>
          </div>
          <div class="item">
            <div class="top">
              <h3>{{ $t('member.apply_flow.apply_way_27') }}</h3>
              <div
                class="up mt-16px"
                :class="{dash: isIn}"
                @dragleave="dragleave"
                @dragenter="dragenter"
                @drop="dropUpload"
                @dragover="stoppre"
              >
                <img src="@renderer/assets/member/icon/excel_f.png">
                <span v-show="fileDowArray.length < 1" class="text">{{ $t('member.apply_flow.apply_way_28') }}</span>
                <span v-show="fileDowArray.length > 0" class="error">
                  {{ fileDowArray[0]?.tip }}
                </span>
              </div>
            </div>
            <div class="bottom">
              <t-button
                theme="default"
                variant="outline"
                class="mo"
                @click="onUploadFileEmit"
              >{{ $t('member.apply_flow.apply_way_29') }}</t-button>
            </div>
          </div>
        </div>
      </div>
    </template>

    <template #closeBtn>

      <iconpark-icon name="iconerror" style="font-size: 24px"></iconpark-icon>
    </template>
    <!-- <template #footer>
      <div class="operates">


        <t-button
          theme="primary"
          class="operates-item"
          @click="onClose"
        >确定</t-button>
      </div>
    </template> -->
  </t-dialog>
  <!-- <t-upload
    ref="uploadRef"
    v-model="files"
    class="ossUpload"
    theme="custom"
    multiple
    :max="20"
    :action="null"
    :before-upload="beforeUpload"
    :on-select-change="onSelectChange"
  /> -->

</template>

<script lang="ts" setup>

/**
 * @description 选择组织
 * <AUTHOR>
 */
import { debounce } from "lodash";
import { Ref, reactive, ref } from "vue";
import { MessagePlugin } from "tdesign-vue-next";
// import OSS from "ali-oss";
// import {
//   getStsToken,
// } from "@renderer/api/cloud.ts";
import { v4 as uuidv4 } from "uuid";
import md5 from "js-md5";
const emits = defineEmits("reload", "onSelectChange", "onUploadFileEmit", "onDownloadModel", "onShowMemberFlow");
const visible = ref(false);
const isIn = ref(false);
const dragenter = (e) => {
  e.stopPropagation();
  e.preventDefault();
  console.log('dragenter');
  isIn.value = true;
};

const dragleave = (e) => {
  e.stopPropagation();
  e.preventDefault();
  console.log(e.layerX, e.layerY);
  // console.log(e.relatedTarget.className);
  if (e.layerX > 0 && e.layerX < 200 && e.layerY > 0 && e.layerY < 150) {

  } else {
    isIn.value = false;
  }

};
const stoppre = (e) => {
  e.stopPropagation();
  e.preventDefault();
};
const fileDowArray = ref([]);
const dropUpload = (e) => {
  e.stopPropagation();
  e.preventDefault();
  console.log('ff');
  if (isIn.value) {
    isIn.value = false;
    fileDowArray.value = [];
    console.log('开始上传', e);
    let fileListFile = Array.from(e.dataTransfer.files);
    uploadFile(fileListFile);
  }
};

const onUploadFileEmit = () => {

  emits('onUploadFileEmit');
};

const onShowMemberFlow = () => {
  emits("onShowMemberFlow");
};
const client = ref(null);
const uploadFile = (val) => {
  console.log(val);
  let obj = null;

  if (Array.isArray(val) && val.length > 0) {
    obj = val[0];
    const uuid = uuidv4();
    if (!(obj.name.endsWith('.xlsx') || obj.name.endsWith('.xls'))) {
      fileDowArray.value.push({
        name: obj.name,
        percent: obj.percent || 0,
        size: obj.size,
        status: "error",
        tip: "请上传xlsx或xls格式文件",
        id: uuid,
        row: obj,
        raw: obj,
      });
    } else if (obj.size > 2 * 1024 * 1024) {
      fileDowArray.value.push({
        name: obj.name,
        percent: obj.percent || 0,
        size: obj.size,
        status: "error",
        tip: "上传失败,文件超过2M",
        id: uuid,
        row: obj,
        raw: obj,
      });
    } else if (obj.size === 0) {
      fileDowArray.value.push({
        name: obj.name,
        percent: obj.percent || 0,
        size: obj.size,
        status: "error",
        tip: "上传失败,文件內容為空",
        id: uuid,
        row: obj,
        raw: obj,
      });
    } else {
      fileDowArray.value.push({
        name: obj.name,
        percent: obj.percent || 0,
        size: obj.size,
        status: "waiting",
        tip: "上传完成",
        id: uuid,
        row: obj,
        raw: obj,
      });
    }

    console.log(fileDowArray.value);
    if (fileDowArray.value[0].status !== 'waiting') {
      return;
    }
  } else {
    return;
  }
  onClose();

  // 导入数据

  emits('onSelectChange', fileDowArray.value[0].row, { currentSelectedFiles: fileDowArray.value });


 /*  getStsToken().then((res) => {
    client.value = new OSS({
      region: "oss-cn-shenzhen",
      accessKeyId: res.data.data.AccessKeyId,
      accessKeySecret: res.data.data.AccessKeySecret,
      stsToken: res.data.data.SecurityToken,
      refreshSTSTokenInterval: 300000,
      bucket: "kuaiyouyi",
    });
    const result = fileDowArray.value.reduce(
      (pre, cur) => pre.then(() => updAwaitList(cur)),
      Promise.resolve()
    );
    result.then((re) => {
      console.log(re, "qqqqqqq");
    });

  }); */
};




const formatDate = (timeStamp) => {
  const date = new Date(timeStamp); // 创建Date对象
  const year = date.getFullYear(); // 获取年份
  const month = date.getMonth() + 1; // 获取月份，记得+1
  const day = date.getDate(); // 获取日期
  return `${year}/${month}/${day}`; // 返回格式化后的日期字符串
};

const updAwaitList = (es) => {
  let fileName = es.name;
  let esNAme = "";
  let types = fileName.substring(fileName.lastIndexOf(".") + 1);
  esNAme = `disk/${formatDate(new Date().getTime())}/${md5(
    fileName + new Date().getTime()
  )}.${types}`;

  return new Promise((resolve) => {
    if (es.status !== 'waiting') {
      resolve();
    } else {
      console.log(esNAme);
      console.log(es);
      client.value
        .multipartUpload(esNAme, es.row ? es.row : es, {
          progress: (p, cpt) => {
            // 获取上传进度。
            const index = fileDowArray.value.findIndex(
              (e) => e.id === es.id
            );
            const nump = p * 100;
            fileDowArray.value[index].percent = nump.toFixed(2);
            fileDowArray.value[index].cpt = cpt;
            if (p > 0) {
              fileDowArray.value[index].status = "waiting";
              fileDowArray.value[index].tip = "等待中";
            }
            if (p * 100 === 100) {
              fileDowArray.value[index].status = "success";
              fileDowArray.value[index].tip = "已完成";
            }
          },
        }).then((res) => {
          resolve(res);
          console.log(res, "上傳qqqqqqqqqqqsssssssssss");
        });
    }
  });
};


const onOpen = () => {
  fileDowArray.value = [];
  visible.value = true;
};
const onClose = () => {
  visible.value = false;
};

defineExpose({
  onOpen,
  onClose
});
</script>

<style lang="less" scoped>
@import "@renderer/views/engineer/less/common.less";

:deep(.t-dialog__header-content) {
  font-size: 16px;

  font-weight: 700;
  text-align: left;
  color: #13161b;
}

.msgList {
  display: flex;
  flex-direction: column;
  &-item {
  }
}

.header {
  display: flex;
  align-items: center;
  gap: 16px;
  .title {
    color: var(--kyy_color_modal_title, #1A2139);
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px; /* 150% */
  }
  .tip {
    color: var(--brand-kyy-color-brand-default, #4D5EFF);

    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */

    display: flex;
    align-items: center;
    .iconhelp {
      font-size: 20px;
    }
  }
}
.toBody {
  .info {
    color: var(--kyy_color_alert_text, #1A2139) !important;

    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
    padding: 8px 24px;
    :deep(.t-alert__description) {
      color: var(--kyy_color_alert_text, #1A2139) !important;
    }
  }
  .fox {
    // column-count:2;
    display: flex;

    .item {
      width: 50%;
      height: 232px;
      -webkit-column-break-inside: avoid;
      display: flex;
      flex-direction: column;
      align-items: center;
      // padding: 0 24px;
      justify-content: space-between;
      &:first-child {
        position: relative;
        &::after {
          position: absolute;
          content: " ";
          height: 224px;
          border-left: 1px dashed var(--divider-kyy-color-divider-deep, #D5DBE4);
          right: -4px;
          top: 0;
          bottom: 0;
          margin: auto 0;
        }

      }
      .ecex {

        display: flex;
        flex-direction: column;
        align-items: center;
        .text {
          overflow: hidden;
          color: var(--text-kyy-color-text-3, #828DA5);
          text-align: center;
          text-overflow: ellipsis;
          white-space: nowrap;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px; /* 157.143% */
        }
      }
      .up {

        position: relative;
        z-index: 2;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 12px 24px;
        transition: all 0.25s linear;
        border: 0 dashed var(--border-kyy-color-border-default, #D5DBE4);

        .text {
          overflow: hidden;
          color: var(--text-kyy-color-text-3, #828DA5);
          text-align: center;
          text-overflow: ellipsis;
          white-space: nowrap;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 44px; /* 157.143% */
          height: 44px;
        }
        .error {
          overflow: hidden;
          color: var(--error-kyy-color-error-default, #D54941);
          text-align: center;
          text-overflow: ellipsis;
          white-space: nowrap;

          /* kyy_fontSize_2/regular */
          font-family: PingFang SC;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 44px; /* 157.143% */
          height: 44px;
        }

        // &:hover {
        //   background: var(--bg-kyy-color-bg-deep, #F5F8FE);
        //   transition: all 0.25s linear;
        // }
      }

      .dash {
        border: 1px dashed var(--border-kyy-color-border-default, #D5DBE4);

      }
      img {
        width: 80px;
        height: 80px;
      }
      h3 {
        color: var(--text-kyy-color-text-1, #1A2139);
        text-align: center;
        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        line-height: 24px; /* 150% */
      }

      .mo {
        height: 28px;
        color: var(--color-button-border-kyy-color-button-border-text-default, #516082);
        text-align: center;
        font-size: 14px !important;
        font-style: normal;
        font-weight: 400 !important;
        line-height: 22px; /* 157.143% */
        :deep(.t-button__text) {
          font-weight: 400 !important;
        }
      }

    }
  }
}


:deep(.t-form__item) {
  margin-bottom: 10px;
}

.t-dialog__ctx .t-dialog__position {
  padding: 0;
}


</style>
