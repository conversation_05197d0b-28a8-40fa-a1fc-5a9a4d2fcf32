<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="20" height="20" rx="4" fill="url(#paint0_linear_73_5451)"/>
<g clip-path="url(#clip0_73_5451)">
<path d="M3.46478 8.28849C3.08128 8.10856 3.08128 7.56311 3.46478 7.38318L9.82315 4.39996C9.95769 4.33683 10.1134 4.33683 10.2479 4.39996L16.6063 7.38318C16.9898 7.56311 16.9898 8.10856 16.6063 8.28849L10.2479 11.2717C10.1134 11.3348 9.95769 11.3348 9.82315 11.2717L3.46478 8.28849Z" stroke="white" stroke-width="1.5"/>
<path d="M5.57104 8.83584L5.50377 14.1997C5.50145 14.3847 5.60146 14.5557 5.76634 14.6397C6.49699 15.0116 8.57322 16.0004 10 16.0004C11.4319 16.0004 13.518 15.0045 14.2415 14.6357C14.4023 14.5537 14.5014 14.3887 14.5037 14.2082L14.571 8.83584" stroke="white" stroke-width="1.5"/>
<path d="M16.9 11.3V7.8" stroke="white" stroke-width="1.5" stroke-linecap="round"/>
</g>
<defs>
<linearGradient id="paint0_linear_73_5451" x1="-2.5" y1="-2.5" x2="8" y2="8.5" gradientUnits="userSpaceOnUse">
<stop offset="0.452081" stop-color="#25F1FF"/>
<stop offset="1" stop-color="#2ED2FF"/>
</linearGradient>
<clipPath id="clip0_73_5451">
<rect width="16" height="16" fill="white" transform="translate(2 2)"/>
</clipPath>
</defs>
</svg>
