<template>
  <t-drawer
    v-model:visible="visible"
    :close-btn="true"
    class="drawerSetForm"
    size="472px"
    :header="t('forum.addTopic')"
    @close="closeDrawer"
  >
    <div class="topic-form-box">
      <t-form ref="topicFormRef" :data="topicFormData" :rules="topicFormRules" labelAlign="top" :labelWidth="100">
        <t-form-item :label="t('forum.topicName')" name="name">
          <t-input
            v-model="topicFormData.name"
            :placeholder="t('forum.topicNamePlaceholder')"
            :maxlength="30"
            show-limit-number
          ></t-input>
        </t-form-item>
        <t-form-item :label="t('forum.topicDes')" name="description">
          <t-textarea
            v-model="topicFormData.description"
            :placeholder="t('forum.topicDesPlaceholder')"
            :maxlength="200"
            :autosize="{ minRows: 2 }"
            show-limit-number
            style="padding-bottom: 20px"
          ></t-textarea>
        </t-form-item>
      </t-form>
    </div>

    <template #footer>
      <div class="foot">
        <t-button variant="outline" class="btn1" @click="closeDrawer">
          {{ t("forum.cl") }}
        </t-button>
        <t-button class="btn3" @click="submit" :loading="submitLoading">
          {{ t("forum.addTopicFinish") }}
        </t-button>
      </div>
    </template>
  </t-drawer>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import { useI18n } from "vue-i18n";
import to from "await-to-js";
import { addTopic } from "@/api/uni/api/forumAdminApi";
import { MessagePlugin } from "tdesign-vue-next";

const emit = defineEmits(["addSuccess"]);

const { t } = useI18n();

const topicFormRef = ref(null);

// 抽屉显示状态
const visible = ref(false);

// 提交状态
const submitLoading = ref(false);

// 话题表单
const topicFormData = reactive({
  name: null,
  description: null,
});

// 表单验证规则
const topicFormRules = {
  name: [
    {
      required: true,
      message: t("forum.topicNamePlaceholder"),
      trigger: "blur",
    },
  ],
  description: [
    {
      required: true,
      message: t("forum.topicDesPlaceholder"),
      trigger: "blur",
    },
  ],
};

// 打开抽屉
const openDrawer = () => {
  visible.value = true;
};

// 关闭抽屉
const closeDrawer = () => {
  visible.value = false;
  setTimeout(() => {
    topicFormRef.value.reset();
  }, 200);
};

// 提交表单
const submit = async () => {
  const result = await topicFormRef.value.validate();
  if (result !== true) {
    return;
  }
  submitLoading.value = true;

  const teamInfo = JSON.parse(localStorage.getItem("honorteam"));
  const [error] = await to(addTopic({ ...topicFormData, creator: teamInfo.cardId }));

  submitLoading.value = false;

  if (error) {
    return;
  }

  MessagePlugin.success(t("forum.addTopicSuccess"));
  emit('addSuccess');
  closeDrawer();
};

defineExpose({
  openDrawer,
});
</script>

<style lang="less" scoped>
.topic-form-box {
  margin-top: 6px;
  word-break: break-all;

  :deep(.t-input--suffix){
    padding-right: 4px;
  }

  :deep(.t-input__limit-number){
    color: var(--text-kyy_color_text_5, #ACB3C0);
    font-size: 12px;
  }

  :deep(.t-textarea__inner){
    border: 1px solid var(--textarea-kyy_color_textarea_border_default, #D5DBE4);
  }

  :deep(.t-textarea__limit) {
    display: inline !important;
    line-height: 20px;
    position: absolute;
    right: 4px;
    bottom: 0;
    color: var(--text-kyy_color_text_5, #ACB3C0);
    font-size: 12px;
  }
}

.foot {
  width: 100%;
  display: flex;
  justify-content: end;
  .btn1 {
    display: flex;
    cursor: pointer;
    height: 32px;
    min-width: 80px;
    min-height: 32px;
    max-height: 32px;
    padding: 0 16px;
    justify-content: center;
    align-items: center;
    gap: 4px;
    color: var(--color-button-border-kyy-color-button-border-text-default, #516082);
    text-align: center;
    border-radius: var(--radius-kyy-radius-button-s, 4px);
    border: 1px solid var(--color-button-border-kyy-color-button-border-border-dedault, #d5dbe4) !important;
    background: var(--color-button-border-kyy-color-button-border-bg-default, #fff);
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px; /* 157.143% */
    margin-right: 8px;

    &:hover {
      color: var(--color-button-border-kyy-color-button-border-text-default, #516082) !important;
      background: transparent !important;
    }
  }
  .btn2 {
    display: flex;
    cursor: pointer;
    width: 88px;
    height: 32px;
    min-height: 32px;
    max-height: 32px;
    padding: 6px 16px;
    justify-content: center;
    align-items: center;
    gap: 4px;
    border-radius: var(--radius-kyy-radius-button-s, 4px);
    background: var(--color-button-primary-kyy-color-button-primary-bg-disabled, #c9cfff);
    color: var(--color-button-primary-kyy-color-button-primary-text, #fff);
    text-align: center;

    /* kyy_fontSize_2/bold */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px; /* 157.143% */
  }
  .btn3 {
    display: flex;
    cursor: pointer;
    min-width: 88px;
    height: 32px;
    min-height: 32px;
    max-height: 32px;
    padding: 6px 16px;
    justify-content: center;
    align-items: center;
    gap: 4px;
    border-radius: var(--radius-kyy-radius-button-s, 4px);
    background: var(--color-button-primary-kyy-color-button-primary-bg-default, #4d5eff);

    color: var(--color-button-primary-kyy-color-button-primary-text, #fff);
    text-align: center;
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px;
    margin-left: 0;
  }
}
</style>
