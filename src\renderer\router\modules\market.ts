export default [
  {
    path: '/bigMarketIndex',
    name: 'bigMarketIndex',
    component: () => import('@renderer/views/big-market/index.vue'),
    children: [
      {
        path: 'home',
        name: 'bigMarketHome',
        component: () => import('@renderer/views/big-market/home.vue'),
      },
      {
        path: 'desired',
        name: 'bigMarketDesired',
        component: () => import('@renderer/views/big-market/desired.vue'),
      },
      {
        path: 'supply',
        name: 'bigMarketSupply',
        component: () => import('@renderer/views/big-market/supply.vue'),
      },
      {
        path: 'classify',
        name: 'bigMarketClassify',
        component: () => import('@renderer/views/big-market/classify.vue'),
      },
      {
        path: 'bigMarketDetailReadOnly',
        name: 'bigMarketDetailReadOnly',
        component: () => import('@renderer/views/niche/nicheDetailReadOnly.vue'),
      },
    ],
  },
];
