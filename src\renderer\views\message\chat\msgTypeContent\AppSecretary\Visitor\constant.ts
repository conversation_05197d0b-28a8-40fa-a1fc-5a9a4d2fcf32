import { i18nt } from '@renderer/i18n';

/**
 * 场景类型枚举
 */
export enum SceneType {
  /**
   * 加入组织
   */
  VISIT_BE_MEMBER = 5062,
  /**
   * 访客申请拒绝
   */
  VISIT_REJECT_MEMBER = 5063,
  /**
   * 移除组织
   */
  VISIT_DELETE_MEMBER = 5042,
  /**
   * 入会申请
   */
  VISIT_BE_POLITICS = 14032,
  /**
   * 入会通过
   */
  VISIT_REJECT_POLITICS = 14033,
  /**
   * 入会申请
   */
  VISIT_BE_CBD = 16032,
  /**
   * 入会通过
   */
  VISIT_REJECT_CBD = 16033,
  /**
   * 入会申请
   */
  VISIT_BE_ASSOCIATION = 19032,
  /**
   * 入会通过
   */
  VISIT_REJECT_ASSOCIATION = 19033,
  /**
  * 入会通过
  */
  VISIT_REJECT = 51033,
  /**
  * 入会申请
  */
  VISIT_BE = 51032
}

/**
 * 展示团队logo和标题
 */
export const SHOW_DETAIL_BTN_SCENES = [
  SceneType.VISIT_BE_MEMBER,
  SceneType.VISIT_BE_POLITICS,
  SceneType.VISIT_BE_CBD,
  SceneType.VISIT_BE_ASSOCIATION,
];
/**
 * 展示团队logo和标题
 */
// export const titleMap = {
//   [SceneType.VISIT_BE_MEMBER]: i18nt("im.public.biz"),
//   [SceneType.VISIT_REJECT_MEMBER]: i18nt("im.public.biz"),
//   [SceneType.VISIT_DELETE_MEMBER]: i18nt("im.public.biz"),
//   [SceneType.VISIT_BE_POLITICS]: i18nt("im.public.government"),
//   [SceneType.VISIT_REJECT_POLITICS]: i18nt("im.public.government"),
//   [SceneType.VISIT_BE_CBD]: i18nt("application.digital_cbd"),
//   [SceneType.VISIT_REJECT_CBD]: i18nt("application.digital_cbd"),
//   [SceneType.VISIT_BE_ASSOCIATION]: i18nt("niche.szsq"),
//   [SceneType.VISIT_REJECT_ASSOCIATION]: i18nt("niche.szsq"),
// };

export const getTitle = (type) => {
  const titleMap = {
    cbd: i18nt('application.digital_cbd'),
    government: i18nt('im.public.government'),
    member: i18nt('im.public.biz'),
    association: i18nt('niche.szsq'),
    uni: i18nt('niche.szgx'),
  };
  return titleMap[type] || '';
};
export const SceneTypeArr = Object.values(SceneType).filter((value) => typeof value === 'number');
