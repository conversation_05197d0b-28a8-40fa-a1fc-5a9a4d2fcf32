<template>
  <div>
    <div class="text-[#1A2139] text-16 leading-24 font-600">
      <span>2.</span>
      {{ t('activity.activity.detail') }}
    </div>
    <t-form ref="form" class="activity-particulars-form mt-16" :data="activityFormData.particulars" label-align="top">
      <div class="flex gap-24">
        <!--活动详情-->
        <t-form-item class="!mb-0 editorWrap" :label="t('activity.activity.content_label')" name="content">
          <div class="h-full w-608 editor-wrap">
            <Editor
              ref="editorRef"
              editor-type="A"
              root-dir="activity"
              :options="{ toolbar: ['annex', 'link', 'code'] }"
              @img-insert="imgInsert"
              @update="handleContentChange"
              :maxlength="false"
            />
          </div>
        </t-form-item>

        <!--上传附件-->
        <div class="activity-files-box pl-24 h-462">
          <t-form-item name="files">
            <template #label>
              <div class="flex items-center gap-4">
                <span>{{ t('activity.activity.files_label') }}</span>
                <t-tooltip :content="t('activity.activity.files_tip')">
                  <img class="flex-shrink-0" src="@renderer/assets/activity/icon_help.svg" alt="" />
                </t-tooltip>
              </div>
            </template>

            <activity-file-upload v-model:files="activityFormData.particulars.files.files" />
          </t-form-item>
        </div>
      </div>
    </t-form>
  </div>
</template>

<script setup lang="ts">
import { inject, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import _ from 'lodash';
import Editor from '@/components/editor/index.vue';
import ActivityFileUpload from './ActivityFileUpload.vue';

const { t } = useI18n();

const editorRef = ref(null);

// 表单数据
const activityFormData = inject('activityFormData');

const noteData = ref({
  content: {
    images: [],
  },
  annex_size: 0,
  user_id: '',
  openId: '',
});

// 上传图片
const imgInsert = (val) => {
  val.forEach((img) => {
    // 更新content images
    const imgObj = {
      name: img.name,
      size: img.size,
      type: img.type.split('/')[1],
      url: img.url,
    };
    noteData.value.content.images.push(imgObj);
  });
};

// 富文本内容更新
const handleContentChange = (contents) => {
  const content = {
    imageUrl: '',
    description: '',
    images: [],
    attachments: [],
    delta: '',
  };
  /**
   * 处理content里的数据
   * description： 所有文本信息加起来
   * imageUrl：第一张图片
   * images：所有的图片集合
   * attachments：所有的文件集合
   * delta：富文本内容
   */
  const delta = [];
  // 附件大小
  let size = 0;
  contents.forEach((v) => {
    let deltaItem = _.cloneDeepWith(v);
    if (typeof v.insert === 'string') {
      content.description += v.insert.trim();
    }
    if (v.insert?.image) {
      !content.imageUrl && (content.imageUrl = v.insert.image);
      const imgItem = noteData.value.content.images.find((img) => img.url === v.insert.image);
      content.images.push(imgItem);
      imgItem?.size && (size += imgItem.size);
    }
    if (v.insert?.custom) {
      size += v.insert.custom.size;
      content.attachments.push(v.insert.custom);
      const atta = { attachment: JSON.stringify(v.insert.custom) };
      deltaItem.insert = { custom: JSON.stringify(atta) };
    }
    delta.push(deltaItem);
  });
  content.delta = JSON.stringify(delta);
  activityFormData.particulars.content = content.delta;
  if (content.delta === '[{"insert":"\\n"}]') {
    activityFormData.particulars.content = null;
  }
};

defineExpose({
  editorRenderContent: (content) => {
    const delta = content ? JSON.parse(content) : [];
    editorRef.value?.renderContent({ ops: delta }, false);
  },
});
</script>

<style lang="less" scoped>
:deep(.activity-particulars-form) {
  .t-form__label {
    line-height: 22px;
    min-height: 22px;
    padding: 0;

    &.t-form__label--top {
      margin-bottom: 8px;
    }
  }

  .t-form__controls {
    min-height: 22px;
    .t-form__controls-content {
      min-height: 22px;
    }
  }

  .lk-editor {
    border-radius: 4px;
    border: 1px solid #d5dbe4;
  }

  .activity-files-box {
    border-left: 1px solid #eceff5;
  }

  .upload-file-card-list {
    height: calc(100vh - 276px);
    overflow: auto;
    overflow-x: hidden;
    padding-right: 8px;

    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: 4px;
      background: var(--icon-kyy_color_icon_transparent, rgba(26, 33, 57, 0.36));
    }

    &::-webkit-scrollbar-track {
      background-color: transparent;
    }
  }

  .editorWrap {
    display: flex;
    flex-direction: column;
    .t-form__controls {
      flex-grow: 2;
    }
    .t-form__controls-content {
      height: 100%;
    }
  }
}
.editor-wrap {
  :deep(.lk-editor) {
    overflow: hidden;
  }
  :deep(.lk-toolbar) {
    border-radius: 0;
  }
  :deep(.lk-editor-body) {
    height: calc(100% - 48px) !important;
  }
}
</style>
