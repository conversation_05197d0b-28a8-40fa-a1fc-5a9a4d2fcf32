<template>
  <div class="container">
    <div class="head" style="padding: 16px">
      <t-button theme="primary" variant="base" @click="onAddManage">
        <template #icon>
          <iconpark-icon name="iconadd" class="iconadd"></iconpark-icon>
        </template>
        {{$t('member.card.e')}}
      </t-button>
    </div>
    <div v-if="false" class="header">
      <h1 class="header-title">    {{$t('member.card.f')}}</h1>
      <ul class="header-buttons">
        <li>
          <t-button theme="primary" variant="base" @click="onAddManage">
            {{$t('member.card.e')}}</t-button>
        </li>
      </ul>
    </div>
    <div class="body">
      <div class="body-content">
        <div class="table">
          <!-- <span @click="onToWaySuper">转移超级管理员</span> -->
          <t-table
            row-key="id"
            :columns="memberColumns"
            :pagination="pagination.total > 10 ? pagination : null"
            :data="memberData"
            style="width: 100%; margin-top: 8px"
          >
            <template #name="{ row }">
              <div class="name-av">
                <div>
                  <kyy-avatar
                    :avatar-size="'32px'"
                    :image-url="row.avatar"
                    :user-name="row?.name"
                    :shape="'circle'"
                  />
                </div>
                <div>
                  {{ row.name }}
                </div>
              </div>
            </template>
            <template #role="{ row }">
              <div>
                {{ row.type_text }}
              </div>
            </template>
            <template #empty>
              <div class="empty">
                <noData :text="$t('engineer.no_data')" />
              </div>
            </template>
            <template #operate="{ row }">
              <!-- v-show="row.type === 2" -->
              <div class="btns">
                <t-link
                  v-show="
                    row.type === 2 &&
                      (isAdminValue?.superAdmin || isAdminValue?.isAdmin)
                  "
                  theme="primary"
                  hover="color"
                  class="operates-item"
                  @click="onToWaySuper(row)"
                >
                  {{ $t("engineer.move_super_admin") }}
                </t-link>
                <t-link
                  v-show="
                    row.type === 1 &&
                      (isAdminValue?.isAdmin ||
                        isAdminValue?.super ||
                        isAdminValue?.superAdmin)
                  "
                  theme="danger"
                  hover="color"
                  class="operates-item"
                  @click="delItem(row)"
                >
                  {{ $t("member.sadim.s_6") }}
                </t-link>
              </div>
            </template>
          </t-table>
        </div>
      </div>
    </div>
  </div>

  <to-way-super-admin-modal ref="toWaySuperAdminModalRef" @reload="onReload" />
  <SelectMemberModal
    ref="selectMemberModalRef"
    :options="optionsMembers"
    :header="$t('member.card.g')"
    @sub-form="onListenMembers"
  />
</template>

<script lang="ts" setup>
import {
  getMemberAdminAxios,
  getAppStaffAxios,
  addAdminAxios,
  deleteAdminAxios,
  checkIsAdminAxios
} from "@renderer/api/uni/api/businessApi";
import noData from "@renderer/components/approvalSatellite/approvalData/noData.vue";
import { getResponseResult } from "@renderer/utils/myUtils";
import { DialogPlugin, MessagePlugin } from "tdesign-vue-next";
import { computed, reactive, ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import ToWaySuperAdminModal from "@renderer/views/uni/member_home/panel/membership-setting-panel/modal/to-way-super-admin-modal.vue";
import SelectMemberModal from "@renderer/views/uni/member_home/panel/membership-setting-panel/modal/select-member-modal.vue";
import { useUniStore } from "@renderer/views/uni/store/uni";
import { useRoute, useRouter } from "vue-router";
import KyyAvatar from "@/components/kyy-avatar/index.vue";
import { platform } from "@renderer/views/digital-platform/utils/constant";
import { useDigitalPlatformStore } from "@renderer/views/digital-platform/store/digital-platform-store";
import { getUniTeamID } from "@renderer/views/uni/utils/auth";
import { onMountedOrActivated } from "@renderer/hooks/onMountedOrActivated";
const digitalPlatformStore = useDigitalPlatformStore();

const store = useUniStore();
const { t } = useI18n();
const router = useRouter();
const memberColumns = ref([
  { colKey: "name", title: t('member.manager.name'), width: 160 },
  { colKey: "role", title:  t('member.card.h'), width: 300, ellipsis: false },
  { colKey: "operate", title:  t('member.card.i'), width: 120 }
]);
const route = useRoute();
const memberData = ref([]);
const optionsMembers = ref([]);
// getMemberAdminAxios

const props = defineProps({
  platform: {
    type: String,
    default: '',
  },
})
// 平台类型 目前只有digital-platform
const platformCpt = computed(() => {
  return props.platform || route.query?.platform
})

const activeAccount = computed(() => {
  if(platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount
  } else if(platformCpt.value === platform.digitalWorkbench)  {
    return route.query;
  } else {
    return store.activeAccount
  }
})


const currentTeamId = computed(() => {
  if(platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount?.teamId
  } else if(platformCpt.value === platform.digitalWorkbench){
    return route.query?.teamId || 0;
  } else {
    return  getUniTeamID()
  }
})


const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
  onChange: (pageInfo) => {
    console.log("pagination.onChange", pageInfo);
    pagination.current = pageInfo.current;
    pagination.pageSize = pageInfo.pageSize;

    getMemberList({});
  }
});

// 获取管理员列表
const getMemberList = async (params) => {
  // teamId
  params.page = pagination.current;
  params.pageSize = pagination.pageSize;
  try {
    let result = await getMemberAdminAxios(params, currentTeamId.value);
    console.log(result);
    result = getResponseResult(result);
    if (!result) return;
    memberData.value = result.data.list;
    pagination.total = result.data.total;
    console.log(memberData.value);
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    MessagePlugin.error(errMsg);
  }
};

getMemberList({});

const selectMemberModalRef = ref(null);
const onAddManage = () => {
  getAppMemberList().then(() => {
    // const arr = memberData.value.map((v) => v.idStaff);
    // selectMemberModalRef.value.onSetDisableArr(arr);
    selectMemberModalRef.value.onOpen();
  });
};

const isAdminValue = ref(null);

// 判断当前用户是否为管理员
const onCheckIsAdmin = (idStaff) => {
  let res: any = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      res = await checkIsAdminAxios({ idStaff }, currentTeamId.value);
      res = getResponseResult(res);
      if (!res) {
        reject();
        return;
      }

      isAdminValue.value = res.data;
      resolve(res.data);
    } catch (error) {
      const errMsg = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
      reject();
    }
  });
};

const onReload = () => {
  getMemberList({});
  if (store.activeAccount) {
    onCheckIsAdmin(store.activeAccount?.staffId).then(() => {
      console.log(isAdminValue.value);
      if (
        isAdminValue.value.super ||
        isAdminValue.value.isAdmin ||
        isAdminValue.value.superAdmin
      ) {
        console.log("");
      } else {
        removeTab();
      }
    });
  }
};
const removeTab = () => {
  console.log(store.getActiveTab());
  // store.removeTab(item.fullPath);
  // router.replace(store.getActiveTab().fullPath);
  const activeTab = store.getActiveTab();

  store.removeTab(activeTab.fullPath);
  router.replace(store.getActiveTab().fullPath);
};
// watch(
//   () => store.activeAccount,
//   (val) => {
//     if (val) {
//       setTimeout(() => {
//         onCheckIsAdmin(val?.staffId);
//       });
//       getMemberList({});
//     }
//   },
//   {
//     deep: true,
//     immediate: true
//   }
// );

const getAppMemberList = () => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await getAppStaffAxios({}, currentTeamId.value);
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      optionsMembers.value = result.data;
      resolve("success");
    } catch (error) {
      reject();
      const errMsg = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
    }
  });
};

// 添加成员
const onListenMembers = async (arr) => {
  console.log(arr);
  if (arr && arr.length < 1) return;
  let result = null;
  try {
    result = await addAdminAxios({ idStaffs: arr }, currentTeamId.value);
    result = getResponseResult(result);
    if (!result) return;
    MessagePlugin.success(t('member.impm.pta_3'));
    getMemberList({});
    selectMemberModalRef.value.onClose();
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    MessagePlugin.error(errMsg);
  }
};

const toWaySuperAdminModalRef = ref(null);

const onToWaySuper = (row) => {
  toWaySuperAdminModalRef.value.onOpen(row);
};

const delItem = (val) => {
  // 改后端判断

  let res = null;
  const confirmDia = DialogPlugin({
    header: t('member.card.j'),
    theme: "info",
    className: "modeIconPadding",
    confirmBtn: t('member.impm.input_9'),
    body:t('member.card.k'),
    // cancelBtn: null,
    closeBtn: false,
    onConfirm: async () => {
      try {
        res = await deleteAdminAxios(val.id, currentTeamId.value);
        res = getResponseResult(res);
        if (!res) return;
        // // getRoleLists();
        // onGetAdminList();
        MessagePlugin.success(t('member.card.l'));
        // getMemberList({});
        onReload();
      } catch (error) {
        const errMsg = error instanceof Error ? error.message : error;
        MessagePlugin.error(errMsg);
      }
      confirmDia.hide();
    },
    onClose: () => {
      confirmDia.hide();
    }
  });

  // 去掉后面部分，但留着备用


};

const onEnter = () => {};

onMountedOrActivated(() => {
  if(activeAccount.value) {
    onCheckIsAdmin(activeAccount.value?.staffId);
  }
  getMemberList({});
});
</script>

<style lang="less" scoped>
@import "./public.less";
.iconadd {
  color: #ffffff;
  font-size: 24px;
}

:deep(.t-table table) {
  width: 100%;
  // min-width: 100%;
}
.btns {
  display: flex;
  gap: 8px;
}
.name-av {
  display: flex;
  gap: 12px;
  align-items: center;
}

:deep(.t-table__body) {
  td {
    color: var(--kyy_color_table_text, #1A2139);
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
}
</style>
