<template>
  <!-- background: rgb(39,43,79); calc(100% - 48px)  -->
  <div class="cardBox">
    <div class="bg">
      <work-area-head @refresh="refresh" ref="digitalPlatformHeadRef" />
      <!-- v-if="store.activeAccount" -->

      <!-- <template> -->
        <!-- :key="$route.fullPath" -->
      <router-view
        v-slot="{ Component }"

        class="square-content routerView"
      >
        <!-- :exclude="data.exc" -->
        <!-- :key="pageKey" -->
        <!--  -->
        <keep-alive
          :key="pageKey"
        >
          <component
            :is="Component"
            :key="keyInfo"
            :platform="platform.digitalPlatform"
            ref="currComponentRef"
          />
        </keep-alive>
      </router-view>
      <!-- :uuid="currentUuid" -->

      <!-- <div v-else class="empty">
          <div class="nodepan">
            <img src="@renderer/assets/prompt-picture/notepurview.png">
            <div>你没有会员管理权限</div>
          </div>
        </div> -->
      <!-- </template> -->
      <!-- <template v-else>
        <div class="empty">
          <div class="nodepan">
            <img src="@renderer/assets/prompt-picture/notepurview.png">
            <div>你没有开通商协会组织</div>
          </div>
        </div>
      </template> -->
    </div>
  </div>
</template>

<script setup lang="tsx">
import { computed, onMounted, ref, watch } from "vue";
import { useRouter, useRoute} from "vue-router";
import WorkAreaHead from "@renderer/views/digital-platform/components/work-area-head.vue";
// import WorkAreaHead from "@renderer/views/digital-platform/components/work-area-head-2.vue";
import { platform } from "@renderer/views/digital-platform/utils/constant";
import { useDigitalPlatformStore } from "@renderer/views/digital-platform/store/digital-platform-store";
import { HEADER_FORUM_KEY } from '@renderer/constants/localKey';
import { getResponseResult, getSubRoutes } from "@renderer/utils/myUtils";
import { checkIsAdminAxios } from "@renderer/api/member/api/businessApi";
import { useForumStore } from "@renderer/views/digital-platform/forum/store";
import { DialogPlugin, MessagePlugin } from "tdesign-vue-next";
import { refreshBus } from "./utils/eventBus";
import LynkerSDK from "@renderer/_jssdk";
import { destroyNode, getShowNode, refreshNode } from "@renderer/_jssdk/components/iframe/iframePool";
const { ipcRenderer } = LynkerSDK;
const router = useRouter();
const route = useRoute();
const digitalPlatformHeadRef = ref(null);

const store = useDigitalPlatformStore();
const forumStore = useForumStore();

// 判断是否 webview 路由（根据你的实际路由规则调整）
const isWebviewRoute = computed(() => {
  // 例1：通过 meta 字段
  return route.path.startsWith('/webview/');
  // 例2：通过 path
  // return route.path.startsWith('/webview');
});

ipcRenderer.on('forum-unread-post', () => {
  if (store.activeAccount.teamId) {
    store.getUnreadStats(store.activeAccount.teamId);
    forumStore.getUnreadStats();
  }
});
// const iconIconWindow = ref(true);
// const routerViewRef = ref(null);
const tabList = ref([]);
// const groupList = ref([]);
// const cloudDiskType = ref({
//   name: "",
//   id: "",
//   icon: "",
//   teamId: "",
// });
// const tabIndex = ref(0);
const data = ref({
  showCompoent: false,
  exc: []
});

const pageKey = ref(1);

const routeList = getSubRoutes("digitalPlatformIndex", router);
console.log(routeList);
// 将默认页面固定显示
if (store.tabs.length < 1) {
  const home = routeList.find((v) => v.affix);
  store.addTab(home);
}

const isAdminValue = ref(null);

const keyInfo = computed(() => {
  const forum = '/digitalPlatformIndex/digital_platform_forum/';
  if (route.path.indexOf(forum) !== -1) {
    return forum;
  }
  console.log('route.fullPath', route.fullPath, route.path)
  if(route?.fullPath?.indexOf('/digitalPlatformIndex/digital_platform_home') !== -1) {
    return route.path;
  }
  return route.meta?.isAdd?route.path:route.fullPath;
});

// 判断当前用户是否为管理员
const onCheckIsAdmin = async (idStaff) => {
  console.log('work-area-head onCheckIsAdmin');
  let res: any = null;
  try {
    res = await checkIsAdminAxios({ idStaff });
    res = getResponseResult(res);
    if (!res) return;

    isAdminValue.value = res.data;
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    MessagePlugin.error(errMsg);
  }
};
console.log(router,'343阿萨达');

const refresh = (changeAccount: boolean) => {
  // 论坛内部自行处理刷新逻辑
  const inForum = route.path.startsWith('/digitalPlatformIndex/digital_platform_forum');
  if (inForum) {
    if (!changeAccount) {
      refreshBus.emit('refresh', true);
    }
    return;
  }

  let showNode = null;
  try {
    showNode = getShowNode();
    console.log('refresh', showNode);
  } catch (error) {

  }
  pageKey.value++;
  store.setIsLoadHomeInfo(true);
  data.value.showCompoent = false;
  showNode.forEach((v) => {
    refreshNode(v);
  });

  setTimeout(() => {
    data.value.showCompoent = true;
    // removeTab();
  }, 900);
  // TODO 主窗口的刷新工作
};

const removeTab = () => {
  console.log(store.getActiveTab());
  // store.removeTab(item.fullPath);
  // router.replace(store.getActiveTab().fullPath);
  const activeTab = store.getActiveTab();
  if (activeTab && activeTab.fullPath === "/memberIndex/member_manage") {
    // store.removeTab(activeTab.fullPath);
    // router.replace(store.getActiveTab().fullPath);
  }
};
const handleBeforeClose = (title: string, content: string): Promise<boolean> => new Promise((resolve) => { const confirmDia = DialogPlugin.confirm({ theme: 'info', header: title, body: content, confirmBtn: '确认关闭', cancelBtn: { content: '取消', theme: 'default', variant: 'outline' }, onConfirm: async () => { confirmDia.hide(); resolve(true); }, onClose: () => { confirmDia.destroy(); resolve(false); }, }); });

onMounted(() => {
  // getGroupListabi();
  // onCheckIsAdmin(store.activeAccount?.staffId);
  LynkerSDK.ipc.handleRenderer('digital-platform-is-inited', async () => {
    console.log('digital-platform-is-inited');
    return true;
  });
  LynkerSDK.ipc.handleRenderer('open-digital-platform-tab-item', async (val) => {
    try {
      const index = store.tabs.findIndex((v) => v.path_uuid === val.path_uuid);
      if (val.path_uuid && index > -1) {
        store.tabs[index] = {
          ...store.tabs[index],
          ...val,
          title: val.title || store.tabs[index].title,
          url: val.url || store.tabs[index].url,
          icon: val.icon || store.tabs[index].icon,
          activeIcon: val.activeIcon || store.tabs[index].activeIcon,
        };
        store.activeIndex = index;
      } else {
        store.addTab(val);
      }
      router.push({
        path: val.fullPath,
        query: {
          ...(val.query || {}),
          __tabs_id__: val.path_uuid,
          __tabs_title__: val.title || store.tabs[index].title,
          __tabs_icon__: val.icon,
          __tabs_active_icon__: val.activeIcon,
        },
      });
      console.log('open-digital-platform-tab-item', val, store.tabs);
      let query = "";
      if (val.query) {
        console.log("🚀 ~ switchTab ~ item.query:", val.query);
        query = `?${new URLSearchParams(val.query).toString()}`;
      }
      console.log(`${val.path}${query}`, "switchTabswitchTab");
      console.log(val, "switchTabswitchTabitemitemitem");
      router.replace({
        path: `${val.fullPath}${query}`,
        query: {
          ...(val.query || {}),
          __tabs_id__: val.path_uuid,
          __tabs_title__: val.title,
          __tabs_icon__: val.icon,
          __tabs_active_icon__: val.activeIcon,
        },
      });
    } catch (error) {
      console.log('open-digital-platform-tab-item', error);
    } finally {
      return true;
    }
  });
  LynkerSDK.ipc.handleRenderer('close-digital-platform-tab-item', async (val) => {
    try {
      const path_uuid = val.path_uuid;
      if (path_uuid) {
        const index = store.tabs.findIndex((v) => v.path_uuid === path_uuid);
        // 关闭页面前确认
        const beforeClose = store.tabs[index]?.beforeCloseOptions;
        console.log('close-digital-platform-tab-item',  store.tabs[index]);
        if (beforeClose && beforeClose.title && beforeClose.content) {
          const confirm = await handleBeforeClose(beforeClose.title, beforeClose.content);
          if (!confirm) return;
        }
        if (index > -1) {
          const item = store.tabs[index];
          store.activeIndex
          store.removeTab(item);
          getShowNode().forEach((node) => {
            destroyNode(node.getAttribute('id') || '');
          });
          setTimeout(() => {
            const next = Math.max(0, index - 1);
            let query = "";
            const nextItem = store.tabs[next];
            if (nextItem.query) {
              console.log("🚀 ~ switchTab ~ item.query:", nextItem.query);
              query = `?${new URLSearchParams(nextItem.query).toString()}`;
            }
            console.log(`${nextItem.path}${query}`, "switchTabswitchTab");
            console.log(nextItem, "switchTabswitchTabitemitemitem");
            router.replace({
              path: `${nextItem.fullPath}${query}`,
              query: {
                ...(nextItem.query || {}),
                __tabs_id__: nextItem.path_uuid,
                __tabs_title__: nextItem.title,
                __tabs_icon__: nextItem.icon,
                __tabs_active_icon__: nextItem.activeIcon,
              },
            });
          }, 0);
        }
      }
    } catch (error) {
      console.log('close-digital-platform-tab-item', error);
    } finally {
      return true;
    }
  });
  LynkerSDK.ipc.handleRenderer('update-digital-platform-tab-item', async (val) => {
    try {
      const path_uuid = val.path_uuid;
      if (path_uuid) {
        console.log('update-digital-platform-tab-item', val, store.tabs);
        const index = store.tabs.findIndex((v) => v.path_uuid === path_uuid);
        if (index > -1) {
          store.tabs[index] = { ...val };
        }
      }
    } catch (error) {
      console.log('update-digital-platform-tab-item', error);
    } finally {
      return true;
    }
  });
  LynkerSDK.ipc.handleRenderer('get-digital-platform-tab-list', async () => {
    try {
      const list = Array.from(store.tabs)?.map((v) => ({
        title: v.title,
        path_uuid: v.path_uuid,
        path: v.fullPath,
      })) || [];
      console.log('get-digital-platform-tab-list',  list);
      return list;
    } catch (error) {
      console.log('get-digital-platform-tab-list', error);
    } finally {
      return [];
    }
  });
  LynkerSDK.ipc.handleRenderer('digital-platform-reload', async () => {
    digitalPlatformHeadRef.value.refreshTeams();
    return true;
  })
  LynkerSDK.ipc.handleRenderer('digital-platform-set-active-team-id', async (val) => {
    const res = await digitalPlatformHeadRef.value.selectTeam(val.teamId);
    return res;
  })
  LynkerSDK.ipc.handleRenderer('digital-platform-get-active-team-id', async () => {
    return digitalPlatformHeadRef.value.getActiveTeamId();
  })
});
// const settabItem = (item) => {
//   tabList.value.push(item);
// };
// const setCloudDiskType = (item) => {
//   cloudDiskType.value = item;
// };
</script>

<style lang="less" scoped>
.bg:has(.notbg) {
  background: url("@/assets/about_bg.png") !important;
}
.cardBox {
  height: 100%;
  background-color:  rgb(39,43,79);
  overflow-y: hidden !important;
  .bg {
    background: #fff;
    height: inherit;
    border-top-left-radius: 8px;
    border-bottom-right-radius: 8px;

  }
}
.routerView {
  // margin-top: 48px;
  width: 100%;
  overflow-y: auto;

  // background-color: #fff !important;
  // margin-top: 40px;
}
.empty {
  padding: 12px;
  background: #f1f2f5;
  height: calc(100% - 20px) !important;
  border-left: 1px solid #e3e6eb;
  .nodepan {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;

    height: 100%;
    width: 100%;
    background: #fff;
    img {
      width: 200px;
      height: 200px;
    }
    div {
      margin-top: 8px;
      height: 24px;
      font-size: 16px;

      font-weight: 700;
      text-align: center;
      color: #13161b;
      line-height: 24px;
    }
  }
}

.square-content {
  height: calc(100% - 40px) !important;
}
</style>
