<template>
	<t-space direction="vertical" class="page-container">
		<div class="head-title">应用管理</div>
		<t-form
			ref="form"
			:data="formData"
			layout="inline"
			label-align="right"
			reset-type="initial"
			@reset="onReset"
			@submit="onSearch"
		>
			<t-form-item label="发布组织" name="team_name">
				<t-input v-model="formData.team_name" theme="column" placeholder="请输入" class="w-260!" />
			</t-form-item>
			<t-form-item label="应用名称" name="name">
				<t-input v-model="formData.name" theme="column" placeholder="请输入" class="w-260!" />
			</t-form-item>
			<t-form-item label="创建时间">
				<t-date-range-picker
					v-model="dateRange"
					:placeholder="['开始日期', '结束日期']"
					allow-input
					clearable
					class="w-260"
					@change="onDateChange"
				/>
			</t-form-item>
			<t-form-item label="应用类型" name="type">
				<t-select v-model="formData.type" class="w-260">
					<!-- <t-option key="全部" label="全部" :value="undefined" /> -->
					<t-option v-for="(item, key) in advertiseStatus" :key="key" :label="item.label" :value="item.value" />
				</t-select>
			</t-form-item>
			<t-form-item label="保证金" name="bond_status">
				<t-select v-model="formData.bond_status" class="w-260" placeholder="请选择保证金状态">
					<t-option v-for="(item, key) in bond_status" :key="key" :label="item.label" :value="item.value" />
				</t-select>
			</t-form-item>
			<t-form-item label="状态" name="deleted_status">
				<t-select v-model="formData.deleted_status" class="w-260" placeholder="请选择应用状态">
					<t-option v-for="(item, key) in deleted_status" :key="key" :label="item.label" :value="item.value" />
				</t-select>
			</t-form-item>
			<t-form-item label-width="0">
				<t-space size="small">
					<t-button theme="primary" type="submit">查询</t-button>
					<t-button variant="outline" type="reset">重置</t-button>
				</t-space>
			</t-form-item>
		</t-form>
		<t-table
			row-key="id"
			:loading="loading"
			:data="tableData"
			:columns="columns"
			:pagination="pagination.total > 10 ? pagination : false"
			cell-empty-content="--"
			class="person-table"
			@page-change="onPageChange"
		>
			<template #type="{ row }">
				<span>{{ status_val(row.type) }}</span>
			</template>
			<template #name="{ row }">
				<div style="display: flex; align-items: center">
					<img
						v-if="row.picture_linking"
						:src="row.picture_linking"
						alt=""
						style="width: 35px; height: 35px; margin-right: 8px; border-radius: 50%"
					/>
					<!-- <square-avatar v-if="row.team_logo" :square="row" size="32px" /> -->
					<img v-else src="@/assets/yingyong.png" alt="" style="margin-right: 8px" />
					<span>{{ row.name }}</span>
				</div>
			</template>
			<template #bond_status="{ row }">
				<span>{{ margin_status_val(row.bond_status) }}</span>
			</template>
			<template #deleted_status="{ row }">
				<span>{{ app_status_val(row.deleted_status) }}</span>
			</template>
			<template #actions="{ row }">
				<a href="javascript:;" @click="goDetail(row)">查看</a>
			</template>
		</t-table>
	</t-space>
</template>
<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { useRouter } from 'vue-router';
import { externalApp } from '@/api/advertisement/index';

const loading = ref(false);
const tableData = ref([]);
const advertiseStatus = [
	{
		label: 'App跳转',
		value: 'app',
	},
	{
		label: '网页H5跳转',
		value: 'h5',
	},
	{
		label: '微信小程序',
		value: 'mini_program',
	},
	{
		label: '微信公众号',
		value: 'wechat_official',
	},
];

// 保证金状态枚举
const bond_status = [
	{
		label: '无需支付',
		value: 0,
	},
	{
		label: '待支付',
		value: 1,
	},
	{
		label: '已支付',
		value: 2,
	},
	{
		label: '已退款',
		value: 3,
	},
];

// 应用状态枚举
const deleted_status = [
	{
		label: '正常',
		value: 1,
	},
	{
		label: '已删除',
		value: 2,
	},
];
const columns = [
	{ title: '应用类型', colKey: 'type', width: 150, align: 'left' },
	{ title: '应用名称', colKey: 'name', width: 150, align: 'left' },
	{ title: '发布组织', colKey: 'team_name', width: 200, align: 'left' },
	{ title: '保证金', colKey: 'bond_status', width: 120, align: 'left' },
	{ title: '状态', colKey: 'deleted_status', width: 100, align: 'left' },
	{ title: '创建时间', colKey: 'created_at', width: 150, align: 'left' },
	{ title: '操作', colKey: 'actions', width: 150, align: 'left' },
];

const pagination = {
	current: 1,
	pageSize: 10,
	total: 0,
	showJumper: true,
	onChange: (pageInfo) => {
		console.log('pagination.onChange', pageInfo);
		pagination.current = pageInfo.current;
		pagination.pageSize = pageInfo.pageSize;

		getList();
	},
};
const formData = ref({
	type: undefined,
	name: '',
	team_name: '',
	created_at: '',
	bond_status: undefined,
	deleted_status: undefined,
});
const dateRange = ref([]);

const status_val = (status) => {
	switch (status) {
		case 'app':
			return 'App跳转';
		case 'h5':
			return '网页H5跳转';
		case 'mini_program':
			return '微信小程序';
		case 'wechat_official':
			return '微信公众号';
		default:
			return '--';
	}
};

// 保证金状态转换
const margin_status_val = (status) => {
	switch (status) {
		case 0:
			return '无需支付';
		case 1:
			return '待支付';
		case 2:
			return '已支付';
		case 3:
			return '已退款';
		default:
			return '--';
	}
};

// 应用状态转换
const app_status_val = (status) => {
	switch (status) {
		case 1:
			return '正常';
		case 2:
			return '已删除';
		default:
			return '--';
	}
};

onMounted(() => {
	getList();
});

const onDateChange = (val: any) => {
	formData.value.created_at = '';
	if (val[0]) {
		formData.value.created_at += `${val[0]},${val[1]}`;
	}
};

const onReset = () => {
	dateRange.value = [];
	formData.value = {
		type: undefined,
		name: '',
		team_name: '',
		created_at: '',
		bond_status: undefined,
		deleted_status: undefined,
	};
	pagination.current = 1;
	getList();
};
const onSearch = () => {
	pagination.current = 1;
	getList();
};

const onPageChange = (pageInfo: any) => {
	console.log('onPageChange', pageInfo);
	pagination.current = pageInfo.current;
	pagination.pageSize = pageInfo.pageSize;
};

const getList = () => {
	loading.value = true;
	const params = {
		pageSize: pagination.pageSize,
		page: pagination.current,
		...formData.value,
	};
	externalApp(params).then((res) => {
		console.log(res);
		if (res.data.code === 0) {
			loading.value = false;
			tableData.value = res.data.data.items;
			pagination.total = res.data.data.total;
			tableData.value.forEach((item) => {
				item.avatar = item.picture_linking;
			});
		} else {
			MessagePlugin.error(res.data.message);
		}
	});
};

const router = useRouter();
const goDetail = (row: any) => {
	router.push({
		name: 'externalApplication-detail',
		query: { id: row.app_id },
	});
};
</script>
<style lang="less" scoped>
.page-container {
	background-color: #fff;
	padding: 16px 24px;
	display: flex;
	flex-direction: column;
	height: calc(100% - 30px);
}
.head-title {
	height: 24px;
	font-size: 16px;
	font-weight: 700;
	color: #13161b;
	line-height: 24px;
}
.twelve-name {
	width: 180px;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}
</style>
