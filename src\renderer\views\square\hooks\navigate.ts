import { useRouter } from 'vue-router';
import to from 'await-to-js';
import { inject } from 'vue';
import omit from 'lodash/omit';
import { useSquareStore } from '@/views/square/store/square';
import { i18n } from '@/i18n';
import { SquareType } from '@/api/square/enums';
import { getSquareInfo } from '@/api/square/home';
import { POST_IS_FROM_OUTER, POST_IS_PREVIEW } from '@/views/square/constant';
import { useTabsStore } from '@/components/page-header/store';
import { goSquareModule } from '@/views/square/utils/business';
import { eventBus } from '@/utils/eventBus';

export default function useNavigate({ fromOuter = false } = {}) {
  const router = useRouter();
  const store = useSquareStore();
  const tabStore = useTabsStore();
  // @ts-ignore
  const t = i18n.global.t;

  const isPreview = inject(POST_IS_PREVIEW, false);
  const isFromOuter = inject(POST_IS_FROM_OUTER, false) || fromOuter;

  /**
   * 跳广场主页
   * @param square 广场号信息
   * @param allowSelfRedirect 是否允许跳转到个人广场号
   */
  const goHomePage = (
    square: Partial<{ squareId: string; name: string }>,
    allowSelfRedirect = true,
    options: {
      query: Record<string, any>,
    } = { query: {} },
  ) => {
    // 组织广场中，禁用点击头像、名称跳转事件
    // if (!store.isPersonal && !isFromOuter) return;

    if (isPreview || isFromOuter) {
      goSquareModule({ redirect: '/square/info', id: square.squareId, needShowDialog: true });
      return;
    }

    const { query } = options;
    const queryStr = query ? new URLSearchParams(query).toString() : '';

    const { squareId, name } = square;
    const isSelf = store.squareInfo?.square?.squareId === squareId;
    setTimeout(() => {
      eventBus.emit('square:page-params-update', {
        sender: 'SquareNavigate',
        payload: {
          action: 'update_params',
          squareId,
          query,
        },
      });
    }, 1000);

    if (allowSelfRedirect && isSelf) {
      router.push(`/square/homepage?id=${squareId}&${queryStr}`);
      tabStore.activeIndex = 0;
      return;
    }

    const fullPath = `/square/info?id=${squareId}&${queryStr}`;
    tabStore.addTab({
      label: (name || '').concat(t('square.route.home')),
      fullPath,
    });
    router.push(fullPath);
    store.activeMenuIdx = 0;
  };

  // 跳转到服务
  const goServicePage = (squareId, name, teamId) => {
    const fullPath = `/square/shome/service?id=${squareId}&teamId=${teamId}`;
    tabStore.addTab({
      label: name ? `${name}的服务` : t('square.square.home'),
      fullPath,
    });
    router.push(fullPath);
  };

  // 跳转到时光相册
  const goAlbumPage = (squareId) => {
    const fullPath = `/square/friend-ablum/album-index?squareId=${squareId}`;
    tabStore.addTab({
      label: t('square.route.album'),
      fullPath,
      icon: 'photoalbum',
    });
    router.push(fullPath);
  };

  // 跳模板页
  const goTemplate = () => {
    tabStore.addTab({
      label: t('square.page.tplLib'),
      fullPath: '/assemble/template',
    });
    router.push('/assemble/template');
  };

  // 返回广场主页
  const backIndex = (route) => {
    tabStore.removeTab(route.fullPath);
    tabStore.activeIndex = 0;

    let path = store.homePage;
    // 回到当前的选中菜单
    if (store.activeMenu) path = store.activeMenu.fullPath;
    router.replace(path);
  };

  const redirectToSquareHome = async (id: string, query: Record<string, any>, options: Record<string, any> = {}) => {
    // 未开通广场号
    if (id === '0') {
      await store.getIndividualInfo();
      return;
    }

    // 如果是组织，先切换到个人
    if (!store.isPersonal && !options.skipSwitchToPersonal) {
      await store.getIndividualInfo();

      const tab = tabStore.tabs[0];
      if (tab?.fullPath === '/square/publish-records') {
        tab.fullPath = '/square/friend-circle';
      }
    }

    const [err, res] = await to(getSquareInfo({ square_id: id }));
    if (err) return;
    const { individualProfile, individualSetting, square } = res.data.info;

    // 个人广场号更新广场信息（TODO）
    if (store.isSelfSquare(+id)) {
      const isIndividual = square.squareType === SquareType.Individual;
      store.squareInfo.square = square;
      if (isIndividual) {
        store.squareInfo.individualProfile = individualProfile;
        store.squareInfo.individualSetting = individualSetting;
      }
    }

    goHomePage({ squareId: id, name: square.name }, true, { query });
  };

  /**
   * 从外部跳转过来，如身份卡
   * @param query 跳转参数
   * @param query.toOrg 是否跳转到组织广场
   * @param query.squareId 广场号
   * @param query.teamId 组织ID
   * @param query.needShowDialog 是否需要显示确认对话框
   * @param query.skipSwitchToPersonal 是否跳过切换到个人广场号
   * @param switchAccount 切换账号回调
   */
  const redirectFromOuter = async (query, switchAccount?) => {
    if (!query || query.from !== 'outer') return;
    const { id, redirect, toOrg, squareId, teamId, needShowDialog, skipSwitchToPersonal } = query;
    // if (!redirect) return;

    let currentSquare = null;
    if (needShowDialog) {
      await store.getSquaresList();
      currentSquare = store.squareSelected.square;
    }

    // const isPersonal = currentSquare?.squareType === SquareType.Individual;
    // 如果没有当前选中的广场，或者广场为个人广场号直接跳转主页详情
    const directly = !currentSquare;

    const homeQuery = omit(query, ['from', 'id', 'redirect', 't', 'toOrg']);

    // 到广场首页
    if (id && redirect === '/square/info') {
      if (directly) {
        await redirectToSquareHome(id as string, homeQuery, { skipSwitchToPersonal: skipSwitchToPersonal === 'true' });
      } else {
        // 当为组织广场号并且确认跳转后，进行主页详情的跳转
        const item = store.squareList.find((v) => v.square.squareType === SquareType.Individual);
        item && switchAccount?.({ id: item.square.squareId, extra: item }, true, async () => {
          await redirectToSquareHome(id as string, homeQuery);
        });
      }
      return;
    }

    const go = async () => {
      await router.replace(`${redirect}?${new URLSearchParams(query as Record<string, string>).toString()}`);

      // 相册详情页面的tab跳转路径设置为对应相册首页
      const detailToIndex = {
        '/square/friend-ablum/ablum-detail': '/square/friend-ablum/album-index',
        '/square/phone-album/album-detail': '/square/phone-album/time-line',
      };
      Object.keys(detailToIndex).forEach((path) => {
        if (path === redirect) {
          tabStore.addTab({
            label: t('square.route.album'),
            fullPath: `${detailToIndex[path]}?id=${id}&squareId=${squareId}`,
          });
        }
      });
    };

    // 切换到组织广场
    if (toOrg === 'true') {
      await store.getSquaresList();

      let squareId = id;
      if (teamId) {
        const item = store.squareList.find((v) => v.square.originId === teamId);
        if (item) squareId = item.square.squareId;
      }

      const item = store.squareList.find((v) => v.square.squareId === squareId);
      switchAccount?.({ id, extra: item });
      return;
    }

    if (directly && redirect) {
      go();
    } else {
      // 当为组织广场号并且确认跳转后
      const item = store.squareList.find((v) => v.square.squareType === SquareType.Individual);
      item && switchAccount?.({ id: item.square.squareId, extra: item }, true, async () => {
        go();
      });
    }
  };

  return {
    goHomePage,
    goTemplate,
    goServicePage,
    goAlbumPage,
    backIndex,
    redirectFromOuter,
  };
}
