{"npmRebuild": true, "asar": true, "extraFiles": [], "publish": [{"provider": "generic", "url": "https://dl.ringkol.com/"}], "afterPack": ".electron-vite/afterPack.js", "beforePack": ".electron-vite/beforePack.js", "afterSign": ".electron-vite/afterSign.js", "afterAllArtifactBuild": ".electron-vite/afterAllArtifactBuild.js", "productName": "另可", "appId": "com.xht.macringkol", "directories": {"output": "build/${version}/${os}/${arch}", "buildResources": "icons"}, "extraResources": [{"from": "./src/renderer/assets/account/<EMAIL>", "to": "./<EMAIL>"}, {"from": "./src/renderer/assets/account/docker.png", "to": "./docker.png"}], "asarUnpack": "node_modules/better-sqlite3/**/*", "files": ["dist/electron/**/*"], "dmg": {"sign": false, "contents": [{"x": 410, "y": 150, "type": "link", "path": "/Applications"}, {"x": 130, "y": 150, "type": "file"}]}, "mac": {"artifactName": "ringkol_mac_${arch}.${ext}", "icon": "icons/icon.icns", "entitlements": "entitlements.mac.plist", "entitlementsInherit": "entitlements.mac.plist", "hardenedRuntime": true, "gatekeeperAssess": false, "target": {"target": "default", "arch": ["universal"]}, "extendInfo": {"CFBundleURLSchemes": ["ringkol"], "NSMicrophoneUsageDescription": "请允许本程序访问您的麦克风", "NSCameraUsageDescription": "请允许本程序访问您的摄像头"}}, "win": {"artifactName": "ringkol_win.${ext}", "icon": "icons/icon.png", "target": "nsis"}, "linux": {"target": "deb", "icon": "build/icons"}, "nsis": {"oneClick": false, "perMachine": true, "allowElevation": true, "allowToChangeInstallationDirectory": true, "deleteAppDataOnUninstall": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "include": ".electron-vite/urlProtocol.nsh", "guid": "com.xht.macringkol"}, "protocols": [{"name": "ringkol", "schemes": ["ringkol"]}]}