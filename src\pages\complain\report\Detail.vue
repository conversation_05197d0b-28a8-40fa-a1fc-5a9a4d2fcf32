<template>
	<div class="page-container-info">
		<div class="tab-wrap">
			<div
				v-for="item in tabList"
				:key="item.value"
				class="tab-item"
				:class="{ active: currentTab === item.value }"
				@click="onTabChange(item)"
			>
				{{ item.label }}
			</div>
		</div>

		<div v-loading="loading" class="right-content">
			<div ref="anchor1" class="title inline-block">
				投诉举报内容 <span v-if="formData.status === 1">（处理中）</span><span v-else>（已处理）</span>
			</div>
			<t-divider />

			<div ref="anchor1" />
			<div class="title">举报信息</div>
			<t-form label-align="top" disabled>
				<t-row :gutter="20">
					<t-col :span="4" class="mb-8">
						<t-form-item label="工单编号">
							<t-input :value="formData.no" />
						</t-form-item>
					</t-col>
					<t-col :span="4" class="mb-8">
						<t-form-item label="提交时间">
							<t-input :value="dayjs(formData.created_at).format(DATE_TIME_FORMAT)" />
						</t-form-item>
					</t-col>
					<t-col :span="4" class="mb-8">
						<t-form-item label="举报类型">
							<t-input :value="reportLabel(formData.type)" />
						</t-form-item>
					</t-col>

					<t-col :span="4" class="mb-8">
						<t-form-item label="举报来源">
							<t-input :value="related_sources_string[formData.related_sources || 0]" />
						</t-form-item>
					</t-col>
					<t-col :span="4" class="mb-8">
						<t-form-item label="被举报人/群名称">
							<t-input v-model="formData.related_name" />
						</t-form-item>
					</t-col>
					<t-col :span="4" class="mb-8">
						<t-form-item label="被举报人openid/群id">
							<t-input v-model="formData.related_key" />
						</t-form-item>
					</t-col>

					<t-col :span="4" class="mb-8">
						<t-form-item label="被举报人另可Id">
							<t-input v-model="formData.reported_rkid" />
						</t-form-item>
					</t-col>

					<t-col :span="8" class="mb-8">
						<t-form-item label="原因描述">
							<t-textarea v-model="formData.remark" :autosize="{ maxRows: 2 }" />
						</t-form-item>
					</t-col>
					<t-col :span="12" class="mb-8">
						<t-form-item label="图片凭证">
							<t-image-viewer
								v-model:visible="visible"
								v-model:index="viewerIndex"
								:default-index="0"
								:images="formData.image?.map((item) => item.file_url)"
							>
								<template #trigger>
									<t-image
										v-for="(src, index) in formData.image"
										:key="index"
										:src="src.file_url"
										fit="cover"
										class="pic"
										style="cursor: pointer"
										@click="onOpen(index)"
									>
										<template #error><t-icon name="image-error" class="text-25!" /></template>
									</t-image>
								</template>
							</t-image-viewer>
							<span v-if="!formData.image?.length">--</span>
						</t-form-item>
					</t-col>
				</t-row>
			</t-form>

			<div ref="anchor2" />
			<div class="title">提交人信息</div>
			<t-form label-align="top" disabled>
				<t-row :gutter="20">
					<t-col :span="4" class="mb-8">
						<div style="display: flex; gap: 12px">
							<div>头像</div>
							<kyy-avatar avatar-size="64px" :image-url="formData.reporter_head" :user-name="formData.reporter_name" />
						</div>
					</t-col>
					<t-col :span="4" class="mb-8">
						<t-form-item label="姓名">
							<t-input :value="formData.reporter_name" />
						</t-form-item>
					</t-col>
					<t-col :span="4" class="mb-8">
						<t-form-item label="另可ID">
							<t-input :value="formData.link_id" />
						</t-form-item>
					</t-col>
					<t-col :span="4" class="mb-8">
						<t-form-item label="手机号">
							<t-input :value="formData.reporter_phone" />
						</t-form-item>
					</t-col>
					<t-col :span="4" class="mb-8">
						<t-form-item label="邮箱">
							<t-input :value="formData.reporter_email" />
						</t-form-item>
					</t-col>
				</t-row>
			</t-form>

			<template v-if="formData.action_log?.length">
				<div ref="anchor3" />
				<div class="title">处理记录</div>
				<t-table :data="formData.action_log" :columns="columns" row-key="property"> </t-table>
			</template>

			<div v-if="formData.status === 1" class="footer">
				<t-button theme="primary" class="ml-8" @click="handle">受理</t-button>
			</div>
		</div>

		<audit-dialog
			v-if="rejectVisible"
			v-model="rejectVisible"
			:is-pass="isPass"
			:relate-id="formData.id"
			:audit-type="AuditType.PostReport"
			@confirm="auditConfirm"
		/>
		<handleModal ref="handleModalRef" @handle-ok="auditConfirm" />
	</div>
</template>

<script lang="ts" setup>
import { computed, reactive, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import to from 'await-to-js';
import dayjs from 'dayjs';
import AuditDialog from '@/pages/square/components/AuditDialog.vue';
import { AuditStatus, AuditType } from '@/api/square/models/audit';
import { getReportInfo } from '@/api/square';
import { DATE_TIME_FORMAT, reportAuditStatusHelper } from '@/pages/square/constant';
import { Report } from '@/api/square/models/report';
import KyyAvatar from '@/components/kyy-avatar/index.vue';
import handleModal from './handle-modal.vue';
import { reportDetailGet } from '@/api/complain';

const anchor1 = ref(null);
const anchor2 = ref(null);
const anchor3 = ref(null);
const anchors = {
	anchor1,
	anchor2,
	anchor3,
};
const tabList = ref([
	{ label: '投诉举报内容', value: 'anchor1' },
	{ label: '提交人信息', value: 'anchor2' },
	{ label: '处理记录', value: 'anchor3' },
]);
const related_sources_string = ['--', '通用', '会话', '账号'];

const currentTab = ref('anchor1');
const onTabChange = (item) => {
	currentTab.value = item.value;
	anchors[item.value].value?.scrollIntoView({ behavior: 'smooth' });
};

const formData: any = reactive<
	Report & {
		auditStatusTheme?: string;
		auditStatusText?: string;
	}
>({});

const canAudit = computed(() => formData.auditStatus === AuditStatus.Pending);

const loading = ref(false);
const getInfo = async (id) => {
	loading.value = true;
	const [err, res] = await to(reportDetailGet(id));
	loading.value = false;
	if (err) return;

	const data = res.data?.data?.detail;
	Object.assign(formData, data);
};

const route = useRoute();
watch(
	() => route.query.id,
	(id) => {
		if (!id) return;

		getInfo(id);
		formData.id = id as string;
	},
	{ immediate: true },
);

const rejectVisible = ref(false);
const isPass = ref(false);
const reject = () => {
	rejectVisible.value = true;
	isPass.value = false;
};

const pass = () => {
	rejectVisible.value = true;
	isPass.value = true;
};

const auditTableKey = ref(1);
const auditConfirm = () => {
	getInfo(formData.id);
	auditTableKey.value++;
};

const visible = ref(false);
const viewerIndex = ref(0);
const onOpen = (index) => {
	visible.value = true;
	viewerIndex.value = index;
};

const auData = ref([]);

const columns = ref([
	{
		title: '审核时间',
		colKey: 'created_at',
	},
	{
		title: '操作',
		colKey: 'operate_status_name',
	},
	{
		title: '备注',
		colKey: 'remark',
	},
	{
		title: '审批人',
		colKey: 'operate_openid_name',
	},
]);

const reportLabel = (type) => {
	// eslint-disable-next-line eqeqeq
	const item = violationTypeList.find((item) => item.value == type);
	if (item) {
		return item.label;
	}
	return '--';
};

const violationTypeList = [
	{
		value: '1',
		label: '色情低俗',
		desc: '可能含有展示或隐晦表达淫秽色情、诱惑引导性交友、渲染低级趣味的内容。',
	},
	{
		value: '2',
		label: '违法违规',
		desc: '可能含有管制枪械、刀具、毒品、等违禁品，或涉嫌诈骗、赌博、侵害野生动植物的相关内容。',
	},
	{
		value: '3',
		label: '政治敏感',
		desc: '可能含有非权威媒体发布有关政治的争议，有关国防、外交政策等方面的重大分歧等相关内容',
	},
	{
		value: '4',
		label: '违规营销',
		desc: '可能含有虚假营销、夸张宣传、售卖假冒商品等有关售卖及违规广告的内容。',
	},
	{
		value: '5',
		label: '不实信息',
		desc: '可能含有虚假信息、错误解释社会新闻事件或专业领域知识的内容。',
	},
	{
		value: '6',
		label: '危害人身安全',
		desc: '可能含有宣传自杀/自残场景、教唆他人自杀或其他容易造成人身伤害危险行为的内容。',
	},
	{
		value: '7',
		label: '未成年相关',
		desc: '可能含有未成年人抽烟、喝酒等不文明行为，侵害未成年人身心健康以及未成年人低俗相关内容。',
	},
	{
		value: '8',
		label: '不规范表达问题',
		desc: '可能含有使用表情、英文缩写、谐音、变体字等形式替代规范表述，影响内容理解。',
	},
	{
		value: '9',
		label: '侵犯权益',
		desc: '可能涉及侵犯肖像、隐私、名誉、商标、专利权等违规行为。',
	},
	{
		value: '10',
		label: '其他',
		desc: '包括未提及的违规类型。',
	},
];
const handleModalRef = ref(null);
const handle = () => {
	handleModalRef.value.open(formData.id);
};
</script>

<style scoped lang="less">
.page-container-info {
	display: flex;
	background: #fff;
	border-radius: 4px;
	height: 100%;
	position: relative;
	padding: 20px 24px 20px 220px;
}

.tab-wrap {
	position: fixed;
	top: 130px;
	margin-left: -200px;
}

.tab-item {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 200px;
	height: 40px;
	border: 1px solid #e3e6eb;
	border-radius: 4px;
	font-size: 14px;
	font-weight: 400;
	color: #13161b;
	background-color: #fff;
	margin-bottom: 8px;
	cursor: pointer;
	&.active {
		color: #2069e3;
		background-color: #daecff;
		border-color: #2069e3;
	}
}

.page-container {
	background-color: #fff;
	padding: 16px 24px;
	min-width: 1191px;
}

.head-title {
	height: 24px;
	font-size: 16px;
	font-weight: 700;
	color: #13161b;
	line-height: 24px;
}

.right-content {
	flex: 1;
	padding-left: 20px;
	.title {
		margin: 16px 0;
		font-size: 16px;
		font-family: Microsoft YaHei, Microsoft YaHei-Bold;
		font-weight: 700;
		color: #13161b;
		line-height: 24px;
	}
}

.footer {
	//position: absolute;
	//right: 0;
	//bottom: 0;
	margin-top: 20px;
	height: 60px;
	padding-right: 32px;
	padding-bottom: 12px;
	display: flex;
	align-items: center;
	justify-content: flex-end;
}

.pic {
	width: 60px;
	height: 60px;
	border-radius: 4px;
	margin-right: 8px;
}
</style>
