<template>
  <!-- 原组织端 -->
  <div ref="containerFlets" class="home">
    <!-- v-show="scrolledDistance > 1000" :class="{isOpacity: scrolledDistance > 1200}" -->
    <!-- <div class="backTop cursor" v-show="scrolledDistance > 180" :class="{isOpacity: scrolledDistance > 200}" @click="scrollToTop">
      <iconpark-icon class="iconarrowup" name="icontopinside"></iconpark-icon>

    </div> -->

    <!-- <div class="backTop cursor" :class="{isOpacity: scrolledDistance > 300}"   @click="scrollToTop">
      <iconpark-icon class="iconarrowup" name="icontopinside"></iconpark-icon>
    </div> -->

    <div class="mt-20px box">
      <div class="combine">
        <div class="team-head">
          <div class="logo-box">
            <!-- 单位会员 -->
            <template v-if="currentMemberCard">
              <kyy-avatar
                v-show="currentMemberCard.type === 1"
                class="rd-10"
                :avatar-size="'88px'"
                :image-url="currentMemberCard.logo || ORG_DEFAULT_AVATAR"
                :user-name="currentMemberCard?.name"
                :shape="'circle'"
              />
              <!-- 个人会员 -->
              <kyy-avatar
                v-show="currentMemberCard.type === 2"
                class="rd-10"
                :avatar-size="'88px'"
                :image-url="currentMemberCard.logo"
                :user-name="currentMemberCard?.name"
                :shape="'circle'"
              />
            </template>
            <template v-else>
              <kyy-avatar
                class="rd-10"
                :avatar-size="'88px'"
                :image-url="ORG_DEFAULT_AVATAR"
                :shape="'circle'"
              />
            </template>
          </div>

          <div class="info">
            <template v-if="memberCards.length > 1">
              <t-popup placement="bottom" overlay-inner-class-name="list-popup">
                <div class="name">
                  <span class="line-1 max-w-380">{{ currentMemberCard?.name }}</span>
                  <iconpark-icon
                    name="iconarrowdown"
                    class="iconarrowdown"
                  ></iconpark-icon>
                </div>
                <template #content>
                  <div class="mer-list">
                    <div
                      v-for="men in memberCards"
                      :key="men.id"
                      class="m-item"
                      @click="onChangeCurrentMember(men.id)"
                    >
                      <div
                        class="se"
                        :style="{
                          opacity: men.id === currentMemberCard.id ? 100 : 0
                        }"
                      >
                        <svg
                          width="20"
                          height="20"
                          viewBox="0 0 20 20"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <rect
                            x="2"
                            y="2"
                            width="16"
                            height="16"
                            rx="8"
                            fill="#4D5EFF"
                          />
                          <path
                            d="M8.12117 12.3542L6 10.233L7.43479 8.83941L9.53768 10.9423L13.5 7L14.9046 8.40456L10.9422 12.3469L10.9497 12.3544L10.9289 12.3746C10.8856 12.4167 10.8409 12.4564 10.795 12.4937L10.233 13.054C9.84229 13.4435 9.20995 13.443 8.81985 13.0529L8.12117 12.3542Z"
                            fill="white"
                          />
                        </svg>
                      </div>
                      <div class="av">
                        <!-- :user-name="men.type === 1 ? men?.team_name : men?.name" -->
                        <kyy-avatar
                          v-show="men.type === 1"
                          class="rd-10"
                          :avatar-size="'24px'"
                          :image-url="men.logo || ORG_DEFAULT_AVATAR"
                          :user-name="men?.name"
                          :shape="'circle'"
                        />
                        <kyy-avatar
                          v-show="men.type === 2"
                          class="rd-10"
                          :avatar-size="'24px'"
                          :image-url="men.logo"
                          :user-name="men?.name"
                          :shape="'circle'"
                        />
                      </div>
                      <div class="name">
                        <!-- {{ men.type === 1 ? men?.team_name : men?.name }} -->
                        {{ men?.name }}
                      </div>
                    </div>
                  </div>
                </template>
              </t-popup>
            </template>
            <template v-else>
              <div class="name">
                <span class="line-1 max-w-380">{{ currentMemberCard.name }}</span>
                <!-- <iconpark-icon
                  name="iconarrowdown"
                  class="iconarrowdown"
                ></iconpark-icon> -->
              </div>
            </template>

            <div v-if="currentMemberCard.type === 1" class="dp">
              <span class="line-1 max-w-300px">
                <MyTooltipComp
                  :text="$filters.isPeriodEmpty(currentMemberCard.staff_name)"
                />
              </span>
              <span class="line-1 max-w-120px" style="margin: 0 8px">
                <MyTooltipComp
                  :text="$filters.isPeriodEmpty(currentMemberCard.job)"
                />
              </span>
              <div class="tg">
                {{ currentMemberCard.tag || "--" }}
              </div>
            </div>

            <div :class="'tag' + currentMemberCard.type">
              {{ currentMemberCard.level_name || "--" }}
            </div>
          </div>
          <div
            theme="default"
            class="btn cursor"
            @click="onInviteJobClub"
          >
            <img src="@renderer/assets/member/icon/icon_invite.png" class="svg">
            <span class="text">{{ $t('member.apply_flow.apply_way_23') }}</span>
          </div>
          <div
            v-show="isManage"
            theme="default"
            class="btn_2 cursor ml-24px"
            @click="onSelectPlatform('manage')"
          >
            <t-badge style="line-height: 20px;" :count="store.getAllTeamApplyCount + store.getAllTeamActiveCount" :offset="[8, 5]">
              <img src="@renderer/assets/member/icon/icon_web.png" class="svg">
            </t-badge>
            <span class="text">{{ $t('member.sv17.admin') }}</span>
          </div>
        </div>
        <!-- <div v-show="isManage" class="team-manage cursor" @click="onSelectPlatform('manage')">
          <t-badge :count="store.getAllTeamApplyCount + store.getAllTeamActiveCount" :offset="[8, 5]">
            <img src="@renderer/assets/member/icon/icon_web.png" class="svg">
          </t-badge>
          <span class="text">进入管理端</span>
        </div> -->

        <img
          v-if="proxy.$i18n.locale === 'zh-cn'"
          src="@renderer/assets/member/icon/entry.png"
          class="entry cursor"
          @click="goLeaf"
        >
        <img
          v-else
          class="entry cursor"
          src="@renderer/assets/member/icon/entry_hk.png"
          @click="goLeaf"
        >

      </div>
      <div class="body">
        <div class="sticky">
          <div class="tabs">
            <div
              class="item"
              :class="{
                active: 3 === currentTab
              }"
              @click="setTab(3)"
            >
              <!-- 会员广场号 -->
              {{ $t('member.sv17.s_1') }}
            </div>
            <div
              class="item"
              :class="{
                active: 4 === currentTab
              }"
              @click="setTab(4)"
            >
              <!-- 会员商机 -->
              {{ $t('member.sv17.s_2') }}

            </div>
            <div
              class="item"
              :class="{
                active: 5 === currentTab
              }"
              @click="setTab(5)"
            >
              <!-- 会员名录 -->
              {{ $t('member.sv17.s_3') }}

            </div>
            <div
              class="item"
              :class="{
                active: 7 === currentTab
              }"
              @click="setTab(7)"
            >
            {{$t("member.syd.a")}}

            </div>

            <div
              class="item"
              :class="{
                active: 6 === currentTab
              }"
              @click="setTab(6)"
            >
              <!-- 会员活动 -->
              {{ $t('member.sv17.s_10') }}
            </div>

            <div
              class="item"
              :class="{
                active: 1 === currentTab
              }"
              @click="setTab(1)"
            >
              <!-- 会员资料 -->
              {{ $t('member.sv17.s_4') }}
            </div>
            <div
              v-if="
                !(currentMemberCard?.is_contact || currentMemberCard?.type === 2)
              "
              class="item"
              :class="{
                active: 2 === currentTab
              }"
              @click="setTab(2)"
            >
              <!-- 联系人 -->
              {{ $t('member.sv17.s_5') }}

            </div>
            <!-- <div class="act-tag" :class="'act-' + currentTab"></div> -->
          </div>
        </div>

        <div v-if="currentTab === 1" class="comp-box">
          <t-loading
            size="medium"
            class="memberLoading"
            :loading="isLoading"
            show-overlay
            text="加载中..."
          >
            <div v-if="resData" class="regular mb-80px">
              <LookRegularComp ref="lookRegularModalRef" />
            </div>
          </t-loading>
          <template v-if="isNetworkError && !resData">
            <div class="noEmpty">
              <Empty name="offline">
                <template #tip>
                  <div class="tipEmpty">
                    <span class="text">网络链接失败，请检查网络后重试</span>
                    <t-button theme="primary" class="btn" @click="onSearch">点击重试</t-button>
                  </div>
                </template>
              </Empty>
            </div>
          </template>
          <div v-if="!isNetworkError" class="edit-btn">
            <div class="cen">
              <t-button
                theme="primary"
                class="b"
                @click="onEditMember(currentMemberCard)"
              >
                <!-- 编辑 -->
                {{ $t('member.sv17.s_6') }}

              </t-button>
            </div>
          </div>
        </div>
        <div v-else-if="currentTab === 2" class="contact-box">
          <contact-comp
            :current-row="currentMemberCard"
            :setting-info="settingInfo"
          />
        </div>
        <div v-else-if="currentTab === 3">
          <square-comp />
        </div>
        <div v-else-if="currentTab === 4">
          <rich-comp />
        </div>
        <div v-else-if="currentTab === 5">
          <name-comp />
        </div>
        <div v-else-if="currentTab === 7">
          <ebook-comp />
        </div>
        <div v-else-if="currentTab === 6">
          <ActiveComp />
        </div>



        <!-- <t-tabs v-if="false" :value="currentTab" @change="onChangeCurrentTab">
          <t-tab-panel :value="1" label="会员资料" :destroy-on-hide="false">
            <LookRegularComp ref="lookRegularModalRef" class="mt-20px" />
            <div class="btn flex-justify-center">
              <t-button
                theme="primary"
                @click="onEditMember(currentMemberCard)"
              >     {{ $t('member.sv17.s_6') }}</t-button>
            </div>
          </t-tab-panel>
          <t-tab-panel
            v-if="
              !(currentMemberCard?.is_contact || currentMemberCard.type === 2)
            "
            :value="2"
            :label="$t('member.regular.contact')"
          >
            <contact-comp
              :current-row="currentMemberCard"
              :setting-info="settingInfo"
            />
          </t-tab-panel>
        </t-tabs> -->
      </div>
    </div>
  </div>


  <!-- @reload="onSearch" -->
  <AddMemberModal
    ref="addMemberModalRef"
    :is-hidden-arr="[
      'memberLevel',
      'joinTime',
      'expireTime',
      'department',
      'relateRespector'
    ]"
    :is-member="1"
    @reload="onInitData"
    @on-show-member-flow="onShowMemberFlow"
  />
  <AddInMemberModal ref="addInMemberModalRef" />
  <InviteQrcodeModal
    ref="inviteQrcodeModalRef"
    :header-text="$t('member.squarek.f')"
    :way-tips="$t('member.squarek.e')"
    :member="currentMemberCard"
  />
</template>

<script lang="ts" setup>
import {
  getMemberApplyLinkAxios,
  getMemberCardsAxios,
  getMemberSettingAxios,
  getRegularDetailAxios,
 checkIsAdminAxios } from "@renderer/api/uni/api/businessApi";
import InviteQrcodeModal from "@renderer/views/uni/member_number/modal/invite-qrcode-modal.vue";
import useRouterHelper from "@renderer/views/square/hooks/routerHelper";
import AddInMemberModal from "@renderer/views/uni/member_home/panel/regular-member-panel/modal/add-in-member-modal.vue";
import Empty from "@renderer/components/common/Empty.vue";
import { getResponseResult } from "@renderer/utils/myUtils";
import { MessagePlugin } from "tdesign-vue-next";
import { ref, onMounted, onActivated, watch, Ref, toRaw, computed, getCurrentInstance, defineAsyncComponent } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useUniStore } from "@renderer/views/uni/store/uni";
import lodash from "lodash";
import LookRegularComp from "@renderer/views/uni/member_number/components/look-regular-comp.vue";
import ContactComp from "@renderer/views/uni/member_number/components/contact-comp.vue";
import SquareComp from "@renderer/views/uni/member_number/components/square-comp.vue";
import ActiveComp from "@renderer/views/uni/member_number/components/active-comp.vue";

import NameComp from "@renderer/views/uni/member_number/components/name-comp.vue";
import EbookComp from "@renderer/views/uni/member_number/components/ebook.vue";
import RichComp from "@renderer/views/uni/member_number/components/rich-comp.vue";


import AddMemberModal from "@renderer/views/uni/member_home/panel/regular-member-panel/modal/add-member-modal.vue";
import MyTooltipComp from "@renderer/components/engineer/components/MyTooltipComp.vue";
import { getProfilesInfo } from '@renderer/utils/auth';
import { getUniTeamID } from "@renderer/views/uni/utils/auth";
import KyyAvatar from "@/components/kyy-avatar/index.vue";
import { ORG_DEFAULT_AVATAR } from "@/views/square/constant";

// const RichComp = defineAsyncComponent(() => import("@renderer/views/uni/member_number/components/rich-comp.vue"));


const { menuList, routeList, roleFilter } = useRouterHelper("associationIndex");

const router = useRouter();
const { proxy } = getCurrentInstance() as any;


const currentTab = ref(3); // 1 会员资料 2联系人、3会员广场、4会员商机
const memberCards: Ref<any> = ref([]);
const currentMemberCard: Ref<any> = ref(null);
const store = useUniStore();
const profile = getProfilesInfo();

const isAdminValue = ref(null);
// 判断当前用户是否为管理员
const onCheckIsAdmin = async (idStaff) => {
  let res: any = null;
  try {
    res = await checkIsAdminAxios({ idStaff });
    res = getResponseResult(res);
    if (!res) return;

    isAdminValue.value = res.data;
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    if (errMsg === 'Network Error') {

    } else {
      MessagePlugin.error(errMsg);
    }
  }
};


// const scrolledDistance = ref(0); // 滚动距离
// const containerFlets = ref(null);
// const handleScroll = (event) => {
//   console.log(event.target.scrollTop, 'e');
//   scrolledDistance.value = event.target.scrollTop;
//   console.log(scrolledDistance.value, 'few')
//   // scrolledDistance.value = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop;
// };
// let animationId = null;
// const scrollToTop = () => {
//   console.log(containerFlets.value.scrollTop);
//   // containerFlets.value.scrollTop = 0; // 滚动容器到顶部
//   cancelAnimationFrame(animationId); // 取消之前的动画

//       const scrollTop = containerFlets.value.scrollTop;
//       console.log(containerFlets.value.scrollTop);
//       const step = Math.ceil(scrollTop / 6); // 每帧滚动的步长
//       console.log(step);
//       const animate = () => {
//         if (containerFlets.value.scrollTop > 0) {
//           containerFlets.value.scrollTop -= step;
//           animationId = requestAnimationFrame(animate); // 请求下一帧动画
//         } else {
//           cancelAnimationFrame(animationId); // 动画结束，取消请求
//         }
//       };

//       animationId = requestAnimationFrame(animate); // 开始动画
// };



const goLeaf = () => {
  // router.push({
    //   path: "/memberIndex/member_manage",
    //   query: {
    //     // projectId: props.projectId
    //   }
    // });
    const searchMenu = routeList.find((v) => v.name === "member_leaflets");
    // store.addTab(routeList.filter((v) => v.affix).filter(roleFilter));
    router.push({ path: searchMenu.fullPath, query: { } });
    store.addTab(toRaw(searchMenu));
};

const isManage = computed(() => store.activeAccount &&
        (isAdminValue.value?.super ||
          isAdminValue.value?.isAdmin ||
          isAdminValue.value?.superAdmin));


const emits = defineEmits(['selectPlatform']);
const onSelectPlatform = (type) => {
  selectApp(type);
  emits('selectPlatform', type);
};

const selectApp = (type) => {
  const arr = store.getStorePlatforms;
  const result = arr.find((v) => v.openId === profile.openid && v.teamId === store.activeAccount?.teamId);
  if (result) {
    result.apply = type;

  } else {
    arr.push({ openId: profile.openid, teamId: store.activeAccount?.teamId, apply: type });
  }
  store.setStorePlatforms(arr);
};

const onChangeCurrentMember = (e) => {
  const result = memberCards.value.find((v) => v.id === e);
  console.log(e, result, memberCards.value, '内容');
  setMemberOraganizetion(e);
  if (result) {
    currentMemberCard.value = result;
    // onLookDetail(result);
    onSearch();
  }
};

const memberOraganizetion = ref({});
const localStorageMemberOraganizetion = () => window.localStorage.getItem('memberOraganizetion');
const setMemberOraganizetion = (e) => {
  if (!localStorageMemberOraganizetion()) {
    memberOraganizetion[store.activeAccount.idTeam] = e;
    console.log(memberOraganizetion, 'store.activeAccount');
    window.localStorage.setItem('memberOraganizetion', JSON.stringify(memberOraganizetion.value));
  }
  const a = JSON.parse(localStorageMemberOraganizetion());
  a[store.activeAccount.idTeam] = e;
  window.localStorage.setItem('memberOraganizetion', JSON.stringify(a));




};

// 邀请入会
const inviteQrcodeModalRef = ref(null);
const onInviteJobClub = () => {
  getInviteLinkAxios().then((val) => {
    inviteQrcodeModalRef.value.onOpen(val);
  });
};

// 获取邀请链接
const getInviteLinkAxios = () => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await getMemberApplyLinkAxios();
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      resolve(result.data.link);
    } catch (error) {
      reject();
      const errMsg = error instanceof Error ? error.message : error;
      if (errMsg !== 'Network Error') MessagePlugin.error(errMsg);
    }
  });
};

const onSearch = () => {
  onLookDetail(toRaw(currentMemberCard.value));
};

const onChangeCurrentTab = (e) => {
  console.log(e);
  currentTab.value = e;

  if (e === 1) {
    onSearch();
  }
};
const addMemberModalRef = ref(null);
// 编辑会员
const onEditMember = (row) => {
  onGetRegularDetailAxios(row).then((res) => {
    addMemberModalRef.value.onOpen(res);
  });
};

const lookRegularModalRef = ref(null);
const settingInfo = ref(null);
const resData = ref(null); // 用来判定有无数据

const isNetworkError = ref(false);
const isLoading = ref(false);
const onLookDetail = (row) => {
  // onGetRegularDetailAxios(row).then((res) => {
  //   console.log(res);
  //   lookRegularModalRef.value.onOpen(res);
  // });
  resData.value = null;
  console.log(row);
  // 缓存信息存储
  const caches = store.getStorageDatas;
  const cache = caches.find((v) => v.teamId === getUniTeamID());
  if (!cache) {
    isLoading.value = true;

  }
  Promise.all([onGetRegularDetailAxios(row), onGetMemberSetting()]).then(
    (res) => {
      console.log(res);
      resData.value = res;
      isLoading.value = false;

      isNetworkError.value = false;

      // 缓存处理 start
      const memberMeterials = {
        items: res || [],
      };
      if (cache) {
        cache.memberMeterials = memberMeterials;
      } else {
        caches.push({ teamId: getUniTeamID(), memberMeterials });
      }
      // 缓存处理end

      settingInfo.value = res[1];
      // 这里要座一层逻辑
      let detailItem: any = res[0];
      // 激活状态，1：已激活，2：未激活
      if (detailItem.activate === 2) {
        // 未激活，读取data数据
        detailItem.submit_data = lodash.cloneDeep(detailItem.data);
        console.log(detailItem.data);
        res[0] = detailItem;
      }
      lookRegularModalRef.value?.onClose();
      setTimeout(() => {
        lookRegularModalRef.value?.onOpen(res);
      });
    }
  ).catch((error) => {
    console.log('memberInfo: ', error);
    isLoading.value = false;

    if (error === 'Network Error') {
      isNetworkError.value = true;
      if (!cache) { resData.value = null; return; }
      const res = cache.memberMeterials?.items || [];
      if (res.length < 1) {
        resData.value = null;
        return;
      }
      resData.value = res;
      settingInfo.value = res[1];

      // 这里要座一层逻辑
      let detailItem: any = res[0];
      // 激活状态，1：已激活，2：未激活
      if (detailItem.activate === 2) {
        // 未激活，读取data数据
        detailItem.submit_data = lodash.cloneDeep(detailItem.data);
        console.log(detailItem.data);
        res[0] = detailItem;
      }
      lookRegularModalRef.value?.onClose();
      setTimeout(() => {
        lookRegularModalRef.value?.onOpen(res);
      });

    }
  });
};


const addInMemberModalRef = ref(null);
const onShowMemberFlow = () => {
  console.log('点击了吗');
  addInMemberModalRef.value?.onOpen();
};


const onGetRegularDetailAxios = async (row) => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {

      result = await getRegularDetailAxios(row?.id);
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      resolve(result.data);
    } catch (error) {
      const errMsg = error instanceof Error ? error.message : error;
      // MessagePlugin.error(errMsg);
      reject(errMsg);
    }
  });
};

// 获取设置
const onGetMemberSetting = async () => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await getMemberSettingAxios();
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      resolve(result.data);
    } catch (error) {
      const errMsg = error instanceof Error ? error.message : error;
      // MessagePlugin.error(errMsg);
      reject(errMsg);
    }
  });
};

const onGetMemberCardsAxios = () => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await getMemberCardsAxios();
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      resolve(result.data);
    } catch (error) {
      reject();
      const errMsg = error instanceof Error ? error.message : error;
      if (errMsg !== 'Network Error') MessagePlugin.error(errMsg);
    }
  });
};
const onInitData = () => {
  onGetMemberCardsAxios().then(
    (res: any) => {
      if (res && res.length > 0) {
        memberCards.value = res.map((v) => {
          // v.label = v.type === 1 ? v.team_name : v.name;
          v.label = v.name;
          return v;
        });
        console.log(res);
        if (currentMemberCard.value) {
          console.log(currentMemberCard.value);
          currentMemberCard.value = memberCards.value.find(
            (v) => v.id === currentMemberCard.value?.id
          );
          console.log(currentMemberCard.value);
        } else {
          currentMemberCard.value = res[0];
          console.log(currentMemberCard.value);
        }
        console.log(currentMemberCard.value);
        // currentMemberCard.value = currentMemberCard.value
        //   ? currentMemberCard.value
        //   : res[0];
        // onLookDetail(res[0]);


        // 这段代码绑定切换组织 pzy这个傻逼写的 别删
        if (localStorageMemberOraganizetion()) {
          const a = JSON.parse(localStorageMemberOraganizetion());
          for (let aKey in a) {
            if (store.activeAccount.idTeam == aKey) {
              console.log('执行了吗', a[aKey], memberCards.value);
              onChangeCurrentMember(Number(a[aKey]));
            }
          }
        }
        onSearch();
      } else {
        currentMemberCard.value = null;
      }
    },
    (err) => {
      currentMemberCard.value = null;
    }
  );
};
onMounted(() => {
  // containerFlets.value?.addEventListener('scroll', handleScroll); // 监听滚动事件

});

const setTab = (index) => {
  currentTab.value = index;
  if (index === 1) {
    onSearch();
  }
};


watch(
  () => store.activeAccount,
  (val) => {
    if (val) {
      currentMemberCard.value = null;
      setTimeout(async () => {
        // onCheckIsAdmin(val?.staffId);
        await onInitData();

      });

      setTimeout(() => {
        onCheckIsAdmin(val?.staffId);
      });

      console.log('内容，store.activeAccount', val, localStorageMemberOraganizetion());
    }
  },
  {
    deep: true,
    immediate: true
  }
);
</script>

<style lang="less" scoped>
// :deep(.t-avatar-fonts2){
//   font-size: 20px !important;
// }

.home {
  max-width: 1152px;
  min-width: 1088px;
  margin: 0 32px;
  width: 100%;


  .box {

    width: 100%;
    min-height: calc(100% - 40px);
    background-color: #fff;
    display: flex;
    // align-items: center;
    // justify-content: center;
    // font-size: 20px;
    flex-direction: column;

    .header {
      background: #fff;
      display: flex;
      justify-content: space-between;
      padding-bottom: 10px;

      &-left {
        display: flex;
        gap: 10px;
        .column {
          display: flex;
          flex-direction: column;
          gap: 6px;
          .tag {
            padding: 0 10px;
            border: 1px solid red;
            width: fit-content;
          }
          .name {
            display: flex;
            align-items: center;
            gap: 10px;
            .tip {
              padding: 0 6px;
              border: 1px solid #d6d6d6;
            }
          }
        }
      }
    }
    .body {
      background: #fff;
      height: 100%;

    }
  }
}
.combine {
  display: flex;
  gap: 12px;
  .team-manage {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    flex: none;
    width: 136px;
    border-radius: 8px;
    background: linear-gradient(315deg, #A1D7FE -62.86%, #EAF5FF 75.49%);
    .text {
      color: var(--text-kyy-color-text-1, #1A2139);
      text-align: center;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
    }
    .svg {
      width: 40px;
      height: 40px;
    }
  }
}
.team-head {
  flex: 1;
  display: flex;
  width: 100%;
  padding: 24px;
  background: #fff;
  align-items: center;
  background-image: url('@renderer/assets/member/icon/vip_img_bg.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  object-fit: cover;
  height: 136px;

  .logo-box {
    width: 88px;
    height: 88px;
    margin-right: 16px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .info {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    gap: 8px;
    flex: 1 0 0;
    // width: 510px;
    .name {
      color: var(--text-kyy-color-text-1, #1a2139);
      display: flex;
      align-items: center;
      font-family: PingFang SC;
      font-size: 18px;
      font-style: normal;
      font-weight: 600;
      line-height: 26px;
      cursor: pointer;
      .iconarrowdown {
        color: #828da5;
        font-size: 18px;
        margin-left: 2px;
      }
    }

    .dp {
      color: var(--text-kyy-color-text-3, #828da5);

      /* kyy_fontSize_2/regular */
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
      display: flex;
      .tg {
        display: flex;
        height: 20px;
        min-height: 20px;
        max-height: 20px;
        padding: 0px 4px;
        justify-content: center;
        align-items: center;
        gap: 4px;
        border-radius: var(--kyy_radius_tag_s, 4px);
        background: var(--kyy_color_tag_bg_gray, #eceff5);
        // color: var(--kyy_color_tag_text_gray, #516082);
        color: var(--kyy_color_tag_text_brand, #4D5EFF);
        text-align: center;
        font-family: PingFang SC;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px; /* 166.667% */
      }
    }
    .tag1 {
      display: flex;
      height: 24px;
      min-height: 24px;
      max-height: 24px;
      padding: 0px 8px;
      justify-content: center;
      align-items: center;
      gap: 4px;
      color: var(--kyy_color_tag_text_purple, #CA48EB);
      text-align: center;
      border-radius: var(--kyy_radius_tag_s, 4px);
      background: var(--kyy_color_tag_bg_purple, #FAEDFD);
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: 22px; /* 157.143% */
    }
    .tag2 {
      display: flex;
      height: 24px;
      min-height: 24px;
      max-height: 24px;
      padding: 0px 8px;
      justify-content: center;
      align-items: center;
      gap: 4px;
      color: var(--kyy_color_tag_text_purple, #ca48eb);
      text-align: center;
      border-radius: var(--kyy_radius_tag_s, 4px);
      background: var(--kyy_color_tag_bg_purple, #faedfd);
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: 22px; /* 157.143% */
    }
  }
  .btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    gap: 8px;
    // padding-left: 24px;
    padding: 0 10px;

    position: relative;
    transition: all 0.15s linear ;

    &:hover {
      border-radius: 8px;
      background: var(--bg-kyy_color_bgBrand_hover, #EAECFF);
      transition: all 0.15s linear ;
    }
    &::before {
      content: " ";
      position: absolute;
      width: 1px;
      top: 0;
      bottom: 0;
      height: 88px;
      margin: auto;
      background: #fff;
      left: -24px;
    }

    .iconpeopleadd {
      font-size: 24px;
      color: #516082;
    }
    .text {
      color: var(--text-kyy-color-text-1, #1A2139);
      text-align: center;

      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
    }
    .svg {
      width: 32px;
      height: 32px;
    }
  }
  .btn_2 {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: all 0.15s linear ;

    width: 80px;
    height: 80px;
    gap: 3px;
    // padding-left: 24px;
    padding: 0 10px;

    position: relative;
    &:hover {
      border-radius: 8px;
      background: var(--bg-kyy_color_bgBrand_hover, #EAECFF);
      transition: all 0.15s linear ;
    }
    .svg {
      width: 32px;
      height: 32px;
    }


    .iconpeopleadd {
      font-size: 24px;
      color: #516082;
    }
    .text {
      color: var(--text-kyy-color-text-1, #1A2139);
      text-align: center;

      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
    }


  }
}

.entry {
  height: 136px;
}

.list-popup {
  .mer-list {
    display: flex;
    width: 267px;
    background: #fff;
    flex-direction: column;
    // align-items: center;
    .m-item {
      display: flex;
      width: 251px;
      height: 32px;
      padding: 8px 12px;
      align-items: center;
      gap: 12px;
      cursor: pointer;
      .se {
        margin-top: 5px;
      }
      .av {
        // margin: 0 12px;
      }
      .name {
        color: var(--lingke-black-90, #1a2139);

        font-family: PingFang SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
        max-width: 154px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}

.sticky {
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: #fff;
}
.tabs {
  display: flex;
  position: relative;
  border-bottom: 1px solid var(--divider-kyy-color-divider-light, #eceff5);
  height: 56px;
  width: 100%;
  align-items: center;
  padding: 0px 24px;
  gap: 44px;
  align-self: stretch;
  margin: 16px 0;
  .item {
    // margin-right: 44px;
    color: var(--text-kyy-color-text-1, #1a2139);
    text-align: center;

    /* kyy_fontSize_3/regular */
    font-family: PingFang SC;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px; /* 150% */
    cursor: pointer;
    :v-deep(.t-badge--circle) {
      right: -3px;
    }
  }
  .active {
    color: var(--brand-kyy-color-brand-default, #4d5eff);

    /* kyy_fontSize_3/bold */
    font-family: PingFang SC;
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px; /* 150% */
    position: relative;
    &:after {
      position: absolute;
      content: " ";
      width: 25px;
      height: 3px;
      flex-shrink: 0;
      border-radius: 1.5px;
      background: var(--brand-kyy-color-brand-default, #4d5eff);

      bottom: -16px;
      left: 0;
      right: 0;
      margin: 0 auto;
    }
  }
}



.act-1 {
  position: absolute;
  bottom: 0px;
  left: 46px;
}
.act-2 {
  position: absolute;
  bottom: 0px;
  left: 139px;
}

.comp-box {
  width: 100%;
  background: #fff;
  margin-bottom: 24px;
  .regular {
    padding: 0 24px;
  }
  .edit-btn {
    position: fixed;
    left: 0;
    right: 0;

    bottom: 0px;
    z-index: 9;
    background-color: #fff;

    display: flex;
    height: 64px;
    padding: 16px 24px;
    justify-content: left;
    align-items: center;
    align-self: stretch;
    border-top: 1px solid var(--divider-kyy-color-divider-light, #eceff5);

    .cen {
      width: 1110px;
      margin: 0 auto;
      .b {
        // margin-left: 40%;
      }
    }

  }
  .btm24 {
    height: 24px;
    width: 100%;
    background: var(--kyy_color_tabbar_bg, #ebf1fc);
    color: #ebf1fc;
  }
}

.contact-box {
  width: 100%;
  background: #fff;
}

:deep(.t-badge--circle) {
  background-color: var(--kyy_color_badge_bg, #FF4AA1);
}



.backTop {
  position: fixed;
  right: 16px;
  bottom: 32px;
  opacity: 0;
  transition:all 0.25s linear;

  z-index: 99;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  width: 64px;
  height: 64px;
  border: 1px solid var(--border-kyy_color_border_default, #D5DBE4);
  background: var(--divider-kyy_color_divider_deep, #D5DBE4);
  .iconarrowup {
    font-size: 30px;
    color: #1A2139;
  }
  .text {
    color: var(--text-kyy_color_text_1, #1A2139);
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px; /* 166.667% */
  }
}
.isOpacity {
  opacity: 1 !important;
  transition:all 0.25s linear;
}
:deep(.t-badge--circle){
  
  padding-right: calc((16px - 8px) / 2);
  padding-left: calc((16px - 8px) / 2);
  min-width: 8px;
  height: 16px;
  background-color: var(--td-error-color);
  line-height: 16px;
}
</style>
