<template>
  <div class="commonly-used-box">
    <!-- -->
    <div class="commonly-box">
      <div class="item-head">
        <div class="head-item-text">
          <!-- <iconpark-icon class="head-item-img" name="iconsetUp"></iconpark-icon> -->
          <iconpark-icon class="head-item-img" name="workshop-gc11ahki"></iconpark-icon>
          <span class="head-item-text-app">应用</span>
        </div>
        <div class="head-item-icon" @click="toSetUp(undefined)">
          <iconpark-icon class="icon-img" name="iconsetUp"></iconpark-icon>
          <span>{{ t("banch.set") }}</span>
        </div>
      </div>
      <div class="item-body-box">
        <div class="item-body" v-for="item in  allAppListLength" :key="item.uuid" @click="openApp(item)">
          <div class="relative">
            <t-badge :count="!dot.includes(item.uuid) ? item.count : 0" class="badge-dian">
              <div class="item-body-img-box">
                <img class="item-body-img"
                  :src="filterImg(item)?.img || item?.external_app?.picture_linking || external_app" />
                <div v-if="item.type === 1" class="item-body-img-type">
                  外部
                </div>
              </div>
            </t-badge>
            <img v-if="dot.includes(item.uuid) && item.count" src="@/assets/redpoint.svg" class="redpoint" />
          </div>
          <span class="item-body-text">{{ filterImg(item)?.name || item?.external_app?.name }}</span>
        </div>
        <div v-if="!menuAll&&allAppList.length>8" class="item-body item-body-all" @click="menuAll=true">
 
          <!-- @click="toSetUp('add')" -->
          <span class="item-body-text-all">查看全部</span>
          <iconpark-icon class="dow-icons" name="iconarrowdownnew"></iconpark-icon>
        </div>
      </div>
    </div>
    <!-- <div class="all-app" v-if="allAppList.length>0">
      <div class="item-head">
        <div class="head-item-text">{{t("im.public.allMember")}}</div>
      </div>
      <div class="item-body-box">
        <div class="item-body" v-for="item in allAppList" :key="item.uuid" @click="openApp(item)">
          <div class="relative">
            <t-badge :count="!dot.includes(item.uuid) ? item.count : 0" class="badge-dian">
              <img :src="filterImg(item)?.img" />
            </t-badge>
            <img v-if="dot.includes(item.uuid) && item.count" src="@/assets/redpoint.svg" class="redpoint" />
          </div>
          <span class="item-body-text">{{ filterImg(item)?.name }}</span>
        </div>
      </div>
    </div> -->
    <DraggableApp @getList="getList" :activationGroupItem="activationGroupItem" ref="draggableAppref"></DraggableApp>

    <!-- 组织认证 -->
    <OrgAuth v-if="orgAuthDetail?.teamId" v-model:visible="orgAuthVisible" :show-type="orgAuthShowType"
      :region="orgAuthDetail.region" :team-id="orgAuthDetail.teamId" :org-type="orgAuthDetail.orgType"
      @success="fetchTeamAuthStatus" />

    <OpenStoreApply v-model="openStoreApplyVisible" :data="storeInfo" :team-id="activationGroupItem.teamId"
      :showOverlay='true' @open-auth="handleOpenAuth" />
    <!-- <AnnualFeeDialog v-if="openVisible" v-model="openVisible" open :team-id="activationGroupItem.teamId"
      :invite-code="''" @success="buySuccess" /> -->

  </div>
</template>
<script setup lang="ts">
  import LynkerSDK from "@renderer/_jssdk";
  import { onMountedOrActivated } from "@renderer/hooks/onMountedOrActivated";
  import DraggableApp from "../components/DraggableApp.vue";
  import { filterImg, navigateToExternalApp, navigateToShop, openAppListFn, navigateToWebview } from "../utils";
  import { ref, computed } from "vue";
  import { MessagePlugin, DialogPlugin } from "tdesign-vue-next";
  import { useI18n } from "vue-i18n";
  import { useRoute, useRouter } from "vue-router";
  import OrgAuth from '@renderer/components/orgAuth/orgAuth.vue';
  // import AnnualFeeDialog from "@/views/square/components/annual-fee/AnnualFeeDialog.vue";
  import OpenStoreApply from "./OpenStoreApply.vue";
  import { useStoreOpen } from "../hooks/useStoreOpen";
  import { adManageAuthByMember } from '@renderer/api/ad';
  import { getMyRole as getMyRoleApi } from '@renderer/api/curtureTourism';
  import { getOpenid } from "@renderer/utils/auth";
  import to from "await-to-js";
  import { getTeamAnnualFee } from "@renderer/api/digital-platform/api/businessApi";
  const dot = ['society-article', 'notice', 'policy'];
  import { checkExpiration, getResponseResult } from "@renderer/utils/myUtils";
  import external_app from "@/assets/bench/external_app.svg";
  interface AppItem {
    uuid: string;
    name?: string;
    count?: number;
  }

  const props = defineProps < {
    activationGroupItem: {
      teamId: string;
    };
    allAppList: AppItem[];
    checkIsAdminData: any;
  } > ();
  let sixMenu = false;
  const emits = defineEmits(["getList"]);
  const menuAll = ref(false);
  const router = useRouter();
  const { t } = useI18n();
  const allAppListLength = computed(() => {
    if (menuAll.value) {
      return props.allAppList;
    }
    return props.allAppList.slice(0, 8);;
  });

  // 店铺开通
  const {
    openStoreApplyVisible,
    orgAuthVisible,
    orgAuthDetail,
    orgAuthShowType,
    storeInfo,
    openStore,
    handleOpenAuth,
    fetchTeamAuthStatus,
  } = useStoreOpen(props.activationGroupItem.teamId);

  // 标志位，防止重复打开店铺页面
  const isStoreOpening = ref(false);
  const canOpenWebview = ['store', 'culture-travel'];

  const getMyRole = async () => {
    const { teamId, cardId } = props.activationGroupItem;
    const openId = getOpenid();
    const [err, res] = await to(getMyRoleApi({
      'me.teamId': teamId,
      'me.cardId': cardId,
      'me.openId': openId,
    }));
    if (err) {
      return false;
    }
    return res.data?.data;
  }

  const storeClick = async (type) => {
    // 防止重复打开
    if (isStoreOpening.value) return;
    isStoreOpening.value = true;

    try {
      if (type === 'store') {
        if (!await openStore()) return;
        navigateToShop();
      } else {
        const myRole = await getMyRole();
        if (!myRole) return;
        const { role, superAdminName } = myRole;
        if (['RoleAdmin', 'RoleSuperAdmin'].includes(role)) {
          navigateToWebview(type);
          return;
        }
        const dialog = DialogPlugin.confirm({
          header: '提示',
          theme: 'info',
          // tNode写法
          body: (h) => h('div', {}, [
            h('div', {}, '你没有当前组织另可文旅的使用权限，请联系另可文旅管理员'),
            h('div', {}, `（${superAdminName}）`),
          ]),
          confirmBtn: '知道了',
          cancelBtn: null,
          onConfirm: () => {
            dialog.destroy();
          },
        });
      }
    } finally {
      // 延迟重置标志位，防止快速重复点击
      setTimeout(() => {
        isStoreOpening.value = false;
      }, 1000);
    }
  }
  const openApp = async (item) => {
    if (item.type === 1) {
      const externalApp = item?.external_app;
      let url = '';
      switch (externalApp.type) {
        case 'app':
          url = externalApp?.share_link;
          break;
        case 'h5':
          url = externalApp?.desktop_link || externalApp?.h5_link;
          break;
        case 'wechat_official':
          url = externalApp?.article_link;
          break;
        case 'mini_program':
          url = '';
          break;
        case 'wechat_mini':
        default:
          url = '';
      }
      if (!url) {
        MessagePlugin.error('该应用暂无分享链接');
        return;
      }
      LynkerSDK.openExternalApp({
        url,
      })
      return;
    }
    // 店铺22
    if (canOpenWebview.includes(item.uuid)) {
      storeClick(item.uuid);
      return;
    }

    if (item.uuid === 'external_app') {
      navigateToExternalApp(item);
      return;
    }

    item.name = filterImg(item).name;
    if (["disk", "activities", "square"].includes(item.uuid)) {
      openAppListFn(item, props.activationGroupItem);
      return;
    }
    if (item.uuid === 'ad-lk') {
      adManageAuthByMember().then((res) => {
        if (res.data.data.ad_lk_super === 1 || res.data.data.manage_auth === 1 || res.data.data.team_auth === 1) {
          router.push(openAppListFn(item, props.activationGroupItem));
        } else {
          const confirmDia = DialogPlugin.alert({
            header: '提示',
            theme: 'info',
            body: '你没有另可广告权限,请联系管理员',
            closeBtn: false,
            confirmBtn: '知道了',
            className: 'delmode',
            onConfirm: async () => {
              confirmDia.hide();
            },
            onClose: () => {
              confirmDia.hide();
            },
          });
        }
      });
      return;
    }
    router.push(openAppListFn(item, props.activationGroupItem));
  };
  const is100W = ref(false);
  const draggableAppref = ref(null);
  const getList = () => {
    emits("getList");
  };
  const toSetUp = (val) => {
    if (val) {

      // draggableAppref.value.openAddWin(props.activationGroupItem.teamId, props.staffAppList);
    } else {
      draggableAppref.value.openWin(props.activationGroupItem.teamId);
    }
  };

  const route = useRoute();
  // 标志位，防止重复处理路由查询参数
  const hasProcessedStoreQuery = ref(false);

  onMountedOrActivated(() => {
    // if (route.query.module === 'ad-lk') {
    //   openApp({
    //     uuid: 'ad-lk',
    //     name: '另可广告',
    //   });
    // }

    // FIXME: 再次定位到数智工厂时不会触发
    if (route.query.module === 'store' && !hasProcessedStoreQuery.value) {
      hasProcessedStoreQuery.value = true;

      openApp({
        uuid: 'store',
        name: '店铺',
      });

      // 清理查询参数，防止重复触发
      const newQuery = { ...route.query };
      delete newQuery.module;
      router.replace({ query: newQuery });
    }
  });
</script>
<style lang="less" scoped>
  .head-item-img {
    width: 20px;
    height: 20px;
    font-size: 20px;
    margin-right: 8px;
  }

  .head-item-text-app {
    font-size: 14px;
    color: #1A2139;
  }

  :deep(.t-badge--dot) {
    width: 10px;
    height: 10px;
  }

  .commonly-used-box {
    /* padding: 16px !important; */
  }

  .commonly-used-box {
    background-color: #fff;
    border-radius: 8px;
    /* padding: 16px 8px 8px; */
    position: relative;
  }

  .item-head {
    display: flex;
    align-items: center;
    padding: 12px;
    border-radius: 8px;
    background: linear-gradient(111deg, #C7E5E2 0.63%, rgba(199, 229, 226, 0.40) 28.93%);
    justify-content: space-between;
  }

  .head-item-text {
    color: #1a2139;
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
    /* 150% */
    display: flex;
    align-items: center;
    position: relative;
    margin-left: 8px;
  }



  .all-app {
    margin-top: 12px;
  }

  .commonly-box {
    padding-bottom: 12px;
    border-bottom: 1px solid #eceff5;
  }

  .item-body-box {
    display: flex;
    flex-wrap: wrap;
    padding: 12px 16px;
    gap: 16px 24px;
  }

  .item-body-box {
    .item-body {
      position: relative;
      display: flex;
      width: 164px;
      padding: 12px 16px 12px 12px;
      align-items: center;
      gap: 8px;
      border-radius: 12px;
      cursor: pointer;
      background: var(--bg-kyy_color_bg_deep, #f5f8fe);

      :deep(.t-badge) {
        height: 36px;
      }

      .redpoint {
        position: absolute;
        width: 16px;
        height: 16px;
        top: -7px;
        right: -4px;
      }

      img {
        width: 36px;
        height: 36px;
      }

      .item-body-text {
        color: #000;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        /* 157.143% */
        margin-left: 8px;
      }
    }
  }

  .head-item-icon:hover {
    width: 68px;
    height: 24px;
    padding: 0 4px 0 8px;
    border-radius: 12px;
    background: var(--bg-kyy_color_bgBrand_hover, #EAECFF);
    color: #707eff;

    .icon-img {
      color: #707eff !important;

    }
  }

  .head-item-icon {
    width: 68px;
    height: 24px;
    padding: 0 4px 0 8px;
    border-radius: 12px;
    text-align: center;
    cursor: pointer;
    display: flex;
    align-items: center;
    color: #516082;
    text-align: center;
    font-size: 14px;

    font-style: normal;
    font-weight: 400;
    line-height: 22px;

    /* 157.143% */
    .icon-img {
      color: #828DA5;
      font-size: 20px;
      width: 20px;
      height: 20px;
      margin-right: 4px;
    }
  }

  .relative {
    height: 36px;
  }

  .item-body:hover {
    border-radius: 12px;
    background: var(--bg-kyy_color_bg_light, #fff);
    box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.12);
  }

  :deep(.t-badge--circle) {

    padding-right: calc((16px - 8px) / 2);
    padding-left: calc((16px - 8px) / 2);
    min-width: 8px;
    height: 16px;
    background-color: var(--td-error-color);
    line-height: 16px;
  }

  .dow-icons {
    font-size: 20px;
    color: #1A2139;
  }

  .item-body-text-all {
    color: #000;
    font-size: 14px;
  }

  .item-body-all {
    height: 60px !important;
    justify-content: center !important;
  }

  .item-body-img-box {
    position: relative;

    .item-body-img-type {
      position: absolute;
      top: -4px;
      right: -4px;
      border-radius: 99px;
      background: linear-gradient(316deg, #6C95FF -1.59%, #DEEFF7 23.81%, #D0F8FA 49.21%, #FFF 74.6%, #A3AFFD 100%);
      display: flex;
      padding: 0px 4px;
      justify-content: center;
      align-items: center;
      gap: 4px;
      color: var(--text-kyy_color_text_1, #1A2139);
      text-align: center;
      font-family: "PingFang SC";
      font-size: 8px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }
  }

  .item-body-img {
    border-radius: 12px;
    object-fit: contain;
    overflow: hidden;
    background: #fff;

  }
</style>