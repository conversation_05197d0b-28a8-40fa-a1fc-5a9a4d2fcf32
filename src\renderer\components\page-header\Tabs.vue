<template>
  <div v-show="needScrollTab" class="tabs-arrow left" @click="scrollTo(-1)">
    <iconpark-icon name="iconarrowlift" class="icon" />
  </div>

  <div ref="tabsRef" class="tabs-list" :style="{ paddingLeft: `${gap}px`, paddingRight: `${gap}px` }">
    <div ref="tabInnerRef" class="tabs-inner" :style="scrollStyle">
      <div
        v-for="(item, index) in tabStore.tabs"
        :ref="(el) => setActiveTab(el, index)"
        :key="index"
        :class="['tabs-item', { selected: tabStore.activeIndex === index, 'is-min': tabItemStyle.width === `${minWidth}px` }]"
        :style="tabItemStyle"
        @click.stop="switchTab(item, index)"
      >
        <t-tooltip :content="item.label" placement="bottom" :show-arrow="false">
          <div class="tab-content">
            <template v-if="item.icon && /^http/.test(item.icon)">
              <img :src="item.icon" class="icon">
            </template>
            <template v-else>
              <iconpark-icon
                v-if="item.icon || icon"
                :name="item.icon || icon"
                class="icon"
              />
            </template>

            <div class="title">{{ item.label }}</div>

            <iconpark-icon
              v-if="item.closable !== false"
              name="iconerror"
              class="close-icon"
              @click.stop="removeTab(item, index)"
            />
          </div>
        </t-tooltip>

        <div v-if="showDivider(item, index)" class="tab-divider" />
      </div>
    </div>
  </div>

  <div v-show="needScrollTab" class="tabs-arrow right" @click="scrollTo(1)">
    <iconpark-icon name="iconarrowright" class="icon" />
  </div>
</template>

<script setup lang="ts">
import {
  ref, watch, nextTick, computed,
} from 'vue';
import { useResizeObserver } from '@vueuse/core';
import { useRouter } from 'vue-router';
import { onMountedOrActivated } from '@renderer/hooks/onMountedOrActivated';
import { destroyNode, getShowNode } from '@renderer/_jssdk/components/iframe/iframePool';
import { DialogPlugin } from 'tdesign-vue-next';
import qs from 'qs';
import { TabItem } from './type';
import { useTabsStore } from './store';

const props = withDefaults(defineProps<{
  minWidth?: number,
  maxWidth?: number,
  gap?: number,
  icon?: string;
  autoRoute?: boolean;
}>(), {
  minWidth: 88,
  maxWidth: 168,
  gap: 4,
  icon: 'square-fill',
});

const emit = defineEmits(['change', 'remove', 'refresh', 'loaded']);
const tabStore = useTabsStore();
const router = useRouter();

// 分隔线显示
const showDivider = (item: TabItem, index: number) => {
  const notNext = index + 1 !== tabStore.activeIndex;
  return tabStore.activeIndex !== index && notNext;
};

const tabsScrollLeft = ref(0);
const tabsRef = ref(null);
const tabInnerRef = ref(null);
const needScrollTab = ref(false);
const activeTabRef = ref();

const getDomWidth = (dom: HTMLElement): number => dom?.offsetWidth || 0;

const scrollStyle = computed(() => ({
  transform: `translate3d(${-tabsScrollLeft.value}px, 0, 0)`,
  gap: `${props.gap}px`,
}));

// 页签宽度
const tabWidth = ref(0);
const setTabWidth = async () => {
  await nextTick();
  if (!tabsRef.value || !tabsRef.value.clientWidth) return 0;
  const len = tabStore.tabs.length;
  tabWidth.value = (tabsRef.value.clientWidth - ((len + 1) * props.gap)) / len;
};

// 单个页签样式
const tabItemStyle = computed(() => ({
  width: `${Math.min(props.maxWidth, Math.max(tabWidth.value, props.minWidth))}px`,
  maxWidth: `${props.maxWidth}px`,
  minWidth: `${props.minWidth}px`,
}));

// 获取页签尺寸
const getWidth = () => {
  const containerWidth = getDomWidth(tabsRef.value);
  const tabInnerWidth = getDomWidth(tabInnerRef.value);
  return {
    containerWidth,
    tabInnerWidth,
    diffWidth: tabInnerWidth - containerWidth,
  };
};

/**
 * 点击左右滚动页签的按钮
 * @param direction 移动的方向，-1 为左滚动
 */
const scrollTo = async (direction: -1 | 1) => {
  await nextTick();

  const { containerWidth, tabInnerWidth, diffWidth } = getWidth();
  const scrollLeft = tabsScrollLeft.value + diffWidth * direction;

  // 往左滚
  if (direction === -1) {
    if (tabsScrollLeft.value <= 0) return;
    tabsScrollLeft.value = Math.max(0, scrollLeft);
  }

  // 往右滚
  if (direction === 1) {
    if (tabsScrollLeft.value + containerWidth - tabInnerWidth >= -1) return;
    tabsScrollLeft.value = Math.min(diffWidth, scrollLeft);
  }
};

// const delay = (ms) => new Promise((resolve) => { setTimeout(resolve, ms); });

// 增删页签时更新尺寸及定位
const updateTabs = async () => {
  // await delay(200);
  await setTabWidth();

  // 删除页签后更新页签位置
  const { diffWidth } = getWidth();
  if (diffWidth <= tabsScrollLeft.value) {
    tabsScrollLeft.value = diffWidth;
  }

  if (!tabsRef.value) return;
  const { scrollWidth, offsetWidth } = tabsRef.value;
  if (tabWidth.value <= props.minWidth) {
    needScrollTab.value = scrollWidth > offsetWidth;
    // tabsScrollLeft.value = moveActiveTabIntoView(tabsScrollLeft.value);
  } else {
    needScrollTab.value = false;
    tabsScrollLeft.value = 0;
  }
};

// 激活页签
const setActiveTab = (ref: any, index: number) => {
  if (!ref) return;
  if (index === tabStore.activeIndex && activeTabRef.value !== ref) {
    activeTabRef.value = ref;
    tabsScrollLeft.value = moveActiveTabIntoView(tabsScrollLeft.value);
  }
};

/**
 * 计算将当前 tab 滚动到可视区域的偏移值
 * @param scrollLeft 当前的偏移值
 * @returns number
 */
const moveActiveTabIntoView = (scrollLeft: number): number => {
  if (!activeTabRef.value) return scrollLeft;
  const totalWidthBeforeActiveTab = activeTabRef.value.offsetLeft;
  if (!tabsRef.value) return scrollLeft;

  // 判断当前tab是不是在左边被隐藏
  const isCurrentTabHiddenInLeftZone = scrollLeft > totalWidthBeforeActiveTab;
  if (isCurrentTabHiddenInLeftZone) {
    return totalWidthBeforeActiveTab;
  }

  // move to right
  const activeTabWidth = activeTabRef.value.offsetWidth;
  if (!tabsRef.value) return scrollLeft;
  const containerWidth = getDomWidth(tabsRef.value);
  // 判断当前tab是不是在右边被隐藏
  const isHiddenInRightZone = scrollLeft + containerWidth < totalWidthBeforeActiveTab + activeTabWidth;
  if (isHiddenInRightZone) {
    return totalWidthBeforeActiveTab + activeTabWidth - containerWidth;
  }
  return scrollLeft;
};

const bodyWidth = ref(0);
onMountedOrActivated(() => {
  bodyWidth.value = document.body.getBoundingClientRect().width;
});

useResizeObserver(document.body, (entries) => {
  // HACK: 未变更窗口大小也会被触发
  if (bodyWidth.value === entries[0].contentRect.width) return;

  updateTabs();
});

// 检测是否需要显示左右滚动页签的按钮
watch(() => tabStore.tabs, () => {
  updateTabs();
}, {
  immediate: true,
  deep: true,
});

// 解析 fullPath，返回 { path, query }
const parseFullPath = (fullPath: string) => {
  const [path, search] = fullPath?.split('?') || [];
  const query = search ? qs.parse(search) : {};
  return { path, query };
};

// 切换页签
const switchTab = (item: TabItem, index: number) => {
  tabStore.switchTab(index);
  emit('change', item, index);
  console.log('=====>switchTab', tabStore.getActiveTab());
  if (props.autoRoute) {
    setTimeout(() => {
      const { path, query } = parseFullPath(tabStore.getActiveTab().fullPath);
      const mergedQuery = {
        ...query,
        ...(tabStore.getActiveTab()?.query || {}),
        __tabs_id__: tabStore.getActiveTab()?.query?.path_uuid,
        __tabs_title__: tabStore.getActiveTab()?.query?.label,
        __tabs_icon__: tabStore.getActiveTab()?.query?.icon,
        __tabs_active_icon__: tabStore.getActiveTab()?.query?.icon,
      };
      const options = {
        path,
        query: mergedQuery,
      };
      console.log('=====>options', options);
      router.replace(options);
    }, 0);
  }
};
const handleBeforeClose = (title: string, content: string): Promise<boolean> => new Promise((resolve) => { const confirmDia = DialogPlugin.confirm({ theme: 'info', header: title, body: content, confirmBtn: '确认关闭', cancelBtn: { content: '取消', theme: 'default', variant: 'outline' }, onConfirm: async () => { confirmDia.hide(); resolve(true); }, onClose: () => { confirmDia.destroy(); resolve(false); } }); });

// 删除页签
const removeTab = async (item: TabItem, index: number) => {
  if (item?.beforeCloseOptions) {
    const isClose = await handleBeforeClose(item.beforeCloseOptions.title, item.beforeCloseOptions.content);
    if (!isClose) return;
  }
  item && tabStore.removeTab(item);
  getShowNode()?.forEach((i) => {
    destroyNode(i.getAttribute('data-id'));
  });
  updateTabs();
  emit('remove', item, index);
  console.log('=====>tabStore.getActiveTab()', tabStore.getActiveTab());
  if (props.autoRoute) {
    setTimeout(() => {
      const { path, query } = parseFullPath(tabStore.getActiveTab().fullPath);
      const mergedQuery = {
        ...query,
        ...(tabStore.getActiveTab()?.query || {}),
        __tabs_id__: tabStore.getActiveTab()?.query?.path_uuid,
        __tabs_title__: tabStore.getActiveTab()?.query?.label,
        __tabs_icon__: tabStore.getActiveTab()?.query?.icon,
        __tabs_active_icon__: tabStore.getActiveTab()?.query?.icon,
      };
      router.replace({
        path,
        query: mergedQuery,
      });
    }, 0);
  }
};
</script>

<style lang="less" scoped>
.tabs-arrow {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 28px;
  background: var(--kyy_color_tabbar_bg, #EBF1FC);
  z-index: 99;
  &.left {
    border-radius: 4px 0 0 0;
    box-shadow: 3px 0 4px 0 rgba(0, 0, 0, 0.12);
  }
  &.right {
    box-shadow: -3px 0 4px 0 rgba(0, 0, 0, 0.12);
  }
  &:hover {
    cursor: pointer;
    border-radius: 0 4px 0 0;
    background: var(--kyy_color_tabbar_item_bg_hover, rgba(255, 255, 255, 0.5));
  }
  .icon {
    color: #828DA5;
  }
}

.tabs-list {
  display: flex;
  flex: 1;
  width: 0;
  overflow-x: hidden;
  background: var(--kyy_color_tabbar_bg, #ebf1fc);
  z-index: 9;
  .tabs-inner {
    display: flex;
    transition: transform 0.5s;
    gap: 4px;
  }
  .tabs-item {
    position: relative;
    cursor: pointer;
    min-width: 88px;
    justify-content: space-between;
    height: 32px;
    display: flex;
    align-items: center;
    border-radius: 4px;
    padding: 0 12px;
    &:hover {
      background: var(--kyy_color_tabbar_item_bg_hover, rgba(255, 255, 255, 0.5));
      .close-icon {
        color: #707EFF;
      }
    }
    &.selected {
      background: var(--kyy_color_tabbar_item_bg_active, #fff);
    }
    &.is-min {
      .close-icon {
        display: none;
      }
      &:hover .close-icon,
      &.selected .close-icon {
        display: inherit;
      }
    }
  }

  .tab-content {
    display: flex;
    align-items: center;
    //gap: 8px;
    width: 100%;
    .icon {
      width: 20px;
      height: 20px;
      font-size: 20px;
      color: #fff;
      border-radius: 4px !important;
      margin-right: 0;
    }

    .title {
      height: 20px;
      font-size: 14px;
      font-weight: 400;
      color: var(--kyy_color_tabbar_item_text, #1a2139);
      line-height: 20px;
      flex: 1;
      margin-left: 8px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .close-icon {
      margin-left: 0;
      font-size: 20px;
    }
  }

  .tab-divider {
    display: flex;
    height: 20px;
    min-width: 1px;
    max-width: 1px;
    justify-content: flex-end;
    align-items: center;
    position: absolute;
    right: -1px;
    top: 6px;
    background-color: #d5dbe4;
  }
}
</style>
