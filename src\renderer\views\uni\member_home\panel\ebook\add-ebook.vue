<template>
  <t-drawer
    v-model:visible="visible"
    class="drawerSet ebdrawer"
    :header="etype === 1 ?  $t('member.culture.o') : $t('member.rongeb.a') "
    :z-index="1500"
    :on-confirm="onClickConfirm"
    :close-btn="true"
    :close-on-overlay-click="false"
    :size="'472px'"
  >
    <template #closeBtn>
      <!-- <svg class="iconpark-icon" style="width: 16px; height: 16px">
        <use href="#close" />
      </svg> -->
      <iconpark-icon name="iconerror" style="font-size: 24px"></iconpark-icon>
    </template>
    <template #footer>
      <div class="operates">
        <t-button
          theme="default"
          variant="outline"
          class="operates-item"
          @click="onClose"
        >取消</t-button>
        <t-button
          class="operates-item"
          @click="submitForm"
          :disabled="loading"
          :loading="loading"
        >{{$t('member.kaxi.s')}}</t-button>
      </div>
    </template>
    <div v-if="visible" class="drawerSet-body">
      <t-form
        ref="form"
        label-align="top"
        :rules="FORM_RULES"
        :data="formData"
        :colon="false"
        @reset="onReset"
        @submit="onSubmit"
      >
        <t-form-item
          :label="t('member.digital.j')"
          name="type"
          class="searchForm-item-input"
        >
          <t-radio-group v-model="formData.type" :disabled="etype === 2">
            <t-radio
              v-for="op in optionsType"
              :key="op.value"
              :value="op.value"
            >
              {{ op.label }}
            </t-radio>
          </t-radio-group>
        </t-form-item>
        <FImageUploadComp
          :attrs="attachImage"
          :size-limit="{size: 5, unit: 'MB'}"
          :tips="t('ebook.covtip')"
          :validator-tip="t('ebook.validatorTipkw')"
          @clear-validate="clearValidate"
        />
        <t-form-item :label="$t('member.culture.z')" name="name" initial-data="TDesign">
          <t-input v-model="formData.name" :maxlength="20" :placeholder="$t('member.rongeb.g')" />
        </t-form-item>
        <FFileUpload
          v-if="formData.type === 1"
          :attrs="attachment"
          :is-onepoint-sixe="true"
          :accept="'application/pdf'"
          :validator-tip="t('ebook.validatorTip23')"
          :size="100"
          :upload-format="uploadFormat"
          @clear-validate="clearValidate"
        />
        <t-form-item  v-if="formData.type === 2" :label="$t('member.kaxi.t2')" name="url">
          <t-input v-model="formData.url" :maxlength="500" :placeholder="$t('member.kaxi.u')" />
        </t-form-item>
        <t-form-item
          name="channelType"
          label-align="top"
          class="sendTo"
          :label="t('banch.sendTo')"
        >
          <t-checkbox-group v-model="formData.channel_type">
            <t-checkbox
              key="0"
              value="square"
              :label="t('banch.square')"
            ></t-checkbox>
            <t-checkbox
              key="1"
              value="digital_work"
              :label="t('banch.banch')"
            ></t-checkbox>
            <t-checkbox
              key="2"
              value="digital_platform"
              :label="t('banch.member')"
            ></t-checkbox>
          </t-checkbox-group>
        </t-form-item>
        <t-form-item
          v-if="formData.type === 1"
          :label="t('member.culture.m')"
          name="describe"
        >
          <t-textarea v-model="formData.describe" :maxlength="200" :placeholder="t('ebook.plin')" />
        </t-form-item>
      </t-form>

    </div>
  </t-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from "vue";
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';
import { useI18n } from "vue-i18n";
import FFileUpload from "@renderer/components/free-from/runtime/controls/FFileUpload.vue";
import { createEbookAxios, putEbookAxios } from "@renderer/api/uni/api/ebookApi";
import { imgUrl } from "@renderer/utils/myUtils";
import FImageUploadComp from "@/components/free-from/runtime/controls/FImageUpload.vue";
import { getResponseResult } from "@/utils/myUtils";
import { useDigitalPlatformStore } from "@renderer/views/digital-platform/store/digital-platform-store";
import { useRoute } from "vue-router";
import { platform } from "@renderer/views/digital-platform/utils/constant";
import { useUniStore } from '@renderer/views/uni/store/uni';
import { getUniTeamID } from "@renderer/views/uni/utils/auth";


const digitalPlatformStore = useDigitalPlatformStore();
const route = useRoute();
const { t } = useI18n();
const store = useUniStore();
const props = defineProps({
  platform: {
    type: String,
    default: '',
  },
  ebookSettingInfo: {
    type: Object,
    default: null,
  },
})
// 平台类型 目前只有digital-platform
const platformCpt = computed(() => {
  return props.platform || route.query?.platform
})

const activeAccount = computed(() => {
  if(platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount
  } else if(platformCpt.value === platform.digitalWorkbench)  {
    return route.query;
  } else {
    return null
  }
})


const currentTeamId = computed(() => {
  if(platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount?.teamId
  } else if(platformCpt.value === platform.digitalWorkbench){
    return route.query?.teamId || 0;
  } else {
    return  getUniTeamID()
  }
})

const optionsType = [
  //   { value: 0, label: "全部" },
  { value: 1, label: t('member.kaxi.v') },
  { value: 2, label: t('member.kaxi.w') }
];
const optionsChannelType = [
  { value: 'square', label: t('member.kaxi.x') },
  { value: 'digital_work', label: t('member.kaxi.w') },
  { value: 'digital_platform', label: t('member.kaxi.z') }
];

const attachImage = reactive({
  required: true,
  name: t('member.rongeb.b'),
  editable: true,
  max: 1,
  value: [],
  id: 'images'
});
const attachment = reactive({
  required: true,
  name: '刊物文件',
  actionName: t('ebook.up'),
  editable: true,
  fullUrl: true,
  max: 1,
  fileMax: 100,
  setip: t('ebook.uptip'),
  value: [],
  id: 'file'
});
const uploadFormat = ['pdf'];
const formData = reactive({
  name: '',
  id: 0,
  describe: '',
  type: 1,
  url: '',
  channel_type: [...optionsChannelType.map((item) => item.value)]
});
const visible = ref(false);
const form = ref(null);
const FORM_RULES = {
  name: [{ required: true, message: t('ebook.namerq2') }],
  url: [
    { required: true, message: '请输入以https://开头的链接' },
    {
      url: {
        protocols: ["https"],
        require_protocol: true
      },
      message: '请输入以https://开头的链接'
    },
    {
      validator: (val) => {
        if (val && val.length > 500) return { result: false, message:  '请输入500字符以内', type: 'error' };
        return { result: true, type: 'success' };
      }
    }
  ],
};
const emits = defineEmits(["reload"]);
const clearValidate = (name) => {
  form.value.clearValidate([name]);
};

const onReset = () => {
  MessagePlugin.success(t('ebook.namerq'));
};
const dataHandle = () => {
  if(formData.type === 1) {
    attachment.value[0].url = imgUrl + attachment.value[0].file_name;
  }
  const params = {
    name: formData.name,
    id: formData.id,
    describe: formData.describe,
    cover: attachImage.value[0].file_name,
    file: formData.type === 1 ? attachment.value[0] : {url: formData.url} ,
    type: formData.type,
    channel_type: formData.channel_type.join(','),
    digital_type: 'uni'
  };
  return params;
};
const loading = ref(false);
const onSubmit = async ({ validateResult, firstError }) => {
  if(loading.value) return;

  // 若当前已使用生成页数大于等于总量，提示
  console.log(props.ebookSettingInfo)
  if(etype.value === 1 && props.ebookSettingInfo) {
    // props.ebookSettingInfo
    if(props.ebookSettingInfo?.use_page >= props.ebookSettingInfo?.total_page) {
      MessagePlugin.warning('生成页数不足');
      return;
    }
  }

  if (validateResult === true) {
    loading.value = true;

    try {
      let result = etype.value === 2 ? await putEbookAxios(formData.id, dataHandle(), currentTeamId.value) : await createEbookAxios(dataHandle(), currentTeamId.value);
      result = getResponseResult(result);
      loading.value = false;
      if (!result) return;
    if (etype.value === 2) {
      MessagePlugin.success(t('ebook.edsucc'));
    } else {
      if(formData.type === 2) {
        MessagePlugin.success('添加成功');
      } else {
        addSucc();
      }
    }
      onClose();
      emits("reload");
    } catch (error) {
      MessagePlugin.error(error.message);
    }
  } else {
    console.log('Validate Errors: ', firstError, validateResult);
    MessagePlugin.warning(firstError);
  }
  loading.value = false;
};

const addSucc = () => {
  const confirmDia = DialogPlugin({
    header: t('ebook.addse'),
    theme: 'info',
    body: '正在为你生成电子刊物，生成后将自动发布，请稍后查看',
    closeBtn: null,
    // confirmBtn: t('ebook.del'),
    className: 'delmode',
    onConfirm: async () => {
      confirmDia.hide();
    },
    onClose: () => {
      confirmDia.hide();
    },
  });
};


const submitForm = async () => {
  form.value.submit();
};

const setdata = (data) => {
  formData.name = data.name;
  formData.id = data.id;
  formData.describe = data.describe;
  formData.type = data.type;
  formData.channel_type = data.channel_type.split(',');
  if(data.type === 2) {
    formData.url = data?.file?.url;
  }
  attachImage.value = [{
    file_name: data.cover,
    file_name_short: "0.jpeg",
    isShow: false,
    original_name: data.cover,
    size: 1356530,
    fileMax: 50,

    type: "jpeg"
  }];
  attachment.value = [data.file];
  attachment.editable = false;
  attachment.setip = null;
};

const initData = () => {
  attachment.value = [];
  attachImage.value = [];
  formData.name = '';
  formData.id = 0;
  formData.describe = '';
  formData.type = 1;
  formData.channel_type = [...optionsChannelType.map((item) => item.value)]
  formData.url = '';
  attachment.editable = true;
  attachment.setip = t('ebook.uptip');
};

const etype = ref(1);
const onOpen = (type, data?) => {
  etype.value = type;
  initData();
  if (type === 2) {
    // 编辑回填
    console.log('data', data);
    setdata(data);
  }
  visible.value = true;
  console.log('attachment', attachment);

};
const onClose = () => {
  visible.value = false;
};
const onClickConfirm = () => {
};

defineExpose({
  onOpen,
  onClose,
});
</script>
<style lang="less" scoped>
.operates{
  display: flex;
  justify-content: flex-end;
  .operates-item{
    width: 80px;
  }
}

.file-item:hover {
    background: var(--bg-kyy-color-bg-deep, #f5f8fe);
    border-radius: 8px;
    cursor: pointer;
    border: 1px solid var(--kyy_color_upload_border_default, #f5f8fe);
  }

  .file-item {
    display: flex;
    align-items: center;
    //background: #f0f8ff;
    padding: 0px 8px;
    height: 64px;
    border-radius: 8px;
    border: 1px solid var(--kyy_color_upload_border_default, #d5dbe4);
    background: var(--kyy_color_upload_bg, #fff);

    img {
      width: 40px;
      height: 40px;
      margin-right: 8px;
    }

    .content {

      div:first-child {
        color: var(--kyy_color_upload_text_success, #1A2139);

        /* kyy_fontSize_2/regular */
        font-family: PingFang SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }

      div:last-child {
        color: var(--kyy_color_upload_text_disabled, #ACB3C0);
        font-family: PingFang SC;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px; /* 166.667% */
      }
    }

    .btns {
      a:first-child {
        font-size: 14px;

        font-weight: 400;
        text-align: center;
        // color: #2069e3;
        // line-height: 22px;
        margin-right: 16px;
      }

      a:last-child {
        font-size: 14px;

        font-weight: 400;
        text-align: center;
        color: #da2d19;
        // line-height: 22px;
      }
    }
  }

.drawerSet-body{
  padding: 0 8px;
}


</style>
