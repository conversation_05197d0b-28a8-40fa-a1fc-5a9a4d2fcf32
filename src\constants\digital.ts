export enum PlatformType {
	MEMBER = 'member',
	GOVERNMENT = 'government',
	CBD = 'cbd',
	ASSOCIATION = 'association',
	UNI = 'uni',
}

export const platformOptions: { label: string; value: PlatformType | '' }[] = [
	{ label: '全部', value: '' },
	{ label: '数字商协', value: PlatformType.MEMBER },
	{ label: '数字城市', value: PlatformType.GOVERNMENT },
	{ label: '数字cbd', value: PlatformType.CBD },
	{ label: '数字社群', value: PlatformType.ASSOCIATION },
	{ label: '数字高校', value: PlatformType.UNI },
];

export const exclusiveStatusOptions = [
	{ label: '全部', value: '' },
	{ label: '未获取', value: 0 },
	{ label: '生效中', value: 1 },
	{ label: '已失效', value: 2 },
];

// 特性等级
export const featureLevelOptions = [
	{ label: '全部', value: '' },
	{ label: 1, value: 1 },
	{ label: 2, value: 2 },
	{ label: 3, value: 3 },
	{ label: 4, value: 4 },
	{ label: 5, value: 5 },
	{ label: 6, value: 6 },
	{ label: 7, value: 7 },
	{ label: 8, value: 8 },
	{ label: 9, value: 9 },
];

// （0禁用 1启用）
export const featureStatusOptions = [
	{ label: '全部', value: '' },
	{ label: '禁用', value: 0 },
	{ label: '启用', value: 1 },
];
