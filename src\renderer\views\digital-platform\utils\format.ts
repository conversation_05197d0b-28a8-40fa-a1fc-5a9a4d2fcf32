type Unit = {
  value: number;
  symbol: string;
};

type FormatOptions = {
  decimalPlaces?: number;
};

/**
 * 将数字格式化为带有中文单位的字符串
 * @param number - 要格式化的数字
 * @param options - 格式化选项
 * @param options.decimalPlaces - 要保留的小数位数，默认为1
 * @returns 格式化后的字符串
 */
export function formatNumber(number: number, options: FormatOptions = {}): string {
  const { decimalPlaces = 1 } = options;
  const units: Unit[] = [
    { value: 1e8, symbol: '亿' },
    { value: 1e4, symbol: '万' }
  ];

  for (const { value, symbol } of units) {
    if (number >= value) {
      const scale = number / value;
      let formattedNumber = scale.toFixed(decimalPlaces);
      // 移除小数点后不必要的0，并且如果小数部分为0，则不显示小数点
      formattedNumber = formattedNumber.replace(/(\.0+$)/, '').replace(/(\.$)/, '');
      return formattedNumber + symbol;
    }
  }

  return number.toString();
}
