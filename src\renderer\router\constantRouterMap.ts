import { RouteRecordRaw } from 'vue-router';

// 动态导入模块路由
const modules = import.meta.globEager('./modules/*.ts');
const moduleRoutes: Array<RouteRecordRaw> = Object.keys(modules).reduce(
  (acc, fileName) => [...acc, ...(modules[fileName] as { default: Array<RouteRecordRaw> }).default],
  [],
);

const routes: Array<RouteRecordRaw> = [
  {
    path: '/main',
    name: 'main', // 主进程
    component: () => import('@renderer/layouts/main.vue'),
    children: [
      {
        path: 'message',
        name: 'message',
        component: () => import('@renderer/views/message/index.vue'),
      },
      // 通讯录,不需要新建窗口，所以放在layout下
      {
        path: 'contactsIndex',
        name: 'contactsIndex',
        component: () => import('@renderer/views/contacts/index.vue'),
        redirect: '/main/contactsIndex/recent',
        children: [
          {
            path: 'recent',
            name: 'contactsRecent',
            component: () => import('@renderer/views/contacts/pages/recent.vue'),
          },
          {
            path: 'follow',
            name: 'contactsFollow',
            component: () => import('@renderer/views/contacts/pages/follow.vue'),
          },
          {
            path: 'organization/:teamId/:id?',
            name: 'contactsOrganization',
            component: () => import('@renderer/views/contacts/pages/organization.vue'),
          },
          {
            path: 'newContacts',
            name: 'contactsNew',
            component: () => import('@renderer/views/contacts/pages/newContacts.vue'),
          },
          {
            path: 'friend',
            name: 'contactsFriend',
            component: () => import('@renderer/views/contacts/pages/friend.vue'),
          },
          {
            path: 'groups',
            name: 'contactsGroup',
            component: () => import('@renderer/views/contacts/pages/group.vue'),
          },
          {
            path: 'organize',
            name: 'contactsOrganize',
            component: () => import('@renderer/views/contacts/pages/organizeMember.vue'),
          },
          {
            path: 'tag',
            name: 'contactsTag',
            component: () => import('@renderer/views/contacts/pages/tag.vue'),
          },
          {
            path: 'tagDetail/:id/:des/:cardId',
            name: 'tagDetail',
            component: () => import('@renderer/views/contacts/pages/tagDetail.vue'),
          },
        ],
      },
    ],
  },
  {
    path: '/identitycard',
    name: 'identitycard',
    component: () => import('@renderer/views/identitycard/index.vue'),
    children: [
      {
        path: 'load/:cardId/:myId',
        name: 'identitycardLoad',
        component: () => import('@renderer/views/identitycard/loading.vue'),
      },
      {
        path: 'view/:cardId/:myId',
        name: 'identitycardView',
        component: () => import('@renderer/views/identitycard/mainCard.vue'),
      },
      {
        path: 'more',
        name: 'identitycardMore',
        component: () => import('@renderer/views/identitycard/allCard.vue'),
      },
      {
        path: 'note',
        name: 'identitycardNote',
        component: () => import('@renderer/views/identitycard/setNotes.vue'),
      },
      {
        path: 'identityCardEdit',
        name: 'identityCardEdit',
        component: () => import('@renderer/views/identitycard/editInfo.vue'),
      },
      {
        path: 'contactValidate/:cardId/:myId',
        name: 'contactValidate',
        component: () => import('@renderer/views/identitycard/contactValidate.vue'),
      },
    ],
  },
  {
    path: '/h5',
    name: 'h5', // iframe 加载 h5 页面
    component: () => import('@renderer/views/h5/index.vue'),
  },
  {
    path: '/account',
    name: 'account', // 登陆注册
    component: () => import('@renderer/views/account/index.vue'),
    redirect: '/account/loginNormal',
    children: [
      {
        path: 'loginNormal',
        name: 'loginNormal',
        component: () => import('@renderer/views/account/login/normal.vue'),
      },
      {
        path: 'registerNormal',
        name: 'registerNormal',
        component: () => import('@renderer/views/account/register/normal.vue'),
      },
      {
        path: 'forgot',
        name: 'accountForgot',
        component: () => import('@renderer/views/account/login/verify.vue'),
      },
    ],
  },
  {
    path: '/merged',
    name: 'merged',
    component: () => import('@renderer/views/merged/index.vue'),
  },
  {
    path: '/monitor',
    name: 'monitor',
    component: () => import('@renderer/views/monitor/index.vue'),
  },
  {
    path: '/iframe',
    name: 'iframe',
    component: () => import('@renderer/views/iframe/index.vue'),
  },
  {
    path: '/loading',
    name: 'loading',
    component: () => import('@renderer/views/loading/index.vue'),
  },
  {
    path: '/myOrder',
    name: 'myOrder', // 应用模板带有titelBar和leftBar,独立进程
    component: () => import('@renderer/components/order/myOrder.vue'),
  },
  {
    path: '/nicheReadOnly',
    name: 'nicheReadOnly', // 应用模板带有titelBar和leftBar,独立进程
    component: () => import('@renderer/views/niche/nicheDetailReadOnly.vue'),
  },
  {
    path: '/nicheExamineWin',
    name: 'nicheExamineWin', // 应用模板带有titelBar和leftBar,独立进程
    component: () => import('@renderer/views/niche/extendDetail.vue'),
  },
  {
    path: '/viewFile',
    name: 'ViewFile', // 预览文件
    component: () => import('@renderer/components/preview/ViewFile.vue'),
  },
  {
    path: '/viewVideo',
    name: 'viewVideo', // 预览視頻
    component: () => import('@renderer/components/preview/viewVideo.vue'),
  },
  {
    path: '/viewImg',
    name: 'viewImg', // 预览图片
    component: () => import('@renderer/components/preview/ViewImg.vue'),
  },
  {
    path: '/myInvoice',
    name: 'myInvoice', // 应用模板带有titelBar和leftBar,独立进程
    component: () => import('@renderer/components/order/myInvoice.vue'),
  },

  {
    path: '/myHelp',
    name: 'myHelp', // 应用模板带有titelBar和leftBar,独立进程
    component: () => import('@renderer/components/order/myHelp.vue'),
  },
  {
    path: '/layout',
    name: 'appLayout', // 应用模板带有titelBar和leftBar,独立进程
    component: () => import('@renderer/views/layout.vue'),
    children: [],
  },
  {
    path: '/clouddiskLayout',
    name: 'clouddiskLayout', // 云盘模板带有titelBar,独立进程
    component: () => import('@renderer/views/clouddiskLayout.vue'),
  },
  {
    path: '/approvalLayout',
    name: 'approvalLayout', // 审批titelBar,独立进程
    component: () => import('@renderer/views/approvalLayout.vue'),
  },
  {
    path: '/:pathMatch(.*)*',
    component: () => import('@renderer/views/404.vue'),
  },
  {
    path: '/Print',
    name: '打印',
    component: () => import('@renderer/views/Print.vue'),
  },
  // {
  //   path: "/workBenchIndex",
  //   name: "workBenchIndex", // 工作台
  //   component: () => import("@renderer/views/workBench/index.vue"),
  //   children: [
  //     {
  //       path: "workBenchHome",
  //       name: "workBenchHome", // 工作台
  //       component: () => import("@renderer/views/workBench/workBenchHome/index.vue"),
  //     },
  //     {
  //       path: "workBenchEnterprise",
  //       name: "workBenchEnterprise", // 管理工作台
  //       component: () => import("@renderer/views/workBench/enterprise/index.vue"),
  //     },

  //     {
  //       path: "groupInfo",
  //       name: "groupInfo", // 工作台
  //       component: () => import("@renderer/views/workBench/groupInfo/index.vue"),
  //     },
  //     {
  //       path: "engineer_home",
  //       name: "workBench_engineer_home", // 工作台下的工程
  //       component: () => import("@renderer/views/engineer/engineer_home/index.vue"),
  //     },
  //     {
  //       path: "approve_home",
  //       name: "workBench_approve_home", // 工作台下的审批
  //       component: () => import("@renderer/views/approve/approve_home/index.vue"),
  //     },
  //     {
  //       path: "admin/flow",
  //       name: "workBench_approve_home", // 工作台下的审批管理后台
  //       component: () => import("@renderer/views/approval/admin/flow.vue"),

  //     },
  //     {
  //       path: "administrator",
  //       name: "workBench_clouddiskadministrator", // 工作台下的云盘
  //       component: () => import("@renderer/views/clouddisk/administrator/index.vue"),
  //     },
  //   ],
  // },
  {
    path: '/clouddiskIndex',
    name: 'clouddiskIndex', // 云盘route-view
    component: () => import('@renderer/views/clouddisk/index.vue'),
    children: [
      {
        path: 'clouddiskhome',
        name: 'clouddiskhome', // 云盘列表
        component: () => import('@renderer/views/clouddisk/clouddiskhome/index.vue'),
      },
      {
        path: 'clouddisk',
        name: 'clouddisk', // 云盘
        component: () => import('@renderer/views/clouddisk/enterprise/index.vue'),
      },
      {
        path: 'administrator',
        name: 'clouddiskadministrator', // 云盘管理员
        component: () => import('@renderer/views/clouddisk/administrator/index.vue'),
      },
    ],
  },

  {
    path: '/approvalIndex',
    name: 'approvalIndex', // 云盘route-view
    component: () => import('@renderer/views/approval/index.vue'),
    children: [
      {
        path: 'approve_home',
        name: '审批',
        component: () => import('@renderer/views/approve/approve_home/index.vue'),
      },
      {
        path: 'init_apply',
        name: '发起申请',
        component: () => import('@renderer/views/approve/init_apply/index.vue'),
      },
      {
        path: 'approve_center',
        name: '审批中心',
        component: () => import('@renderer/views/approve/approve_center/index.vue'),
      },
      {
        path: 'admin/flow',
        name: 'flow',
        component: () => import('@renderer/views/approval/admin/flow.vue'),
      },
      {
        path: 'admin/approvalDesgin',
        name: 'approvalDesgin',
        component: () => import('@renderer/views/approval/admin/approvalDesgin.vue'),
      },
      {
        path: 'admin/approvalData',
        name: 'approvalData',
        component: () => import('@renderer/views/approval/admin/approvalData.vue'),
      },
      {
        path: 'admin/handover',
        name: 'handover',
        component: () => import('@renderer/views/approval/admin/handover.vue'),
      },
      {
        path: 'admin/derived-record',
        name: 'derived-record',
        component: () => import('@renderer/views/approval/admin/derived-record.vue'),
      },
      {
        path: 'admin/administrator',
        name: 'administrator',
        component: () => import('@renderer/views/approval/admin/administrator.vue'),
      },
      {
        path: 'free-form/form-design',
        name: 'freeForm',
        component: () => import('@renderer/views/free-form/form-design.vue'),
      },

      {
        path: 'free-form/form-runtime',
        name: 'freeFormRuntime',
        component: () => import('@renderer/views/free-form/form-runtime.vue'),
      },
      {
        path: 'free-form/form-detail',
        name: 'freeFormDetail',
        component: () => import('@renderer/views/free-form/form-detail.vue'),
      },
    ],
  },
  // 客户管理
  {
    path: '/customerIndex',
    name: 'customerIndex', // 云盘route-view
    component: () => import('@renderer/views/customer/index.vue'),
    children: [
      {
        path: 'customer-list',
        name: 'customerList',
        component: () => import('@renderer/views/customer/customer-list.vue'),
      },
      {
        path: 'customer-admin',
        name: 'customerAdmin',
        component: () => import('@renderer/views/customer/customer-admin.vue'),
      },
      {
        path: 'customer-admin-contact',
        name: 'customerAdminContact',
        component: () => import('@renderer/views/customer/customer-admin-contact.vue'),
      },
      {
        path: 'customer-admin-employee',
        name: 'customerAdminEmployee',
        component: () => import('@renderer/views/customer/customer-admin-employee.vue'),
      },
      {
        path: 'customer-admin-permission',
        name: 'customerAdminPermission',
        component: () => import('@renderer/views/customer/customer-admin-permission.vue'),
      },
      {
        path: 'customer-form-design',
        name: 'customerFormDesign',
        component: () => import('@renderer/views/customer/customer-form-design.vue'),
      },
    ],
  },
  // 供应商管理
  {
    path: '/supplierIndex',
    name: 'supplierIndex', // 云盘route-view
    component: () => import('@renderer/views/supplier/index.vue'),
    children: [
      {
        path: 'supplier-list',
        name: 'supplierList',
        component: () => import('@renderer/views/supplier/supplier-list.vue'),
      },
      {
        path: 'supplier-admin',
        name: 'supplierAdmin',
        component: () => import('@renderer/views/supplier/supplier-admin.vue'),
      },
      {
        path: 'supplier-admin-contact',
        name: 'supplierAdminContact',
        component: () => import('@renderer/views/supplier/supplier-admin-contact.vue'),
      },
      {
        path: 'supplier-admin-employee',
        name: 'supplierAdminEmployee',
        component: () => import('@renderer/views/supplier/supplier-admin-employee.vue'),
      },
      {
        path: 'supplier-admin-permission',
        name: 'supplierAdminPermission',
        component: () => import('@renderer/views/supplier/supplier-admin-permission.vue'),
      },
      {
        path: 'supplier-form-design',
        name: 'supplierFormDesign',
        component: () => import('@renderer/views/supplier/supplier-form-design.vue'),
      },
    ],
  },
  // 工程管理
  {
    path: '/engineerIndex',
    name: 'engineerIndex', // route-view
    component: () => import('@renderer/views/engineer/index.vue'),
    children: [
      {
        path: 'engineer_home',
        name: 'engineer_home',
        component: () => import('@renderer/views/engineer/engineer_home/index.vue'),
        meta: {
          hidden: true,
          parentPath: '/engineerIndex/engineer_home',
          title: '工程',
          icon: '',
          role: 'personal',
          affix: true,
        },
      },
      {
        path: 'engineer_set',
        name: 'engineer_set',
        component: () => import('@renderer/views/engineer/engineer_set/index.vue'),
        meta: {
          hidden: true,
          parentPath: '/engineerIndex/engineer_set',
          title: '工程设置',
          icon: '',
          role: 'personal',
        },
      },
      {
        path: 'engineer_admin',
        name: 'engineer_admin',
        component: () => import('@renderer/views/engineer/engineer_admin/index.vue'),
        meta: {
          hidden: true,
          parentPath: '/engineerIndex/engineer_admin',
          title: '工程管理后台',
          icon: '',
          role: 'personal',
        },
      },
    ],
  },

  {
    path: '/serviceIndex',
    name: 'serviceIndex', // route-view
    component: () => import('@renderer/views/service/index.vue'),
    children: [
      {
        path: 'service_home',
        name: 'service_home',
        component: () => import('@renderer/views/service/service_home/index.vue'),
        meta: {
          hidden: true,
          parentPath: '/serviceIndex/service_home',
          title: '服务',
          icon: '',
          role: 'personal',
          affix: true,
        },
      },
      {
        path: 'service_admin',
        name: 'service_admin',
        component: () => import('@renderer/views/service/service_admin/index.vue'),
        meta: {
          hidden: true,
          parentPath: '/serviceIndex/service_admin',
          title: '服务管理后台',
          icon: '',
          role: 'personal',
        },
      },
    ],
  },

  // {
  //   path: "/memberApp",
  //   name: "h5", // iframe 加载 h5 页面
  //   component: () => import("@renderer/views/member/app/index.vue")
  // },

  {
    path: '/projectLayout',
    name: 'projectLayout', // 工程titelBar,独立进程
    component: () => import('@renderer/views/projectLayout.vue'),
  },

  {
    path: '/zhixingLayout',
    name: 'zhixingLayout', // 独立进程
    component: () => import('@renderer/views/zhixingLayout.vue'),
  },

  ...moduleRoutes,

  {
    path: '/zhixing',
    name: 'zhixingIndex',
    component: () => import('@renderer/views/zhixing/index.vue'),
    children: [
      {
        path: 'note',
        name: 'zhixingNote',
        component: () => import('@renderer/views/zhixing/note/index.vue'),
      },
      {
        path: 'schedule',
        name: 'zhixingSchedule',
        component: () => import('@renderer/views/zhixing/schedule/index.vue'),
      },
      {
        path: 'todo',
        name: 'zhixingTodo',
        component: () => import('@renderer/views/zhixing/todo/index.vue'),
      },
      {
        path: 'remind',
        name: 'zhixingRemind',
        component: () => import('@renderer/views/zhixing/remind/index.vue'),
      },
    ],
  },
  {
    path: '/setting',
    name: 'sysSetting',
    component: () => import('@renderer/views/setting/index.vue'),
  },
  {
    path: '/setting/setGroup',
    name: 'setGroup',
    component: () => import('@renderer/views/setting/setGroup.vue'),
  },
  {
    path: '/personalWin',
    name: 'personalWin',
    component: () => import('@renderer/components/account/personalDialog.vue'),
  },
  {
    path: '/customerServiceIndex',
    name: 'customerServiceIndex',
    component: () => import('@renderer/views/customerService/index.vue'),
    children: [
      {
        path: 'customerServiceList',
        name: 'customerServiceList',
        component: () => import('@renderer/views/customerService/content.vue'),
      },
    ],
  },
  // 活动
  {
    path: '/activity',
    name: 'activity',
    component: () => import('@renderer/views/activity/index.vue'),
    children: [
      {
        path: 'participateActivity',
        name: 'participateActivity',
        component: () => import('@renderer/views/activity/window/participateActivity/index.vue'),
      },
      {
        path: 'manage/:id',
        name: 'manage',
        component: () => import('@renderer/views/activity/manage/index.vue'),
      },
      {
        path: 'activityCreate/:id?',
        name: 'activityCreate',
        component: () => import('@renderer/views/activity/create/index.vue'),
      },
      {
        path: 'activityProfessionalCreate/:id?',
        name: 'activityProfessionalCreate',
        component: () => import('@renderer/views/activity/create/index.vue'),
      },
      {
        path: 'activityLiteCreate/:id?',
        name: 'activityLiteCreate',
        component: () => import('@renderer/views/activity/create/lite/index.vue'),
      },
      {
        path: 'activityProfessionalPreview/:id?',
        name: 'activityProfessionalPreview',
        component: () => import('@renderer/views/activity/create/preview/index.vue'),
      },
      {
        path: 'activityPreview/:id?',
        name: 'activityPreview',
        component: () => import('@renderer/views/activity/create/preview/index.vue'),
      },
      {
        path: 'activityLitePreview/:id?',
        name: 'activityLitePreview',
        component: () => import('@renderer/views/activity/create/preview/index.vue'),
      },
      {
        path: 'activityList',
        name: 'activityList',
        component: () => import('@renderer/views/activity/activityListInvolved.vue'),
      },
      {
        path: 'activityAdmin',
        name: 'activityAdmin',
        component: () => import('@renderer/views/activity/activityAdmin.vue'),
      },
      {
        path: 'activityAdminList',
        name: 'activityAdminList',
        component: () => import('@renderer/views/activity/activityAdminList.vue'),
      },
      {
        path: 'activityListCreated',
        name: 'activityListCreated',
        component: () => import('@renderer/views/activity/activityListCreated.vue'),
      },
      {
        path: 'activityListDraft',
        name: 'activityListDraft',
        component: () => import('@renderer/views/activity/activityListDraft.vue'),
      },
      {
        path: 'activityNotice',
        name: 'activityNotice',
        component: () => import('@renderer/views/activity/notice/index.vue'),
      },
      {
        path: 'activityDetail/:id',
        name: 'activityDetail',
        component: () => import('@renderer/views/activity/activityDetail.vue'),
      },
      {
        path: 'activityEdit/:id?',
        name: 'activityEdit',
        component: () => import('@renderer/views/activity/activityEdit.vue'),
      },
      {
        path: 'activityParticipantDetail/:id',
        name: 'activityParticipantDetail',
        component: () => import('@renderer/views/activity/activityParticipantDetail.vue'),
      },
      {
        path: 'ablum-view/ablum-detail',
        name: 'activityAblumViewDetail',
        component: () => import('@renderer/views/activity/manage/album/albumViewDetail.vue'),
      },
    ],
  },
  {
    path: '/layoutActivity',
    name: 'layoutActivity',
    component: () => import('@renderer/views/square/layout/SingleLayout.vue'),
    children: [
      // {
      //   path: "activityLayout",
      //   name: "activityLayout",
      //   component: () => import("@renderer/views/activity/index.vue"),
      //   children: [
      //
      //   ]
      // }
      {
        path: 'activityDetailLayout/:id',
        name: 'activityDetailLayout',
        component: () => import('@renderer/views/activity/activityDetail.vue'),
      },
      {
        path: 'activityCreateLayout/:id?',
        name: 'activityCreateLayout',
        component: () => import('@renderer/views/activity/create/index.vue'),
      },
      {
        path: 'activityProfessionalCreateLayout/:id?',
        name: 'activityProfessionalCreateLayout',
        component: () => import('@renderer/views/activity/create/index.vue'),
      },
      {
        path: 'activityLiteCreateLayout/:id?',
        name: 'activityLiteCreateLayout',
        component: () => import('@renderer/views/activity/create/lite/index.vue'),
      },
      {
        path: 'activityPreviewLayout/:id?',
        name: 'activityPreviewLayout',
        component: () => import('@renderer/views/activity/create/preview/index.vue'),
      },
      {
        path: 'activityProfessionalPreviewLayout/:id?',
        name: 'activityProfessionalPreviewLayout',
        component: () => import('@renderer/views/activity/create/preview/index.vue'),
      },
      {
        path: 'activityLitePreviewLayout/:id?',
        name: 'activityLitePreviewLayout',
        component: () => import('@renderer/views/activity/create/preview/index.vue'),
      },
      {
        path: 'manageLayout/:id?',
        name: 'manageLayout',
        component: () => import('@renderer/views/activity/manage/index.vue'),
      },
      {
        path: 'activityParticipantDetailLayout/:id?',
        name: 'activityParticipantDetailLayout',
        component: () => import('@renderer/views/activity/activityParticipantDetail.vue'),
      },
      {
        path: 'ablum-view/ablum-detail',
        name: 'activityAblumViewDetailLayout',
        component: () => import('@renderer/views/activity/manage/album/albumViewDetail.vue'),
      },
    ],
  },
  {
    path: '/layoutWinRemindDialog',
    name: 'layoutWinRemindDialog',
    component: () => import('@renderer/views/activity/layout/dialogLayout.vue'),
  },
  /**
   * SDK业务路由
   */
  {
    path: '/sdk_view_select_member',
    name: 'selectMember',
    component: () => import('@renderer/_jssdk/components/SelectMember/App.vue'),
  },
];
export default routes;
