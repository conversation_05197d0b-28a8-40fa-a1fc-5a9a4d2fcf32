<template>
  <t-dialog
    v-model:visible="visible"
    :z-index="2500"
    attach="body"
    width="480px"
  >
    <template #header>
      <div class="header">
        <!-- <svg class="iconpark-icon header-svg"><use href="#attention-6ebn71gl"></use></svg>
				<div class="header-title">提示</div> -->
        {{ $t("member.bolit.i") }}
      </div>
    </template>
    <template #body>
      <div class="toBody">
        <t-textarea
          v-model="select.area"
          clearable
          :maxlength="200"
          :autosize="false"
          class="area"
          :placeholder="'请输入拒绝原因，最多支持200个字'"
        />
      </div>
    </template>
    <template #closeBtn>
      <!-- <iconpark-icon name="close" size="16"></iconpark-icon> -->
      <!-- <svg class="iconpark-icon" style="width: 16px; height: 16px">
        <use href="#close" />
      </svg> -->
      <iconpark-icon name="iconerror-a961a3n0" style="font-size: 24px"></iconpark-icon>
    </template>
    <template #footer>
      <div class="footer">
        <t-button
          theme="default"
          variant="outline"
          @click="onClose"
        >取消</t-button>
        <t-button theme="primary" class="ml-8" @click="onSave">{{
          $t("member.sure")
        }}</t-button>
      </div>
    </template>
  </t-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, Ref } from "vue";
import { MessagePlugin } from "tdesign-vue-next";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const emits = defineEmits(["onSend"]);

// 从公司组织架构导入
// const onImportFromCompany = () => {
// 	emits('onImportFromCompany');
// };
// // 手工建立组织架构
// const onHandCreate = () => {
// 	emits('onHandCreate');
// };

const select = reactive({
  id: 0,
  area: ""
});

const onChange = () => {};

const visible = ref(false);
const onOpen = (data?: any) => {
  if (data) {
    select.id = data.id;
    select.area = "";
  }
  visible.value = true;
};
const onClose = () => {
  visible.value = false;
};
// 确定发送消息
const onSave = () => {
  if (!select.area) {
    MessagePlugin.error('拒绝原因不能为空');
    return;
  }
  emits("onSend", select);
  onClose();
};
defineExpose({
  onOpen,
  onClose
});
</script>

<style lang="less" scoped>
:deep(.t-textarea__limit) {
  display: none;
}
:deep(.t-textarea__inner) {
  height: 146px !important;
}

.t-button + .t-button {
  margin-left: 0;
}
.header {
  display: flex;
  &-svg {
    width: 22px;
    height: 22px;
    color: #2069e3;
    margin-right: 8px;
  }
  &-title {
    font-size: 16px;

    font-weight: 700;
    color: #13161b;
  }
}
.toBody {
  .msg {
    margin-bottom: 16px;
    margin-left: 30px;
  }
  .btns {
    display: flex;
    margin-left: 30px;
    .btn {
      width: 320px;
      margin-bottom: 16px;
    }
  }
  .area {
    // margin-left: 30px;
    width: auto !important;
    margin-top: 10px;
  }
}
// :deep(.t-dialog--default) {
// 	padding-bottom: 0;
// }

.footer {
  display: flex;
  justify-content: flex-end;
  gap: 5px;
}

.t-dialog__body {
}
</style>
