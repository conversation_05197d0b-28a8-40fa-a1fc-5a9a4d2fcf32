import { defineStore } from "pinia";
import { setMemberTeamID } from "@renderer/views/member/utils/auth";
import { RouteItem } from "./types";
import to from "await-to-js";
import {checkIsAdmin} from "@/api/uni/api/forumAdminApi";
import {getOwnerId} from "@/api/forum/user";
import {OwnerType} from "@/api/forum/models/forum";
interface StorageData {
  teamId: string,
  memberSquare: Array<any>,
  memberRich: Array<any>,
  memberName: Array<any>,
  memberMeterials: Object,
  memberContacts: Array<any> // 联系人列表数据
}
export const useUniStore = defineStore("uni", {
  state: () => ({
    tabs: [],
    activeIndex: 0, // 用这个记录的是路由名字
    activeAccount: null,
    // 扩展一个用于切换主页面panel的
    currentPanel: "",
    isLoadingShow: false, // work-area-head是否出现loading

    quickCreatePosTag: false, // 是否调起快速新增
    isBrushTeam: false, // 是否刷新组织列表
    goTeamId: '', // 需要跳转的teamId



    storePlatforms: [], // 绑定对应用户openid所对应的组织teamId,所选择的端 {openId、teamId、apply}
    // 下面变量为扩展属性
    applyTotals: [], // 入会申请数量
    activeTotals: [], // 激活会员数量


    storageDatas: [], // 按组织存储teamid

    isForumAdminRedirected: false, // 是否已经定位过论坛管理
    isForumAdmin: null, // 是否是论坛管理员
    ownerId: null, // 论坛所有人id
  }),

  getters: {
    getStorageDatas() {
      return this.storageDatas;
    },
    getStorePlatforms() {
      return this.storePlatforms;
    },

    // 如果这样使用，getter 不会缓存，它只会当作一个普通函数使用
    getCountByTeam(state) {
      // console.log(state, 'dddd')
      return (teamId: any) => {
        const applyItem = state.applyTotals.find((v) => v.teamId === teamId);
        const applyCount = applyItem ? applyItem.total : 0;
        const activeItem = state.activeTotals.find((v) => v.teamId === teamId);
        const activeCount = activeItem ? activeItem.total : 0;

        return applyCount + activeCount;
        // return applyCount;
      };
    },

    // 获取非当前商协会下，是否存在待审核
    isIncludeWait() {
      let bool = false;
      if (this.activeAccount) {
        this.applyTotals
          .filter((v) => v.teamId !== this.activeAccount.teamId)
          .map((v) => {
            if (v.total > 0) bool = true;
            return v;
          });
        this.activeTotals
          .filter((v) => v.teamId !== this.activeAccount.teamId)
          .map((v) => {
            if (v.total > 0) bool = true;
            return v;
          });
      }
      return bool;
    },

    // 获取所有商协会的所有待审核数
    getAllTeamAndAll() {
      const applyTotalCount =
        this.applyTotals.length > 0
          ? this.applyTotals
              .map((v) => v.total)
              .reduce((preVal, currVal) => preVal + currVal, 0)
          : 0;
      const activeTotalCount =
        this.activeTotals.length > 0
          ? this.activeTotals
              .map((v) => v.total)
              .reduce((preVal, currVal) => preVal + currVal, 0)
          : 0;
      console.log(applyTotalCount + activeTotalCount);
      return applyTotalCount + activeTotalCount;
    },

    // 获取当前商协会申请入会中待审核的数量总和
    getAllTeamApplyCount() {
      // const teamId = getMemberTeamID()
      // console.log(
      //   "getAllTeamApplyCount",
      //   this.activeAccount,
      //   this.applyTotals,
      //   this.applyTotals.filter(
      //     (v) => v.teamId === this.activeAccount.teamId,
      //     "没数据"
      //   )
      // );
      const arr = this.activeAccount
        ? this.applyTotals.filter((v) => v.teamId === this.activeAccount.teamId)
        : this.applyTotals;
      const result =
        arr.length > 0
          ? arr
              .map((v) => v.total)
              .reduce((preVal, currVal) => preVal + currVal, 0)
          : 0;
      console.log(result);
      return result;
    },
    // 获取当前商协会激活入会中待审核的数量总和
    getAllTeamActiveCount() {
      // const teamId = getMemberTeamID()
      // return this.activeTotals
      //   .filter((v) => v.teamId === this.activeAccount.teamId)
      //   .map((v) => v.total)
      //   .reduce((preVal, currVal) => preVal + currVal, 0);
      const arr = this.activeAccount
        ? this.activeTotals.filter(
            (v) => v.teamId === this.activeAccount.teamId
          )
        : this.activeTotals;
      const result =
        arr.length > 0
          ? arr
              .map((v) => v.total)
              .reduce((preVal, currVal) => preVal + currVal, 0)
          : 0;

      console.log("activeCount:", result);
      return result;
    },

    getAllTeamApplyAndActive() {
      const arr = this.activeAccount
      ? this.applyTotals.filter((v) => v.teamId === this.activeAccount.teamId)
      : this.applyTotals;
      const result1 =
        arr.length > 0
          ? arr
              .map((v) => v.total)
              .reduce((preVal, currVal) => preVal + currVal, 0)
          : 0;


          const arr2 = this.activeAccount
          ? this.activeTotals.filter(
              (v) => v.teamId === this.activeAccount.teamId
            )
          : this.activeTotals;
      const result2 =
        arr2.length > 0
          ? arr2
              .map((v) => v.total)
              .reduce((preVal, currVal) => preVal + currVal, 0)
          : 0;

      return result1 + result2;
    },
    isPersonal() {
      if (!this.activeAccount) return true;
      return this.activeAccount.type === 1;
    }
  },

  actions: {

    setStorageDatas(teamObjs: Array<StorageData>) {
      this.storageDatas = teamObjs;
    },


    setLoadingShow(val) {
      this.isLoadingShow = val;
    },
    setStorePlatforms(arr) {
      this.storePlatforms = arr;
    },
    setGoTeam(val) {
      this.isBrushTeam = !!val;
      this.goTeamId = val;
    },
    setCurrentPanel(val) {
      console.log("为啥我不是方法", val);
      this.currentPanel = val;
    },

    setApplyTotals(totals) {
      this.applyTotals = totals;
    },
    setActiveTotals(totals) {
      this.activeTotals = totals;
    },

    setActiveAccount(item) {
      if (item) {
        setMemberTeamID(item.teamId);
      }
      this.activeAccount = item;
      // localStorage.setItem('project_teamid', item.teamId);
    },


    setQuickCreatePosTag(bool) {
      this.quickCreatePosTag = bool;
    },

    addTab(item: RouteItem, addNew = false): void {
      if (addNew) {
        const index = this.tabs.findIndex((v: RouteItem) => v?.path === item.path && JSON.stringify(v?.query) === JSON.stringify(item.query));
        if (index > -1) {
          this.activeIndex = index;
          return;
        }
      } else {
        const index = this.tabs.findIndex((v: RouteItem) => v.path === item.path);
        // 已存在，更新
        if (index > -1) {
          this.activeIndex = index;
          // 更新标题及参数
          this.tabs[index] = { ...item };
          return;
        }
      }

      // 添加
      this.tabs.push(item);
      this.activeIndex = this.tabs.length - 1;


      // const itemsToAdd = Array.isArray(items) ? items : [items];
      // itemsToAdd.forEach((item) => {
      //   const index = this.tabs.find(
      //     (v: RouteItem) => v.fullPath === item.fullPath
      //   );
      //   if (index) {
      //     this.activeIndex = index.name;
      //     return;
      //   }

      //   this.tabs.push(item);
      //   this.activeIndex = this.tabs[this.tabs.length - 1]?.name;
      // });
    },
    /**
     *
     * @param item
     * @param toItem 移除后，指定跳到
     */
    removeTab(item, toItem?:any) {
      // const item = this.tabs.find((v: RouteItem) => v.fullPath === fullPath);
      // const index = this.tabs.findIndex((v: RouteItem) => v.fullPath === fullPath);
      // console.log(index, unchanged)
      // if (item) {
      //   this.tabs = this.tabs.filter((v) => v.name !== item.name);
      //   if (!unchanged) {
      //     this.activeIndex = this.tabs[Math.max(0, index - 1)]?.name;
      //     console.log('removeAfter:', this.activeIndex)
      //   }
      // }
      // const whiteList = ['/square/search']; && (whiteList.includes(v?.path)
      console.log(this.tabs)

      const index = this.tabs.findIndex((v) => {
        const queryStr = JSON.stringify(v?.query) === JSON.stringify(item.query);
        console.log(v.path, item.path)
        return v?.path === item.path  && queryStr;
      });
      console.log(index)
      if (index > -1) {
        // this.isRemoveTag = true;
        this.tabs.splice(index, 1);

        // 处理特殊情况跳指定tab
        if(toItem) {
          const toIndex = this.tabs.findIndex((v) => {
            const queryStr = JSON.stringify(v?.query) === JSON.stringify(toItem.query);
            console.log(v.path, toItem.path)
            return v?.path === toItem.path  && queryStr;
          });
          console.log(toIndex)
          this.activeIndex = toIndex;
          return;
        }

        // 处理焦点在左侧的时候的逻辑
        if(index < this.activeIndex || index === this.activeIndex ) {
          this.activeIndex = Math.max(0, this.activeIndex - 1);
        }
      }
    },
    removeAllTab() {
      // this.tabs = this.tabs.filter((v) => v.affix);
      // this.tabs = [];
      this.tabs = this.tabs.filter((v) => v.affix);

    },

    switchTab(index: number) {
      if (index === this.activeIndex) return;
      this.activeIndex = index;
    },

    getActiveTab() {
      return this.tabs[this.activeIndex];
      // return this.tabs.find((v) => v.name === this.activeIndex);
    },

    updateTitle(title: string) {
      this.getActiveTab().title = title;
    },
    logout() {
      this.tabs = [];
      // this.squareInfo = null;
    },
    setForumAdminRedirected(val){
      this.isForumAdminRedirected = val;
    },
    setForumAdminPermission(val){
      this.isForumAdmin = val;
    },
    // 获取所有者id
    async loadOwnerId(account) {
      // 先重置状态
      this.isForumAdmin = null;
      this.ownerId = null;

      // 先查询是否是管理员
      const [checkErr, checkRes]= await to(checkIsAdmin());
      if (checkErr) return;

      this.isForumAdmin = checkRes.data.data.is_admin;

      if(!this.isForumAdmin){
        return;
      }

      const [err, res] = await to(getOwnerId({
        ownerType: OwnerType.DigitalPlatform,
        teamId: account.teamId,
        cardId: account.cardId,
        appUuid: 'uni'
      }));
      if (err) return;

      localStorage.setItem('forumAdminOwnerId', res.data.data.ownerId);
      this.ownerId = res.data.data.ownerId;
    },
  },
  // persist: {
  //   key: "member",
  //   storage: localStorage
  // }
});
