/**
 * 快速导出压缩包 需要安装依赖 npm install archiver -D
 * 这个库的文档地址 https://github.com/archiverjs/node-archiver
 */
const path = require("path");
const fs = require('fs');
const fse = require('fs-extra');
const archiver = require('archiver');
const homedir = path.join(__dirname, "../../build/");
const { init } = require("./signature");
async function createZip(buildContext) {
  const {
    outDir,
    artifactPaths,
  } = buildContext;
  fse.mkdirSync(homedir, { recursive: true });
  const artifactPath = path.dirname(artifactPaths[0])
  console.log("App has been packed!");
  console.log("=========================")
  console.log("outDir", outDir)
  console.log("artifactPath", artifactPath)
  console.log("=========================")
  // 判断是否是win
  const isWin = artifactPath.includes("win")


  //const timeString = new Date().toLocaleDateString().replace(/\//g, '-'); // 日期充当hash值防止覆盖之前的压缩包

  //配置要打包的路径列表,需要打包某些目录，添加到数组里面即可 相对路径
  // const target = [
  //   'build/*.yml',
  //   'build/ringkol_mac_universal.dmg',
  //   'build/ringkol_mac_universal.zip',
  //   'build/ringkol_mac_universal.zip.blockmap',
  //   'build/*.exe',
  // ]
  const directory = artifactPath

  const files = [
    'latest-mac.yml',
    'latest.yml',
    'ringkol_mac_universal.dmg',
    'ringkol_mac_universal.zip',
    'ringkol_mac_universal.zip.blockmap',
    'ringkol_win.exe',
    'ringkol_win.exe.blockmap'
  ]

  const distName =  `ringkol-${isWin ? "win" : "mac"}-dist.zip`
  // 默认在当前目录路径生成此文件 dist.zip
  const output = fs.createWriteStream(path.join(homedir, distName));
  const archive = archiver('zip', {
      zlib: { level: 9 } // 设置压缩级别
  });

  archive.on('error', function (err) {
      throw err;
  });

  output.on('close', function () {

      console.log(`
      --------- ---------压缩完毕--------- ---------
      生成文件大小${(archive.pointer() / 1024 / 1024).toFixed(1)}MB
      请在当前项目路径下寻找 dist.zip 文件,系统路径为 ${output}
      ---------如需配置生成路径或文件名,请配置output---------
      `);
  });

  setTimeout(async () => {
    archive.pipe(output);
    if (isWin) {
      // 签名
      await init(artifactPath)
    }
    for (i of files) {
      console.log(`文件 ${i} 存在状态: ${fs.existsSync(path.join(directory, i)) ? '存在' : '不存在'}`);
      archive.file(path.join(directory, i), { name: i});
    }
    archive.finalize();
  }, 1000 * 6)

}

exports.createZip = createZip
