import { Leader } from './listModel';

// 部门/岗位信息
interface Position {
  departmentId: string,
  jobId: number
}

// body参数 编辑成员信息
export interface EditMember {
  avatar: string; // 头像
  name: string; // 姓名
  no: string; // 工号
  role: string; // 员工角色ID标识集合
  position: Array<Position>; // 部门/岗位信息
  leader: string; // 直接上级标识
}


// 批量设置成员的部门及岗位
export interface BatchSetPosition {
  position: Array<Position>; // 部门/岗位信息
  staffIds: Array<number>; // 批量操作的员工ID标识集合
}


// 批量调整直接上级
export interface BatchSetSuperior {
  staffIds: Array<number>; // 批量操作的员工ID标识集合
  Leader: string; // 直接上级的标识
}


// 批量调整成员角色
export interface BatchSetRole {
  staffIds: Array<number>; // 批量操作的员工ID标识集合
  roleId: number; // 角色ID标识
}

