<template>
  <div class="detail-page">
    <title-bar v-if="from === 'im'" />
    <div class="detail-content">
      <div class="niche-src-box" :class="showClass">
        <div v-if="nicheDetail?.id" class="create-info">
        <div class="create-item">
          <div class="label">{{ t("niche.zzmc") }}</div>
          <div class="text">{{ nicheDetail?.apply_team_name }}</div>
        </div>
        <div class="create-item">
          <div class="label">{{ t("niche.sqid") }}</div>
          <div class="text">
            {{ nicheDetail?.apply_team_id }}
          </div>
        </div>
        <div class="create-item">
          <div class="label">{{ t("niche.sqsj2") }}</div>
          <div class="text">{{ nicheDetail?.created_at }}</div>
        </div>
        <div class="create-item">
          <div class="label">{{ t("niche.ztai") }}</div>
          <div class="text">
            <template v-if="nicheDetail?.process_state === 0">
              <div :class="`st-tag0 `">{{ t("niche.status_pending") }}</div>
            </template>
            <template v-if="nicheDetail?.process_state === 1">
              <div :class="`st-tag${nicheDetail?.release_state} `">
                {{ releaseStateText[nicheDetail?.release_state] }}
              </div>
            </template>
            <template v-if="nicheDetail?.process_state === 2">
              <div :class="`st-tag2 `">{{ t("niche.status_rejected") }}</div>
            </template>
          </div>
        </div>
      </div>
      <div v-if="nicheDetail?.id" class="detail-box " >
        <nicheDateil :review="false" :niche-detail="nicheDetail" />
      </div>
      </div>


      <div v-if="from === 'def' && showFooter" class="detail-footer">
        <template v-if="nicheDetail?.process_state === 0 && nicheDetail?.can_examine">
          <t-button class="f-btn" theme="default" @click="examineRun(2)">
            {{ t("niche.juj") }}
          </t-button>
          <t-button class="f-btn" @click="examineRun(1)">
            {{ t("niche.pass") }}
          </t-button>
        </template>
        <template v-if="nicheDetail?.process_state === 1">
          <template v-if="nicheDetail?.release_state === 0">
            <t-button class="f-btn" @click="editHotRun()">
              {{ nicheDetail?.is_top ? t("niche.topcal") : t("niche.top") }}
            </t-button>
          </template>
          <template v-if="nicheDetail?.release_state === 1">
            <t-button class="f-btn" theme="default" @click="delistRun()">
              {{ t("niche.xj") }}
            </t-button>

            <t-button class="f-btn" @click="editHotRun()">
              {{ nicheDetail?.is_top ? t("niche.topcal") : t("niche.top") }}
            </t-button>
          </template>
          <template v-if="nicheDetail?.release_state === 2">
            <t-button class="f-btn" theme="default" @click="publishOne()">
              {{ t("niche.sj") }}
            </t-button>

            <t-button class="f-btn" @click="editHotRun()">
              {{ nicheDetail?.is_top ? t("niche.topcal") : t("niche.top") }}
            </t-button>
            <a style="margin-left: 8px" @click="reonOpen(nicheDetail?.off_reason)">{{ t("niche.reson") }}</a>
          </template>
        </template>
        <template v-if="nicheDetail?.process_state === 2">
          <t-button class="f-btn" @click="reonOpen(nicheDetail?.refuse_reason)">
            {{ t("niche.reson") }}
          </t-button>
        </template>
      </div>
      <template v-else>
        <div
          v-if="(nicheDetail?.process_state === 0 && nicheDetail?.can_examine) || nicheDetail?.process_state === 2"
          class="detail-footer"
        >
          <template v-if="nicheDetail?.process_state === 0 && nicheDetail?.can_examine">
            <t-button class="f-btn" theme="default" @click="examineRun(2)">
              {{ t("niche.juj") }}
            </t-button>
            <t-button class="f-btn" @click="examineRun(1)">
              {{ t("niche.pass") }}
            </t-button>
          </template>

          <template v-if="nicheDetail?.process_state === 2">
            <t-button class="f-btn" @click="reonOpen(nicheDetail?.refuse_reason)">
              {{ t("niche.reson") }}
            </t-button>
          </template>
        </div>
      </template>
    </div>
  </div>
  <delist ref="delistRef" :team-id="teamId" @delist-succ="getDetailRun" />
  <RenewalDialog
    v-if="renewalDialogVisible"
    v-model="renewalDialogVisible"
    :square-id="squareData?.square?.squareId"
    :upgrade="upgrade"
    :team-id="teamId"
    @success="renewalSuccess"
  />
  <OpenSquare v-if="openSquareVisible" v-model="openSquareVisible" @success="renewalSuccess" />
  <t-dialog
    v-model:visible="reVisible"
    :header="t('niche.reson')"
    :on-cancel="reonCancel"
    :on-close="reonCancel"
    width="384px"
    :footer="null"
  >
    <div class="re-con">
      {{ reConValue }}
    </div>
  </t-dialog>
  <turnDown ref="turnDownRef" @delist-succ="getDetailRun" />
</template>

<script setup lang="ts" name="extendDetail">
import TitleBar from "@renderer/components/common/BusinessBar.vue";
import { ClientSide } from "@renderer/types/enumer";
import { computed, onActivated, onMounted, ref } from "vue";
import { useRoute, useRouter } from "vue-router";
import nicheDateil from "@renderer/views/niche/components/nicheHome/nicheDateil.vue";
import { DialogPlugin, MessagePlugin } from "tdesign-vue-next";
import { useI18n } from "vue-i18n";
import delist from "@renderer/views/niche/components/nicheHome/delist.vue";
import turnDown from "@renderer/views/niche/components/nicheHome/turnDown.vue";
import { getTeamAnnualFee } from "@renderer/api/service";
import { getCommonAppAuthAxios } from "@renderer/api/digital-platform/api/businessApi";
import to from "await-to-js";
import { examineDetail, examinePass, exhotSet, examineOn, releaseRelease } from "./apis";
import OpenSquare from "@/views/square/components/OpenSquare.vue";
import RenewalDialog from "@/views/square/components/annual-fee/AnnualFeeDialog.vue";
import LynkerSDK from '@renderer/_jssdk';

const { ipcRenderer } = LynkerSDK;
const emits = defineEmits(["setActiveIndexAndName", "deltabItem"]);
const { t } = useI18n();

const closePage = () => {
  const index = props.tabList.findIndex(
    (e) => e.path === "/workBenchIndex/nicheEdit" || e.path === "/workBenchIndex/nicheCreate",
  );
  emits("deltabItem", index, true, true);
};
const route = useRoute();
const releaseStateText = ref([
  t("niche.status_not_active"),
  t("niche.status_in_progress"),
  t("niche.status_off_shelf"),
  t("niche.status_expired"),
  t("niche.status_deleted"),
]);

const props = defineProps({
  groupList: {
    type: Array,
    default: () => [],
  },
  tabList: {
    type: Array,
    default: () => [],
  },
  tabIndex: {
    type: Number,
    default: 0,
  },
  activationGroupItem: {
    type: Object,
    default: null,
  },
  cloudDiskType: {
    type: Object,
    default: null,
  },
});


const settabItem = () => {
  ipcRenderer.invoke("set-work-bench-tab-item", {
    path: `/workBenchIndex/extendDetail`,
    path_uuid: "niche",
    name: "extendDetail",
    query: route.query,
    title: "审核详情",
    type: ClientSide.NICHE,
  });
};

const nicheDetail: any = ref({});

const squareData = ref(null);
const pageShow = ref(true);
const tipsData = ref({
  con: "",
  btn: "",
});
const renewalDialogVisible = ref(false);
const upgrade = ref(false);
const openSquareVisible = ref(false);
const squareShow = ref(false);
const checkExpiration = (expiredAt) => {
  const now = new Date();
  const expirationDate = new Date(expiredAt);
  return now > expirationDate;
};
const getTeamAnnualFeeRun = async () => {
  const res = await getTeamAnnualFee(teamId.value);
  if (res.data) {
    squareData.value = res.data;
    if (squareData.value.opened) {
      pageShow.value = false;
      squareShow.value = false;
      if (checkExpiration(squareData.value.annualFeeExpiredAt)) {
        pageShow.value = true;
        tipsData.value.con = t("niche.vip_open_tip1");
        tipsData.value.btn = t("niche.vip_open_btn1");
      } else {
        pageShow.value = false;
        if (squareData.value?.annualFeeDetail?.package) {
          const item = squareData.value.annualFeeDetail?.package?.items.find((item) => item.itemType === "NICHE");
          if (!item) {
            tipsData.value.con = t("niche.vip_open_tip2");
            tipsData.value.btn = t("niche.vip_open_btn2");
            upgrade.value = true;
            pageShow.value = true;
          } else {
            pageShow.value = false;
          }
        } else {
          tipsData.value.con = t("niche.vip_open_tip2");
          tipsData.value.btn = t("niche.vip_open_btn2");
          upgrade.value = true;
          pageShow.value = true;
        }
      }
    } else {
      tipsData.value.con = t("niche.vip_open_tip3");
      tipsData.value.btn = t("niche.vip_open_btn3");
      pageShow.value = true;
      squareShow.value = true;
    }
  }
};

const renewalSuccess = () => {
  renewalDialogVisible.value = false;
  openSquareVisible.value = false;
  getTeamAnnualFeeRun();
};

const delSucc = () => {
  // closePage();
  MessagePlugin.success("操作成功");
  getDetailRun();
};

const router = useRouter();

const plazaRenew = () => {
  const myDialog = DialogPlugin({
    header: t("niche.tip"),
    theme: "info",
    body: tipsData.value.con,
    className: "dialog-classp32",
    confirmBtn: tipsData.value.btn,
    onConfirm: () => {
      if (squareShow.value) {
        openSquareVisible.value = true;
      } else {
        renewalDialogVisible.value = true;
      }
      myDialog.hide();
    },
    onCancel: () => {
      myDialog.hide();
    },
  });
};

const figureAuth = ref(false);
const getAppAuth = async () => {
  const [err, res] = await to(getCommonAppAuthAxios({ teamId: teamId.value }, teamId.value));
  if (err) {
    return;
  }
  const { data } = res;
  if (data.data.find((v) => v.uuid === "government")?.auth) {
    console.log("该组织已开启【数字城市】");
    figureAuth.value = true;
    return;
  }
  if (data.data.find((v) => v.uuid === "member")?.auth) {
    console.log("该组织已开启【数字商协】");
    figureAuth.value = true;
    return;
  }
  figureAuth.value = false;
};

const listingRunReq = (id, myDialog) => {
  releaseRelease(id, teamId.value).then((res: any) => {
    console.log(res);
    if (res.data.code === 0) {
      delSucc();
      myDialog.hide();
    }
  });
};
const listingMain = (rowData) => {
  const myDialog = DialogPlugin({
    header: t("niche.tip"),
    theme: "info",
    body: t("niche.sjhhc"),
    className: "dialog-classp32",
    onConfirm: () => {
      listingRunReq(rowData.id, myDialog);
    },
    onCancel: () => {
      myDialog.hide();
    },
  });
};
const listingRun = async (rowData) => {
  console.log(t("niche.sj"));
  if (pageShow.value) {
    plazaRenew();
  } else {
    listingMain(rowData);
  }
};

const getDetailRun = () => {
  examineDetail(nicheId.value, teamId.value).then((res) => {
    if (res.data) {
      nicheDetail.value = res.data.data;
    }
  });
};
const nicheId: any = ref(0);
const teamId: any = ref("");
const from: any = ref("");
onActivated(() => {
  init();
});
onMounted(() => {
  // getTeamAnnualFeeRun();
  // getAppAuth();
  init();
  ipcRenderer.on("update-niche-examine", (event, value) => {
    console.log(event, value);
    nicheId.value = value.id;
    teamId.value = value.teamId;
    from.value = value.from;
    getDetailRun();
  });
});
const init = () => {
  nicheId.value = route.query?.id || 0;
  teamId.value = route.query?.teamId || localStorage.getItem("businessTeamId");
  from.value = route.query?.from || "def";
  if (from.value !== "im") {
    settabItem();
  }
  getDetailRun();
};

const reConValue = ref(null);
const reVisible = ref(null);
const reonCancel = () => {
  reVisible.value = false;
};
const reonOpen = (reason) => {
  reConValue.value = reason;
  reVisible.value = true;
};
const publishOneReq = (myDialog) => {
  examineOn({ id: nicheDetail.value?.id, type: 1 }, teamId.value).then((res: any) => {
    console.log(res);
    if (res.data.code === 0) {
      delSucc();
      myDialog.hide();
    } else {
      // MessagePlugin.error(res.data.message);
    }
  });
};
const publishOne = () => {
  const myDialog = DialogPlugin({
    header: t("niche.tip"),
    theme: "info",
    body: t("niche.jzszs"),
    className: "dialog-classp32",
    confirmBtn: t("niche.sj"),
    onConfirm: () => {
      publishOneReq(myDialog);
    },
    onCancel: () => {
      myDialog.hide();
    },
  });
};

const editHotReq = (hot, row, myDialog) => {
  exhotSet({ is_top: hot }, row.id, teamId.value).then((res: any) => {
    console.log(res);
    if (res.data?.code === 0) {
      delSucc();
      myDialog.hide();
    }
  });
};

const editHotRun = () => {
  console.log(t("niche.top"));
  const val = nicheDetail.value?.is_top ? 0 : 1;
  const tipText = [t("niche.topcl"), t("niche.topop")];
  const myDialog = DialogPlugin({
    header: t("niche.tip"),
    theme: "info",
    body: tipText[val],
    className: "dialog-classp32",
    onConfirm: () => {
      editHotReq(val, nicheDetail.value, myDialog);
    },
    onCancel: () => {
      myDialog.hide();
    },
  });
};

const examinePassReq = (myDialog) => {
  examinePass(nicheDetail.value?.id, teamId.value).then((res: any) => {
    console.log(res);
    if (res.data?.code === 0) {
      myDialog.hide();
      delSucc();
    }
  });
};
const turnDownRef = ref(null);
const examineRun = (type) => {
  if (type === 1) {
    const myDialog = DialogPlugin({
      header: t("niche.tip"),
      theme: "info",
      body: t("niche.sptgh"),
      className: "dialog-classp32",
      confirmBtn: t("niche.pass"),
      onConfirm: () => {
        examinePassReq(myDialog);
      },
      onCancel: () => {
        myDialog.hide();
      },
    });
  } else {
    turnDownRef.value.turnOpen(nicheDetail.value?.id, teamId.value);
  }
};
const delistRef = ref(null);
const delistRun = () => {
  delistRef.value.deOpen(nicheDetail.value, 3, true);
};

const showFooter = computed(() => {
  if (from.value === "def") {
    if (nicheDetail.value?.process_state === 1 && nicheDetail.value?.release_state === 3) {
      return false;
    }
  } else if (nicheDetail.value?.process_state === 1) {
    return false;
  }
  return true;
});
const showClass = computed(() => {
  if (from.value === "def") {
    if (nicheDetail.value?.process_state === 1 && nicheDetail.value?.release_state === 3) {
      return `detail-box-max`;
    }
  } else {
    if (
      (nicheDetail.value?.process_state === 0 && nicheDetail.value?.can_examine) ||
      nicheDetail?.value.process_state === 2
    ) {
      return `detail-box-im`;
    } else {
      return `detail-box-max`;
    }
  }
  return "";
});
</script>

<style lang="less" scoped>
@import "./styles/common.less";
.detail-page {
  display: flex;
  width: 100%;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  border-radius: 0px 0px 16px 0px;
  height: 100vh;
  overflow-y: auto;
  background-image: url(@/assets/niche/bg_small.png);
  overflow-y: auto;
  background-position: center;
  background-size: 100% 100%;
  background-repeat: no-repeat;

  .detail-content {
    width: 1168px;
    height: 100%;
    // overflow-y: auto;
    .tab {
      height: 56px;
      display: flex;
      justify-content: center;
      align-items: center;
      align-self: stretch;
      border-bottom: 1px solid var(--divider-kyy_color_divider_deep, #d5dbe4);
      gap: 44px;

      .item {
        color: var(--brand-kyy_color_brand_default, #4d5eff);
        font-family: "PingFang SC";
        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        line-height: 24px; /* 150% */
        position: relative;
        cursor: pointer;
        .tag {
          width: 16px;
          height: 3px;
          flex-shrink: 0;
          border-radius: 1.5px;
          background: var(--brand-kyy_color_brand_default, #4d5eff);
          position: absolute;
          top: 37px;
          left: 22px;
        }
      }
      .item2 {
        color: var(--text-kyy_color_text_1, #1a2139);
        text-align: center;
        font-family: "PingFang SC";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 24px; /* 150% */
        cursor: pointer;
      }
    }
    .create-info {
      display: flex;
      padding: 12px 16px;
      justify-content: center;
      align-items: flex-start;
      gap: 12px;
      align-self: stretch;
      border-radius: 8px;
      background: var(--bg-kyy_color_bg_light, #fff);
      width: 100%;
      flex-wrap: wrap;
      margin: 12px 0;
      .create-item {
        width: 552px;
        display: flex;
        align-items: center;
        gap: 12px;
        .label {
          width: 56px;
          color: var(--text-kyy_color_text_3, #828da5);

          /* kyy_fontSize_2/regular */
          font-family: "PingFang SC";
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px; /* 157.143% */
        }
        .text {
          color: var(--text-kyy_color_text_1, #1a2139);

          /* kyy_fontSize_2/regular */
          font-family: "PingFang SC";
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px; /* 157.143% */
        }
      }
    }

    .detail-box {
      width: 100%;
      background: #fff;
      padding: 16px 24px;

      border-radius: 8px;
    }

    .placeholder {
      width: 100%;
      height: 56px;
      opacity: 0;
    }
  }

  .detail-footer {
    display: flex;
    height: 64px;
    padding: 16px 24px;
    align-items: center;
    gap: 8px;
    align-self: stretch;
    background: var(--bg-kyy_color_bg_light, #fff);
    width: 100%;
    justify-content: start;
    border-radius: 8px;
    margin-top: 16px;
  }
}
.f-btn {
  width: 80px;
}
.niche-src-box{
  height: calc(100vh - 160px);
  overflow-y: auto;
}
.detail-box-max {
  height: calc(100vh - 80px);
}
.detail-box-im {
  height: calc(100vh - 160px);
}
.niche-src-box::-webkit-scrollbar {
  width: 0px;
}
</style>
