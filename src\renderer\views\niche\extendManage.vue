<template>
  <div class="extendManage">
    <div class="content">
      <div class="tab-bar">
        <div class="tabs">
          <div class="tab-item" :class="{ atv: params.promotion_type === 1 }" @click="tabIndexChange(1)">
            {{ t('niche.gch') }}
            <div v-if="params.promotion_type === 1" class="atv-line"></div>
          </div>
          <div class="tab-item" :class="{ atv: params.promotion_type === 2 }" @click="tabIndexChange(2)">
            {{ t('niche.szpt') }}
            <div v-if="params.promotion_type === 2" class="atv-line2"></div>
          </div>
          <div class="tab-item" :class="{ atv: params.promotion_type === 3 }" @click="tabIndexChange(3)">
            {{ t("niche.lkpt") }}
            <div v-if="params.promotion_type === 3" class="atv-line2"></div>
          </div>
        </div>
        <div class="search">
          <div @keyup.enter="getDataRunDr">
            <t-input
              v-model="params.title"
              style="width: 304px"
              :placeholder="t('niche.sbar')"
              clearable
              @blur="getDataRunDr"
            >
              <template #prefix-icon>
                <iconpark-icon name="iconsearch" class="name-icon" />
              </template>
            </t-input>
          </div>
          <div
            v-if="paramsSuper"
            class="af-icon"
            style="margin-left: 0"
            @click="showFilter"
          >
            <img src="@renderer/assets/approval/icons/fbutton.svg" style="width: 32px; height: 32px" alt="" />
          </div>
          <t-button
            v-else
            style="width: 32px; height: 32px"
            type="button"
            theme="default"
            variant="outline"
            @click="showFilter"
          >
            <iconpark-icon name="iconscreen" class="icon iconscreen" />
          </t-button>



        </div>
      </div>

      <div class="srr-box">
        <div v-if="params.promotion_type === 2" class="btns">
          <t-button
            :theme="params.digital_type === 0 ? 'primary' : 'default'"
            style="height: 28px"
            @click="setDigitalType(0)"
          >
            {{ t('niche.all') }}
          </t-button>
          <t-button
            style="height: 28px"
            :theme="params.digital_type === 1 ? 'primary' : 'default'"
            @click="setDigitalType(1)"
          >
          {{t('niche.szsxx')}}
          </t-button>
          <t-button
            style="height: 28px"
            :theme="params.digital_type === 2 ? 'primary' : 'default'"
            @click="setDigitalType(2)"
          >
          {{t('niche.szzqx')}}
          </t-button>
          <t-button
            style="height: 28px"
            :theme="params.digital_type === 3 ? 'primary' : 'default'"
            @click="setDigitalType(3)"
          >
          {{t('niche.szcbdx')}}
          </t-button>
          <t-button
            style="height: 28px"
            :theme="params.digital_type === 4 ? 'primary' : 'default'"
            @click="setDigitalType(4)"
          >
          {{t('niche.szsq')}}
          </t-button>
        </div>

        <div v-if="paramsSuper" class="filter-res filter-box">
          <div class="tit">{{ t("approval.approval_data.sures") }}</div>
          <div v-if="params.promotion_keyword" class="kword te">
            <span>{{t('niche.qdxx')}}{{ params.promotion_keyword }}</span>
            <span class="close2" @click="clearFilterKey('name')">
              <iconpark-icon name="iconerror" style="font-size: 20px" />
            </span>
          </div>
          <div v-if="params.process_state || params.process_state === 0" class="stat te">
            <span>{{t('niche.shzt')}}{{ nicheStatusLabel() }}</span>
            <span class="close2" @click="clearFilterKey('process_state')">
              <iconpark-icon name="iconerror" style="font-size: 20px" />
            </span>
          </div>
          <div v-if="params.release_state || params.release_state === 0" class="stat te">
            <span>{{t('niche.tgztx')}}{{ channelStatusLabel() }}</span>
            <span class="close2" @click="clearFilterKey('release_state')">
              <iconpark-icon name="iconerror" style="font-size: 20px" />
            </span>
          </div>
          <div v-if="params.created_at_begin || params.created_at_end" class="ov-time te">
            <span>{{ t('niche.sqsj') }} {{ params.created_at_begin }} ~ {{ params.created_at_end }}</span>
            <span class="close2" @click="clearFilterFinish">
              <iconpark-icon name="iconerror" style="font-size: 20px" />
            </span>
          </div>
          <div v-if="params.classify_id" class="kword te">
            <span>{{ t("niche.nrfl") }}{{ classify_name }}</span>
            <span class="close2" @click="clearClassifytree">
              <iconpark-icon name="iconerror" style="font-size: 20px" />
            </span>
          </div>

          <div class="icon" @click="clearFilters">
            <img src="@renderer/assets/approval/icons/del8.svg" alt="" />
            <a>{{ t("approval.approval_data.clearFilters") }}</a>
          </div>
        </div>

        <div class="table-box">
          <t-table
            :row-key="'id'"
            :data="listData"
            cell-empty-content="--"
            :columns="columns"
          >
            <template #approve_status="{ row }">
              <div :class="'status-box' + row.approve_status">
                {{ row.approve_status }}
              </div>
            </template>
            <template #title="{ row }">
              <div class="title-box">
                <div :class="`st-tag${row.effective_state}`">{{ effective_state_text[row.effective_state] }}</div>
                <div class="title-text">
                  <t-tooltip :content="row.title">
                    <span>{{ row.title }}</span>
                  </t-tooltip>
                </div>
              </div>
            </template>
            <template #channel_name="{ row }">
              <div class="title-box">
                <!-- <template v-if="row.process_state === 0">
                  <div :class="`st-tag0`">{{ t('niche.status_pending') }}</div>
                </template>
                <template v-if="row.process_state === 1">
                  <div :class="`st-tag${row.release_state}`">{{ releaseStateText[row.release_state] }}</div>
                </template>
                <template v-if="row.process_state === 2">
                  <div :class="`st-tag2`">{{ t('niche.status_rejected') }}</div>
                </template> -->

              <div :class="`st-tag${row.state}`">{{ releaseStateText2[row.state] }}</div>

                <template v-if="params.promotion_type === 2">
                  <div v-if="row.digital_type === 1" class="type-tag type-tag3">
                    <img class="icon" src="@renderer/assets/niche/type3.png" alt="" />
                    <span>{{t('niche.szsxx')}}</span>
                  </div>
                  <div v-if="row.digital_type === 2" class="type-tag type-tag1">
                    <img class="icon" src="@renderer/assets/niche/type1.png" alt="" />
                    <span>{{t('niche.szzqx')}}</span>
                  </div>
                  <div v-if="row.digital_type === 3" class="type-tag type-tag2">
                    <img class="icon" src="@renderer/assets/niche/type2.png" alt="" />
                    <span>{{t('niche.szcbdx')}}</span>
                  </div>
                  <div v-if="row.digital_type === 4" class="type-tag type-tag4">
                    <img class="icon" src="@renderer/assets/niche/type4.png" alt="" />
                    <span>{{t('niche.szsq')}}</span>
                  </div>
                </template>
                <div v-if="params.promotion_type !== 3" class="title-text">
                  <t-tooltip :content="row.promotion_data?.name">
                    <span>{{ row.promotion_data?.name }}</span>
                  </t-tooltip>
                </div>
                <div v-else class="title-text">{{t('niche.lkfind')}}</div>
              </div>
            </template>
            <template #channel_id="{ row }">
              {{ row.promotion_uuid }}
            </template>
            <template #time="{ row }">
              <div>
                {{ row.created_at }}
              </div>
            </template>
            <template #actions="{ row }">
              <div class="actions">
                <template v-if="row.state === 1">
                    <div class="mbtna">
                      <a @click="delistRun(row)">{{ t('niche.xj') }}</a>
                    </div>
                  </template>

                <template v-if="row.state === 2">
                  <div class="mbtna">
                    <a @click="listingRun(row)">申请上架</a>
                  </div>
                    <div class="mbtna">
                      <a @click="reonOpen(row.off_reason)">{{ t('niche.reson') }}</a>
                    </div>
                  </template>

                  <template v-if="row.state === 5 && row.remind === 0 && row.promotion_type !== 3">
                <div class="mbtna">
                  <a  @click="remindRun(row)">提醒</a>
                </div>
              </template>

                <template v-if="row.state === 6">
                  <div class="mbtna">
                    <a @click="reApp(row)">{{t('niche.reApp')}}</a>
                  </div>
                  <div class="mbtna">
                    <a @click="reonOpen(row.refuse_reason)">{{ t('niche.reson') }}</a>
                  </div>
                </template>
              </div>
            </template>
          </t-table>
        </div>
        <noData v-if="!listData.length" style="margin-top: 100px" :text="t('approval.no_data')" />
        <div v-if="total && total > 10" class="pagination">
          <t-pagination
            :total="total"
            :total-content="false"
            show-previous-and-next-btn
            :show-page-size="false"
            :current="params.page"
            @change="pageChange"
          />
        </div>
      </div>
    </div>
  </div>

  <t-drawer
    v-model:visible="filterVisible"
    :close-btn="true"
    size="472px"
    :header="t('approval.approval_data.sur')"
    class="filterDrawer"
  >
    <div class="form-boxxx">
      <div class="fitem">
        <div class="title">{{t('niche.qdxx2')}}</div>
        <div class="ctl">
          <t-input v-model="drawerForm.promotion_keyword" :maxlength="20" :placeholder="'请输入渠道信息'" />
        </div>
      </div>
      <div class="fitem">
        <div class="title">{{t('niche.qdxx23')}}</div>
        <div class="ctl">
          <t-select
            v-model="drawerForm.release_state"

            :options="nicheOptions"
            clearable
            :placeholder="t('approval.operation.select')"
          ><template #suffixIcon>
            <img src="@/assets/svg/icon_arrow_down.svg" />
          </template>
        </t-select>
        </div>
      </div>
      <div class="fitem">
        <div class="title">{{t('niche.shzt2')}}</div>
        <div class="ctl">
          <t-select
            v-model="drawerForm.process_state"

            :options="channelOptions"
            clearable
            :placeholder="t('approval.operation.select')"
          ><template #suffixIcon>
            <img src="@/assets/svg/icon_arrow_down.svg" />
          </template>
        </t-select>
        </div>
      </div>
      <div class="fitem">
        <div class="title">{{ t('niche.sqsj2') }}</div>
        <div class="ctl">
          <t-date-range-picker
            v-model="drawerForm.finish"
            enable-time-picker
            style="width: 100%"
            :placeholder="[t('approval.approval_data.start_time'), t('approval.approval_data.end_time')]"
            clearable
          >
            <template #suffixIcon>
              <iconpark-icon name="icondate" class="iconorientation" />
            </template>
          </t-date-range-picker>
        </div>
      </div>

      <div class="fitem">
        <div class="title">{{ t('niche.hyfl2') }}</div>
        <div class="ctl">
          <t-cascader
            v-model="drawerForm.classifytree"
            :options="classifytree"
            :keys="{ label: 'name', value: 'id', children: 'children' }"
            clearable
            :placeholder="t('niche.cla_tip')"
            @change="classifytreeChange"
          />
        </div>
      </div>
    </div>
    <template #footer>
      <div class="foot">
        <t-button
          type="button"
          style="width: 80px"
          theme="default"
          @click="initF"
        >
          {{ t("niche.rest") }}
        </t-button>

        <t-button style="width: 80px" type="button" @click="getDataRunDr">
          {{ t("niche.ss") }}
        </t-button>
      </div>
    </template>
  </t-drawer>

  <t-dialog
    v-model:visible="reVisible"
    :header="t('niche.reson')"
    :on-cancel="reonCancel"
    :on-close="reonCancel"
    width="384px"
    :footer="null"
  >
    <div class="re-con">
      {{ reConValue }}
    </div>
  </t-dialog>
  <delist ref="delistRef" @delist-succ="getData" />
</template>

<script setup lang="ts" name="nicheExtendManage">
import { computed, onActivated, onMounted, reactive, ref } from "vue";
import { DialogPlugin, MessagePlugin } from "tdesign-vue-next";
import delist from "@renderer/views/niche/components/nicheHome/delist.vue";
import { useI18n } from "vue-i18n";
import { ClientSide } from "@renderer/types/enumer";
import { useRoute } from "vue-router";
import noData from "@renderer/components/approvalSatellite/approvalData/noData.vue";
import { manageApply,manageRemind, manageList, marketClassifytree } from "./apis";
import LynkerSDK from '@renderer/_jssdk';

const { ipcRenderer } = LynkerSDK;
const { t } = useI18n();

const channelOptions = computed(() => [
  { label: t('niche.status_pending'), value: 0 },
  { label: "已通过", value: 1 },
  { label: t('niche.status_rejected'), value: 3 },
]);
const nicheOptions = computed(() => [
  { label: t('niche.status_not_active'), value: 0 },
  { label: t('niche.status_in_progress'), value: 1 },
  { label: t('niche.status_off_shelf'), value: 2 },
  { label: t('niche.status_expired'), value: 3 },
  { label: t('niche.status_deleted'), value: 4 },
]);
const effective_state_text = ref([t('niche.status_not_active'), t('niche.status_in_progress'), t('niche.status_expired'), t('niche.status_deleted')]);
const releaseStateText = ref([t('niche.status_not_active'), t('niche.status_in_progress'), t('niche.status_off_shelf'), t('niche.status_expired'), t('niche.status_deleted')]);
const releaseStateText2 = ref([
  t("niche.status_not_active"),
  t("niche.status_in_progress"),
  t("niche.status_off_shelf"),
  t("niche.status_expired"),
  t("niche.status_deleted"),
  '待审核',
  '审核已拒绝',
]);
const emits = defineEmits(["setActiveIndexAndName"]);

onActivated(() => {
  settabItem();
});
const props = defineProps({
  groupList: {
    type: Array,
    default: () => [],
  },
  tabList: {
    type: Array,
    default: () => [],
  },
  tabIndex: {
    type: Number,
    default: 0,
  },
  activationGroupItem: {
    type: Object,
    default: null,
  },
  cloudDiskType: {
    type: Object,
    default: null,
  },
});
const route = useRoute();
const settabItem = () => {
  ipcRenderer.invoke("set-work-bench-tab-item", {
    path: `/workBenchIndex/nicheExtendManage`,
    name: 'nicheExtendManage',
    path_uuid: "niche",
    title: "推广管理",
    type: ClientSide.NICHE,
  });
};

onMounted(() => {
  settabItem();
  getMarketClassifytree();
  getData();
});

const changeColumns = (index) => {
  if (index === 3) {
    columns.value = [
      {
        colKey: "title",
        title: t("niche.ni_info"),
        width: "432",
        ellipsis: true,
      },
      {
        colKey: "channel_name",
        title: t('niche.qdmc'),
        width: "320",
        ellipsis: true,
      },
      {
        colKey: "time",
        title: t('niche.sqsj2'),
        width: "200",
      },
      {
        colKey: "actions",
        title: t('niche.opt'),
        width: "192",
      },
    ];
  } else {
    columns.value = [
      {
        colKey: "title",
        title: t("niche.ni_info"),
        width: "320",
        ellipsis: true,
      },
      {
        colKey: "channel_name",
        title: t('niche.qdmc'),
        width: "280",
        ellipsis: true,
      },
      {
        colKey: "channel_id",
        title: "ID",
        width: "240",
        ellipsis: true,
      },
      {
        colKey: "time",
        title: t('niche.sqsj2'),
        width: "200",
      },
      {
        colKey: "actions",
        title: t('niche.opt'),
        width: "144",
      },
    ];
  }
};

const tabIndexChange = (index) => {
  params.promotion_type = index;
  params.page = 1;
  params.pageSize = 10;
  changeColumns(index);
  getDataRun();
};

const classifytree = ref([]);
const getMarketClassifytree = () => {
  const area = props.activationGroupItem.teamRegion;
  marketClassifytree(area).then((res) => {
    console.log(res);
    if (res.data) {
      classifytree.value = res.data.data;
    }
  });
};

const getDataRunDr = () => {
  filterVisible.value = false;
  params.page = 1;
  params.pageSize = 10;
  getDataRun();
};
const clearFilters = () => {
  params.created_at_begin = null;
  params.created_at_end = null;
  params.release_staff_name = "";
  params.process_state = null;
  params.release_state = null;
  params.classify_id = null;
  drawerForm.process_state = null;
  drawerForm.release_state = null;
  drawerForm.classifytree = null;
  drawerForm.promotion_keyword = "";
  drawerForm.finish = [];
  params.page = 1;
  params.pageSize = 10;
  getDataRun();
};

const clearFilterKey = (key) => {
  drawerForm[key] = null;
  params[key] = null;
  getDataRun();
};
const clearFilterFinish = () => {
  drawerForm.finish = [];
  getDataRun();
};
const clearClassifytree = () => {
  params.classify_id = null;
  drawerForm.classifytree = null;
  getDataRun();
};
const paramsSuper = computed(
  () =>
    params.release_state ||
    params.release_state === 0 ||
    params.process_state ||
    params.process_state === 0 ||
    params.promotion_keyword ||
    params.classify_id ||
    params.created_at_end ||
    params.created_at_begin,
);
const pageChange = (e) => {
  params.page = e.current;
  params.pageSize = e.pageSize;
  getData();
};
const listData = ref([]);
const getData = () => {
  manageList(params).then((res: any) => {
    console.log(res);
    if (res.data) {
      listData.value = res.data.data.list;
      total.value = res.data.data.total;
    }
  });
};
const nicheStatusLabel = () => {
  const opt = channelOptions.value.find((item) => item.value === params.process_state);
  console.log(opt);
  return opt.label;
};
const channelStatusLabel = () => {
  const opt = nicheOptions.value.find((item) => item.value === params.release_state);
  console.log(opt);
  return opt.label;
};
const reqParamsHandle = () => {
  params.created_at_begin = drawerForm.finish[0];
  params.created_at_end = drawerForm.finish[1];
  params.promotion_keyword = drawerForm.promotion_keyword;
  params.process_state = drawerForm.process_state;
  params.release_state = drawerForm.release_state;
  params.classify_id = drawerForm.classifytree;
};
const total = ref(0);
const params = reactive({
  title: "",
  promotion_type: 1,
  digital_type: 0,
  release_staff_name: undefined,
  process_state: undefined,
  release_state: undefined,
  promotion_keyword: "",
  classify_name: null,
  created_at_begin: "",
  created_at_end: "",
  classify_id: null,
  page: 1,
  pageSize: 10,
});

const drawerForm = reactive({
  promotion_keyword: "",
  classifytree: null,
  process_state: null,
  release_state: null,
  finish: [],
  type: "",
});
const filterVisible = ref(false);
const showFilter = () => {
  filterVisible.value = true;
};
const columns = ref([
  {
    colKey: "title",
    title: t("niche.ni_info"),
    width: "320",
    ellipsis: true,
  },
  {
    colKey: "channel_name",
    title: t('niche.qdmc'),
    width: "280",
    ellipsis: true,
  },
  {
    colKey: "channel_id",
    title: "ID",
    width: "240",
    ellipsis: true,
  },
  {
    colKey: "time",
    title: t('niche.sqsj2'),
    width: "200",
  },
  {
    colKey: "actions",
    title: t('niche.opt'),
    width: "144",
  },
]);
const getDataRun = () => {
  reqParamsHandle();
  getData();
};

const classify_name = ref("");
const classifytreeChange = (e, ctx) => {
  console.log(e);
  let str = "";
  const item = ctx.node["__tdesign_tree-node__"];
  if (item.parent) {
    str = `${item.parent.label}/${item.label}`;
  } else {
    str = item.label;
  }
  classify_name.value = str;
};

const initF = () => {
  filterVisible.value = false;
  clearFilters();
};

const delistRef = ref(null);
const delistRun = (data) => {
  delistRef.value.deOpen(data, data.source_type === 1 ? 0 : 3);
};

const reConValue = ref(null);
const reVisible = ref(null);
const reonCancel = () => {
  reVisible.value = false;
};
const reonOpen = (reason) => {
  reConValue.value = reason;
  reVisible.value = true;
};

const reAppReq = (id, myDialog) => {
  manageApply(id, 1).then((res: any) => {
    if (res.data?.code === 0) {
      MessagePlugin.success(t('niche.sqcg'));
      myDialog.hide();
      getData();
    }
  });
};

const reApp = (row) => {
  console.log("row", row);
  const myDialog = DialogPlugin({
    header: t("niche.tip"),
    theme: "info",
    body: t("niche.reApp_tips", { name: row.promotion_type === 3 ? t("niche.lkpt") : row.promotion_data.name }),
    className: "dialog-classp32",
    onConfirm: () => {
      reAppReq(row.id, myDialog);
    },
    onCancel: () => {
      myDialog.hide();
    },
  });
};
const setDigitalType = (setDigitalType) => {
  params.digital_type = setDigitalType;
  getData();
};

const remindRunReq = (row) => {
  manageRemind(row.id).then((res: any) => {
    if (res.data.code === 0) {
      MessagePlugin.success("已发送提醒");
      getDataRun();
    }
  });
};



const remindRun = (row) => {
  const myDialog = DialogPlugin({
    header: t("niche.tip"),
    theme: "info",
    body: `仅对当前推广渠道进行审核提醒，不影响其他渠道，确认提醒吗？`,
    className: "dialog-classp32",
    onConfirm: () => {
      remindRunReq(row);
      myDialog.hide();
    },
    onCancel: () => {
      myDialog.hide();
    },
  });
};

const listingRunReq = (id) => {
  manageApply(id, 2).then((res: any) => {
    if (res.data.code === 0) {
      MessagePlugin.success(t("已提交申请"));
      getDataRun();
    }
  });
};

const listingRun = (rowData) => {
  const myDialog = DialogPlugin({
    header: t("niche.tip"),
    theme: "info",
    body: `仅对当前推广渠道进行上架申请，不影响其他渠道，是否继续申请？`,
    className: "dialog-classp32",
    confirmBtn: `申请`,
    onConfirm: () => {
      listingRunReq(rowData.id);
      myDialog.hide();
    },
    onCancel: () => {
      myDialog.hide();
    },
  });
};

</script>

<style lang="less" scoped>
@import "./styles/common.less";

.extendManage {
  height: 100%;
  width: 100vw;
}

.content::-webkit-scrollbar {
  width: 0px;
}
.content {
  padding: 0 16px;

  width: 100%;
  .srr-box::-webkit-scrollbar {
    width: 0px;
  }
  .srr-box {
    height: calc(100vh - 126px);
    overflow-y: auto;
  }
  .tab-bar {
    display: flex;
    justify-content: space-between;
    height: 64px;
    align-items: center;
    border-bottom: 1px solid var(--divider-kyy_color_divider_light, #eceff5);
    margin-bottom: 16px;
    .tabs {
      display: flex;
      gap: 28px;
      width: 100%;
      height: 36px;
      .tab-item {
        color: var(--text-kyy_color_text_1, #1a2139);
        text-align: center;
        font-family: PingFang SC;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        cursor: pointer;
        position: relative;
        display: flex;
        align-items: center;
      }
      .atv-line {
        width: 16px;
        height: 3px;
        border-radius: 1.5px;
        background: var(--brand-kyy_color_brand_default, #4d5eff);
        position: absolute;
        bottom: -14px;
        left: 15px;
      }
      .atv {
        color: var(--brand-kyy_color_brand_default, #4d5eff);
        font-family: PingFang SC;
        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        line-height: 24px; /* 150% */
      }
      .atv-line2 {
        width: 16px;
        height: 3px;
        border-radius: 1.5px;
        background: var(--brand-kyy_color_brand_default, #4d5eff);
        position: absolute;
        bottom: -15px;
        left: 21px;
      }
    }
    .search {
      display: flex;
      gap: 8px;
    }
  }
  .filter-box {
    margin-bottom: 24px;
  }
  .table-box {
    .actions {
      display: flex;
      gap: 8px;
    }
    .title-box {
      display: flex;
      gap: 4px;
      .title-text {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-weight: 600;
        span {
          display: block;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }
  }
}

.iconscreen {
  font-size: 20px;
  color: #828da5;
  cursor: pointer;
}
.iconscreen:hover {
  color: #4d5eff;
}
.name-icon {
  font-size: 20px;
  color: #828da5;
}
.form-boxxx {
  padding: 0 8px;
  .fitem {
    margin-bottom: 24px;
    .title {
      color: var(--text-kyy-color-text-3, #828da5);

      /* kyy_fontSize_2/regular */
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
    }
    .ctl {
      margin-top: 8px;
    }
  }
}
.pagination {
  margin: 15px 0;
}
.btns {
  display: flex;
  gap: 8px;
  margin-bottom: 24px;
}

</style>
