<template>
  <Empty name="no-auth" class="forum-no-permission" :tip="t('forum.noPermission')" />
</template>
<script setup lang="ts">
import Empty from "@/components/common/Empty.vue";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
</script>

<style lang="less" scoped>
.forum-no-permission{
  margin: 0;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
</style>
