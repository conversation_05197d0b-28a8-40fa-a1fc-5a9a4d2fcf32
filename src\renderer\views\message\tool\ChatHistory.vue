<template>
  <div class="drawer-root">
    <div class="chat-drawer-header">
      {{ t("im.public.chat_record") }}
      <div @click="emits('close')" class="iconhover" style="position: absolute; right: 24px; top: 18px">
        <img style="height: 16px" src="@/assets/im/im_close.png" />
      </div>
    </div>
    <div class="drawer-content">
      <t-tabs class="history-tabs" v-model="curTab" @change="onTabChange">
        <t-tab-panel value="all" :label="t('im.public.all1')">
          <div
            class="editSync"
            v-if="msgStore.chatingSession?.conversationType === 3 && [2, 3].includes(setting?.groupInfo?.type)"
          >
            <div
              v-if="
                Boolean(setting?.groupInfo?.attachment?.openPictureFile) ||
                Boolean(setting?.groupInfo?.attachment?.openCloudFile)
              "
            >
              <span class="editSync_label">{{
                t("im.public.syncTip", [setting.groupInfo.members?.[0]?.attachments?.teamName ?? ""])
              }}</span>
              <span class="editSync_value" @click="goToClouddisk">{{ t("im.public.goDisk") }}</span>
            </div>
            <div v-else>
              <span class="editSync_label">{{ t("im.public.saveTip") }}</span>
              <span class="editSync_value" @click="onClickClouddiskSetting">{{ t("im.public.openSync") }}</span>
            </div>
          </div>
          <div style="padding: 0 12px;flex:1;overflow: hidden;">
            <div style="display: flex; gap: 10px" class="searchTab">
              <div style="flex: 1; width: 0">
                <t-input :placeholder="t('im.public.search')" v-model="searchInfo.keyword" @change="(e) => search(1)">
                  <template #prefix-icon>
                    <!-- <SvgIcon name="im-history" class="svg-size20" /> -->
                    <t-tag
                      v-if="searchInfo.date?.length"
                      theme="default"
                      closable
                      :max-width="80"
                      @close="handleClose(index, 'date')"
                    >
                      {{
                        `${moment(searchInfo.date?.[0]).format("YYYY-MM-DD")}-${moment(searchInfo.date?.[1]).format(
                          "YYYY-MM-DD",
                        )}`
                      }}
                    </t-tag>
                    <t-tag
                      v-for="(tag, index) in searchInfo.member.slice(0, 2)"
                      :key="index"
                      theme="default"
                      closable
                      :max-width="80"
                      style="margin-left: 2px"
                      @close="handleClose(index, 'member')"
                    >
                      {{ optionsMember.find((item) => item.value === tag)?.label || tag }}
                    </t-tag>
                    <span v-if="searchInfo.member?.length > 2">+{{ searchInfo.member?.length - 2 }}</span>
                  </template>
                </t-input>
              </div>
              <t-popup placement="bottom-right" trigger="click">
                <t-button variant="text" style="padding: 0">
                  <div
                    style="
                      display: flex;
                      flex-direction: row;
                      align-items: center;
                      gap: 4px;
                      color: var(--color-button_text_secondray-kyy_color_button_text_secondray_font_default, #516082);
                    "
                  >
                    <svg class="svg-size20">
                      <use href="#iconscreen"></use>
                    </svg>
                    {{ t("im.public.filter") }}
                  </div>
                </t-button>
                <template #content>
                  <div style="width: 364px; display: flex; flex-direction: column; gap: 8px; padding: 8px">
                    <div>{{ t("im.public.groupMember") }}</div>
                    <t-select

                      v-model="searchInfo.member"
                      :options="optionsMember"
                      :placeholder="t('im.public.chooseMember')"
                      multiple
                      clearable
                      @change="search"
                    > <template #suffixIcon>
                      <img src="@/assets/svg/icon_arrow_down.svg" />
                    </template>
                  </t-select>
                    <div style="margin-top: 4px">{{ t("im.public.date") }}</div>
                    <!-- @pick="onPick" @change="onChange"  -->
                    <t-date-range-picker
                      allow-input
                      clearable
                      valueType="time-stamp"
                      v-model="searchInfo.date"
                      @change="searchDate"
                    >
                      <template #suffix-icon>
                        <svg class="svg-size20">
                          <use href="#icondate"></use>
                        </svg>
                      </template>
                    </t-date-range-picker>
                  </div>
                </template>
              </t-popup>
            </div>
            <div
              ref="chatHistory"
              class="chat-item"
              @scroll="onScroll"
            >
              <template v-if="allMsgs.length">
                <template v-for="msg in allMsgs" >
                  <div v-if="msg.sentStatus !== 20" class="history-row" @click="gotoMessage(msg)">
                      <chat-avatar :size="28" :message="msg" />
                      <div class="history-msg">
                        <div class="time" style="margin-bottom: 4px">
                          <div>{{ getMsgSenderName(msg) }}</div>
                          <div>{{ getMsgSendTime(msg) }}</div>
                        </div>
                        <message-content class="select-text" :message="{ msg }" from="history" />
                      </div>
                  </div>
                </template>
              </template>
              <div v-else>
                <noData :name="noDataType" />
              </div>
            </div>
          </div>
        </t-tab-panel>
        <t-tab-panel value="file" :label="t('im.public.file1')">
          <div style="padding: 24px 12px 12px 12px;height: 100%;overflow-x: auto;">
            <div style="display: flex; gap: 10px" class="searchFile">
              <div style="flex: 1">
                <t-input :placeholder="t('im.public.search')" v-model="searchInfo.keyword" @change="search">
                  <template #prefix-icon>
                    <SvgIcon name="im-history" class="svg-size20" />
                    <t-tag
                      v-if="searchInfo.date?.length"
                      theme="default"
                      closable
                      :max-width="80"
                      @close="handleClose(index, 'date')"
                    >
                      {{
                        `${moment(searchInfo.date?.[0]).format("YYYY-MM-DD")}-${moment(searchInfo.date?.[1]).format(
                          "YYYY-MM-DD",
                        )}`
                      }}
                    </t-tag>
                    <t-tag
                      v-for="(tag, index) in searchInfo.member.slice(0, 2)"
                      :key="index"
                      theme="default"
                      closable
                      :max-width="80"
                      style="margin-left: 2px"
                      @close="handleClose(index, 'member')"
                    >
                      {{ optionsMember.find((item) => item.value === tag)?.label || tag }}
                    </t-tag>
                    <span v-if="searchInfo.member?.length > 2">+{{ searchInfo.member?.length - 2 }}</span>
                  </template>
                </t-input>
              </div>
              <t-popup placement="bottom-right" trigger="click">
                <t-button variant="text">
                  <div
                    style="
                      display: flex;
                      flex-direction: row;
                      align-items: center;
                      gap: 4px;
                      color: var(--color-button_text_secondray-kyy_color_button_text_secondray_font_default, #516082);
                    "
                  >
                    <svg class="svg-size20">
                      <use href="#iconscreen"></use>
                    </svg>
                    {{ t("im.public.filter") }}
                  </div>
                </t-button>
                <template #content>
                  <div style="width: 364px; display: flex; flex-direction: column; gap: 8px; padding: 8px">
                    <div>{{ t("im.public.groupMember") }}</div>
                    <t-select

                      v-model="searchInfo.member"
                      :options="optionsMember"
                      :placeholder="t('im.public.chooseMember')"
                      multiple
                      clearable
                      @change="search"
                    > <template #suffixIcon>
                      <img src="@/assets/svg/icon_arrow_down.svg" />
                    </template>
                  </t-select>
                    <div style="margin-top: 4px">{{ t("im.public.date") }}</div>
                    <!-- @pick="onPick" @change="onChange"  -->
                    <t-date-range-picker
                      allow-input
                      clearable
                      valueType="time-stamp"
                      v-model="searchInfo.date"
                      @change="searchDate"
                    >
                      <template #suffix-icon>
                        <svg class="svg-size20">
                          <use href="#icondate"></use>
                        </svg>
                      </template>
                    </t-date-range-picker>
                  </div>
                </template>
              </t-popup>
            </div>
            <template v-if="fileMsgs.length">
              <div v-for="msg in fileMsgs" class="history-row">
                <chat-avatar :size="28" :message="msg" />
                <div class="history-msg" style="flex: 1;overflow: hidden; margin-left: 8px" @click="gotoMessage(msg)">
                  <div class="time">
                    <div>{{ msgStore.getMsgSenderName(msg) }}</div>
                    <div>{{ getMsgSendTime(msg) }}</div>
                  </div>
                  <FileEle :msg="msg"/>
                </div>
              </div>
            </template>
            <div v-else>
              <noData :name="noDataType" />
            </div>
          </div>
        </t-tab-panel>
        <t-tab-panel value="media" :label="t('im.public.imgVideo')">
          <div style="padding: 6px 12px; height: 100%;overflow-y: auto;">
            <t-date-range-picker
              class="date-range searchMedia"
              allow-input
              clearable
              valueType="time-stamp"
              v-model="searchInfo.date"
              @change="searchDate"
            >
              <template #suffix-icon>
                <svg class="svg-size20">
                  <use href="#icondate"></use>
                </svg>
              </template>
            </t-date-range-picker>
            <div style="padding: 0 8px 0 12px;">
              <template v-if="mediaMsgs.length">
                <div
                  v-for="(msgs,index) in mediaMsgs"
                  :key="index"
                  class="media-warp"
                >
                  <div style="width: 100%; padding: 12px 0 8px 0">
                    {{ msgs[0] === "thisWeek" ? t("im.public.thisWeek") : msgs[0] }}
                  </div>
                  <div
                    v-for="(msg,ind) in msgs[1]"
                    :key="ind"
                    class="media-item"
                    @click="gotoMessage(msg)"
                  >
                    <img class="media-img" :src="getMsgThumbnail(msg) || ''" />
                    <div class="media-icon">
                      <t-icon
                        v-if="msg.contentExtra?.contentType === 'video'"
                        name="play-circle-stroke"
                        style="color: #fff"
                      />
                    </div>
                  </div>
                </div>
              </template>
              <div v-else>
                <noData :name="noDataType" />
              </div>
            </div>
          </div>
        </t-tab-panel>
        <t-tab-panel value="link" :label="t('im.public.link')">
          <div style="padding: 16px 12px 6px;  height: 100%;overflow-y: auto;">
            <div style="display: flex; gap: 10px" class="searchLink">
              <div style="flex: 1; width: 0">
                <t-input :placeholder="t('im.public.search')" v-model="searchInfo.keyword" @change="search">
                  <template #prefix-icon>
                    <SvgIcon name="im-history" class="svg-size20" />
                    <t-tag
                      v-if="searchInfo.date?.length"
                      theme="default"
                      closable
                      :max-width="80"
                      @close="handleClose(index, 'date')"
                    >
                      {{
                        `${moment(searchInfo.date?.[0]).format("YYYY-MM-DD")}-${moment(searchInfo.date?.[1]).format(
                          "YYYY-MM-DD",
                        )}`
                      }}
                    </t-tag>
                    <t-tag
                      v-for="(tag, index) in searchInfo.member.slice(0, 2)"
                      :key="index"
                      theme="default"
                      closable
                      :max-width="80"
                      style="margin-left: 2px"
                      @close="handleClose(index, 'member')"
                    >
                      {{ optionsMember.find((item) => item.value === tag)?.label || tag }}
                    </t-tag>
                    <span v-if="searchInfo.member?.length > 2">+{{ searchInfo.member?.length - 2 }}</span>
                  </template>
                </t-input>
              </div>
              <t-popup placement="bottom-right" trigger="click">
                <t-button variant="text">
                  <div
                    style="
                      display: flex;
                      flex-direction: row;
                      align-items: center;
                      gap: 4px;
                      color: var(--color-button_text_secondray-kyy_color_button_text_secondray_font_default, #516082);
                    "
                  >
                    <svg class="svg-size20">
                      <use href="#iconscreen"></use>
                    </svg>
                    {{ t("im.public.filter") }}
                  </div>
                </t-button>
                <template #content>
                  <div style="width: 364px; display: flex; flex-direction: column; gap: 8px; padding: 8px">
                    <div>{{ t("im.public.groupMember") }}</div>
                    <t-select

                      v-model="searchInfo.member"
                      :options="optionsMember"
                      :placeholder="t('im.public.chooseMember')"
                      multiple
                      clearable
                      @change="search"
                    >   <template #suffixIcon>
                      <img src="@/assets/svg/icon_arrow_down.svg" />
                    </template>
                  </t-select>
                    <div style="margin-top: 4px">{{ t("im.public.date") }}</div>
                    <!-- @pick="onPick" @change="onChange"  -->
                    <t-date-range-picker
                      allow-input
                      clearable
                      valueType="time-stamp"
                      v-model="searchInfo.date"
                      @change="searchDate"
                    >
                      <template #suffix-icon>
                        <svg class="svg-size20">
                          <use href="#icondate"></use>
                        </svg>
                      </template>
                    </t-date-range-picker>
                  </div>
                </template>
              </t-popup>
            </div>
            <template v-if="linkMsgs.length">
              <div v-for="msg in linkMsgs" class="history-row" @click.capture.stop="gotoMessage(msg)">
                <chat-avatar :size="28" :message="msg" />
                <div class="history-msg">
                  <div class="time" style="margin-bottom: 4px">
                    <div>{{ getMsgSenderName(msg) }}</div>
                    <div>{{ getMsgSendTime(msg) }}</div>
                  </div>
                  <message-content class="select-text" :message="{ msg }" />
                </div>
              </div>
            </template>
            <div v-else>
              <noData :name="noDataType" />
            </div>
          </div>
        </t-tab-panel>
      </t-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive, toRaw, computed, nextTick } from "vue";
import { DialogPlugin, MessagePlugin } from "tdesign-vue-next";

import ChatAvatar from "../chat/ChatAvatar.vue";
import MessageContent from "../chat/MessageContent.vue";
import SvgIcon from "@/components/SvgIcon.vue";
import noData from "@renderer/components/common/Empty.vue";
import FileEle from "@/views/message/chat/msgTypeContent/FileEle.vue";

import { useMessageStore } from "../service/store";
import {
  getMsgSendTime,
  getMsgSenderName,
  msgGroupJumpToCloudDisk,
  getMsgImageSrc,
} from "../service/msgUtils";
import _ from "lodash";
import moment from "moment";
import { useSessionSetting } from "./service/chatSetting";
import { useI18n } from "vue-i18n";
import { loadOpenIMHistory, searchMessages } from "@/views/message/service/msgHistory";
import { getDiskList } from "@renderer/api/cloud.ts";
import LynkerSDK from '@renderer/_jssdk';

const { t } = useI18n();

const emits = defineEmits(["close", "open-setting"]);

const msgStore = useMessageStore();
const setting = useSessionSetting();

const curTab = ref("all");
const allMsgs = ref<MessageToSave[]>([]);
const fileMsgs = ref<MessageToSave[]>([]);
const mediaMsgs = ref([]);
const linkMsgs = ref<MessageToSave[]>([]);
const searchInfo = reactive({
  member: "",
  keyword: "",
  date: [],
  latestMsgSendTime:'',
  pageIndex: 1,
});
const noDataType = ref<"no-data" | "no-result">("no-data"); // 空页面类型，搜索结果就是暂无搜索。没有聊天记录就是暂无数据
let params = { count: 20, lastMinSeq: 0, startClientMsgID: "", conversationID: "" };
let hasMore = true;
let isLoading = false;
const optionsMember = computed(() => {
  const session = msgStore.chatingSession;
  if (!session) {
    return;
  }
  const allMember = msgStore.allMembers;
  const id = session.conversationType === 3 ? session.targetId : session.localSessionId;
  const members = allMember.get(id);
  return Array.from(members)?.map((item) => {
    return {
      ...item?.[1],
      label: item?.[1]?.nickname || item?.[1]?.staffName,
      value: item?.[1]?.cardId,
      openId: item?.[1].openId,
    };
  });
});

const handleClose = (index, type) => {
  if (type === "member") {
    searchInfo.member?.splice(index, 1);
  } else if (type === "date") {
    searchInfo.date = [];
  }
  search();
};
const searchDate = () => {
  console.log(searchInfo);
  if (searchInfo.date?.length === 2 || searchInfo.date?.length === 0) search();
};
const search = _.debounce(async (pageIndex = 1, lastTime?) => {
  console.log('====>searchInfo.keyword', searchInfo.keyword);
  if (searchInfo.keyword || searchInfo.date?.length || searchInfo.member) {
    // hasMore = true;
    // isLoading = false;
    noDataType.value = "no-result";
  }
    params = { count: 20, lastMinSeq: 0, startClientMsgID: "", conversationID: "" };
    hasMore = true;
    isLoading = false;
    pageIndex === 1 && (allMsgs.value = []);
  searchCase(curTab.value, undefined, pageIndex, lastTime);
}, 500);

const searchCase = async (value, isScroll = false, pageIndex = 1, lastTime?) => {
  const conversation = useMessageStore().chatingSession;
  // const pair = conversation.conversationType === 1 ? conversation.localSessionId : null;
  // const group = conversation.conversationType === 3 ? conversation.targetId : null;

  const start = searchInfo.date?.[0] ? moment(searchInfo.date?.[0]).startOf("day").valueOf() : null;
  const end =  lastTime || searchInfo.date?.[1] ? moment(searchInfo.date?.[1]).endOf("day").valueOf() : null;

  // let userIds: string[] = Array.isArray(searchInfo.member) ? toRaw(searchInfo.member) : null;
  // if (userIds?.length) {
  //   userIds = optionsMember.value.filter(item => userIds.includes(item.cardId)).map(item => item.openId);
  // }

  const searchParams = {
    conversationID: conversation.conversationID,
    keywordList: [searchInfo.keyword],
    subContentTypeList: [],
    messageTypeList: [101, 114],
    searchStartTime: start,
    searchEndTime: end,
    senderUserIDList: [],
    pageIndex: Number(pageIndex || 1),
    count: 20,
  };

  switch (value) {
    case "all": {
      if (searchInfo.keyword) {
        searchParams.subContentTypeList = ["file", "link", "text", "location", "richText"];
        const searchData = (await searchMessages(searchParams)) || {};
        searchInfo.pageIndex = searchData.pageIndex;
        searchInfo.latestMsgSendTime = searchData.latestMsgSendTime;
        if (searchData.pageIndex <= 1) {
          allMsgs.value = searchData.result;
        } else {
          if (!allMsgs.value) {
            allMsgs.value = [];
          }
          allMsgs.value = allMsgs.value.concat(searchData.result);
        }
      } else {
        await getHistoryList();
      }
      break;
    }
    case "file": {
      searchParams.subContentTypeList = ["file"];
      const searchData = (await searchMessages(searchParams)) || {};
      searchInfo.pageIndex = searchData.pageIndex;
      if (searchData.pageIndex <= 1) {
        fileMsgs.value = searchData.result;
      } else {
        if (!fileMsgs.value) {
          fileMsgs.value = [];
        }
        fileMsgs.value = fileMsgs.value.concat(searchData.result);
      }
      break;
    }
    case "media": {
      searchParams.subContentTypeList = ["image", "video"];
      searchParams.count = 1000;
      const searchData = (await searchMessages(searchParams));
      searchInfo.pageIndex = searchData.pageIndex;
      const temp = searchData?.result;
      if (searchData.pageIndex <= 1) {
        mediaMsgs.value = _transDataByMonth(temp);
      } else {
        if (!mediaMsgs.value) {
          mediaMsgs.value = [];
        }
        mediaMsgs.value = mediaMsgs.value.concat(_transDataByMonth(temp));
      }
      break;
    }
    case "link": {
      searchParams.subContentTypeList = ["link"];
      const searchData = (await searchMessages(searchParams));
      searchInfo.pageIndex = searchData.pageIndex;
      linkMsgs.value = searchData?.result;
      if (linkMsgs.pageIndex <= 1) {
        linkMsgs.value = searchData.result;
      } else {
        if (!linkMsgs.value) {
          linkMsgs.value = [];
        }
        linkMsgs.value = linkMsgs.value.concat(searchData.result);
      }
      break;
    }
  }
  isScroll && scrollPosition(value);
};

const goToClouddisk = () => {
  getDiskList(setting.groupInfo.members?.[0]?.attachments?.teamId).then((res) => {
    console.log(res, setting.groupInfo,"阿萨大大撒上的");
    if (res.data.data.list.find((ele) => ele.teamId === setting.groupInfo.team)) {
      msgGroupJumpToCloudDisk({
        // teamId: setting.groupInfo.members?.[0]?.attachments?.teamId,
        teamId:setting.groupInfo.team,
        file_id: setting.groupInfo.disk_folder,
      });
    } else {
      MessagePlugin.error(t("clouddisk.nmyqx"));
    }
  });
};

const onClickClouddiskSetting = () => {
  if (!setting.isGroupOwner) {
    const confirmDia = DialogPlugin.confirm({
      header: t("im.public.tips"),
      body: t("im.public.noAuth"),
      confirmBtn: t("im.public.known"),
      cancelBtn: null,
      closeBtn: null,
      closeOnOverlayClick: false,
      theme: "info",
      onConfirm: async () => {
        confirmDia.destroy();
      },
      onClose: () => {
        confirmDia.hide();
      },
    });
  } else {
    emits("open-setting");
  }
};

const onTabChange = async (value) => {
  noDataType.value = "no-data";
  searchInfo.member = "";
  searchInfo.keyword = "";
  searchInfo.date = [];
  searchInfo.latestMsgSendTime = '';
  params = { count: 20, lastMinSeq: 0, startClientMsgID: "", conversationID: "" };
  hasMore = true;
  isLoading = false;
  allMsgs.value = [];
  searchCase(value, true);
};

// 按本周、月分组图片展示
const _transDataByMonth = (arr) => {
  let groupData = new Map();
  arr?.forEach((item) => {
    let month = moment(item.sentTime).format("YYYY-MM");
    if (moment(item.sentTime).isSame(new Date(), "week")) {
      month = "thisWeek";
    }
    if (!groupData.has(month)) {
      groupData.set(month, [item]);
    } else {
      groupData.get(month).push(item);
    }
  });
  return [...groupData.entries()];
};

const getMsgThumbnail = (msg: MessageToSave) => {
  const extraData = msg.contentExtra;
  if (extraData?.contentType === "video") {
    return extraData?.data?.videoImageUrl;
  } if (extraData?.contentType === "image") {
    return getMsgImageSrc(extraData?.data).thumbnail;
  }

  return "";
};

const gotoMessage = (msg: MessageToSave) => {
  msgStore.cancelHighlightedMsg();
  // 同时更新时响应值在一起更新，还是没变
  nextTick(() => {
    msgStore.gotoChatingSessionMessage(msg.messageUId);
  });
};
// 聊天滚动的时候顶部操作栏固定
const scrollPosition = (value) => {
  let navbar = document.querySelector(".t-tabs__nav-container"); //tab栏
  navbar.style.position = "fixed";
  navbar.style.top = "100px";
  navbar.style.width = "100%";
  navbar.style.background = "#fff";
  navbar.style.zIndex = 10;
  let sync = document.querySelector(".editSync"); //云盘
  let search = document.querySelector(".searchTab"); //搜索框
  let nav = document.querySelector(".t-tabs__nav"); //tab设置高度
  //设置tab栏样式
  document.querySelector(".t-tabs__nav-scroll").style.left = "12px";
  let active = document.querySelector(".t-tabs__bar");
  active.style.width = "28px";
  value == "file"
    ? (search = document.querySelector(".searchFile"))
    : value == "media"
    ? (search = document.querySelector(".searchMedia"))
    : value == "all"
    ? (search = document.querySelector(".searchTab"))
    : (search = document.querySelector(".searchLink"));
  value == "file"
    ? (active.style.left = "75px")
    : value == "media"
    ? (active.style.left = "156px")
    : value == "all"
    ? (active.style.left = "16px")
    : (active.style.left = "237px");
  // console.log(document.querySelector('.searchFile'), value, search, '-------------------------------------------------------');
  value == "all" ? (nav.style.height = "70px") : (nav.style.height = "72px");
  search.style.position = "fixed";
  search.style.top = "128px";
  search.style.background = "#fff";
  search.style.zIndex = 10;
};


const getHistoryList = async () => {
  isLoading = true;
  const conversation = useMessageStore().chatingSession;
  const result = await loadOpenIMHistory(conversation, params, 'getHistoryList', true);
  allMsgs.value = allMsgs.value.concat(result.data.reverse());
  params = result.option;
  hasMore = result.hasMore;
  isLoading = false;
  console.log("=====>result", result);
};

const chatHistory = ref(null);
const onScroll = (e) => {
  const { scrollTop, clientHeight, scrollHeight } = chatHistory.value;
  console.error('onScroll', !isLoading, hasMore, scrollTop + clientHeight > scrollHeight - 50);
  if (!isLoading && hasMore && scrollTop + clientHeight > scrollHeight - 50) {
    /**
     * fix：
     * 如果是搜索有关键词 下拉就不返回
     */
    if (searchInfo.keyword) {
      search(searchInfo.pageIndex + 1, searchInfo.latestMsgSendTime);
    } else {
      getHistoryList();
    }
  }

};
onMounted(async () => {
  curTab.value = "all";
  onTabChange("all");
  setting.loadSessionInfo(msgStore.chatingSession);
});
</script>

<style lang="less" scoped>
@import "./style/setting.less";
.t-tabs__nav-container {
  background: transparent;
}

.t-tabs__header {
  height: 300px;
}

.t-tabs__nav-scroll {
  padding-left: 10px;
}

.t-tabs__bar {
  width: 28px !important;
}

.searchTab,
.searchFile,
.searchMedia,
.searchLink {
  width: 450px;
  background: #fff;
  margin: 0px;
  padding: 12px 0;

  button:hover {
    background-color: #eaecff !important;
  }

  button:active {
    background-color: #dbdfff !important;
  }
}
.searchTab{
  width: 415px;
}
.searchMedia {
  width: 344px;
}

.drawer-content {
  .t-tabs__bar {
    display: none;
  }
  .history-tabs{
    height: 100%;
    display: flex;
    flex-direction: column;
    ::v-deep(t-tabs__content){
      flex:1;
      overflow: hidden;
    }
    ::v-deep(.t-tab-panel){
      height: 100%;
      display: flex;
      flex-direction: column;
    }
    ::v-deep(.t-tabs__nav-item.t-size-m){
      height: 28px;
    }
    .chat-item{
      height: 100%;
      flex: 1;
      overflow: hidden auto;
    }
  }
  .editSync {
    padding: 10px 20px;

    .editSync_label {
      font-size: 14px;

      font-weight: 400;
      text-align: left;
      color: #717376;
      line-height: 22px;
      margin-right: 10px;
    }

    .editSync_value {
      font-size: 14px;

      font-weight: 400;
      text-align: center;
      color: #2069e3;
      line-height: 22px;
    }
  }
}

.time {
  font-size: @kyy_fontSize_1;
  color: @kyy_font_3;
  min-width: 36px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  line-height: 20px;
  margin-bottom: 4px;
}

.history-row {
  display: flex;
  flex-direction: row;
  padding: 12px;
  position: relative;

  .content {
    overflow: visible;
    :deep(.avatar) {
      width: 28px;
      height: 28px;
      border-radius: 50%;
    }
  }
  &::after {
    content: "";
    display: block;
    height: 1px;
    background-color: var(--divider-kyy_color_divider_light, #eceff5);
    position: absolute;
    left: 48px;
    right: 12px;
    bottom: 0;
  }

  &:hover {
    border-radius: 8px;
    background: var(--bg-kyy_color_bg_list_hover, #f3f6fa);

    &::after {
      display: none;
    }
  }
}

.history-msg {
  flex: 1;
  margin-left: 8px;
  width: 280px;

  & .chat-text {
    border: none;
    padding: 0;
    background-color: transparent;
  }
}

.history-file-msg {
  border: 1px solid var(--kyy_color_upload_border_default, #d5dbe4);
  background: var(--bg-kyy_color_bg_light, #fff);
  border-radius: 8px;
  padding: 12px;
  display: flex;
  flex-direction: row;
  gap: 8px;
}
.file-name {
  width: 100%;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.date-range {
  display: block;
  padding: 12px;
}

:deep(.t-tabs__nav-container.t-is-top::after) {
  display: none;
}

.iconhover {
  border-radius: 4px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;

  img {
    line-height: 16px;
  }
}

.iconhover:hover {
  background-color: #eaecff;
}

.iconhover:active {
  background-color: #dbdfff;
}
.media-warp {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: flex-start;
  gap: 2px;

  .media-item{
    width: 80px;
    height: 80px;
    position: relative;
    border-radius: 8px;
    display: flex;
    align-items: center;
    overflow: hidden;
    .media-img{
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    .media-icon{
      position: absolute;
      top: 0;
      left: 0;
      bottom: 0;
      right: 0;
      background-color: rgba(150, 150, 150, 0.08);
    }
  }
}
</style>
<style lang="less">
.drawer-root .t-tabs__nav-item-wrapper:hover {
  background-color: #eaecff !important;
}

.drawer-root .t-tabs__nav-item-wrapper:active {
  background-color: #dbdfff !important;
}

.history-row .history-msg {
  .chat-merged, .app-card, .im-app-card, .chat-file-wrap{
    width: 280px !important;
  }
}

</style>