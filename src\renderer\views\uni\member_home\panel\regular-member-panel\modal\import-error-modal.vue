<template>
  <t-dialog
    v-model:visible="visible"
    header="导入异常"
    :z-index="2500"
    attach="body"
    width="480px"
  >
    <template #body>
      <div v-if="errorData" class="toBody">
        <div class="tooltip">
          <img
            class="w-16 h-16 mr-8"
            src="@/assets/member/icon/<EMAIL>"
          />
          有{{ errorData.length }}条异常记录，请修改后重新上传
        </div>
        <t-table
          row-key="line"
          :columns="errorColumns"
          :data="errorData"
          :max-height="420"
        >
          <template #msg="{ row }">
            <div class="msgList">
              <span
                v-for="(msgItem, msgIndex) in row.msg"
                :key="msgIndex"
                class="msgList-item"
              >{{ msgItem }}；</span>
            </div>
          </template>
        </t-table>
      </div>
    </template>

    <template #closeBtn>
      <!-- <iconpark-icon name="close" size="16"></iconpark-icon> -->
      <!-- <svg class="iconpark-icon" style="width: 16px; height: 16px">
        <use href="#close" />
      </svg> -->
      <iconpark-icon name="iconerror" style="font-size: 24px"></iconpark-icon>
    </template>
    <template #footer>
      <div class="operates">
        <!-- <t-button
          theme="default"
          variant="outline"
          class="operates-item"
          @click="onClose"
        >取消</t-button> -->

        <t-button
          theme="primary"
          class="operates-item"
          @click="onClose"
        >确定</t-button>
      </div>
    </template>
  </t-dialog>
</template>

<script lang="ts" setup>
/**
 * @description 选择组织
 * <AUTHOR>
 */
import { debounce } from "lodash";
import { Ref, reactive, ref } from "vue";
import { MessagePlugin } from "tdesign-vue-next";

const emits = defineEmits("reload");
const visible = ref(false);

const errorColumns = [
  { colKey: "line", title: "行数", width: "20%" },
  { colKey: "msg", title: "异常提示", width: "80%" }
];
const errorData = ref(null);

const onOpen = (data) => {
  errorData.value = data;
  visible.value = true;
};
const onClose = () => {
  visible.value = false;
};

defineExpose({
  onOpen,
  onClose
});
</script>

<style lang="less" scoped>
:deep(.t-dialog__header-content) {
  font-size: 16px;
  
  font-weight: 700;
  text-align: left;
  color: #13161b;
}

.msgList {
  display: flex;
  flex-direction: column;
  &-item {
  }
}

.toBody {
  //   max-height: 70vh;
  //   overflow: auto;
  // padding: 0 24px;
  // display: flex;
  // flex-direction: column;
  // align-items: center;
  .tooltip {
    padding: 8px 16px;
    background: #ffeee8;
    border-radius: 4px;

    display: flex;
    align-items: center;
    margin-bottom: 16px;

    font-size: 14px;
    
    font-weight: 400;
    text-align: left;
    color: #13161b;
  }

  .data {
    display: flex;
    flex-direction: column;
    .active {
      background: #daecff;
    }
    &-item {
      display: flex;
      align-items: center;
      padding: 8px;

      border-radius: 8px;
      margin-bottom: 4px;
      transition: all 0.15s linear;
      &:hover {
        background: #daecff;
      }
      .img {
        width: 40px;
        height: 40px;
      }
      .name {
        flex: 1;
        font-size: 14px;
        
        font-weight: 400;
        text-align: left;
        color: #13161b;
        margin-left: 8px;
        margin-right: 8px;
      }
      .icon {
        width: 24px;
        height: 24px;
        color: #2069e3;
      }
    }
  }
}

:deep(.t-form__item) {
  margin-bottom: 10px;
}

.t-dialog__ctx .t-dialog__position {
  padding: 0;
}
</style>
