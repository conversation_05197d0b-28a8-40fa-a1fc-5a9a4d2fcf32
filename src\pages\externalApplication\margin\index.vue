<template>
	<div class="margin-list-box">
		<div class="head-title">保证金列表</div>
		<!-- 头部搜索 -->
		<t-form
			ref="form"
			:data="formData"
			layout="inline"
			label-align="right"
			reset-type="initial"
			class="search-form"
			@reset="onReset"
			@submit="onSearch"
		>
			<t-form-item label="保证金编号" name="bond_sn">
				<t-input v-model="formData.bond_sn" placeholder="请输入保证金编号" class="w-240" @enter="searchFor" />
			</t-form-item>
			<t-form-item label="组织名称" name="team_name">
				<t-input v-model="formData.team_name" placeholder="请输入组织名称" class="w-240" @enter="searchFor" />
			</t-form-item>
			<t-form-item label="应用名称" name="name">
				<t-input v-model="formData.name" placeholder="请输入应用名称" class="w-240" @enter="searchFor" />
			</t-form-item>
			<t-form-item label="状态" name="bond_status">
				<t-select v-model="formData.bond_status" placeholder="请选择状态" class="w-240">
					<t-option label="已支付" :value="2" />
					<t-option label="已退款" :value="3" />
				</t-select>
			</t-form-item>
			<t-form-item label="付款日期" name="payed_at">
				<t-date-range-picker
					v-model="dateRange"
					:placeholder="['开始日期', '结束日期']"
					allow-input
					clearable
					class="w-240"
					@change="onDateChange"
				/>
			</t-form-item>
			<t-form-item label-width="0">
				<t-space size="small">
					<t-button theme="primary" type="submit">搜索</t-button>
					<t-button variant="outline" type="reset">重置</t-button>
				</t-space>
			</t-form-item>
		</t-form>

		<!-- 保证金设置区域 -->
		<div class="margin-setting-box">
			<span class="margin-text">
				{{
					marginConfig?.need === 1
						? `当前保证金：${marginConfig?.region === 'CN' ? '¥' : 'MOP'} ${marginConfig?.bond_amount.toFixed(2)}`
						: '当前保证金：无需支付'
				}}
			</span>

			<t-button v-if="marginConfig?.enable === 1" @click="openMarginSetting">保证金设置</t-button>
		</div>

		<!-- 表格 -->
		<div>
			<t-config-provider>
				<t-table
					row-key="id"
					height="100%"
					hide-sort-tips
					:columns="columns"
					:data="tableData"
					:loading="isLoading"
					:pagination="pagination.total > 10 ? pagination : null"
				>
					<template #name="{ row }">
						<div style="display: flex; align-items: center">
							<img
								v-if="row.picture_linking"
								:src="row.picture_linking"
								alt=""
								style="width: 35px; height: 35px; margin-right: 8px; border-radius: 50%"
							/>
							<!-- <square-avatar v-if="row.team_logo" :square="row" size="32px" /> -->
							<img v-else src="@/assets/yingyong.png" alt="" style="margin-right: 8px" />
							<span>{{ row.name }}</span>
						</div>
					</template>

					<template #bond_status="{ row }">
						<div
							class="status-tag"
							:style="{
								background: getStatusStyle(row.bond_status).background,
								color: getStatusStyle(row.bond_status).color,
							}"
						>
							{{ getStatusText(row.bond_status) }}
						</div>
					</template>
					<template #payed_at="{ row }">
						<div>{{ row.payed_at || '--' }}</div>
					</template>

					<template #operate="{ row }">
						<t-space>
							<t-link theme="primary" @click="viewDetail(row)">详情</t-link>
							<t-link v-if="row.bond_status === 2" theme="primary" @click="refundMargin(row)">退款</t-link>
						</t-space>
					</template>

					<template #empty>
						<div>
							<img
								style="width: 200px; height: 200px; display: block; margin: 49px auto 8px"
								src="@/assets/img/emptyState.png"
							/>
							<div style="font-size: 14px; color: #13161b; text-align: center">暂无数据</div>
						</div>
					</template>
				</t-table>
			</t-config-provider>
		</div>

		<!-- 退款确认弹窗 -->
		<refund-dialog v-model="refundDialogVisible" :refund-id="currentRefundId" @success="handleRefundSuccess" />

		<!-- 保证金设置弹窗 -->
		<margin-setting-dialog v-model="marginSettingVisible" :margin-config="marginConfig" @save="handleMarginSave" />

		<!-- 详情抽屉 -->
		<margin-detail-drawer ref="detailDrawerRef" />
	</div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue';
import MarginSettingDialog from './MarginSettingDialog.vue';
import RefundDialog from './RefundDialog.vue';
import MarginDetailDrawer from './MarginDetailDrawer.vue';
import { getBondList, getBondDetail, BondItem } from '@/api/advertisement';

// 表单数据
const formData = ref({
	bond_sn: '',
	team_name: '',
	name: '',
	bond_status: undefined,
	payed_at: '',
});

const dateRange = ref([]);

// 分页配置
const pagination = reactive({
	current: 1,
	pageSize: 10,
	total: 0,
	showJumper: true,
	onChange: (pageInfo) => {
		pagination.current = pageInfo.current;
		pagination.pageSize = pageInfo.pageSize;
		getList();
	},
});

// 表格数据
const tableData = ref<BondItem[]>([]);
const isLoading = ref(false);
const isMarginLoading = ref(false);

// 退款弹窗
const refundDialogVisible = ref(false);
const currentRefundId = ref<number>(null);

// 详情抽屉
const detailDrawerRef = ref<InstanceType<typeof MarginDetailDrawer>>();

// 保证金设置
const marginConfig = ref<{
	bond_id: number;
	bond_amount: number;
	currency: 'CNY' | 'MOP';
	need: number;
	region: 'CN' | 'MO';
	enable: number;
}>();

const marginSettingVisible = ref(false);

// 表格列配置
const columns = ref([
	{ colKey: 'bond_sn', title: '保证金编号', ellipsis: true, width: 180 },
	{ colKey: 'team_name', title: '组织名称', ellipsis: true },
	{ colKey: 'name', title: '应用信息', ellipsis: true },
	{ colKey: 'bond_status', title: '状态', width: 100 },
	{ colKey: 'payed_at', title: '付款时间', width: 200 },
	{ colKey: 'operate', title: '操作', width: 150 },
]);

const onDateChange = (val: string[]) => {
	formData.value.payed_at = '';
	if (val[0]) {
		formData.value.payed_at += `${val[0]},${val[1]}`;
	}
};

// 获取列表数据
const getList = () => {
	isLoading.value = true;

	getBondList({
		page: pagination.current,
		pageSize: pagination.pageSize,
		...formData.value,
	})
		.then((res) => {
			pagination.total = res.data?.data?.total ?? 0;
			tableData.value = res.data?.data?.items ?? [];
		})
		.finally(() => {
			isLoading.value = false;
		});
};

// 获取保证金设置详情
const getMarginDetail = () => {
	isMarginLoading.value = true;
	getBondDetail()
		.then((res) => {
			marginConfig.value = res.data.data;
		})
		.finally(() => {
			isMarginLoading.value = false;
		});
};

// 搜索
const searchFor = () => {
	pagination.current = 1;
	getList();
};

// 表单提交
const onSearch = () => {
	searchFor();
};

// 表单重置
const onReset = () => {
	dateRange.value = [];
	formData.value = {
		bond_sn: '',
		team_name: '',
		name: '',
		bond_status: '',
		payed_at: '',
	};
	pagination.current = 1;
	getList();
};

// 查看详情
const viewDetail = (row: BondItem) => {
	detailDrawerRef.value?.open(row.app_id);
};

// 退款操作
const refundMargin = (row: BondItem) => {
	currentRefundId.value = row.app_id;
	refundDialogVisible.value = true;
};

// 获取状态样式
const getStatusStyle = (status) => {
	const statusMap = {
		1: { background: '#e3f9e9', color: '#1c8710' }, // 已支付
		2: { background: '#f0f8ff', color: '#2069e3' }, // 已退款
	};
	return statusMap[status] || { background: '#f1f2f5', color: '#13161b' };
};

// 获取状态文本
const getStatusText = (status) => {
	const statusMap = {
		0: '无需支付',
		1: '待支付',
		2: '已支付',
		3: '已退款',
	};
	return statusMap[status] || '--';
};

// 打开保证金设置弹窗
const openMarginSetting = () => {
	marginSettingVisible.value = true;
};

// 保存保证金设置
const handleMarginSave = () => {
	getMarginDetail();
};

// 退款成功回调
const handleRefundSuccess = () => {
	getList();
};

onMounted(() => {
	getList();
	getMarginDetail();
});
</script>

<style lang="less" scoped>
.margin-list-box {
	width: 100%;
	height: 100%;
	background-color: #fff;
	padding: 16px 24px;
	position: relative;
}

.head-title {
	font-size: 16px;
	font-weight: bold;
	color: #13161b;
	margin-bottom: 16px;
}

.search-form {
	.w-240 {
		width: 240px;
		flex-shrink: 0;
	}
}

.status-tag {
	width: 58px;
	height: 24px;
	border-radius: 4px;
	font-size: 14px;
	text-align: center;
	line-height: 24px;
}

.margin-setting-box {
	display: flex;
	align-items: center;
	padding-top: 24px;
	padding-bottom: 24px;
	gap: 10px;
	.margin-text {
		font-size: 16px;
		color: #13161b;
		font-weight: 500;
	}
}
</style>
