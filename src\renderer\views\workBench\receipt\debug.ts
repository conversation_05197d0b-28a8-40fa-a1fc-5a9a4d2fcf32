import { ref, computed } from 'vue';
import { isDevEnvironment } from '@renderer/utils/env';
import { MerchantStatus } from '@renderer/api/workBench/merchant.model';

// 定义收款调试配置类型
export interface ReceiptDebugConfig {
  /** 商户状态 */
  status?: Partial<MerchantStatus>;
  /** mock 场景标识 */
  scene?: MerchantMockScene;
}

// 商户状态模拟场景映射表
// 用于开发和测试阶段，模拟不同商户状态下的界面展示（避免开发过程中过度依赖后端真实数据）
export const merchantMockSceneMap: Record<string, Partial<MerchantStatus>> = {
  未入网: { is_open_merchant: '0', apply_status: '0', is_wechat_merchant: '0', is_alipay_merchant: '0', is_separate_accounts: '0', is_bind: '0', is_receiver_no: '0', merchant_id: 0, message: '' },
  重新入网_签约中: { is_open_merchant: '0', apply_status: '1', is_wechat_merchant: '0', is_alipay_merchant: '0', is_separate_accounts: '0', is_bind: '0', is_receiver_no: '0', merchant_id: 0, message: '' },
  入网审核中: { is_open_merchant: '1', apply_status: '0', is_wechat_merchant: '0', is_alipay_merchant: '0', is_separate_accounts: '0', is_bind: '0', is_receiver_no: '0', merchant_id: 0, message: '' },
  入网失败: { is_open_merchant: '3', apply_status: '0', is_wechat_merchant: '0', is_alipay_merchant: '0', is_separate_accounts: '0', is_bind: '0', is_receiver_no: '0', merchant_id: 0, message: '商户入网-审核失败原因' },
  入网失败_签约成功: { is_open_merchant: '3', apply_status: '2', is_wechat_merchant: '0', is_alipay_merchant: '0', is_separate_accounts: '0', is_bind: '0', is_receiver_no: '0', merchant_id: 0, message: '商户入网-审核失败原因' },
  签约中: { is_open_merchant: '2', apply_status: '1', is_wechat_merchant: '0', is_alipay_merchant: '0', is_separate_accounts: '0', is_bind: '0', is_receiver_no: '0', merchant_id: 0, message: '' },
  入网成功: { is_open_merchant: '2', apply_status: '2', is_wechat_merchant: '0', is_alipay_merchant: '0', is_separate_accounts: '0', is_bind: '0', is_receiver_no: '0', merchant_id: 0, message: '' },
  部分实名: { is_open_merchant: '2', apply_status: '2', is_wechat_merchant: '1', is_alipay_merchant: '0', is_separate_accounts: '0', is_bind: '0', is_receiver_no: '0', merchant_id: 0, message: '' },
  实名成功: { is_open_merchant: '2', apply_status: '2', is_wechat_merchant: '1', is_alipay_merchant: '1', is_separate_accounts: '0', is_bind: '0', is_receiver_no: '0', merchant_id: 0, message: '' },
  可申请分账: { is_open_merchant: '2', apply_status: '2', is_wechat_merchant: '1', is_alipay_merchant: '1', is_separate_accounts: '0', is_bind: '0', is_receiver_no: '0', merchant_id: 12345, message: '' },
  可申请分佣: { is_open_merchant: '2', apply_status: '2', is_wechat_merchant: '1', is_alipay_merchant: '1', is_separate_accounts: '0', is_bind: '0', is_receiver_no: '0', merchant_id: 12345, message: '' },
  分账审核中: { is_open_merchant: '2', apply_status: '2', is_wechat_merchant: '1', is_alipay_merchant: '1', is_separate_accounts: '1', is_bind: '0', is_receiver_no: '0', merchant_id: 0, message: '' },
  分账失败: { is_open_merchant: '2', apply_status: '2', is_wechat_merchant: '1', is_alipay_merchant: '1', is_separate_accounts: '3', is_bind: '0', is_receiver_no: '0', merchant_id: 0, message: '分账失败原因' },
  分账成功: { is_open_merchant: '2', apply_status: '2', is_wechat_merchant: '1', is_alipay_merchant: '1', is_separate_accounts: '2', is_bind: '0', is_receiver_no: '0', merchant_id: 0, message: '' },
} as const;

export type MerchantMockScene = keyof typeof merchantMockSceneMap;

// 是否启用调试模式（但最好提交代码前设置为 false）
const defaultIsDebugEnabled = false;

// 默认调试配置
export const defaultDebugConfig: ReceiptDebugConfig = {
  scene: '未入网',
  // 或自定义状态，优先级高于 scene
  // status: { is_open_merchant: '2', apply_status: '2', ... }
};

// 创建调试状态
export function useDebugState(initialConfig?: Partial<ReceiptDebugConfig>) {
  const isDebugEnabled = ref(defaultIsDebugEnabled);

  // 非开发环境强制关闭
  const isDebugEnabledSafe = computed(() => isDevEnvironment() && isDebugEnabled.value);

  // 合并默认配置和自定义配置
  const debugConfig = ref({
    ...defaultDebugConfig,
    ...(initialConfig || {}),
  });

  return {
    isDebugEnabled: isDebugEnabledSafe,
    debugConfig,
  };
}
