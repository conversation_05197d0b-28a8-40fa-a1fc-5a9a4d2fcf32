<template>
  <div class="container">
    <div class="su-header">
      <div class="more-search">
        <div class="in-box">
          <t-input
            v-model="formData.team_name"
            :placeholder="$t('member.winter.search_organize_name')"
            :maxlength="50"
            clearable
            style="width: 304px"
            @change="onSearch"
          >
            <template #prefix-icon>
              <iconpark-icon name="iconsearch" class="iconsearch"></iconpark-icon>
            </template>
          </t-input>
        </div>
        <!-- <div v-if="paramsSuper" class="af-icon" @click="showFilter">
          <img
            src="@renderer/assets/approval/icons/fbutton.svg"
            style="width: 32px; height: 32px"
            alt=""
          />
        </div>
        <div v-else class="f-icon" @click="showFilter">
          <img
            src="@renderer/assets/approval/icons/icon_screen.svg"
            style="width: 20px; height: 20px"
            alt=""
          />
        </div> -->
        <div :class="{'f-icon': true, 'factive': paramsSuper }" @click="showFilter">
          <iconpark-icon name="iconscreen" class="iconscreen"></iconpark-icon>
        </div>
      </div>
      <!-- <div class="opt">
        <t-button theme="primary" variant="base" @click="onInviteJobClub">
          邀请入会</t-button>
      </div> -->
    </div>

    <div class="body">
      <div v-if="paramsSuper" class="filter-res">
        <div class="tit">{{ t("approval.approval_data.sures") }}</div>
        <div v-if="formData.status" class="stat te">
          <span> {{ $t('member.winter_column.apply_status') }}： {{ formData.status_text }}</span>
          <span class="close2" @click="clearStatus">
            <iconpark-icon class="closeIcon" name="iconerror"></iconpark-icon>
          </span>
        </div>
        <div v-if="formData.dateTime.length" class="kword te">
          <span>{{ $t('member.winter_column.apply_1') }}：{{ formData.dateTime[0] }}~{{
            formData.dateTime[1]
          }}</span>
          <span class="close2" @click="clearDateTime">
            <iconpark-icon class="closeIcon" name="iconerror"></iconpark-icon>
          </span>
        </div>
        <div v-if="formData.level" class="ov-time te">
          <span>{{ t("member.svip.member_level") }}：
            {{ formData.level_text }}</span>
          <span class="close2" @click="clearlevel">
            <iconpark-icon class="closeIcon" name="iconerror"></iconpark-icon>
          </span>
        </div>
        <div v-if="formData.telephone" class="ov-time te">
          <span>{{ t("member.regular.phone") }}： {{ formData.telephone }}</span>
          <span class="close2" @click="clearFiltertelephone">
            <iconpark-icon class="closeIcon" name="iconerror"></iconpark-icon>
          </span>
        </div>
        <div v-if="formData.type" class="ov-time te">
          <span>{{ t("member.digital.j") }}： {{ showTextType(formData.type) }}</span>
          <span class="close2" @click="clearType">
            <iconpark-icon class="closeIcon" name="iconerror"></iconpark-icon>
          </span>
        </div>
        <div v-if="formData.name" class="ov-time te">
          <span>{{ t("member.regular.respect") }}： {{ formData.name }}</span>
          <span class="close2" @click="clearName">
            <iconpark-icon class="closeIcon" name="iconerror"></iconpark-icon>
          </span>
        </div>
        <div v-if="formData.group !== undefined" class="ov-time te">
          <span>分组：
            {{ formData.group_text }}</span>
          <span class="close2" @click="clearGroup">
            <iconpark-icon class="closeIcon" name="iconerror"></iconpark-icon>
          </span>
        </div>
        <div class="icon" @click="clearFilters">
          <img src="@renderer/assets/approval/icons/del8.svg" alt="" />
          <a>{{ t("approval.approval_data.clearFilters") }}</a>
        </div>
      </div>
      <div class="body-content">
        <!-- <t-form
          v-if="false"
          ref="form"
          :rules="FORM_RULES"
          class="searchForm"
          :data="formData"
          layout="inline"
          :label-align="'right'"
          :colon="false"
        >
          <t-form-item
            name="team_name"
            class="searchForm-item"
            label-width="64px"
          >
            <template #label>
              <div>  {{ t("member.regular.name") }}</div>
            </template>
            <t-input
              v-model="formData.team_name"
              class="searchForm-item-input"
              :maxlength="50"
              clearable
              placeholder="请输入姓名"
              @enter="onEnter"
            />
          </t-form-item>
          <t-form-item name="status" class="searchForm-item" label-width="64px">
            <template #label>
              <div> {{ t("member.winter.apply_status") }}</div>
            </template>
            <t-select
              v-model="formData.status"
              class="searchForm-item-input"
              :options="statusOptions"
              clearable
            ><template #suffixIcon>
              <img src="@/assets/svg/icon_arrow_down.svg" />
            </template>
          </t-select>
          </t-form-item>
          <t-form-item
            name="dateTime"
            class="searchForm-item"
            :label-align="'right'"
            label-width="64px"
          >
            <template #label>
              <div>

                {{ t("member.winter.apply_time") }}</div>
            </template>
            <t-date-range-picker
              v-model="formData.dateTime"
              :placeholder="[
                t('approval.approval_data.start_time'),
                t('approval.approval_data.end_time')
              ]"
              style="width: 244px"
              class="!w-240"
              clearable
            />
          </t-form-item>
          <t-form-item name="level" class="searchForm-item" label-width="64px">
            <template #label>
              <div>
                {{ t('member.winter_column.organize_3') }}
              </div>
            </template>
            <t-select
              v-model="formData.level"
              class="searchForm-item-input"
              :options="levelOptions"
              clearable
            ><template #suffixIcon>
              <img src="@/assets/svg/icon_arrow_down.svg" />
            </template>
          </t-select>
          </t-form-item>

          <t-form-item name="type" class="searchForm-item" label-width="64px">
            <template #label>
              <div>

                {{ t('member.active.membership_type') }}
              </div>
            </template>
            <t-select
              v-model="formData.type"
              class="searchForm-item-input"
              :options="typeOptions"
              clearable
            ><template #suffixIcon>
              <img src="@/assets/svg/icon_arrow_down.svg" />
            </template>
          </t-select>
          </t-form-item>

          <t-form-item
            v-show="isMoreSearch"
            name="telephone"
            class="searchForm-item"
            label-width="64px"
          >
            <template #label>
              <div>手机号</div>
            </template>
            <t-input
              v-model="formData.telephone"
              class="searchForm-item-input"
              :maxlength="50"
              clearable
              placeholder="请输入"
              @enter="onEnter"
            />
          </t-form-item>
          <t-form-item
            v-show="isMoreSearch"
            name="name"
            class="searchForm-item"
            label-width="74px"
          >
            <template #label>
              <div>代表人姓名</div>
            </template>
            <t-input
              v-model="formData.name"
              class="searchForm-item-input"
              :maxlength="50"
              clearable
              placeholder="请输入"
              style="width: 190px"
              @enter="onEnter"
            />
          </t-form-item>

          <div class="searchForm-buttons">
            <t-button theme="primary" variant="base" @click="onSearch">
              搜索</t-button>
            <t-button theme="default" variant="outline" @click="onReset">
              重置</t-button>
          </div>
          <div class="searchForm-buttons">
            <t-link
              theme="primary"
              class="ml-16"
              hover="color"
              @click="setMoreSearch"
            >
              {{ isMoreSearch ? $t('member.winter_column.close_1') : $t('member.winter_column.close_2') }}
            </t-link>
          </div>
        </t-form> -->

        <div class="table">
          <t-table
            row-key="id"
            :columns="memberColumns"
            :pagination="pagination.total > 10 ? pagination : null"
            :data="memberData"
          >
            <template #main="{ row }">
              <div class="main_body">
                <span
                  v-show="row.type === 1"
                  class="main_body-item"
                  style="overflow: hidden; text-overflow: ellipsis"
                >
                  {{ $filters.isPeriodEmpty(row.team_name) }}
                </span>
                <span class="main_body-item">
                  {{ $filters.isPeriodEmpty(row.name) }}
                </span>

                <span class="main_body-item phone">
                  +{{ $filters.isPeriodEmpty(row.telCode) }}

                  {{ $filters.isPeriodEmpty(row.telephone) }}
                </span>
              </div>
            </template>
            <template #level_name="{ row }">
              <div>{{ $filters.isPeriodEmpty(row.level_name) }}</div>
            </template>
            <template #status="{ row }">
              <div :class="showClassStatus(row.status)">
                {{ showTextStatus(row.status) }}
              </div>
            </template>
            <template #type="{ row }">
              <div class="status">{{ showTextType(row.type) }}</div>
            </template>
            <template #pay_status="{ row }">
              <div class="status">{{ showTextFee(row.status) }}</div>
            </template>
            <template #group="{ row }">
              <t-tooltip placement="top">
                <template #content>
                  <!-- {{ row.group_arr && row.group_arr.length > 0 ? row.group_arr.map(v=>v.group_name).join(',') : '--' }} -->
                  {{ row?.group_name || '--'}}
                </template>
                <div class="line-2">
                  <!-- {{ row.group_arr && row.group_arr.length > 0 ? row.group_arr.map(v=>v.group_name).join(',') : '--' }} -->
                  {{ row?.group_name || '--'}}
                </div>
              </t-tooltip>
            </template>

            <!-- 13 -->
            <template #operate="{ row }">
              <span class="operates">
                <span class="optn" @click="onLookDetail(row)">
                  {{ $t('member.winter_column.close_3') }}
                </span>
              </span>
            </template>

            <template #empty>
              <div class="empty">
                <noData :text="$t('engineer.no_data')" />
              </div>
            </template>
          </t-table>
        </div>
      </div>
    </div>
  </div>

  <t-drawer
    v-model:visible="filterVisible"
    :close-btn="true"
    size="472px"
    :header="t('approval.approval_data.sur')"
  >
    <div class="form-boxxx">
      <div class="fitem">
        <div class="title"> {{ $t('member.winter_column.apply_status') }}</div>
        <div class="ctl">
          <t-select
            v-model="paramsTemp.status"
            clearable
            :options="statusOptions"
            style="width: 422px"
            :popupProps="{
              overlayInnerStyle: {
                width: '420px',
              },
            }"
            @change="statusChange"
          ><template #suffixIcon>
            <img src="@/assets/svg/icon_arrow_down.svg" />
          </template>
        </t-select>
        </div>
      </div>
      <div class="fitem">
        <div class="title">{{ $t('member.winter_column.apply_1') }}</div>
        <div class="ctl">
          <t-date-range-picker
            v-model="paramsTemp.dateTime"
            :placeholder="[
              $t('approval.approval_data.start_time'),
              $t('approval.approval_data.end_time')
            ]"
            style="width: 422px"
            clearable
          />
        </div>
      </div>
      <!-- <div class="fitem">
        <div class="title">{{ t("member.svip.member_level") }}</div>
        <div class="ctl">
          <t-select v-replace-svg
            v-model="paramsTemp.level"
            :options="levelOptions"
            clearable
            style="width: 422px"
            @change="levelChange"
          />
        </div>
      </div> -->
      <div class="fitem">
        <div class="title">{{ t("member.regular.phone") }}</div>
        <div class="ctl">
          <t-input
            v-model="paramsTemp.telephone"
            class="searchForm-item-input"
            :maxlength="50"
            clearable
            :placeholder="$t('member.regular.please_input')"
            style="width: 422px"
          />
        </div>
      </div>
      <div class="fitem">
        <div class="title">
          <!-- 类型 -->
          {{ t("member.digital.j") }}
        </div>
        <div class="ctl">
          <t-select
            v-model="paramsTemp.type"
            clearable
            :options="typeOptions"
            style="width: 422px"
          ><template #suffixIcon>
            <img src="@/assets/svg/icon_arrow_down.svg" />
          </template>
        </t-select>
        </div>
      </div>
      <!-- <div class="fitem">
        <div class="title">{{ t("member.regular.respect") }}</div>
        <div class="ctl">
          <t-input
            v-model="paramsTemp.name"
            class="searchForm-item-input"
            :maxlength="50"
            clearable
            :placeholder="'搜索代表人名称'"
            style="width: 422px"
          />
        </div>
      </div> -->
      <div class="fitem" v-show="groupOptions?.length > 0">
        <div class="title">分组</div>
        <div class="ctl">
          <t-select
            v-model="paramsTemp.group"
            :options="groupOptions"
            :popupProps="{
              overlayInnerStyle: {
                width: '420px',
              },
            }"
            clearable
            :keys="{label: 'group_name', value: 'id'}"
            style="width: 422px"
            @change="groupOptionsChange"
          >  <template #suffixIcon>
              <img src="@/assets/svg/icon_arrow_down.svg" />
            </template>
        </t-select>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="foot">
        <div class="btn1" @click="footerCla">
          {{ t("approval.approval_data.cl") }}
        </div>
        <div v-if="paramsSuperFoot" class="btn3" @click="getDataRunDr">
          {{ t("approval.approval_data.cm") }}
        </div>
        <div v-else class="btn2">
          {{ t("approval.approval_data.cm") }}
        </div>
      </div>
    </template>
  </t-drawer>

  <InviteQrcodeModal ref="inviteQrcodeModalRef" />
  <LookApplyModal ref="lookApplyModalRef" @reload="onSearch" />
</template>

<script lang="ts" setup>
import noData from "@renderer/components/approvalSatellite/approvalData/noData.vue";
import { ref, reactive, watch, computed } from "vue";
import { useI18n } from "vue-i18n";
import {
  getMemberJobsListAxios,
  getMemberApplyListAxios,
  getMemberApplyLinkAxios,
  getMemberApplyDetailAxios,
  getAllGroupListAxios
} from "@renderer/api/uni/api/businessApi";
import { getResponseResult } from "@renderer/utils/myUtils";
import { MessagePlugin, FormRule, DialogPlugin } from "tdesign-vue-next";
import InviteQrcodeModal from "@renderer/views/uni/member_home/panel/apply-member-panel/modal/invite-qrcode-modal.vue";
import LookApplyModal from "@renderer/views/uni/member_home/panel/apply-member-panel/modal/look-apply-modal.vue";
import { updateAllCount } from "@renderer/views/uni/hooks/total";
import { useUniStore } from "@renderer/views/uni/store/uni";
import { allRoutersUuid } from "@renderer/constants/index";
import { useDigitalPlatformStore } from "@renderer/views/digital-platform/store/digital-platform-store";
import { useRoute } from "vue-router";
import { platform } from "@renderer/views/digital-platform/utils/constant";
import { getUniTeamID } from "@renderer/views/uni/utils/auth";
import { onActivated } from "vue";
import { onMountedOrActivated } from "@renderer/hooks/onMountedOrActivated";
import to from "await-to-js";
import LynkerSDK from "@renderer/_jssdk";
const { ipcRenderer } = LynkerSDK;

const store = useUniStore();
const digitalPlatformStore = useDigitalPlatformStore();
const route = useRoute();

const { t } = useI18n();

const statusOptions = [
  // 申请状态
  //   { label: "全部", value: 0 },
  // { label: "待审核", value: 1 },
  // { label: "已入会", value: 2 },
  // { label: "已驳回", value: 3 }

  { label: t('member.winter_column.statusOptions_2'), value: 1 },
  { label: '已通过', value: 2 },
  { label: '已拒绝', value: 3 },
  { label: '已退出', value: 4 },
  { label: '已失效', value: 5 },
  // { label: t('member.bing.a'), value: 4 },
];
const levelOptions = ref([
  //   {
  //     label: "全部",
  //     value: 0
  //   }
]);

const props = defineProps({
  platform: {
    type: String,
    default: '',
  },
})
// 平台类型 目前只有digital-platform
const platformCpt = computed(() => {
  return props.platform || route.query?.platform
})

const activeAccount = computed(() => {
  if(platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount
  } else if(platformCpt.value === platform.digitalWorkbench)  {
    return route.query;
  } else {
    return store.activeAccount
  }
})


const currentTeamId = computed(() => {
  if(platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount?.teamId
  } else if(platformCpt.value === platform.digitalWorkbench){
    return route.query?.teamId || 0;
  } else {
    return  getUniTeamID()
  }
})


const typeOptions = [
  // 入会类型
  //   { label: "全部", value: 0 },
  { label: '成员', value: 1 },
  { label: '个人', value: 2 }
];

const FORM_RULES: Record<string, FormRule[]> = {
  // phone: [{ required: false, message: '内容超出', max: 10 }],
};
const formData = reactive({
  team_name: "", // 会员名称
  status: undefined, // 申请状态 1：待审核，2：已通过，3：已驳回
  dateTime: [], // 申请开始、结束时间
  level: undefined, // 会员级别
  type: undefined, // 入会类型
  name: "", // 代表人姓名
  telephone: "", // 手机号
  group: undefined, // 分组
  level_text: undefined,
  status_text: undefined,
  group_text: undefined,
});
const lookApplyModalRef = ref(null);
const form = ref(null);
const isMoreSearch = ref(false);
const memberColumns = ref([
  { colKey: "main", title: t('member.impm.app_1'), width: "20%", ellipsis: true },
  { colKey: "group", title:  '加入分组', width: `16%`, ellipsis: false },
  { colKey: "type", title: '类型', width: "10%", ellipsis: true },
  // { colKey: "level_name", title: t('member.impm.app_3'), width: "10%", ellipsis: true },
  { colKey: "apply_time", title: t('member.impm.app_4'), width: "13%", ellipsis: true },
  { colKey: "status", title: t('member.impm.app_5'), width: "10%", ellipsis: true },
  // { colKey: "pay_status", title: t('member.impm.app_6'), width: "10%", ellipsis: true },
  { colKey: "operate", title: t('member.impm.app_7'), width: "10%", ellipsis: true }
]);
const emits = defineEmits(["onPage", "onBrush"]);
const memberData = ref([]);
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
  onChange: (pageInfo) => {
    console.log("pagination.onChange", pageInfo);
    pagination.current = pageInfo.current;
    pagination.pageSize = pageInfo.pageSize;

    // getMemberList({});
    onSearch();
  }
});
// 会费状态
const showTextFee = (val) => {
  let msg = "";
  // 1：未缴费，2：已缴费，3：无需缴费
  switch (val) {
    case 1:
      msg = t('member.winter.no_fee1');
      break;
    case 2:
      msg = t('member.winter.no_fee2');
      break;
    case 3:
      msg = t('member.winter.no_fee3');
      break;
    default:
      msg = "--";
  }
  return msg;
};
let groupOptions = ref([]);

const showClassStatus = (val) => {
  // 申请状态，1：待审核，2：已入会，3：已驳回
  let result = {};
  if (val === 1) {
    result = { wait: true };
  } else if (val === 2) {
    result = { success: true };
  } else if (val === 3) {
    result = { reject: true };
  } else {
    result = {
      gray: true
    };
  }
  return result;
};
const showTextStatus = (val) => {
  const option = statusOptions.find((v: any) => v.value === val);
  if (option) {
    return option.label;
  }
  return "--";
};
// 入会类型
const showTextType = (val) => {
  const option = typeOptions.find((v: any) => v.value === val);
  if (option) {
    return option.label;
  }
  return "--";
};

// watch(
//   () => store.activeAccount,
//   (val) => {
//     if (val) {
//       // updateAllCount();
//       onSearch();
//     }
//   },
//   {
//     deep: true
//     // immediate: true
//   }
// );

const setMoreSearch = () => {
  isMoreSearch.value = !isMoreSearch.value;
};

const onReset = () => {
  form.value.reset();
  onSearch();
};
const onSearch = () => {
  const params = {
    ...formData,
    group_id: formData.group,
    apply_time_start: formData.dateTime.length > 0 ? formData.dateTime[0] : "",
    apply_time_end: formData.dateTime.length > 1 ? formData.dateTime[1] : ""
  };
  getMemberList(params);
  // updateAllCount();
  // ipcRenderer.send(
  //   "update-nume-index",
  //   allRoutersUuid.findIndex((v) => v === "government")
  // );
  emits('onBrush')
};

// 邀请入会
/* const inviteQrcodeModalRef = ref(null);
const onInviteJobClub = () => {
  if (levelOptions.value.length < 1) {
    const confirmDia = DialogPlugin({
      header: "未设置会员职务",
      theme: "info",
      body: "请先前往设置会员职务与会员级别，再添加会员",
      closeBtn: null,
      confirmBtn: "立即设置",
      className: "delmode",
      onConfirm: async () => {
        confirmDia.hide();
        emits("onPage", "PMembershipPositionPanel");
      },
      onClose: () => {
        confirmDia.hide();
      }
    });
    return;
  }

  getInviteLinkAxios().then((val) => {
    inviteQrcodeModalRef.value.onOpen(val);
  });
}; */

// 获取邀请链接
/* const getInviteLinkAxios = () => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await getMemberApplyLinkAxios();
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      resolve(result.data.link);
    } catch (error) {
      reject();
      const errMsg = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
    }
  });
}; */

// 获取入会申请详情
const onGetMemberApplyDetailAxios = (res) => {
  let result = null;
  console.log(res,'uniiiiiiiiiiiiii');
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await getMemberApplyDetailAxios(res.id);
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      resolve(result.data);
    } catch (error) {
      reject();
      const errMsg = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
    }
  });
};
// getMemberApplyDetailAxios

// 获取入会申请列表
const getMemberList = async (params) => {
  // teamId
  params.page = pagination.current;
  params.pageSize = pagination.pageSize;

  if (params.team_name) {
    params.keyword = params.team_name;
    delete params.team_name;
  }
  try {
    let result = await getMemberApplyListAxios(params, currentTeamId.value);
    console.log(result);
    result = getResponseResult(result);
    if (!result) return;
    memberData.value = result.data.list;
    pagination.total = result.data.total;
    console.log(memberData.value);
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    MessagePlugin.error(errMsg);
  }
};

// 获取会员职务列表
const getMemberJobsList = async () => {
  // teamId
  // params.page = pagination.current;
  // params.pageSize = pagination.pageSize;
  try {
    let result: any = await getMemberJobsListAxios();
    console.log(result);
    result = getResponseResult(result);
    if (!result) return;

    levelOptions.value = [
      ...result.data.map((v) => ({
        label: v.level_name,
        value: v.id
      }))
    ];
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    MessagePlugin.error(errMsg);
  }
};

const initData = () => {
  // getMemberJobsList();
  getMemberList({});
};
// initData();

onMountedOrActivated(() => {
  // updateAllCount();
  initData();
});

// onActivated(() => {
//   // updateAllCount();
//   initData();
// });



const onLookDetail = (row: any) => {
  onGetMemberApplyDetailAxios(row).then((res: any) => {
    lookApplyModalRef.value.onOpen(res);
  });
};

const onEnter = () => {};
const getGroupList =  () => {
  return new Promise(async(resolve, reject) => {
    const [err, res] = await to(getAllGroupListAxios({},currentTeamId.value));

    if (err) {
      reject(err);
      return;
    }
    const { data }:any = res;
    resolve( data?.data?.list || []);
  });


};
const filterVisible = ref(false);
const paramsSuper = computed(
  () =>
    formData.status ||
    formData.dateTime.length ||
    formData.level ||
    formData.name ||
    formData.type ||
    formData.telephone ||
    formData.group !== undefined
);
const paramsSuperFoot = computed(
  () =>
    paramsTemp.value.status ||
    paramsTemp.value.level ||
    paramsTemp.value.type ||
    paramsTemp.value.name ||
    paramsTemp.value.dateTime.length ||
    paramsTemp.value.telephone ||
    paramsTemp.value.group !== undefined
);
const showFilter = () => {
  filterVisible.value = true;
  getGroupList().then((arr: any)=> {
    console.log(arr)
    // if(arr && arr.length === 0) {
    //   groupOptions.value = [];
    // } else {
    //   groupOptions.value = arr?.concat([{id: 0, group_name:'未分组'}]);
    // }
    groupOptions.value = arr?.concat([{id: 0, group_name:'未分组'}]);
    console.log(groupOptions.value)
  });
};
const paramsTemp = ref({
  level: undefined,
  level_text: undefined,
  status: undefined,
  type: undefined,
  status_text: undefined,
  name: undefined,
  telephone: undefined,
  dateTime: [],
  group: undefined,
});
const statusChange = (e, ctx) => {
  console.log(e, ctx);
  paramsTemp.value.status_text = ctx.option.label;
};
const levelChange = (e, ctx) => {
  console.log(e, ctx);
  paramsTemp.value.level_text = ctx.option.label;
};
const groupOptionsChange = (e, ctx) => {
  console.log(e, ctx);
  paramsTemp.value.group_text = ctx.option.label;
};

const clearFilters = () => {
  formData.level = undefined;
  formData.status = undefined;
  formData.telephone = undefined;
  formData.type = undefined;
  formData.name = undefined;
  formData.dateTime = [];
  formData.group = undefined;
  paramsTemp.value.dateTime = [];
  paramsTemp.value.type = undefined;
  paramsTemp.value.name = undefined;
  paramsTemp.value.level = undefined;
  paramsTemp.value.status = undefined;
  paramsTemp.value.telephone = undefined;
  paramsTemp.value.group = undefined;
  onSearch();
};
const clearStatus = () => {
  formData.status = undefined;
  paramsTemp.value.status = undefined;
  onSearch();
};
const clearFiltertelephone = () => {
  formData.telephone = undefined;
  paramsTemp.value.telephone = undefined;
  onSearch();
};
const clearName = () => {
  formData.name = undefined;
  paramsTemp.value.name = undefined;
  onSearch();
};

const clearGroup = () => {
  formData.group = undefined;
  paramsTemp.value.group = undefined;
  onSearch();
};


const clearType = () => {
  formData.type = undefined;
  paramsTemp.value.type = undefined;
  onSearch();
};
const clearlevel = () => {
  formData.level = undefined;
  paramsTemp.value.level = undefined;
  onSearch();
};
const clearDateTime = () => {
  formData.dateTime = undefined;
  paramsTemp.value.dateTime = undefined;
  onSearch();
};
const getDataRunDr = () => {
  filterVisible.value = false;
  formData.level = paramsTemp.value.level;
  formData.type = paramsTemp.value.type;
  formData.status = paramsTemp.value.status;
  formData.telephone = paramsTemp.value.telephone;
  formData.name = paramsTemp.value.name;
  formData.group = paramsTemp.value.group;
  formData.dateTime = paramsTemp.value.dateTime;
  formData.status_text = paramsTemp.value.status_text;
  formData.level_text = paramsTemp.value.level_text;
  formData.group_text = paramsTemp.value.group_text;
  onSearch();
};
const footerCla = () => {
  filterVisible.value = false;
  paramsTemp.value.level = undefined;
  paramsTemp.value.status = undefined;
  paramsTemp.value.telephone = undefined;
  paramsTemp.value.name = undefined;
  paramsTemp.value.type = undefined;
  paramsTemp.value.dateTime = [];
};
</script>

<style lang="less" scoped>
@import "./public.less";
.iconsearch {
  font-size: 20px;
}
.main_body {
  display: flex;
  flex-direction: column;
}
.body {
  height: calc(100vh - 184px) !important;
}
</style>
