/**
 * 商户基础字段 - 包含 MerchantForm 和 MerchantInfo 的共同字段
 */
export interface MerchantBaseFields {
  /**
   * 经营内容
   */
  business_content: string;
  /**
   * 信用代码/注册号
   */
  credit_code?: string;
  /**
   * 结束时间
   */
  end_time?: string;
  /**
   * 是否政企 1是
   */
  is_government?: string;
  /**
   * 商户法人身份证号
   */
  legal_person_id_card: string;
  /**
   * 法人身份证有效期
   */
  legal_person_id_card_end_time: string;
  /**
   * 法人身份证正面照片
   */
  legal_person_id_card_img: LegalPersonidCardImg[];
  /**
   * 法人身份证反面照片
   */
  legal_person_id_card_img_tow: LegalPersonidCardImgTow[];
  /**
   * 法人身份证有效期开始时间
   */
  legal_person_id_card_start_time: string;
  /**
   * 商户法人姓名
   */
  legal_person_name: string;
  /**
   * 商户地址
   */
  merchant_address: string;
  /**
   * 商户名称
   */
  merchant_name: string;
  /**
   * 营业执照图片
   */
  merchant_team_img?: { [key: string]: any }[];
  /**
   * 组织名称
   */
  merchant_team_name?: string;
  /**
   * 商户环境图
   */
  pay_merchant_context_diagram_img: PayMerchantContextDiagramImg[];
  /**
   * 银行卡名称
   */
  pay_merchant_legal_person_bank_card_bank_name: string;
  /**
   * 银行卡号
   */
  pay_merchant_legal_person_bank_card_number: string;
  /**
   * 银行名称开户行名称
   */
  pay_merchant_legal_person_bank_card_open_bank_name: string;
  /**
   * 银行名称开户行号
   */
  pay_merchant_legal_person_bank_card_open_bank_number: string;
  /**
   * 银行卡正面照片
   */
  pay_merchant_legal_person_bank_img: PayMerchantLegalPersonBankImg[];
  /**
   * 银行卡反面照片
   */
  pay_merchant_legal_person_bank_img_tow: PayMerchantLegalPersonBankImgTow[];
  /**
   * 银行预留手机号
   */
  pay_merchant_legal_person_bank_mobile: string;
  /**
   * 商户门店照片
   */
  pay_merchant_store_img: PayMerchantStoreImg[];
  /**
   * 开始时间
   */
  start_time?: string;
}

/**
 * 商户申请表单
 */
export interface MerchantForm extends MerchantBaseFields {
  /**
   * 邮箱
   */
  email: string;
  /**
   * MCC编号不能为空
   */
  merchant_code: string;
  /**
   * 商户入网失败后面再次提交携带请求
   */
  old_merchant_id?: number;
}

/**
 * 商户信息
 */
export interface MerchantInfo extends MerchantBaseFields {
  /**
   * 经营内容名称
   */
  business_content_name?: string;
  /**
   * 信用代码/注册号
   */
  credit_code: string;
  /**
   * 商户组织营业执照结束时间
   */
  end_time: string;
  /**
   * 是否政企
   */
  is_government: string;
  merchant_id: number;
  /**
   * MCC编号  商户类别
   */
  merchant_mcc_code: string;
  /**
   * MCC编号  商户类别
   */
  merchant_mcc_code_name: string;
  /**
   * 营业执照图片
   */
  merchant_team_img: { [key: string]: any }[];
  /**
   * 商户组织名称
   */
  merchant_team_name: string;
  /**
   * 支付类型 1 对公支付
   */
  pay_merchant_legal_person_bank_type: number;
  /**
   * 商户组织营业执照开始时间
   */
  start_time: string;
  team_id: string;
}

/**
 * 商户表单数据类型
 */
export type MerchantFormData = Partial<MerchantForm & MerchantInfo> & {
  // 开户行号
  clear_bank_code?: string;
};

export interface LegalPersonidCardImg {
  /**
   * 1.png
   */
  file_name: string;
  /**
   * png
   */
  file_suffix: string;
  /**
   * 地址
   */
  file_url: string;
  [property: string]: any;
}

/**
 * 法人身份证反面照片
 */
export interface LegalPersonidCardImgTow {
  /**
   * 1.png
   */
  file_name: string;
  /**
   * png
   */
  file_suffix: string;
  /**
   * 地址
   */
  file_url: string;
  [property: string]: any;
}

/**
 * 商户环境图
 */
export interface PayMerchantContextDiagramImg {
  file_name: string;
  file_suffix: string;
  file_url: string;
  [property: string]: any;
}

/**
 * 银行卡正面照片
 */
export interface PayMerchantLegalPersonBankImg {
  file_name: string;
  file_suffix: string;
  file_url: string;
  [property: string]: any;
}

/**
 * 银行卡反面照片
 */
export interface PayMerchantLegalPersonBankImgTow {
  file_name: string;
  file_suffix: string;
  file_url: string;
  [property: string]: any;
}

/**
 * 商户门店照片
 */
export interface PayMerchantStoreImg {
  file_name: string;
  file_suffix: string;
  file_url: string;
  [property: string]: any;
}

/**
 * 商户状态
 */
export interface MerchantStatus {
  /**
   * 电子合同申请 1待审核 2审核通过 3审核失败
   */
  apply_status: string;
  /**
   * 是否支付宝支付商户 0 否 1 是
   */
  is_alipay_merchant: string;
  /**
   * 绑定分账 0   1绑定中 2绑定成功  3绑定失败
   */
  is_bind: string;
  /**
   * 是否开通商户 0 否 1审核中  2成功 3失败
   */
  is_open_merchant: string;
  /**
   * 分佣 分账接收方 0 未开通 1已开通
   */
  is_receiver_no: string;
  /**
   * 是否开通分账 0 否 1 待审核 2审核通过 3审核失败
   */
  is_separate_accounts: string;
  /**
   * 是否微信支付商户 0 否 1 是
   */
  is_wechat_merchant: string;
  merchant_id: number;
  /**
   * 对应失败状态的错误信息
   */
  message: string;
}

/**
 * 商户二维码
 */
export interface MerchantQrCode {
  /**
   * 支付宝子商户号
   */
  alipay_number: string;
  /**
   * 支付宝二维码
   */
  alipay_qrcode_url: string;
  /**
   * 是否支付宝支付商户 0 否 1 是
   */
  is_alipay_merchant: number;
  /**
   * 是否微信支付商户 0 否 1 是
   */
  is_wechat_merchant: number;
  /**
   * 商户号
   */
  mer_cup_no: string;
  /**
   * 微信二维码
   */
  wechat_qrcode_url: string;
  /**
   * 微信子商户号
   */
  wx_number: string;
}

/**
 * 实名认证状态
 */
export interface RealNameStatus {
  /** 是否支付宝支付商户 0 否 1 是 */
  is_alipay_merchant: string;
  /** 是否微信支付商户 0 否 1 是 */
  is_wechat_merchant: string;
}

/**
 * 电子合同签约状态
 */
export interface ElectronicContractStatus {
  /**
   * 电子合同申请 1待签约 2审核通过 3审核失败
   */
  apply_status: number;
  /**
   * 电子签约申请受理编号
   */
  ec_apply_id: string;
  /**
   * 商户id
   */
  merchant_id: number;
  /**
   * 签约地址
   */
  sign_h5_url: string;
  /**
   * 签约有效期
   */
  sign_h5_url_exp_tm: string;
  [property: string]: any;
}

/**
 * 分账申请请求
 */
export interface SeparateAccountsRequest {
  /**
   * 绑定分账协议
   */
  apply_bind_img: ApplyBindImg[];
  [property: string]: any;
}

export interface ApplyBindImg {
  file_name: string;
  file_suffix: string;
  file_url: string;
  [property: string]: any;
}

/**
 * 商户状态类型
 *
 * startOpen 入网开始
 * pending 未入网
 * success 入网成功
 * failed 入网失败
 * needSign 签约中
 * signSuccess 签约成功
 * splitPending 分账中
 * splitSuccess 分账成功
 * splitFailed 分账失败
 * realNamePending 实名中
 * realNameSuccess 实名成功
 * commissionSuccess 分佣成功
 * commissionPending 分佣中
 */
export type StatusType = 'startOpen' | 'pending' | 'success' | 'failed' | 'needSign' | 'signSuccess' | 'splitPending' | 'splitSuccess' | 'splitFailed' | 'realNamePending' | 'realNameSuccess' | 'commissionSuccess' | 'commissionPending';

/**
 * 提现密码设置/重置入参
 */
export interface WithdrawalPasswordPayload {
  id: number;
  legal_person_name: string;
  legal_person_id_card: string;
  mobile_code: string;
  password: string;
  phone: string;
}

/**
 * 提现记录
 */
export interface WithdrawalItem {
  /** 提现记录ID */
  id: number;
  /** 组织ID */
  team_id: string;
  /** 商户ID */
  merchant_id: number;
  /** 图标 */
  logo_url: string;
  /** 标题 */
  title: string;
  /** 描述 */
  desc: string;
  /** 创建时间 */
  created_at: string;
  /** 密码 */
  password: string;
}

/**
 * 开户行号
 */
export interface BankCodeItem {
  /**
   * 地区机构编码
   */
  address_code: string;
  /**
   * 银行名称
   */
  bank_name: string;
  /**
   * 清算行号
   */
  clear_code: string;
  id: number;
  /**
   * 银行机构编码
   */
  institution_code: string;
  /**
   * 开户行号
   */
  open_code: string;
  [property: string]: any;
}

/**
 * 密码弹窗模式
 *
 * set 设置密码
 * modify 修改密码
 * reset 重置密码
 */
export type PasswordDialogMode = 'set' | 'modify' | 'reset';
