<template>
  <!-- v-lkloading="{ show:loading, height:false, opacity:false }" -->
  <!-- v-lkloading="{ show:loading, height:false, opacity:false }" -->
  <div class="container">
    <div class="su-header">
      <div class="more-search">
        <div class="in-box">

          <t-input
            v-model="formData.keyword"
            :placeholder="'搜索租户名称/负责人/姓名'"

            :maxlength="50"
            clearable
            style="width: 304px"
            @change="onSearch"
            class="inSearch"
          >
            <template #prefix-icon>
              <iconpark-icon name="iconsearch" class="iconsearch"></iconpark-icon>
            </template>
          </t-input>
        </div>
        <!-- <div v-if="paramsSuper" class="af-icon" @click="showFilter">
          <img
            src="@renderer/assets/approval/icons/fbutton.svg"
            style="width: 32px; height: 32px"
            alt=""
          />
        </div>
        <div v-else class="f-icon" @click="showFilter">
          <img
            src="@renderer/assets/approval/icons/icon_screen.svg"
            style="width: 20px; height: 20px"
            alt=""
          />
        </div> -->

        <div :class="{'f-icon': true, 'factive': paramsSuper }" @click="showFilter">
          <iconpark-icon name="iconscreen" class="iconscreen"></iconpark-icon>
        </div>
      </div>
      <div class="opt">
        <!-- 下载导入模板
        <t-button
          theme="default"
          variant="outline"

          @click="onDownloadModel"
        >
          {{ $t("member.regular.download_template") }}</t-button> -->
          <!-- :count="store.getAllTeamApplyCount + store.getAllTeamActiveCount" -->
        <t-badge :count="appCount">
          <t-button
            id="applyList"
            theme="default"
            variant="outline"
            class="bold"
            @click="onApply"
          >
            {{ $t('member.apply_flow.apply_way_21') }}</t-button>
        </t-badge>
        <span class="line"></span>
        <div id="importList">
          <t-button theme="default" variant="outline" class="bold" @click="onUploadFileShow">
             {{t('member.cbd.drzh')}}</t-button>

          <t-button theme="default" class="bold" variant="outline" @click="onInviteActiveMember">
             邀请激活</t-button>

          <t-button theme="default" class="bold" variant="outline" @click="onInviteMember">
            {{ $t('member.digital.o') }}</t-button>

          <t-button theme="primary" variant="base" @click="onAddMember">
            <template #icon>

              <iconpark-icon name="iconadd" class="iconadd"></iconpark-icon>

            </template>
            {{t('member.cbd.tjzh')}}
          </t-button>
        </div>
      </div>
    </div>

    <div class="body">
      <div v-if="paramsSuper" class="filter-res">
        <div class="tit">{{ t("approval.approval_data.sures") }}</div>
        <div v-if="formData.status" class="stat te">
          <span>
            {{ $t("member.regular.group") }}： {{ formData.status_text }}</span>
          <span class="close2" @click="clearStatus">
            <iconpark-icon class="closeIcon" name="iconerror"></iconpark-icon>
          </span>
        </div>
        <div v-if="formData.type" class="stat te">
          <span>
            {{$t("member.second.z")}}： {{ formData.type_text }}</span>
          <span class="close2" @click="clearType">
            <iconpark-icon class="closeIcon" name="iconerror"></iconpark-icon>
          </span>
        </div>

        <div v-if="formData.dateTime.length" class="kword te">
          <span>{{ t("member.active.apply_time") }}：{{ formData.dateTime[0] }}~{{
            formData.dateTime[1]
          }}</span>
          <span class="close2" @click="clearDateTime">
            <iconpark-icon class="closeIcon" name="iconerror"></iconpark-icon>
          </span>
        </div>
        <div v-if="formData.level" class="ov-time te">
          <span>{{ t("member.svip.member_level") }}：
            {{ formData.level_text }}</span>
          <span class="close2" @click="clearlevel">
            <iconpark-icon class="closeIcon" name="iconerror"></iconpark-icon>
          </span>
        </div>
        <div v-if="formData.telephone" class="ov-time te">
          <span>{{ t("member.regular.phone") }}： {{ formData.telephone }}</span>
          <span class="close2" @click="clearFiltertelephone">
            <iconpark-icon class="closeIcon" name="iconerror"></iconpark-icon>
          </span>
        </div>
        <div v-if="formData.name" class="ov-time te">
          <span>{{  t("member.winter_column.organize_2")  }}： {{ formData.name }}</span>
          <span class="close2" @click="clearName">
            <iconpark-icon class="closeIcon" name="iconerror"></iconpark-icon>
          </span>
        </div>
        <div v-if="formData.activate" class="ov-time te">
          <span>{{ t("member.regular.active_status") }}：
            {{ formData.activate_text }}</span>
          <span class="close2" @click="clearActivate">
            <iconpark-icon class="closeIcon" name="iconerror"></iconpark-icon>
          </span>
        </div>
        <div class="icon" style="height: 26px; padding: 2px 0" @click="clearFilters">
          <img src="@renderer/assets/approval/icons/del8.svg" alt="" />
          <a>{{ t("approval.approval_data.clearFilters") }}</a>
        </div>
      </div>

      <div class="body-content">
        <t-form
          v-if="false"
          ref="form"
          :rules="FORM_RULES"
          class="searchForm"
          :data="formData"
          layout="inline"
          :label-align="'right'"
          :colon="false"
        >
          <t-form-item
            name="keyword"
            class="searchForm-item"
            label-width="64px"
          >
            <template #label>
              <div>{{ $t("member.regular.name") }}</div>
            </template>
            <t-input
              v-model="formData.keyword"
              class="searchForm-item-input"
              :maxlength="50"
              clearable
              :placeholder="$t('member.regular.fzr_input_name')"
              @enter="onEnter"
            />
          </t-form-item>
          <t-form-item name="status" class="searchForm-item" label-width="64px">
            <template #label>
              <div>{{ $t("member.regular.group") }}</div>
            </template>
            <t-select
              v-model="formData.status"
              clearable
              :options="memberOptions"
            >
            <template #suffixIcon>
              <img src="@/assets/svg/icon_arrow_down.svg" />
            </template>
          </t-select>
          </t-form-item>
          <t-form-item
            name="dateTime"
            class="searchForm-item"
            :label-align="'right'"
            label-width="78px"
          >
            <template #label>
              <div>{{ $t("member.active.apply_time") }}</div>
            </template>
            <t-date-range-picker
              v-model="formData.dateTime"
              :placeholder="[
                $t('approval.approval_data.start_time'),
                $t('approval.approval_data.end_time')
              ]"
              style="width: 244px"
              class="!w-240"
              clearable
            />
          </t-form-item>
          <t-form-item name="level" class="searchForm-item" label-width="64px">
            <template #label>
              <div>{{ $t("member.svip.member_level") }}</div>
            </template>
            <t-select
              v-model="formData.level"
              :options="levelOptions"
              clearable
            >
            <template #suffixIcon>
              <img src="@/assets/svg/icon_arrow_down.svg" />
            </template>
          </t-select>
          </t-form-item>
          <t-form-item
            name="telephone"
            class="searchForm-item"
            label-width="64px"
          >
            <template #label>
              <div>{{ $t("member.regular.phone") }}</div>
            </template>
            <t-input
              v-model="formData.telephone"
              class="searchForm-item-input"
              :maxlength="50"
              clearable
              :placeholder="$t('member.regular.please_input')"
              @enter="onEnter"
            />
          </t-form-item>

          <t-form-item
            v-show="isMoreSearch"
            name="name"
            class="searchForm-item"
            label-width="78px"
          >
            <template #label>
              <div>{{  t("member.cbd.fzrxm")  }}</div>
            </template>
            <t-input
              v-model="formData.name"
              class="searchForm-item-input !w-240"
              :maxlength="50"
              clearable
              :placeholder="$t('member.regular.fzr_input_name')"
              @enter="onEnter"
            />
          </t-form-item>
          <t-form-item
            v-show="isMoreSearch"
            name="activate"
            class="searchForm-item"
            label-width="64px"
          >
            <template #label>
              <div>{{ $t("member.regular.active_status") }}</div>
            </template>
            <t-select
              v-model="formData.activate"
              clearable
              :options="activeOptions"
            ><template #suffixIcon>
              <img src="@/assets/svg/icon_arrow_down.svg" />
            </template>
          </t-select>
          </t-form-item>

          <div class="searchForm-buttons">
            <t-button theme="primary" variant="base" @click="onSearch">
              {{ $t("member.regular.search") }}</t-button>
            <t-button theme="default" variant="outline" @click="onReset">
              {{ $t("member.regular.reset") }}</t-button>
          </div>
          <div class="searchForm-buttons">
            <t-link
              theme="primary"
              class="ml-16"
              hover="color"
              @click="setMoreSearch"
            >
              {{ isMoreSearch ? "关闭高级搜索" : "高级搜索" }}
            </t-link>
          </div>
        </t-form>

        <!-- <div style="padding: 0 16px">
          <div class="tabsss">
            <div
              class="item"
              :class="{
                active: 1 === formData.type
              }"
              @click="onSwitchTab(1)"
            >
              单位会员
            </div>
            <div
              class="item"
              :class="{
                active: 2 === formData.type
              }"
              @click="onSwitchTab(2)"
            >
              个人会员
            </div>
            <div class="act-tag" :class="'act-' + formData.type"></div>
          </div>
        </div> -->
        <!-- {{ formData }}
        {{ paramsSuper }}
        {{ paramsSuperFoot }} -->
        <div  class="table">

          <!-- <ApplyFlowComp
            v-show="!loading"
            v-if="!paramsSuper&& !formData.keyword && memberData.length < 1"
            @on-add-member="onAddMember"
            @on-import-member="onUploadFileShow"
            @on-invite-member="onInviteMember"
            @on-copy-link="onCopyLink"
          /> -->
          <!-- @row-mouseleave="onRowMouseleave"
            @row-mouseenter="onRowMousenter" -->
          <Empty class="setE"  v-show="!loading" v-if="!paramsSuper&& !formData.keyword && memberData.length < 1"/>
          <t-table
            v-else
            row-key="id"
            :pagination="
              pagination.total > pagination.pageSize ? pagination : null
            "
            :columns="memberColumns"
            :data="memberData"

          >
            <template #empty>
              <div class="empty">
                <!-- <noData :text="$t('engineer.no_data')" /> -->
                <Empty />
              </div>

            </template>
            <template #expire_time="{ row }">
              <div v-if="row.no_expire">长期有效</div>
              <div v-else>
                {{ $filters.isPeriodEmpty(row.expire_time) }}
              </div>
            </template>
            <template #no="{ row }">
              <!-- v-show="row.type === 1" -->
              <div class="main_body">
                <span class="main_body-item line-1">
                  <!-- {{ $filters.isPeriodEmpty(row.name) }} -->
                  <MyTooltipComp :text="row.name"/>
                </span>
                <span class="main_body-item phone">
                  +{{ $filters.isPeriodEmpty(row.telCode) }}&nbsp;{{
                    $filters.isPeriodEmpty(row.telephone)
                  }}
                </span>
              </div>
              <!-- <div v-show="row.type === 2" class="main_body">
                <span class="main_body-item">
                  {{ $filters.isPeriodEmpty(row.name) }}
                </span>
                <span class="main_body-item phone">
                  +{{ $filters.isPeriodEmpty(row.telCode) }}&nbsp;{{
                    $filters.isPeriodEmpty(row.telephone)
                  }}
                </span>
              </div> -->
            </template>
            <!-- <template #status="{ row }">
                {{ filterStatusText(row.status) }}
              </template> -->
            <template #team_name="{ row }">
              <div class="org">
                <!-- <div v-if="row.type === 1">{{ row.team_name }}</div>
                <div v-else class="person">个人会员</div> -->
                <template v-if="row.type === 2">
                  <div class="person">个人</div>
                  <div v-if=" ![2,3].includes(row.status) && row.activate === 2" class="unactive">未激活</div>
                </template>
                <template v-if="row.type === 1">
                  <div class="team line-2">
                    <MyTooltipComp :text="row.team_name"/>

                  </div>
                  <template v-if="(row.status === 1&& row.activate === 1) || row.status === 2">
                    <div v-if="row.is_connect" class="connected">已连接</div>
                    <!-- <div v-else class="unconnected">未连接</div> -->
                  </template>
                  <template v-else-if="![2,3].includes(row.status) && row.activate === 2">
                    <div class="unactive">未激活</div>
                  </template>

                </template>
              </div>
            </template>

            <template #status="{ row }">
              <div class="column">
                <div :class="showClassStatus(row.status)">
                  {{ filterStatusText(row.status) }}
                </div>

                <!-- <div v-if=" ![2,3].includes(row.status) && row.activate === 2" v-show="!row?.isShow" class="unactive mt-8px">
                  <iconpark-icon name="iconunusual" class="iconunusual"></iconpark-icon>  未激活
                </div>


                <t-popup
                  theme="default"
                  placement="bottom"
                  class="regular"
                  :cancel-btn="null"
                  :confirm-btn="null"
                >
                  <template #icon />
                  <template #content>
                    <div class="pop">
                      <span class="pop-item" @click="onInviteJobClub(row)">
                        <iconpark-icon name="iconshare" class="icondepartment1"></iconpark-icon>
                        {{ $t('member.regular.share_link_qrcode') }}

                      </span>
                    </div>
                    <div class="pop">
                      <span class="pop-item" @click="onSendSMS(row)">
                        <iconpark-icon name="iconshare" class="icondepartment1"></iconpark-icon>
                        {{ $t('member.regular.send_message') }}
                      </span>
                    </div>
                  </template>
                  <span></span>
                  <div v-show="![2,3].includes(row.status) && row.activate === 2 && row?.isShow" class="sendmessage mt-8px cursor">
                    <iconpark-icon name="iconpositioning-1" class="iconpositioning"></iconpark-icon>  通知激活
                  </div>

                </t-popup> -->
              </div>
            </template>

            <template #operate="{ row }">
              <span class="operates">
                <a class="operates-item" @click="onLookDetail(row)">
                  {{ $t("member.regular.detail") }}
                </a>
                <a
                  v-show="row.status !== 3"
                  class="operates-item"
                  @click="onEditMember(row)"
                >
                  {{ $t("member.edit") }}
                </a>

                <t-popup
                  theme="default"
                  placement="bottom-right"
                  class="regular"
                  :cancel-btn="null"
                  :confirm-btn="null"
                  v-model:visible="row.popMoreconfirmVisible"
                >
                  <template #icon />
                  <template #content>
                    <div class="pop">
                      <span class="pop-item" @click="onMemberWithdraw(row)">
                        <iconpark-icon
                          name="iconrefund"
                          class="icondepartment1"
                        />
                        {{$t('member.squarek.o')}}
                      </span>
                      <!-- <span
                        v-show="row.no_expire !== 1"
                        class="pop-item"
                        @click="onMemberRenewal(row)"
                      >
                        <iconpark-icon
                          name="iconrenew"
                          class="icondepartment1"
                        />
                        {{ $t("member.regular.membership_renewal") }}
                      </span> -->
                      <!-- <span
                        v-show="row.activate === 2"
                        class="pop-item"
                        @click="onInviteJobClub(row)"
                      >

                        <iconpark-icon name="iconactivation" class="icondepartment1"></iconpark-icon>
                        {{ $t("member.regular.invite_active") }}
                      </span> -->
                      <span
                        v-show="row.type !== 2"
                        class="pop-item"
                        @click="onContact(row)"
                      >
                        <iconpark-icon
                          name="icondepartment"
                          class="icondepartment1"
                        />
                       代表人管理
                      </span>
                    </div>
                  </template>
                  <!-- v-show="row.status === 3" -->
                  <!-- <a

                    class="operates-item"
                    @click="onShowMore(row)"
                  >
                    {{ $t("member.more") }}
                  </a> -->

                  <div
                    v-show="row.status !== 3"
                    class="more-box"
                    :class="{moreActive: row.popMoreconfirmVisible}"
                    @click="onShowMore(row)"
                  >
                    <iconpark-icon name="iconmore" class="iconmore" />
                  </div>
                </t-popup>
              </span>
            </template>
          </t-table>
        </div>
      </div>
    </div>
  </div>

  <t-drawer
    v-model:visible="filterVisible"
    :close-btn="true"
    size="472px"
    :header="t('approval.approval_data.sur')"
  >
    <div class="form-boxxx">
      <div class="fitem">
        <div class="title">组织状态</div>
        <div class="ctl">
          <t-select
            v-model="paramsTemp.status"
            clearable
            :options="memberOptions"
            style="width: 422px"
            @change="statusChange"
          ><template #suffixIcon>
            <img src="@/assets/svg/icon_arrow_down.svg" />
          </template>
        </t-select>
        </div>
      </div>

      <div class="fitem">
        <div class="title">类型</div>
        <div class="ctl">
          <t-select
            v-model="paramsTemp.type"
            clearable
            :options="typeOptions"
            style="width: 422px"
            @change="typeChange"
          ><template #suffixIcon>
            <img src="@/assets/svg/icon_arrow_down.svg" />
          </template>
        </t-select>
        </div>
      </div>


      <!-- <div class="fitem">
        <div class="title">{{ t("member.active.apply_time") }}</div>
        <div class="ctl">
          <t-date-range-picker
            v-model="paramsTemp.dateTime"
            :placeholder="[
              $t('approval.approval_data.start_time'),
              $t('approval.approval_data.end_time')
            ]"
            style="width: 422px"
            clearable
          />
        </div>
      </div>
      <div class="fitem">
        <div class="title">{{ t("member.svip.member_level") }}</div>
        <div class="ctl">
          <t-select v-replace-svg
            v-model="paramsTemp.level"
            :options="levelOptions"
            clearable
            style="width: 422px"
            @change="levelChange"
          />
        </div>
      </div> -->
      <div class="fitem">
        <div class="title">{{ t("member.regular.phone") }}</div>
        <div class="ctl">
          <t-input
            v-model="paramsTemp.telephone"
            class="searchForm-item-input"
            :maxlength="50"
            clearable
            :placeholder="$t('member.regular.please_input')"
            style="width: 422px"
          />
        </div>
      </div>
      <!--      <div class="fitem">-->
      <!--        <div class="title">{{ t("member.regular.respect") }}</div>-->
      <!--        <div class="ctl">-->
      <!--          <t-input-->
      <!--            v-model="paramsTemp.name"-->
      <!--            class="searchForm-item-input"-->
      <!--            :maxlength="50"-->
      <!--            clearable-->
      <!--            :placeholder="$t('member.regular.fzr_input_name')"-->
      <!--            style="width: 422px"-->
      <!--          />-->
      <!--        </div>-->
      <!--      </div>-->
      <div class="fitem">
        <div class="title">{{ t("member.regular.active_status") }}</div>
        <div class="ctl">
          <t-select
            v-model="paramsTemp.activate"
            :options="activeOptions"
            clearable
            style="width: 422px"
            @change="activeOptionsChange"
          ><template #suffixIcon>
            <img src="@/assets/svg/icon_arrow_down.svg" />
          </template>
        </t-select>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="foot">
        <div class="btn1" @click="footerCla">
          {{ t("approval.approval_data.cl") }}
        </div>
        <div v-if="paramsSuperFoot" class="btn3" @click="getDataRunDr">
          {{ t("approval.approval_data.cm") }}
        </div>
        <div v-else class="btn2">
          {{ t("approval.approval_data.cm") }}
        </div>
      </div>
    </template>
  </t-drawer>

  <t-upload
    ref="uploadRef"
    v-model="files"
    class="ossUpload"
    theme="custom"
    multiple
    :max="20"
    :action="null"
    :before-upload="beforeUpload"
    :on-select-change="onSelectChange"
  />


  <ImportMemberModal
    ref="importMemberModalRef"
    @on-download-model="onDownloadModel"
    @on-select-change="onSelectChange"
    @on-upload-file-emit="onUploadFile"
    @on-show-member-flow="onShowMemberFlow"
  />
  <ImportErrorModal ref="importErrorModalRef" />
  <LoadingModal ref="loadingModalRef" @reload="onSearch" />
  <MemberWithdrawalModal
    ref="memberWithdrawalModalRef"
    @on-send="onSetWithDrawal"
  />
  <AddMemberModal
    ref="addMemberModalRef"
    :is-hidden-arr="isHiddenArr"
    @reload="onSearch"
    @on-show-member-flow="onShowMemberFlow"
  />
  <MembershipRenewalModal
    ref="membershipRenewalModalRef"
    @on-send="onSaveMemberRenewal"
  />
  <LookRegularModal
    ref="lookRegularModalRef"
    @update-detail="onLookDetail"
    @reload="onSearch"
  />
  <InviteQrcodeModal
    ref="inviteQrcodeModalRef"
    :header-text="$t('member.regular.invite_active')"
    :down-load-text="'扫一扫上面的二维码，申请激活'"
    :tip-text="$t('member.regular.share_qrcode_to_join')"
    :copy-link-text="'复制链接'"
    :type="'active'"
    @on-show-member-flow="onShowMemberFlow"
  />

  <InviteQrcodeModal ref="inviteQrcodeMemberModalRef" :copy-link-text="'复制链接'" @on-show-member-flow="onShowInviteFlow" />
  <AddInMemberModal ref="addInMemberModalRef" />
  <InviteFlowModal ref="inviteFlowModalRef" />

  <InviteActiveQrModal
    :header-text="$t('member.regular.invite_active')"
    :down-load-text="$t('member.syd.p')"
    :tip-text="$t('member.regular.share_qrcode_to_join')"
    :copy-link-text="'复制链接'"
    :type="'active'"
    @on-show-member-flow="onShowMemberFlow"
    ref="inviteActiveQrModalRef"
  />


</template>

<script lang="ts" setup>
import {
  getMemberJobsListAxios,
  getRegularListAxios,
  downloadRegularAxios,
  exitRegularAxios,
  sendActivateSmsAxios,
  importRegularAxios,
  getRegularDetailAxios,
  getMemberSettingAxios,
  getRegularLinkAxios,
  renewalRegularAxios,
  getMemberApplyLinkAxios,
  getTeamsAxios,
getStatistics,
} from "@renderer/api/cbd/api/businessApi";

import AddInMemberModal from "@renderer/views/cbd/member_home/panel/regular-member-panel/modal/add-in-member-modal.vue";
import InviteFlowModal from "@renderer/views/cbd/member_home/panel/regular-member-panel/modal/invite-flow-modal.vue";
import MemberWithdrawalModal from "@renderer/views/cbd/member_home/panel/regular-member-panel/modal/member-withdrawal-modal.vue";
import AddMemberModal from "@renderer/views/cbd/member_home/panel/regular-member-panel/modal/add-member-modal.vue";
import LoadingModal from "@renderer/views/cbd/member_home/panel/regular-member-panel/modal/loading-modal.vue";
import ImportErrorModal from "@renderer/views/cbd/member_home/panel/regular-member-panel/modal/import-error-modal.vue";
import ImportMemberModal from "@renderer/views/cbd/member_home/panel/regular-member-panel/modal/import-member-modal.vue";
import MembershipRenewalModal from "@renderer/views/cbd/member_home/panel/regular-member-panel/modal/membership-renewal-modal.vue";
import LookRegularModal from "@renderer/views/cbd/member_home/panel/regular-member-panel/modal/look-regular-modal.vue";
import InviteQrcodeModal from "@renderer/views/cbd/member_home/panel/apply-member-panel/modal/invite-qrcode-modal.vue";
import ApplyFlowComp from "@renderer/views/cbd/member_home/panel/regular-member-panel/components/apply-flow-comp.vue";
import InviteActiveQrModal from "@renderer/views/cbd/member_home/panel/apply-member-panel/modal/invite-active-qr-modal.vue";
import MyTooltipComp from "@renderer/components/engineer/components/MyTooltipComp.vue";


// import noData from "@renderer/components/approvalSatellite/approvalData/noData.vue";
import { getResponseResult } from "@renderer/utils/myUtils";
import { FormRule, MessagePlugin, DialogPlugin } from "tdesign-vue-next";

import { computed, onActivated, onMounted, reactive, ref, toRaw, watch } from "vue";
import { useI18n } from "vue-i18n";

import { getBaseUrl } from "@renderer/utils/apiRequest";
import useClipboard from "vue-clipboard3";

import { useCbdStore } from "@renderer/views/cbd/store/cbd";
import lodash from "lodash";
import { inviteUrl } from "@renderer/utils/baseUrl";
import Qs from "qs";
import Empty from "@renderer/components/common/Empty.vue";
import to from "await-to-js";
import { getCbdTeamID } from "@renderer/views/cbd/utils/auth";
import { useDigitalPlatformStore } from "@renderer/views/digital-platform/store/digital-platform-store";
import { useRoute } from "vue-router";
import { platform } from "@renderer/views/digital-platform/utils/constant";
import {
  workShopAppAxios
} from "@renderer/api/cbd/api/businessApi";
import { onMountedOrActivated } from "@renderer/hooks/onMountedOrActivated";
import { goCertifiedDialog } from "@renderer/views/digital-platform/utils/auth";
import { IMRefreshType } from "@renderer/views/message/common/constant";
import LynkerSDK from "@renderer/_jssdk";
const { ipcRenderer, shell } = LynkerSDK;

const emits = defineEmits(["onPage", "onSetCurrentPanel", "onSetCurrentRow", "getRedNum"]);
const store = useCbdStore();
const { toClipboard } = useClipboard();
const loading = ref(false);
const digitalPlatformStore = useDigitalPlatformStore();
const route = useRoute();

const { t } = useI18n();

const isMoreSearch = ref(false);
// const popMoreconfirmVisible = ref(false);

const statusOptions = [
  // { label: "全部", value: 0 },
  // { label: "待审核", value: 1 },
  // { label: "已通过", value: 2 },
  // { label: "已驳回", value: 3 }
  { label: t('member.winter_column.statusOptions_1'), value: 0 },
  { label: t('member.winter_column.statusOptions_2'), value: 1 },
  { label: t('member.winter_column.statusOptions_3'), value: 2 },
  { label: t('member.winter_column.statusOptions_4'), value: 3 }
];

const memberOptions = [
  // { label: "全部", value: 0 },
  // { label: "正常", value: 1 },
  // { label: "已到期", value: 2 },
  // { label: "已退会", value: 3 }
  { label: t('member.winter_column.memberOptions_1'), value: 1 },
  // { label: t('member.winter_column.memberOptions_2'), value: 2 },
  { label: '已退出', value: 3 }
];

const typeOptions = [
  // { label: "全部", value: 0 },
  // { label: "正常", value: 1 },
  // { label: "单位会员", value: 1 },
  // { label: "个人会员", value: 2 }
  { label: '组织', value: 1 },
  { label: '个人', value: 2 },
];

const activeOptions = [
  // { label: "全部", value: 0 },
  // { label: "已激活", value: 1 },
  // { label: "未激活", value: 2 }
  { label: t('member.winter_column.activeOptions_1'), value: 1 },
  { label: t('member.winter_column.activeOptions_2'), value: 2 },

];

const showClassStatus = (val) => {
  // 会员状态，1：正常，2：已到期，3：已退会
  let result = {};
  if (val === 3) {
    result = { gray: true };
  } else if (val === 1) {
    result = { success: true };
  } else if (val === 2) {
    result = { reject: true };
  }
  return result;
};

const FORM_RULES: Record<string, FormRule[]> = {
  // phone: [{ required: false, message: '内容超出', max: 10 }],
};
const formData = ref({
  keyword: "", // 会员名称
  status: undefined, // 会员状态 1：待审核，2：已通过，3：已驳回
  dateTime: [], // 申请开始、结束时间
  level: undefined, // 会员级别
  name: "", // 负责人姓名
  telephone: "", // 手机号
  type: 0, // 入会类型，1：单位，2：个人
  activate: undefined, // 激活状态，1：已激活，2：未激活
  level_text: undefined,
  status_text: undefined,
  activate_text: undefined
});
const form = ref(null);

const onReset = () => {
  form.value.reset();
  onSearch();
};


const props = defineProps({
  platform: {
    type: String,
    default: '',
  },
})
// 平台类型 目前只有digital-platform
const platformCpt = computed(() => {
  return props.platform || route.query?.platform
})

const activeAccount = computed(() => {
  if(platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount
  } else if(platformCpt.value === platform.digitalWorkbench)  {
    return route.query;
  } else {
    return store.activeAccount
  }
})


const currentTeamId = computed(() => {
  if(platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount?.teamId
  } else if(platformCpt.value === platform.digitalWorkbench){
    return route.query?.teamId || 0;
  } else {
    return  getCbdTeamID()
  }
})


const memberColumns = ref([]);
const initColumns = () => {
  // if (formData.value.type === 1) {
    const totalWidth = 944;
    memberColumns.value = [
      // {
      //   colKey: "row-select",
      //   type: "multiple",
      //   width: "4%"
      // },
      { colKey: "team_name", title: t('member.winter_column.organize_1'), width: `${192/totalWidth}%` },
      { colKey: "no", title:  t("member.winter_column.fzrxm") , width: `${140/totalWidth}%`  },
      // {
      //   colKey: "level_name",
      //   title: t('member.winter_column.organize_3'),
      //   width: `${112/totalWidth}%`,
      //   ellipsis: false,
      //   align: "left"
      // },
      // { colKey: "join_time", title: t('member.winter_column.organize_4'), width: `${112/totalWidth}%`, ellipsis: false },
      // {
      //   colKey: "expire_time",
      //   title: t('member.winter_column.organize_5'),
      //   width: `${112/totalWidth}%`,
      //   ellipsis: false
      // },
      { colKey: "status", title: t('member.winter_column.organize_6'), width: `${90/totalWidth}%`, },
      { colKey: "operate", title: t('member.winter_column.organize_7'),width: `${172/totalWidth}%`, }
    ];
  // } else {
    // memberColumns.value = [
    //   {
    //     colKey: "row-select",
    //     type: "multiple",
    //     width: "5%"
    //   },
    //   { colKey: "team_name", title: "组织名称", width: "10%" },
    //   { colKey: "no", title: "姓名", width: "15%" },
    //   {
    //     colKey: "level_name",
    //     title: "会员级别",
    //     width: "12%",
    //     ellipsis: false,
    //     align: "left"
    //   },
    //   { colKey: "join_time", title: "入会时间", width: "10%", ellipsis: false },
    //   {
    //     colKey: "expire_time",
    //     title: "过期时间",
    //     width: "10%",
    //     ellipsis: false
    //   },
    //   { colKey: "status", title: "状态", width: "8%" },
    //   { colKey: "operate", title: "操作", width: "15%" }
    // ];
 // }
};







initColumns();
const filterStatusText = (val) => {
  // 会员状态，1：正常，2：已到期，3：已退会，0：未激活
  // const arr = ["未激活", "正常", "已到期", "已退会"];
  const arr = [
    t('member.winter_column.activeOptions_2'),
    t('member.winter_column.memberOptions_1'),
    t('member.winter_column.memberOptions_2'),
    '已退出',
  ];

  return val ? arr[val] : "--";
};


const onSwitchTab = (val) => {
  formData.value.type = val;
  pagination.current = 1;
  initColumns();
  onSearch();
};

const setMoreSearch = () => {
  isMoreSearch.value = !isMoreSearch.value;
};



const memberData = ref([]);
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
  onChange: (pageInfo) => {
    console.log("pagination.onChange", pageInfo);
    pagination.current = pageInfo.current;
    pagination.pageSize = pageInfo.pageSize;


    const params = {
      ...toRaw(formData.value),
      join_time_start:
        formData.value.dateTime.length > 0 ? formData.value.dateTime[0] : "",
      join_time_end:
        formData.value.dateTime.length > 1 ? formData.value.dateTime[1] : ""
    };
    getMemberList(params);
    // onSearch();
  }
});

const onSearch = () => {
  pagination.current = 1;

  const params = {
    ...toRaw(formData.value),
    join_time_start:
      formData.value.dateTime.length > 0 ? formData.value.dateTime[0] : "",
    join_time_end:
      formData.value.dateTime.length > 1 ? formData.value.dateTime[1] : ""
  };
  getMemberList(params);
};

const addInMemberModalRef = ref(null);
const onShowMemberFlow = () => {
  addInMemberModalRef.value?.onOpen();
};


const inviteFlowModalRef = ref(null);
const onShowInviteFlow = () => {
  inviteFlowModalRef.value?.onOpen();
};

/**
 *
 * @param idStaff 获取应用统计
 */
 const appCount = ref(0);
const onWorkShopAppAxios = async () => {
  let res: any = null;
  try {

    // res = await workShopAppAxios({uuids: ['cbd']}, currentTeamId.value);
    res = await getStatistics(currentTeamId.value);
    res = getResponseResult(res);
    if (!res) return;
    console.log(res)
    const {data} = res;
    console.log(data);
    // appCount.value = data && (data.length > 0) ? data[0].count: 0;
    appCount.value = data?.apply_count || 0
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    if (errMsg === 'Network Error') {

    } else {
      // MessagePlugin.error(errMsg);
      console.log(errMsg)
    }
  }
};

// 跳转到联系人管理
const onContact = (row) => {
  emits("onSetCurrentRow", toRaw(row));
  setTimeout(() => {
    emits("onSetCurrentPanel", "PContact");
  });
};

// 跳转到申请列表
const onApply = (row) => {
  // emits("onSetCurrentRow", toRaw(row));
  // onLoadingShow();
  setTimeout(() => {
    emits("onSetCurrentPanel", "PApply");
  });
};


// 邀请入会
const inviteQrcodeMemberModalRef = ref(null);
const onInviteMember = () => {
  // if (levelOptions.value.length < 1) {
  //   unSetPositionDialog();
  //   return;
  // }
  onGetTeamsInfo({teamId: currentTeamId.value}).then((res)=> {
    console.log(res.data);
    if(res?.data?.auth === 1) {
       // loading.value = true;
      getInviteLinkAxios().then((val) => {
        // loading.value = false;

        inviteQrcodeMemberModalRef.value.onOpen(val);
      }).catch(() => {
        // loading.value = false;

      });
    } else {
      // MessagePlugin.error('组织未认证');
      goCertifiedDialog(currentTeamId.value)
    }
  });

};
const onGetExitBool = () => {
  return new Promise(async (resolve, reject) => {
    const [err, res] =  await to(getRegularListAxios({page: 1, pageSize: 10}, currentTeamId.value));
    if(err) {
      return reject();
    }

    const { data } = res;
    if(data?.data?.total > 0) {
      return resolve();
    }
    return reject();

  });
}


const inviteActiveQrModalRef = ref(null);
// 邀请激活
const onInviteActiveMember = async () => {
  onGetTeamsInfo({teamId: currentTeamId.value}).then(async (res:any) => {
    if(res?.data?.auth !== 1) {

      goCertifiedDialog(currentTeamId.value)
      return
    }

    // if (levelOptions.value.length < 1) {
    //   unSetPositionDialog();
    //   return;
    // }

    try {
      const res = await onGetExitBool();

    } catch (error) {

      MessagePlugin.error('请先添加或导入租户');

      return ;
    }


    inviteActiveQrModalRef.value.onOpen(
      '',
      "/account/jump?to=commonActive&type=cbd&teamId=" + currentTeamId.value,
      {}
    );
  })
};


const onRowMouseleave = ({ row }) => {
  // console.log(context);
  if (![2, 3].includes(row.status) && row.activate === 2) {
    row.isShow = false;
  }
};

const onRowMousenter = ({ row }) => {
  console.log(row, 'onRowMousenter');
  if (![2, 3].includes(row.status) && row.activate === 2) {
    row.isShow = true;
  }
};



const onCopyLink = () => {
  console.log(levelOptions.value);
  if (levelOptions.value.length < 1) {
    unSetPositionDialog();
    return;
  }

  loading.value = true;
  const path = "/account/jump?to=cbdInvite";
  let link = '';
  getInviteLinkAxios().then(async (val) => {
    loading.value = false;
    if (val) {
      let params = {
        link: val
      };
      // const inviteUrl = getBaseUrl("square-operation-manage");
      link = `${inviteUrl}${path}&${Qs.stringify(params)}`;
    }

    try {
      await toClipboard(link);
      // console.log('Copied to clipboard');
      // MessagePlugin.success("已复制到剪贴板");
      MessagePlugin.success(t('member.winter_column.copy_line'));
    } catch (e) {
      console.error(e);
    }

  }).catch(() => {
    loading.value = false;

  });
};



const inviteQrcodeModalRef = ref(null);

// 邀请入会
// const inviteInQrcodeModalRef = ref(null);
const onInviteInJobClub = () => {
  if (levelOptions.value.length < 1) {
    const confirmDia = DialogPlugin({
      header: "未设置会员职务",
      theme: "info",
      body: "请先前往设置会员职务与会员级别，再添加会员",
      closeBtn: '以后设置',
      confirmBtn: "快速新增",
      className: "delmode",
      onConfirm: async () => {
        confirmDia.hide();
        emits("onPage", "PMembershipPositionPanel");
      },
      onClose: () => {
        confirmDia.hide();
      }
    });
    return;
  }

  getInviteLinkAxios().then((val) => {
    inviteQrcodeModalRef.value.onOpen(val);
  });
};

// 获取邀请链接
const getInviteLinkAxios = () => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await getMemberApplyLinkAxios({}, currentTeamId.value);
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      resolve(result.data);
    } catch (error) {
      reject();
      const errMsg = error instanceof Error ? error.message : error;
      if(errMsg === 'Network Error' ) {

      } else {
        MessagePlugin.error(errMsg);
      }
    }
  });
};


// 邀请激活

const onInviteJobClub = (row: any) => {
  getRegularLinkAxios({ id: row.id }, currentTeamId.value).then((val) => {
    console.log(val);
    inviteQrcodeModalRef.value.onOpen(
      val.data.data.link,
      "/account/jump?to=cbdActiveInvite",
      row
    );
  });
  row.popMoreconfirmVisible = false;
};


/**
 * 获取组织信息
 */
const onGetTeamsInfo = (params)=> {
  return new Promise(async (resolve, reject) => {
    const [err, res] =  await to(getTeamsAxios(params));
    console.log(res);
    if(err) {
      return reject();
    }

    const { data } = res;
    resolve(data);
  });

}



const onShowMore = (row) => {
  // row.popMoreconfirmVisible = true;
  // 关闭其他的
  console.log(row);
  memberData.value
    .filter((v) => v.id !== row.id)
    .map((v) => (v.popMoreconfirmVisible = false));
};

const onVisibleChange = (val, context = {}, row) => {
  console.log(row.popMoreconfirmVisible, "触发了呀哈哈哈");
  if (context && context.trigger === "confirm") {
    // const msg = MessagePlugin.info('提交中');
    // const timer = setTimeout(() => {
    //   MessagePlugin.close(msg);
    //   MessagePlugin.success('提交成功！');
    //   visible.value = false;
    //   clearTimeout(timer);
    // }, 1000);
  } else {
    row.popMoreconfirmVisible = val;
    console.log(row.popMoreconfirmVisible, "哈哈");
  }
};

// 获取列表
const getMemberList = async (params) => {
  // teamId
  params.page = pagination.current;
  params.pageSize = pagination.pageSize;

  loading.value = true;
  try {
    let result = await getRegularListAxios(params, currentTeamId.value);
    loading.value = false;

    console.log(result);
    result = getResponseResult(result);
    if (!result) return;
    memberData.value = result.data.list
      ? result.data.list.map((v) => {
          v.popMoreconfirmVisible = false;

          return v;
        })
      : [];
    pagination.total = result.data.total;
    console.log(memberData.value);
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    if(errMsg === 'Network Error') {

    } else {
      MessagePlugin.error(errMsg);
    }

  }
  loading.value = false;

};

const levelOptions = ref([
  //   {
  //     label: "全部",
  //     value: 0
  //   }
]);

// 获取会员职务列表
const getMemberJobsList = async () => {
  // teamId
  // params.page = pagination.current;
  // params.pageSize = pagination.pageSize;
  try {
    let result: any = await getMemberJobsListAxios();
    console.log(result);
    result = getResponseResult(result);
    if (!result) return;

    levelOptions.value = [
      ...result.data.map((v) => ({
        label: v.level_name,
        value: v.id
      }))
    ];
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    if(errMsg === 'Network Error' ) {

    } else {
      MessagePlugin.error(errMsg);
    }
    // MessagePlugin.error(errMsg);
  }
};

const initData = () => {
  // getMemberJobsList();
  // getMemberList({});
  onSearch();
};
// initData();
// watch(
//   () => store.activeAccount,
//   (val) => {
//     if (val) {
//       // updateAllCount();
//       initData();
//     }
//   },
//   {
//     deep: true
//     // immediate: true
//   }
// );
onMountedOrActivated(() => {
  initData();
  onWorkShopAppAxios();
  emits("getRedNum");
});

ipcRenderer.on('IM-refresh', (e, data) => {
  const { type, data: { extend } } = data || {};
  if (type === IMRefreshType.IMRefreshDigital) {
    if (activeAccount.value) {
      onWorkShopAppAxios();
    }
  }
});

// 下载导入模板
const onDownloadModel = async () => {
  try {
    let result: any = await downloadRegularAxios();
    console.log(result);
    result = getResponseResult(result);
    if (!result) return;
    // // createVNDDownload(result);
    // const blob = new Blob([result], {
    //   type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    // }); // 替换为实际的 XLSX 数据
    // const url = URL.createObjectURL(blob);
    // console.log(url);
    // console.log(`${BaseUrl}/member/download`);
    console.log(result);
    const dres = await ipcRenderer.invoke("download-file", {
      title: "租户导入模板.xlsx",
      url: `${getBaseUrl("client-organize")}/cbd/download`
    });
    if (dres) {
      MessagePlugin.success(`保存成功`);
      importMemberModalRef.value?.onClose();
    }

    // const blob = new Blob([result], {
    //   type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    // });
    // const url = URL.createObjectURL(blob);
    // ipcRenderer.invoke("dow-file-tc", {
    //   title: "模板1.xlsx",
    //   url
    // });
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    MessagePlugin.error(errMsg);
  }
};
// 导入
const uploadRef = ref(null);
const files = ref([]);
const loadingModalRef = ref(null);
const importErrorModalRef = ref(null);

// 2023-12-7 新版优化
const importMemberModalRef = ref(null);
const onUploadFileShow = () => {

  // if (levelOptions.value.length < 1) {
  //   unSetPositionDialog();
  //   return;
  // }


  onGetTeamsInfo({teamId: currentTeamId.value}).then((res)=> {
    console.log(res.data);
    if(res?.data?.auth === 1) {
      importMemberModalRef.value?.onOpen();
    } else {
      // MessagePlugin.error('组织未认证');
      goCertifiedDialog(currentTeamId.value)
    }
  });
};

const onUploadFile = () => {
  uploadRef.value.triggerUpload();
};
const beforeUpload = () => {};
const onSelectChange = async (file, val) => {
  // paddingUdpList.value = val.currentSelectedFiles;
  console.log(file, val);
  loadingModalRef.value.onOpen();
  const fileItem = val.currentSelectedFiles[0];
  console.log(fileItem.raw);
  // const form_data = new FormData();
  // console.log(form_data);
  // form_data.append("additionalData", "value");
  // form_data.append("file", fileItem.raw);
  // console.log(form_data);
  try {
    let result: any = await importRegularAxios({ file: fileItem.raw }, currentTeamId.value);
    result = getResponseResult(result);
    loadingModalRef.value.onClose();
    if (!result) return;

    if (result.code === 0) {
      MessagePlugin.success("导入成功");
      importMemberModalRef.value?.onClose();
      onSearch();
    } else {
      console.log(result.data);
      importErrorModalRef.value.onOpen(result.data);
    }
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    MessagePlugin.error(errMsg);
    loadingModalRef.value.onClose();
  }
};

// 会员续期
const membershipRenewalModalRef = ref(null);
const onMemberRenewal = (row) => {
  onGetRegularDetailAxios(row).then((res) => {
    console.log(res);
    membershipRenewalModalRef.value.onOpen(res);
  });
  row.popMoreconfirmVisible = false;
};

const onSaveMemberRenewal = async (params) => {
  console.log(params);
  let result = null;
  // eslint-disable-next-line no-async-promise-executor

  try {
    result = await renewalRegularAxios(params, currentTeamId.value);
    result = getResponseResult(result);
    if (!result) {
      return;
    }
    MessagePlugin.success("操作成功");
    membershipRenewalModalRef.value.onClose();
    onSearch();
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    MessagePlugin.error(errMsg);
  }
};

// const onMemberRenewal = ()=> {

// }

// 获取设置
const onGetMemberSetting = async () => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await getMemberSettingAxios({}, currentTeamId.value);
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      resolve(result.data);
    } catch (error) {
      const errMsg = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
      reject();
    }
  });
};

const onGetRegularDetailAxios = async (row) => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await getRegularDetailAxios(row.id, {}, currentTeamId.value);
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      resolve(result.data);
    } catch (error) {
      const errMsg = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
      reject();
    }
  });
};

// 会员退会
const memberWithdrawalModalRef = ref(null);
const onMemberWithdraw = (row) => {
  memberWithdrawalModalRef.value.onOpen(row);
  row.popMoreconfirmVisible = false;
};


const onSendSMS = async (row) => {
  try {
    let result: any = await sendActivateSmsAxios({ id: row.id }, currentTeamId.value);
    console.log(result);
    result = getResponseResult(result);
    if (!result) return;
    MessagePlugin.success("短信发送成功！");

  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    MessagePlugin.error(errMsg);
  }
};
// 退会
const onSetWithDrawal = async (data) => {
  console.log(data);
  try {
    let result: any = await exitRegularAxios(data, currentTeamId.value);
    console.log(result);
    result = getResponseResult(result);
    if (!result) return;
    MessagePlugin.success("退会成功");
    memberWithdrawalModalRef.value.onClose();
    onSearch();
    // if (data.isremovePolitics) {
    //   shell.openExternal(AreaUrl);
    // }
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    MessagePlugin.error(errMsg);
  }
};

// 添加会员
const addMemberModalRef = ref(null);
const isHiddenArr = ref([]);
const onAddMember = () => {
  // 去掉会员职务
  // if (levelOptions.value.length < 1) {
  //   unSetPositionDialog();
  //   return;
  // }
  onGetTeamsInfo({teamId:  currentTeamId.value}).then((res)=> {
    console.log(res.data);
    if(res?.data?.auth === 1) {
      isHiddenArr.value = [];
      addMemberModalRef.value.onOpen();
    } else {
      // MessagePlugin.error('组织未认证');
      goCertifiedDialog(currentTeamId.value)
    }
  });

};

// 编辑会员
const onEditMember = (row) => {
  isHiddenArr.value = ["relateRespector"];
  onGetRegularDetailAxios(row).then((res) => {
    ;
    addMemberModalRef.value.onOpen(res);
  });
};


const unSetPositionDialog = () => {
  const confirmDia = DialogPlugin({
    header: t('member.winter_column.invite_1'),
    theme: "info",
    // body: "请先前往设置会员职务与会员级别，再添加会员",
    body: t('member.winter_column.invite_2'),
    closeBtn: false,
    // cancelBtn: '以后设置',
    cancelBtn: t('member.winter_column.invite_3'),
    confirmBtn: t('member.winter_column.invite_4'),
    className: "delmode",
    closeOnOverlayClick: false,
    onConfirm: async () => {
      confirmDia.hide();
      store.setQuickCreatePosTag(true);
      emits("onPage", "PMembershipPositionPanel");
    },
    onClose: () => {
      confirmDia.hide();
    }
  });
};

// 详情
const lookRegularModalRef = ref(null);
const onLookDetail = (row) => {
  // onGetRegularDetailAxios(row).then((res) => {
  //   console.log(res);
  //   lookRegularModalRef.value.onOpen(res);
  // });

  Promise.all([onGetRegularDetailAxios(row), onGetMemberSetting()]).then(
    (res) => {
      console.log(res);
      // 这里要座一层逻辑
      let detailItem: any = res[0];
      // 激活状态，1：已激活，2：未激活
      if (detailItem.activate === 2) {
        // 未激活，读取data数据
        detailItem.submit_data = lodash.cloneDeep(detailItem.data);
        console.log(detailItem.data);
        res[0] = detailItem;
      }


       // 特殊处理一下名录照片赋值问题2024-09-12 start-------------------------------
       if(detailItem?.submit_data?.free_form?.length > 0) {
        const freeForm = detailItem?.submit_data?.free_form;
        const baseList = freeForm.filter((v:any)=> {
          return ['BaseInfoPolitics'].includes(v.type)
        })
        console.log(baseList)
        baseList.map(async (v: any) => {
          // 会员级别的设置
          let origin = null;
          const origins_arr = v.origin;
          origin = origins_arr.find((or: any) => or.vModel === "nameLogo");
          if (origin) {
            origin.value =  detailItem?.submit_data?.directory_image_values  ||  origin.value  || [];
            // console.log(detailItem?.submit_data?.directory_image_values)
          }
          // console.log(origin);
        })
      }

      lookRegularModalRef.value.onOpen(res);
    }
  );
};

const onMore = (val) => {};

const onEnter = () => {};

const filterVisible = ref(false);
const paramsSuper = computed(
  () =>
    formData.value.status ||
    formData.value.dateTime.length ||
    formData.value.level ||
    formData.value.type ||
    formData.value.name ||
    formData.value.activate ||
    formData.value.telephone
);
const paramsSuperFoot = computed(
  () =>
    paramsTemp.value.status ||
    paramsTemp.value.level ||
    paramsTemp.value.type ||
    paramsTemp.value.activate ||
    paramsTemp.value.name ||
    paramsTemp.value.dateTime.length ||
    paramsTemp.value.telephone
);
const showFilter = () => {
  filterVisible.value = true;
};
const paramsTemp = ref({
  level: undefined,
  level_text: undefined,
  status: undefined,
  status_text: undefined,

  type: undefined,
  type_text: undefined,

  telephone: undefined,
  name: undefined,
  activate: undefined,
  activate_text: undefined,
  dateTime: []
});
const statusChange = (e, ctx) => {
  console.log(e, ctx);
  paramsTemp.value.status_text = ctx.option.label;
};

const typeChange = (e, ctx) => {
  console.log(e, ctx);
  paramsTemp.value.type_text = ctx.option.label;
};

const levelChange = (e, ctx) => {
  console.log(e, ctx);
  paramsTemp.value.level_text = ctx.option.label;
};
const activeOptionsChange = (e, ctx) => {
  console.log(e, ctx);
  paramsTemp.value.activate_text = ctx.option.label;
};
const clearFilters = () => {
  formData.value.level = undefined;
  formData.value.status = undefined;
  formData.value.type = undefined;
  formData.value.telephone = undefined;
  formData.value.name = undefined;
  formData.value.activate = undefined;
  formData.value.dateTime = [];
  paramsTemp.value.dateTime = [];
  paramsTemp.value.level = undefined;
  paramsTemp.value.status = undefined;
  paramsTemp.value.telephone = undefined;
  paramsTemp.value.activate = undefined;
  paramsTemp.value.name = undefined;
  onSearch();
};
const clearName = () => {
  formData.value.name = undefined;
  paramsTemp.value.name = undefined;
  onSearch();
};
const clearActivate = () => {
  formData.value.activate = undefined;
  paramsTemp.value.activate = undefined;
  onSearch();
};
const clearStatus = () => {
  formData.value.status = undefined;
  paramsTemp.value.status = undefined;
  onSearch();
};

const clearType = () => {
  formData.value.type = undefined;
  paramsTemp.value.type = undefined;
  onSearch();
};
const clearFiltertelephone = () => {
  formData.value.telephone = undefined;
  paramsTemp.value.telephone = undefined;
  onSearch();
};
const clearlevel = () => {
  formData.value.level = undefined;
  paramsTemp.value.level = undefined;
  onSearch();
};
const clearDateTime = () => {
  formData.value.dateTime = undefined;
  paramsTemp.value.dateTime = undefined;
  onSearch();
};
const getDataRunDr = () => {
  filterVisible.value = false;
  formData.value.level = paramsTemp.value.level;
  formData.value.status = paramsTemp.value.status;
  formData.value.type = paramsTemp.value.type;
  formData.value.telephone = paramsTemp.value.telephone;
  formData.value.dateTime = paramsTemp.value.dateTime;
  formData.value.status_text = paramsTemp.value.status_text;
  formData.value.type_text = paramsTemp.value.type_text;
  formData.value.activate = paramsTemp.value.activate;
  formData.value.name = paramsTemp.value.name;
  formData.value.activate_text = paramsTemp.value.activate_text;
  formData.value.level_text = paramsTemp.value.level_text;
  onSearch();
};
const footerCla = () => {
  filterVisible.value = false;
  paramsTemp.value.level = undefined;
  paramsTemp.value.status = undefined;
  paramsTemp.value.telephone = undefined;
  paramsTemp.value.dateTime = [];
};
</script>

<style lang="less" scoped>
:deep(.t-popconfirm__content) {
}
@import "@renderer/views/member/member_home/panel/public.less";
.main_body {
  display: flex;
  flex-direction: column;
}
.iconsearch {
  font-size: 20px;
}
// :deep(.t-popconfirm__content) {
//   padding: 8px !important;
// }

.pop {
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 144px;
  &-item {
    padding: 8px;

    border-radius: 4px;
    cursor: pointer;
    user-select: none;
    transition: all 0.2s linear;

    display: flex;
    align-items: center;
    img {
      margin-right: 8px;
    }

    &:hover {
      background: var(--kyy_color_dropdown_bg_hover, #F3F6FA);
    }
  }
}
.phone {
  color: var(--text-kyy-color-text-3, #828da5) !important;

  /* kyy_fontSize_2/regular */
  font-family: PingFang SC;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
}
.more-box {
  display: flex;
  width: 28px;
  height: 28px;
  padding: 0px 4px;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  color: #828da5;
  cursor: pointer;
  font-size: 18px;
  transition: all 0.15s linear ;
  .iconmore {
    font-size: 20px;
  }
}

.moreActive {
  border-radius: 4px;
  background: var(--bg-kyy-color-bg-brand-foucs, #dbdfff);
  color: #3e4cd1;
  transition: all 0.15s linear ;
}
.more-box:hover {
  border-radius: 4px;
  background: var(--bg-kyy-color-bg-brand-foucs, #dbdfff);
  color: #3e4cd1;
  transition: all 0.15s linear ;
}
.pop-item {
  display: flex;
  height: 32px;
  min-width: 136px;
  min-height: 32px;
  max-height: 32px;
  padding: 0px 16px;
  align-items: center;
  gap: 12px;
  align-self: stretch;
  color: var(--kyy_color_dropdown_text_default, #1a2139);

  /* kyy_fontSize_2/regular */
  font-family: PingFang SC;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
}
.icondepartment1 {
  font-size: 20px;
  width: 20px;
  height: 20px;
  color: #828da5;
}
.pop-item:hover {
  // color: var(--kyy_color_dropdown_text_active, #4d5eff);
  border-radius: var(--kyy_radius_dropdown_s, 4px);
  background: var(--kyy_color_dropdown_bg_active, #e1eaff);
  .icondepartment1 {
    // color: var(--kyy_color_dropdown_text_active, #4d5eff);
  }
}

.tabsss {
  display: flex;
  position: relative;
  border-bottom: 1px solid var(--divider-kyy-color-divider-light, #eceff5);
  height: 56px;
  width: 100%;
  align-items: center;
  gap: 44px;
  align-self: stretch;
  margin-bottom: 8px;
  .item {
    color: var(--text-kyy-color-text-1, #1a2139);
    text-align: center;

    /* kyy_fontSize_3/regular */
    font-family: PingFang SC;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px; /* 150% */
    cursor: pointer;
    :v-deep(.t-badge--circle) {
      right: -3px;
    }
  }
  .active {
    color: var(--brand-kyy-color-brand-default, #4d5eff);

    /* kyy_fontSize_3/bold */
    font-family: PingFang SC;
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px; /* 150% */
  }
}
.act-tag {
  width: 16px;
  height: 3px;
  flex-shrink: 0;
  border-radius: 1.5px;
  background: var(--brand-kyy-color-brand-default, #4d5eff);
}
.act-1 {
  position: absolute;
  bottom: 0px;
  left: 22px;
}
.act-2 {
  position: absolute;
  bottom: 0px;
  left: 132px;
}

.opt {
  display: flex;
  align-items: center;
  gap: 8px;
  .line {

    height: 24px;
    width: 1px;
    background-color: #D5DBE4;
    margin: auto 0;
  }
  #importList {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}
.su-header {
  align-items: center;
}
:deep(.t-badge) {
  margin-right: 0 !important;
}
:deep(.t-badge--dot) {
  right: 3px;
  margin-top: 2px;

}
:deep(.t-badge--circle) {

  color: var(--kyy_color_tag_text_magenta, #ff4aa1);

  background: var(--kyy_color_badge_bg, #FF4AA1) !important;
}

.person {
  display: inline-flex;
  height: 24px;

  padding: 0px 8px;
  justify-content: center;
  align-items: center;
  gap: 10px;

  color: var(--kyy_color_tag_text_success, #499D60);
  text-align: center;
  border-radius: var(--kyy_radius_tag_s, 4px);
  background: var(--kyy_color_tag_bg_success, #E0F2E5);

  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 22px; /* 157.143% */
}

.column {
  display: flex;
  flex-direction: column;

}

.unactive {
  color: var(--warning-kyy-color-warning-default, #FC7C14);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
  display: flex;
  align-items: center;
  gap: 4px;
  .iconunusual {
    font-size: 20px;
    color: #FC7C14;
  }
}

.sendmessage {
  color: var(--brand-kyy_color_brand_hover, #707EFF);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
  display: flex;
  align-items: center;
  gap: 4px;
  .iconpositioning {
    color: #707EFF;
    width: 20px;
    height: 20px;
    font-size: 20px;
  }
}

.iconadd {
  color: #ffffff;
  font-size: 24px;
}


// :deep(.t-table__empty-row) {
//   td {
//     padding: 0;
//   }
// }

.setE {
  height: 70vh;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
.inSearch {
  height: 32px;
  :deep(.t-input) {
    padding: 0 8px !important;
  }
}
</style>
