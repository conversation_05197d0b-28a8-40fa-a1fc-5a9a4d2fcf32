<template>
  <!-- v-lkloading="{ show:loading, height:false, opacity:false }" -->
  <div class="container">


    <div class="bodys">
      <div class="suheader">
        <div class="suheader-list">
          <div class="suheader-list-item cursor" :class="{active: currentTab === Approval.value}" v-for="Approval in ApprovalStatusList" @click="onTab(Approval)" :key="Approval.value">
            {{Approval.label}}
          </div>
        </div>
      </div>

      <div class="bodys-content">
        <div  class="table">
          <!-- <ApplyFlowComp
            v-show="!loading"
            v-if="!paramsSuper&& !formData.keyword && memberData.length < 1"
            @on-add-member="onAddMember"
            @on-import-member="onUploadFileShow"
            @on-invite-member="onInviteMember"
            @on-copy-link="onCopyLink"
          /> -->
          <!-- @row-mouseleave="onRowMouseleave" -->
          <!-- @row-mouseenter="onRowMousenter" -->
          <t-table
            row-key="id"
            :pagination="
              pagination.total > pagination.pageSize - 1 ? pagination : null
            "
            :columns="memberColumns"
            :data="memberData"
            @row-click="rowClick"
          >
            <template #empty>
              <div class="empty" v-show="!loading">
                <!-- <noData :text="$t('engineer.no_data')" /> -->
                <Empty />
              </div>

            </template>
            <template #expire_time="{ row }">
              <div v-if="row.no_expire">长期有效</div>
              <div v-else>
                {{ $filters.isPeriodEmpty(row.expire_time) }}
              </div>
            </template>
            <template #unit="{ row }">
              <!-- v-show="row.type === 1" -->
               <div>

                <span v-if="row.member_type===2" style="
                border-radius:  4px;
                background:#E0F2E5;
                padding: 2px 4px;
                font-size: 12px;
                margin-right: 4px;
                color:#499D60;
                ">{{t("banch.gr")}}</span>
                 {{row.member_team_name }}
               </div>
            </template>
            <template #dynamic="{ row }">
              <DynamicList  :row="row"></DynamicList>
            </template>
            <template #push_time="{ row }">
              <div class="dynamic">{{ row.created_at }}</div>
            </template>


            <template #operate="{ row }">
              <span class="operates">
                <template v-if="row.status === 5">
                  <t-link theme="primary" hover="color" class="operates-item" @click.stop="onAgree(row)">
                    同意
                  </t-link>
                  <t-link theme="danger" hover="color" class="operates-item" @click.stop="onRejectModal(row)">
                    拒绝
                  </t-link>
                </template>
                <template v-else-if="[ApprovalStatus.Agreed, ApprovalStatus.Rejected, ApprovalStatus.Cancelled, ApprovalStatus.Invalidated].includes(row.status)">
                  <t-tooltip
                    :showArrow="false"
                    :zIndex="888"
                    class="icon_help-tooltip"
                  >
                    <template #content>
                      <div class="tooltip">
                        <div class="tooltip-item" v-show="[ApprovalStatus.Rejected, ApprovalStatus.Cancelled].includes(row.status)">
                          <div class="tooltip-item-title" v-show="ApprovalStatus.Cancelled === row.status">
                            取消原因：
                          </div>
                          <div class="tooltip-item-title" v-show="ApprovalStatus.Rejected === row.status">
                            拒绝原因：
                          </div>
                          <div class="tooltip-item-text">
                            {{ row.reason }}
                          </div>
                        </div>
                        <div class="tooltip-item">
                          <div class="tooltip-item-title">
                            操作时间：
                          </div>
                          <div class="tooltip-item-text">
                            {{ row.operation_date }}
                          </div>
                        </div>
                        <div class="tooltip-item"  v-show="ApprovalStatus.Cancelled !== row.status">
                          <div class="tooltip-item-title">
                            操作人：
                          </div>
                          <div class="tooltip-item-text">
                            {{ row.operator }}
                          </div>
                        </div>
                      </div>
                    </template>
                    <span class="status">
                      {{ ApprovalStatusList.find(v=>v.value === row.status)?.label }}
                      <iconpark-icon name="iconhelp" v-show="[ApprovalStatus.Agreed, ApprovalStatus.Rejected, ApprovalStatus.Cancelled].includes(row.status)" class="iconhelp cursor"></iconpark-icon>
                    </span>
                  </t-tooltip>

                </template>



              </span>
            </template>
          </t-table>
        </div>
      </div>
    </div>
  </div>

  <t-drawer
    v-model:visible="filterVisible"
    :close-btn="true"
    class="drawerSetForm"
    size="472px"
    :header="t('approval.approval_data.sur')"
  >
    <div class="form-boxxx">
      <div class="fitem">
        <div class="title">{{ t("member.regular.status") }}</div>
        <div class="ctl">
          <t-select
            v-model="paramsTemp.status"
            clearable
            :options="memberOptions"
            style="width: 422px"
            @change="statusChange"
          >  <template #suffixIcon>
            <img src="@/assets/svg/icon_arrow_down.svg" />
          </template>
        </t-select>
        </div>
      </div>

      <div class="fitem">
        <div class="title">会员类型</div>
        <div class="ctl">
          <t-select
            v-model="paramsTemp.type"
            clearable
            :options="typeOptions"
            style="width: 422px"
            @change="typeChange"
          >  <template #suffixIcon>
            <img src="@/assets/svg/icon_arrow_down.svg" />
          </template>
        </t-select>
        </div>
      </div>


      <div class="fitem">
        <div class="title">{{ t("member.active.apply_time") }}</div>
        <div class="ctl">
          <t-date-range-picker
            v-model="paramsTemp.dateTime"
            :placeholder="[
              $t('approval.approval_data.start_time'),
              $t('approval.approval_data.end_time')
            ]"
            style="width: 422px"
            clearable
          />
        </div>
      </div>
      <div class="fitem">
        <div class="title">{{ t("member.svip.member_level") }}</div>
        <div class="ctl">
          <t-select
            v-model="paramsTemp.level"
            :options="levelOptions"
            clearable
            style="width: 422px"
            @change="levelChange"
          >  <template #suffixIcon>
            <img src="@/assets/svg/icon_arrow_down.svg" />
          </template>
        </t-select>
        </div>
      </div>
      <div class="fitem">
        <div class="title">{{ t("member.regular.phone") }}</div>
        <div class="ctl">
          <t-input
            v-model="paramsTemp.telephone"
            class="searchForm-item-input"
            :maxlength="50"
            clearable
            :placeholder="$t('member.regular.please_input')"
            style="width: 422px"
          />
        </div>
      </div>
      <!--      <div class="fitem">-->
      <!--        <div class="title">{{ t("member.regular.respect") }}</div>-->
      <!--        <div class="ctl">-->
      <!--          <t-input-->
      <!--            v-model="paramsTemp.name"-->
      <!--            class="searchForm-item-input"-->
      <!--            :maxlength="50"-->
      <!--            clearable-->
      <!--            :placeholder="$t('member.regular.please_input_name')"-->
      <!--            style="width: 422px"-->
      <!--          />-->
      <!--        </div>-->
      <!--      </div>-->
      <div class="fitem">
        <div class="title">{{ t("member.regular.active_status") }}</div>
        <div class="ctl">
          <t-select
            v-model="paramsTemp.activate"
            :options="activeOptions"
            clearable
            style="width: 422px"
            @change="activeOptionsChange"
        >  <template #suffixIcon>
            <img src="@/assets/svg/icon_arrow_down.svg" />
          </template>
        </t-select>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="foot">
        <div class="btn1" @click="footerCla">
          {{ t("approval.approval_data.cl") }}
        </div>
        <div v-if="paramsSuperFoot" class="btn3" @click="getDataRunDr">
          {{ t("approval.approval_data.cm") }}
        </div>
        <div v-else class="btn2">
          {{ t("approval.approval_data.cm") }}
        </div>
      </div>
    </template>
  </t-drawer>

  <!-- <t-upload
    ref="uploadRef"
    v-model="files"
    class="ossUpload"
    theme="custom"
    multiple
    :max="20"
    :action="null"
    :before-upload="beforeUpload"
    :on-select-change="onSelectChange"
  />


  <ImportMemberModal
    ref="importMemberModalRef"
    @on-download-model="onDownloadModel"
    @on-select-change="onSelectChange"
    @on-upload-file-emit="onUploadFile"
    @on-show-member-flow="onShowMemberFlow"
  />
  <ImportErrorModal ref="importErrorModalRef" />
  <LoadingModal ref="loadingModalRef" @reload="onSearch" />
  <MemberWithdrawalModal
    ref="memberWithdrawalModalRef"
    @on-send="onSetWithDrawal"
  />
  <AddMemberModal
    ref="addMemberModalRef"
    :is-hidden-arr="isHiddenArr"
    @reload="onSearch"
    @on-show-member-flow="onShowMemberFlow"
  />
  <MembershipRenewalModal
    ref="membershipRenewalModalRef"
    @on-send="onSaveMemberRenewal"
  />
  <LookRegularModal
    ref="lookRegularModalRef"
    @update-detail="onLookDetail"
    @reload="onSearch"
  />
  <InviteQrcodeModal
    ref="inviteQrcodeModalRef"
    :header-text="$t('member.regular.invite_active')"
    :down-load-text="'扫一扫上面的二维码，申请激活'"
    :tip-text="$t('member.regular.share_qrcode_to_join')"
    :copy-link-text="'复制链接'"
    :type="'active'"
    @on-show-member-flow="onShowMemberFlow"
  />

  <InviteQrcodeModal ref="inviteQrcodeMemberModalRef" :copy-link-text="'复制链接'" @on-show-member-flow="onShowInviteFlow" />
  <AddInMemberModal ref="addInMemberModalRef" />
  <InviteFlowModal ref="inviteFlowModalRef" /> -->
  <RejectModal ref="rejectModalRef" @onSend="onReject"/>

	<PostDetail
    v-if="detailVisible"
    :id="detailId"
    v-model="detailVisible"
    :scroll-to-comment="defaultToolbar === 'comment'"
    :default-toolbar="defaultToolbar"
    only-like-list
    private-hide-share
    from-outer
    team-id="-1"
    hide-setting
    @removed="detailVisible = false;"
  />

</template>

<script lang="ts" setup>
import {
  getMemberJobsListAxios,
  getDynamicsApplyListAxios,
  downloadRegularAxios,
  exitRegularAxios,
  sendActivateSmsAxios,
  importRegularAxios,
  getRegularDetailAxios,
  getMemberSettingAxios,
  getRegularLinkAxios,
  renewalRegularAxios,
  getMemberApplyLinkAxios,
  DynamicsApplyRejectAxios,
  DynamicsApplyAgreeAxios,
} from "@renderer/api/association/api/businessApi";
import { getSrcThumbnail, getSrcLogo } from "@renderer/views/message/service/msgUtils";

import {SquareContentType, ApprovalStatusList, ApprovalStatus, FENGCAIImageDefault, DYNAMICImageDefault} from '@renderer/views/digital-platform/utils/constant'

// import AddInMemberModal from "@renderer/views/member/member_home/panel/regular-member-panel/modal/add-in-member-modal.vue";
// import InviteFlowModal from "@renderer/views/member/member_home/panel/regular-member-panel/modal/invite-flow-modal.vue";
// import MemberWithdrawalModal from "@renderer/views/member/member_home/panel/regular-member-panel/modal/member-withdrawal-modal.vue";
// import AddMemberModal from "@renderer/views/member/member_home/panel/regular-member-panel/modal/add-member-modal.vue";
// import LoadingModal from "@renderer/views/member/member_home/panel/regular-member-panel/modal/loading-modal.vue";
// import ImportErrorModal from "@renderer/views/member/member_home/panel/regular-member-panel/modal/import-error-modal.vue";
// import ImportMemberModal from "@renderer/views/member/member_home/panel/regular-member-panel/modal/import-member-modal.vue";
// import MembershipRenewalModal from "@renderer/views/member/member_home/panel/regular-member-panel/modal/membership-renewal-modal.vue";
// import LookRegularModal from "@renderer/views/member/member_home/panel/regular-member-panel/modal/look-regular-modal.vue";
// import InviteQrcodeModal from "@renderer/views/member/member_home/panel/apply-member-panel/modal/invite-qrcode-modal.vue";
// import ApplyFlowComp from "@renderer/views/member/member_home/panel/regular-member-panel/components/apply-flow-comp.vue";
// import { AddIcon } from "tdesign-icons-vue-next";

// import noData from "@renderer/components/approvalSatellite/approvalData/noData.vue";
import { getResponseResult } from "@renderer/utils/myUtils";
import { FormRule, MessagePlugin, DialogPlugin } from "tdesign-vue-next";
import DynamicList from "@renderer/views/digital-platform/components/DynamicList.vue";

import { computed, onActivated, onMounted, reactive, ref, toRaw, watch } from "vue";
import { useI18n } from "vue-i18n";

import { getBaseUrl } from "@renderer/utils/apiRequest";
import useClipboard from "vue-clipboard3";

import { useAssociationStore } from "@renderer/views/association/store/association";
import lodash from "lodash";
import { inviteUrl } from "@renderer/utils/baseUrl";
import Qs from "qs";
import Empty from "@renderer/components/common/Empty.vue";
import { platform } from "@renderer/views/digital-platform/utils/constant";
import { useDigitalPlatformStore } from "@renderer/views/digital-platform/store/digital-platform-store";
import { useRoute } from "vue-router";
import { getAssociationTeamID } from "@renderer/views/association/utils/auth";
import { onMountedOrActivated } from "@renderer/hooks/onMountedOrActivated";
import { workShopAppAxios } from "@renderer/api/member/api/businessApi";
import { getTeamsAxios } from "@renderer/api/association/api/businessApi";
import to from "await-to-js";
import { goCertifiedDialog } from "@renderer/views/digital-platform/utils/auth";
import { SNAPSHOT } from "@/views/square/constant";
import dayjs from 'dayjs';
import RejectModal from'@renderer/views/association/member_home/panel/trends-panel/modal/reject-modal.vue';
import PostDetail from '@/views/square/components/post/PostDetail.vue';
import LynkerSDK from "@renderer/_jssdk";
const { ipcRenderer } = LynkerSDK;

const emits = defineEmits(["onPage", "onSetCurrentPanel", "onSetCurrentRow", "getRedNum"]);
const store = useAssociationStore();
const { toClipboard } = useClipboard();
const loading = ref(false);
const digitalPlatformStore = useDigitalPlatformStore();
const route = useRoute();

const { t } = useI18n();

const isMoreSearch = ref(false);
// const popMoreconfirmVisible = ref(false);
const rejectModalRef = ref(null);
const statusOptions = [
  // { label: "全部", value: 0 },
  // { label: "待审核", value: 1 },
  // { label: "已通过", value: 2 },
  // { label: "已驳回", value: 3 }
  { label: t('member.winter_column.statusOptions_1'), value: 0 },
  { label: t('member.winter_column.statusOptions_2'), value: 1 },
  { label: t('member.winter_column.statusOptions_3'), value: 2 },
  { label: t('member.winter_column.statusOptions_4'), value: 3 }
];

const memberOptions = [
  // { label: "全部", value: 0 },
  // { label: "正常", value: 1 },
  // { label: "已到期", value: 2 },
  // { label: "已退会", value: 3 }
  { label: t('member.winter_column.memberOptions_1'), value: 1 },
  { label: t('member.winter_column.memberOptions_2'), value: 2 },
  { label: t('member.winter_column.memberOptions_3'), value: 3 }
];

const typeOptions = [
  // { label: "全部", value: 0 },
  // { label: "正常", value: 1 },
  // { label: "单位会员", value: 1 },
  // { label: "个人会员", value: 2 }
  { label: t('member.winter_column.unit'), value: 1 },
  { label: t('member.winter_column.person'), value: 2 },
];

const activeOptions = [
  // { label: "全部", value: 0 },
  // { label: "已激活", value: 1 },
  // { label: "未激活", value: 2 }
  { label: t('member.winter_column.activeOptions_1'), value: 1 },
  { label: t('member.winter_column.activeOptions_2'), value: 2 },

];

const default_text = ref('发布了动态')

const showClassStatus = (val) => {
  // 会员状态，1：正常，2：已到期，3：已退会
  let result = {};
  if (val === 3) {
    result = { gray: true };
  } else if (val === 1) {
    result = { success: true };
  } else if (val === 2) {
    result = { reject: true };
  }
  return result;
};

const FORM_RULES: Record<string, FormRule[]> = {
  // phone: [{ required: false, message: '内容超出', max: 10 }],
};
const formData = ref({
  keyword: "", // 会员名称
  status: undefined, // 会员状态 1：待审核，2：已通过，3：已驳回
  dateTime: [], // 申请开始、结束时间
  level: undefined, // 会员级别
  name: "", // 代表人姓名
  telephone: "", // 手机号
  type: 0, // 入会类型，1：单位，2：个人
  activate: undefined, // 激活状态，1：已激活，2：未激活
  level_text: undefined,
  status_text: undefined,
  activate_text: undefined
});
const form = ref(null);

const onReset = () => {
  form.value.reset();
  onSearch();
};



const props = defineProps({
  platform: {
    type: String,
    default: '',
  },
})
// 平台类型 目前只有digital-platform
const platformCpt = computed(() => {
  return props.platform || route.query?.platform
})

const activeAccount = computed(() => {
  if(platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount
  } else if(platformCpt.value === platform.digitalWorkbench)  {
    return route.query;
  } else {
    return store.activeAccount
  }
})


const currentTeamId = computed(() => {
  if(platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount?.teamId
  } else if(platformCpt.value === platform.digitalWorkbench){
    return route.query?.teamId || 0;
  } else {
    return  getMemberTeamID()
  }
})




const memberColumns = ref([]);
const initColumns = () => {
  // if (formData.value.type === 1) {
    const totalWidth = 944;
    memberColumns.value = [
      // {
      //   colKey: "row-select",
      //   type: "multiple",
      //   width: "4%"
      // },
      { colKey: "dynamic", title: t('member.eb.x'), width: `${280/totalWidth}%` },
      { colKey: "unit", title:  t('member.eb.y'), width: `${150/totalWidth}%`, ellipsis: true  },

      { colKey: "push_time", title: t('member.eb.z'), width: `${112/totalWidth}%`, ellipsis: false },

      { colKey: "operate", title:  '操作',width: `${100/totalWidth}%`, }
    ];
  // } else {
    // memberColumns.value = [
    //   {
    //     colKey: "row-select",
    //     type: "multiple",
    //     width: "5%"
    //   },
    //   { colKey: "team_name", title: "组织名称", width: "10%" },
    //   { colKey: "no", title: "姓名", width: "15%" },
    //   {
    //     colKey: "level_name",
    //     title: "会员级别",
    //     width: "12%",
    //     ellipsis: false,
    //     align: "left"
    //   },
    //   { colKey: "join_time", title: "入会时间", width: "10%", ellipsis: false },
    //   {
    //     colKey: "expire_time",
    //     title: "过期时间",
    //     width: "10%",
    //     ellipsis: false
    //   },
    //   { colKey: "status", title: "状态", width: "8%" },
    //   { colKey: "operate", title: "操作", width: "15%" }
    // ];
 // }
};


const currentTab = ref(ApprovalStatus.All);

const onTab = (tab) => {
  currentTab.value = tab.value;
  onSearch();
}



initColumns();
const filterStatusText = (val) => {
  // 会员状态，1：正常，2：已到期，3：已退会，0：未激活
  // const arr = ["未激活", "正常", "已到期", "已退会"];
  const arr = [
    t('member.winter_column.activeOptions_2'),
    t('member.winter_column.memberOptions_1'),
    t('member.winter_column.memberOptions_2'),
    t('member.winter_column.memberOptions_3'),
  ];

  return val ? arr[val] : "--";
};


const onSwitchTab = (val) => {
  formData.value.type = val;
  pagination.current = 1;
  initColumns();
  onSearch();
};

const setMoreSearch = () => {
  isMoreSearch.value = !isMoreSearch.value;
};



const memberData = ref([]);
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
  onChange: (pageInfo) => {
    console.log("pagination.onChange", pageInfo);
    pagination.current = pageInfo.current;
    pagination.pageSize = pageInfo.pageSize;


    const params = {
      ...toRaw(formData.value),
      join_time_start:
        formData.value.dateTime.length > 0 ? formData.value.dateTime[0] : "",
      join_time_end:
        formData.value.dateTime.length > 1 ? formData.value.dateTime[1] : ""
    };
    getMemberList(params);
    // onSearch();
  }
});

const onSearch = () => {
  pagination.current = 1;

  const params = {
    ...toRaw(formData.value),
    // join_time_start:
    //   formData.value.dateTime.length > 0 ? formData.value.dateTime[0] : "",
    // join_time_end:
    //   formData.value.dateTime.length > 1 ? formData.value.dateTime[1] : ""
    status: currentTab.value
  };
  getMemberList(params);
};

const addInMemberModalRef = ref(null);
const onShowMemberFlow = () => {
  addInMemberModalRef.value?.onOpen();
};


const inviteFlowModalRef = ref(null);
const onShowInviteFlow = () => {
  inviteFlowModalRef.value?.onOpen();
};

const onAgree = async (row)=> {
  let [err, res] = await to(DynamicsApplyAgreeAxios({application_dynamics_log_uuid: row.application_dynamics_log_uuid}, currentTeamId.value));
  if(err) {

    const confirmDia = DialogPlugin({
      header: '提示',
      theme: 'info',
      body: err?.message,
      closeBtn: null,
      confirmBtn: '确定',
      className: 'delmode',
      onConfirm: async () => {
        confirmDia.hide();
      },
      onClose: () => {
        confirmDia.hide();
      },
    });
    return;
  }
  emits('getRedNum');
  MessagePlugin.success('已同意');
  onSearch();
}
const onRejectModal = (row) => {
  rejectModalRef.value.onOpen({id: row.application_dynamics_log_uuid});
}

const onReject = async (row)=> {
  console.log(row)
  let [err, res] = await to(DynamicsApplyRejectAxios({reason: row.area, application_dynamics_log_uuid: row.id}, currentTeamId.value));
  if(err) {

    const confirmDia = DialogPlugin({
      header: '提示',
      theme: 'info',
      body: err?.message,
      closeBtn: null,
      confirmBtn: '确定',
      className: 'delmode',
      onConfirm: async () => {
        confirmDia.hide();
      },
      onClose: () => {
        confirmDia.hide();
      },
    });
    return;
  }
  emits('getRedNum');
  onSearch();
}

/**
 *
 * @param idStaff 获取应用统计
 */
 const appCount = ref(0);
const onWorkShopAppAxios = async () => {
  let res: any = null;
  try {

    res = await workShopAppAxios({uuids: ['member']}, currentTeamId.value);
    res = getResponseResult(res);
    if (!res) return;
    console.log(res)
    const {data} = res;
    console.log(data);
    appCount.value = data || 0;

  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    if (errMsg === 'Network Error') {

    } else {
      // MessagePlugin.error(errMsg);
      console.log(errMsg)
    }
  }
};



// 跳转到联系人管理
const onContact = (row) => {
  emits("onSetCurrentRow", toRaw(row));
  setTimeout(() => {
    emits("onSetCurrentPanel", "PContact");
  });
};

// 跳转到申请列表
const onApply = (row) => {
  // emits("onSetCurrentRow", toRaw(row));
  // onLoadingShow();
  setTimeout(() => {
    emits("onSetCurrentPanel", "PApply");
  });
};


// 邀请入会
const inviteQrcodeMemberModalRef = ref(null);
const onInviteMember = () => {

  onGetTeamsInfo({teamId: currentTeamId.value}).then((res:any) => {
    if(res?.data?.auth !== 1) {

      goCertifiedDialog(currentTeamId.value)
      return
    }

    if (levelOptions.value.length < 1) {
      unSetPositionDialog();
      return;
    }

    // loading.value = true;
    getInviteLinkAxios().then((val) => {
      // loading.value = false;

      inviteQrcodeMemberModalRef.value.onOpen(val);
    }).catch(() => {
      // loading.value = false;

    });
  })



};


const onRowMouseleave = ({ row }) => {
  // console.log(context);
  if (![2, 3].includes(row.status) && row.activate === 2) {
    row.isShow = false;
  }
};

const onRowMousenter = ({ row }) => {
  console.log(row, 'onRowMousenter');
  if (![2, 3].includes(row.status) && row.activate === 2) {
    row.isShow = true;
  }
};



const onCopyLink = () => {
  console.log(levelOptions.value);
  if (levelOptions.value.length < 1) {
    unSetPositionDialog();
    return;
  }

  loading.value = true;
  const path = "/account/jump?to=memberInvite";
  let link = '';
  getInviteLinkAxios().then(async (val) => {
    loading.value = false;
    if (val) {
      let params = {
        link: val
      };
      // const inviteUrl = getBaseUrl("square-operation-manage");
      link = `${inviteUrl}${path}&${Qs.stringify(params)}`;
    }

    try {
      await toClipboard(link);
      // console.log('Copied to clipboard');
      // MessagePlugin.success("已复制到剪贴板");
      MessagePlugin.success(t('member.winter_column.copy_line'));
    } catch (e) {
      console.error(e);
    }

  }).catch(() => {
    loading.value = false;

  });
};



const inviteQrcodeModalRef = ref(null);

// 邀请入会
// const inviteInQrcodeModalRef = ref(null);
const onInviteInJobClub = () => {
  if (levelOptions.value.length < 1) {
    const confirmDia = DialogPlugin({
      header: "未设置会员职务",
      theme: "info",
      body: "请先前往设置会员职务与会员级别，再添加会员",
      closeBtn: '以后设置',
      confirmBtn: "快速新增",
      className: "delmode",
      onConfirm: async () => {
        confirmDia.hide();
        emits("onPage", "PMembershipPositionPanel");
      },
      onClose: () => {
        confirmDia.hide();
      }
    });
    return;
  }

  getInviteLinkAxios().then((val) => {
    inviteQrcodeModalRef.value.onOpen(val);
  });
};

// 获取邀请链接
const getInviteLinkAxios = () => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await getMemberApplyLinkAxios({}, currentTeamId.value);
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      resolve(result.data.link);
    } catch (error) {
      reject();
      const errMsg = error instanceof Error ? error.message : error;
      if(errMsg === 'Network Error' ) {

      } else {
        MessagePlugin.error(errMsg);
      }
    }
  });
};


// 邀请激活

const onInviteJobClub = (row: any) => {
  getRegularLinkAxios({ id: row.id }, currentTeamId.value).then((val) => {
    console.log(val);
    inviteQrcodeModalRef.value.onOpen(
      val.data.data.link,
      "/account/jump?to=memberActiveInvite",
      row
    );
  });
  row.popMoreconfirmVisible = false;
};




/**
 * 获取组织信息
 */
 const onGetTeamsInfo = (params)=> {
  return new Promise(async (resolve, reject) => {
    const [err, res] =  await to(getTeamsAxios(params));
    console.log(res);
    if(err) {
      return reject();
    }

    const { data } = res;
    resolve(data);
  });

}



const onShowMore = (row) => {
  // row.popMoreconfirmVisible = true;
  // 关闭其他的
  console.log(row);
  memberData.value
    .filter((v) => v.id !== row.id)
    .map((v) => (v.popMoreconfirmVisible = false));
};

const onVisibleChange = (val, context = {}, row) => {
  console.log(row.popMoreconfirmVisible, "触发了呀哈哈哈");
  if (context && context.trigger === "confirm") {
    // const msg = MessagePlugin.info('提交中');
    // const timer = setTimeout(() => {
    //   MessagePlugin.close(msg);
    //   MessagePlugin.success('提交成功！');
    //   visible.value = false;
    //   clearTimeout(timer);
    // }, 1000);
  } else {
    row.popMoreconfirmVisible = val;
    console.log(row.popMoreconfirmVisible, "哈哈");
  }
};

// 获取列表
const getMemberList = async (params) => {
  // teamId
  params.page = pagination.current;
  params.pageSize = pagination.pageSize;

  loading.value = true;
  try {
    let result = await getDynamicsApplyListAxios(params, currentTeamId.value);
    loading.value = false;

    console.log(result);
    result = getResponseResult(result);
    if (!result) return;
    memberData.value = result.data.list
      ? result.data.list.map((v) => {
          v.popMoreconfirmVisible = false;
          if( v.push_type === SquareContentType.VIDEO) {
            v.img = v.img ? `${v.img}${SNAPSHOT}` : ''
          } else {
            v.img = v.img ? getSrcLogo(v.img) : '';
          }
          return v;
        })
      : [];
    pagination.total = result.data.total;
    console.log(memberData.value);
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    if(errMsg === 'Network Error') {

    } else {
      MessagePlugin.error(errMsg);
    }

  }
  loading.value = false;

};

const levelOptions = ref([
  //   {
  //     label: "全部",
  //     value: 0
  //   }
]);

// 获取会员职务列表
const getMemberJobsList = async () => {
  // teamId
  // params.page = pagination.current;
  // params.pageSize = pagination.pageSize;
  try {
    let result: any = await getMemberJobsListAxios({}, currentTeamId.value);
    console.log(result);
    result = getResponseResult(result);
    if (!result) return;

    levelOptions.value = [
      ...result.data.map((v) => ({
        label: v.level_name,
        value: v.id
      }))
    ];
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    if(errMsg === 'Network Error' ) {

    } else {
      MessagePlugin.error(errMsg);
    }
    // MessagePlugin.error(errMsg);
  }
};

const initData = () => {
  // getMemberJobsList();
  getMemberList({});
  onSearch();
};
onMountedOrActivated(() => {
  initData();
  console.log('执行了多少次')
  onWorkShopAppAxios();
});
// watch(
//   () => store.activeAccount,
//   (val) => {
//     if (val) {
//       // updateAllCount();
//       initData();
//     }
//   },
//   {
//     deep: true
//     // immediate: true
//   }
// );
// 下载导入模板
const onDownloadModel = async () => {
  try {
    let result: any = await downloadRegularAxios({}, currentTeamId.value);
    console.log(result);
    result = getResponseResult(result);
    if (!result) return;
    // // createVNDDownload(result);
    // const blob = new Blob([result], {
    //   type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    // }); // 替换为实际的 XLSX 数据
    // const url = URL.createObjectURL(blob);
    // console.log(url);
    // console.log(`${BaseUrl}/member/download`);
    console.log(result);
    const dres = await ipcRenderer.invoke("download-file", {
      title: "会员导入模板.xlsx",
      url: `${getBaseUrl("client-organize")}/member/download`
    });
    if (dres) {
      MessagePlugin.success(`保存成功`);
      importMemberModalRef.value?.onClose();
    }

    // const blob = new Blob([result], {
    //   type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    // });
    // const url = URL.createObjectURL(blob);
    // ipcRenderer.invoke("dow-file-tc", {
    //   title: "模板1.xlsx",
    //   url
    // });
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    MessagePlugin.error(errMsg);
  }
};
// 导入
const uploadRef = ref(null);
const files = ref([]);
const loadingModalRef = ref(null);
const importErrorModalRef = ref(null);

// 2023-12-7 新版优化
const importMemberModalRef = ref(null);
const onUploadFileShow = () => {

  onGetTeamsInfo({teamId: currentTeamId.value}).then((res:any) => {
    if(res?.data?.auth !== 1) {
      //  MessagePlugin.error('组织未认证');
      goCertifiedDialog(currentTeamId.value)
      return
    }
    if (levelOptions.value.length < 1) {
      unSetPositionDialog();
      return;
    }

    importMemberModalRef.value?.onOpen();
  });

};

const onUploadFile = () => {
  uploadRef.value.triggerUpload();
};
const beforeUpload = () => {};
const onSelectChange = async (file, val) => {
  // paddingUdpList.value = val.currentSelectedFiles;
  console.log(file, val);
  loadingModalRef.value.onOpen();
  const fileItem = val.currentSelectedFiles[0];
  console.log(fileItem.raw);
  // const form_data = new FormData();
  // console.log(form_data);
  // form_data.append("additionalData", "value");
  // form_data.append("file", fileItem.raw);
  // console.log(form_data);
  try {
    let result: any = await importRegularAxios({ file: fileItem.raw }, currentTeamId.value);
    result = getResponseResult(result);
    loadingModalRef.value.onClose();
    if (!result) return;

    if (result.code === 0) {
      MessagePlugin.success("导入成功");
      importMemberModalRef.value?.onClose();
      onSearch();
    } else {
      console.log(result.data);
      importErrorModalRef.value.onOpen(result.data);
    }
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    MessagePlugin.error(errMsg);
    loadingModalRef.value.onClose();
  }
};

// 会员续期
const membershipRenewalModalRef = ref(null);
const onMemberRenewal = (row) => {
  onGetRegularDetailAxios(row).then((res) => {
    console.log(res);
    membershipRenewalModalRef.value.onOpen(res);
  });
  row.popMoreconfirmVisible = false;
};

const onSaveMemberRenewal = async (params) => {
  console.log(params);
  let result = null;
  // eslint-disable-next-line no-async-promise-executor

  try {
    result = await renewalRegularAxios(params, currentTeamId.value);
    result = getResponseResult(result);
    if (!result) {
      return;
    }
    MessagePlugin.success("操作成功");
    membershipRenewalModalRef.value.onClose();
    onSearch();
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    MessagePlugin.error(errMsg);
  }
};

// const onMemberRenewal = ()=> {

// }

// 获取设置
const onGetMemberSetting = async () => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await getMemberSettingAxios({}, currentTeamId.value);
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      resolve(result.data);
    } catch (error) {
      const errMsg = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
      reject();
    }
  });
};

const onGetRegularDetailAxios = async (row) => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await getRegularDetailAxios(row.id, {}, currentTeamId.value);
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      resolve(result.data);
    } catch (error) {
      const errMsg = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
      reject();
    }
  });
};

// 会员退会
const memberWithdrawalModalRef = ref(null);
const onMemberWithdraw = (row) => {
  memberWithdrawalModalRef.value.onOpen(row);
  row.popMoreconfirmVisible = false;
};


const onSendSMS = async (row) => {
  try {
    let result: any = await sendActivateSmsAxios({ id: row.id }, currentTeamId.value);
    console.log(result);
    result = getResponseResult(result);
    if (!result) return;
    MessagePlugin.success("短信发送成功！");

  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    MessagePlugin.error(errMsg);
  }
};
// 退会
const onSetWithDrawal = async (data) => {
  console.log(data);
  try {
    let result: any = await exitRegularAxios(data,currentTeamId.value);
    console.log(result);
    result = getResponseResult(result);
    if (!result) return;
    MessagePlugin.success("退会成功");
    memberWithdrawalModalRef.value.onClose();
    onSearch();
    // if (data.isRemoveMember) {
    //   shell.openExternal(AreaUrl);
    // }
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    MessagePlugin.error(errMsg);
  }
};

// 添加会员
const addMemberModalRef = ref(null);
const isHiddenArr = ref([]);
const onAddMember = () => {



  onGetTeamsInfo({teamId:  currentTeamId.value}).then((res:any)=> {
    console.log(res.data);
    if(res?.data?.auth !== 1) {
      //  MessagePlugin.error('组织未认证');
      goCertifiedDialog(currentTeamId.value)
      return
    }
    if (levelOptions.value.length < 1) {
      unSetPositionDialog();
      return;
    }
    isHiddenArr.value = [];
    addMemberModalRef.value.onOpen();
  });
};

// 编辑会员
const onEditMember = (row) => {
  isHiddenArr.value = ["relateRespector"];
  onGetRegularDetailAxios(row).then((res) => {
    addMemberModalRef.value.onOpen(res);
  });
};


const unSetPositionDialog = () => {
  const confirmDia = DialogPlugin({
    header: t('member.winter_column.invite_1'),
    theme: "info",
    // body: "请先前往设置会员职务与会员级别，再添加会员",
    body: t('member.winter_column.invite_2'),
    closeBtn: false,
    // cancelBtn: '以后设置',
    cancelBtn: t('member.winter_column.invite_3'),
    confirmBtn: t('member.winter_column.invite_4'),
    className: "delmode",
    closeOnOverlayClick: false,
    onConfirm: async () => {
      confirmDia.hide();
      store.setQuickCreatePosTag(true);
      emits("onPage", "PMembershipPositionPanel");
    },
    onClose: () => {
      confirmDia.hide();
    }
  });
};

// 详情
const lookRegularModalRef = ref(null);
const onLookDetail = (row) => {
  // onGetRegularDetailAxios(row).then((res) => {
  //   console.log(res);
  //   lookRegularModalRef.value.onOpen(res);
  // });

  Promise.all([onGetRegularDetailAxios(row), onGetMemberSetting()]).then(
    (res) => {
      console.log(res);
      // 这里要座一层逻辑
      let detailItem: any = res[0];
      // 激活状态，1：已激活，2：未激活
      if (detailItem.activate === 2) {
        // 未激活，读取data数据
        detailItem.submit_data = lodash.cloneDeep(detailItem.data);
        console.log(detailItem.data);
        res[0] = detailItem;
      }

      lookRegularModalRef.value.onOpen(res);
    }
  );
};

const onMore = (val) => {};

const onEnter = () => {};

const filterVisible = ref(false);
const paramsSuper = computed(
  () =>
    formData.value.status ||
    formData.value.dateTime.length ||
    formData.value.level ||
    formData.value.type ||
    formData.value.name ||
    formData.value.activate ||
    formData.value.telephone
);
const paramsSuperFoot = computed(
  () =>
    paramsTemp.value.status ||
    paramsTemp.value.level ||
    paramsTemp.value.type ||
    paramsTemp.value.activate ||
    paramsTemp.value.name ||
    paramsTemp.value.dateTime.length ||
    paramsTemp.value.telephone
);
const showFilter = () => {
  filterVisible.value = true;
};
const paramsTemp = ref({
  level: undefined,
  level_text: undefined,
  status: undefined,
  status_text: undefined,

  type: undefined,
  type_text: undefined,

  telephone: undefined,
  name: undefined,
  activate: undefined,
  activate_text: undefined,
  dateTime: []
});
const statusChange = (e, ctx) => {
  console.log(e, ctx);
  paramsTemp.value.status_text = ctx.option.label;
};

const typeChange = (e, ctx) => {
  console.log(e, ctx);
  paramsTemp.value.type_text = ctx.option.label;
};

const levelChange = (e, ctx) => {
  console.log(e, ctx);
  paramsTemp.value.level_text = ctx.option.label;
};
const activeOptionsChange = (e, ctx) => {
  console.log(e, ctx);
  paramsTemp.value.activate_text = ctx.option.label;
};
const clearFilters = () => {
  formData.value.level = undefined;
  formData.value.status = undefined;
  formData.value.type = undefined;
  formData.value.telephone = undefined;
  formData.value.name = undefined;
  formData.value.activate = undefined;
  formData.value.dateTime = [];
  paramsTemp.value.dateTime = [];
  paramsTemp.value.level = undefined;
  paramsTemp.value.status = undefined;
  paramsTemp.value.telephone = undefined;
  paramsTemp.value.activate = undefined;
  paramsTemp.value.type = undefined;
  paramsTemp.value.name = undefined;
  onSearch();
};
const clearName = () => {
  formData.value.name = undefined;
  paramsTemp.value.name = undefined;
  onSearch();
};
const clearActivate = () => {
  formData.value.activate = undefined;
  paramsTemp.value.activate = undefined;
  onSearch();
};
const clearStatus = () => {
  formData.value.status = undefined;
  paramsTemp.value.status = undefined;
  onSearch();
};

const clearType = () => {
  formData.value.type = undefined;
  paramsTemp.value.type = undefined;
  onSearch();
};
const clearFiltertelephone = () => {
  formData.value.telephone = undefined;
  paramsTemp.value.telephone = undefined;
  onSearch();
};
const clearlevel = () => {
  formData.value.level = undefined;
  paramsTemp.value.level = undefined;
  onSearch();
};
const clearDateTime = () => {
  formData.value.dateTime = undefined;
  paramsTemp.value.dateTime = undefined;
  onSearch();
};
const getDataRunDr = () => {
  filterVisible.value = false;
  formData.value.level = paramsTemp.value.level;
  formData.value.status = paramsTemp.value.status;
  formData.value.type = paramsTemp.value.type;
  formData.value.telephone = paramsTemp.value.telephone;
  formData.value.dateTime = paramsTemp.value.dateTime;
  formData.value.status_text = paramsTemp.value.status_text;
  formData.value.type_text = paramsTemp.value.type_text;
  formData.value.activate = paramsTemp.value.activate;
  formData.value.name = paramsTemp.value.name;
  formData.value.activate_text = paramsTemp.value.activate_text;
  formData.value.level_text = paramsTemp.value.level_text;
  onSearch();
};
const footerCla = () => {
  filterVisible.value = false;
  paramsTemp.value.level = undefined;
  paramsTemp.value.status = undefined;
  paramsTemp.value.telephone = undefined;
  paramsTemp.value.dateTime = [];
};

const detailVisible = ref(false);
const detailId = ref('');
const postTeamId=ref('-1')
const rowClick = ({ row }) => {
  postTeamId.value= row.member_type===1?currentTeamId.value:"-1"

  console.log(row)
  detailId.value = row.origin_id;
  detailVisible.value = true;
};

// 打开对应的详情页
const defaultToolbar = ref('');
const toolbarClick = (row) => {
  // defaultToolbar.value = type;
  rowClick({ row });
};

</script>

<style lang="less" scoped>
:deep(.t-popconfirm__content) {
}
@import "@renderer/views/member/member_home/panel/public.less";
@import "@renderer/views/member/member_home/panel/trends-panel/panel/trends-apply.less";

</style>
