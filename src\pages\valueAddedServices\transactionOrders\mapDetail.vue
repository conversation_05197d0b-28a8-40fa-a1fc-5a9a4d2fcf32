<template>
	<t-drawer
		v-model:visible="routeMapVisible"
		class="route-map-drawer"
		destroy-on-close
		:header="true"
		size="472px"
		:footer="false"
	>
		<template #header>
			<div class="header-box">
				<div style="display: flex; align-items: center; width: 100%; justify-content: space-between">
					<div>配送详情</div>
					<iconpark-icon
						name="iconerror-a961a3n0"
						style="font-size: 24px; cursor: pointer"
						@click="routeMapVisible = false"
					></iconpark-icon>
				</div>
				<div class="order-time">下单时间：{{ orderTime }}</div>
			</div>
		</template>
		<div class="route-map-info-box">
			<div class="route-map-box">
				<r-baidu-map-route-card v-if="end.lat !== 0" :start="start" :end="end" :rider="rider" />
			</div>
			<div class="mapDetail-content">
				<p
					v-if="deliverType === 1"
					class="mapDetail-content-title"
					:style="{
						color:
							contentDetail.deliveryStatus === 1
								? '#FC7C14'
								: contentDetail.deliveryStatus === 2
								? '#4093E0'
								: contentDetail.deliveryStatus === 3
								? '#499D60'
								: contentDetail.deliveryStatus === 4
								? '#499D60'
								: '#516082',
					}"
				>
					{{ qishou_status(contentDetail.deliveryStatus) }}
				</p>
				<p
					v-if="deliverType === 2"
					class="mapDetail-content-title"
					:style="{
						color:
							contentDetail.status === 'MERCHANT_STATUS_RECEIVED'
								? '#FC7C14'
								: contentDetail.status === 'MERCHANT_STATUS_DELIVERED'
								? '#4093E0'
								: contentDetail.status === 'MERCHANT_STATUS_DONE'
								? '#499D60'
								: '#516082',
					}"
				>
					{{ sj_status(contentDetail.status) }}
				</p>
				<div v-if="deliverType === 1" class="mapDetail-content-mes" style="margin: 12px 0">
					<div class="content-mes-item">
						<span class="mes-item-left">商家联系方式</span>
						<span class="mes-item-right"
							>{{ contentDetail.shopInfo?.phone }} ({{ contentDetail.shopInfo?.name || '--' }})</span
						>
					</div>
					<div class="content-mes-item">
						<span class="mes-item-left">商家地址</span>
						<span class="mes-item-right">{{ contentDetail.shopInfo?.address?.address }}</span>
					</div>
					<div
						v-if="
							contentDetail.deliveryStatus !== 1 &&
							contentDetail.deliveryStatus !== 2 &&
							contentDetail.deliveryStatus !== 5 &&
							contentDetail.deliveryStatus !== 8
						"
						class="content-mes-item"
					>
						<span class="mes-item-left">骑手联系方式</span>
						<span class="mes-item-right"
							>{{ contentDetail.deliveryManInfo?.phoneNum }} ({{ contentDetail.deliveryManInfo?.name || '--' }})</span
						>
					</div>
					<div class="content-mes-item">
						<span class="mes-item-left">收货人联系方式</span>
						<span class="mes-item-right"
							>{{ contentDetail.receiverInfo?.phone }} ({{ contentDetail.receiverInfo?.name || '--' }})</span
						>
					</div>
					<div class="content-mes-item">
						<span class="mes-item-left">收货人地址</span>
						<span class="mes-item-right">
							<span v-if="contentDetail.receiverInfo?.address.location.address">
								{{ contentDetail.receiverInfo?.address.location.address }}
								{{ contentDetail.receiverInfo?.address.location.name }}
								{{ contentDetail.receiverInfo?.address.houseNumber }}
							</span>
							<span v-else>--</span>
						</span>
					</div>
					<div class="content-mes-item">
						<span class="mes-item-left">买家支付配送费</span>
						<span class="mes-item-right">
							{{ (contentDetail.customerDeliveryAmount / 100).toFixed(2) || '0.00' }}
							<span v-if="contentDetail.status === 6">(已退回)</span>
						</span>
					</div>
					<div v-if="contentDetail.status === 6" class="content-mes-item">
						<span class="mes-item-left">取消订单违约金</span>
						<span class="mes-item-right">
							{{ contentDetail.punishAmount || '--' }}
						</span>
					</div>
					<div v-if="contentDetail.status === 7" class="content-mes-item">
						<span class="mes-item-left">实际送达时间</span>
						<span class="mes-item-right">{{ contentDetail.arriveTime || '--' }}</span>
					</div>
					<div
						v-if="
							contentDetail.deliveryStatus !== 1 &&
							contentDetail.deliveryStatus !== 2 &&
							contentDetail.deliveryStatus !== 5 &&
							contentDetail.deliveryStatus !== 8
						"
						class="content-mes-item"
					>
						<span class="mes-item-left">配送实际费用</span>
						<span class="mes-item-right">{{ contentDetail.discountLastMoney || '--' }}</span>
					</div>
				</div>
				<div v-else-if="deliverType === 2" class="mapDetail-content-mes">
					<div class="content-mes-item">
						<span class="mes-item-left">商家联系方式</span>
						<span class="mes-item-right">{{ contentDetail.shop?.phone }} ({{ contentDetail.shop?.name || '--' }})</span>
					</div>
					<div class="content-mes-item">
						<span class="mes-item-left">商家地址</span>
						<span class="mes-item-right">
							<span v-if="contentDetail.shop?.address.location.address">
								{{ contentDetail.shop?.address.location.address }}
								{{ contentDetail.shop?.address.location.name }} {{ contentDetail.shop?.address.houseNumber }}
							</span>
							<span v-else>--</span>
						</span>
					</div>
					<div class="content-mes-item">
						<span class="mes-item-left">收货人联系方式</span>
						<span class="mes-item-right"
							>{{ contentDetail.receiver?.phone }} {{ contentDetail.receiver?.name || '--' }}</span
						>
					</div>
					<div class="content-mes-item">
						<span class="mes-item-left">收货人地址</span>
						<span class="mes-item-right" style="margin-right: 0">
							<span v-if="contentDetail.receiver?.address.location.address">
								{{ contentDetail.receiver?.address.location.address }}
								{{ contentDetail.receiver?.address.location.name }} {{ contentDetail.receiver?.address.houseNumber }}
							</span>
							<span v-else>--</span>
						</span>
					</div>
					<div class="content-mes-item">
						<span class="mes-item-left">买家支付配送费</span>
						<span class="mes-item-right">
							{{ (contentDetail.deliveryFee / 100).toFixed(2) || '0.00' }}
							<span v-if="contentDetail.status === 'MERCHANT_STATUS_CANCELLED'">(已退回)</span>
						</span>
					</div>
				</div>
			</div>
		</div>
	</t-drawer>
</template>
<script setup lang="ts">
import { ref } from 'vue';
import { RBaiduMapRouteCard } from '@rk/unitPark';
import dayjs from 'dayjs';

// import { MessagePlugin } from 'tdesign-vue-next';
import { getIntraCityDeliveryDetail, queryMerchantDeliveryOrder } from '@/api/store';

const props = defineProps({
	hasfooter: {
		type: Boolean,
		default: false,
		description: '是否显示底部',
	},
});
const orderTime = ref('');
const routeMapVisible = ref(false);
const rider = ref({
	lat: 0,
	lng: 0,
});
const end = ref({
	// lat: 40.003417, lng: 116.310791
	lat: 0,
	lng: 0,
});
const start = ref({
	// lat: 40.013417, lng:116.310791
	lat: 0,
	lng: 0,
});
const contentDetail = ref({});

const qishou_status = (type) => {
	switch (type) {
		case 1:
			return '新订单';
		case 2:
			return '待骑手接单';
		case 3:
			return '骑手取货中';
		case 4:
			return '骑手配送中';
		case 5:
			return '当前配送订单异常';
		case 6:
			return '订单已退款';
		case 7:
			return '骑手已送达';
		case 8:
			return '订单已取消';
		default:
			return '--';
	}
};

const sj_status = (type) => {
	switch (type) {
		case 'MERCHANT_STATUS_RECEIVED':
			return '待商家出货';
		case 'MERCHANT_STATUS_DELIVERED':
			return '商家配送中';
		case 'MERCHANT_STATUS_DONE':
			return '商家已送达';
		case 'MERCHANT_STATUS_CANCELLED':
			return '商家已取消';
		default:
			return '--';
	}
};

// type === 1 是 骑手
// type === 2 是 商家
const deliverType = ref(1);
const openWin = (val, type) => {
	deliverType.value = type;
	let params = {};
	if (type === 1) {
		params = {
			serialNumber: val.serialNumber,
			orderId: val.orderId,
		};
		getIntraCityDeliveryDetail(params).then((res) => {
			if (res.data.code === 0) {
				orderTime.value = dayjs(res.data.data.orderTime * 1000).format('YYYY-MM-DD HH:mm');
				contentDetail.value = res.data.data;
				start.value.lat = res.data.data.shopInfo?.address?.location?.latLng?.latitude;
				start.value.lng = res.data.data.shopInfo?.address?.location?.latLng?.longitude;
				end.value.lat = res.data.data.receiverInfo?.address?.location?.latLng?.latitude;
				end.value.lng = res.data.data.receiverInfo?.address?.location?.latLng?.longitude;
				setTimeout(() => {
					routeMapVisible.value = true;
				}, 2000);
			}
		});
	} else {
		params = {
			orderId: val.orderId,
		};
		queryMerchantDeliveryOrder(params).then((res) => {
			if (res.data.code === 0) {
				orderTime.value = dayjs(res.data.data.orderTime * 1000).format('YYYY-MM-DD HH:mm');
				contentDetail.value = res.data.data;
				start.value.lat = res.data.data.shop?.address?.location?.latLng?.latitude;
				start.value.lng = res.data.data.shop?.address?.location?.latLng?.longitude;
				end.value.lat = res.data.data.receiver?.address?.location?.latLng?.latitude;
				end.value.lng = res.data.data.receiver?.address?.location?.latLng?.longitude;
				console.log(start.value, end.value, 'start,end');
				setTimeout(() => {
					console.log(11111);
					routeMapVisible.value = true;
				}, 2000);
			}
		});
	}
	// 接收两个参数一个是serialNumber一个是orderId`
	// getIntraCityDeliveryDetailApi({
	// 	serialNumber: val.serialNumber,
	// 	orderId: val.orderId,
	// }).then((res) => {
	// 	if (res.code === 0) {
	// 		orderTime.value = dayjs(res.data.orderTime * 1000).format('YYYY-MM-DD HH:mm');
	// 		// 骑手信息
	// 		res.data.deliveryManInfo.latLngs
	// 		// 收货人信息
	// 		end.value.lat = res.data.receiverInfo.address.location.latLng.latitude
	// 		end.value.lng = res.data.receiverInfo.address.location.latLng.longitude
	// 		start.value.lat = res.data.shopInfo.address.location.latLng.latitude
	// 		start.value.lng = res.data.shopInfo.address.location.latLng.longitude
	// 		routeMapVisible.value = true;

	// 		//  setTimeout(() => {
	// 		//   routeMapVisible.value = true;
	// 		//  }, 2000);
	// 		console.log(end.value, start.value, 'qqqqqq');
	// 	} else {
	// 		MessagePlugin.error(res.message);
	// 	}
	// });
};
defineExpose({
	openWin,
});
</script>
<style scoped lang="less">
:global(.route-map-drawer .t-drawer__content-wrapper) {
	background-image: url('http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/bg_left.png');
	background-size: 472px 200px;
	background-repeat: no-repeat;
}

.foot {
	display: flex;
	justify-content: end;
}

.route-map-info-box {
	display: flex;
	flex-direction: column;
	gap: 12px;
}

:global(.route-map-drawer .t-drawer__body) {
	padding: 0 12px;
}

.header-box {
	width: 100%;
	display: flex;
	flex-direction: column;
	gap: 4px;
	margin: 16px 0;
}

.order-time {
	color: #516082;
	font-size: 14px;
	font-style: normal;
	font-weight: 400;
	line-height: 22px;
	/* 157.143% */
}

.route-map-box {
	width: 448px;
	height: 252px;
	border-radius: 8px;
	overflow: hidden;
	border: 1px solid #fff;
}

/* 内容样式 */
.content-mes-item {
	margin-bottom: 8px;
	display: flex;
	align-items: center;
}
.mes-item-left {
	width: 98px;
	margin-right: 16px;
	color: #828da5;
}
.mes-item-right {
	margin-right: 16px;
	color: #1a2139;
	max-width: 300px;
}
.mapDetail-content {
	border-radius: 8px;
	padding: 12px;
}
.mapDetail-content-title {
	font-size: 16px;
	font-weight: 600;
}
.mapDetail-content-mes {
	margin: 12px 0;
}
</style>
