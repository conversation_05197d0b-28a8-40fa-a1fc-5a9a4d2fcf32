<template>
  <div :class="['box', opacityBackGround ? 'opacityBackGround' : '']" @click="hide">
<!--    <t-dialog-->
<!--      class="changeAccountVisibleDialog"-->
<!--      v-model:visible="changeAccountVisible"-->
<!--      :header="false"-->
<!--      :footer="false"-->
<!--      :cancel-btn="null"-->
<!--      :close-btn="false"-->
<!--      :confirm-btn="null"-->
<!--      :closeOnEscKeydown="false"-->
<!--      :closeOnOverlayClick="false"-->
<!--    >-->
<!--      -->
<!--    </t-dialog>-->

    <div class="changeAccountVisible" v-if="dialogType === 'changeAccountVisible'" @click.stop="">
      <div v-for="(item, index) in accountList" :key="item.openid" class="card" @click.stop="index !== 0 && changeAccountLogin(item)">
        <div class="avatar-box">
          <avatar
            avatarSize="28px"
            :image-url="item.avatar ?? ''"
            :user-name="item.name"
            :round-radius="true"
          />
          <div class="name">{{ item.name }}</div>
          <div class="online" v-if="index === 0">{{ t('account.online') }}</div>
          <div class="offline" v-else-if="item.offline">{{ t('account.offline') }}</div>
        </div>
        <img v-if="index !== 0" class="delete" @click.stop="deleteAccount(item)" src="@renderer/assets/svg/icon_delete.svg" alt="">
      </div>
      <div class="border-line"></div>
      <t-button v-if="showAddOther" block theme="default" variant="text" class="addOther" @click.stop="addOtherAccount('changeAccountVisible')">
        <template #icon><plus-icon /></template>
        {{ t('account.addOtherAccount') }}
      </t-button>
    </div>

    <div class="changeAccountVisible" v-if="dialogType === 'changeLoginAccountVisible'" @click.stop="">
      <div class="loginAccountTitle">{{ t('account.loginAccount') }}</div>
      <div v-for="(item, index) in accountSwitchList" :key="item.openid" class="card" @click.stop="changeAccountLogin(item)">
        <div class="avatar-box">
          <avatar
            avatarSize="28px"
            :image-url="item.avatar ?? ''"
            :user-name="item.name"
            :round-radius="true"
          />
          <div class="name">{{ item.name }}</div>
        </div>
      </div>
      <div class="border-line"></div>
      <t-button v-if="showAddOther" block theme="default" variant="text" class="addOther" @click.stop="addOtherAccount('changeLoginAccountVisible')">
        <template #icon><plus-icon /></template>
        {{ t('account.loginOtherAccount') }}
      </t-button>
    </div>

    <div class="loginVisible" v-else-if="dialogType === 'loginVisible'" @click.stop="">
      <login v-if="loginDialogType === 'login'" :isMoreAccount="true" :loginPhoneInfo="loginPhoneInfo" @goRegister="loginDialogType = 'register'" @close="closeLogin"></login>
      <register v-else-if="loginDialogType === 'register'" :isMoreAccount="true" :loginPhoneInfo="loginPhoneInfo" @goLogin="loginDialogType = 'login'"></register>
    </div>

    <div class="logoutVisible changeAccountVisible" v-if="showLogout && dialogType === 'logoutVisible'" @click.stop="">
      <div class="card" @click.stop="logoutItem(currentAccount)">
        <div class="avatar-box">
          <avatar
            avatarSize="28px"
            :image-url="currentAccount.avatar ?? ''"
            :user-name="currentAccount.name"
            :round-radius="true"
          />
          <div class="name">{{ `${t('account.logoutAccount')}"${currentAccount.name}"${t('account.account')}` }}</div>
        </div>
      </div>
      <div class="card" @click.stop="logoutAll">
        <div class="avatar-box">
          <div class="name">{{ t('account.logoutAllAccount') }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch, nextTick, onUnmounted } from "vue";
import avatar from "@renderer/components/kyy-avatar/index.vue";
import { PlusIcon } from 'tdesign-icons-vue-next';
import {DialogPlugin, MessagePlugin, LoadingPlugin} from "tdesign-vue-next";
import login from '@renderer/views/account/login/normal.vue';
import register from '@renderer/views/account/register/normal.vue';

import { useI18n } from "vue-i18n";
import {
  changeMoreAccountInfo,
  emptyAndQuit, filterAccountSwitchList,
  getAccountSwitchList, getCurrentAccount,
  removeAppTeamId,
  setAccountAuthRouters,
  loginSucSetLocal,
  setAccountSwitchList, setCurrentAccount, setProfilesInfo, removeStore,
  setCards, setStaff, checkCommonTeams
} from "@/utils/auth";
import {getProfile, refreshAccountLogin} from "@/api/account";
import { refreshPlatformCards } from "@/views/message/service/accountUtils";
import {removeProjectTeamID} from "@/views/engineer/utils/auth";
import {removeMember} from "@/views/member/utils/auth";
import { staffOpenid } from '@renderer/api/contacts/api/recent';
import { removePolitics } from "@renderer/views/politics/utils/auth";
import LynkerSDK from '@renderer/_jssdk';
const { t } = useI18n();
const sourceType = ref('');
const dialogType = ref('');
const loginDialogType = ref('');
const showLogout = ref(true);
const changeAccountVisible = ref(false);
const loadingAttachInstance = ref(null);
const currentAccount = ref(getCurrentAccount() || {});
const allAccountSwitchList = ref(getAccountSwitchList() || []);
const accountSwitchList = computed(() => allAccountSwitchList.value?.filter(item => item.openid !== currentAccount.value.openid));
const accountList = computed(() => [ currentAccount.value, ...accountSwitchList.value ]);
const opacityBackGround = computed(() => !loadingAttachInstance.value && (!showLogout.value || dialogType.value === 'changeAccountVisible' || dialogType.value === 'logoutVisible'))

const showAddOther = computed(() => accountList.value?.length < 10);
let confirmDia = null;

const props = defineProps({
  showOtherData: {
    type: Object,
    default: () => {},
  },
});

const hide = () => {
  if (['changeAccountVisible', 'logoutVisible'].includes(dialogType.value) && !loadingAttachInstance.value) {
    LynkerSDK.ipcRenderer.invoke('set-popbv-other', {show: false});
  }
  MessagePlugin.closeAll();
};

const oncancel = () => {
  dialogType.value = '';
  // ipcRenderer.invoke('2', {show: false});
}

const closeAll = () => {
  LynkerSDK.ipcRenderer.invoke('set-popbv-other', {show: false});
  LynkerSDK.ipcRenderer.invoke('set-popbv', {show: false});
}

const logoutItem = (item) => {
  confirmDia = DialogPlugin.confirm({
    header: t('account.logout'),
    theme: 'info',
    body: t('account.logoutAllAccountTip'),
    closeBtn: null,
    closeOnOverlayClick:false,
    closeOnEscKeydown:false,
    cancelBtn: t('account.cancelLogout'),
    confirmBtn: t('account.confirmLogout'),
    zIndex: ********,
    onConfirm: async () => {
      confirmDia.hide();
      dialogType.value = 'changeLoginAccountVisible';
      let currentAccountTemp = getCurrentAccount() || {};
      let allAccountSwitchListTemp = getAccountSwitchList() || [];
      currentAccountTemp = {
        ...currentAccountTemp,
        isLogOut: true
      };
      allAccountSwitchListTemp?.forEach(ite => {
        if (ite.openid === item.openid){
          ite.isLogOut = true
        }
      });
      setCurrentAccount(currentAccountTemp, false);
      setAccountSwitchList(allAccountSwitchListTemp);
      currentAccount.value = getCurrentAccount() || {};
      allAccountSwitchList.value = getAccountSwitchList() || [];
    },
    onClose: () => {
      confirmDia.destroy();
      closeAll();
    },
  });
}

const logoutAll = () => {
  confirmDia = DialogPlugin.confirm({
    header: t('account.logout'),
    theme: 'info',
    body: t('account.logoutAllAccountTip'),
    closeBtn: null,
    closeOnOverlayClick:false,
    closeOnEscKeydown:false,
    cancelBtn: t('account.cancelLogout'),
    confirmBtn: t('account.confirmLogout'),
    className: showLogout.value ? '' : "changeAccountLogoutAllDialog",
    zIndex: ********,
    onConfirm: async () => {
      confirmDia.hide();
      oncancel();
      quitAccount();
    },
    onClose: () => {
      confirmDia.destroy();
      !showLogout.value && closeAll();
    },
  });
}

const closeLogin = () => {
  dialogType.value = sourceType.value ?? 'changeAccountVisible';
  loginDialogType.value = 'login'
}
const loginPhoneInfo = ref(null);
const addOtherAccount = (type) => {
  sourceType.value = type;
  loginPhoneInfo.value = null;
  dialogType.value = 'loginVisible';
  loginDialogType.value = 'login'
}

const deleteAccount = (item) => {
  confirmDia = DialogPlugin.confirm({
    header: t('account.deleteAccount'),
    theme: 'info',
    body: t('account.deleteAccountTip'),
    closeBtn: null,
    closeOnOverlayClick:false,
    closeOnEscKeydown:false,
    cancelBtn: t('account.cancelDelete'),
    confirmBtn: t('account.confirmDelete'),
    className: 'confirmDelete',
    zIndex: ********,
    onConfirm: async () => {
      confirmDia.hide();
      filterAccountSwitchList(item, true);
      allAccountSwitchList.value = (getAccountSwitchList() || []);
      MessagePlugin.success(t('account.deleteAccountSuccess'));
    },
    onClose: () => {
      confirmDia.hide();
    },
  });
};

const quitAccount = async () => {
  removeStore('secondInstanceParams');
  removeProjectTeamID();
  removeMember(); // 数字商协
  removePolitics(); // 数字城市
  removeAppTeamId();
  emptyAndQuit(true);
};

const changeAccountLogin = async (item) => {
  console.log('changeAccountLogin', item);
  loadingAttachInstance.value = LoadingPlugin({
    attach: '.box',
    showOverlay: true,
    size: '26px',
    text: `${t('account.changeAccountIng')}...`,
  });
  refreshAccountLogin({
    jwt: item.jwt
  }).then(async (res: any) => {
    try {
      if (res.status === 200) {
        // MessagePlugin.success(t('account.changeAccountSuccess'));
        removeStore('secondInstanceParams');
        removeProjectTeamID();
        removeMember();
        removeAppTeamId();

        loginSucSetLocal(res.data.data);
        LynkerSDK.ipcRenderer.invoke("im.bridge.invoke", { action: 'disconnect' });

        let allAccountSwitchListTemp = getAccountSwitchList() || [];
        allAccountSwitchListTemp?.forEach(ite => {
          if (ite.openid === item.openid){
            res.data.data.jwt && (ite.jwt = res.data.data.jwt);
            res.data.data.openid && (ite.openid = res.data.data.openid);
          }
        });
        setAccountSwitchList(allAccountSwitchListTemp);
        let profile = await getProfile();
        if (profile.status === 200) {
          setProfilesInfo(profile.data);
          // 更新localStorage身份卡信息
          const cardsData = await staffOpenid();
          if (cardsData.data?.code === 0) {
            const data = cardsData.data;
            setCards(data.data.cards);
            setStaff(data.data.staff);
          }
          refreshPlatformCards()
          // loadAcountCards(false);
          await setAccountAuthRouters('click-menu-item');
          await checkCommonTeams();
          LynkerSDK.ipcRenderer
            .invoke("click-menu-item", {
              url: "/main/message",
            })
            .then((res) => {
              if (res) {
                LynkerSDK.ipcRenderer.send("update-nume-index", 0);
              }
            });
          setTimeout(() => {
            LynkerSDK.ipcRenderer.invoke('login-suc-moreAccount', { refreshMainWindow: true, openid: res.data.data.openid }).then(res => {
              console.log(res)
              changeMoreAccountInfo(false, true);
              loadingAttachInstance.value?.hide();
              loadingAttachInstance.value = null;
              oncancel();
              // ipcRenderer.invoke('set-popbv-other', {show: false});
              // ipcRenderer.invoke('set-popbv', {show: false});
            });
          });
        } else {
          MessagePlugin.error(profile.data?.message);
          loadingAttachInstance.value?.hide();
          loadingAttachInstance.value = null;
        }
      } else {
        // REFRESH_TOKEN_EXPIRED
        MessagePlugin.error(res.data.data.message);
        loadingAttachInstance.value?.hide();
        loadingAttachInstance.value = null;
      }
    } catch (e) {
      loadingAttachInstance.value?.hide();
      loadingAttachInstance.value = null;
    }
  }).catch((error) => {
    loadingAttachInstance.value?.hide();
    loadingAttachInstance.value = null;
    console.error('changeAccountLogin', error, item)
    if (error?.data?.reason === "REFRESH_TOKEN_EXPIRED"){
      let list = allAccountSwitchList.value?.map(ite => {
        if (ite.openid === item.openid) {
          return {
            ...ite,
            offline: true
          }
        }
        return ite
      });
      setAccountSwitchList(list);
      allAccountSwitchList.value = (getAccountSwitchList() || []);
      confirmDia = DialogPlugin.confirm({
        header: t('account.changeAccountError'),
        theme: 'info',
        body: t('account.changeAccountTip'),
        closeBtn: null,
        closeOnOverlayClick:false,
        closeOnEscKeydown:false,
        cancelBtn: t('account.cancelLogin'),
        confirmBtn: t('account.confirmLogin'),
        zIndex: ********,
        onConfirm: async () => {
          confirmDia.hide();
          addOtherAccount(dialogType.value);
          loginPhoneInfo.value = {
            ...item,
            mobile: item?.account_login_info?.account ?? item?.account_mobile ?? '',
            region: item?.account_mobile_region ?? '',
            email: item?.account_login_info?.account ?? item?.account_email ?? ''
          }
        },
        onClose: () => {
          confirmDia.hide();
        },
      });
    }
  })
}

const filterOffLineAccount = () => {
  let list = getAccountSwitchList() || [];
  // list = list?.filter(item => !item.offline);
  list = list?.filter(item => !item?.isLogOut);
  setAccountSwitchList(list);
  allAccountSwitchList.value = (getAccountSwitchList() || []);
}

onMounted(() => {
  filterOffLineAccount();
})
onUnmounted(() => {
  confirmDia?.hide();
  confirmDia = null;
})

watch(() => props.showOtherData, (val) => {
  if (val) {
    dialogType.value = val?.dialogType ?? 'changeAccountVisible';
    if (dialogType.value === 'logoutVisible') {
      let accountList = getAccountSwitchList();
      accountList && (accountList.length === 1) && (showLogout.value = false,logoutAll());
    }
  }
}, { immediate: true })

</script>

<style lang="less" scoped>
::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}
/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  border-radius: 3px;
  background: rgba(0, 0, 0, 0.1);
  -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.5);
}
.box{
  position: fixed;
  top: 0;
  left: 0;
  z-index: 110000;
  width: 100vw;
  height: 100vh;
  background: rgba(114, 114, 114);
  border-radius: 16px;
  display: flex;
  justify-content: center;
  align-items: center;

  :deep(.t-loading){
    flex-direction: column;
    gap: 16px;
    .t-loading__text{
      color: var(--text-kyy-color-text-1, #1A2139);
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
    }
  }
}

.opacityBackGround{
  background: rgba(114, 114, 114, 0.5);
}
:global(.changeAccountLogoutAllDialog .t-dialog__mask) {
  background: rgba(114, 114, 114, 0.8);
}
:global(.confirmDelete .t-dialog__footer .t-dialog__confirm){
  border: 1px solid var(--color-button-secondary-error-kyy-color-button-secondary-error-border-dedault, #D54941) !important;
  background: var(--color-button-secondary-error-kyy-color-button-secondray-error-bg-default, #FDF5F6) !important;
}
:global(.confirmDelete .t-dialog__footer .t-dialog__confirm .t-button__text){
  color: var(--color-button-secondary-error-kyy-color-button-secondray-error-text-default, #D54941) !important;
}

.changeAccountVisible{
  min-width: 240px;
  width:auto;
  height: auto;
  max-height: 400px;
  overflow-y: auto;
  padding: 16px;
  border-radius: var(--kyy_radius_dropdown_m, 8px);
  background: var(--kyy_color_dropdown_bg_default, #FFF);

  /* kyy_shadow_m */
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.12);
  .card:hover{
    border-radius: var(--kyy_radius_dropdown_s, 4px);
    background: var(--bg-kyy-color-bg-list-hover, #F3F6FA);
  }
  .card{
    cursor: pointer;
    padding: 4px 8px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
    .avatar-box{
      display: flex;
      align-items: center;
    }
    .name {
      margin-left: 12px;
      margin-right: 4px;
      color: var(--text-kyy-color-text-1, #1A2139);
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
    }
    .online{
      padding: 0 4px;
      border-radius: var(--kyy_radius_tag_s, 4px);
      background: var(--kyy_color_tag_bg_purple, #FAEDFD);
      color: var(--kyy_color_tag_text_purple, #CA48EB);
      text-align: center;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
    }
    .offline{
      padding: 0 4px;
      border-radius: var(--kyy_radius_tag_s, 4px);
      background: var(--kyy_color_tag_bg_gray, #ECEFF5);
      color: var(--kyy_color_tag_text_gray, #516082);
      text-align: center;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
    }
    .delete{
      cursor: pointer;
      width: 20px;
      height: 20px;
    }
  }
  .loginAccountTitle{
    padding-bottom: 10px;
    color: var(--text-kyy-color-text-1, #1A2139);
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px; /* 150% */
  }
  .border-line{
    width: 100%;
    min-height: 1px;
    max-height: 1px;
    background: #ECEFF5;
    margin-bottom: 8px;
  }
  .addOther{
    padding-left: 8px;
    display: flex;
    height: 32px;
    justify-content: flex-start;
    align-items: center;
    gap: 4px;
    color: var(--color-button-text-brand-kyy-color-button-text-brand-font-default, #4D5EFF) !important;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
    :deep(.t-button__text){
      font-weight: 400;
    }
  }
}

.loginVisible{
  height: 448px;
  width: 328px;
  border-radius: 12px;
  background: var(--bg-kyy-color-bg-light, #FFF);

  /* kyy_shadow_l */
  position: relative;

  .icon-close{
    -webkit-app-region: no-drag;
    cursor: pointer;
    position: absolute;
    top: 20px;
    right: 24px;
    width: 24px;
    height: 24px;
  }
}
</style>
