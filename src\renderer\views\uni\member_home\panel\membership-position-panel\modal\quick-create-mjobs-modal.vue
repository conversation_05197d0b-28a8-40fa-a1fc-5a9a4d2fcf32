<template>
  <t-dialog
    v-model:visible="visible"
    :z-index="2501"
    attach="body"
    :close-on-overlay-click="false"
    class="dialog dialogHeight560 "
    width="600px"
  >
    <template #header>
      <div class="header">
        <span class="title">{{ $t('member.impm.quick_1') }} </span>
      </div>

    </template>
    <template #body>
      <div class="toBody mt-24px">
        <t-form
          ref="formRef"
          :data="formData"
          scroll-to-first-error="smooth"
          :rules="[]"
          layout="inline"
          label-width="0"
        >
          <t-table
            row-key="uuid"
            :columns="positionColumns"
            :data="posData"
            :height="'364px'"
          >
            <template #name="{ row }">
              <div class="toItem">
                <t-form-item
                  :name="row.uuid + 'name'"
                  :rules=" [{
                    message: '',
                    validator: () => validator(row, $t('member.impm.quick_2'), 'name'),
                    required: true,
                    trigger: 'blur'
                  }]"
                >
                  <t-input v-model="row.name" style="width: 120px" />
                </t-form-item>
              </div>
            </template>

            <template #level_name="{ row }">
              <div class="toItem">
                <t-form-item
                  :name="row.uuid + 'level_name'"
                  :rules=" [{
                    message: '',
                    validator: () => validator(row, $t('member.impm.quick_3'), 'level_name'),
                    required: true,
                    trigger: 'blur'
                  }]"
                >
                  <t-input v-model="row.level_name" style="width: 120px" />
                </t-form-item>
              </div>
            </template>

            <template #money="{ row }">
              <div class="toItem">
                <t-input-number v-model="row.money" theme="normal" :decimal-places="2" />
              </div>
            </template>

            <template #operate="{ row, rowIndex }">
              <div class="toItem">
                <t-link
                  v-show="rowIndex"
                  theme="danger"
                  hover="color"
                  @click="delPos(row)"
                >{{ $t('member.impm.quick_4') }}</t-link>
              </div>
            </template>

            <template #empty>
              <div class="empty">
                <noData />
              </div>
            </template>
          </t-table>
        </t-form>
      </div>
    </template>

    <template #closeBtn>

      <iconpark-icon name="iconerror" style="font-size: 24px"></iconpark-icon>
    </template>
    <template #footer>
      <div class="opt mt-16px">
        <t-button
          theme="default"
          style="height: 28px;"
          class="opt-item"
          :disabled="posData.length === 10 || posData.length > 10"
          @click="addPos"
        >
          <iconpark-icon name="iconadd" class="iconadd"></iconpark-icon>
          {{ $t('member.impm.quick_5') }}

        </t-button>
      </div>
      <div class="operates">
        <div class="left">
          {{ $t('member.impm.quick_6') }}
        </div>

        <div class="right">
          <t-button
            theme="default"
            style="width: 80px;"
            class="operates-item"
            @click="onClose"
          >{{ $t('member.impm.quick_7') }}</t-button>
          <t-button
            theme="primary"
            class="operates-item"
            @click="submitRun"
          >{{ $t('member.impm.quick_8') }}</t-button>
        </div>
      </div>
    </template>
  </t-dialog>


</template>

<script lang="ts" setup>

/**
 * @description 选择组织
 * <AUTHOR>
 */
import { debounce } from "lodash";
import { Ref, reactive, ref, toRaw } from "vue";
import { MessagePlugin } from "tdesign-vue-next";
import noData from "@renderer/components/common/Empty.vue";
import { v4 as uuidv4 } from "uuid";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const formRef = ref(null);
const formData = ref({});
const positionColumns = ref([

  { colKey: "name", title: t('member.impm.quick_9'), width: "28%" },
  { colKey: "level_name", title: t('member.impm.quick_10'), width: "28%" },
  { colKey: "money", title: t('member.impm.quick_11'), width: "30%", align: "right" },
  { colKey: "operate", title: t('member.impm.quick_12'), width: "14%" }
]);
const visible = ref(false);
const posData = ref([]);
const emits = defineEmits(['onQuickCreate']);
// const formData = ref({});
// 姓名的校验、会员级别的校验
const validator = (val: any, tip: string, key: string) => {
  console.log("ddd");
  if (!val[key]) {
    return {
      message: tip,
      required: true,
      trigger: "blur"
    };
  }
  console.log(posData.value);
  if (posData.value.some((v) => v[key] === val[key] && val.uuid !== v.uuid)) {
    return {
      message: key==='name'? t('member.impm.quick_13'): t('member.impm.quick_14'),
      required: true,
      trigger: "blur"
    };
  }


  return { result: true, message: "", type: "success" };
};


const addPos = (row) => {
  posData.value.push({
    uuid: uuidv4(),
    name: '',
    level_name: '',
    money: 0,
    currency: posData.value.length > 0 ? posData.value[0].currency : 'CNY'
  });
  if (posData.value.length > 9) {
    MessagePlugin.error(t('member.impm.quick_15'));
  }
};
const delPos = (row) => {
  posData.value = posData.value.filter((v) => v.uuid !== row.uuid);
};

const submitRun = () => {
  formRef.value.validate({ showErrorMessage: true }).then((validateResult) => {
    console.log(validateResult);
    if (validateResult && Object.keys(validateResult).length) {
      const firstError = Object.values(validateResult)[0]?.[0]?.message;
      MessagePlugin.warning(firstError);
    } else {
      // emits("release", approvalData.value);
      // console.log('ok');
      // posData.value

      // const result = posData.value.some((v) => !(v.name && v.level_name));
      // if (result) {
      //   console.log('有问题');
      // } else {
      //   console.log('没问题');
      //   emits('onQuickCreate', toRaw(posData.value));
      // }

      if (posData.value.length > 10) {
        MessagePlugin.error(t('member.impm.quick_15'));
        return;
      }
      emits('onQuickCreate', toRaw(posData.value));


    }
  });
};



const onOpen = (arr) => {
  console.log(arr);
  if (arr && arr.length > 0) {
    arr.map((v) => {
      v.uuid = uuidv4();
      // formData.value[v.uuid] = v;
      return v;
    });
    posData.value = arr;

  } else {
    posData.value = [];

  }

  visible.value = true;
};
const onClose = () => {
  visible.value = false;
};

defineExpose({
  onOpen,
  onClose
});
</script>

<style lang="less" scoped>
@import "@renderer/views/engineer/less/common.less";


.header {

}
.toBody {

}

:deep(.toItem) {
  // height: 100%;
  display: flex;
  flex-direction: column;
}


:deep(.t-form__item) {
  margin-bottom: 10px;
}

.t-dialog__ctx .t-dialog__position {
  padding: 0;
}

.opt {
  display: flex;
  &-item {
    color: var(--color-button-border-kyy-color-button-border-text-default, #516082) !important;

    font-size: 14px !important;
    font-weight: 400 !important;
    line-height: 22px; /* 157.143% */
    .iconadd {
      // width: 20px;
      // height: 20px;
      font-size: 20px;
      // color: var(--color-button-border-kyy-color-button-border-text-default, #516082) !important;

    }



  }
}

.operates {
  display: flex;
  justify-content: space-between;
  // margin-top: 24px;
  padding: 24px 0;
  align-items: center;
  .left {
    color: var(--text-kyy-color-text-3, #828DA5);
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
}

:deep(.t-form__item){
  margin-bottom: 0px !important;
}
:deep(.t-form__item) {
  min-width: 0 !important;
  margin-right: 0 !important;
}
</style>
