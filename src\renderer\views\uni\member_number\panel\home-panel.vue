<template>
  <div class="home">
    <!-- <img src="/assets/member/home.png" alt="" /> -->
    即可设计地方
  </div>
</template>

<script lang="ts" setup></script>

<style lang="less" scoped>
.home {
  padding: 24px 0;
  display: flex;
  justify-content: center;
  width: 100%;
  height: 100%;
  overflow-y: auto;
  img {
    width: 750px;
    height: 1210px;
  }
  .bott {
    height: 24px;
    width: 100%;
  }
}
.home::-webkit-scrollbar {
  width: 4px;
}
.home::-webkit-scrollbar-thumb {
  background-color: #ccc;
}
</style>
