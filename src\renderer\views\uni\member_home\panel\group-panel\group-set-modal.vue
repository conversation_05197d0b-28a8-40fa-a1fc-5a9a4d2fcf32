<template>
  <div class="detail-review">
    <t-dialog
      v-model:visible="visible"
      :header="t('ebook.fzck')"
      :confirmLoading="confirmLoading"
      :on-close="onCancel"
      :destroy-on-close="true"
      width="440px"
      :confirm-btn="t('ebook.bc')"
      @confirm="onConfirm"
    >
      <div class="detail-review-hex">
        <div class="radio-gr">
          <div class="label">
            {{ t("ebook.fzfw")
            }}<t-tooltip :showArrow="false">
              <iconpark-icon
                name="iconhelp"
                class="icon"
                style="font-size: 20px; color: #828da5; position: relative; top: 5px; left: 4px"
              />
              <template #content>
                <div style="width: 240px">
                  <p>{{ t("ebook.fw1") }}</p>
                  <br />
                  <p>{{ t("ebook.fw12") }}</p>
                </div>
              </template>
            </t-tooltip>
          </div>
          <div class="radio">
            <t-radio-group v-model="type" @change="onTypeChange">
              <t-radio :value="1">{{ t("ebook.all") }}</t-radio>
              <t-radio :value="2">{{ t("ebook.only") }}</t-radio>
            </t-radio-group>
          </div>
        </div>
        <div class="list-box" v-if="type === 2">
          <div class="label">
            {{ t("ebook.liw") }}
            <t-tooltip :showArrow="false">
              <template #content>
                <div style="width: 240px">
                  <p>{{ t("ebook.dgx") }}</p>
                </div>
              </template>
              <iconpark-icon
                name="iconhelp"
                class="icon"
                style="font-size: 20px; color: #828da5; position: relative; top: 5px; left: 4px"
              />
            </t-tooltip>
          </div>
          <div class="user-box">
            <div class="user">
              <div class="addbtn" @click="addRun">
                <iconpark-icon name="iconadd" class="iconadd"></iconpark-icon>
                {{ t("ebook.tianj") }}
              </div>
              <t-tag
                v-for="(tag, index) in userList"
                :key="index"
                class="tagText"
                theme="default"
                :closable="true"
                :max-width="124"
                @close="handleCloseUser(index)"
              >
                <template #icon>
                  <kyy-avatar
                    round-radius
                    avatar-size="20px"
                    :image-url="tag.avatar"
                    :user-name="tag.name"
                    style="margin-right: 8px"
                  />
                </template>
                {{ tag.name }}
              </t-tag>
            </div>
          </div>
        </div>
      </div>
    </t-dialog>
  </div>
  <selectMember
    v-model:visible="selectMemberVisible"
    :select-list="selectList"
    :disabled-card="true"
    :menu="['platform', 'organize']"
    :active-card-id="[activeAccount.cardId]"
    :team-id="activeAccount.teamId"
    :change-menus="false"
    :emptyCardId="true"
    @confirm="selectMemberConfirm"
  />
</template>

<script setup lang="ts">
import { computed, ref } from "vue";
import { useI18n } from "vue-i18n";
import { MessagePlugin } from "tdesign-vue-next";
import { editChannel, getDateil } from "@renderer/api/notice/manage";
import kyyAvatar from "@renderer/components/kyy-avatar/index.vue";
import selectMember from "@renderer/components/selectMember/audio-add-members.vue";
import { useDigitalPlatformStore } from "@renderer/views/digital-platform/store/digital-platform-store";
import { platform } from "@renderer/views/digital-platform/utils/constant";
import { useRoute } from "vue-router";
import { governmentgetsetting, governmentsetting } from "@renderer/api/uni/api/businessApi";
const { t } = useI18n();
const visible = ref(false);
const channelsRef = ref(null);

const digitalPlatformStore = useDigitalPlatformStore();
const nicheDetail = ref(null);
const userList = ref([]);
const cardId = ref([]);
const selectList = ref([]);
const type = ref(1);
const selectMemberVisible = ref(false);
const addRun = () => {
  selectList.value = userList.value.map((item) => item.cardId || item.openid);
  console.log(activeAccount.value);
  selectMemberVisible.value = true;
};
const route = useRoute();

const props = defineProps({
  etype: {
    type: Number,
    default: 1,
  },
  teamId: {
    type: String,
    default: "",
  },
});
const platformCpt: any = computed(() => props.platform || route.query?.platform);

const activeAccount = computed(() => {
  if (platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount;
  }
  if (platformCpt.value === platform.digitalWorkbench) {
    return route.query;
  }
  return null;
});

const selectMemberConfirm = (e) => {
  console.log(e);
  userList.value = e;
};
const setOpen = () => {
  governmentgetsetting(activeAccount.value.teamId).then((res: any) => {
    if (res.data?.code === 0) {
      console.log("res", res);
      const { detail } = res.data.data;
      if (detail) {
        console.log("detail", detail);
        type.value = detail.is_range;
        // detail.contacts_info.map((item) => {
        //   item.cardId = item.card_id;
        //   item.openId = item.openid;
        // });
        userList.value = detail.contacts_info;
        console.log(userList.value, "selectList.value");
      }
      visible.value = true;
    }
  });
};
const onTypeChange = () => {};
const handleCloseUser = (index) => {
  console.log(index);

  userList.value.splice(index, 1);
  console.log(userList.value);
};

const onConfirm = () => {
  confirmLoading.value = true;
  const params = {
    is_range: type.value,
    card_ids: userList.value.map((item) => item.cardId || item.openid),
  };
  settingReq(params);
};
const onCancel = () => {
  visible.value = false;
};
const confirmLoading = ref(false);
const settingReq = (data) => {
  console.log(props.teamId, "props.teamId");

  governmentsetting(data, activeAccount.value.teamId).then((res: any) => {
    console.log(res);
  confirmLoading.value = false;
  if (res.data?.code === 0) {
      MessagePlugin.success(t("ebook.edsucc2"));
      visible.value = false;
      emits("delist-succ");
    }
  }).catch((err) => {
    MessagePlugin.error(err.message || "设置失败");

  confirmLoading.value = false;

  });
};

const emits = defineEmits(["delist-succ"]);

defineExpose({
  setOpen,
});
</script>

<style lang="less" scoped>
.in {
  margin-top: 12px;
  :deep(.t-textarea__inner) {
    border-color: #d5dbe4 !important;
  }
  :deep(.t-textarea__info_wrapper) {
    display: none !important;
  }
}
.detail-review-hex::-webkit-scrollbar {
  width: 4px;
}
.detail-review-hex {
  width: 100%;
  overflow-y: auto;
  padding-top: 24px;
  margin-bottom: 16px;
}
.detail-review {
  :deep(.t-dialog__body__icon) {
    margin-right: 0 !important;
  }
}

.radio-gr {
  display: flex;
  flex-direction: column;
  gap: 8px;
  .label {
    color: var(--text-kyy_color_text_3, #828da5);
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
}
.list-box {
  margin-top: 24px;
  .label {
    color: var(--text-kyy_color_text_3, #828da5);
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
    .req {
      color: var(--error-kyy_color_error_default, #d54941);
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
      margin-right: 4px;
    }
  }
  .user-box {
    width: 100%;
    border: 1px solid #d5dbe4;
    border-radius: var(--input-kyy_radius_input, 4px);
    margin-top: 8px;
    padding-top: 4px;
    padding-bottom: 4px;
    padding-right: 6px;
  }
  .user {
    display: flex;
    padding: 0px 12px;
    align-items: flex-start;
    align-self: stretch;
    max-height: 96px;
    overflow-y: auto;
    width: 100%;
    flex-wrap: wrap;
    gap: 8px;
    padding-right: 6px;
    .addbtn {
      display: flex;
      height: 24px;
      min-height: 24px;
      max-height: 24px;
      padding: 0px 12px 0px 8px;
      justify-content: center;
      align-items: center;
      gap: 4px;
      border-radius: var(--radius-kyy_radius_button_s, 4px);
      border: 1px solid var(--color-button_secondaryBrand-kyy_color_button_secondaryBrand_border_dedault, #4d5eff);
      background: var(--color-button_secondaryBrand-kyy_color_button_secondrayBorder_bg_default, #eaecff);
      color: var(--color-button_secondaryBrand-kyy_color_button_secondrayBorder_text_default, #4d5eff);
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      cursor: pointer;
      .iconadd {
        font-size: 20px;
      }
    }

    .tagText {
      display: flex;
      height: 24px;
      min-height: 24px;
      max-height: 24px;
      padding: 2px 8px;
      align-items: center;
      border-radius: 4px;
      background: var(--kyy_color_tag_bg_gray, #eceff5);
    }
  }
  .user::-webkit-scrollbar {
    width: var(--kyy_radius_toopltip, 6px);
    border-radius: 7px;
  }
  .user::-webkit-scrollbar-thumb {
    border-radius: 7px;
    background: var(--divider-kyy_color_divider_deep, #d5dbe4);
  }
  .user::-webkit-scrollbar-track {
    background-color: #fff;
  }
}
</style>
