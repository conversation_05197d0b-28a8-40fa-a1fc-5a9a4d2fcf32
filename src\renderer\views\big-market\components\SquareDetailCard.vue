<template>
  <div class="square-detail-card">
    <!-- 图片展示区域 -->
    <div v-if="data.imgs?.length" class="detail-images">
      <img
        v-for="(img, idx) in data.imgs"
        :key="img"
        :src="img"
        :alt="`图片${idx + 1}`"
        class="detail-image"
        @click="emit('image-click', idx, data.imgs)"
      />
    </div>

    <!-- 信息展示区域 -->
    <div class="detail-info">
      <div class="info-section" v-if="data.serviceTypeIntro">
        <div class="info-label">商品/服务</div>
        <div class="info-content">
          {{ data.serviceTypeIntro || '--' }}
        </div>
      </div>

      <div class="info-section">
        <div class="info-label">简介</div>
        <div class="info-content">
          {{ data.intro || '--' }}
        </div>
      </div>

      <div class="info-section" v-if="data.region">
        <div class="info-label">地区</div>
        <div class="info-content">
          {{ data.region || '--' }}
        </div>
      </div>

      <div class="info-section">
        <div class="info-label">认证组织</div>
        <div class="info-content">
          {{ data.squareOrganize?.name || '--' }}
        </div>
      </div>

      <div class="info-section" v-if="data?.phoneNumbers?.length">
        <div class="info-label">客服电话</div>
        <div class="info-content">
          <p
            v-for="(phone, index) in data.phoneNumbers"
            :key="index"
            class="phone-item"
          >
            {{ phone.countryCode }} {{ phone.number }}
          </p>
          <span v-if="!data.phoneNumbers?.length">--</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { RingkolMarketMarketSquare } from '../types';

interface Props {
  /** 广场号数据 */
  data: RingkolMarketMarketSquare;
}

interface Emits {
  (e: 'image-click', index: number, images: string[]): void;
}

const { data } = withDefaults(defineProps<Props>(), {});
const emit = defineEmits<Emits>();
</script>

<style lang="less" scoped>
// .card-boxxxx{
//     display: flex;
//   width: 296px;
//   min-height: 50px;
//   max-height: 492px;
//   overflow-y: auto;
//   flex-direction: column;
//   align-items: flex-start;
//   gap: 16px;
// }
.square-detail-card {
  display: flex;
  width: 296px;
  min-height: 50px;
  max-height: 492px;
  overflow-y: auto;
  flex-direction: column;
  align-items: flex-start;
  gap: 16px;
  padding-right: 3px;
  .detail-images {
    display: flex;
    align-items: flex-start;
    gap: 4px;
    align-self: stretch;

    .detail-image {
      width: 90px;
      height: 78px;
      border-radius: 8px;
      object-fit: cover;
      cursor: pointer;
      transition: transform 0.2s ease;

      // &:hover {
      //   transform: scale(1.05);
      // }
    }
  }

  .detail-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    align-self: stretch;
  }

  .info-section {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
    align-self: stretch;

    .info-label {
      color: var(--text-kyy_color_text_3, #828DA5);
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
    }

    .info-content {
      color: var(--text-kyy_color_text_1, #1A2139);
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;

      .phone-item {
        margin: 0;
        padding: 0;
      }
    }
  }
}
</style>
