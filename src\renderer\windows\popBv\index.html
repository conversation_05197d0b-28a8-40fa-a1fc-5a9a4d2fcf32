<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>弹出层</title>
  <!-- <script>
      window._smReadyFuncs = [];
      window.SMSdk = {
        onBoxDataReady(boxData) { // 非必填
          console.log('此时拿到的数据为boxData或者boxId', boxData);
        },
        ready(fn) {
          fn && _smReadyFuncs.push(fn);
        }
      };
      window.SMSdkError = false;

      // 1. 通用配置项
      window._smConf = {
        organization: 'OioUFNPPerhbBNzpgJoi', // 必填，组织标识，邮件中organization项
        appId: 'default', // 必填，应用标识，默认传值default，其他应用标识提前联系数美协助定义
        publicKey: 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCbkXhIXYNLFtBl91+pKZIaiElM0dxi3LZWsal13AtvwLGD2HI3maECRIHQvk8ehaRgbnq3KRDTGKbU2Nuw8jsQDTsb/bHu16XEYJX0MVxQo0R+rLRlWAwKT6laC1K4XbznFlYGTHghBuU+SK2MOrvMQb7HoYsRfTIgsHej8C6grwIDAQAB', // 必填，私钥标识，邮件中publicKey项
        staticHost: 'static.portal101.cn', // 必填, 设置JS-SDK文件域名，建议填写static.portal101.cn
        protocol: 'https', // 如果使用https，则设置，如不使用，则不设置这个字段
      };
    </script>
    <script src="../../outcdn/shumei.js"></script> -->
  <script src="https://res.hc-cdn.com/web-sdk-cdn/1.0.24/websdk.min.js"></script>
  <script src="https://castatic.fengkongcloud.cn/pr/v1.0.4/smcp.min.js"></script>
  <style>
    img {
      -webkit-user-drag: none;
    }
  </style>
</head>

<body>
  <div id="app" style="background: none;"></div>
  <script src="https://gosspublic.alicdn.com/aliyun-oss-sdk-6.18.1.min.js"></script>
  <script src="https://lf1-cdn-tos.bytegoofy.com/obj/iconpark/icons_27918_593.e792a97e045174a47acd82a16d1c1cff.js"></script>


  <script type="module" src="./main.ts"></script>
</body>

</html>
