export interface OwnerRequest {
  /**
   * 应用标识
   */
  appUuid?: string;
  /**
   * 身份卡 ID
   */
  cardId?: string;
  /**
   * 所有者类型
   */
  ownerType?: OwnerType;
  /**
   * 组织 ID
   */
  teamId?: string;
}

/**
 * 数据
 */
export interface OwnerData {
  /**
   * 所有者 ID
   */
  ownerId?: string;
}

/**
 * 所有者类型
 */
export enum OwnerType {
  DigitalPlatform = "DIGITAL_PLATFORM",
}

/** 平台类型 */
export type PlatformType = 'member' | 'government' | 'cbd' | 'association'| 'uni';
