<template>
  <div class="niche-admin">
    <leftMenu />
    <div class="right-box">
      <div class="right-content">
        <div class="right-header">
          <div @keyup.enter="getDataRunDr">
            <t-input
              v-model="params.title"
              style="width: 304px"
              :placeholder="t('niche.sbar')"
              clearable
              @blur="getDataRunDr"
            >
              <template #prefix-icon>
                <iconpark-icon name="iconsearch" class="name-icon" />
              </template>
            </t-input>
          </div>
          <t-button
            style="width: 32px; margin-left: 8px; height: 32px"
            type="button"
            theme="default"
            variant="outline"
            @click="showFilter"
          >
            <iconpark-icon name="iconscreen" class="icon iconscreen" style="font-size: 20px" />
          </t-button>
        </div>
        <div v-if="searchShow" class="filter-res filter-box">
          <div class="tit">{{ t("approval.approval_data.sures") }}</div>
          <!--<div v-if="params.title" class="kword te">
            <span>：{{ params.title }}</span>
            <span class="close2" @click="clearFilterKey('title')">
              <iconpark-icon name="iconerror" style="font-size: 20px" />
            </span>
          </div>-->
          <div class="stat te">
            <span>{{ t("niche.res_type") }}{{ params.type === 1 ? t("niche.gy") : t("niche.xq") }}</span>
            <span class="close2" @click="clearFilterStatus">
                <iconpark-icon name="iconerror" style="font-size: 20px" />
              </span>
          </div>
        </div>
        <div class="data-box-con">
          <t-table :row-key="'id'" :data="listData" cell-empty-content="--" :columns="columns">
            <template #team_name="{ row }">
              <div class="g-box">
                <div class="main-img">
                  <img
                    v-if="row.images && row.images[0]"
                    :src="row.images[0].file_name + '?x-oss-process=image/resize,m_fill,w_200,quality,q_60'"
                    alt=""
                    @click="preview(row.images)"
                  />
                  <img v-else src="/assets/business/Rectangle.png" alt="" />
                </div>
                <div class="good-info">
                  <div class="good-title">
                    <div v-if="row.type === 1" class="tag1">
                      {{ t("niche.gy") }}
                    </div>
                    <div v-else class="tag2">{{ t("niche.xq") }}</div>
                    <div class="tc" :title="row.title">
                      <t-tooltip :content="row.title">
                        <span>{{ row.title }}</span>
                      </t-tooltip>
                    </div>
                  </div>
                  <div class="good-time"><iconpark-icon name="icondate" class="name-icon" /> {{ row.created_at }}</div>
                </div>
              </div>
            </template>
            <template #approval_name="{ row }">
              <p v-if="row.effective_unlimited">长期</p>
              <p v-else>{{ row.effective_begin }} ～ {{ row.effective_end }}</p>
            </template>
            <template #actions="{ row }">
              <div class="actions">
                <div class="mbtna">
                  <a href="javascript:;" @click="draftEdit(row.id)">编辑</a>
                </div>
                <div class="mbtna">
                  <a href="javascript:;" @click="deleteWays(row.id)">{{ t("niche.del") }}</a>
                </div>
              </div>
            </template>
          </t-table>
          <noData v-if="!listData.length" style="margin-top: 88px" :text="t('approval.no_data')" />
          <div v-if="total && total > 10" class="pagination">
            <t-pagination
              :total="total"
              :total-content="false"
              show-previous-and-next-btn
              :show-page-size="false"
              :current="params.page"
              @change="pageChange"
            />
          </div>
        </div>
      </div>
    </div>
    <t-drawer
      v-model:visible="filterVisible"
      :close-btn="true"
      size="472px"
      :header="t('approval.approval_data.sur')"
      class="filterDrawer"
    >
      <div class="form-boxxx">
        <div class="fitem">
          <div class="title">{{ t("niche.draft_type") }}</div>
          <div class="ctl">
            <!--<t-input v-model="params.type" :maxlength="20" :placeholder="t('niche.res_peo_i')" />-->
            <t-select
              v-model="params.type"

              :options="optionsType"
              clearable
              :placeholder="t('approval.operation.select')"
            > <template #suffixIcon>
              <img src="@/assets/svg/icon_arrow_down.svg" />
            </template>
          </t-select>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="foot">
          <t-button type="button" style="width: 80px" theme="default" @click="initF">
            {{ t("niche.rest") }}
          </t-button>

          <t-button style="width: 80px" type="button" @click="getDataRunDrTwo">
            {{ t("niche.ss") }}
          </t-button>
        </div>
      </template>
    </t-drawer>
  </div>
</template>

<script setup lang="ts" name="nicheDraftList">
import { computed, onActivated, onMounted, reactive, ref } from "vue";
import leftMenu from "@renderer/views/niche/components/leftMenu.vue";
import { DialogPlugin, MessagePlugin } from "tdesign-vue-next";
import noData from "@renderer/components/approvalSatellite/approvalData/noData.vue";
import { useI18n } from "vue-i18n";
import { ClientSide } from "@renderer/types/enumer";
import { useRoute, useRouter } from "vue-router";
import { getDraftList, getDraftDelete, getDraftDetail } from "./apis";
import LynkerSDK from '@renderer/_jssdk';

const { ipcRenderer } = LynkerSDK;
const { t } = useI18n();

const emits = defineEmits(["setActiveIndexAndName", "uptWorkBenchTabItem"]);
const optionsType = computed(() => [
  { label: t("niche.gy"), value: 1 },
  { label: t("niche.xq"), value: 2 },
]);

onActivated(() => {
  settabItem();
  getData();
});
const props = defineProps({
  groupList: {
    type: Array,
    default: () => [],
  },
  tabList: {
    type: Array,
    default: () => [],
  },
  tabIndex: {
    type: Number,
    default: 0,
  },
  activationGroupItem: {
    type: Object,
    default: null,
  },
  cloudDiskType: {
    type: Object,
    default: null,
  },
});
const route = useRoute();
const total = ref(0);
const listData = ref([]);
const params = ref({
  title: undefined,
  type: undefined,
  page: 1,
  pageSize: 10,
});
const columns = [
  {
    colKey: "team_name",
    title: t("niche.ni_info"),
    width: "344",
  },
  {
    colKey: "staff_name",
    title: t("niche.cjz"),
    width: "160",
    ellipsis: true,
  },
  {
    colKey: "approval_name",
    title: t("niche.yxsj"),
    width: "296",
  },
  {
    colKey: "actions",
    title: t("niche.opt"),
    width: "144",
  },
];

const settabItem = () => {
  // ipcRenderer.invoke("set-work-bench-tab-item", {
  //   path: `/workBenchIndex/nicheDraftList`,
  //   path_uuid: "niche",
  //   name: 'nicheDraftList',
  //   title: '草稿箱',
  //   type: ClientSide.NICHE,
  // });
  emits("uptWorkBenchTabItem", {
    path: `/workBenchIndex/nicheDraftList`,
    path_uuid: "niche",
    name: "nicheHome,nicheExtend,nicheDraftList,nicheAdmin",
    title: "草稿箱",
    type: ClientSide.NICHE,
    updateKey: "nicheHome",
  });
};

onMounted(() => {
  settabItem();
  getData();
});

const getData = () => {
  // 获取列表数据
  console.log(params.value, "params.value");
  getDraftList(params.value).then((res) => {
    if (res.data) {
      listData.value = res.data.data.list;
      total.value = res.data.data.total;
    }
  });
};
const preview = (imgs) => {
  const temp = imgs.map((item) => ({ url: item.file_name }));
  console.log("preview", temp);
  ipcRenderer.invoke("view-img", JSON.stringify(temp));
};
const router = useRouter();
const draftEdit = (val) => {
  router.push(`/workBenchIndex/nicheDraft?isDraft=1&idDraft=${val}`);
};

const initF = () => {
  searchShow.value = false;
  params.value = {
    title: undefined,
    type: undefined,
    page: 1,
    pageSize: 10,
  };
  getData();
};

const getDataRunDr = () => {
  console.log(params, "ppp");
  // 搜索
  filterVisible.value = false;
  getData();
};

const searchShow = ref(false);
const getDataRunDrTwo = () => {
  // 搜索
  filterVisible.value = false;
  searchShow.value = true;
  getData();
};

const clearFilterStatus = () => {
  params.value.type = undefined;
  searchShow.value = false;
  getData();
};

const deleteWays = (id) => {
  const myDialog = DialogPlugin({
    header: t("niche.administrator.del"),
    theme: "info",
    body: t("niche.delcg"),
    className: "dialog-classp24",
    style: "color: rgba(0, 0, 0, 0.6)",
    onConfirm: () => {
      getDraftDelete(id).then((res) => {
        if (res.data) {
          MessagePlugin.success(t("niche.cdelsucc"));
          getData();
          myDialog.hide();
        }
      });
    },
    onCancel: () => {
      myDialog.hide();
    },
  });
};

const filterVisible = ref(false);
const showFilter = () => {
  filterVisible.value = true;
};

const pageChange = (e) => {
  params.value.page = e.current;
  params.value.pageSize = e.pageSize;
  getData();
};
</script>

<style lang="less" scoped>
@import "./styles/common.less";
.niche-admin {
  height: 100%;
  display: flex;
  padding: 0 !important;
}
.right-box {
  width: calc(100vw - 240px);
}
.right-content::-webkit-scrollbar {
  width: 0px;
}
.right-content {
  padding: 16px !important;
  position: relative;
  border-radius: 4px;
  background: #fff;
  height: calc(100vh - 40px);
  overflow-y: auto;
  width: 100%;
}
.right-header {
  display: flex;
  border-bottom: 1px solid #eceff5;
  padding: 12px 0;
  justify-content: end;
  margin-bottom: 24px;
}
.data-box-con {
  color: var(--kyy-color-table-text, #1a2139);
  height: calc(100% - 87px);
  overflow-y: auto;
}
.g-box {
  display: flex;
  .main-img {
    width: 72px;
    height: 72px;
    border-radius: 4px;
    img {
      cursor: pointer;
      width: 72px;
      height: 72px;
      border-radius: 4px;
      object-fit: cover;
    }
  }
  .good-info {
    margin-left: 12px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    .good-title {
      display: flex;
      .tag {
        display: flex;
        height: 20px;
        flex-shrink: 0;
        padding: 2px 4px;
        justify-content: center;
        align-items: center;
        margin-right: 4px;
        border-radius: var(--kyy_radius_tag_s, 4px);
        background: var(--kyy_color_tag_bg_brand, #eaecff);
        color: var(--kyy_color_tag_text_brand, #4d5eff);
        text-align: center;
        float: left;
        font-size: 12px;
      }
      .tag1 {
        display: flex;
        height: 20px;
        flex-shrink: 0;
        padding: 2px 4px;
        justify-content: center;
        align-items: center;
        margin-right: 4px;
        border-radius: var(--kyy_radius_tag_s, 4px);
        background: var(--kyy_color_tag_bg_cyan, #e6f9f8);
        color: var(--kyy_color_tag_text_cyan, #11bdb2);
        text-align: center;

        /* kyy_fontSize_1/regular */
        font-family: "PingFang SC";
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px; /* 166.667% */
        text-align: center;
        font-size: 12px;
      }
      .tag2 {
        display: flex;
        height: 20px;
        flex-shrink: 0;
        padding: 2px 4px;
        justify-content: center;
        align-items: center;
        margin-right: 4px;
        border-radius: var(--kyy_radius_tag_s, 4px);
        background: var(--kyy_color_tag_bg_warning, #ffe5d1);
        color: var(--kyy_color_tag_text_warning, #fc7c14);
        text-align: center;
        font-size: 12px;
      }
      .tc {
        color: var(--text-kyy-color-text-1, #1a2139);
        width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-family: PingFang SC;
        font-size: 14px;
        font-style: normal;
        line-height: 22px; /* 157.143% */
        flex: 1;
        font-weight: 600;
        span {
          display: block;
          width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
    .good-time {
      color: var(--text-kyy-color-text-3, #828da5);
      display: flex;
      gap: 2px;
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
      margin-top: 6px;
    }
  }
}
:deep(.data-box-con .t-table__empty) {
  display: none;
}
.filter-res{
  margin-bottom: 24px;
}
</style>
