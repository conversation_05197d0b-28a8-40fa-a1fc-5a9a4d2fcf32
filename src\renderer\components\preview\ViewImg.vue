<template>
  <div class="view-img-box container-view" @contextmenu="onRightClick($event)" @click.capture="visible = false"  @mouseup.stop="">
    <title-bar style="background-color: #f5f8fe"  is-title="图片预览">
      <template #content>
        <div class="title-bar-content">
          <!-- <iconpark-icon class="bar-icon" name="iconleft"></iconpark-icon>

          <iconpark-icon class="bar-icon iconrights" name="iconright"></iconpark-icon> -->

          <iconpark-icon
            :class="{ 'not-click': previewIndex === 0 }"
            v-if="previewIndex !== 0"
            class="bar-icon"
            name="iconleft"
            @click="onSwitchLeft"
          ></iconpark-icon>

          <iconpark-icon
            v-if="previewIndex !== images.length - 1"
            :class="{
              'not-click': previewIndex === images.length - 1,
            }"
            class="bar-icon iconrights"
            name="iconright"
            @click="onSwitchRight"
          ></iconpark-icon>

          <iconpark-icon class="bar-icon" name="iconproportion" @click="barIconOpt('onetoone')"></iconpark-icon>

          <iconpark-icon class="bar-icon" name="iconreduce-b762a0k5" @click="barIconOpt('onZoomout')"></iconpark-icon>

          <iconpark-icon class="bar-icon" name="iconadd-b762a0p0" @click="barIconOpt('onZoomin')"></iconpark-icon>

          <iconpark-icon
            class="bar-icon"
            name="iconforward"
            @click="barIconOpt('onAnticlockwiseRotate')"
          ></iconpark-icon>

          <iconpark-icon class="bar-icon" name="iconretreat" @click="barIconOpt('onClockwiseRotate')"></iconpark-icon>

          <!-- -->

          <!-- isDowFile: true,

            isCopyFile: true,

            isShareFile: true, -->

          <iconpark-icon
            v-show="images.length > 0 && !images[previewIndex].isCopyFile"
            class="bar-icon"
            name="iconcopy-b762a0ma"
            @click="barIconOpt('copy')"
          ></iconpark-icon>

          <iconpark-icon
            v-if="isDow"
            v-show="images.length > 0 && !images[previewIndex].isDowFile"
            class="bar-icon"
            name="icondownload-b766bhki"
            @click="dowFile"
          ></iconpark-icon>

          <iconpark-icon
            v-show="images.length > 0 && !images[previewIndex].isDowFile"
            v-else
            class="bar-icon"
            name="iconfolderopen-b766bhgc"
            @click="dowFile"
          ></iconpark-icon>
          <fileInfo
          v-model:visible="visible"
          class="bar-icon" :objs="images[previewIndex]"></fileInfo>
        </div>
      </template>
    </title-bar>
    <!-- showFlag -->
    <imageView
      v-lkloading="{ show: showFlag, opacity: true, height: true }"
      v-show="images.length > 0"
      ref="imageViewRef"
      :src="images"
      :menu-items="menuItems"
      @on-upd-index="onUpdIndex"
      @fist="fist"
      @last="last"
    ></imageView>

    <div
      v-if="
        showContextMenu &&
        images.length > 0 &&
        images[previewIndex] &&
        (!images[previewIndex].isDowFile || !images[previewIndex].isCopyFile || !images[previewIndex].isShareFile)
      "
      class="menu"
      :style="menuStyle"
    >
      <ul>
        <li v-for="(item, index) in menuItems" v-show="showBtn(item)" :key="index" @click="handleItemClick(item)">
          {{ item }}
        </li>
      </ul>
    </div>

    <select-member
      v-model:visible="shareVisible"
      :active-card-id="images[previewIndex]?.myCardId ? [images[previewIndex].myCardId] : []"
      :show-my-group-menu="true"
      :show-dropdown-menu="false"
      :change-menus="true"
      :attach="'body'"
      @confirm="shareList"
    />
  </div>
</template>

<script setup lang="ts">
import TitleBar from "@renderer/components/common/viewFileBar.vue";
import fileInfo from "@/components/preview/components/fileInfo.vue";

import { ref, computed, watch } from "vue";

import { MessagePlugin } from "tdesign-vue-next";

import { useI18n } from "vue-i18n";
import { copyImage } from '@renderer/utils/myUtils'

import selectMember from "@renderer/components/rk-business-component/select-member/common-add-members.vue";

import { getImageThumbnail, MsgShareType, sendApplicationMsg } from "@renderer/utils/share.ts";

import imageView from "@/components/preview/components/imageView.vue";
import LynkerSDK from "@renderer/_jssdk";

const { t } = useI18n();
const showFlag = ref(false);
const imageViewRef = ref(null);

const visible = ref(false);
const images = ref([]);

let isDow = ref(true);

const shareVisible = ref(false);

// 获取图片尺寸
const getImageSize = (url) => {
    return new Promise(function (resolve, reject) {
        let image = new Image();
        image.onload = function () {
            resolve({
                width: image.width,
                height: image.height,
            });
        };
        image.onerror = function () {
            reject(new Error('error'));
        };
        image.src = url;
    });
}
const shareList = async(data) => {
  if (data) {
    const image = images.value[previewIndex.value]
    const res = await getImageSize(image.url)
    let objs = {
      imgUrl: image.url,

      thumbnail: getImageThumbnail(image.url),

      width: res?.width,

      height: res?.height,

      size: image.size,

      type: image.type,
    };

    console.log(objs, "objssssss");

    sendApplicationMsg(MsgShareType.image, objs, data);

    MessagePlugin.success({
      content: t("clouddisk.success"),

      duration: 3000,

      zIndex: 99999,

      offset: ["0", "30"],
    });
  }
};

const showContextMenu = ref(null);

const previewIndex = ref(0);

const menuItems = ref([t("niche.copy"), t("order.saveas"), t("square.post.forward")]);

const showBtn = (item) => {
  if (images.value.length > 0) {
    if (item === t("niche.copy") && !images.value[previewIndex.value].isCopyFile) {
      return true;
    }
    if (((item === t("order.saveas") || item === t("order.openFiles")) )&& !images.value[previewIndex.value].isDowFile) {
      return true;
    }
    if (item === t("square.post.forward") && !images.value[previewIndex.value].isShareFile) {
      return true;
    }
  }

  return false;
};

const position = ref({
  top: 0,

  left: 0,
});

const menuStyle = computed(() => ({
  top: `${position.value.top}px`,

  left: `${position.value.left}px`,
}));

const fist = () => {
  MessagePlugin.error({
    content: t("order.fistimg"),

    duration: 3000,

    zIndex: 99999,

    offset: ["0", "30"],
  });
};

const onRightClick = (event, url) => {
  if (isDow.value) {
    menuItems.value[1] = t("order.saveas");
  } else {
    menuItems.value[1] = t("order.openFiles");
  }

  event.preventDefault();

  showContextMenu.value = true;

  const windowHeight = window.innerHeight;

  const windowWidth = window.innerWidth;

  const clickX = event.clientX;

  const clickY = event.clientY;

  let left = clickX;

  let top = clickY;

  if (clickY + 152 > windowHeight) {
    top = clickY - 105;
  }

  if (clickX + 152 > windowWidth) {
    left = windowWidth - 155;
  }

  position.value = { top, left };
};

window.addEventListener("click", () => {
  showContextMenu.value = false;
}); // 右鍵

const onUpdIndex = (val) => {
  previewIndex.value = val;

  getDBurl(previewIndex.value);

  console.log(val, "vvvvvvvvvvvvvvv");
};

const handleItemClick = (item) => {
  if (item === t("niche.copy")) {
    barIconOpt("copy");
  } else if (item === t("square.post.forward")) {
  //  const myCardId = images.value[previewIndex.value].myCardId || '';
  //  if (myCardId) {
  //   dialogData.value = getSelectDialogData(myCardId);
  //  }
    shareVisible.value = true;
  } else {
    dowFile();
  }

  showContextMenu.value = false;
};

const last = () => {
  MessagePlugin.error({
    content: t("order.lastimg"),

    duration: 3000,

    zIndex: 99999,

    offset: ["0", "30"],
  });
};

const onSwitchRight = () => {
  imageViewRef.value.onSwitchRight();
};

const onSwitchLeft = () => {
  imageViewRef.value.onSwitchLeft();
};

document.onkeydown = (event) => {
  let e = event || window.event;

  if (e && e.keyCode === 38) {
    onSwitchLeft();
  }

  if (e && e.keyCode === 40) {
    onSwitchRight();
  }
};

const dowFile = async () => {
  const item = images.value[previewIndex.value];

  const image = item.downloadUrl || item.url;

  const fileName = getImageInfo(image, true);

  const fileType = getImageInfo(image);

  let filePath = null;

  const result = await LynkerSDK.ipcRenderer.invoke("get-dow-record-by-url", image);
  console.log('====>download-info',image );
  if (result) {
    isDow.value = false;

    const { download_path } = result;

    const fileFlag = await LynkerSDK.ipcRenderer.invoke("open-folder", download_path);

    if (!fileFlag) {
      LynkerSDK.ipcRenderer.invoke("del-dow-record", image);

      isDow.value = true;

      MessagePlugin.error({
        content: t("order.notFilePlaseDownload"),

        duration: 3000,

        offset: ["0", "30"],
      });
    }
  } else {
    isDow.value = true;

    filePath = await LynkerSDK.ipcRenderer.invoke("download-file", {
      title: fileName,

      url: image,
    });
    console.log(filePath,'filePathfilePathfilePath');

    if (filePath) {
      isDow.value = false;

      LynkerSDK.ipcRenderer.invoke("add-dow-record", {
        url: image,

        download_path: filePath,

        file_name: fileName,

        file_type: fileType,

        file_size: 0,
      });
      console.log(getImageInfo(filePath,true),'232323232');

      MessagePlugin.success({
        content: `${getImageInfo(filePath,true) || ""}${t("activity.activity.downloadOverTip")}`,

        duration: 3000,

        offset: ["0", "30"],
      });
    }else if(filePath === false){
      // 取消下载
    }else{
      MessagePlugin.error({
        content: `下载失败`,

        duration: 3000,

        offset: ["0", "30"],
      });
    }
  }
};

const getImageInfo = (includeExtension, flag?) => {
  const pathWithoutQuery = includeExtension.split('?')[0];
  const normalizedPath = pathWithoutQuery.replace(/\\/g, '/');
  const fileName = normalizedPath.split('/').pop();
  const fileExtension = fileName.split('.').pop().toLowerCase();
  const baseFileName = fileName.slice(0, -(fileExtension.length + 1));
  const truncatedFileName = baseFileName.length > 32 ? baseFileName.substring(0, 32) + '.' + fileExtension : fileName;
  console.log(fileName, 'fileName');
  console.log(fileExtension, 'fileExtension');
  console.log(truncatedFileName, 'truncatedFileName');

  return flag ? truncatedFileName : fileExtension;
};
const filterImgSrc = (e): string => {
  try {
    const url = new URL(e.url);
    const isHeicRequest = (e.type?.toLowerCase() === 'heic') || url.pathname.toLowerCase().endsWith('.heic')|| url.pathname.toLowerCase().endsWith('.tiff');

    if (isHeicRequest) {
      url.searchParams.set('x-oss-process', 'image/format,webp');
      return url.href;
    }
    return e.url;
  } catch (error) {
    return '';
  }
};
const barIconOpt = async (text) => {
  const actions = {
    copy: async () => {
      console.log('images', images.value[previewIndex.value])
      const image = images.value[previewIndex.value]
      let imageUrl = image.url;
      if (image.size > 1024 * 1024 * 5) {
        imageUrl = image.thumb
      }
      copyImage(imageUrl);
    },

    onZoomout: () => {
      imageViewRef.value.onZoomout();
    },

    onZoomin: () => {
      imageViewRef.value.onZoomin();
    },

    onAnticlockwiseRotate: () => {
      imageViewRef.value?.onAnticlockwiseRotate();
    },

    onClockwiseRotate: () => {
      imageViewRef.value?.onClockwiseRotate();
    },

    onetoone: () => {
      imageViewRef.value?.onResetOrigin();
    },
  };

  const action = actions[text];

  if (action) {
    action();
  }
};

const getDBurl = (val) => {
  console.log(images.value, "images.valueimages.value");

  console.log(val, "images.valueimages.valuevalval");

  LynkerSDK.ipcRenderer

    .invoke("get-dow-record-by-url", images.value[val].url)

    .then((result) => {
      console.log(result, "resultresult");
      if (result) {
        isDow.value = false;
      } else {
        isDow.value = true;
      }
    });
};

LynkerSDK.ipcRenderer.send("getArrayData");
LynkerSDK.ipcRenderer.on("arrayData", (event, array) => {
  console.log(array, "arrayarrayarray");
  showFlag.value = true;
  if (!array || array.length === 0) {
    images.value = [];
    return;
  }

  images.value = [];
  for (let e of array) {
    const filteredUrl = filterImgSrc(e);
    images.value.push({ ...e, thumb: filteredUrl });
  }

  barIconOpt("onetoone");
  const imgIndexAct = images.value[0].imgIndex || 0;

  previewIndex.value = imgIndexAct;
  imageViewRef.value?.editPreviewIndex(imgIndexAct);
  showFlag.value = true;

  if (images.value[imgIndexAct] && images.value[imgIndexAct].url) {
    const image = new Image();
    image.src = images.value[imgIndexAct].url;
    image.crossOrigin = "*";

    image.onload = function () {
      showFlag.value = false;
    };

    image.onerror = () => {
      showFlag.value = false;
    };

    getDBurl(imgIndexAct);
  }
});
</script>

<style lang="less" scoped>
.bar-icon:last-child {
  -webkit-app-region: no-drag;

  margin-right: 0 !important;
  .dow-file {
    margin-left: 0 !important;
  }
  img {
    margin-left: 0 !important;
  }
}
.not-click {
  color: #d5dbe4;
}

.bar-icon {
  font-size: 24px;
  line-height: 24px;
  cursor: pointer;

  margin-right: 24px;

  -webkit-app-region: no-drag;
}

.title-bar-content {
  display: flex;

  align-items: center;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  height: 100%;

  justify-content: center;

  .iconrights {
    margin-right: 24px;

    position: relative;

    &::after {
      content: "";

      width: 1px;

      height: 16px;

      position: absolute;

      top: 4px;

      right: -11px;

      background: #d5dbe4;
    }
  }
}

.container-view {
  // position: relative;

  height: 100%;

  // display: inline-block;
}

.menu {
  position: absolute;

  background-color: #f1f1f1;

  border: 1px solid #ddd;

  z-index: 1999999;

  width: 152px;

  display: inline-flex;

  padding: var(--kyy_radius_dropdown_m, 8px);

  align-items: center;

  border-radius: var(--kyy_radius_dropdown_m, 8px);

  background: var(--kyy_color_dropdown_bg_default, #fff);

  /* kyy_shadow_m */

  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.12);
}

ul {
  margin: 0;

  padding: 0;

  list-style: none;
}

li {
  cursor: pointer;

  display: flex;

  height: 32px;

  min-width: 136px;

  min-height: 32px;

  max-height: 32px;

  padding: 0px 16px;

  align-items: center;

  gap: 12px;

  align-self: stretch;

  color: var(--kyy_color_dropdown_text_default, #1a2139);

  font-size: 14px;

  font-weight: 400;

  line-height: 32px; /* 157.143% */
}

li:hover {
  border-radius: var(--kyy_radius_dropdown_s, 4px);

  background: var(--kyy_color_dropdown_bg_hover, #f3f6fa);
}
img {
    -webkit-user-drag:auto  !important;
}
</style>
