<template>
  <div class="container">
    <div class="head">
      <t-button theme="primary" variant="base" @click="onAddManage">
        <template #icon>
          <iconpark-icon name="iconadd" class="iconadd"></iconpark-icon>
        </template>
        {{ $t('member.impm.pos_1') }}</t-button>

      <div class="tip">
        会员名录按职务排序
        <t-tooltip placement="bottom">
          <template #content>
            按职务排序顺序，展示成员端的会员名录
          </template>
            <iconpark-icon name="iconhelp" class="icon"></iconpark-icon>

        </t-tooltip>


      </div>
    </div>
    <div v-if="false" class="header">
      <h1 class="header-title">{{ $t('member.impm.pos_2') }}</h1>
      <ul class="header-buttons">
        <li>
          <t-button theme="primary" variant="base" @click="onAddManage"> {{ $t('member.impm.pos_1') }}</t-button>
        </li>
      </ul>
    </div>

    <div class="body">
      <div class="body-content">
        <div class="table">
          <!-- <span @click="switchAdmin">转移超级管理员</span> -->
          <!-- :pagination="pagination.total > 10 ? pagination : null" -->
          <t-table
            row-key="id"
            :columns="memberColumns"
            :data="memberData"
            style="width: 100%; margin-top: 24px"
            drag-sort="row-handler"
            @drag-sort="onDragSort"
          >
            <template #drag>
              <!-- <svg class="iconpark-icon w-20 h-20"><use href="#drag" /></svg> -->
              <iconpark-icon name="icondrag" style="font-size: 20px; color: #828da5; margin-top: 5px"></iconpark-icon>
            </template>
            <template #role="{ row }">
              <div>
                {{ row.type_text }}
              </div>
            </template>
            <template #status="{ row }">
              <div>
                {{ switchStatusOptions(row.status) }}
              </div>
            </template>
            <template #available="{ row }">
              <div>
                {{ row.show_status === 1 ? $t('member.impm.pos_3') : $t('member.impm.pos_4') }}
              </div>
            </template>

            <template #money="{ row }">
              <div>
                {{ numberFormat(row.money / 100, 2, '.', ',') }}
              </div>
            </template>

            <template #operate="{ row }">
              <div class="links">
                <t-link
                  theme="primary"
                  hover="color"
                  class="operates-item"
                  @click="editItem(row)"
                >
                  {{ $t('member.impm.pos_5') }}
                </t-link>
                <t-link
                  theme="danger"
                  hover="color"
                  class="operates-item"
                  @click="delItem(row)"
                >
                  {{ $t('engineer.delete') }}
                </t-link>
              </div>
            </template>
            <template #empty>
              <div class="empty">
                <noData :tip="$t('member.impm.pos_6')" :type="'no-member-position'">
                  <template #tip>
                    <div class="flex-column-center">
                      {{ $t('member.impm.pos_6') }}
                      <t-button
                        theme="primary"
                        variant="outline"
                        class="mt-12px"
                        @click="onQuickCreatePositions"
                      >{{$t('member.impm.quick_1')}}</t-button>
                    </div>
                  </template>
                </noData>
              </div>
            </template>
          </t-table>
        </div>
      </div>
    </div>
  </div>
  <QuickMJobsModal ref="quickMJobsModalRef" @on-quick-create="onQuickCreate" />
  <CreateUpdateMJobsModal ref="createUpdateMJobsModalRef" @reload="getMemberList({})" />
</template>

<script lang="ts" setup>
import {
  getAppStaffAxios,
  addAdminAxios,
  getMemberJobsListAxios,
  sortMemberJobsAxios,
  deleteMemberJobsAxios,
  getMemberJobsDetailAxios,
  fastMemberJobsListAxios,
  fastMemberJobsAxios,
} from '@renderer/api/uni/api/businessApi';
import noData from '@renderer/components/common/Empty.vue';
import CreateUpdateMJobsModal from '@renderer/views/uni/member_home/panel/membership-position-panel/modal/create-update-mjobs-modal.vue';
import QuickMJobsModal from '@renderer/views/uni/member_home/panel/membership-position-panel/modal/quick-create-mjobs-modal.vue';

import { getResponseResult, numberFormat } from '@renderer/utils/myUtils';
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';
import { reactive, ref, watch, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { useUniStore } from '@renderer/views/uni/store/uni';
const createUpdateMJobsModalRef = ref(null);

const store = useUniStore();
const quickMJobsModalRef = ref(null);
const { t } = useI18n();
const statusOptions = [
  { label: t('member.impm.pos_7'), value: 1 },
  { label: t('member.impm.pos_8'), value: 2 },
];

const switchStatusOptions = (val) => {
  let res = statusOptions.find((v) => v.value === val);
  if (res) return res.label;

  return '--';
};

const memberColumns = ref([
  {
    colKey: 'drag', // 列拖拽排序必要参数
    title: t('member.impm.drag_1'),
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    width: '64px',
  },
  { colKey: 'name', title: t('member.impm.drag_2'), width: '300px' },
  { colKey: 'level_name', title: t('member.impm.drag_3'), width: '160px' },
  { colKey: 'status', title: t('member.impm.drag_4'), width: '88px' },
  { colKey: 'money', title: t('member.impm.drag_5'), width: '144px', align: 'right' },
  { colKey: 'available', title: t('member.impm.drag_6'), width: '144px', align: 'left' },
  // { colKey: "currency", title: "币种", width: "104px", ellipsis: false },
  { colKey: 'operate', title: t('member.impm.drag_7'), width: '104px' },
]);
const memberData = ref([]);
const optionsMembers = ref([]);
// getMemberAdminAxios

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
  onChange: (pageInfo) => {
    console.log('pagination.onChange', pageInfo);
    pagination.current = pageInfo.current;
    pagination.pageSize = pageInfo.pageSize;

    getMemberList({});
  },
});

const onSearch = () => {
  getMemberList({});
};
// 获取会员职务列表
const getMemberList =(params) => {
  // teamId
  // params.page = pagination.current;
  // params.pageSize = pagination.pageSize;
  return new Promise(async (resolve, reject)=> {
    try {
      let result = await getMemberJobsListAxios(params);
      console.log(result);
      result = getResponseResult(result);
      if (!result) {
        reject();
        return
      } ;
      memberData.value = result.data;
      // pagination.total = result.data.total;
      console.log(memberData.value);
      resolve(result.data)
    } catch (error) {
      const errMsg = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
      reject()
    }
  })

};

watch(
  () => store.activeAccount,
  (val) => {
    if (val) {
      // updateAllCount();
      getMemberList({});
    }
  },
  {
    deep: true,
    // immediate: true
  },
);

const initLass = async ()=> {
  await getMemberList({});
  setTimeout(() => {
    if (store.quickCreatePosTag ) {
      // 这里要注意，
      store.setQuickCreatePosTag(false);
      if(memberData.value.length < 1) {
        onQuickCreatePositions();
      }
    }
  }, 100);

}

onMounted(() => {
  initLass();
});

const onDragSort = (params) => {
  console.log('交换行 ', params);
  // data.value = params.newData;
  onSetSortAxios(params.newData.map((v) => v.id)).then(() => {
    memberData.value = params.newData;
  });
};

// 快速创建职务

const onQuickCreatePositions = async () => {
  console.log('onCreatePositions', quickMJobsModalRef.value);
  const result = await onGetQuickListAxios();
  quickMJobsModalRef.value?.onOpen(result);
};

const onQuickCreate = async (arr) => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  // return;
  try {
    result = await fastMemberJobsAxios({ data: arr }, currentTeamId.value);
    result = getResponseResult(result);
    if (!result) {
      return;
    }
    MessagePlugin.success(t('member.impm.drag_8'));
    quickMJobsModalRef.value?.onClose();
    onSearch();
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    if (errMsg) MessagePlugin.error(errMsg);
  }
};

const onGetQuickListAxios = () => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await fastMemberJobsListAxios();
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }

      resolve(result.data);
    } catch (error) {
      reject();
      const errMsg = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
    }
  });
};

// 编辑
const editItem = async (row) => {
  try {
    let result = await getMemberJobsDetailAxios(row.id);
    console.log(result);
    result = getResponseResult(result);
    if (!result) return;
    // console.log(toRaw(createUpdateMJobsModalRef.value));
    createUpdateMJobsModalRef.value.onOpen(result.data);
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    MessagePlugin.error(errMsg);
  }
};

// 删除

const delItem = (row) => {
  // 请先移除除角色内所有人员再进行删除角色！
  const str = t('member.impm.drag_9');
  const rightBtn = t('member.impm.pta_1'); // 知道了
  const confirmDia = DialogPlugin({
    header: t('member.impm.pta_2'),
    theme: 'info',
    body: str,
    closeBtn: null,
    confirmBtn: rightBtn,
    className: 'delmode',
    onConfirm: async () => {
      // 删除字段操作
      let res = null;

      try {
        res = await deleteMemberJobsAxios(row.id);
        res = getResponseResult(res);
        if (!res) return;
        getMemberList({});
      } catch (error) {
        MessagePlugin.error(error.message);
      }
      confirmDia.hide();

      // onLoading();
    },
    onClose: () => {
      confirmDia.hide();
    },
  });
};

// 排序
const onSetSortAxios = (arr) => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await sortMemberJobsAxios({ ids: arr });
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      optionsMembers.value = result.data;
      resolve('success');
    } catch (error) {
      reject();
      const errMsg = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
    }
  });
};

const onAddManage = () => {
  // getAppMemberList().then(() => {
  //   selectMemberModalRef.value.onOpen();
  // });
  createUpdateMJobsModalRef.value.onOpen();
};

const getAppMemberList = () => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await getAppStaffAxios();
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      optionsMembers.value = result.data;
      resolve('success');
    } catch (error) {
      reject();
      const errMsg = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
    }
  });
};

// 添加成员
const onListenMembers = async (arr) => {
  console.log(arr);
  if (arr && arr.length < 1) return;
  let result = null;
  try {
    result = await addAdminAxios({ idStaffs: arr });
    result = getResponseResult(result);
    if (!result) return;
    MessagePlugin.success(t('member.impm.pta_3'));
    getMemberList({});
    // selectMemberModalRef.value.onClose();
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    MessagePlugin.error(errMsg);
  }
};

const onEnter = () => {};
</script>

<style lang="less" scoped>
@import './public.less';
:deep(.empty) {
  min-height: 70vh;
}
.iconadd {
  font-size: 20px;
}
.table {
  :deep(.t-table table) {
    width: 100%;
    // min-width: 100%;
  }
  :deep(.t-table--layout-fixed) {
    table-layout: auto !important;
  }
}

:deep(.t-dialog--default) {
  padding: 0 !important;
}

.links {
  display: flex;
  gap: 16px;
}

.head {
  background-color: #ffffff;
  padding: 0 16px;
  margin-top: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .tip {
    color: var(--text-kyy_color_text_3, #828DA5);
    text-align: center;


    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
    display: flex;
    align-items: center;
    gap: 4px;
    .icon {
      color: #828DA5;
      font-size: 20px;
    }
  }
}
.body-content {
  min-height: min-content !important;
}

.flex-column-center {
  display: flex;
  flex-direction: column;
}
</style>
