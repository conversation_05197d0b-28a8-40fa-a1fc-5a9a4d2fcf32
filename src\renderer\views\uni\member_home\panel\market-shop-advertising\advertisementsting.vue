<template>
  <div>
    <!-- 市场广告的广告设置 -->
    <div class="card-box">
      <div class="item-card" v-for="(row, index) in listData" :key="index">
        <div class="header-card">
          <div class="card-title">{{ row.name }}</div>
          <t-switch style="width: 62px; padding-left: 8px" :value="row?.status === 1" @change="(e) => onSwitch(e, row)">
            <template #label="slotProps">{{ row?.status ? t("ad.kq") : t("ad.jy") }}</template>
          </t-switch>
        </div>
        <div class="card-body-box">
          <div class="card-body-left" @click="viewadfn(row, index)">
            <img v-if="index === 0" src="@/assets/member/Frame1312318337.svg" />
            <img v-if="index === 1" src="@/assets/member/app1.svg" />
            <img v-if="index === 2" src="@/assets/member/app2.svg" />
            <img v-if="index === 3" src="@/assets/member/app3.svg" />
          </div>
          <div class="card-body-right">
            <div class="card-body-right-item">
              <div class="card-body-right-title">{{ t("ad.lx") }}：</div>
              <div class="card-body-right-value">{{ row.type === 1 ? t("ad.lbt") : t("ad.cgt") }}</div>
            </div>
            <div class="card-body-right-item">
              <div class="card-body-right-title">{{ t("ad.cc") }}：</div>
              <div class="card-body-right-value">{{ `${row.px_h} * ${row.px_v}px` }}</div>
            </div>
            <div class="card-body-right-item">
              <div class="card-body-right-title">{{ t("ad.ktsl") }}：</div>
              <div class="card-body-right-value">{{ t("ad.zdtstf") }}{{ row.max_count }}{{ t("ad.gggw") }}</div>
            </div>
            <div class="card-body-right-item">
              <div class="card-body-right-title">{{ t("ad.jg") }}：</div>
              <div class="card-body-right-value">
                {{
                  row.price_count === 1
                    ? `${row.symbol == "¥" ? "¥" : "MOP"}${row.min_price_text}`
                    : `${row.symbol == "¥" ? "¥" : "MOP"}${row.min_price_text}~${row.symbol == "¥" ? "¥" : "MOP"}${
                        row.max_price_text
                      }`
                }}/天
              </div>
            </div>
            <t-button theme="default" variant="outline" style="font-weight: 600" @click="openPrice(row)">{{
              t("ad.jgsz")
            }}</t-button>
          </div>
        </div>
      </div>
    </div>
    <!-- 广告预览 -->
    <t-dialog v-model:visible="priceFlag" :close-btn="false" :header="true" width="600">
      <template #header>
        <div style="display: flex; align-items: center; width: 100%; justify-content: space-between">
          <div>{{ t("ad.jgsz") }}</div>
          <img
            style="width: 24px; cursor: pointer; height: 24px"
            src="@/assets/<EMAIL>"
            @click="(priceFlag = false), (timeListData = [])"
          />
        </div>
      </template>
      <div class="price-content-box" style="margin-top: 24px;height: 326px;">
        <t-table
          :row-key="'id'"
          :data="timeListData"
          cell-empty-content="--"
          :columns="columns"
          :hover="false"
          :keyboardRowHover="false"
        >
          <template #day="{ row }">
            <div style="padding: 12px">
              <t-input-number
                v-model="row.price"
                :label="activeAccount?.teamRegion === 'CN' ? '￥' : 'MOP'"
              class="iconinput4px"
                style="width: 100%"
                placeholder="0"
                large-number
                theme="normal"
                :allow-input-over-limit="false"
                :decimal-places="0"
                :max="9999"
                :min="0"
              ></t-input-number>
            </div>
          </template>
          <template #times="{ row }">
            <div style="padding: 12px">
              <t-date-range-picker
                allow-input
                v-model="row.times"
                clearable
                @focus="focusss"
                :disable-date="{
                  before: dayjs().subtract(0, 'day').format(),
                  after: maxDay,
                }"
                @pick="handleChange"
                v-if="row.is_default !== 1"
              />
              <div v-else>{{ t("ad.mrjg") }}</div>
            </div>
          </template>
          <template #actions="{ row, rowIndex }">
            <span style="padding: 12px" class="delclass" @click="delItem(row, rowIndex)" v-if="row.is_default !== 1">
              {{ t("ad.del") }}
            </span>
          </template>
        </t-table>
        <t-button  class="btn-icon-font12"  @click="addPriceList" v-if="!businessItem" style="margin-top: 12px" theme="default" variant="outline">
          <template #icon><add-icon /></template>
          {{ t("ad.tjjg") }}</t-button
        >
      </div>
      <template #footer>
        <div class="footer">
          <t-button theme="default" variant="outline" @click="(priceFlag = false), (timeListData = [])">
            取消
          </t-button>
          <t-button theme="primary" @click="onSave"> 确定 </t-button>
        </div>
      </template>
    </t-dialog>
    <viewAdImg ref="viewAdImgref"></viewAdImg>
  </div>
</template>
<script setup lang="ts">
import Frame1312318337 from "@/assets/member/Frame1312318337.svg";
// import pcptapp from "@/assets/member/pcptapp.png";
// import pcpthome from "@/assets/member/pcpthome.png";
// import pcptys from "@/assets/member/pcptys.png";
// import pcptyx from "@/assets/member/pcptyx.png";
const pcptapp = new URL("@/assets/member/pcptapp.png", import.meta.url);
const pcpthome = new URL("@/assets/member/pcpthome.png", import.meta.url);
const pcptys = new URL("@/assets/member/pcptys.png", import.meta.url);
const pcptyx = new URL("@/assets/member/pcptyx.png", import.meta.url);
const pcschome = new URL("@/assets/member/pcschome.png", import.meta.url);
const pcscys = new URL("@/assets/member/pcscys.png", import.meta.url);
const pcscyx = new URL("@/assets/member/pcscyx.png", import.meta.url);
const pcscapp = new URL("@/assets/member/pcscapp.png", import.meta.url);
const imgArrAy = [pcschome, pcscys, pcscyx, pcscapp];
import { viewAdModel } from "@renderer/utils/auth";

import app1 from "@/assets/member/app1.svg";
import app2 from "@/assets/member/app2.svg";
import app3 from "@/assets/member/app3.svg";
import { ref, onMounted, computed } from "vue";
import { DialogPlugin, MessagePlugin } from "tdesign-vue-next";
import { AddIcon, Icon } from "tdesign-icons-vue-next";
import { debounce } from "lodash";
import {
  admanagespacestatus,
  admanagespacelistplatform,
  managespacepricelist,
  admanagespacepriceset,
} from "@renderer/api/member/api/ebookApi";
import { onMountedOrActivated } from "@/hooks/onMountedOrActivated";
import { useMemberStore } from "@renderer/views/member/store/member";
import viewAdImg from "@renderer/views/member/member_home/panel/mark-advertising/components/viewAdImg.vue";

import { useI18n } from "vue-i18n";
import view1 from "@/assets/member/view1.svg";
import { platform } from "@renderer/views/digital-platform/utils/constant";
import { useRoute } from "vue-router";
import dayjs from "dayjs";
import view2 from "@/assets/member/view2.svg";
import { useDigitalPlatformStore } from "@renderer/views/digital-platform/store/digital-platform-store";
import { getMemberTeamID } from "@renderer/views/member/utils/auth";
const digitalPlatformStore = useDigitalPlatformStore();
const route = useRoute();
const priceFlag = ref(false);
const store = useMemberStore();
const viewAdImgref = ref(null);

const pickerValue = ref(null);
const today = dayjs();
const maxDay = ref(dayjs().add(365, "day").format());
const timevalue = ref([]);
const focusss = (val, val1) => {
  if (val.value[0]) {
    maxDay.value = dayjs(val.value[0]).add(30, "day").format();
  } else {
    maxDay.value = dayjs().add(365, "day").format();
  }
};
const viewadfn = (row, index) => {
  viewAdImgref.value?.openWin({
    tabFlag: 1,
    imgIndex: index,
    imgItem: {
      name: row.name,
    },
  });
};
const handleChange = (val, val1) => {
  console.log(val, "valllllllll1");
  console.log(val1, "valllllllll11111111");
  if (val1.partial === "start") {
    maxDay.value = dayjs(val).add(30, "day").format();
  } else {
    maxDay.value = dayjs().add(365, "day").format();
  }
};
const delItem = (row, index) => {
  // const confirmDia = DialogPlugin({
  //   header: "删除",
  //   theme: "info",
  //   body: "确定删除当前价格吗?",
  //   confirmBtn: "确定",
  //   cancelBtn: "取消",
  //   className: "delmode",
  //   onConfirm: async () => {
  timeListData.value.splice(index, 1);
  //   confirmDia.hide();
  // },
  // onClose: () => {
  //   confirmDia.hide();
  // },
  // });
};
const listData = ref([]);
const getList = () => {
  admanagespacelistplatform(
    {
      platform_type: 5,
      ad_type:3
    },
    route.query.teamId,
  ).then((res) => {
    listData.value = res.data.data.list;
    console.log(listData.value, "热啊大叔大婶");
  });
};
onMountedOrActivated(() => {
  getList();
});
onMounted(() => {
  // 初始化时检查value，如果有值，确保它们符合规则
  if (value.value && value.value[0] && value.value[1]) {
    const [startDate, endDate] = value.value;
    if (endDate.diff(startDate, "day") > 30) {
      value.value = null; // 重置value，因为不符合规则
    }
  }
});

const onChange = (dates) => {
  // 这里可以添加额外的逻辑，例如更新界面状态或发送请求
  console.log("Selected dates:", dates);
};
const value = ref(0);
const endValue = ref("");
const { t } = useI18n();
const columns = [
  {
    colKey: "day",
    title: t("ad.jgmt"),
    width: "144",
    ellipsis: true,
  },
  {
    colKey: "times",
    title: t("ad.sxsj"),
    width: "328",
  },
  {
    colKey: "actions",
    title: t("approval.approval_data.ops"),
    width: "80",
  },
];

const timeListData = ref([]);
const dataValue = ref(null);
const openPrice = (val) => {
  managespacepricelist(val.id, route.query.teamId).then((res) => {
    console.log(res, "热死事实上事实上事实上撒阿萨达");
    for (let index = 0; index < res.data.data.list.length; index++) {
      const element = res.data.data.list[index];
      if (element?.is_default !== 1) {
        element.times = [element?.begin_at, element?.end_at];
      }
    }
    timeListData.value = res.data.data.list;
  });
  dataValue.value = val;
  maxDay.value = dayjs().add(365, "day").format();

  priceFlag.value = true;
};
const viewImgAdPre = (val) => {
  const { ipcRenderer } = require("electron");
  ipcRenderer.invoke(
    "preview-file",
    JSON.stringify({
      url: val,
      type: "jpg",
    }),
  );
};
const addPriceList = () => {
  // priceFlag.value = true;
  if (timeListData.value.length >= 4) {
    return MessagePlugin.error(t("ad.zdtjsg"));
  }
  timeListData.value.push({

    day: "",
    times: [],
    type: "",
    id: 2,
  });
};

const platformCpt = computed(() => {
  return props.platform || route.query?.platform;
});
const activeAccount = computed(() => {
  if (platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount;
  } else if (platformCpt.value === platform.digitalWorkbench) {
    return route.query;
  } else {
    return store.activeAccount;
  }
});
console.log(activeAccount, "activeAccountactiveAccount");
function daysBetweenDates(beginAt, endAt) {
  const startDate = dayjs(beginAt);
  const endDate = dayjs(endAt).add(1, "day");

  // 计算天数差
  const daysBetween = endDate.diff(startDate, "day");

  return daysBetween;
}
let btnFlag=false

const onSave = () => {
  if (btnFlag) {
    return
  }
  console.log(timeListData.value, "timeListDatatimeListData");
  let arrs = [];

  for (let index = 0; index < timeListData.value.length; index++) {
    const element = timeListData.value[index];
    if (!element.price && element.price !== 0) {
      element.price=0;

      // MessagePlugin.error(t("ad.qsrjg"));
      // arrs.length = 0;
      // return;
    }
    if (element?.is_default !== 1 && (element?.times?.length == 0 || !element?.times || element?.times?.length == 1)) {
      MessagePlugin.error(t("ad.qsrsxsj"));
      arrs.length = 0;
      return;
    }
    console.log(element, "elementelementelement");

    if (element?.times && daysBetweenDates(element?.times[0], element?.times[1]) > 30) {
      MessagePlugin.error(t("ad.qzrqzd30"));
      return;
    }
    if (element?.is_default === 1) {
      arrs.push({
        price: element.price - 0,
        is_default: 1,
      });
    } else {
      arrs.push({
        price: element.price - 0,
        begin_at: element?.times[0] || "",
        end_at: element?.times[1] || "",
        is_default: 0,
      });
    }
  }
  btnFlag=true

  admanagespacepriceset(
    {
      set_id: dataValue.value.id,
      price_arr: arrs,
    },
    route.query.teamId,
  )
    .then((res) => {
      btnFlag=false

      if (res.data.code === 0) {
        getList();
      MessagePlugin.success('操作成功');
        priceFlag.value = false;
        timeListData.value = [];
      }
    })
    .catch((err) => {
      btnFlag=false

      MessagePlugin.error(err.message);
    });
};
const titleFlag = ref("PC_市场_主轮播图");
const titleIndex = ref(0);
const props = defineProps({
  platform: {
    type: String,
    default: "",
  },
});

const currentTeamId = computed(() => {
  if (platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount?.teamId;
  } else if (platformCpt.value === platform.digitalWorkbench) {
    return route.query?.teamId || 0;
  } else {
    return getMemberTeamID();
  }
});

const onSwitch = debounce((e, row) => {
  if (e) {
    const params = JSON.parse(JSON.stringify(row));
    params.enable = e ? 1 : 0;
    admanagespacestatus(
      {
        set_id: row.id,
        status: (params.enable = e ? 1 : 0),
      },
      route.query.teamId,
    )
      .then((res) => {
        if (res.data.code === 0) {
          getList();
          MessagePlugin.success("启用成功");
        }
      })
      .catch((err) => {
        MessagePlugin.warning(err.message);
      });
  } else {
    const confirmDia = DialogPlugin({
      theme: "info",
      header: t("ad.jyggw"),
      body: t("ad.jyh"),

      confirmBtn: "确定",
      cancelBtn: "取消",
      className: "delmode",
      onConfirm: async () => {
        const params = JSON.parse(JSON.stringify(row));
        params.enable = e ? 1 : 0;
        admanagespacestatus(
          {
            set_id: row.id,
            status: (params.enable = e ? 1 : 0),
          },
          route.query.teamId,
        )
          .then((res) => {
            if (res.data.code === 0) {
              getList();
              confirmDia.hide();
              MessagePlugin.success("禁用成功");
            }
          })
          .catch((err) => {
              confirmDia.hide();
              MessagePlugin.warning(err.message);
          });
      },
      onClose: () => {
        confirmDia.hide();
      },
    });
  }
}, 0);
</script>

<style lang="less" scoped>
.delclass {
  color: #d54941;
  cursor: pointer;
}
.content-box {
  position: relative;
  display: flex;
  margin-top: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  /* border-radius: 11.139px; */
}
.content-box-bgc {
  width: 808px;
}
.img1 {
  border-radius: 11.139px;
  position: absolute;
  top: 88px;
  left: 31px;
  width: 544px;
  height: 200px;
}
.img2 {
  position: absolute;
  top: 89px;
  border-radius: 11.139px;
  right: 31px;
  width: 208px;
  height: 96px;
}
.img3 {
  position: absolute;
  top: 192px;
  right: 31px;
  width: 208px;
  height: 96px;
}
.content-bgc {
  width: 375px;
  height: 550px;
  border-radius: 17px 17px 0 0;
  border: 1px solid var(--border-kyy_color_border_default, #d5dbe4);
  border-bottom: none;
}
.img4 {
  position: absolute;
  top: 262px;
  left: 239px;
  width: 343px;
  height: 108px;
  border-radius: 11px;
}

.btn-icon-font12{
  .t-icon{
    font-size: 16px !important;
  }
}
:deep(.t-input .t-input__prefix:not){
  margin-right: 4px !important;
}
.card-box {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
  .card-body-box {
    display: flex;
    padding: 24px 0 24px 16px;
    align-items: center;
    gap: 16px;
    width: 460px;
    align-self: stretch;
  }
  .card-body-right {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    flex: 1;
    gap: 8px;
  }
  .card-body-right-item {
    width: 244px;
    display: flex;
    align-items: center;
    color: var(--text-kyy_color_text_1, #1a2139);

    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
  }
  .card-body-left:hover {
    border: 1px solid var(--brand-kyy_color_brand_hover, #707eff);
  }

  .card-body-left {
    padding: 12px;
    width: 184px;
    border-radius: 4px;
    background: var(--bg-kyy_color_bg_light, #fff);
    box-shadow: 0px 3px 8px 0px rgba(0, 0, 0, 0.12);
    border: 1px solid transparent;
    cursor: pointer;
    height: 158px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .item-card {
    display: flex;
    width: 464px;
    overflow: hidden;
    flex-direction: column;
    align-items: flex-start;
    border-radius: 8px;
    border: 1px solid var(--border-kyy_color_border_default, #d5dbe4);
    .header-card {
      display: flex;
      padding: 12px 16px;
      width: 100%;border-radius: 8px 8px 0 0;
      align-items: center;
      gap: 24px;
      background: var(--bg-kyy_color_bg_deepest, #eceff5);
      justify-content: space-between;
      .card-title {
        color: var(--text-kyy_color_text_1, #1a2139);

        /* kyy_fontSize_2/bold */
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 22px; /* 157.143% */
        margin-right: 24px;
      }
    }
  }
}
.card-body-right-value {
  width: 172px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  word-break: break-all;
}
.iconinput4px {
  :deep(.t-input__prefix) {
    margin-right: 4px !important;
  }
}
</style>
