<template>
  <t-popup
    v-model="visible"
    v-bind="$attrs"
    attach="body"
    destroy-on-close
    overlay-inner-class-name="d-square-o-setting"
    placement="bottom-right"
  >
    <slot>
      <span class="btn-more">
        <iconpark-icon name="iconmore" class="icon" />
      </span>
    </slot>

    <template #content>
      <div class="dropdown-select" :style="{ width: $attrs.width }">
        <div
          v-for="item in listData"
          :key="item.value"
          class="item"
          @click="emits('change', item); visible = false"
        >
          <slot name="label">{{ item.label }}</slot>

          <t-icon
            v-if="item.trigger"
            class="arrow"
            name="chevron-right"
            size="16"
          />
          <t-switch
            v-if="item.switch"
            v-model="item.switchValue"
            size="small"
            @click.stop
            @change="emits('change', item); visible = false"
          />

          <slot name="append" />
        </div>
      </div>
    </template>
  </t-popup>
</template>

<script setup lang='tsx'>
import { ref, watch } from 'vue';

const props = defineProps({
  list: {
    type: Array,
    default: () => [],
  },
});
const emits = defineEmits(['select', 'change']);
const listData = ref(props.list);
watch(() => props.list, (newVal) => {
  listData.value = newVal;
});

const visible = ref(false);
</script>

<style scoped lang='less'>
.btn-more {
  width: 24px;
  height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  &:hover {
    cursor: pointer;
    background: var(--td-bg-color-container-hover);
    border-radius: var(--td-radius-default);
  }
}
</style>
