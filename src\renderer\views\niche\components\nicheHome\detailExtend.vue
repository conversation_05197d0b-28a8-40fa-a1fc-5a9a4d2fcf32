<template>
  <div class="detailExtend">
    <div class="header">
      <div class="tab">
        <!-- <div class="item" @click="goDetail">{{ t("niche.sjxq") }}</div> -->
        <!-- <div class="item2">
          {{ t("niche.tg") }}
          <div class="tag"></div>
        </div> -->
        <div class="chan-box">
        <div :class="`st-tag${promotioneData.effective_state}`" v-show="false">
          {{ effective_state_text[promotioneData.effective_state] }}
        </div>
        <div class="title">{{ promotioneData.title }}</div>
      </div>
      </div>
      <div class="search">
        <div @keyup.enter="getDataRunDr">
          <t-input
            v-model="params.promotion_keyword"
            style="width: 304px"
            :placeholder="t('niche.ssqd')"
            clearable
            @blur="getDataRunDr"
          >
            <template #prefix-icon>
              <iconpark-icon name="iconsearch" class="name-icon" />
            </template>
          </t-input>
        </div>
        <div v-if="paramsSuper" class="af-icon" @click="showFilter">
          <img src="@renderer/assets/approval/icons/fbutton.svg" style="width: 32px; height: 32px" alt="" />
        </div>
        <t-button
          v-else
          style="width: 32px; height: 32px"
          type="button"
          theme="default"
          variant="outline"
          @click="showFilter"
        >
          <iconpark-icon name="iconscreen" class="icon iconscreen" />
        </t-button>
        <t-popup overlay-class-name="niche-popup-de" placement="bottom-right" :z-index="1000" destroy-on-close>
          <template #content>
            <div class="actions-boxs niche-popup-de-actions">
              <div class="item" @click.stop="addSqRun()">
                <div class="text">{{ t("niche.gch") }}</div>
              </div>
              <div class="item" @click.stop="addfigureRun()">
                <div class="text">{{ t("niche.szpt") }}</div>
              </div>
              <div v-if="promotioneData.platform_discovery === 0" class="item" @click.stop="addLK()">
                <div class="text">{{ t("niche.lkpt") }}</div>
              </div>
            </div>
          </template>
          <t-button style="margin-left: 8px"> <iconpark-icon name="iconadd" class="name-icona" />添加渠道</t-button>
        </t-popup>
      </div>
    </div>
    <div class="srr-box">

      <div class="btns">
        <t-button
          class="f-btn"
          :theme="
            !params.process_state && params.process_state !== 0 && !params.release_state && params.release_state !== 0
              ? 'primary'
              : 'default'
          "
          @click="clearState()"
        >
          {{ t("niche.all") }}
        </t-button>

        <t-button class="f-btn" :theme="params.process_state === 0 ? 'primary' : 'default'" @click="setProcessState(0)">
          {{ t("niche.shenhez") }}
        </t-button>
        <t-button class="f-btn" :theme="params.process_state === 2 ? 'primary' : 'default'" @click="setProcessState(2)">
          {{ t("niche.status_rejected") }}
        </t-button>

        <t-button class="f-btn" :theme="params.release_state === 1 ? 'primary' : 'default'" @click="setReleaseState(1)">
          {{ t("niche.status_in_progress") }}
        </t-button>
        <t-button class="f-btn" :theme="params.release_state === 0 ? 'primary' : 'default'" @click="setReleaseState(0)">
          {{ t("niche.status_not_active") }}
        </t-button>
        <t-button class="f-btn" :theme="params.release_state === 2 ? 'primary' : 'default'" @click="setReleaseState(2)">
          {{ t("niche.status_off_shelf") }}
        </t-button>
        <t-button class="f-btn" :theme="params.release_state === 3 ? 'primary' : 'default'" @click="setReleaseState(3)">
          {{ t("niche.status_expired") }}
        </t-button>
        <t-button class="f-btn" :theme="params.release_state === 4 ? 'primary' : 'default'" @click="setReleaseState(4)">
          {{ t("niche.status_deleted") }}
        </t-button>
      </div>

      <div v-if="paramsSuper" class="filter-res filter-box" style="margin-bottom: 12px">
        <div class="tit">{{ t("approval.approval_data.sures") }}</div>
        <div v-if="params.promotion_type" class="kword te">
          <span>{{ t("niche.tgqd2") }}{{ chanTextget(params.promotion_type, params.digital_type) }}</span>
          <span class="close2" @click="clearFilterKey()">
            <iconpark-icon name="iconerror" style="font-size: 20px" />
          </span>
        </div>
      </div>

      <div class="table data-box-con">
        <t-table :row-key="'id'" :data="listData" cell-empty-content="--" :columns="columns">
          <template #approve_status="{ row }">
            <div :class="'status-box' + row.approve_status">
              {{ row.approve_status }}
            </div>
          </template>
          <template #promotion_data="{ row }">
            <!-- <div class="title-box">
            <div class="title-text">
              <t-tooltip :content="row.promotion_data?.name">
                {{ row.promotion_data?.name }}
              </t-tooltip>
            </div>
          </div> -->
            <span v-if="row.promotion_type == 3">{{ t("niche.bigsc") }}</span>
            <span v-else>{{ row.promotion_data?.name }}</span>
          </template>
          <template #process_state="{ row }">
            <div class="title-box">
              <!-- <template v-if="row.process_state === 0">
                <div :class="`st-tag0 exsty`">{{ t("niche.status_pending") }}</div>
              </template>
              <template v-if="row.process_state === 1">
                <div :class="`st-tag${row.release_state} exsty`">{{ releaseStateText[row.release_state] }}</div>
              </template>
              <template v-if="row.process_state === 2">
                <div :class="`st-tag2 exsty`">{{ t("niche.status_rejected") }}</div>
              </template> -->
              <div :class="`st-tag${row.state} exsty`">{{ releaseStateText2[row.state] }}</div>

            </div>
          </template>
          <template #channel_id="{ row }">
            <span v-if="row.promotion_type != 3">
              {{ row.promotion_uuid }}
            </span>
          </template>
          <template #promotion_type="{ row }">
            <div>
              <span v-if="row.promotion_type == 1">{{ t("niche.gch") }}</span>
              <span v-if="row.promotion_type == 2">
                {{ digital_type_text[row.digital_type] }}
              </span>
              <span v-if="row.promotion_type == 3">{{ t("niche.lkpt") }}</span>
            </div>
          </template>

          <template #time="{ row }">
            <div>
              {{ row.created_at }}
            </div>
          </template>
          <template #actions="{ row }">
            <div class="actions">
              <template v-if="row.state === 1">
                  <div class="mbtna">
                    <a @click="delistRun(row)">{{ t("niche.xj") }}</a>
                  </div>
                </template>
                <template v-if="row.state === 6">
                <div class="mbtna">
                  <a  @click="reApp(row)">重新申请</a>
                </div>
                <div class="mbtna">
                  <a @click="reonOpen(row.refuse_reason)">{{ t("niche.reson") }}</a>
                </div>
              </template>
              <template v-if="row.state === 2">
                <div class="mbtna">
                  <a @click="listingRun(row)">申请上架</a>
                </div>
                <div class="mbtna">
                  <a @click="reonOpen(row.off_reason)">{{ t("niche.reson") }}</a>
                </div>
              </template>
              <template v-if="row.state === 5 && row.remind === 0 && row.promotion_type !== 3">
                <div class="mbtna">
                  <a  @click="remindRun(row)">提醒</a>
                </div>
              </template>
              <!-- <template v-if="row.process_state === 1">
                <template v-if="row.release_state === 1">
                  <div class="mbtna">
                    <a @click="delistRun(row)">{{ t("niche.xj") }}</a>
                  </div>
                </template>
                <template v-if="row.release_state === 2">
                  <div class="mbtna">
                    <a @click="reonOpen(row.off_reason)">{{ t("niche.reson") }}</a>
                  </div>
                </template>
              </template> -->
              <!-- <template v-if="row.process_state === 2">
                <div class="mbtna">
                  <a style="margin-right: 12px" @click="reApp(row)">重新申请</a>
                </div>
                <div class="mbtna">
                  <a @click="reonOpen(row.refuse_reason)">{{ t("niche.reson") }}</a>
                </div>
              </template> -->
            </div>
          </template>
        </t-table>
        <noData v-if="!listData.length" style="margin-top: 40px" :text="t('approval.no_data')" />
        <!--  -->
        <div v-if="total && total > 10" class="pagination">
          <t-pagination
            :total="total"
            :total-content="false"
            show-previous-and-next-btn
            :show-page-size="false"
            :current="params.page"
            @change="pageChange"
          />
        </div>
      </div>
    </div>
  </div>

  <t-drawer
    v-model:visible="filterVisible"
    :close-btn="true"
    size="472px"
    :header="t('approval.approval_data.sur')"
    class="filterDrawer"
  >
    <div class="form-boxxx">
      <div class="fitem">
        <div class="title">{{ t("niche.tgqd") }}</div>
        <div class="ctl">
          <t-select
            v-model="drawerForm.promotion_type"

            :options="nicheOptions"
            clearable
            :placeholder="t('approval.operation.select')"
          ><template #suffixIcon>
            <img src="@/assets/svg/icon_arrow_down.svg" />
          </template>
        </t-select>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="foot">
        <t-button type="button" style="width: 80px" theme="default" @click="initF">
          {{ t("niche.rest") }}
        </t-button>

        <t-button style="width: 80px" type="button" @click="getDataRunDr">
          {{ t("niche.ss") }}
        </t-button>
      </div>
    </template>
  </t-drawer>

  <t-dialog
    v-model:visible="reVisible"
    :header="t('niche.reson')"
    :on-cancel="reonCancel"
    :on-close="reonCancel"
    width="384px"
    :footer="null"
  >
    <div class="re-con">
      {{ reConValue }}
    </div>
  </t-dialog>
  <delist ref="delistRef" @delist-succ="getData" />

  <addSquare ref="addSquareRef" @add-event="addEvent" />
</template>

<script setup lang="ts" name="detailExtend">
import { computed, onActivated, onMounted, reactive, ref } from "vue";
import { useI18n } from "vue-i18n";
import { useRoute } from "vue-router";
import delist from "@renderer/views/niche/components/nicheHome/delist.vue";
import { DialogPlugin, MessagePlugin } from "tdesign-vue-next";
import noData from "@renderer/components/approvalSatellite/approvalData/noData.vue";
import addSquare from "@renderer/views/niche/components/nicheHome/addSquare.vue";
import { manageApply, manageRemind, manageList, promotioneget, releaseAddChannel, releaseSelf } from "../../apis";

const { t } = useI18n();
const route = useRoute();

const props = defineProps({
  detail: {
    type: Object,
    default: null,
  },
});
onActivated(() => {
  params.release_id = route.query?.id || 0;
  getDataRun();
});
onMounted(() => {
  params.release_id = route.query?.id || 0;
  getDataRun();
});
const emits = defineEmits(["go-detail"]);
const goDetail = () => {
  emits("go-detail");
};
const releaseStateText = ref([
  t("niche.status_not_active"),
  t("niche.status_in_progress"),
  t("niche.status_off_shelf"),
  t("niche.status_expired"),
  t("niche.status_deleted"),
]);
const releaseStateText2 = ref([
  t("niche.status_not_active"),
  t("niche.status_in_progress"),
  t("niche.status_off_shelf"),
  t("niche.status_expired"),
  t("niche.status_deleted"),
  '待审核',
  '审核已拒绝',
]);
const nicheOptions = computed(() => [
  { label: t("niche.gch"), value: 1 },
  { label: t("niche.szsxx"), value: 21 },
  { label: t("niche.szzqx"), value: 22 },
  { label: t("niche.szcbdx"), value: 23 },
  { label: t("niche.szsq"), value: 24 },
  { label: t("niche.lkpt"), value: 3 },
]);

const chanTextget = (promotion_type, digital_type) => {
  console.log(promotion_type, digital_type, "chanTextget");

  if (promotion_type === 2) {
    return digital_type_text.value[digital_type];
  }
  return chanText.value[promotion_type];
};

const digital_type_text = ref(["", t("niche.szsxx"), t("niche.szzqx"), t("niche.szcbdx"), t("niche.szsq")]);
const chanText = ref(["", t("niche.gch"), t("niche.szpt"), t("niche.lkpt")]);

const effective_state_text = ref([
  t("niche.status_not_active"),
  t("niche.status_in_progress"),
  t("niche.status_expired"),
  t("niche.status_deleted"),
]);

const reqParamsHandle = () => {
  if (drawerForm.promotion_type > 3) {
    const val = drawerForm.promotion_type.toString().split("");
    params.promotion_type = Number(val[0]);
    params.digital_type = Number(val[1]);
  } else {
    params.promotion_type = drawerForm.promotion_type;
    params.digital_type = undefined;
  }
};
const pageChange = (e) => {
  params.page = e.current;
  params.pageSize = e.pageSize;
  getData();
};
const getDataRun = () => {
  reqParamsHandle();
  getData();
};
const paramsSuper = computed(() => params.promotion_type);
const getDataRunDr = () => {
  filterVisible.value = false;
  params.page = 1;
  params.pageSize = 10;
  getDataRun();
};
const total = ref(0);
const params = reactive({
  title: "",
  promotion_type: undefined,
  release_staff_name: undefined,
  process_state: undefined,
  release_state: undefined,
  promotion_keyword: "",
  classify_name: null,
  created_at_begin: "",
  created_at_end: "",
  digital_type: undefined,
  release_id: null,
  page: 1,
  pageSize: 10,
});
const listData = ref([]);
const promotioneData = ref({
  title: "",
  effective_state: 0,
  platform_discovery: 0,
});

const getData = () => {
  manageList(params).then((res: any) => {
    console.log(res);
    if (res.data) {
      listData.value = res.data.data.list;
      total.value = res.data.data.total;
    }
  });
  promotioneget(params.release_id).then((res: any) => {
    console.log(res);
    if (res.data) {
      promotioneData.value = res.data.data;
    }
  });
};
const drawerForm = reactive({
  promotion_type: null,
});
const filterVisible = ref(false);
const showFilter = () => {
  filterVisible.value = true;
};
const clearFilters = () => {
  params.promotion_type = undefined;
  params.page = 1;
  params.pageSize = 10;
  getDataRun();
};
const initF = () => {
  filterVisible.value = false;
  clearFilters();
};
const columns = ref([
  {
    colKey: "promotion_type",
    title: t("niche.tgqd"),
    width: "120",
  },
  {
    colKey: "promotion_data",
    title: t("niche.qdmc"),
    width: "184",
    ellipsis: true,
  },
  {
    colKey: "channel_id",
    title: "ID",
    width: "152",
    ellipsis: true,
  },
  {
    colKey: "time",
    title: t("niche.sqsj2"),
    width: "152",
  },
  {
    colKey: "process_state",
    title: t("niche.ztai"),
    width: "88",
  },
  {
    colKey: "actions",
    title: t("niche.opt"),
    width: "128",
  },
]);
const clearFilterKey = () => {
  drawerForm.promotion_type = undefined;
  params.promotion_type = undefined;
  params.digital_type = undefined;
  getDataRun();
};

const clearState = () => {
  params.process_state = undefined;
  params.release_state = undefined;
  getDataRun();
};
const setProcessState = (key) => {
  params.process_state = key;
  params.release_state = undefined;
  getDataRun();
};
const setReleaseState = (key) => {
  params.release_state = key;
  params.process_state = undefined;
  getDataRun();
};
const reConValue = ref(null);
const reVisible = ref(null);
const reonCancel = () => {
  reVisible.value = false;
};
const reonOpen = (reason) => {
  reConValue.value = reason;
  reVisible.value = true;
};
const delistRef = ref(null);
const delistRun = (data) => {
  delistRef.value.deOpen(data, data.source_type === 1 ? 0 : 3);
};

const reAppReq = (id, myDialog) => {
  manageApply(id, 1).then((res: any) => {
    if (res.data.code === 0) {
      MessagePlugin.success(t("niche.sqcg"));
      getDataRun();
    }else{
      getDataRun();
    }
  }).catch(() => {
    getDataRun();
  });
};

const reApp = (row) => {
  const myDialog = DialogPlugin({
    header: t("niche.tip"),
    theme: "info",
    body: t("niche.reApp_tips", { name: row.promotion_type === 3 ? t("niche.lkpt") : row.promotion_data.name }),
    className: "dialog-classp32",
    onConfirm: () => {
      reAppReq(row.id, myDialog);
      myDialog.hide();
    },
    onCancel: () => {
      myDialog.hide();
    },
  });
};

const releaseAddChannelRun = (channel) => {
  releaseAddChannel({ id: params.release_id, channel }).then((res: any) => {
    if (res.data.code === 0) {
      MessagePlugin.success("操作成功！");
      getDataRun();
    }
  });
};
const addEvent = (data) => {
  console.log("addEventdata", data);
  const channel = [data];
  releaseAddChannelRun(channel);
};
const addSquareRef = ref(null);
const addSqRun = () => {
  if (!props.detail?.channel_data[0]?.id) {
    sqTip();
    return;
  }
  addSquareRef.value.sqOpen(1, params.release_id, []);
};
const addfigureRun = () => {
  if (!props.detail?.channel_data[0]?.id) {
    sqTip();
    return;
  }
  addSquareRef.value.sqOpen(2, params.release_id, []);
};

const releaseSelfRun = (myDialog) => {
  releaseSelf(params.release_id, 3).then((res: any) => {
    if (res.data.code === 0) {
      MessagePlugin.success("操作成功！");
      myDialog.hide();
      getDataRun();
    }
  });
};

const addLK = () => {
  if (!props.detail?.channel_data[0]?.id) {
    sqTip();
    return;
  }
  const myDialog = DialogPlugin({
    header: t("niche.tip"),
    theme: "info",
    body: t("niche.lktip"),
    className: "dialog-classp32",
    onConfirm: () => {
      releaseSelfRun(myDialog);
    },
    onCancel: () => {
      myDialog.hide();
    },
  });
};

const sqTip = () => {
  const myDialog = DialogPlugin({
    header: t("niche.tip"),
    theme: "info",
    body: t("niche.tgskss"),
    className: "dialog-classp32",
    cancelBtn: null,
    onConfirm: () => {
      myDialog.hide();
    },
  });
};

const remindRunReq = (row) => {
  manageRemind(row.id).then((res: any) => {
    if (res.data.code === 0) {
      MessagePlugin.success("已发送提醒");
      getDataRun();
    }
  });
};

const remindRun = (row) => {
  const myDialog = DialogPlugin({
    header: t("niche.tip"),
    theme: "info",
    body: `仅对当前推广渠道进行审核提醒，不影响其他渠道，确认提醒吗？`,
    className: "dialog-classp32",
    onConfirm: () => {
      remindRunReq(row);
      myDialog.hide();
    },
    onCancel: () => {
      myDialog.hide();
    },
  });
};

const listingRunReq = (id) => {
  manageApply(id, 2).then((res: any) => {
    if (res.data.code === 0) {
      MessagePlugin.success("已提交申请");
      getDataRun();
    }
  });
};

const listingRun = (rowData) => {
  const myDialog = DialogPlugin({
    header: t("niche.tip"),
    theme: "info",
    body: `仅对当前推广渠道进行上架申请，不影响其他渠道，是否继续申请？`,
    className: "dialog-classp32",
    confirmBtn: `申请`,
    onConfirm: () => {
      listingRunReq(rowData.id);
      myDialog.hide();
    },
    onCancel: () => {
      myDialog.hide();
    },
  });
};

</script>

<style lang="less" scoped>
@import "../../styles/common.less";
.detailExtend {
  display: flex;
  width: 100%;
  height: 100%;
  flex-direction: column;
  align-items: flex-start;
  flex: 1 0 0;
  // margin-bottom: 50px;
  .header {
    display: flex;
    align-items: center;
    gap: 24px;
    align-self: stretch;
    height: 56px;
    border-bottom: 1px solid #fff;
    justify-content: space-between;

    .tab {
      display: flex;
      align-items: flex-start;
      gap: 44px;
      .item {
        color: var(--text-kyy_color_text_1, #1a2139);
        text-align: center;
        font-family: "PingFang SC";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 24px; /* 150% */
        cursor: pointer;
      }
      .item2 {
        color: var(--brand-kyy_color_brand_default, #4d5eff);
        font-family: "PingFang SC";
        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        line-height: 24px; /* 150% */
        position: relative;
        cursor: pointer;
        .tag {
          width: 16px;
          height: 3px;
          flex-shrink: 0;
          border-radius: 1.5px;
          background: var(--brand-kyy_color_brand_default, #4d5eff);
          position: absolute;
          top: 37px;
          left: 5px;
        }
      }
    }
    .search {
      display: flex;
      gap: 8px;
    }
  }
  .chan-box {
    display: flex;
    padding: 12px 0px;
    align-items: center;
    gap: 8px;
    align-self: stretch;
    border-radius: 8px;
    .tag {
    }
    .title {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;
      flex: 1 0 0;
      overflow: hidden;
      color: var(--text-kyy_color_text_1, #1a2139);
      text-overflow: ellipsis;
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: 22px; /* 157.143% */
    }
  }
  .btns {
    display: flex;
    padding: 16px 0px 24px 0px;
    align-items: center;
    align-self: stretch;
    gap: 8px;
  }
  .table {
  }
}
.name-icona {
  font-size: 20px;
  color: #fff;
}
.f-btn {
  width: 80px;
}

.exsty {
  height: 24px;
  min-height: 24px;
  max-height: 24px;
  border-radius: 50px;
  font-size: 14px;
  font-weight: 600;
  width: fit-content;
}
.srr-box::-webkit-scrollbar {
  width: 0px;
}
.srr-box {
  border-radius: 8px;
  margin-top: 12px;
  height: calc(100% - 62px);
  padding: 0px 24px 16px 24px;
  overflow-y: auto;
  background-color: #fff;
}
// .title-box {
//   display: flex;
//   justify-content: center;
// }
</style>
