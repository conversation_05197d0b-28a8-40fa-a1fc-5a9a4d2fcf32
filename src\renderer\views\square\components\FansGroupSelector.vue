<template>
  <span>
    <!-- TODO 拆分出两个独立组件-->
    <!--粉丝组-->
    <t-dialog
      v-model:visible="visible"
      width="600"
      attach="body"
      :header="title"
      prevent-scroll-through
      :footer="false"
      z-index="2600"
      class="square-d-fans-group"
    >
      <t-checkbox-group v-model="groupChecked" class="check-list gap-8">
        <t-checkbox
          v-for="item in groupList"
          :key="item.id"
          :value="item.id"
          class="check-item"
        >
          <template #label>
            <div class="flex-y-center rd-4 h-32" :class="{ active: groupChecked.includes(item.id) }" @click.stop.prevent="toEditGroup(item)">
              <div class="inline-block flex-1 min-w-0">
                <div v-if="item.name">{{ item.name }}（{{ item.fans.length }}）</div>
                <div class="line-1" :class="{ 'color-text-3': !!item.name }">{{ item.fans.map(v => v.name).join('、') || '...' }}</div>
              </div>
              <iconpark-icon class="icon text-20" name="iconedit" />
            </div>
          </template>
        </t-checkbox>
      </t-checkbox-group>

      <div class="footer">
        <div class="btn-wrap" @click="toAddFans">
          <iconpark-icon name="iconadd" class="btn-icon" />
          <span>新建分组</span>
        </div>
        <t-button :disabled="groupChecked.length === 0" class="w-88" @click="confirmGroup">{{ $t('square.action.confirm') }}</t-button>
      </div>
    </t-dialog>

    <!--选择人员-->
    <t-dialog
      v-model:visible="selectFansVisible"
      width="680"
      attach="body"
      :header="editingGroup ? '编辑分组' : '新建分组'"
      prevent-scroll-through
      z-index="2601"
      class="square-d-select-fans"
    >
      <div class="flex flex-items-center mb-16">
        <span class="flex-shrink-0 mr-8">分组名称</span>
        <t-input v-model="groupName" placeholder="请输入分组名称" :maxlength="14" />
      </div>
      <div class="container">
        <div v-loading="loading && allFansList.length === 0" class="left">
          <div class="mr-10">
            <t-input
              v-model="fansKeyword"
              :placeholder="$t('square.search')"
              class="mb-12"
              @change="debounceReloadFansList"
              @enter="reloadFansList"
            >
              <template #prefix-icon>
                <iconpark-icon name="iconsearch" class="icon" />
              </template>
            </t-input>
          </div>

          <t-checkbox-group
            v-model="fansChecked"
            v-infinite-scroll="loadFansList"
            :infinite-scroll-immediate-check="false"
            :infinite-scroll-distance="20"
            :infinite-scroll-disabled="loaded"
            infinite-scroll-watch-disabled="loaded"
            class="fans-list w-full gap-8"
            @change="fansChange"
          >
            <t-checkbox
              v-for="item in allFansList"
              :key="item.squareId"
              :value="item.squareId"
              class="w-full flex"
            >
              <template #label>
                <div class="name-wrap">
                  <KyyAvatar
                    class="mr-4"
                    shape="circle"
                    avatar-size="24px"
                    :image-url="item.avatar"
                    :user-name="item.name"
                  />
                  <div class="name line-1">{{ item.name }}</div>
                </div>
              </template>
            </t-checkbox>

            <div class="text-center w-full">
              <t-loading v-if="loading" :text="$t('components.infiniteLoading.loading')" size="small" />
            </div>
          </t-checkbox-group>
          <Empty v-if="!allFansList.length" />
        </div>

        <div class="right">
          <div class="select-count">{{ $t('square.selected') }}: {{ selectedFans.length }}</div>
          <div v-for="item in selectedFans" :key="item.squareId" class="select-item">
            <div class="name-wrap">
              <KyyAvatar
                class="mr-4"
                shape="circle"
                avatar-size="24px"
                :image-url="item.avatar"
                :user-name="item.name"
              />
              <div class="name line-1">{{ item.name }}</div>
            </div>
            <iconpark-icon name="iconerror" class="text-20 cursor-pointer color-text-3" @click="removeFans(item)" />
          </div>
        </div>
      </div>

      <template #footer>
        <div class="flex">
          <t-button
            v-if="editingGroup"
            theme="danger"
            variant="outline"
            @click="removeGroup"
          >删除分组</t-button>
          <div class="flex-1">
            <t-button theme="default" @click="selectFansVisible = false">
              取消
            </t-button>
            <t-button
              theme="primary"
              style="order: 2"
              :disabled="!canSubmit"
              @click="onFansConfirm"
            >
              {{ $t('square.action.confirm') }}
            </t-button>
          </div>
        </div>
      </template>
    </t-dialog>
  </span>
</template>

<script lang="tsx">
export default {
  inheritAttrs: false,
};
</script>

<script lang="tsx" setup>
import to from 'await-to-js';
import {
  computed, nextTick, ref, watch, PropType,
} from 'vue';
import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next';
import { useI18n } from 'vue-i18n';
import debounce from 'lodash/debounce';
import { searchFans } from '@/api/square/home';
import { useSquareStore } from '@/views/square/store/square';
import {
  addFansGroup, delFansGroup, getFansGroupList, updateFansGroup,
} from '@/api/square/fans';
import KyyAvatar from '@/components/kyy-avatar/index.vue';
import Empty from '@/components/common/Empty.vue';

const { t } = useI18n();
const store = useSquareStore();

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  fansGroupId: {
    type: Array as PropType<number[]>,
    default: () => [],
  },
  title: String,
});

const emit = defineEmits(['update:modelValue', 'click', 'confirm']);
const visible = computed({
  get() {
    return props.modelValue;
  },
  set(value) {
    emit('update:modelValue', value);
  },
});

// 选分组
// const groupVisible = ref(false);
const groupChecked = ref([]);
const groupList = ref([]);

const getGroupList = async () => {
  const [err, res] = await to(getFansGroupList());
  if (err) return;
  groupList.value = res.data.groups;
};

watch(() => visible.value, async (newVal: boolean) => {
  if (!newVal) return;
  await getGroupList();
}, {
  immediate: true,
});

watch(() => props.fansGroupId, () => {
  groupChecked.value = props.fansGroupId;
}, { immediate: true });

watch(() => props.title, (val) => {
  // groupChecked.value = props.fansGroupId;
});

// ================ 选人员 ================

const selectFansVisible = ref(false);
const fansKeyword = ref('');
const fansChecked = ref([]);
const selectedFans = ref([]);
const groupName = ref('');

const toAddFans = () => {
  selectFansVisible.value = true;
  fansKeyword.value = '';
  fansChecked.value = [];
  selectedFans.value = [];
  groupName.value = '';
  editingGroup.value = null;
};

const allFansList = ref([]);
const loading = ref(false);
const loaded = ref(false);
const nextPageToken = ref('');
const fansParams = computed(() => ({
  'page.size': 20,
  square_id: store.squareId,
  keyword: fansKeyword.value,
  'page.next_page_token': nextPageToken.value,
}));

// 搜索粉丝列表
const getFansList = async () => {
  if (loading.value) return;

  loading.value = true;
  const [err, res] = await to(searchFans(fansParams.value));
  loading.value = false;
  if (err) return;

  const { page, squares } = res.data;
  loaded.value = page.size < fansParams.value['page.size'];
  nextPageToken.value = page.nextPageToken;
  allFansList.value = allFansList.value.concat(squares);
};

const loadFansList = () => {
  if (!loaded.value) getFansList();
};

const reloadFansList = () => {
  nextPageToken.value = '';
  allFansList.value = [];
  getFansList();
};

const debounceReloadFansList = debounce(reloadFansList, 600);

const fansChange = async (ids) => {
  await nextTick();

  // 左侧勾选数据
  const checkedList = allFansList.value.filter((v) => fansChecked.value.includes(v.squareId));
  // 选中数据去重的id列表
  const selectedIds = new Set(selectedFans.value.map((v) => v.squareId));
  // 将选中的添加到右侧
  checkedList.forEach((v) => {
    if (!selectedIds.has(v.squareId)) {
      selectedFans.value.push(v);
      selectedIds.add(v.squareId);
    }
  });

  // 移除取消勾选的
  selectedFans.value = selectedFans.value.filter((v) => ids.includes(v.squareId) && selectedIds.has(v.squareId));
};

const removeFans = (item) => {
  let idx = selectedFans.value.findIndex((v) => v.squareId === item.squareId);
  if (idx > -1) {
    selectedFans.value.splice(idx, 1);

    idx = fansChecked.value.indexOf(item.squareId);
    idx > -1 && fansChecked.value.splice(idx, 1);
  }
};

watch(() => selectFansVisible.value, (val) => {
  if (!val) return;
  reloadFansList();
});

// 编辑分组
const editingGroup = ref(null);
const toEditGroup = (item) => {
  editingGroup.value = item;
  groupName.value = item.name;
  selectedFans.value = item.fans;
  fansChecked.value = item.fans.map((v) => v.squareId);
  selectFansVisible.value = true;
};
const doEditGroup = async () => {
  const [err] = await to(updateFansGroup({
    fans_group_id: editingGroup.value.id,
    name: groupName.value,
    squareIds: selectedFans.value.map((v) => v.squareId),
  }));
  if (err) return;

  selectFansVisible.value = false;
  editingGroup.value = null;
  await MessagePlugin.success(t('square.post.fansTip1'));

  await getGroupList();
};

const removeConfirm = () =>
  // eslint-disable-next-line no-new, implicit-arrow-linebreak
  new Promise<boolean>((resolve) => {
    const confirmDia = DialogPlugin.confirm({
      header: '确定要删除该分组吗？',
      body: () => '删除后无法恢复',
      closeOnOverlayClick: false,
      theme: 'info',
      onConfirm: async () => {
        confirmDia.destroy();
        resolve(true);
      },
      onCancel: async () => {
        confirmDia.destroy();
        resolve(false);
      },
    });
  });

// 移除粉丝组
const removeGroup = async () => {
  const sure = await removeConfirm();
  if (!sure) return;

  const [err] = await to(delFansGroup({ fans_group_id: editingGroup.value.id }));
  if (err) return;

  selectFansVisible.value = false;
  editingGroup.value = null;
  await MessagePlugin.success(t('square.post.fansTip2'));

  await getGroupList();
};

// 新增粉丝组，需选择
// const canSubmit = computed(() => editingGroup.value || (!editingGroup.value && selectedFans.value.length));
const canSubmit = computed(() => !!groupName.value);

const onFansConfirm = async () => {
  if (!editingGroup.value && !selectedFans.value.length) {
    await MessagePlugin.warning('请选择成员');
    return;
  }

  if (editingGroup.value) {
    await doEditGroup();
    return;

    // await removeGroup();
    // return;
  }

  // 新增粉丝组
  const [err] = await to(addFansGroup({
    name: groupName.value,
    squareIds: selectedFans.value.map((v) => v.squareId),
  }));
  if (err) return;

  selectFansVisible.value = false;
  await MessagePlugin.success(t('square.post.fansTip3'));

  await getGroupList();
};

const confirmGroup = () => {
  console.log(groupChecked.value, groupList.value.filter((v) => groupChecked.value.includes(v.id)));
  emit('confirm', groupChecked.value, groupList.value.filter((v) => groupChecked.value.includes(v.id)));
  visible.value = false;
};
</script>

<style lang="less">
.square-d-fans-group {
  .t-dialog {
    padding: 0;
  }
  .t-dialog__header {
    padding: 24px;
  }
  .t-dialog__body {
    padding-top: 0;
    padding-bottom: 0;
    background: var(--bg-kyy-color-bg-deep, #F5F8FE);
    border-radius: 0 0 16px 16px;
  }
  .active {
    //background-color: #F0F8FF;
  }
  .check-list {
    width: 100%;
    display: flex;
    height: 302px;
    padding: 12px 24px;
    flex-direction: column;
    align-items: flex-start;
    flex-wrap: nowrap;
    gap: 12px;
    align-self: stretch;
    overflow-y: auto;
    background: var(--bg-kyy-color-bg-deep, #F5F8FE);
    .icon {
      font-size: 20px;
      color: #828DA5;
    }
    .check-item {
      width: 100%;
      display: flex;
      padding: 12px 16px;
      align-items: center;
      gap: 16px;
      align-self: stretch;
      border-radius: 8px;
      background: var(--bg-kyy-color-bg-light, #FFF);
      box-shadow: 0 3px 8px 0 rgba(0, 0, 0, 0.12);
      .t-checkbox__input {
        flex-shrink: 0;
      }
      .t-checkbox__label {
        min-width: 0;
      }
    }
    .t-checkbox__label {
      width: 100%;
      margin-left: 0;
    }
  }
  .footer {
    display: flex;
    padding: 24px;
    justify-content: space-between;
    align-items: center;
    align-self: stretch;
    background-color: #fff;
    .btn-icon {
      font-size: 20px;
      margin-right: 4px;
      color: var(--color-button-text-brand-kyy-color-button-text-brand-font-default, #4D5EFF);
    }
    .btn-wrap {
      flex: 1;
      display: flex;
      align-items: center;
      font-size: 14px;
      color: var(--color-button-text-brand-kyy-color-button-text-brand-font-default, #4D5EFF);
      cursor: pointer;
    }
  }
}

.square-d-select-fans {
  .container {
    border: 1px solid var(--divider-kyy-color-divider-light, #ECEFF5);;
    border-radius: 4px;
    display: flex;
    height: 372px;
    .left, .right {
      width: 50%;
      overflow-y: auto;
    }
    .left {
      position: relative;
      padding: 12px 6px 12px 16px;
      display: flex;
      flex-direction: column;
      .scrollbar(6px);
      &:before {
        position: absolute;
        top: 0;
        right: 0;
        content: ' ';
        width: 1px;
        height: 100%;
        background: #e3e6eb;
        border-radius: 8px;
      }
    }
    .right {
      padding: 12px 16px;
    }
  }
  .fans-list {
    overflow-y: auto;
    .t-checkbox__label {
      flex: 1;
    }
  }
  .name-wrap {
    display: flex;
    flex: 1;
    .name {
      flex: 1;
      width: 0;
    }
  }

  .select-count {
    margin-bottom: 16px;
    color: var(--text-kyy-color-text-1, #1A2139);
  }
  .select-item {
    display: flex;
    padding: 8px;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
    border-radius: 4px;
    background: var(--bg-kyy-color-bg-list-hover, #F3F6FA);
  }
}
</style>

<style lang="less" scoped>

</style>
