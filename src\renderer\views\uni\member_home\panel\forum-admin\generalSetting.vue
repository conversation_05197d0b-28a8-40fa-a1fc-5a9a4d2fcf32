<template>
  <div class="setting-wrapper">
    <div class="setting-box">
      <div class="setting-box-header">{{ t("forum.postSetting") }}</div>
      <t-loading size="small" :loading="loading" showOverlay>
        <div class="setting-box-body">
          <div class="setting-item">
            <label>{{ t("forum.postPermission") }}</label>
            <t-checkbox-group v-model="mode" @change="onChange">
              <t-checkbox key="1" :value="0">{{ t("forum.modeAllDes") }}</t-checkbox>
              <t-checkbox key="2" :value="1">{{ t("forum.modePlatformDes") }}</t-checkbox>
              <t-checkbox key="3" :value="2">{{ t("forum.modeAdminDes") }}</t-checkbox>
            </t-checkbox-group>
          </div>
        </div>
      </t-loading>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useI18n } from "vue-i18n";
import { getPlatformSetting, setPlatformSetting } from "@/api/uni/api/forumAdminApi";
import { MessagePlugin } from "tdesign-vue-next";

const { t } = useI18n();

const loading = ref(false);

const mode = ref([null]);

const loadSetting = async () => {
  loading.value = true;

  const res = await getPlatformSetting();

  loading.value = false;

  mode.value = [res.data.data.mode];
};

loadSetting();

const onChange = async (value, context) => {
  if(context.type === 'uncheck'){
    mode.value = [context.option.value];
    return;
  }
  mode.value = [context.option.value];
  await setPlatformSetting({ mode: context.option.value });
  MessagePlugin.success(t("forum.settingSuccess"));
};
</script>

<style lang="less" scoped>
.setting-wrapper {
  padding: 16px;

  .setting-box {
    border: 1px solid var(--divider-kyy_color_divider_deep, #d5dbe4);

    .setting-box-header {
      padding: 12px;
      background: var(--kyy_color_table_hrading_bg, #e2e6f5);
      color: var(--kyy_color_table_hrading_text, #516082);
      font-weight: 600;
    }

    .setting-box-body {
      padding: 16px;
      min-height: 106px;

      .setting-item {
        display: flex;
        gap: 24px;

        label {
          color: var(--text-kyy_color_text_1, #1a2139);
          font-weight: 600;
        }

        .t-checkbox-group {
          flex-direction: column;
          gap: 12px;

          :deep(.t-checkbox__input) {
            border-radius: 50%;
          }
        }
      }
    }
  }
}
</style>
