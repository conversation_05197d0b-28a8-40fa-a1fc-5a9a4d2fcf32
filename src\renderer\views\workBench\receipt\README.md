# 组织收款

## 组织收款1.0（25/04/8 - 25/04/28）

> [TAPD](https://www.tapd.cn/69781318/prong/iterations/card_view/1169781318001001261?q=7cecf03c0c0c6b30136c0956780bba70) / [原型](https://app.mockplus.cn/run/prototype/cQlhQUX3hg/ud2xAIs_r/8OKCXV_gXq5?allowShare=1&cps=expand&ha=1&isShare=false&sr=collapse) / [UI](https://www.figma.com/design/OKIBMDgretWDhsvLUIlyOl/%E7%BB%84%E7%BB%87%E6%94%B6%E6%AC%BE-%E6%A1%8C%E9%9D%A2%E7%AB%AF?node-id=314-38552&m=dev)

### 项目描述

> TODO 项目描述

---

## 流程与状态说明（对齐 PRD）

### 总体流程

共三步：商户入网、商户实名、商户分账

- 入口页：SettlementJoin.vue（已入网显示入网信息、未入网显示申请按钮）
- 申请页：JoinApply.vue（包含入网申请表单、审核状态、实名认证、分账等部分）
- 状态组件：VerifyStatus.vue（渲染所有状态页面）
- 入网表单：ApplyForm.vue

#### 1. 组织认证阶段

- **未认证/已过期**：需先完成组织认证，弹出组织认证弹窗。
- **认证中**：弹出认证中弹窗。
- **认证失败**：弹出认证失败弹窗。
- **已认证**：进入商户入网阶段。

#### 2. 商户入网阶段

- **未入网**：
  - 显示"需先开通商户入网以及商户分账业务后，才可进行交易收款"，显示"申请"按钮。
  - 点击"申请"后，若组织已认证，则新开 Tab 进入商户入网申请页面。
  - 填写商户信息，提交后进入电子合同签约。
  - 电子合同签约后，进入审核中。
- **审核中**：
  - 页面提示"审核人员会在 1~3 工作日内完成审核，审核结果会通过'小秘书'进行通知"。
  - 可点击刷新按钮重新获取状态。
- **审核失败**：
  - 显示失败原因。
  - 可点击"去修改"进入入网信息修改页面，回显原内容，重新提交后重新走电子合同签约和审核流程。
- **审核通过**：
  - 显示审核通过页面，回显申请内容。
  - 下一步进入商户实名。

#### 3. 商户实名阶段

- **未实名**：
  - 显示微信、支付宝实名认证二维码，认证标识均为"未认证"。
  - "我已核实商户号"按钮：点击后查询实名状态，未实名则弹 toast，已实名则状态变为"已实名"且按钮置灰。
  - 商户号为入网成功后获得的拉卡拉商户号。
  - 下一步需微信、支付宝均实名认证后才可进入分账业务。
- **已实名**：
  - 显示"已实名"，按钮不可点击。

#### 4. 分账业务阶段

- **未开通**：
  - 商户实名后，进入分账业务开通页面。
  - 提交后进入分账审核中。
- **审核中**：
  - 等待拉卡拉人工审核。
- **审核失败**：
  - 显示失败原因，可重新提交。
- **审核通过/已开通**：
  - 流程结束，显示入网成功信息。

---

### 状态流转节点与条件（参考 useMerchantFlow.ts）

| 阶段         | 关键状态字段                  | 状态值 | 说明                         | 下一步流转                |
|--------------|-------------------------------|--------|------------------------------|---------------------------|
| 组织认证     | orgAuthDetail.auth            | 0      | 未认证/已过期                | 弹出组织认证弹窗          |
|              |                               | 1      | 已认证                      | 进入商户入网              |
| 商户入网     | merchantStore.isExist         | false  | 未入网                      | 进入入网申请              |
|              | status.is_open_merchant | 0 | 未入网信息                  | 填写入网信息              |
|              | status.is_open_merchant | 1 | 入网审核中                  | 等待审核                  |
|              | status.is_open_merchant | 2 | 已入网                      | 进入签约/实名             |
|              | status.is_open_merchant | 3 | 入网失败                    | 重新提交                  |
| 签约         | status.apply_status | 1  | 待签约                      | 跳转签约                  |
|              | status.apply_status | 2  | 签约成功                    | 进入实名                  |
|              | status.apply_status | 3  | 入网失败                    | 重新提交                  |
| 商户实名     | status.is_alipay_merchant<br>status.is_wechat_merchant | 1/1 | 已实名 | 进入分账业务 |
|              |                               | 0/0    | 未实名                      | 跳转实名                  |
|              |                               | 1/0 或 0/1 | 部分实名                  | 实名审核中                |
| 分账业务     | status.is_separate_accounts | 0 | 未分账（实名后）           | 分账审核中                |
|              | status.is_separate_accounts | 1 | 分账审核中                  | 等待审核                  |
|              | status.is_separate_accounts | 2 | 分账已开通                  | 流程结束                  |
|              | status.is_separate_accounts | 3 | 分账审核失败                | 重新提交                  |

> **优先级顺序：分账 > 实名 > 签约 > 入网 > 成功**  
> 其中，分账状态优先于实名、签约、入网等其他节点。

---

### Mermaid 流程图（对齐 useMerchantFlow.ts 最新实现）

```mermaid
sequenceDiagram
  participant 用户
  participant 业务中台
  participant 拉卡拉

  用户->>业务中台: 访问收款入口
  业务中台->>业务中台: 校验组织认证状态
  alt 未认证/已过期
    业务中台-->>用户: 显示认证弹窗
    用户->>业务中台: 提交认证材料
    业务中台->>业务中台: 完成认证
  end

  业务中台->>拉卡拉: 发起商户入网
  alt 首次入网
    拉卡拉-->>用户: 返回入网表单
    用户->>拉卡拉: 提交入网信息
    拉卡拉->>拉卡拉: 生成电子合同
    拉卡拉-->>用户: 跳转签约
    用户->>拉卡拉: 完成签约
  else 审核失败
    拉卡拉->>业务中台: 返回失败原因
    业务中台-->>用户: 重新提交入网信息
  end

  拉卡拉->>拉卡拉: 执行实名认证
  alt 未完成实名
    拉卡拉-->>用户: 展示双渠道二维码
    用户->>拉卡拉: 完成微信认证
    用户->>拉卡拉: 完成支付宝认证
  end

  拉卡拉->>拉卡拉: 发起分账审核
  alt 审核通过
    拉卡拉->>业务中台: 通知流程完成
    业务中台-->>用户: 显示入网成功
  else 审核失败
    拉卡拉->>业务中台: 返回失败原因
    业务中台-->>用户: 重新提交申请
  end
```

---

### 详细流程节点说明（补充 PRD 细节）

- 所有状态页面均由 VerifyStatus.vue 渲染，页面跳转均为新 Tab。
- 电子合同签约状态通过 getApplyStatus 查询，apply_status=1 为待签约，2 为签约成功，3 为签约失败。
- 商户实名需微信、支付宝均认证通过。
- 分账业务审核失败可重新提交，成功后流程结束。
- 极端情况：若页面状态已变更（如分账业务已开通），需刷新页面显示最新信息。
- 所有失败原因均需回显，toast 提示需与 PRD 保持一致。

---

### 备注

- 具体接口申明见 src/renderer/api/workBench/merchant.ts
- TypeScript 数据模型见 src/renderer/api/workBench/merchant.model.ts
- 主要页面：
  - src/renderer/views/workBench/merchant/SettlementJoin.vue
  - src/renderer/views/workBench/merchant/JoinApply.vue
    - src/renderer/views/workBench/merchant/components/ApplyForm.vue
    - src/renderer/views/workBench/merchant/components/VerifyStatus.vue
- "小秘书"消息由后端处理，无需前端处理
- 详细交互、极端情况、页面跳转等请参考 PRD 文档

---

## 提现管理 PRD 文档

### 业务流程

提现账户的开通与展示依赖于商户入网状态，整体流程与商户入网流程保持一致：

1. **未入网**：
   - 提现账户页面显示"没有账户"，并显示"申请"按钮。
   - 点击"申请"后，判断组织认证情况：
     - 未认证/已过期：侧边弹出组织认证弹窗。
     - 认证中：侧边弹出认证中弹窗。
     - 认证失败：侧边弹出认证失败弹窗。
     - 已认证：判断是否已商户入网。
   - 若未入网，则新开 Tab 进入商户入网页面。
2. **已入网**：
   - 若已入网，弹出 toast 提示"当前组织已成功入网"，并刷新页面显示提现账户信息。

### 备注

- 提现账户的入网状态判断逻辑与 SettlementJoin.vue 保持一致。
- 组织认证、商户入网等弹窗交互与主流程一致。
- 具体页面与组件可根据实际业务需求复用商户入网相关实现。

---

## 前端调试工具说明

支持在开发环境下通过可视化调试面板一键切换商户入网流程各节点，便于前端独立调试和联调。调试功能仅在开发环境下生效，生产环境无效。

### 调试面板功能

#### 1. 启动调试面板

- **快捷键**：`Ctrl+D` (Windows/Linux) 或 `Cmd+D` (macOS)
- **URL 参数**：在地址栏添加 `?debug=true`
- **场景切换**：添加 `&scene=场景名称` 直接跳转到指定场景

#### 2. 面板功能

- **流程类型切换**：支持商户入网和入网分佣两种流程
- **场景一键切换**：下拉选择预设场景，自动应用对应状态
- **步骤精确控制**：手动控制当前流程步骤
- **状态实时显示**：查看当前流程状态和步骤信息
- **链接分享**：生成包含当前调试状态的 URL 链接
- **存储管理**：自动保存调试状态，支持状态重置

#### 3. 技术架构

```
JoinApply.vue (主页面)
├── useDebug.ts (调试状态管理)
├── DebugPanel.vue (调试面板 UI)
├── useFlowState.ts (流程状态管理)
└── debugHelpers.ts (调试工具函数)
```

### 可用调试场景

#### 商户入网流程

- **初始状态**：未开始入网申请
- **填写信息**：正在填写入网信息
- **等待签约**：信息已提交，等待电子合同签约
- **审核中**：已签约，等待拉卡拉审核
- **审核失败**：入网审核被驳回
- **等待实名**：入网成功，等待实名认证
- **部分实名**：微信或支付宝其中一个已认证
- **等待分账**：实名完成，等待分账业务开通
- **分账审核中**：分账申请已提交，等待审核
- **分账失败**：分账申请被驳回
- **流程完成**：所有步骤已完成

#### 入网分佣流程

- **初始状态**：分佣流程起始状态
- **审核中**：分佣申请审核中
- **流程完成**：分佣流程已完成

### 使用方法

#### 1. 基础使用

```typescript
// 页面中集成调试工具
import { useDebug } from './hooks/useDebug';
import { useFlowState } from './hooks/useFlowState';

const flowState = useFlowState();
const debugState = useDebug(flowState);

// 传递给调试面板
<DebugPanel v-bind="debugState" />
```

#### 2. URL 调试链接

```bash
# 直接切换到指定场景
http://localhost:9080/#/workBenchIndex/merchant-settlement-apply?debug=true&scene=审核中

# 切换到分佣流程场景
http://localhost:9080/#/workBenchIndex/merchant-settlement-apply?isCommission=true&debug=true&scene=分佣审核中
```

#### 3. 快速切换场景

1. 按 `Ctrl+D` 打开调试面板
2. 选择目标场景
3. 系统自动应用对应的流程状态
4. 页面立即更新为新状态

### 开发扩展

#### 添加新场景

在 `debugHelpers.ts` 中扩展场景配置：

```typescript
export const normalFlowScenes = {
  // 现有场景...
  '新场景名称': {
    step: 2,
    flowControl: [
      { status: 'success' as StatusType, isShowVerifyStatus: false },
      { status: 'success' as StatusType, isShowVerifyStatus: false },
      { status: 'processing' as StatusType, isShowVerifyStatus: true },
      { status: 'default' as StatusType, isShowVerifyStatus: false },
    ],
  },
} as const;
```

### 注意事项

- ✅ 调试功能仅在开发环境 (`NODE_ENV !== 'production'`) 下生效
- ✅ 调试状态会自动保存到本地存储，页面刷新后恢复
- ✅ URL 参数优先级高于本地存储
- ✅ 调试面板支持拖拽，位置会自动保存
- ✅ 所有状态变更都是实时的，无需页面刷新
- ⚠️ 生产环境下所有调试代码都会被自动禁用
- ⚠️ 调试状态不会影响真实的后端数据
