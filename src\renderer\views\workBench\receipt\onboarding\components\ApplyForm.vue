<script setup lang="ts">
import { ref, computed, watch, nextTick, reactive } from 'vue';
import { FormRule, MessagePlugin } from 'tdesign-vue-next';
import { storeToRefs } from 'pinia';
import { useRoute } from 'vue-router';
import { saveMerchantDraft, getMerchantDraftDetail } from '@renderer/api/workBench/merchant';
import BaiduMapSelector from '@renderer/components/common/map/BaiduMapSelector.vue';
import { MerchantFormData } from '@renderer/api/workBench/merchant.model';
import { onMountedOrActivated } from '@renderer/hooks/onMountedOrActivated';
import { useMerchantStore } from '@renderer/store/modules/merchant';
import to from 'await-to-js';
import LynkerSDK from '@renderer/_jssdk';
import { getBaseShopInfo } from '@renderer/api/workBench/commission';
import { RUploadImage } from '@rk/unitPark';
import { isValidPhone } from '@renderer/utils/validator';
import { useLicenseOCR } from '@renderer/hooks/useLicenseOCR';
import { useBusinessContent, useMerchantCategory, useMerchantRequest, useTeamAuthStatus, useBankList } from '../hooks/useMerchant';
import { useComponentComm } from '../hooks/useComponentComm';

const props = defineProps<{
  /** 是否重新提交 */
  isResubmit: boolean;
}>();

const emit = defineEmits(['submit', 'close']);

const UPLOAD_FACE_EXAMPLE = 'http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/workbech/receipt/upload_face_example.png';
const UPLOAD_ENV_EXAMPLE = 'http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/workbech/receipt/upload_env_example.png';
const ID_CARD_FRONT = 'https://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/common/id_card_front.png';
const ID_CARD_BACK = 'https://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/common/id_card_back.png';
const BANK_PIC = 'https://kuaiyouyi.oss-cn-shenzhen.aliyuncs.com/square/28d7792/bank_pic.png';
const LONG_TIME = '9999-12-31';

const route = useRoute();
const teamId = computed(() => route.query.teamId as string);

const merchantStore = useMerchantStore();
const { status: merchantStatus } = storeToRefs(merchantStore);
const { createMerchantInfo } = useMerchantRequest();

// 如果重新提交，需传入旧的商户ID
const oldMerchantId = computed(() => {
  if (props.isResubmit && merchantStore.info.merchant_id) {
    return String(merchantStore.info.merchant_id);
  }
  return '';
});

// 使用统一组件通信机制
const { createComponentInterface } = useComponentComm({
  step: computed(() => 0), // ApplyForm固定在步骤0
  flowControl: computed(() => ({})),
  isCommission: computed(() => false),
  setFlowStatus: () => { /* 空实现 */ },
  updateFlowStatus: () => { /* 空实现 */ },
});

// 创建组件接口
const componentInterface = createComponentInterface('ApplyForm');

const { fetchMerchantCategory, categoryOptions } = useMerchantCategory();
const { fetchBusinessContent, contentOptions } = useBusinessContent();
const { bankList, loading: bankListLoading, fetchBankList } = useBankList();

// 组织认证
const { orgAuthDetail, fetchTeamAuthStatus } = useTeamAuthStatus();

const formRef = ref(null);
// 本地变量用于控制长期选项
const isLongTerm = ref(false);
// 组织信息长期有效性
const isOrgLicenseLongTerm = ref(false);

// 是否展开
const sectionOpen = reactive({
  merchant: true,
  legal: true,
  pay: true,
  org: true,
});

// 表单数据模型
const formData = ref<MerchantFormData>({
  business_content: '',
  business_content_name: '',
  email: '',
  legal_person_id_card: '',
  legal_person_id_card_end_time: '',
  legal_person_id_card_img: [],
  legal_person_id_card_img_tow: [],
  legal_person_id_card_start_time: '',
  legal_person_name: '',
  merchant_address: '',
  merchant_id: 0,
  // 商户类别：表单提交用的是此字段
  merchant_code: '',
  // 商户类别：详情返回是这此字段
  merchant_mcc_code: '',
  merchant_mcc_code_name: '',
  merchant_name: '',
  pay_merchant_context_diagram_img: [],
  pay_merchant_legal_person_bank_card_bank_name: '',
  pay_merchant_legal_person_bank_card_number: '',
  pay_merchant_legal_person_bank_card_open_bank_name: '',
  pay_merchant_legal_person_bank_card_open_bank_number: '',
  pay_merchant_legal_person_bank_img: [],
  // 这个字段没用，这里写死的，不然后端会报错：银行卡反面照片不能为空
  pay_merchant_legal_person_bank_img_tow: [{ file_name: 'square/28d7792/bank_pic', file_suffix: 'png', file_url: BANK_PIC }],
  pay_merchant_legal_person_bank_mobile: '',
  pay_merchant_legal_person_bank_type: 1,
  pay_merchant_store_img: [],
  team_id: '',
  old_merchant_id: undefined,
  clear_bank_code: '',
  merchant_team_img: [],
  merchant_team_name: '',
  credit_code: '',
  start_time: '',
  end_time: '',
} as MerchantFormData);

// 表单校验规则
const rules: Record<string, FormRule[]> = {
  merchant_name: [{ required: true, message: '商户名称必填' }],
  merchant_address: [{ required: true, message: '商户地址必填' }],
  merchant_code: [{ required: true, message: '请选择商户类别' }],
  email: [
    { required: true, message: '邮箱必填' },
    { email: { ignore_max_length: true }, message: '请输入正确的邮箱' },
  ],
  business_content: [{ required: true, message: '请选择经营内容' }],
  pay_merchant_store_img: [{ required: true, message: '门脸图必传' }],
  pay_merchant_context_diagram_img: [{ required: true, message: '环境图必传' }],
  legal_person_id_card_img: [{ required: true, message: '法人身份证照片必传' }],
  legal_person_name: [{ required: true, message: '法人姓名必填' }],
  legal_person_id_card: [{ required: true, message: '法人身份证号码必填' }],
  legal_person_id_card_start_time: [{
    validator: (val) => {
      if (!isLongTerm.value && !val) {
        return { result: false, message: '开始日期必选', type: 'error' };
      }
      return { result: true, message: '', type: 'success' };
    },
  }],
  legal_person_id_card_end_time: [{
    validator: (val) => {
      if (!isLongTerm.value && !val) {
        return { result: false, message: '结束日期必选', type: 'error' };
      }
      if (val && formData.value.legal_person_id_card_start_time) {
        const startTimestamp = new Date(formData.value.legal_person_id_card_start_time).getTime();
        const endTimestamp = new Date(val).getTime();
        if (endTimestamp < startTimestamp) {
          return { result: false, message: '结束日期不能小于开始日期', type: 'error' };
        }
      }
      return { result: true, message: '', type: 'success' };
    },
  }],
  pay_merchant_legal_person_bank_img: [{ required: true, message: '结算银行卡照片必传' }],
  pay_merchant_legal_person_bank_card_open_bank_number: [{ required: true, message: '开户行号必填' }],
  pay_merchant_legal_person_bank_card_open_bank_name: [{ required: true, message: '开户行名称必填' }],
  pay_merchant_legal_person_bank_card_number: [
    { required: true, message: '法人银行卡号必填' },
    { pattern: /^\d+$/, message: '法人银行卡号只能输入数字' },
  ],
  pay_merchant_legal_person_bank_card_bank_name: [{ required: true, message: '法人银行卡名称必填' }],
  pay_merchant_legal_person_bank_mobile: [
    { required: true, message: '法人手机号码必填' },
    { validator: (val) => isValidPhone(val), message: '请输入有效的手机号' },
  ],
  merchant_team_img: [{ required: true, message: '图片必传' }],
  merchant_team_name: [{ required: true, message: '营业执照名称必填' }],
  credit_code: [
    { required: true, message: '统一社会信用代码/注册号必填' },
    { max: 18, message: '最多输入18位' },
    { pattern: /^[A-Za-z0-9]+$/, message: '只能输入数字和英文' },
  ],
  start_time: [{
    validator: (val) => {
      if (!isOrgLicenseLongTerm.value && !val) {
        return { result: false, message: '开始日期必选', type: 'error' };
      }
      return { result: true, message: '', type: 'success' };
    },
  }],
  end_time: [{
    validator: (val) => {
      if (!isOrgLicenseLongTerm.value && !val) {
        return { result: false, message: '结束日期必选', type: 'error' };
      }
      if (val && formData.value.start_time) {
        const startTimestamp = new Date(formData.value.start_time).getTime();
        const endTimestamp = new Date(val).getTime();
        if (endTimestamp < startTimestamp) {
          return { result: false, message: '结束日期不能小于开始日期', type: 'error' };
        }
      }
      return { result: true, message: '', type: 'success' };
    },
  }],
};

// 地图选择器
const mapVisible = ref(false);
const mapConfirm = (data) => {
  formData.value.merchant_address = data.addressOnly || data.address;
};

const loading = ref(false);
const submitLoading = ref(false);

// 获取草稿
const getMerchantDraft = async () => {
  const [err, res] = await to(getMerchantDraftDetail(teamId.value));
  if (err) {
    MessagePlugin.error((err as any).response.data.message);
    return;
  }

  if (res.data?.data?.detail_info) {
    formData.value = {
      ...formData.value,
      ...res.data.data.detail_info,
    };
  }
};

// 回显表单数据
const echoFormData = async () => {
  await merchantStore.getDetail();
  if (merchantStore.info) {
    formData.value = merchantStore.info;
  }

  await getMerchantDraft();

  // 接口提交字段和详情返回字段不一致，需要做兼容
  formData.value.merchant_code = formData.value.merchant_code || formData.value.merchant_mcc_code;

  // 如果结束时间为 9999-12-31，则设置长期有效
  if (formData.value.legal_person_id_card_end_time === LONG_TIME) {
    isLongTerm.value = true;
  }

  // 如果营业执照结束时间为 9999-12-31，则设置长期有效
  if (formData.value.end_time === LONG_TIME) {
    isOrgLicenseLongTerm.value = true;
  }
};

// 设置初始值
const setInitValue = () => {
  // 首次入网时，需要传入组织认证信息
  formData.value.merchant_name = formData.value.merchant_name || orgAuthDetail.value.fullname;
  formData.value.legal_person_name = formData.value.legal_person_name || orgAuthDetail.value.contact?.name;

  // 重新入网时，需要传入旧的商户ID
  formData.value.old_merchant_id = oldMerchantId.value ? Number(oldMerchantId.value) : undefined;
};

const mapImageData = (url: string) => {
  const fileName = url.split('/').pop() || '';
  return {
    file_name: fileName,
    file_suffix: fileName.split('.').pop() || '',
    file_url: url,
  };
};

// 获取店铺基本资料
const getShopInfo = async () => {
  const [err, res] = await to(getBaseShopInfo(teamId.value));
  if (err) return;

  if (res.data?.data) {
    const { shop_name, shop_address, shop_store_img, shop_context_diagram_img } = res.data.data;
    formData.value = {
      ...formData.value,
      merchant_name: shop_name || formData.value.merchant_name,
      merchant_address: (shop_address as any)?.address || formData.value.merchant_address,
      pay_merchant_store_img: shop_store_img?.length ? shop_store_img.map(mapImageData) : formData.value.pay_merchant_store_img,
      pay_merchant_context_diagram_img: shop_context_diagram_img?.length ? shop_context_diagram_img.map(mapImageData) : formData.value.pay_merchant_context_diagram_img,
    };
  }
};

const init = async () => {
  loading.value = true;

  try {
    await Promise.all([
      merchantStore.isExist && merchantStore.getStatus(),
      echoFormData(),
      fetchMerchantCategory(),
      fetchBusinessContent(),
      fetchBankList(),
      fetchTeamAuthStatus(),
      getShopInfo(),
    ]);

    setInitValue();
  } finally {
    loading.value = false;

    await nextTick();
    setTimeout(() => {
      formRef.value?.clearValidate();
    }, 100);
  }
};

onMountedOrActivated(() => {
  if (route.query.teamId) {
    merchantStore.setTeamId(route.query.teamId as string);
  }

  init();
});

// 添加计算属性来处理图片数据
const createImageUrlComputed = (fieldName: string) => computed({
  get: () => {
    const fieldValue = formData.value[fieldName];
    if (!fieldValue) return [];
    if (Array.isArray(fieldValue)) {
      return fieldValue.map((img) => img.file_url);
    }
    return [];
  },
  set: (urls: string[]) => {
    // 处理删除所有图片的情况
    if (!urls || urls.length === 0) {
      formData.value[fieldName] = [];
      return;
    }

    formData.value[fieldName] = urls.map(mapImageData);
  },
});

// 使用工厂函数创建所有图片相关的计算属性
const storeImgUrls = createImageUrlComputed('pay_merchant_store_img');
const contextDiagramImgUrls = createImageUrlComputed('pay_merchant_context_diagram_img');
const legalPersonIdCardImgUrls = createImageUrlComputed('legal_person_id_card_img');
const legalPersonIdCardImgTowUrls = createImageUrlComputed('legal_person_id_card_img_tow');
const legalPersonBankImgUrls = createImageUrlComputed('pay_merchant_legal_person_bank_img');
const orgLicenseImgUrls = createImageUrlComputed('merchant_team_img');

// 控制长期按钮是否禁用
const isOrgLicenseLongTermDisabled = computed(() => !formData.value.start_time);

// 用于标记是否正在程序化设置长期选项，避免监听器冲突
let isSettingLongTerm = false;
let isSettingOrgLongTerm = false;

// 监听营业执照长期有效性变化
watch(isOrgLicenseLongTerm, (newVal) => {
  if (newVal) {
    isSettingOrgLongTerm = true;
    // 选择长期时，清空结束时间并清除验证
    formData.value.end_time = '';
    nextTick(() => {
      formRef.value?.clearValidate(['end_time']);
      isSettingOrgLongTerm = false;
    });
  }
});

// 监听营业执照日期变化
watch([
  () => formData.value.start_time,
  () => formData.value.end_time,
], ([_startTime, endTime]) => {
  // 避免在程序化设置时触发
  if (isSettingOrgLongTerm) return;

  // 当手动选择结束日期时，取消长期选项
  if (endTime && isOrgLicenseLongTerm.value) {
    isOrgLicenseLongTerm.value = false;
  }
});

// 监听法人身份证长期有效性的变化
watch(isLongTerm, (newVal) => {
  if (newVal) {
    isSettingLongTerm = true;
    // 选择长期时，清空日期并清除验证
    formData.value.legal_person_id_card_end_time = '';
    nextTick(() => {
      formRef.value?.clearValidate(['legal_person_id_card_end_time']);
      isSettingLongTerm = false;
    });
  }
});

// 控制长期按钮是否禁用
const isLongTermDisabled = computed(() => !formData.value.legal_person_id_card_start_time);

// 监听法人身份证日期变化
watch(
  [
    () => formData.value.legal_person_id_card_start_time,
    () => formData.value.legal_person_id_card_end_time,
  ],
  ([_startTime, endTime]) => {
    // 避免在程序化设置时触发
    if (isSettingLongTerm) return;

    // 当手动选择结束日期时，取消长期选项
    if (endTime && isLongTerm.value) {
      isLongTerm.value = false;
    }
  },
);

// 开户行名称选择
watch(() => formData.value.pay_merchant_legal_person_bank_card_open_bank_name, (newVal, oldVal) => {
  if (newVal === oldVal) return;

  const selectedBank = bankList.value.find((item) => item.value === newVal);
  if (selectedBank?.item) {
    formData.value.clear_bank_code = selectedBank.item.clear_code;
    formData.value.pay_merchant_legal_person_bank_card_open_bank_number = selectedBank.item.open_code;
  }
});

// 获取提交数据
const getSubmitData = () => ({
  ...formData.value,
  // 如果是长期有效，设置结束时间为 9999-12-31
  legal_person_id_card_end_time: isLongTerm.value ? LONG_TIME : formData.value.legal_person_id_card_end_time,
  // 如果营业执照是长期有效，设置结束时间为 9999-12-31
  end_time: isOrgLicenseLongTerm.value ? LONG_TIME : formData.value.end_time,
});

// 保存（草稿）
const handleSubmit = async () => {
  try {
    submitLoading.value = true;
    await saveMerchantDraft(getSubmitData(), teamId.value);
    MessagePlugin.success('保存成功');

    // 使用统一通信机制报告成功
    componentInterface.emitFormEvent('success', {
      type: 'draft-saved',
      message: '草稿保存成功',
    });

    return true;
  } catch (err) {
    const errorMessage = (err as any).response?.data?.message || '保存失败';
    MessagePlugin.error(errorMessage);

    // 使用统一通信机制报告错误
    componentInterface.reportError({
      code: 'DRAFT_SAVE_ERROR',
      message: errorMessage,
      context: { error: err },
      level: 'error',
    });

    return false;
  } finally {
    submitLoading.value = false;
  }
};

// 下一步
const handleNextStep = async () => {
  const valid = await formRef.value.validate();
  if (typeof valid !== 'boolean') return;

  try {
    submitLoading.value = true;
    await createMerchantInfo(getSubmitData());
    MessagePlugin.success('保存成功');

    // 使用统一通信机制报告成功
    componentInterface.emitFormEvent('success', {
      type: 'merchant-created',
      message: '商户信息提交成功',
    });

    // 成功：跳转至【电子合同确认】页面
    emit('submit', true);
  } catch (err) {
    const errorMessage = (err as any).response?.data?.message || '提交失败';
    // MessagePlugin.error(errorMessage);

    // 使用统一通信机制报告错误
    componentInterface.reportError({
      code: 'MERCHANT_SUBMIT_ERROR',
      message: errorMessage,
      context: { error: err },
      level: 'error',
    });

    // 失败：刷新页面
    emit('submit', false);
  } finally {
    submitLoading.value = false;
  }
};

// 预览图片
const previewImg = (url: string, _title?: string) => {
  LynkerSDK.previewImage({
    images: [url],
    index: 0,
    url,
  });
};

// 上传图片预览
const uploadPreviewImg = ({ images, index, url }) => {
  LynkerSDK.previewImage({
    images,
    index,
    url,
  });
};

const { ocrLoading, handleOcrForLicense } = useLicenseOCR();

// 营业执照图 OCR 识别
const handleOrgLicenseImgChange = async (urls: string[]) => {
  if (orgAuthDetail.value?.orgType !== 'GOVERNMENT') return;

  await handleOcrForLicense(
    teamId.value,
    urls?.[0],
    (info) => {
      formData.value.merchant_team_name = info.team_name;
      formData.value.credit_code = info.code;

      // 判断是否长期有效（无有效期或标记为长期）
      if (!info.validity_end || info.no_expire) {
        // formData.value.end_time = LONG_TIME;
        isOrgLicenseLongTerm.value = true;
      } else {
        formData.value.start_time = info.validity_start || '';
        formData.value.end_time = info.validity_end || '';
        isOrgLicenseLongTerm.value = false;
      }
    },
  );
};
</script>

<template>
  <div v-loading="ocrLoading.loading ? ocrLoading : loading" class="form-container">
    <t-form
      ref="formRef"
      class="form-content"
      :data="formData"
      :rules="rules"
      label-align="top"
      scroll-to-first-error="smooth"
    >
      <!-- 商户信息 -->
      <div class="form-card no-top-border-radius" :class="{collapsed: !sectionOpen.merchant}">
        <h3 class="form-title" @click="sectionOpen.merchant = !sectionOpen.merchant">
          商户信息
          <iconpark-icon name="iconarrowup-a960jjb9" :class="['icon-rotate', {collapsed: !sectionOpen.merchant}]" />
        </h3>
        <Transition name="collapse">
          <div v-show="sectionOpen.merchant">
            <template v-if="merchantStore.isExist">
              <t-alert
                v-if="merchantStatus?.is_open_merchant === '3'"
                theme="warning"
                :message="merchantStatus?.message"
                class="mb-16"
              />
              <t-alert
                v-else-if="merchantStatus?.is_open_merchant === '0'"
                theme="info"
                message="商户开通入网后，可进行线上交易行为（如线上销售、收款等）"
                class="mb-16"
              />
            </template>

            <t-form-item required-mask label="商户名称" name="merchant_name">
              <t-input v-model="formData.merchant_name" placeholder="请输入商户名称" />
            </t-form-item>

            <t-form-item required-mask label="商户地址" name="merchant_address">
              <div class="w-full flex gap-8">
                <div class="textarea-wrap">
                  <t-textarea
                    v-model="formData.merchant_address"
                    placeholder="请输入商户地址"
                    :maxlength="200"
                    show-limit-number
                    class="flex-1"
                    :autosize="{ minRows: 1 }"
                  />
                  <div class="t-textarea__limit">{{ formData.merchant_address?.length || 0 }}/200</div>
                </div>

                <t-button variant="outline" theme="primary" @click="mapVisible = true">
                  <template #icon><iconpark-icon name="iconorientation" class="text-20 mr-4" /></template>选择地址
                </t-button>
              </div>
            </t-form-item>

            <t-form-item required-mask label="商户类别" name="merchant_code">
              <t-select
                v-model="formData.merchant_code"
                :options="categoryOptions"
                placeholder="请选择商户类别"
                filterable
                class="w-full"
              />
            </t-form-item>

            <t-form-item required-mask label="经营内容" name="business_content">
              <t-select
                v-model="formData.business_content"
                :options="contentOptions"
                placeholder="请选择经营内容"
                filterable
                class="w-full"
              />
            </t-form-item>

            <div class="upload-fields">
              <t-form-item required-mask name="pay_merchant_store_img">
                <template #label>
                  门脸图（{{ formData.pay_merchant_store_img?.length || 0 }}/1）
                  <t-tooltip placement="top">
                    <t-icon name="help-circle" class="text-16 color-text-3 cursor-pointer" />
                    <template #content>
                      <p>
                        1、【实体店铺】需上传包含完整的门店牌匾、门框和入口，建议距离门店2米外拍摄，若门店正前方有遮挡物，可选择从侧面拍摄
                      </p>
                      <p>2、【线上店铺】需上传包含完整的工作点门店牌匾、门框和入口</p>
                      <p>
                        3、门脸图拍摄时门店必须为营业状态，不可关闭卷帘铁门，不可标示"暂不营业""正在装修""转让""关闭"等信息
                      </p>
                    </template>
                  </t-tooltip>
                </template>

                <RUploadImage v-model="storeImgUrls" root-dir="workBench" @click="uploadPreviewImg" />

                <div class="divider-h" />
                <div class="img-preview">
                  <t-image
                    class="img"
                    :src="UPLOAD_FACE_EXAMPLE"
                    lazy
                    @click="previewImg(UPLOAD_FACE_EXAMPLE, '门脸图')"
                  />
                  <div class="example-tag">示例</div>
                </div>
              </t-form-item>

              <t-form-item required-mask name="pay_merchant_context_diagram_img">
                <template #label>
                  环境图（{{ formData.pay_merchant_context_diagram_img?.length || 0 }}/3）
                  <t-tooltip placement="top">
                    <t-icon name="help-circle" class="text-16 color-text-3 cursor-pointer" />
                    <template #content>
                      <p>1、【实体店铺】需真实反映店内经营环境，拍摄整体经营区域，请勿局部拍摄货架、收银、桌椅等</p>
                      <p>2、 【线上店铺】需真实反映工作点经营环境</p>
                      <p>3、门店经营环境干净整洁，不可有杂物、垃圾或者清晰可见的污垢</p>
                    </template>
                  </t-tooltip>
                </template>

                <RUploadImage
                  v-model="contextDiagramImgUrls"
                  :max-count="3"
                  root-dir="workBench"
                  @click="uploadPreviewImg"
                />

                <div class="divider-h" />

                <div class="img-preview">
                  <t-image
                    class="img"
                    :src="UPLOAD_ENV_EXAMPLE"
                    lazy
                    @click="previewImg(UPLOAD_ENV_EXAMPLE, '环境图')"
                  />
                  <div class="example-tag">示例</div>
                </div>
              </t-form-item>
            </div>
          </div>
        </Transition>
      </div>

      <!-- 组织信息 -->
      <div v-if="orgAuthDetail?.orgType === 'GOVERNMENT'" class="form-card" :class="{collapsed: !sectionOpen.org}">
        <h3 class="form-title" @click="sectionOpen.org = !sectionOpen.org">
          <div class="flex items-center gap-4">
            组织信息
            <t-tooltip placement="top">
              <t-icon name="help-circle" class="text-16 color-text-3 cursor-pointer" />
              <template #content>
                政府单位入网可填写其他代运营组织营业信息（包括商户组织、营业执照、法人、银行收款、微信支付宝实名等均需同一主体）进行入网，后续以入网主体进行交易结算
              </template>
            </t-tooltip>
          </div>
          <iconpark-icon name="iconarrowup-a960jjb9" :class="['icon-rotate', {collapsed: !sectionOpen.org}]" />
        </h3>
        <Transition name="collapse">
          <div v-show="sectionOpen.org">
            <t-form-item required-mask name="merchant_team_img" label="营业执照">
              <div class="flex items-center gap-16">
                <RUploadImage
                  v-model="orgLicenseImgUrls"
                  root-dir="workBench"
                  :max-count="1"
                  :size="{ width: 192, height: 136, iconSize: 48, fontSize: 14 }"
                  @change="handleOrgLicenseImgChange"
                  @click="uploadPreviewImg"
                />
                <div class="text-12 text-[#ACB3C0]">最多上传1张jpeg、jpg、png文件格式，5M以内</div>
              </div>
            </t-form-item>
            <t-form-item required-mask name="merchant_team_name">
              <template #label>
                营业执照名称
                <t-tooltip placement="top">
                  <t-icon name="help-circle" class="text-16 color-text-3 cursor-pointer" />
                  <template #content>即对应组织名称</template>
                </t-tooltip>
              </template>
              <t-input v-model="formData.merchant_team_name" placeholder="请输入营业执照名称" :maxlength="50" />
            </t-form-item>
            <t-form-item required-mask name="credit_code" label="统一社会信用代码/注册号">
              <t-input v-model="formData.credit_code" placeholder="请输入统一社会信用代码/注册号" :maxlength="18" />
            </t-form-item>
            <t-form-item>
              <template #label>
                <div class="t-form__label t-form__label--required t-form__label--top"><label>营业执照有效期</label></div>
              </template>
              <div class="flex gap-8 w-full items-center">
                <t-form-item name="start_time" class="mr-0!">
                  <t-date-picker
                    v-model="formData.start_time"
                    placeholder="开始日期"
                    class="flex-1"
                    clearable
                  />
                </t-form-item>
                <span class="text-[#828DA5]">-</span>
                <t-form-item name="end_time">
                  <t-date-picker
                    v-model="formData.end_time"
                    placeholder="结束日期"
                    class="flex-1"
                    clearable
                    :disabled="isOrgLicenseLongTerm"
                  />
                </t-form-item>
                <span class="text-[#D5DBE4] mx-8">|</span>
                <t-radio
                  v-model="isOrgLicenseLongTerm"
                  allow-uncheck
                  :disabled="isOrgLicenseLongTermDisabled"
                >
                  长期
                </t-radio>
              </div>
            </t-form-item>
          </div>
        </Transition>
      </div>

      <!-- 法人信息 -->
      <div class="form-card" :class="{collapsed: !sectionOpen.legal}">
        <h3 class="form-title" @click="sectionOpen.legal = !sectionOpen.legal">
          法人信息
          <iconpark-icon name="iconarrowup-a960jjb9" :class="['icon-rotate', {collapsed: !sectionOpen.legal}]" />
        </h3>
        <Transition name="collapse">
          <div v-show="sectionOpen.legal">
            <t-form-item label="法人身份证照片" name="legal_person_id_card_img" class="id-card-form-item">
              <RUploadImage
                v-model="legalPersonIdCardImgUrls"
                root-dir="workBench"
                :size="{ width: 140, height: 88 }"
                @click="uploadPreviewImg"
              >
                <div class="id-card-upload">
                  <t-image
                    :src="ID_CARD_FRONT"
                    class="img"
                    fit="cover"
                    lazy
                    @click="uploadPreviewImg"
                  />
                  <div class="tip-wrap">
                    <iconpark-icon name="iconimg" class="icon" />
                    <p>上传人像面</p>
                  </div>
                </div>
              </RUploadImage>

              <RUploadImage
                v-model="legalPersonIdCardImgTowUrls"
                root-dir="workBench"
                :size="{ width: 140, height: 88 }"
                @click="uploadPreviewImg"
              >
                <div class="id-card-upload ml-8">
                  <t-image
                    :src="ID_CARD_BACK"
                    class="img"
                    fit="cover"
                    lazy
                    @click="previewImg(legalPersonIdCardImgTowUrls[0], '法人身份证照片')"
                  />
                  <div class="tip-wrap">
                    <iconpark-icon name="iconimg" class="icon" />
                    <p>上传国徽面</p>
                  </div>
                </div>
              </RUploadImage>
            </t-form-item>

            <t-form-item label="法人姓名" name="legal_person_name">
              <t-input v-model="formData.legal_person_name" placeholder="请输入法人姓名" maxlength="10" />
            </t-form-item>

            <t-form-item label="法人身份证号码" name="legal_person_id_card">
              <t-input
                v-model="formData.legal_person_id_card"
                placeholder="请输入法人身份证号码"
                maxlength="18"
                show-limit-number
              />
            </t-form-item>

            <t-form-item>
              <template #label>
                <div class="t-form__label t-form__label--required t-form__label--top"><label>法人身份证有效期</label></div>
              </template>

              <div class="flex gap-8 w-full items-center">
                <t-form-item name="legal_person_id_card_start_time" class="mr-0!">
                  <t-date-picker
                    v-model="formData.legal_person_id_card_start_time"
                    placeholder="开始日期"
                    class="flex-1"
                    clearable
                  />
                </t-form-item>
                <span class="text-[#828DA5]">-</span>
                <t-form-item name="legal_person_id_card_end_time">
                  <t-date-picker
                    v-model="formData.legal_person_id_card_end_time"
                    placeholder="结束日期"
                    class="flex-1"
                    clearable
                    :disabled="isLongTerm"
                  />
                </t-form-item>
                <span class="text-[#D5DBE4] mx-8">|</span>
                <t-radio
                  v-model="isLongTerm"
                  allow-uncheck
                  :disabled="isLongTermDisabled"
                >
                  长期
                </t-radio>
              </div>
            </t-form-item>

            <t-form-item label="邮箱" name="email">
              <t-input v-model="formData.email" placeholder="请输入邮箱" />
            </t-form-item>
          </div>
        </Transition>
      </div>

      <!-- 结算信息 -->
      <div class="form-card" :class="{collapsed: !sectionOpen.pay}">
        <h3 class="form-title" @click="sectionOpen.pay = !sectionOpen.pay">
          <div class="flex items-center gap-4">
            结算信息
            <t-tooltip placement="top" overlay-inner-class-name="max-w-256!">
              <t-icon name="help-circle" class="text-16 color-text-3 cursor-pointer" />
              <template #content>
                <p>必须填写入网组织的对公账户信息，否则将无法进行后续的交易结算</p>
              </template>
            </t-tooltip>
          </div>
          <iconpark-icon name="iconarrowup-a960jjb9" :class="['icon-rotate', {collapsed: !sectionOpen.pay}]" />
        </h3>
        <Transition name="collapse">
          <div v-show="sectionOpen.pay">
            <t-form-item name="pay_merchant_legal_person_bank_img" class="id-card-form-item">
              <template #label>
                <div class="inline-flex items-center gap-4">
                  结算银行卡照片
                  <t-tooltip placement="top">
                    <t-icon name="help-circle" class="text-16 color-text-3 cursor-pointer" />
                    <template #content>
                      <p class="max-w-240">若没有银行卡照片，则上传开户证明</p>
                    </template>
                  </t-tooltip>
                </div>
              </template>
              <RUploadImage v-model="legalPersonBankImgUrls" root-dir="workBench" @click="uploadPreviewImg">
                <div class="id-card-upload">
                  <t-image
                    :src="BANK_PIC"
                    class="img"
                    fit="cover"
                    lazy
                  />
                  <div class="tip-wrap">
                    <iconpark-icon name="iconimg" class="icon" />
                    <p>上传银行卡照片</p>
                  </div>
                </div>
              </RUploadImage>
            </t-form-item>

            <t-form-item label="结算方式">
              <t-input placeholder="对公" disabled />
            </t-form-item>

            <t-form-item name="pay_merchant_legal_person_bank_card_open_bank_name">
              <template #label>
                开户行名称
                <t-tooltip placement="top">
                  <t-icon name="help-circle" class="text-16 color-text-3 cursor-pointer" />
                  <template #content>
                    <p>若没有对应的开户行名称选项，请前往广场搜索“另可科技”寻找客服沟通</p>
                  </template>
                </t-tooltip>
              </template>
              <t-select
                v-model="formData.pay_merchant_legal_person_bank_card_open_bank_name"
                filterable
                placeholder="请选择开户行名称"
                :loading="bankListLoading"
                :options="bankList"
                @search="fetchBankList"
              />
            </t-form-item>

            <t-form-item name="pay_merchant_legal_person_bank_card_number" label="法人银行卡号">
              <t-input
                v-model="formData.pay_merchant_legal_person_bank_card_number"
                placeholder="请输入法人银行卡号"
                maxlength="50"
              />
            </t-form-item>

            <t-form-item name="pay_merchant_legal_person_bank_card_bank_name" label="法人银行卡名称">
              <t-input
                v-model="formData.pay_merchant_legal_person_bank_card_bank_name"
                placeholder="请输入法人银行卡名称"
                maxlength="50"
              />
            </t-form-item>

            <t-form-item name="pay_merchant_legal_person_bank_mobile" label="法人手机号码">
              <t-input
                v-model="formData.pay_merchant_legal_person_bank_mobile"
                placeholder="请输入法人手机号码"
                maxlength="11"
              />
            </t-form-item>
          </div>
        </Transition>
      </div>
    </t-form>

    <!-- 底部操作栏 -->
    <div class="action-bar">
      <t-button
        theme="default"
        variant="outline"
        class="min-w-80! font-600!"
        :loading="submitLoading"
        @click="handleSubmit"
      >
        保存
      </t-button>
      <t-button theme="primary" :loading="submitLoading" @click="handleNextStep">下一步</t-button>
    </div>

    <BaiduMapSelector
      v-model:visible="mapVisible"
      :loc="{ address: formData.merchant_address }"
      @confirm="mapConfirm"
    />
  </div>
</template>

<style lang="less" scoped>
@import "@/style/transition.less";

.form-container {
  width: 100%;
  display: flex;
  justify-content: center;
  overflow-y: auto;
}

.form-content {
  width: 872px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  /* 给底部操作栏留空间 */
  padding-bottom: 64px;
  overflow-y: auto;
  .scrollbar2(0);

  .form-card {
    width: 100%;
    padding: 16px 24px;
    gap: 16px;
    border-radius: 8px;
    background: var(--bg-kyy_color_bg_light, #fff);
    transition: margin-bottom 0.2s;
    &.collapsed {
      padding-bottom: 4px;
    }
    &:first-of-type {
      padding-top: 0;
    }
    &.no-top-border-radius {
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
    }
  }

  .form-title {
    color: var(--text-kyy_color_text_1, #1a2139);
    font-size: 16px;
    font-weight: 600;
    line-height: 24px;
    padding-left: 11px;
    position: relative;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    &::before {
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      content: ' ';
      width: 3px;
      height: 16px;
      border-radius: 8px;
      background: var(--brand-kyy_color_brand_default, #4d5eff);
    }
    .icon-rotate {
      font-size: 20px;
      color: #828da5;
      cursor: pointer;
      transition: transform 0.3s cubic-bezier(.4,0,.2,1);
      display: inline-block;
      &.collapsed {
        transform: rotate(180deg);
      }
    }
  }
}

:deep(.t-form__label) {
  line-height: 22px;
  min-height: 22px;
  margin-bottom: 8px;
  label {
    display: flex;
    align-items: center;
    gap: 4px;
  }
}

.t-form-item__merchant_address {
  :deep(.t-textarea__inner) {
    padding-right: 50px;
  }
  :deep(.t-textarea__info_wrapper) {
    display: none;
  }
}

:deep(.t-form-item__start_time),
:deep(.t-form-item__end_time),
:deep(.t-form-item__legal_person_id_card_start_time),
:deep(.t-form-item__legal_person_id_card_end_time) {
  flex: 1;
  .t-form__label {
    display: none !important;
  }
}

.textarea-wrap {
  width: 100%;
  position: relative;
  .t-textarea__limit {
    position: absolute;
    right: 8px;
    bottom: 7px;
  }
}

.upload-fields {
  display: flex;
  gap: 24px;

  :deep(.t-form__item) {
    display: flex;
    flex-direction: column;
    margin-bottom: 0;
  }

  .divider-h {
    width: 1px;
    height: 78px;
    background: var(--divider-kyy_color_divider_light, #eceff5);
    margin: 0 16px;
  }

  .img-preview {
    position: relative;
    border-radius: 8px;

    .img {
      width: 78px;
      height: 78px;
      cursor: pointer;
      border-radius: 8px;
    }

    .example-tag {
      position: absolute;
      bottom: 0;
      right: 0;
      display: flex;
      padding: 2px 4px;
      justify-content: center;
      align-items: center;
      gap: 10px;
      border-radius: 8px 0px;
      background: var(--bg-kyy_color_bg_mask, rgba(0, 0, 0, 0.50));
      z-index: 1;
      color: var(--text-kyy_color_text_white, #FFF);
      text-align: center;
      font-size: 12px;
      font-weight: 400;
      line-height: 20px;
    }
  }
}

.id-card-upload {
  width: 140px;
  height: 88px;
  border-radius: 8px;
  border: 1px solid var(--divider-kyy_color_divider_deep, #d5dbe4);
  background: #fff;
  cursor: pointer;
  position: relative;

  .img {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 124px;
    height: 72px;
    cursor: pointer;
  }

  .tip-wrap {
    color: var(--kyy_color_upload_text_default, #516082);
    font-size: 12px;
    font-weight: 400;
    line-height: 20px;
    /* 166.667% */
    position: relative;
    z-index: 2;
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .icon {
    color: #828da5;
    font-size: 32px;
  }
}

.id-card-form-item {
  :deep(.img-wrap) {
    width: 140px;
    height: 88px;
  }
  :deep(.t-form__controls-content) {
    gap: 8px;
  }
}

:deep(.t-form__controls.t-is-error .id-card-upload) {
  border: 1px solid var(--error-kyy_color_error_default, #D54941);
}

.action-bar {
  position: fixed;
  bottom: 16px;
  left: 50%;
  right: 0;
  transform: translateX(-50%);
  z-index: 9;
  display: flex;
  width: 872px;
  height: 64px;
  padding: 16px 24px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  border-radius: 8px;
  border-top: 1px solid var(--bg-kyy_color_bg_list_hover, #f3f6fa);
  background: var(--bg-kyy_color_bg_light, #fff);
}
</style>
