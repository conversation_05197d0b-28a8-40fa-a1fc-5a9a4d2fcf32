export interface SettingApplyInterface {
  /**
   * 入会会费设置，1：缴费后入会，2：无需缴费入会
   */
  pay_setting: number;
  /**
   * 是否支持个人入会，1：是，0：否
   */
  personal_apply: number;
  /**
   * 个人入会表单设置，前端自行定义结构，字段名看表单字段说明
   */
  personal_form: { [key: string]: any }[];
  /**
   * 是否支持单位入会，1：是，0：否
   */
  team_apply: number;
  /**
   * 单位入会表单设置，前端自行定义结构，字段名看表单字段说明
   */
  team_form: { [key: string]: any }[];
}

export interface SettingRemindInterface {
  /**
   * 提前提醒天数
   */
  days: Array<number>;
  /**
   * 是否提醒，1：是，2：否
   */
  is_remind: number;
  /**
   * 部门提醒人员数组
   */
  members: number[];
  /**
   * 提醒类型，1：全部，2：部门人员
   */
  type: number;
}
