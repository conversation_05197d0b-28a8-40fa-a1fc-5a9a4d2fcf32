<template>
  <div v-if="stateStore.refreshKey === currentKey" class="page-content">
    <div ref="editorContainerRef" class="mx-16 overflow-y-auto w-622 pb-50" style="height: calc(100% - 28px)">
      <div class="input-wrap">
        <t-input
          v-model="formData.article.title"
          :maxlength="64"
          show-limit-number
          clearable
          autofocus
          :placeholder="$t('square.page.titleInputTip')"
        />
      </div>
      <Toolbar
        :editor="editorRef"
        :default-config="editorToolbarConfig"
        class="toolbar"
      />
      <div class="editor-content">
        <Editor
          v-model="formData.article.content"
          :default-config="editorConfig"
          style="height: 357px; overflow-y: hidden;"
          @custom-paste="onCustomPast"
          @on-created="editorCreated"
        />
        <div
          v-if="pasteLoading"
          class="paste-loading"
          @click.stop
        >
          <t-loading />
          <span>{{ pasteLoadingProgress ? `${pasteLoadingProgress}%` : '' }}</span>
        </div>
      </div>

      <!--选择海报-->
      <div class="cover-wrap">
        <div class="title">封面</div>
        <t-dropdown :options="[{ content: $t('square.post.selectFromArticle'), value: 1 }, { content: $t('square.action.uploadImg'), value: 2 }]" :max-column-width="120" @click="uploadWay">
          <div v-if="imgSelected" class="btn-upload">
            <t-image :src="imgSelected" fit="cover" @click="cropperRef.open(imgSelected)" />
            <iconpark-icon
              name="iconclean"
              class="btn-close"
              fill="#fff"
              @click="imgSelected =''; formData.article.img = ''"
            />
          </div>
          <div v-else class="btn-upload flex-col-center">
            <iconpark-icon name="iconimg" class="icon" />
            <p>{{ $t('square.upload.clickUpload') }}</p>
          </div>
        </t-dropdown>
        <div class="tips">
          {{ $t('square.article.coverTip') }}{{ $t('square.article.coverTip1') }}
        </div>

        <t-upload
          ref="uploadImgRef"
          v-model="images"
          theme="custom"
          accept="image/jpg,image/jpeg,image/png,image/gif,image/bmp,image/webp"
          :size-limit="{ size: 10, unit: 'MB', message: $t('square.uploadImgLimit', ['{sizeLimit}']) }"
          :request-method="uploadImage"
          allow-upload-duplicate-file
          @validate="onUploadValidate"
        >
&nbsp;
        </t-upload>
      </div>
    </div>

    <div class="footer">
      <t-button variant="outline" :disabled="!formValid" @click="previewArticle">
        <template #icon>
          <iconpark-icon name="iconpreciewopen" class="icon" />&nbsp;
        </template>
        {{ $t('square.action.preview') }}
      </t-button>
      <t-button
        variant="outline"
        class="mx-8"
        :disabled="!formValid"
        @click="handleActionSaveDraft"
      >
        <template #icon>
          <iconpark-icon name="iconsave" class="icon" />&nbsp;
        </template>
        {{ $t('square.article.saveAsDraft') }}
      </t-button>
      <t-button theme="primary" :disabled="!nextValid" @click="nextStep">
        {{ $t('square.nextStep') }}
      </t-button>
    </div>

    <ImageSelect
      v-if="selectImageVisible"
      v-model="selectImageVisible"
      :list="imageList"
      @confirm="imageSelectConfirm"
    />

    <CropperDialog
      ref="cropperRef"
      :options="{ aspectRatio: 16 / 9 }"
      :size-limit="{ size: 10, message: $t('square.uploadImgLimit', ['{sizeLimit}']) }"
      @confirm="cropperConfirm"
    />

    <ArticlePreviewDialog
      v-if="articlePreviewVisible"
      v-model="articlePreviewVisible"
      :data="formData.article"
      :disabled="!formValid"
      @next="nextStep"
    />

    <PostPublishDialog
      v-model="postDialogVisible"
      source="article"
      :draft="isDraft"
      only-text
      :default-value="formData"
      :team-id="squareStore.teamId"
      @submit="submitPost"
      @certified-gov="certifiedGov"
    />
  </div>
</template>

<script setup lang="tsx" name="SquarePublishArticle">
import { ref, computed, watch, toRaw, inject, nextTick, onBeforeUnmount, onDeactivated } from 'vue';
import '@wangeditor/editor/dist/css/style.css';
import { Editor, Toolbar } from '@wangeditor/editor-for-vue';
import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next';
import to from 'await-to-js';
import { onBeforeRouteLeave, useRoute, useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { onMountedOrActivated } from '@renderer/hooks/onMountedOrActivated';
import { IDomEditor } from '@wangeditor/editor';
import ImageSelect from '@/views/square/publish-article/components/ImageSelect.vue';
import { PostType, ROUTE_REFRESH_INJECT_KEY, Visibility } from '@/views/square/constant';
import { PostRequestData, PostStatus } from '@/api/square/models/post';
import { useImageUpload } from '@/views/square/hooks/upload';
import { addDraft, getArticle, getBufferByImgUrl, updateDraft } from '@/api/square/post';
import PostPublishDialog from '@/views/square/components/post/PostPublishDialog.vue';
import ArticlePreviewDialog from '@/views/square/publish-article/components/ArticlePreviewDialog.vue';
import useEditor from '@/views/square/hooks/editor';
import { multipartUpload, onUploadValidate } from '@/views/square/utils/upload';
import { useSquareStore } from '@/views/square/store/square';
import CropperDialog from '@/views/square/components/CropperDialog.vue';
import { useStateStore } from '../store/state';
import { useTabsStore } from '@/components/page-header/store';

const tabStore = useTabsStore();
const squareStore = useSquareStore();
const stateStore = useStateStore();

const { t } = useI18n();
const router = useRouter();
const route = useRoute();
const defaultData = {
  id: squareStore.squareId,
  post: {
    text: '',
    postType: PostType.Article,
    picture: {
      urls: [],
    },
    allowShare: true,
    fansGroupId: [],
    visibility: Visibility.PUBLIC,
    video: '',
    allowComment: true,
    forward: {
      postId: null,
    },
  },
  article: {
    title: '',
    img: '',
    content: '',
  },
};

const publishFormData = stateStore.publishFormData;
const formData = ref<PostRequestData & { id: number }>((Array.isArray(publishFormData) && publishFormData?.find((item) => item.id === squareStore.squareId)) || JSON.parse(JSON.stringify(defaultData)));
const isDraft = computed(() => stateStore.publishIsDraft);
const defaultDraft = ref();
const pasteLoading = ref(false);
const pasteLoadingProgress = ref(0);

const selectImageVisible = ref(false);
const imageList = ref([]);
// 要裁剪的图片
const toResizeImg = ref('');
const imgSelected = ref(formData.value.article.img || '');

const loading = ref(false);
const isSubmitDraft = ref(false);
const getArticleInfo = async (article_id: string) => {
  loading.value = true;
  const [err, res] = await to(getArticle({ article_id }));
  loading.value = false;
  if (err) return;

  const article = res.data.article.article;
  formData.value.article = { ...article };
  defaultDraft.value = { ...article };
  imgSelected.value = article.img;
};

const currentKey = ref(-1);

onMountedOrActivated(async () => {
  stateStore.refreshArticlePublic();
  await nextTick();

  isSubmitDraft.value = false;
  squareStore.isRemoveTag = false;
  formData.value.post.id = route.query.id as string;
  currentKey.value = stateStore.refreshKey;
  handleWatch();
});

const getHasPublishTab = (val?) => {
  const index = (val || tabStore.tabs)?.findIndex((item) => item.fullPath.includes('/square/publish-article'));
  return index > -1;
};

watch(() => tabStore.tabs, (val) => {
  const hasPublishTab = val.find((item) => item.fullPath === '/square/publish-article');
  console.log('val', val, hasPublishTab);
  if (!hasPublishTab) {
    stateStore.isPublishing = false;
  }
}, { deep: true, immediate: true });

const clearFormData = () => {
  formData.value.article.title = '';
  formData.value.article.img = '';
  formData.value.article.content = '';
  imgSelected.value = '';
  editorRef.value?.clear();
};

const unWatchFrom = ref();
const unWatchAritlceId = ref();

const handleWatch = () => {
  unWatchFrom.value = watch(() => route.query.from, async (val) => {
    const hasPublishTab = getHasPublishTab();
    if (val === 'draft') {
      stateStore.setPublishIsDraft(true);
    } else if (val === 'add') {
      await nextTick();
      stateStore.setPublishIsDraft(false);
      // 如果有发布文章的tab，则不执行清空操作
      if (hasPublishTab && stateStore.editingFormData) {
        return;
      }
      setTimeout(() => {
        clearFormData();
        const copyData = JSON.parse(JSON.stringify(defaultData));
        copyData.post.status = PostStatus.Posted;
        stateStore.savePublishFormData(copyData);
        formData.value = copyData;
        formData.value.post.id = '';
        formData.value.article.id = '';
      });

      // 移除路径上的 from=add 参数，避免点击页签切回时数据被清空
      // tabStore.addTab({ fullPath: route.path }, route.fullPath);
    } else if (!isDraft.value) {
      console.log('formData', formData.value, stateStore.editingFormData);
      if (stateStore.editingFormData) {
        formData.value = stateStore.editingFormData;
      }
    }
  }, { immediate: true });

  // 编辑文章时的预览
  unWatchAritlceId.value = watch(() => route.query.articleId, async (id) => {
    if (!id) {
      return;
    }

    formData.value.post.text = route.query.text as string;
    if (String(id) !== String(formData.value.article.id)) {
      getArticleInfo(id as string);
    }
  }, { immediate: true });
};

onBeforeUnmount(() => {
  unWatchFrom.value?.();
  unWatchAritlceId.value?.();
});
onDeactivated(() => {
  // unWatchFrom.value?.();
  // unWatchAritlceId.value?.();
});

watch(() => formData.value, (newVal) => {
  stateStore.savePublishFormData(newVal);
  stateStore.editingFormData = formData.value;
}, { deep: true });

const {
  editorContainerRef, editorRef, editorToolbarConfig, editorConfig, editorCreated,
} = useEditor();

const getImagesInEditor = () => editorRef.value.getElemsByType('image').map((v) => v.src);

// 选择海报图片
const imageSelectConfirm = (img) => {
  toResizeImg.value = img;
  // imgSelected.value = img;
  cropperRef.value.open(img);
};

// 裁剪海报图片
const cropperRef = ref(null);
const cropperConfirm = async (url: string) => {
  imgSelected.value = url;
  formData.value.article.img = url;
};

// 图片上传
const { images, uploadImage } = useImageUpload((res) => {
  imageSelectConfirm(res.url);
});

// 选择海报的方式
const uploadImgRef = ref(null);
const uploadWay = (option) => {
  if (option.value === 1) {
    imageList.value = getImagesInEditor();
    selectImageVisible.value = true;
  } else {
    // uploadImgRef.value.triggerUpload();
    cropperRef.value.open();
  }
};

// 预览
const articlePreviewVisible = ref(false);
const previewArticle = () => {
  // TODO 独立弹窗
  articlePreviewVisible.value = true;
};

const EMPTY_CONTENT = '<p><br></p>';
// const formValid = computed(() => Object.keys(formData.article).map((k) => !!formData.article[k]).every((v) => v));
const formValid = computed(() => {
  const { article } = formData.value;
  return !!article.title || !['', EMPTY_CONTENT].includes(article.content);
});
const nextValid = computed(() => formValid.value && formData.value.article.title && formData.value.article.img);
const isPublished = ref(false); // 是否最直接进行了文章发布（不包括从草稿箱进行发布）
let isSubmitted = false;

const routeRefresh = inject(ROUTE_REFRESH_INJECT_KEY);
const back = async () => {
  // let query = omit(route.query as Record<string, string>, 'from');
  // query = JSON.stringify(query) === '{}' ? {} : query;
  tabStore.removeTab(route.fullPath);
  if (isPublished.value) {
    router.back();
  } else {
    router.replace('/square/drafts');
  }
  routeRefresh?.();
};

const handleActionSaveDraft = () => {
  isSubmitDraft.value = true;
  saveDraft();
};

const saveDraft = async (needBack = true) => {
  const data = toRaw((formData.value));
  const postId = formData.value.post.id;
  const api = postId ? updateDraft : addDraft;
  if (postId) data.post_id = postId;
  if (data.article.id === '' || data.post.id === '') {
    delete data.article.id;
    delete data.post.id;
  }

  data.article.content = justifyFirstParagraph(data.article.content);

  const [err] = await to(api(data));
  if (err) return;

  formData.value.article.id = '';
  isSubmitted = true;
  await MessagePlugin.success(t('square.saveSuccessTip'));
  needBack && back();
};

const postDialogVisible = ref(false);

// 下一步
const nextStep = async () => {
  if (!formData.value.article.title) {
    await MessagePlugin.warning(t('square.page.titleInputTip'));
    return;
  }
  if (!formData.value.article.img) {
    await MessagePlugin.warning(t('square.post.selectPoster'));
    return;
  }
  if (formData.value.article.id === '' || formData.value.post.id === '') {
    delete formData.value.article.id;
    delete formData.value.post.id;
  }
  formData.value.post.text = formData.value.post.text || t('square.post.publishArticle');

  formData.value.article.content = justifyFirstParagraph(formData.value.article.content);

  if (isDraft.value) formData.value.post.status = PostStatus.Draft;
  postDialogVisible.value = true;
};

/**
 * 对文章内容默认进行两端对齐处理
 * @param content - 文章内容，HTML字符串格式
 * @returns 处理后的HTML字符串，如果处理失败则返回原始内容
 */
const justifyFirstParagraph = (content: string): string => {
  if (!content) return content;

  try {
    const doc = new DOMParser().parseFromString(content, 'text/html');
    const paragraphs = doc.body.getElementsByTagName('p');

    for (const paragraph of Array.from(paragraphs)) {
      if (!paragraph.style.textAlign) {
        paragraph.style.textAlign = 'justify';
      }
    }

    return doc.body.innerHTML;
  } catch (error) {
    console.error('Error justifying paragraphs:', error);
    return content;
  }
};

// 文章动态已提交回调
const submitPost = () => {
  isSubmitted = true;
  postDialogVisible.value = false;
  isPublished.value = true;
  back();
};

// 文章是否被更新
const getIsUpdateArticle = () => {
  let isUpdate = false;
  for (const key in defaultDraft.value) {
    if (defaultDraft.value[key] !== formData.value.article[key]) {
      isUpdate = true;
      break;
    }
  }
  return isUpdate;
};

const certifiedGov = (data: PostRequestData) => {
  const { text, id } = data.post;
  formData.value.post.text = text;
  formData.value.post.id = id;
};

const handleDialog = (next: Function, dialogContent: string) => {
  const confirmDia = DialogPlugin.confirm({
    header: t('square.tip'),
    confirmBtn: t('square.action.save'),
    body: () => dialogContent,
    closeOnOverlayClick: false,
    theme: 'info',
    onConfirm: async () => {
      confirmDia.destroy();
      await saveDraft(false);
      formData.value.article.content = '';
      formData.value.article.title = '';
      formData.value.article.img = '';
      next('/square/drafts');
    },
    onCancel: async () => {
      confirmDia.destroy();
      if (squareStore.isRemoveTag) {
        formData.value.article.content = '';
        formData.value.article.title = '';
        formData.value.article.img = '';
        imgSelected.value = '';
      }
      next();
    },
  });
};

onBeforeRouteLeave((to, from, next) => {
  isPublished.value = false;
  console.log('isSubmitDraft.value', isSubmitDraft.value, squareStore.isRemoveTag);
  if (isDraft.value || isSubmitted) {
    if (getIsUpdateArticle() && squareStore.isRemoveTag && !isSubmitDraft.value) {
      isSubmitDraft.value = true;
      handleDialog(next, '内容已修改，是否进行保存？');
      return;
    }
    if (isSubmitted) {
      formData.value.article.title = '';
      formData.value.article.content = '';
      formData.value.article.img = '';
      imgSelected.value = '';
      isSubmitted = false;
    }
    next();
    return;
  }

  const hasContent = formData.value.article.title || (formData.value.article.content && formData.value.article.content !== EMPTY_CONTENT);
  if (hasContent && squareStore.isRemoveTag) {
    handleDialog(next, '是否保存草稿？保存之后则更新保存到草稿中');
    return;
  }

  next();
});

const getImageBlob = (url) => new Promise((resolve, reject) => {
  fetch(url, {
    mode: 'cors',
    referrer: '',
  })
    .then((res) => res.blob())
    .then((blob) => resolve(blob))
    .catch(() => {
    // fetch请求失败时，通过后端接口去获取
      getBufferByImgUrl(url).then((res) => {
        console.log('fetch请求失败，通过后端请求成功！');
        resolve(new Blob([res.data], { type: res.headers['content-type'] }));
      }).catch((err) => {
        console.log('fetch请求失败，通过后端请求也失败！');
        reject(err);
      });
    });
});

const onCustomPast = async (editor: IDomEditor, event: ClipboardEvent, callback: Function) => {
  // 阻止默认的粘贴行为
  event.preventDefault();
  pasteLoading.value = true;
  let text = event.clipboardData.getData('text/html');
  const result = text.match(/<img src=".*?".*?\/?>/g);
  if (text && result && result.length) {
    for (const [idx, item] of result.entries()) {
      const [_, url] = item.match(/src="(.*?)"/);
      if (!url) continue;
      // 把html的转义符替换为正常字符，否则请求不到资源(403)
      const newUrl = url.replaceAll('&amp;', '&');
      try {
        // eslint-disable-next-line no-await-in-loop
        const blob = await getImageBlob(newUrl);
        const fileName = Date.now().toString().slice(7) + Math.round(Math.random() * 1000);
        // eslint-disable-next-line no-await-in-loop
        const res = await multipartUpload({ blob, name: fileName }, {
          rename: true,
          onProgress: (p) => {
            const progress = Math.floor(p * 100 * (1 / result.length));
            pasteLoadingProgress.value = Math.floor(idx * 100 * (1 / result.length)) + progress;
            pasteLoadingProgress.value = pasteLoadingProgress.value > 100 ? 100 : pasteLoadingProgress.value;
          },
        });
        const { url } = res;
        const newItem = item.replace(/src=".*?"/, `src="${url}"`);
        text = text.replace(item, newItem);
      } catch (err) {
        console.log('err', err, item, newUrl);
      }
    }
    pasteLoading.value = false;
    pasteLoadingProgress.value = 0;
    editor.dangerouslyInsertHtml(text);
    callback(true);
    return;
  }
  pasteLoading.value = false;
  pasteLoadingProgress.value = 0;
  editor.dangerouslyInsertHtml(event.clipboardData.getData('text/plain'));
  callback(false); // 返回值（注意，vue 事件的返回值，不能用 return）
  // return false;
};
</script>

<style scoped lang="less">
.page-content {
  padding-top: 44px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.input-wrap {
  padding: 12px 0;
  :deep(.t-input) {
    height: 50px;
  }
  :deep(.t-input__inner) {
    font-size: 18px;
    &::placeholder {
      color: var(--text-kyy-color-text-5, #ACB3C0);
      font-weight: 600;
    }
  }
}

.toolbar {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  top: 1px;
  background: #fff;
  z-index: 9;
  width: 624px;
  border-radius: 8px;
  :deep(.w-e-bar-item button) {
    padding: 0 2px;
  }
}

:deep(.w-e-bar) {
  border-radius: 8px;
  background: var(--bg-kyy-color-bg-default, #FFF);
  //box-shadow: 0 8px 16px 0 rgba(0, 0, 0, 0.12);
}

:deep(.t-input),
:deep(.w-e-text-container) {
  border-radius: 8px;
  border: 1px solid var(--divider-kyy-color-divider-light, #ECEFF5);
  background: var(--bg-kyy-color-bg-light, #FFF);
  h1, h2, h3, h4, h5 {
    line-height: 1.5;
  }
}

:deep(.w-e-text-placeholder) {
  color: var(--text-kyy-color-text-5, #ACB3C0);
  font-style: normal;
  font-size: 14px;
}

:deep(.w-e-select-list) ul {
  li[title="H4"],
  li[title="H5"] {
    display: none;
  }
}

.cover-wrap {
  margin-top: 24px;
  .title {
    color: var(--text-kyy-color-text-1, #1A2139);
    font-size: 16px;
    font-weight: 600;
    line-height: 24px;
    margin-bottom: 12px;
  }
  .btn-upload {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 240px;
    height: 136px;
    margin-right: 16px;
    border-radius: 8px;
    border: 1px solid var(--kyy_color_upload_border_default, #D5DBE4);
    background: var(--kyy_color_upload_bg, #FFF);
    overflow: hidden;
    cursor: pointer;
    .icon {
      font-size: 48px;
      color: #97979a;
    }
    .btn-close {
      position: absolute;
      top: 4px;
      right: 4px;
      font-size: 24px;
      cursor: pointer;
      color: #66686b;
      z-index: 1;
    }
    :deep(.t-image__wrapper) {
      width: 100%;
      height: 100%;
    }
  }
  .tips {
    margin-top: 16px;
    color: var(--text-kyy-color-text-3, #828DA5);
  }
}

.editor-content {
  position: relative;
  .paste-loading {
    position: absolute;
    inset: 0;
    background: rgba(255, 255, 255, 0.1);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    span {
      color: rgb(77, 94, 255);
      margin-top: 8px;
    }
  }
}
.footer {
  position: absolute;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 64px;
  background: #ffffff;
  border-radius: 0 0 4px 4px;
  z-index: 1;
}
</style>
