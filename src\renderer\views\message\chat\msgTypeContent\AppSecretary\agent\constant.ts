import { i18nt } from '@renderer/i18n';

/**
 * 场景类型枚举
 */
export enum SceneType {
  /**
   * 入会申请
   */
  CONTACT_APPLY_MEMBER = 5080,

  /**
   * 入会拒绝
   */
  CONTACT_APPLY_REJECT_MEMBER = 5081,
  /**
   * 入会申请
   */
  CONTACT_APPLY_POLITICS = 14040,

  /**
   * 入会拒绝
   */
  CONTACT_APPLY_REJECT_POLITICS = 14041,
  /**
   * 入会申请
   */
  CONTACT_APPLY_CBD= 16040,

  /**
   * 入会拒绝
   */
  CONTACT_APPLY_REJECT_CBD = 16041,
  /**
   * 入会申请
   */
  CONTACT_APPLY_ASSOCIATION = 19040,

  /**
   * 入会拒绝
   */
  CONTACT_APPLY_REJECT_ASSOCIATION = 19041,
    /**
   * 入会申请
   */
  CONTACT_APPLY_UNI = 51040,

  /**
   * 入会拒绝
   */
  CONTACT_APPLY_REJECT_UNI = 51041,

}

/**
 * 拒绝的场景类型
 */
export const reject = [
  SceneType.CONTACT_APPLY_REJECT_MEMBER,
  SceneType.CONTACT_APPLY_REJECT_POLITICS,
  SceneType.CONTACT_APPLY_REJECT_CBD,
  SceneType.CONTACT_APPLY_REJECT_ASSOCIATION,
  SceneType.CONTACT_APPLY_REJECT_UNI,
];
/**
 * 展示团队logo和标题
 */
export const titleMap = {
  [SceneType.CONTACT_APPLY_MEMBER]: i18nt('im.public.biz'),
  [SceneType.CONTACT_APPLY_REJECT_MEMBER]: i18nt('im.public.biz'),
  [SceneType.CONTACT_APPLY_POLITICS]: i18nt('im.public.government'),
  [SceneType.CONTACT_APPLY_REJECT_POLITICS]: i18nt('im.public.government'),
  [SceneType.CONTACT_APPLY_CBD]: i18nt('application.digital_cbd'),
  [SceneType.CONTACT_APPLY_REJECT_CBD]: i18nt('application.digital_cbd'),
  [SceneType.CONTACT_APPLY_ASSOCIATION]: i18nt('niche.szsq'),
  [SceneType.CONTACT_APPLY_REJECT_ASSOCIATION]: i18nt('niche.szsq'),
  [SceneType.CONTACT_APPLY_UNI]: i18nt('niche.szgx'),
  [SceneType.CONTACT_APPLY_REJECT_UNI]: i18nt('niche.szgx'),
};

export const getBtnTxt = (status) => {
  const titleMap = {
    6: i18nt('ebook.vrefuse'),
    1: i18nt('ebook.vtype3'),
    4: i18nt('ebook.vtype6'),
  };
  return titleMap[status] || null;
};

export const AgentSceneTypeArr = Object.values(SceneType).filter((value) => typeof value === 'number');
