<template>
  <t-drawer :closeOnOverlayClick="false" class="drawerSetForm drawerSetBodyNoPadding drawerSetBodyNoPaddingBottom"
    v-model:visible="visible" :close-btn="true" :size="472" zIndex="6000" :footer="null" header="加入数字平台"
    @close="onClose">
    <template #closeBtn>
      <iconpark-icon name="iconerror" style="font-size: 24px" class="iconerror"></iconpark-icon>
    </template>

    <template #body>
      <div class="cer">
        <div class="serch-bar">
          <t-input ref="inputRef" v-model="keyword" autofocus :placeholder="`请输入要搜索的组织名称/简称`" clearable
            @clear="onSearch" @change="onSearch">
            <template #prefix-icon>
              <iconpark-icon name="iconsearch" class="name-icon" />
            </template>
          </t-input>
          <!-- <t-button style="width: 80px" @click="getDetailRun">{{ t("niche.ss") }} </t-button> -->
        </div>
        <div class="list" v-infinite-scroll="handleInfiniteOnLoad" :infinite-scroll-immediate-check="false"
          :infinite-scroll-disabled="scrollDisabled" infinite-scroll-watch-disabled="scrollDisabled"
          :infinite-scroll-distance="20" v-if="datas?.length > 0">
          <div class="item" v-for="item in datas" :key="item?.teamId">
            <span class="sleft">
              <t-image class="logo" :src="item?.logo" fit="cover">
                <template #loading>
                  <img :src="ORG_DEFAULT_AVATAR" />
                </template>
                <template #error>
                  <img :src="ORG_DEFAULT_AVATAR" />
                </template>
              </t-image>
              <div class="con">
                <span class="tip line-1"
                  v-html="highlightText(item?.exclusive_name || item?.fullName, keyword?.trim())"></span>
                <span class="type government" v-show="type.Government === item?.platform_uuid">
                  <img :src="government" class="img">
                  数字城市
                </span>
                <span class="type member" v-show="type.Member === item?.platform_uuid">
                  <img :src="member" class="img">
                  数字商协
                </span>
                <span class="type cbd" v-show="type.Cbd === item?.platform_uuid">
                  <img :src="cbd" class="img">
                  数字CBD
                </span>
                <span class="type association" v-show="type.Association === item?.platform_uuid">
                  <img :src="association" class="img">
                  数字社群
                </span>
                <span class="type school" v-show="type.Uni === item?.platform_uuid">
                  <img :src="schoolicon" class="img">
                  数字高校
                </span>
              </div>
            </span>
            <span class="sright">
              <t-button theme="primary" class="max-w-80px btn" @click="onInvitApply(item)">申请加入</t-button>
            </span>
          </div>

          <div class="no-more" v-show="listCount <= datas?.length">
            <span class="line"></span>
            <span class="more">没有更多了</span>
            <span class="line"></span>
          </div>
        </div>
        <div class="cont" v-show="!datas || datas.length < 1">
          <Empty :tip="keyword?.trim() ?  '搜索无结果': `输入关键词搜索`" :name="keyword?.trim() ? 'no-result' : 'no-data-new'" />
        </div>
      </div>
    </template>

  </t-drawer>
</template>
<script setup lang="ts">
  import { computed, ref, defineEmits } from 'vue';
  import { useI18n } from 'vue-i18n';
  import Empty from "@renderer/components/common/Empty.vue";
  import { ORG_DEFAULT_AVATAR } from "@/views/square/constant";
  import digital from "@renderer/assets/member/svg/digital.svg";
  import association from "@renderer/assets/member/svg/association.svg";
  import schoolicon from "@renderer/assets/member/svg/schoolicon.svg";
  import government from "@renderer/assets/member/svg/government.svg";
  import business from "@renderer/assets/member/svg/business.svg";
  import member from "@renderer/assets/member/svg/member.svg";
  import cbd from "@/assets/member/svg/cbd.svg";
  import to from 'await-to-js';
  import { getByAppUuidGetTeamApi } from '@renderer/api/digital-platform/api/businessApi';
  import { getResponseResult } from '@renderer/utils/myUtils';
  import { getMemberApplyLinkAxios as getMemberLinkAxios } from '@renderer/api/member/api/businessApi';
  import { getMemberApplyLinkAxios as getPoliticsLinkAxios } from '@renderer/api/politics/api/businessApi';
  import { getMemberApplyLinkAxios as getCbdLinkAxios } from '@renderer/api/cbd/api/businessApi';
  import { getMemberApplyLinkAxios as getAssociationLinkAxios } from '@renderer/api/association/api/businessApi';
  import { getMemberApplyLinkAxios as getUniLinkAxios } from '@renderer/api/uni/api/businessApi';

  import { MessagePlugin } from 'tdesign-vue-next';
  import { getOpenid } from '@renderer/utils/auth';
  import { inviteUrl } from '@renderer/utils/baseUrl';
  import Qs from "qs";
  const { t } = useI18n();
  const keyword = ref('');
  const visible = ref(false);
  import { jumpH5WithLink } from "@renderer/views/contacts/utils";

  enum type {
    Member = 'member',
    Government = 'government',
    Cbd = 'cbd',
    Association = 'association',
    Uni = 'uni',
  }
  const emit = defineEmits(['open', 'close']);
  // const props = defineProps({
  //   type: {
  //     type: String,
  //     default: 'member', // 商协：member，城市：government cbd association社群
  //   }
  // })




  const datas: any = ref([]);

  let listCount = ref(0);
  const pagination = {
    pageSize: 20,
    page: 1,
    // total: 0,
  };
  const handleInfiniteOnLoad = () => {
    console.log("handleInfiniteOnLoad");
    // 异步加载数据等逻辑
    if (scrollDisabled.value) {
      // 数据加载完毕
    } else {
      // 加载数据列表
      pagination.page++;
      onGetOrgList().then((res: any) => {
        datas.value = datas.value.concat(
          datas.value = res?.list?.map((v: any) => {
            if (v.title) {
              v.fullName = v.fullName + `（${v.title}）`
            }
            return v;
          }) || []
        );
        listCount.value = res?.count || 0;
      });
    }
  };
  // 注意scrollDisabled 必须开始的时候就是false, 可以用length > 0 或者loading再渲染

  const scrollDisabled = computed(() => {
    console.log(datas.value.length, listCount.value, datas.value.length >= listCount.value);
    console.log(listCount.value, "dd", listCount.value > 0 ? datas.value.length >= listCount.value : false);
    return listCount.value > 0 ? datas.value.length >= listCount.value : false;
    // return memberLevels.value.length >= listCount ;
  });


  const highlightText = (text, searchTerm) => {
    // 如果搜索词为空，则直接返回原始文本
    if (!searchTerm) {
      return text;
    }

    // 使用正则表达式删除HTML标签
    text = text.replace(/<[^>]+>/g, "");

    // 使用正则表达式进行全局不区分大小写的文本匹配
    const regex = new RegExp(searchTerm, "gi");

    // 使用 <span> 标签包裹匹配到的文本，并添加样式
    const highlightedText = text.replace(
      regex,
      (match) => `<span class="highlight">${match}</span>`
    );

    return highlightedText;
  };


  const onGetOrgList = () => {

    return new Promise(async (resolve, reject) => {
      const [err2, res2]: any = await to(getByAppUuidGetTeamApi({ name: keyword.value?.trim(), ...pagination }));
      if (err2) {
        reject(err2);
        return;
      }
      const { data } = res2?.data;
      resolve(data)
    });
  }
  const onSearch = () => {
    pagination.page = 1;
    console.log('kak', Boolean(keyword.value?.trim()))
    if (keyword.value && keyword.value?.trim()) {
      onGetOrgList().then((res: any) => {
        console.log(res)
        datas.value = res?.list?.map((v: any) => {
          if (v.title) {
            v.fullName = v.fullName + `（${v.title}）`
          }
          return v;
        }) || [];
        listCount.value = res?.count || 0;
      });
    } else {
      datas.value = [];
    }

  }
  // 获取邀请链接
  const getInviteLinkAxios = (item, handlFunc) => {
    let result = null;
    // eslint-disable-next-line no-async-promise-executor
    return new Promise(async (resolve, reject) => {
      try {
        result = await handlFunc({}, item.teamId);
        result = getResponseResult(result);
        if (!result) {
          reject();
          return;
        }
        resolve(result.data);
      } catch (error) {
        reject();
        const errMsg = error instanceof Error ? error.message : error;
        if (errMsg === 'Network Error') {

        } else {
          MessagePlugin.error(errMsg);
        }
      }
    });
  };
  const onInvitApply = (item: any) => {
    let url = ''
    let path = ""
    let handleFunc: Function = () => { };
    switch (item?.platform_uuid) {
      case type.Member:
        path = "/account/jump?to=memberInvite";
        handleFunc = getMemberLinkAxios;
        break;
      case type.Government:
        path = "/account/jump?to=politicsInvite"
        handleFunc = getPoliticsLinkAxios;
        break;
      case type.Cbd:
        path = "/account/jump?to=cbdInvite"
        handleFunc = getCbdLinkAxios;
        break;
      case type.Association:
        path = "/account/jump?to=associationInvite"
        handleFunc = getAssociationLinkAxios;
        break;
      case type.Uni:
        path = "/account/jump?to=uniInvite"
        handleFunc = getUniLinkAxios;
        break;
      default:
        break;
    }

    getInviteLinkAxios(item, handleFunc).then((res: any) => {
      if (res) {

        let params = {
          link: res.link,
        }
        url = `${inviteUrl}${path}&${Qs.stringify(params, { encode: true })}`
      }
      console.log(url)
      // shell.openExternal(url);
      jumpH5WithLink(url);
    })
  }


  const onOpen = () => {
    visible.value = true;
    emit('open');
  }
  const onClose = () => {
    visible.value = false;
    emit('close');
  }

  defineExpose({
    onOpen,
    onClose
  })
</script>

<style lang="less" scoped>
  /*定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
    // height: 2px;
    // background-color: #f5f5f5;
  }

  /*定义滚动条轨道 内阴影+圆角*/
  ::-webkit-scrollbar-track {
    // background-color: #e3e6eb;
    // background-color: #fff;
  }

  /*定义滑块 内阴影+圆角*/
  ::-webkit-scrollbar-thumb {
    border-radius: 7px;
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
    // background-color: #D5DBE4;
    background-color: var(--divider-kyy_color_divider_deep, #D5DBE4);
  }


  .no-more {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 12px;

    .line {
      background-color: #ECEFF5;
      height: 1px;
      // width: 100%;
      flex: 1;

    }

    .more {
      color: var(--text-kyy_color_text_2, #516082);
      font-size: 14px;
      font-weight: 400;
      line-height: 22px;
      /* 157.143% */
    }
  }



  .box {
    width: 100%;
  }

  .cer {
    height: 100%;
    display: flex;
    flex-direction: column;

    .cont {
      flex: 1;
      background: var(--bg-kyy_color_bg_deep, #F5F8FE);

      .wrap {
        height: 100%;
      }

    }

    .list {
      display: flex;
      flex-direction: column;
      padding: 12px 16px;
      background-color: var(--bg-kyy_color_bg_deep, #F5F8FE);
      height: 100%;
      gap: 12px;


      overflow-y: auto;

      .item {
        display: flex;
        justify-content: space-between;
        padding: 16px;
        align-items: center;
        border-radius: 8px;
        background: var(--bg-kyy_color_bg_light, #FFF);

        .sright {
          flex: none;

          .btn {
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
          }
        }

        .sleft {
          display: flex;
          position: relative;
          gap: 12px;
          flex: 1;

          .logo {
            width: 44px;
            height: 44px;
            border-radius: 50%;

            img {
              width: 44px;
              height: 44px;
              border-radius: 50%;
            }
          }

          .con {
            display: flex;
            flex-direction: column;
            flex: 1;

            :deep(.highlight) {
              color: #4d5eff;
            }

            .tip {
              font-size: 14px;
              font-style: normal;
              font-weight: 400;
              flex: inherit;
              max-width: 260px;
              color: var(--text-kyy_color_text_1, #1A2139);
            }

            .type {
              width: fit-content;
              // padding:  4px 0;
              padding: 0 4px;
              border-radius: 4px;
              background: var(--bg-kyy_color_bg_deep, #F5F8FE);
              display: flex;
              height: 20px;
              align-items: center;
              gap: 2px;

              font-size: 12px;
              font-style: normal;
              font-weight: 400;

              .img {
                width: 16px;
                height: 16px;
              }
            }

            .government {
              color: var(--cyan-kyy_color_cyan_default, #11BDB2);
            }

            .member {
              color: #FC7C14;
            }

            .cbd {
              color: #4D5EFF;
            }

            .association {
              color: #ED565C;
            }

            .school {
              color: #49BBFB;
            }
          }

        }
      }
    }
  }

  .serch-bar {
    display: flex;
    padding: 8px 16px 12px 16px;
    align-items: flex-start;
    gap: 10px;
    align-self: stretch;
    border-bottom: 1px solid var(--divider-kyy_color_divider_light, #ECEFF5);
    background: var(--bg-kyy_color_bg_light, #FFF);

    .name-icon {
      font-size: 20px;
      color: #828DA5;
    }
  }
</style>
