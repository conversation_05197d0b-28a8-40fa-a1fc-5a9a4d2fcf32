import { createClientApiFactory as requestFactory, ringkolRequestForForumAdmin } from "@renderer/utils/apiRequest";
const request = requestFactory("");

import { AxiosResponseData } from "../../model";
import { Topic } from "../../forum/models/topic";
import { CardInfo } from "../../forum/models/user";

const getCommonRequestHeaders = () => ({
  teamId: window.localStorage.getItem("workBenchTeamid"),
  "X-Auth-ID": window.localStorage.getItem("forumAdminOwnerId"),
});

const platformType = "uni";

const Api = {
  isAdmin: "/bbs_extend/platform-user/is-admin",
  managerPostList: "/bbs/v1/postManagement/listPost",
  postDetail: "/bbs/v1/postManagement/getPost",
  managerPostCommentList: "/bbs/v1/postManagement/listComments",
  togglePostPin: "/bbs/v1/postManagement/togglePostPin",
  toggleBlockPost: "/bbs/v1/postManagement/toggleBlockPost",
  managerTopicList: "/bbs/v1/topicManagement/getTopicList",
  topicStatus: "/bbs/v1/topicManagement/updateTopicStatus",
  topicSort: "/bbs/v1/topicManagement/updateTopicSort",
  addTopic: "/bbs/v1/topicManagement/createTopic",
  platformUserList: "/bbs_extend/platform-user/list",
  platformUserPostPermission: "/bbs_extend/platform-user/set-post-permission",
  platformUserCommentPermission: "/bbs_extend/platform-user/set-comment-permission",
  getPlatformSetting: "/bbs_extend/platform-setting/get-post-mode",
  setPlatformSetting: "/bbs_extend/platform-setting/set-post-mode",
};

// 查询是否是论坛管理员
export const checkIsAdmin = () =>
  request.get(`${Api.isAdmin}/${platformType}`, {
    headers: {
      teamId: window.localStorage.getItem("workBenchTeamid"),
    },
  });

// 管理员帖子列表
export const getManagerPostList = (params) =>
  ringkolRequestForForumAdmin.get(Api.managerPostList, {
    params,
    headers: getCommonRequestHeaders(),
  });

// 帖子详情
export const getManagerPostDetail = (id) =>
  ringkolRequestForForumAdmin.get(`${Api.postDetail}/${id}`, {
    headers: getCommonRequestHeaders(),
  });

// 帖子评论列表
export const getManagerPostCommentList = (params) =>
  ringkolRequestForForumAdmin.get(Api.managerPostCommentList, {
    params: {
      ...params,
      owner_id: window.localStorage.getItem("forumAdminOwnerId")
    },
    headers: getCommonRequestHeaders(),
  });

// 顶置/取消顶置帖子
export const setPostSticky = (data) =>
  ringkolRequestForForumAdmin.post(Api.togglePostPin,data,  {
    headers: getCommonRequestHeaders(),
  });

// 屏蔽/取消屏蔽帖子
export const setPostShield = (data) =>
  ringkolRequestForForumAdmin.post(Api.toggleBlockPost,data, {
    headers: getCommonRequestHeaders(),
  });

// 管理员话题列表
export const getManagerTopicList = (data): AxiosResponseData<{ topics: Topic[]; total: number }> =>
  ringkolRequestForForumAdmin.post(Api.managerTopicList, data, {
    headers: getCommonRequestHeaders(),
  });

// 设置话题排序
export const setTopicSort = (data) =>
  ringkolRequestForForumAdmin.post(Api.topicSort, data, {
    headers: getCommonRequestHeaders(),
  });

// 设置话题屏蔽状态
export const setTopicStatus = (data) =>
  ringkolRequestForForumAdmin.post(Api.topicStatus, data, {
    headers: getCommonRequestHeaders(),
  });

// 新建话题
export const addTopic = (data) =>
  ringkolRequestForForumAdmin.post(Api.addTopic, data, {
    headers: getCommonRequestHeaders(),
  });

// 成员列表
export const getManagerPlatformUserList = (params): AxiosResponseData<{ list: CardInfo[]; count: number }> =>
  request.get(`${Api.platformUserList}/${platformType}`, {
    params,
    headers: {
      teamId: window.localStorage.getItem("workBenchTeamid"),
    },
  });

// 设置成员是否禁止发帖
export const setUserPostPermission = (data) =>
  request.post(`${Api.platformUserPostPermission}/${platformType}`, data, {
    headers: { teamId: window.localStorage.getItem("workBenchTeamid") },
  });

// 设置成员是否禁止评论
export const setUserCommentPermission = (data) =>
  request.post(`${Api.platformUserCommentPermission}/${platformType}`, data, {
    headers: { teamId: window.localStorage.getItem("workBenchTeamid") },
  });

// 发布权限获取
export const getPlatformSetting = () =>
  request.get(`${Api.getPlatformSetting}/${platformType}`, {
    headers: {
      teamId: window.localStorage.getItem("workBenchTeamid"),
    },
  });

// 发布权限设置
export const setPlatformSetting = (data) =>
  request.post(`${Api.setPlatformSetting}/${platformType}`, data, {
    headers: {
      teamId: window.localStorage.getItem("workBenchTeamid"),
    },
  });
