import { defineAsyncComponent } from "vue";
import Loading from '@/components/common/Loading.vue';

// const PHome = defineAsyncComponent({
//   loader: ()=> import("./home-panel.vue"),
//   loadingComponent: Loading
// });

const PMember = defineAsyncComponent({
  // loader: () => import("@renderer/views/member/member_number/panel/member-panel.vue"),
  // 2.0修改
  loader: () => import("@renderer/views/member/member_number/panel/member-panel/home-panel.vue"),

  loadingComponent: Loading,
  // delay: 2,
  // delay: 2000,
});

const PPolitics = defineAsyncComponent({
  // loader: () => import("@renderer/views/politics/member_number/panel/member-panel.vue"),
  loader: () => import("@renderer/views/politics/member_number/panel/member-panel/home-panel.vue"),
  loadingComponent: Loading,
  // delay: 2,
  // delay: 2000,
});

const PCbd = defineAsyncComponent({
  // loader: () => import("@renderer/views/cbd/member_number/panel/member-panel.vue"),
  loader: () => import("@renderer/views/cbd/member_number/panel/member-panel/home-panel.vue"),

  loadingComponent: Loading,
  // delay: 2,
  // delay: 2000,
});

const PAssociation = defineAsyncComponent({
  // loader: () => import("@renderer/views/cbd/member_number/panel/member-panel.vue"),
  loader: () => import("@renderer/views/association/member_number/panel/member-panel/home-panel.vue"),

  loadingComponent: Loading,
  // delay: 2,
  // delay: 2000,
});
const PUni = defineAsyncComponent({
  // loader: () => import("@renderer/views/cbd/member_number/panel/member-panel.vue"),
  loader: () => import("@renderer/views/uni/member_number/panel/member-panel/home-panel.vue"),

  loadingComponent: Loading,
  // delay: 2,
  // delay: 2000,
});


const PLeaflets = defineAsyncComponent({
  loader: () => import("./leaflets-panel.vue"),
  loadingComponent: Loading,
  // delay: 2,
  // delay: 2000,
});
const PApply = defineAsyncComponent({
  loader: () => import("./apply-panel.vue"),
  loadingComponent: Loading,
  // delay: 2,
  // delay: 2000,
});
const Forum = defineAsyncComponent({
  loader: () => import("@renderer/views/digital-platform/forum/index.vue"),
  loadingComponent: Loading,
  // delay: 2,
  // delay: 2000,
});

// const PPlatform = defineAsyncComponent({
//   loader:() => import("./platform-panel.vue"),
//   loadingComponent: Loading,
//   // delay: 2,
//   // delay: 2000,
// });




export const panels = {
  PLeaflets, // 宣传页
  PApply, // 应用列表页
  PMember, // 数字商协
  PPolitics, // 城市
  PCbd, // CBD
  PAssociation, // PAssociation,
  PUni,
  Forum
};
