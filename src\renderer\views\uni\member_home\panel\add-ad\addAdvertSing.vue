<template>
  <div class="addAdvertSing">
    <!-- 新增广告 -->
    <div class="banner">
      <div class="header-tab">
        <div class="tab-box">
          <div class="tab-item">
            <div class="flex-box" :class="{ active: activeTab === 0 }">
              <div class="num">1</div>
              <div class="num-text">{{ t("ad.xzggw") }}</div>
            </div>
            <div class="lin"></div>
            <div class="flex-box" :class="{ active: activeTab === 1 }">
              <div class="num">2</div>
              <div class="num-text">{{ t("ad.xztfrq") }}</div>
            </div>
            <div class="lin"></div>
            <div class="flex-box" :class="{ active: activeTab === 2 }">
              <div class="num">3</div>
              <div class="num-text">{{ t("ad.szggnr") }}</div>
            </div>
            <div class="lin"></div>
            <div class="flex-box" :class="{ active: activeTab === 3 }">
              <div class="num">4</div>
              <div class="num-text">{{ t("ad.dptsh") }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="content-box-activeTab0" :class="activeTab === 0?'min-height432px':''" v-show="listData.length > 0 && (activeTab === 0 || activeTab === 3)">
        <div class="tab-activeTab0" v-show="activeTab === 0">
          <div class="tab-activeTab0-btn" @click="changtabFlag(0)" :class="tabFlag === 0 ? 'activeBtn' : ''">
            市场店铺广告
          </div>
          <div class="tab-activeTab0-btn" @click="changtabFlag(1)" :class="tabFlag === 1 ? 'activeBtn' : ''">
            {{ t("ad.ptscgg") }}
          </div>

          <div class="tab-activeTab0-btn" @click="changtabFlag(2)" :class="tabFlag === 2 ? 'activeBtn' : ''">
            {{ t("ad.ptsygg") }}
          </div>
        </div>
        <div v-show="listData.length === 0 && activeTab === 0" style="height: 100%; margin: 0 auto; text-align: center;flex-direction: column;">
          <img style="width: 200px; height: 200px" src="@/assets/member/Rectangle.svg" />
          <div style="font-size: 14px; font-style: normal; font-weight: 400; line-height: 22px">暂无可投放的广告位</div>
        </div>
        <div v-show="listData.length === 0 && activeTab === 0" style="height: 100%; margin: 0 auto; text-align: center;flex-direction: column;">
          <img style="width: 200px; height: 200px" src="@/assets/member/Rectangle.svg" />
          <div style="font-size: 14px; font-style: normal; font-weight: 400; line-height: 22px">暂无可投放的广告位</div>
        </div>
        <div v-show="activeTab === 0" style="display: flex; gap: 16px; position: absolute; top: 58px">
          <div class="content-activeTab0" v-for="(item, index) in listData" :key="index">
            <div class="div-img" @click="viewadfn(item)">
              <img class="img0" v-if="(item.name === 'PC_市场_主轮播图' ||item.name === 'PC_市场店铺_主轮播图' )&& (tabFlag === 1||tabFlag === 0)"
                src="@/assets/member/add1.svg" />
              <img class="img0" v-if="(item.name === 'PC_市场_右上'||item.name === 'PC_市场店铺_右上') && (tabFlag === 1||tabFlag === 0)"
                src="@/assets/member/add2.svg" />
              <img class="img0" v-if="(item.name === 'PC_市场_右下'||item.name === 'PC_市场店铺_右下') && (tabFlag === 1||tabFlag === 0)"
                src="@/assets/member/add3.svg" />
              <img class="img0" v-if="(item.name === 'APP_市场_主轮播图'||item.name === 'APP_市场店铺_主轮播图'  )&& (tabFlag === 1||tabFlag === 0)"
                src="@/assets/member/add4.svg" style="object-fit: contain;" />
              <img class="img0" v-if="item.name === 'PC_平台_主轮播图' && tabFlag === 2" src="@/assets/member/app5.svg" />
              <img class="img0" v-if="item.name === 'PC_平台_右上' && tabFlag === 2" src="@/assets/member/app6.svg" />
              <img class="img0" v-if="item.name === 'PC_平台_右下' && tabFlag === 2" src="@/assets/member/app7.svg" />
              <img class="img0" v-if="item.name === 'APP_平台_主轮播图' && tabFlag === 2" src="@/assets/member/app8.svg"
                style="object-fit: contain;" />
            </div>
            <div class="text0">{{ item?.name }}</div>
            <div class="text-price">
              <span style="font-size: 12px">{{ item?.symbol == "¥" ? "¥" : "MOP" }}</span>
              <span>{{
                item.price_count == 1 ? item.min_price_text : `${item.min_price_text}~${item.max_price_text}`
                }}</span>
              <span style="font-size: 12px">/天</span>
            </div>
            <t-button class="tfbtn" theme="primary" @click="tf(item, index)"> {{ t("ad.touf") }}</t-button>
          </div>
        </div>
        <div class="lastsue" v-show="activeTab === 3">
          <img src="@/assets/member/icon_success.svg" />
          <div class="addsec">{{ t("ad.dptsh") }}</div>
          <div class="labsec" style="overflow: inherit; white-space: inherit">
            <div style="display: flex; flex-wrap: wrap">
              <div>{{ t("ad.title") }}: </div>
              <div style="width: 372px">{{ callBackFormValue?.title }}</div>
            </div>
          </div>
          <div class="labsec">
            {{ t("ad.placementDate") }}: {{ dayPriceData?.timeValue[0] }}～{{ dayPriceData?.timeValue[1] }}
          </div>
          <div class="labsec">{{ t("ad.tfwz") }}: {{ listData[imgIndex]?.name }}</div>
          <div class="footers">
            <t-button
              style="min-width: 80px;font-weight: 600"
              theme="default"
              variant="outline"
              @click="viewAd"
              v-show="activeTab === 3"
            >
              {{ t("ad.viewDetails") }}
            </t-button>
            <t-button style="min-width: 80px;font-weight: 600;" theme="primary" @click="nextFn" v-show="activeTab === 3">
              {{ t("ad.fhlb") }}
              <!-- cbd -->
            </t-button>
          </div>
        </div>
      </div>
      <!-- <div class="content-box-activeTab0"  v-show="listData.length === 0 && activeTab === 0">
        <div style="height: 100%; margin: 0 auto; text-align: center">
          <img style="width: 200px; height: 200px" src="@/assets/member/Rectangle.svg" />
          <div style="font-size: 14px; font-style: normal; font-weight: 400; line-height: 22px">{{t('ad.zwktfggw')}}</div>
        </div>
      </div> -->
      <div class="content-box-activeTab1">
        <div :class="activeTab === 1 ? 'h528' : 'h688'" class="left-box" v-show="activeTab === 1 || activeTab === 2">
          <div class="div-img">
            <img class="img0" v-if="(imgItem?.name === 'PC_市场_主轮播图' ||imgItem?.name === 'PC_市场店铺_主轮播图' )&& (tabFlag === 1||tabFlag === 0)"
              src="@/assets/member/add1.svg" />
            <img class="img0" v-if="(imgItem?.name === 'PC_市场_右上'||imgItem?.name === 'PC_市场店铺_右上') && (tabFlag === 1||tabFlag === 0)"
              src="@/assets/member/add2.svg" />
            <img class="img0" v-if="(imgItem?.name === 'PC_市场_右下'||imgItem?.name === 'PC_市场店铺_右下') && (tabFlag === 1||tabFlag === 0)"
              src="@/assets/member/add3.svg" />
            <img class="img0" v-if="(imgItem?.name === 'APP_市场_主轮播图'||imgItem?.name === 'APP_市场店铺_主轮播图') && (tabFlag === 1||tabFlag === 0)"
              src="@/assets/member/add4.svg" style="object-fit: contain;" />
            <img class="img0" v-if="imgItem?.name === 'PC_平台_主轮播图' && tabFlag === 2" src="@/assets/member/app5.svg" />
            <img class="img0" v-if="imgItem?.name === 'PC_平台_右上' && tabFlag === 2" src="@/assets/member/app6.svg" />
            <img class="img0" v-if="imgItem?.name === 'PC_平台_右下' && tabFlag === 2" src="@/assets/member/app7.svg" />
            <img class="img0" v-if="imgItem?.name === 'APP_平台_主轮播图' && tabFlag === 2" src="@/assets/member/app8.svg"
              style="object-fit: contain;" />
          </div>
          <div class="activeTab1-title">
            {{ imgItem?.name }}
          </div>
          <div class="activeTab1-info" v-if="dayPriceData && activeTab === 2">
            <div class="lable-active">{{ t("ad.tfrq") }}</div>
            <div class="value-active">{{ `${dayPriceData.timeValue[0]}～${dayPriceData.timeValue[1]}` }}</div>
            <div class="lable-active">{{ t("ad.duration") }}</div>
            <div class="value-active">{{ dayPriceData.totalDay }}天</div>
            <div class="lable-active">{{ t("ad.cost") }}</div>
            <div class="value-active">{{ dayPriceData.symbol == "¥" ? "¥" : "MOP" }} {{ dayPriceData.price }}</div>
          </div>
        </div>
        <div class="right-box">
          <div class="content3" v-show="activeTab === 2">
            <editFormDataPage
              ref="editFormDataPageRef"
              v-if="activeTab !== 0"
              :tabFlag="tabFlag"
              :imgTypeIndex="imgIndex + 1"
              :imgItemName="imgItem?.name"
              @callBackFormData="callBackFormData"
              @onSave="onSave"
              @updateImg="updateImg"
            ></editFormDataPage>
          </div>
          <priceCalendar
            @restImgIndex="activeTab = 0"
            v-if="activeTab !== 0"
            :setId="imgItem?.id"
            @subTime="subTime"
            ref="priceCalendarRef"
            :priceCalendar="'front'"
            v-show="activeTab === 1"
          >
          </priceCalendar>
          <div class="footer-box" v-if="activeTab === 1 || activeTab === 2">
            <t-button
              style="min-width: 80px"
              theme="default"
              variant="outline"
              @click="backFn"
              v-show="activeTab === 2 || activeTab === 1"
            >
              {{ t("ad.syb") }}
            </t-button>
            <t-button
              style="min-width: 80px"
              theme="default"
              variant="outline"
              v-show="activeTab === 2 && viewImgUrl"
              @click="viewFn"
            >
              {{ t("ad.view") }}
            </t-button>
            <t-button
              :loading="loading"
              style="min-width: 80px"
              theme="primary"
              @click="nextFn"
              v-show="activeTab !== 3"
            >
              {{ t("ad.nextStep") }}
            </t-button>
          </div>
        </div>
      </div>
    </div>
    <viewAdPage ref="viewAdRef"></viewAdPage>
    <viewAdImg ref="viewAdImgref"></viewAdImg>

    <!-- 产品需求暂时取消 -->
    <costDetails ref="costDetailsRef"> </costDetails>
  </div>
</template>
<script setup lang="ts" name="uni_addAd">
import {
  adfrontspaceselectlist,
  adfrontpublish,
  existvacancy,
  frontspacespaceplatformlist,
} from "@renderer/api/member/api/ebookApi";
import viewAdPage from "@/views/member/member_home/panel/mark-advertising/components/viewAd.vue";

import editFormDataPage from "@/views/member/member_home/panel/mark-advertising/components/editFormData.vue";
import costDetails from "@/views/member/member_home/panel/mark-advertising/components/costDetails.vue";
import { ref, watch, toRaw, computed } from "vue";
import { DialogPlugin, MessagePlugin } from "tdesign-vue-next";
import priceCalendar from "@/components/priceCalendar/index.vue";
import { useDigitalPlatformStore } from "@renderer/views/digital-platform/store/digital-platform-store";
import { onMountedOrActivated } from "@renderer/hooks/onMountedOrActivated";
import { useMemberStore } from "@renderer/views/member/store/member";
import { useRouter, useRoute } from "vue-router";
import useRouterHelper from "@renderer/views/square/hooks/routerHelper";
const { menuList, routeList, roleFilter } = useRouterHelper("digitalPlatformIndex");
const router = useRouter();
const route = useRoute();
const digitalPlatformStore = useDigitalPlatformStore();
let cardID = null;
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const activeTab = ref(0);
const imgIndex = ref(0);
const imgItem = ref(null);
const viewAdRef = ref(null);
const costDetailsRef = ref(null);
const priceCalendarRef = ref(null);
const timeArr = ref([]);
const listData = ref([]);
let currentMemberCard = null;

const changtabFlag = (val) => {
  tabFlag.value = val;
  activeTab.value = 0;
  imgIndex.value = 0;
  imgItem.value = null;
  getSpaceselectlist();
};
watch(
  () => activeTab.value,
  async (newValue, oldValue) => {
    //  重置数据
    if (newValue === 0) {
      imgItem.value = null;
      imgIndex.value = 0;
      dayPriceData.value = null;
      getSpaceselectlist();
    }
  },
);
watch(
  () => route.query,
  (newValue, oldValue) => {
    console.log(newValue, "newValuenewValuenewValue1111");
    //  重置数据
    if (newValue.restData && route.path === "/digitalPlatformIndex/uni_addAd") {
      activeTab.value = 0;
    }
  },
);

const backFn = () => {
  activeTab.value--;
};
let platform_type = null;

const viewImgUrl = ref("");
const updateImg = (url) => {
  viewImgUrl.value = url;
};
import viewAdImg from "@renderer/views/member/member_home/panel/mark-advertising/components/viewAdImg.vue";

const viewAdImgref=ref(null)

const viewadfn = (row) => {
  viewAdImgref.value?.openWin({
    tabFlag: tabFlag.value,
    imgItem: {
      name: row.name,
    },
  });
};
const dayPriceData = ref(null);
const callBackFormValue = ref(null);
const resData = ref(null);
const callBackFormData = async (val) => {
  try {
    let str = "";
    if (val.skip_type === 1) {
      str = val.skip_param.uuid;
    } else if (val.skip_type === 2) {
      str = val.skip_param.squareId;
    } else if (val.skip_type === 3) {
      str = val.skip_param;
    }else if (val.skip_type === 5) {
        str = val.skip_param.spuId;
      }
    callBackFormValue.value = val;
    console.log(val, "qingqiujiekou");
    let objs = {
      // 1商机 2广场 3自定义连接 4无跳转
      set_id: imgItem.value?.id,
      begin_at: dayPriceData.value?.timeValue[0],
      end_at: dayPriceData.value?.timeValue[1],
      title: val.title,
      main_body_id: route.query.currentMemberCard,
      image_url: val.image_url,
      skip_type: val.skip_type,
      skip_param: str,
      remark: val.remark,
      platform_type,
    };

    const res = await adfrontpublish(objs, teamId);
    console.log(res, "awaitawaitasdasdasdasd");
    resData.value = res;
    activeTab.value = 3;
  } catch (error) {
    if (error.code === 90014) {
      editFormDataPageRef.value.closeWin();
    }

    MessagePlugin.error(error?.message);
  }
};
let loading = ref(false);
import { debounce } from "lodash";
const nextFn = debounce(async () => {
  if (loading.value) {
    return;
  }
  console.log(route, "activationGroupItemactivationGroupItemrouteroute");
  if (activeTab.value === 1) {
    if (loading.value) {
      return;
    }
    if (!dayPriceData.value) {
      if (dayPriceData.value?.timeValue.length === 1) {
        MessagePlugin.error(t("ad.qxztfrq"));
        return;
      }
        MessagePlugin.error(t("ad.qxztfrq"));
        return;
    }

    loading.value = true;
    existvacancy({
      set_id: imgItem.value?.id,
      begin_at: dayPriceData.value?.timeValue[0],
      end_at: dayPriceData.value?.timeValue[1],
    })
      .then((res) => {
        if (!res.data.data.open) {
          const myDialog = DialogPlugin({
            header: t("ad.ggwyjy"),
            theme: "info",
            body: t("ad.adPositionDisabledtip1"),
            className: "dialog-classp32",
            confirmBtn: t("ad.xzggw"),
            cancelBtn: null,
            closeBtn: null,
            closeOnOverlayClick: false,
            onConfirm: () => {
              activeTab.value = 0;
              myDialog.hide();
            },
          });
          return;
        }
        if (!res.data.data.not_full) {
          const myDialog = DialogPlugin({
            header: t("ad.adPositionFull"),
            theme: "info",
            body: t("ad.selectDate"),
            className: "dialog-classp32",
            closeBtn: null,
            cancelBtn: null,
            confirmBtn: t("ad.xztfrq"),
            closeOnOverlayClick: false,
            onConfirm: () => {
              myDialog.hide();
              dayPriceData.value = null;
              activeTab.value = 1;
              // editFormDataPageRef.value.onClose();
              if (priceCalendarRef.value) {
                priceCalendarRef.value.restPriceCalendar();
                priceCalendarRef.value.getAllPrice();
              }
            },
          });
          return;
        }

        activeTab.value++;

        return;
      })
      .finally(() => {
        loading.value = false;
      });

    return;
  }
  if (activeTab.value === 2) {
    if (loading.value) {
      return;
    }
    loading.value = true;
    existvacancy({
      set_id: imgItem.value?.id,
      begin_at: dayPriceData.value?.timeValue[0],
      end_at: dayPriceData.value?.timeValue[1],
    })
    .then((res) => {
        if (!res.data.data.open) {
          const myDialog = DialogPlugin({
            header: t("ad.ggwyjy"),
            theme: "info",
            body: t("ad.adPositionDisabledtip1"),
            className: "dialog-classp32",
            confirmBtn: t("ad.xzggw"),
            cancelBtn: null,
            closeBtn: null,
            closeOnOverlayClick: false,
            onConfirm: () => {
              activeTab.value = 0;
              myDialog.hide();
            },
          });
          return;
        }
        if (!res.data.data.not_full) {
          const myDialog = DialogPlugin({
            header: t("ad.adPositionFull"),
            theme: "info",
            body: t("ad.selectDate"),
            className: "dialog-classp32",
            closeBtn: null,
            cancelBtn: null,
            confirmBtn: t("ad.xztfrq"),
            closeOnOverlayClick: false,
            onConfirm: () => {
              myDialog.hide();
              dayPriceData.value = null;
              activeTab.value = 1;
              // editFormDataPageRef.value.onClose();
              if (priceCalendarRef.value) {
                priceCalendarRef.value.restPriceCalendar();
                priceCalendarRef.value.getAllPrice();
              }
            },
          });
          return;
        }

        onSave();
        return;
      })
      .finally(() => {
        loading.value = false;
      });
  }
  if (activeTab.value === 3) {
    const query = {
      platform:route.path.includes("/workBenchIndex")?"digital_workbench":"digital_platform",
      teamId: teamId,
      tab:'advertisement',
      ...props.activationGroupItem,
      user_ids: JSON.stringify(store.value.activeAccount.user_ids),

        };

    router.push({
      path: `/digitalPlatformIndex/digital_platform_uni_my?teamId=${teamId}&tab=advertisement`,
      query
    });
    const searchMenu = routeList.find((v) => v.name === "digital_platform_uni_my");
    store.value.removeTabItem(route.name);

    store.value.addTab(
      toRaw({
        ...searchMenu,
        query
      }),
    );
    console.log(teamId, "teamIdteamIdteamIdteamId");
    // ipcRenderer.invoke("set-work-bench-tab-item", {
    //   path: `/workBenchIndex/member_home`,
    //   name: "member_home",
    //   path_uuid: "member",
    //   title: "数字商协",
    //   type: 21,
    // });
    return;
  }
}, 300);

const props = defineProps({
  activationGroupItem: {
    type: Object,
    default: () => {},
  },
});

let teamId = null;
const tabFlag = ref(1);

const getSpaceselectlist = () => {
  if (tabFlag.value === 1) {
    adfrontspaceselectlist(
      {
        platform_type,
      },
      teamId,
    ).then((res) => {
      listData.value = res.data.data.list;
      console.log(res, "23123123");
    });
  } else {
    frontspacespaceplatformlist(
      {
        ad_type : tabFlag.value === 0?3:2,
        platform_type,
      },
      teamId,
    ).then((res) => {
      listData.value = res.data.data.list;
      console.log(res, "23123123");
    });
  }
};
const store = computed(() => {
  // return useMemberStore();
  return digitalPlatformStore;
});
onMountedOrActivated(() => {
  teamId = route.query.teamId;
  platform_type = route.query.platform_type;
  tabFlag.value = route.query.form === "mark" ? 1 : 2;

  // if (route.query.currentMemberCard !== currentMemberCard) {
  //   currentMemberCard = route.query.currentMemberCard;
  //   imgIndex.value = 0;
  //   activeTab.value = 0;
    console.log(route.query,"routequeryroutequery");
  // }
  getSpaceselectlist();
});
const viewAd = () => {
  router.push({
    path: `/digitalPlatformIndex/uni_adDetails?id=${resData.value.data.data.id}&teamId=${teamId}`,
    query: {
      id: resData.value.data.data.id,
      teamId: teamId,
    },
  });

  store.value.removeTabItem(route.name);
  const searchMenu = routeList.find((v) => v.name === "uni_adDetails");
  store.value.addTab(
    toRaw({
      ...searchMenu,
      query: {
        id: resData.value.data.data.id,
        teamId: teamId,
      },
    }),
    true,
  );
};
const editFormDataPageRef = ref(null);
const onSave = () => {
  editFormDataPageRef.value.onSave();
};

const subTime = (val) => {
  dayPriceData.value = val;
  console.log(val, "选择的时间");
  // timeArr.value = val;
};
const viewFn = () => {
  viewAdRef.value.openWin({
    url: viewImgUrl.value,
    imgItem: imgItem.value,
    imgIndex: imgIndex.value,
    tabFlag: tabFlag.value,
  });
};
const openMx = (id) => {
  costDetailsRef.value.openWin(dayPriceData.value, id, teamId);
};
const tf = (item, index) => {
  if (tabFlag.value === 1) {
    adfrontspaceselectlist(
      {
        platform_type,
      },
      teamId,
    ).then((res) => {
      const resData = res.data.data.list.find((res) => res.id === item.id);
      if (!resData) {
        getSpaceselectlist();
        MessagePlugin.warning(t("ad.ggwyjy"));

        return;
      } else {
        imgIndex.value = index;
        activeTab.value = 1;
        imgItem.value = item;
        console.log(item, "itemmmm");
        if (priceCalendarRef.value) {
          priceCalendarRef.value.getAllPrice();
        }
      }
    });
  } else {
    frontspacespaceplatformlist(
      {
        ad_type : tabFlag.value === 0?3:2,
        platform_type,
      },
      teamId,
    ).then((res) => {
      const resData = res.data.data.list.find((res) => res.id === item.id);
      if (!resData) {
        getSpaceselectlist();
        MessagePlugin.warning(t("ad.ggwyjy"));

        return;
      } else {
        imgIndex.value = index;
        activeTab.value = 1;
        imgItem.value = item;
        console.log(item, "itemmmm");
        if (priceCalendarRef.value) {
          priceCalendarRef.value.getAllPrice();
        }
      }
    });
  }
};
</script>
<style lang="less" scoped>
.addAdvertSing {
  background: url("@/assets/member/bg_big.svg") no-repeat 100%;
  background-size: cover;
  background-position: center;
  .h528 {
    height: 528px;

  }
  .h688 {
    height: calc(100vh - 148px);
  }
  .tab-item {
    display: flex;
    align-items: center;
  }
  .tab-box {
    display: flex;
    align-items: center;
    .flex-box {
      display: flex;
      align-items: center;
    }
    .active {
      .num {
        color: #fff;
        background: #4d5eff;
      }
      .num-text {
        color: #4d5eff;
      }
    }
    .num {
      width: 28px;
      height: 28px;
      line-height: 28px;
      background: #f5f8fe;
      border-radius: 50%;
      color: #828da5;
      color: var(--text-kyy_color_text_3, #828da5);
      text-align: center;
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      margin-right: 8px;
    }
    .num-text {
      color: var(--text-kyy_color_text_2, #516082);
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 24px; /* 150% */
    }
    .lin {
      display: flex;
      width: 112px;
      height: 1px;
      margin: 0 16px;
      flex-direction: column;
      align-items: flex-start;
      background: #d5dbe4;
    }
  }
  .banner {
    /* padding: 0 16px; */
  }
  .content3 {
    padding: 9px 20px 0;
    height: calc(100vh - 205px);
    overflow-y: auto;
    overflow-x: hidden;
    border-radius: 8px 8px 0 0;
    border: 1px solid var(--border-kyy_color_border_white, #fff);
    background: var(--bg-kyy_color_bg_light, #fff);
}
  .tab-box {
    margin: 16px auto;
    color: var(--text-kyy_color_text_2, #516082);
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px; /* 150% */
  }
  .header-tab {
    width: 1184px;
    border-radius: 8px;
    border: 1px solid var(--border-kyy_color_border_white, #fff);
    background: rgba(255, 255, 255, 0.5);
    backdrop-filter: blur(2px);
    display: flex;
    padding: 16px 0px;
    justify-content: center;
    align-items: center;
      margin: 16px auto 16px;
      height: 60px;
  }
  .div-img {
    padding: 16px;
    border: 1px solid transparent;

    margin-bottom: 4px;
  }
  .lable-active {
    color: var(--text-kyy_color_text_3, #828da5);

    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
    margin-bottom: 4px;
  }
  .footers {
    display: flex;
    align-items: center;
    gap: 12px;
    padding-top: 12px;
  }
  .value-active {
    color: var(--text-kyy_color_text_1, #1a2139);

    font-size: 14px;
    font-style: normal;
    margin-bottom: 12px;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
    .mx {
      color: var(--color-button_text_brand-kyy_color_button_text_brand_font_default, #4d5eff);
      text-align: center;
      font-size: 14px;
      font-style: normal;
      cursor: pointer;
      margin-left: 12px;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
    }
  }
  .tab-activeTab0 {
    display: flex;
    position: absolute;
    top: 16px;
    align-items: center;
    border-radius: 4px;
    background: var(--bg-kyy_color_bg_light, #fff);
    justify-content: center;
    height: 36px;
    padding: 4px;
    width: 332px;
  }
  .tab-activeTab0-btn {
    display: flex;
    padding: 3px 12px;
    border-radius: 4px;
    height: 100%;
    cursor: pointer;
    justify-content: center;
    align-items: center;
    color: var(--text-kyy_color_text_1, #1a2139);
    font-size: 14px;
    font-style: normal;width: 110px;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
  .tab-activeTab0-btn:hover{
    background:  #EAECFF;
  }
  .activeBtn {
    border-radius: 4px;
    color: #fff;
    background: var(--brand-kyy_color_brand_default, #4d5eff) !important;
  }
  .lastsue {
    display: flex;
    padding: 48px;
    width: 528px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin: 55px auto;
    border-radius: 8px;
    background: var(--bg-kyy_color_bg_light, #fff);
    img {
      width: 48px;
      height: 48px;
      margin: 0 auto 16px;
    }
    .labsec {
      color: var(--text-kyy_color_text_1, #1a2139);
      font-size: 14px;
      font-style: normal;
      margin-bottom: 12px;
      font-weight: 400;
      line-height: 22px;
      width: 100%;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      word-break: break-all;
    }
    .addsec {
      color: var(--text-kyy_color_text_1, #1a2139);
      text-align: center;
      width: 100%;
      margin-bottom: 24px;
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: 22px; /* 157.143% */
    }
  }
  .content-box-activeTab1 {
    display: flex;
    align-items: flex-start;
    justify-content: center;
    margin: 0 auto;
    gap: 16px;
    .footer-box {
      display: flex;
      width: 912px;
      padding: 12px 24px;
      align-items: flex-start;
      gap: 8px;
      background: #fff;
      border-top: 1px solid var(--divider-kyy_color_divider_light, #eceff5);
      border-radius: 0 0 8px 8px;
    }
    .left-box {
      width: 256px;
    overflow: auto;
    border-radius: 8px;
    border: 1px solid var(--border-kyy_color_border_white, #fff);
    padding: 16px 0 12px;
    background-repeat: no-repeat;
    background: url('@/assets/member/left_bg.png') no-repeat 100% 100%;
    background-size: 100%;
    background-color: #fff;
      .div-img:hover {
        border: none !important;
      }
      .div-img {
        padding: 0 16px !important;
        border: none !important;
        margin-bottom: 12px;
      }
      .img0 {
        width: 100%;
        height: 100%;
      }
      .activeTab1-info {
        display: flex;
        padding: 0 12px;
        flex-direction: column;
        align-items: flex-start;
        align-self: stretch;
        border-top: 1px solid var(--divider-kyy_color_divider_light, #eceff5);
        padding-top: 8px;
      }
      .activeTab1-title {
        color: #1a2139;
        font-size: 14px;
        padding: 0 12px;
        font-style: normal;
        font-weight: 600;
        line-height: 22px; /* 157.143% */
        margin-bottom: 12px;
      }
    }
    .right-box {
    }
  }
  .img0 {
    width: 234px;
    height: 134px;
  }
  .tfbtn {
    min-width: 242px;
    margin: 0 auto 12px;
    display: block;
  }
  .div-img:hover {
    border: 1px solid var(--brand-kyy_color_brand_default, #4d5eff);
    border-radius: 8px;
  }
  .text-price {
    color: var(--error-kyy_color_error_default, #d54941);
    text-overflow: ellipsis;
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    display: flex;
    padding-left: 8px;
    align-items: center;
    line-height: 24px; /* 150% */
    margin-bottom: 20px;
    display: flex;
    align-items: baseline;
  }
  .text0 {
    color: #1a2139;
    font-size: 14px;
    padding-left: 8px;
    font-style: normal;
    width: 242px;
    font-weight: 600;
    line-height: 22px; /* 157.143% */
  }
  .content-box-activeTab0 {
    min-height: 525px;
    border-radius: 8px;
    border: 1px solid var(--border-kyy_color_border_white, #fff);
    background: url("@/assets/member/content_bg.svg") no-repeat 100%;

    backdrop-filter: blur(2px);
    /* height: 328px; */
    display: flex;
    padding: 16px 20px;
    align-items: center;
    width: 1184px;
    flex-wrap: wrap;
    margin: 0 auto;
    gap: 16px;
    .content-activeTab0 {
      padding: 8px;
      width: 274px;
      border-radius: 8px;
      position: relative;
      background: var(--bg-kyy_color_bg_light, #fff);
    }
  }
}
::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}
/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  border-radius: 3px;
  background: rgba(0, 0, 0, 0.1);
  -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.5);
}
.min-height432px{
    min-height: 432px !important;
  }
</style>
