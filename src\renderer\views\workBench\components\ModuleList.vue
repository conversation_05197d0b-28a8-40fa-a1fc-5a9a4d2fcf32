<template>
  <div class="bench-module-box" :class="{
      benchmoduleboxdl: allLayoutList[0]?.id === 2,
    }">
    <wc-waterfall gap="16" cols="2">
      <div v-for="(item, index) in tabList" :key="index">
        <component :allAppList="allAppList" @getList="getList" @setWorkBenchTabItem="setWorkBenchTabItem"
          :authority="authority" class="isAllappList" :class="tabList.length > 1 ? 'w100%' : 'w100%'"
         :activationGroupItem="activationGroupItem" :checkIsAdminData="checkIsAdminData"
          :module="item.value" :is="item.component"></component>
      </div>
    </wc-waterfall>
  </div>
  <ComponentLayout :allLayoutList="allLayoutList" @updLayoutList="updLayoutList" ref="componentLayoutDialog"
    :activationGroupItem="activationGroupItem"></ComponentLayout>
</template>

<script setup lang="ts">
  import { getLang } from "@/utils/auth";
  import { useI18n } from "vue-i18n";
  import { ref, onMounted, computed, watch, nextTick, onUnmounted, Ref } from "vue";
  import draggable from "vuedraggable";
  import { AddIcon } from "tdesign-icons-vue-next";
  import Notice from "./notice.vue";
  import EbookWall from "./EbookWall.vue";
  import AllappItem from "./AllappItem.vue";
  import { onMountedOrActivated } from "@/hooks/onMountedOrActivated";
  import { DialogPlugin, MessagePlugin } from "tdesign-vue-next";
  import {
    AppAllApp,
    AllApp,
    staffApp,
    teamcompanysTotal,
    checkIsAdmin,
    workshopStaffSort,
    workshopStaff,
  } from "@renderer/api/workBench/index.ts";
  import activityList from "./activityList.vue";

  import ComponentLayout from "../components/ComponentLayout.vue";
  import { useRouter } from "vue-router";

  import allapp1 from "@/assets/bench/allapp1.svg";
  import { authbymember } from "@renderer/api/notice/index.ts";

  import allapp from "@/assets/bench/allapp.svg";
  import LynkerSDK from '@renderer/_jssdk';

  const { ipcRenderer, shell } = LynkerSDK;
  const router = useRouter();
  const componentLayoutDialog = ref(false);
  const { t } = useI18n();
  const props = defineProps({
    activationGroupItem: {
      type: Object,
    },
    checkIsAdminData: {
      type: Object,
    },
  });

  interface Option {
    component: null;
    value: any;
  }
  // const staffAppList=ref([])
  const routerViewRefs = ref([]);
  const emits = defineEmits(["updisPromotionalpage", "setWorkBenchTabItem"]);
  const numList = ref([]);
  const allLayoutList = ref([]);

  const tabList: Ref<Array<Option>> = ref([]);
  const allAppList = ref([]);
  const AllappItemref = ref(null);
  const routerViewRef = ref(null);

  const Noticeref = ref(null);
  const updLayoutList = (val) => {
    getList(true);
  };
  const setWorkBenchTabItem = (val) => {
    emits("setWorkBenchTabItem", val);
  };

  const dragAreaChange = async (val) => {
    try {
      let arr = allLayoutList.value.map((res) => res.id);
      const res = await workshopStaffSort({ ids: arr }, props.activationGroupItem.teamId || localStorage.getItem("workBenchTeamid"));
      MessagePlugin.success("操作成功");
      // emits("updLayoutList");
      updLayoutList();
    } catch (error) {
      MessagePlugin.error(error.response.data.message);
    }
  };


  watch(
    () => props.activationGroupItem.teamId,
    (newValue) => {
      getList(true);
    },
  );

  ipcRenderer.on("update-approve", (val, i) => {
    getList();
  });

  ipcRenderer.on("update-niche-reddot", (val, i) => {
    getList();
  })



  ipcRenderer.removeAllListeners("IM-refresh");
  ipcRenderer.on("IM-refresh", (val, i) => {
    if (props.activationGroupItem.teamId) {
      let i = 0;
      console.log('Module-list lss', i++)
      getList();
    }
  });
  onMountedOrActivated(() => {
    if (props.activationGroupItem.teamId) {
      getList();
    }
  });
  const updLayoutFn = (val) => {
    // 暂时注释
    // tabList.value = [AllappItem, Notice];
    console.log(allLayoutList.value, "allLayoutList.valueallLayoutList.value");
    tabList.value = allLayoutList.value
      .map((element) => {
        let comp = null;
        switch (element?.module?.uuid) {
          case "app":
            comp = AllappItem;
            break;
          case "notice":
            comp = Notice;
            break;
          // case "culturalWall":
          //   comp = CultureWall;
          //   break;
          case "government":
            comp = EbookWall;
            break;
          case "member":
            comp = EbookWall;
            break;
              case "uni":
            comp = EbookWall;
            break;
          case "association":
            comp = EbookWall;
            break;
          // /注释
          case "activities":
            comp = activityList;
            break;
          case "cbd":
            comp = EbookWall;
            break;
          default:
            comp = null;
        }
        return {
          component: comp,
          value: element?.module,
        };
      })
      .filter((item) => item !== null);
  };
  const authority = ref(null);
  const getList = async (isLayout) => {
    authbymember().then((res) => {
      authority.value = res.data.data;
    });
    console.log(props.activationGroupItem, "啊实打实大师大师大");
    try {
      const res1 = await Promise.all([
        workshopStaff(props.activationGroupItem.teamId || localStorage.getItem("workBenchTeamid")),
        teamcompanysTotal(props.activationGroupItem.teamId || localStorage.getItem("workBenchTeamid")),
      ]);
      //暂时添加
      allLayoutList.value = res1[0].data.data;

      if (isLayout) {
        updLayoutFn();
      } else {
        tabList.value = res1[0].data.data
          .map((element) => {
            let comp = null;
            switch (element?.module?.uuid) {
              case "app":
                comp = AllappItem;
                break;
              case "notice":
                comp = Notice;
                break;
              case "activities":
                comp = activityList;
                break;
              case "government":
                comp = EbookWall;
                break;
              case "association":
                comp = EbookWall;
                break;
                   case "uni":
                comp = EbookWall;
                break;
              case "member":
                comp = EbookWall;
                break;
              case "cbd":
                comp = EbookWall;
                break;
              default:
                comp = null;
            }

            return {
              component: comp,
              value: element?.module,
            };
          })
          .filter((item) => item !== null);
        // tabList.value = allLayoutList.value;
      }
      numList.value = res1[1].data.data;
      const res2 = await Promise.all([
        AppAllApp(props.activationGroupItem.teamId || localStorage.getItem("workBenchTeamid")),
        // staffApp(props.activationGroupItem.teamId || localStorage.getItem("workBenchTeamid")),
      ]);
      for (let element of res2[0].data.data) {
        for (let item of numList.value) {
          if (element.uuid === item.uuid) {
            element.count = item.count;
            break;
          }
        }
      }
      allAppList.value = res2[0].data.data
      console.log(res2[0].data.data, "res2[0].data.data");
      // allAppList.value.push({
      //   uuid: "ad-lk",
      //   name: "另可广告",
      //   path_uuid:'ad-lk',
      //   icon: "ringkolAdvertisement",
      //   type: 1,
      // })

      // for (let element of res2[1].data.data) {
      //   for (let item of numList.value) {
      //     if (element.uuid === item.uuid) {
      //       element.count = item.count;
      //       break;
      //     }
      //   }
      // }
      // staffAppList.value = res2[1].data.data;
    } catch (error) {
      console.log(error, 'rrrrrrrrrrrrrrr');

      MessagePlugin.error(error.response.data.message);
    }
  };
  const goPath = () => {
    emits("updisPromotionalpage");
  };
</script>

<style lang="less" scoped>
  .benchmoduleboxdl {
    flex-direction: row-reverse;
  }

  :deep(.t-badge--circle) {
    top: 5px !important;
    right: 3px !important;
  }

  .component-layout-box {
    .w184 {
      width: 184px;
      padding-left: 30px;
    }

    .drawer-box {
      /* padding: 0 8px; */
    }

    .close {
      width: 22px;
      height: 22px;
      cursor: pointer;
    }

    .drawer-item {
      width: 100%;
      display: flex;
      padding: 6px 8px;
      align-items: center;
      gap: 12px;
      align-self: stretch;
      border-radius: 8px;
      justify-content: space-between;
      padding-bottom: 4px;
      position: relative;
      margin-top: 8px;
    }

    .drawer-item:hover {
      background: var(--bg-kyy_color_bg_list_hover, #f3f6fa);
    }

    .item-text {
      color: var(--text-kyy_color_text_1, #1a2139);
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      /* 157.143% */
    }

    .drawer-item::after {
      content: "";
      width: 100%;
      height: 1px;
      position: absolute;
      bottom: -4px;
      left: 0;
      background: #eceff5;
    }

    .drawer-item-title {
      display: flex;
      align-items: center;
      gap: 12px;

      .app-icon {
        width: 36px;
        height: 36px;
      }
    }

    .btn {
      display: flex;
      align-items: center;
      gap: 16px;
      cursor: pointer;
      color: var(--color-button_text_brand-kyy_color_button_text_brand_font_default, #4d5eff);
      text-align: center;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      /* 157.143% */
    }

    .move {
      cursor: move;
      width: 28px;
      height: 28px;
      text-align: center;
      line-height: 28px;
      padding: 4px;
    }

    .move:hover {
      border-radius: 4px;
      background: var(--bg-kyy_color_bgBrand_hover, #eaecff);
      color: #707eff;
    }

    .btn:hover {
      color: #707eff;
    }

    .header-diy {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 24px 8px 0;
      width: 100%;

      .title {
        font-size: 16px;

        font-weight: 700;
        text-align: left;
        color: #13161b;
      }
    }

    .header-diy {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 24px 8px 0;
      width: 100%;

      .title {
        font-size: 16px;

        font-weight: 700;
        text-align: left;
        color: #13161b;
      }
    }

    .component-layout-header {
      display: flex;
      align-items: center;
      gap: 1px;
      margin-bottom: 12px;

      div {
        background: var(--kyy_color_table_hrading_bg, #e2e6f5);
        padding: 12px;
        color: var(--kyy_color_table_hrading_text, #516082);
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
      }
    }
  }

  .wrap {
    display: flex;
    flex-wrap: wrap;
  }

  .bench-module-box {
    // background-color: #f5f8fe;
    // padding: 0 8px 8px 16px;
    // border-radius: 8px;
    gap: 8px;
    display: flex;
    flex-direction: column;
    /* overflow: hidden; */
    flex-wrap: wrap;
    /* display: grid; */
    /* grid-template-columns: 1fr 1fr;  */
    /* grid-auto-rows: minmax(200px, auto);  */
  }

  ::-webkit-scrollbar {
    width: 4px;
    height: 4px;
    background-color: #f5f5f5;
  }

  /*定义滚动条轨道 内阴影+圆角*/
  ::-webkit-scrollbar-track {
    background-color: #e3e6eb;
  }

  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  /* 滚动条滑块 */
  ::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background: rgba(0, 0, 0, 0.1);
    -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.5);
  }

  .item-head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
  }

  .head-item-text {
    color: #1a2139;
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
    /* 150% */
    position: relative;
    margin-left: 8px;
  }

  .head-item-text::after {
    content: "";
    width: 3px;
    height: 16px;
    border-radius: 8px;
    background: #4d5eff;
    position: absolute;
    top: 4px;
    left: -8px;
  }

  .all-app {
    margin-top: 12px;
  }

  .commonly-box {
    padding-bottom: 12px;
    border-bottom: 1px solid #eceff5;
  }

  .item-body-box {
    display: flex;
    flex-wrap: wrap;
    gap: 16px 24px;
  }

  .item-body-box {
    .item-body {
      display: flex;
      width: 164px;
      padding: 12px 16px 12px 12px;
      align-items: center;
      gap: 8px;
      border-radius: 12px;
      cursor: pointer;
      background: var(--bg-kyy_color_bg_deep, #f5f8fe);

      :deep(.t-badge) {
        height: 36px;
      }

      img {
        width: 36px;
        height: 36px;
      }

      .item-body-text {
        color: #000;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        /* 157.143% */
        margin-left: 8px;
      }
    }
  }

  .head-item-icon:hover {
    color: #707eff;
  }

  .head-item-icon {
    cursor: pointer;
    display: flex;
    align-items: center;
    color: #516082;
    text-align: center;
    font-size: 14px;

    font-style: normal;
    font-weight: 400;
    line-height: 22px;

    /* 157.143% */
    .icon-img {
      font-size: 20px;
      width: 20px;
      height: 20px;
      margin-right: 4px;
    }
  }

  .item-body:hover {
    border-radius: 12px;
    background: var(--bg-kyy_color_bg_light, #fff);
    box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.12);
  }

  .module-btn {
    border-radius: 8px;
    border: 1px solid var(--border-kyy_color_border_default, #d5dbe4);
    display: flex;
    align-items: center;
    padding: 12px;
    gap: 4px;
    text-align: center;
    margin: 16px auto;
    justify-content: center;
    width: fit-content;

    span {
      color: var(--text-kyy_color_text_2, #516082);
      font-size: 14px;
      font-style: normal;
      margin-left: 6px;
      font-weight: 400;
      line-height: 22px;
      /* 157.143% */
    }
  }

  .w49ps {
    width: 49% !important;
  }

  .module-btn:hover {
    color: #707eff;
    border-color: #707eff;

    span {
      color: #707eff;
    }
  }
</style>
