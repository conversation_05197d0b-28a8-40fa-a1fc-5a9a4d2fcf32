<template>
  <platformMembers @change-type="change" v-if="showType === 1" />
  <visitorList @change-type="change" @getRedNum="getRedNumRun" v-else />
</template>
<script setup lang="ts">
import { ref } from 'vue';
const showType = ref(1);
import platformMembers from './platform-members/platform-members.vue';
import visitorList from './platform-members/visitor-list.vue';
import { useRoute } from 'vue-router';
const change = (type) => {
  showType.value = type;
  if (type === 1) {
    route.query.origin = 'visitorList';
  }
}
console.log(showType, 'showType');
const emits = defineEmits(["getRedNum"]);
const getRedNumRun = () => {
  emits("getRedNum");
}
const route = useRoute();
const goApplyOrActive = () => {
  if (route.query && route.query.origin === 'message') {
    const { teamId, redirect } = route.query;
    if (teamId && redirect) {
      showType.value = 2;
      route.query.origin = 'visitorList';
    }
  }
};
goApplyOrActive();
</script>

<style lang="less" scoped>
.box {
  width: 100%;
}
</style>
