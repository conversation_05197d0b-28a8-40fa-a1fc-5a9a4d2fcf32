<template>
  <div class="niche-card" @click="cardClick">
    <div class="main-img">
      <!-- <img :src="props.cardData?.images[0]?.file_name" alt="" /> -->
      <t-image
        :src="props.cardData?.images[0]?.file_name"
        :lazy="true"
        :loading="renderLoading"
        :error="renderLoading"
        style="width: 200px; height: 200px"
      />
    </div>
    <div class="name" style="height: 44px">
      {{ props.cardData?.title }}
    </div>

    <div v-show="props.cardData?.type === 1" class="pr" style="margin-top: -4px">
      <div v-if="props.cardData?.price_option === 0" class="price">
        <span class="pix">{{ props.cardData?.region === "MO" ? "MOP" : "￥" }}</span>
        <span>{{ formatPrice(props.cardData?.price) }}</span>
      </div>
      <div v-else class="price">
        <span>{{ t("market.zx") }}</span>
      </div>
    </div>

    <div class="sq">
      <div class="logo" @click.stop="goHomePageRun">
        <img v-if="props.cardData?.apply_data?.avatar" :src="props.cardData?.apply_data?.avatar" alt="" />
        <img v-else src="@/assets/svg/clouddisk/temaavatar.svg" alt="" />
      </div>
      <div class="name" @click.stop="goHomePageRun">{{ props.cardData?.apply_data?.name }}</div>
      <div class="distance">
        {{ props.cardData?.distance_txt }}
      </div>
    </div>
  </div>
</template>

<script setup lang="tsx">
import { useI18n } from "vue-i18n";
import { toSquareHome } from "@renderer/views/square/utils/ipcHelper";
import { getOpenid } from "@renderer/utils/auth";
import to from "await-to-js";
import { getIndividualSquare } from "@renderer/api/square/home";
import { DialogPlugin } from "tdesign-vue-next";
import { getSquareByopenId } from "@/api/business/manage";
import defimg from "@/assets/big-market/def.png";
const { t } = useI18n();
const props = defineProps({
  cardData: {
    type: Object,
    default: () => {},
  },
});
const renderLoading = () => <img width="200" height="200" src={defimg} alt="cs" />;
const distanceCalc = (distance) => {
  if (distance) {
    distance = distance > 999 ? `${(distance / 1000).toFixed(1)}km` : `${distance}m`;
  }
};

const emits = defineEmits(["card-click"]);
const cardClick = () => {
  emits("card-click", props.cardData);
};

const formatPrice = (price) => {
  // 转换为数字，如果是字符串的话
  const number = typeof price === "string" ? parseFloat(price) : price;
  // 检查是否为有效数字
  // eslint-disable-next-line no-restricted-globals
  if (isNaN(number)) {
    return "无效的价格";
  }
  // 格式化为千分位数字，不包括货币符号
  return number.toLocaleString("zh-CN", {
    style: "decimal",
    minimumFractionDigits: 2,
  });
};
const isSelfSquare = async () => {
  const [err, res] = await to(getSquareByopenId(getOpenid()));
  if (err) return false;
  const { data } = res;
  console.log("data", data);
  return data.opened && data.selfOpened;
};

const goHomePageRun = async () => {
  const res = await isSelfSquare();
  if (res) {
    jumpRun();
  } else {
    const myDialog = DialogPlugin({
      header: "提示",
      theme: "info",
      body: t("niche.sqqiypon"),
      className: "dialog-classp32",
      confirmBtn: t("niche.qding"),
      onConfirm: () => {
        openSquare();
        myDialog.hide();
      },
      onCancel: () => {
        myDialog.hide();
      },
    });
  }
};

const jumpRun = () => {
  console.log("jumpRun", props.cardData?.apply_data);
  toSquareHome(props.cardData?.apply_data?.id);
  console.log("jumpRun");
};

const openSquare = async () => {
  const data = { ip_region: "" };
  const [err, res] = await to(getIndividualSquare(data));
  if (err) return;
  jumpRun();
};
</script>

<style lang="less" scoped>
.niche-card {
  display: flex;
  width: 224px;
  padding: 12px;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
  flex-shrink: 0;
  border-radius: 8px;
  background: var(--bg-kyy_color_bg_light, #fff);
  cursor: pointer;
  .main-img {
    height: 200px;
    width: 200px;
    img {
      height: 200px;
      width: 200px;
    }
  }
  .name {
    overflow: hidden;
    color: var(--text-kyy_color_text_1, #1a2139);
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    word-break: break-all;
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
    min-height: 22px;
    max-height: 44px;
  }
  .distance {
    color: var(--text-kyy_color_text_3, #828da5);
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
  .price {
    .pix {
      overflow: hidden;
      color: var(--error-kyy_color_error_default, #d54941);
      text-overflow: ellipsis;
      font-family: "PingFang SC";
      font-size: 12px;
      font-style: normal;
      font-weight: 500;
      line-height: 20px; /* 144.444% */
    }
    overflow: hidden;
    color: var(--error-kyy_color_error_default, #d54941);
    text-overflow: ellipsis;
    font-family: "PingFang SC";
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px; /* 128.571% */
  }
  .sq {
    display: flex;
    align-items: center;
    gap: 8px;
    align-self: stretch;
    .logo {
      width: 24px;
      height: 24px;
      border-radius: 24px;
      img {
        width: 24px;
        height: 24px;
        border-radius: 24px;
      }
    }
    .name {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;
      flex: 1 0 0;
      overflow: hidden;
      color: var(--text-kyy_color_text_2, #516082);
      text-overflow: ellipsis;
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
      width: 114px;
    }
    .name:hover {
      color: var(--icon-kyy_color_icon_hover, #707eff);
    }
  }
}
.card2 {
}
.niche-card:hover {
  box-shadow: 0px 3px 8px 0px rgba(0, 0, 0, 0.12);
}
</style>
