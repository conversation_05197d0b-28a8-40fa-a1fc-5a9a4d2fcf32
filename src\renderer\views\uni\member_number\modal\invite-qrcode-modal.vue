<template>
  <t-dialog
    v-model:visible="visible"
    :z-index="1500"
    :width="'480px'"
    class="dialogSet dialogSet-noBodyPadding dialogSetHeader dialogSetNP dialogSetFooter_top_0"
  >
    <template #header>
      <div class="header">
        <div class="header-title">
          <span> {{ props.headerText }}</span>
          <Tricks :uuid="uuidComputed" :size="'small'" :isDrag="false"/>
        </div>
        <!-- <div class="header-close" @click="onClose">
          <iconpark-icon
            name="iconerror"
            class="iconerror cursor"
          ></iconpark-icon>
        </div> -->
      </div>
    </template>
    <template #body>
      <div class="tags" v-if="settingInfo?.allow_platform_visitor === 1">
        <span v-for="(item, itemIndex) in tabs" :key="itemIndex" :class="{'tags-item':true, 'cursor': true, 'active': item.value === currentTab.value }" @click="onChangeTab(item)">{{ item.label }}</span>
      </div>
      <div class="boxtag mt-16px">
        <div class="tabs">
          <span v-for="(item, itemIndex) in ways" :key="itemIndex" :class="{'tabs-item':true, 'cursor': true, 'active': item.value === currentWay.value }" @click="onChangeWay(item)">{{ item.label }}</span>
        </div>
      </div>
      <div v-if="props.member" class="toBody mt-16px">
        <!-- <t-alert theme="info" class="w">
          <template #message>
            <span>{{ props.tipText }}</span>
          </template>
        </t-alert> -->
        <div class="scroll">
          <!-- <div class="way" v-show="currentWay.value === ways[0].value">
            <div class="way-tips" v-if="currentTab.value === 'visitor'">{{$t('uni.vis.a')}}</div>
            <div class="way-tips" v-else>{{ props.wayTips }}</div>
            <div class="way-logo">
              <kyy-avatar
                class="rd-10"
                :avatar-size="'46px'"
                :image-url="props.member.team_logo || ORG_DEFAULT_AVATAR"
                :user-name="props.member.exclusive_name || props.member.team_name"
                :shape="'circle'"
              />
              <span class="text">{{ props.member.exclusive_name || props.member.team_name }}</span>
            </div>
          </div> -->

          <div class="way" v-show="currentWay.value === ways[0].value">
            <!-- <div class="way-tips">
              {{$t('member.syd.q')}}
              <a @click="onCopyLink">{{$t('member.syd.r')}}</a>
            </div> -->
            <div class="way-box" v-if="currentTab.value === 'visitor'">
              <div class="head mb-16px">方式一：点击链接申请成为访客</div>
              <div class="shareImg h-328px">
                <div class="text">
                  {{ props.member.name }}{{$t('member.syd.s')}}{{
                  props.member.exclusive_name || props.member.team_name
                  }}

                  ，点击链接成为访客：<span style="color:#4D5EFF ">{{ link_visitor }}</span>
                </div>
              </div>
            </div>
            <div class="way-box" v-else>
              <div class="head mb-16px">方式一：成员访问链接申请加入</div>
              <div class="shareImg h-328px">
                <div class="text">
                  {{ props.member.name }}{{$t('member.syd.s')}}{{
                    props.member.exclusive_name || props.member.team_name
                  }}
                  ，{{$t('member.syd.v')}}：<span style="color:#4D5EFF ">{{ link }}</span>
                </div>
              </div>
            </div>
          </div>
          <!-- <div class="way">
            <div class="way-tips">
              {{$t('member.syd.u')}}
              <div class="sbtn">
                <a @click="onCopyQr">复制二维码</a>
                <a @click="onDownloadQRcode">{{$t('member.syd.t')}}</a>
              </div>
            </div>
          </div> -->
          <div class="box" v-show="currentWay.value === ways[1].value">
            <div v-show="currentTab.value !== 'visitor'" class="head mb-16px">方式二：成员扫描二维码申请加入</div>
            <div v-show="currentTab.value === 'visitor'" class="head mb-16px">方式二：用户扫码申请成为访客</div>
            <div ref="shareImg" class="shareImg">
              <span class="qrcode">
                <div class="qrcode-title" v-show="currentTab.value !== 'visitor'">
                  <span class="staff">
                    <span
                      class="line-1 max-w-208"
                      style="color: #4d5eff; display: inline-block;"
                    >
                      {{ props.member.name }}
                    </span>
                    {{$t('member.syd.s')}}
                  </span>
                  <span class="item">
                    <span
                      class="line-1 max-w-208">
                      {{ props.member.exclusive_name || props.member.team_name }}
                    </span>
                  </span>
                </div>
                <div class="qrcode-title" v-show="currentTab.value === 'visitor'">
                  <span class="staff">
                    <span
                      class="line-1 max-w-208"
                      style="color: #4d5eff; display: inline-block;"
                    >
                      {{ props.member.name }}
                    </span>
                    邀请你成为
                  </span>
                  <span class="item">
                    <span
                      class="line-1 max-w-162">
                      {{ props.member.exclusive_name || props.member.team_name }}
                    </span>
                    的访客
                  </span>
                </div>
                <div id="code-img" class="qr-boxx">
                  <qrcode-vue
                    :value="currentTab.value === 'visitor'? link_visitor : link"
                    :size="qrCodeSetting.size"
                    style="transform: scale(0.4);"
                    level="Q"
                  />
                  <span class="toCircle">
                    <img class="qr" src="@/assets/member/logo.svg" />
                  </span>
                </div>
                <div v-show="currentTab.value !== 'visitor'" class="qrtip mt-8px" >扫一扫上面的二维码，申请加入</div>
                <div v-show="currentTab.value === 'visitor'" class="qrtip mt-8px" >扫一扫上面的二维码，成为访客</div>

                <img class="qrlogo mt-16px" src="@renderer/assets/member/icon/linker.png">
              </span>
            </div>
          </div>
          <div class="box"  v-show="currentWay.value === ways[2].value">
            <div v-show="currentTab.value !== 'visitor'" class="head mb-16px">方式三：微信扫小程序码申请入会</div>
            <div v-show="currentTab.value === 'visitor'" class="head mb-16px">方式三：微信扫小程序码成为访客 </div>

            <div ref="shareImgWeixin" class="shareImg">
              <span class="qrcode">
                <div class="qrcode-title" v-show="currentTab.value === 'visitor'">
                  <span class="staff">
                    <span
                      class="line-1 max-w-208"
                      style="color: #4d5eff; display: inline-block;"
                    >
                      {{ props.member.name }}
                    </span>
                    邀请你成为
                  </span>
                  <span class="item">
                    <span
                      class="line-1 max-w-162">
                      {{ props.member.exclusive_name || props.member.team_name }}
                    </span>
                    的访客
                  </span>
                </div>
                <div class="qrcode-title" v-show="currentTab.value !== 'visitor'">
                  <span class="staff">
                    <span
                      class="line-1 max-w-208"
                      style="color: #4d5eff; display: inline-block"
                    >
                      {{ props.member.name }}
                    </span>
                    {{$t('member.winter_column.know_add_flow_7')}}
                  </span>
                  <span class="item">
                    <span class="line-1 max-w-208">
                      {{ props.member.exclusive_name || props.member.team_name }}
                    </span>
                  </span>
                </div>
                <div id="code-img-weixin" class="qr-boxx">

                  <!-- <qrcode-vue
                    :value="currentTab.value === 'visitor'? link_visitor : link"
                    :size="qrCodeSetting.size"
                    style="transform: scale(0.4);"
                    level="Q"
                  />
                  <span class="toCircle">
                    <img class="qr" src="@/assets/member/logo.svg" />
                  </span> -->
                  <img class="w-124 h-124" :src="image" alt="" />
                </div>
                <div v-show="currentTab.value !== 'visitor'" class="qrtip mt-8px" >微信扫一扫，申请加入 </div>
                <div v-show="currentTab.value === 'visitor'" class="qrtip mt-8px" >微信扫一扫，成为访客</div>

                <img class="qrlogo mt-16px" src="@renderer/assets/member/icon/linker.png">
              </span>
            </div>
          </div>
        </div>
      </div>
    </template>
    <template #closeBtn>
      <!-- <iconpark-icon name="close" size="16"></iconpark-icon> -->
      <!-- <svg class="iconpark-icon" style="width: 16px; height: 16px">
        <use href="#close" />
      </svg> -->
      <iconpark-icon name="iconerror" style="font-size: 24px"></iconpark-icon>
    </template>
    <template #footer>
      <div class="footer">
        <t-button v-if="currentWay.value === ways[0].value" theme="primary" @click="onCopyLink">{{$t('member.syd.r')}}</t-button>
        <template v-else-if="currentWay.value === ways[1].value">
          <t-button variant="outline" theme="primary" style="font-weight: 600" @click="onDownloadQRcode">{{$t('member.syd.t')}}</t-button>
          <t-button  theme="primary" @click="onCopyQr">复制二维码</t-button>
        </template>
        <template v-else-if="currentWay.value === ways[2].value">
          <t-button variant="outline" theme="primary" style="font-weight: 600" @click="onDownloadWeixin">保存小程序码</t-button>
          <t-button  theme="primary" @click="onCopyWeixin">复制小程序码</t-button>
        </template>
      </div>
    </template>
  </t-dialog>
</template>

<script lang="ts" setup>
/**
 * @description 批量调整角色弹层
 * <AUTHOR>
 */
import { debounce } from "lodash";
import { reactive, ref, toRaw, computed } from "vue";
import QrcodeVue from "qrcode.vue";
import html2canvas from "html2canvas";
import useClipboard from "vue-clipboard3";

import { inviteUrl } from "@renderer/utils/baseUrl";
import { MessagePlugin } from "tdesign-vue-next";
import { refreshTokens } from "@renderer/utils/apiRequest";
import Qs from "qs";
import * as htmlToImage from "html-to-image";
import { useI18n } from "vue-i18n";
import KyyAvatar from "@/components/kyy-avatar/index.vue";
import { ORG_DEFAULT_AVATAR } from "@/views/square/constant";
import { getOpenid } from "@renderer/utils/auth";
import { getMemberSettingAxios } from "@renderer/api/uni/api/businessApi";
import to from "await-to-js";
import { getActivityWxQrCode } from '@/api/activity';
import { onDeliverCreateAxios } from "@renderer/views/digital-platform/utils/auth";
import LynkerSDK from "@renderer/_jssdk";
const { ipcRenderer } = LynkerSDK;

const { t } = useI18n();
const { toClipboard } = useClipboard();

const uuidComputed = computed(()=> {
  return '数字高校-成员中心-邀请入会';
})

const props = defineProps({
  headerText: {
    default: "推荐入会",
    type: String
  },
  tipText: {
    default: "分享以下二维码或链接给会员，邀请入会并加入组织",
    type: String
  },
  downLoadText: {
    default: "点击下载入会申请二维码",
    type: String
  },
  copyLinkText: {
    default: "点击复制入会申请链接",
    type: String
  },
  wayTips: {
    default: '通过以下方式推荐会员入会',
    type: String
  },
  member: {
    type: Object,
    default: null
  },
  activeAccount: {
    type: Object,
    default: () => ({})
  }
});

const tabs = [
  {label: t('member.bolit.d'), value: 'official'},
  {label:  t('ebook.vyq'), value: 'visitor'},
]

const ways = [
  {
    label: '链接', value: 'link'
  },
  {
    label: '二维码', value: 'qrcode'
  },
  {
    label: '微信小程序', value: 'weixin'
  },
]

const currentTab = ref(tabs[0]);
const currentWay = ref(ways[0]);
const onChangeTab = (tab) => {
  currentTab.value = tab;
  getCode();
}
const onChangeWay = (tab) => {
  currentWay.value = tab;
}


const shareImg = ref(null);
const shareImgWeixin = ref(null);
const link = ref("");
const link_visitor = ref("");
// 二维码生成 size: 110,
const qrCodeSetting = reactive({
  size: 275,
  level: "Q"
});

const visible = ref(false);

const emits = defineEmits(["reload"]);

const onSave = debounce(() => {}, 500);

let imgUrl = "";
const loading = ref(false);
const onDownloadQRcode = (down = true) => {
  loading.value = true;

  htmlToImage
    .toPng(shareImg.value, {
      quality: 1, // 设置图像质量为最高
      pixelRatio: 4
    })
    .then(async (dataUrl) => {
      if (down) {
        ipcRenderer.invoke("save-base64", dataUrl).then((res) => {
          console.log(res);
          if (res) {
            MessagePlugin.success("保存成功");
          }
        });
      } else {
        const data = await fetch(dataUrl);
        const blob = await data.blob();
        await navigator.clipboard.write([
          new ClipboardItem({
            [blob.type]: blob
          })
        ]);
        MessagePlugin.success(t("zx.contacts.copySuc"));
      }
      loading.value = false;
    })
    .finally(() => {
      // saveStatus.value = false;
      loading.value = false;
    });
  setTimeout(() => {
    loading.value = false;
  }, 5000);
};



const onCopyQr = ()=> {
  htmlToImage
    .toPng(shareImg.value, {
      quality: 1, // 设置图像质量为最高
      pixelRatio: 2 })
    .then(async (dataUrl) => {
      const data = await fetch(dataUrl);
      const blob = await data.blob();
      await navigator.clipboard.write([
        new ClipboardItem({
          [blob.type]: blob
        })
      ]);
      MessagePlugin.success(t("zx.contacts.copySuc"));
    })
    .finally(() => {
      // saveStatus.value = false;
    });
}



// 点击下载二维码
// let imgUrl = "";
// const onDownloadQRcode = () => {
//   html2canvas(document.getElementById("code-img"), {
//     useCORS: true, // 【重要】开启跨域配置
//     scale: window.devicePixelRatio < 3 ? window.devicePixelRatio : 2,
//     allowTaint: true // 允许跨域图片
//   }).then(async (canvas) => {
//     imgUrl = canvas.toDataURL();
//     // console.log(imgUrl);
//     // const data = await fetch(imgUrl);
//     // const blob = await data.blob();
//     // // const myUrl = URL.createObjectURL(blob);
//     // console.log(blob);
//     // // downImg()
//     // const link = document.createElement("a");
//     // link.href = imgUrl;
//     // link.download = "linker.png";
//     // link.click();
//     ipcRenderer
//       .invoke("download-file", {
//         title: "linker.png",
//         url: imgUrl
//       })
//       .then((res) => {
//         // console.log("下载成功");
//         console.log(res);
//         if (res) {
//           MessagePlugin.success("下载成功");
//         }
//       });
//   });
// };

const image = ref(null);
const getCode = async () => {
  // 分享的详情码不确定是谁扫，不用传actorid，因为这是创建者的
  onDeliverCreateAxios({
    content: currentTab.value?.value === 'visitor'? link_visitor.value : link.value
  }).then(async(result: any)=> {
    const res = await getActivityWxQrCode({
      scene: `deliver_key=${result?.deliver_key}`,
      width: 64,
      page: 'pages/webview/index',
    });
    const blob = new Blob([res.data], { type: 'image/jpeg' });
    image.value  = URL.createObjectURL(blob);
  })
}

const onDownloadWeixin = debounce((down = true) => {
  loading.value = true;
  // console.log(dayjs());
  htmlToImage
    .toPng(shareImgWeixin.value, {
      quality: 1, // 设置图像质量为最高
      pixelRatio: 4
    })
    .then(async (dataUrl) => {
      if (down) {
        ipcRenderer.invoke("save-base64", dataUrl).then((res) => {
          console.log(res);
          if (res) {
            MessagePlugin.success("保存成功");
          }
        });
      } else {
        const data = await fetch(dataUrl);
        const blob = await data.blob();
        await navigator.clipboard.write([
          new ClipboardItem({
            [blob.type]: blob
          })
        ]);
        MessagePlugin.success(t("zx.contacts.copySuc"));
      }
      loading.value = false;
    })
    .finally(() => {
      // saveStatus.value = false;
      loading.value = false;
    });
  setTimeout(() => {
    loading.value = false;
  }, 5000);
}, 500);

const onCopyWeixin = ()=> {
  htmlToImage
    .toPng(shareImgWeixin.value, {
      quality: 1, // 设置图像质量为最高
      pixelRatio: 2 })
    .then(async (dataUrl) => {
      const data = await fetch(dataUrl);
      const blob = await data.blob();
      await navigator.clipboard.write([
        new ClipboardItem({
          [blob.type]: blob
        })
      ]);
      MessagePlugin.success(t("zx.contacts.copySuc"));
    })
    .finally(() => {
      // saveStatus.value = false;
    });
}


const onCopyLink = async () => {
  try {
    let copyStr = props.member.name + t('member.syd.s')+ (props.member.exclusive_name || props.member.team_name) + '，点击链接填写加入申请表：'+ link.value
    if(currentTab.value?.value === 'visitor') {
      copyStr = props.member.name + '邀请你加入'+ (props.member.exclusive_name || props.member.team_name) + '，点击链接成为访客：'+ link_visitor.value
    }
    await toClipboard(copyStr);
    // console.log('Copied to clipboard');
    MessagePlugin.success("已复制到剪贴板");
  } catch (e) {
    console.error(e);
  }
};

const onGoLink = () => {
  // previewAppPage({ url: toRaw(link.value) });
  ipcRenderer.invoke(
    "create-h5-preview",
    `${inviteUrl}/member/invite?link=d9844ff11d04752dbbd318c075dee4bf`
  );
};


const settingInfo = ref(null);
const onGetMemberSetting = () => {

  return new Promise(async(resolve, reject)=> {
    try {
      const [err, res] = await to(getMemberSettingAxios({}, props.activeAccount?.teamId));
      loading.value = false;
      if (err) {
        reject();
        return;
      };
      console.log(res)
      const { data } = res?.data;
      settingInfo.value = data;

    } catch(e) {

    }
  })
}


/**
 *
 * @param data 值不为空说明为编辑状态
 */
const onOpen = (url?: any, path = "/account/jump?to=associationInvite") => {
  currentTab.value = tabs[0];
  link.value = "";
  link_visitor.value = "";
  const path_visitor = '/account/jump?to=visitorInvite'
  console.log(props.member);
  onGetMemberSetting();
  if (url) {
    let params = {
      link: url,
      activate_link: props.member.activate_link
      // referer: props.member.staff_name,
      // referer_unit: props.member.name
    };
    link.value = `${inviteUrl}${path}&${Qs.stringify(params, { encode: true })}`;
    link_visitor.value = `${inviteUrl}${path_visitor}&${Qs.stringify(Object.assign({}, params, {type: 'uni',channel_type: 2,openid: getOpenid()}), { encode: true })}`;
    visible.value = true;
    // refreshTokens("PC").then((res) => {
    //   console.log(res);
    //   params = { ...params, ...res };
    //   link.value = `${inviteUrl}${path}?${Qs.stringify(params)}`;
    //   visible.value = true;
    // });
    getCode();
  }
};
const onClose = () => {
  visible.value = false;
};

defineExpose({
  onOpen,
  onClose
});
</script>

<style lang="less" scoped>

@import "@renderer/views/engineer/less/common.less";
@import "@renderer/views/member/member_number/less/inviteqrcode.less";

</style>
