import { getIpc, RendererIPC } from '@lynker-desktop/electron-ipc/renderer';
import EventEmitter from 'events';
import { v5 as uuidv5 } from 'uuid';
import md5 from 'md5';
import { CHANNEL, LynkerSDKConfig, MsgQuery } from './common';
import lodash from 'lodash';

export * from './common';

class WebLynkerSDK extends EventEmitter {
  debug = false;

  /**
   * 当前是否在桌面端中
   */
  get isDesktop() {
    return typeof getIpc()?.invoke === 'function';
  }

  /**
   * 原始electron ipcRenderer
   * @deprecated 请优先使用ipc，该属性即将被移除
   * @ts-deprecated 此属性即将被禁用，请优先使用ipc
   */
  ipcRenderer = getIpc();

  /**
   * 基础IPC组件
   * @lynker-desktop/electron-ipc
   */
  ipc = RendererIPC;

  constructor() {
    super();
    this.log('init');
    try {
      /** 监听桌面端配置 */
      RendererIPC.handleRenderer('__APP_CONFIG_CHANGE__', async (config: LynkerSDKConfig) => {
        this.log('__APP_CONFIG_CHANGE__', config);
        this.emit('config-change', config);
        return 'OK';
      });
    } catch (error) {
      this.log('当前非桌面端环境, 请在桌面端中调用');
    }
  }

  /** 打开桌面端 */
  openDesktop(launchOptions?: string) {
    const url = `ringkol://web-sdk?launchOptions=${launchOptions}`;
    window.location.href = `${url}`;
  }

  /**
   * 与主窗口业务通信
   * @param type
   * @param data
   * @returns
   */
  async invokeRenderer<T extends keyof MsgQuery, K extends MsgQuery>(type:T, data: K[T]['query']): Promise< K[T]['data']> {
    // @ts-ignore
    if (!this.isDesktop) {
      this.log('当前非桌面端环境, 请在桌面端中调用');
    }
    const res = await this?.ipc?.invokeRenderer?.(CHANNEL, type, data);
    this.log(type, res);
    return res;
  }

  /**
   * 获取config
   * @param query
   * @returns
   */
  async getConfig(query?: MsgQuery['getConfig']['query']) {
    const res = await this.invokeRenderer('getConfig', query);
    this.log('getConfig', res);
    return res;
  }

  /**
   * 获取主窗口localStorage
   * @param query
   * @returns
   */
  async getLocalStorage(query: MsgQuery['getLocalStorage']['query']) {
    const res = await this.invokeRenderer('getLocalStorage', query);
    this.log('getLocalStorage', res);
    return res;
  }

  /**
   * 设置主窗口localStorage
   * @param query
   * @returns
   */
  async setLocalStorage(query: MsgQuery['setLocalStorage']['query']) {
    const res = await this.invokeRenderer('setLocalStorage', query);
    this.log('setLocalStorage', res);
    return res;
  }

  /**
   * 获取启动/唤起参数
   * @param query
   * @returns
   */
  async getLaunchOptions(query?: MsgQuery['getLaunchOptions']['query']) {
    const res = await this.invokeRenderer('getLaunchOptions', query);
    this.log('getLaunchOptions', res);
    return res;
  }

  /**
   * 清除启动/唤起参数
   * @param query
   * @returns
   */
  async delLaunchOptions(query?: MsgQuery['delLaunchOptions']['query']) {
    const res = await this.invokeRenderer('delLaunchOptions', query);
    this.log('delLaunchOptions', res);
    return res;
  }

  getRandomUUID(key = `${Date.now()}`): string {
    let webcrypto!: Crypto;
    let randomValue = Math?.random()?.toString()?.replace('0.', '');
    try {
      if (!webcrypto && !!window && window?.crypto) {
        webcrypto = window.crypto;
      }
    } catch (error) { console.log; }
    try {
      const ar = webcrypto.getRandomValues(new Uint8Array(12));
      randomValue = `${md5(ar)}` || randomValue;
    } catch (error) {}
    const uuid = uuidv5(
      `${JSON.stringify(key)}_${Date.now()}_${randomValue}`,
      uuidv5.URL,
    );
    return uuid;
  }

  /**
   * 打开web窗口
   * @param query
   * @returns
   */
  async openWebWindow(query: MsgQuery['openWebWindow']['query']) {
    const res = await this.invokeRenderer('openWebWindow', query);
    this.log('openWebWindow', res);
    return res;
  }

  /**
   * 打开浏览器
   * @param query
   * @returns
   */
  async openBrowser(query: MsgQuery['openBrowser']['query']) {
    const res = await this.invokeRenderer('openBrowser', query);
    this.log('openBrowser', res);
    return res;
  }

  /**
   * 打开帮助中心
   * @param query
   * @returns
   */
  async openMyHelpWindow(query?: MsgQuery['openMyHelpWindow']['query']) {
    const res = await this.invokeRenderer('openMyHelpWindow', query);
    this.log('openMyHelpWindow', res);
    return res;
  }

  /**
   * 打开我的订单
   * @param query
   * @returns
   */
  async openMyOrderWindow(query?: MsgQuery['openMyOrderWindow']['query']) {
    const res = await this.invokeRenderer('openMyOrderWindow', query);
    this.log('openMyOrderWindow', res);
    return res;
  }

  /**
   * 打开发票中心
   * @param query
   * @returns
   */
  async openMyInvoiceWindow(query?: MsgQuery['openMyInvoiceWindow']['query']) {
    const res = await this.invokeRenderer('openMyInvoiceWindow', query);
    this.log('openMyInvoiceWindow', res);
    return res;
  }

  /**
   * 打开选人组件
   * @param query
   * @returns
   */
  async openSelectMember(options?: MsgQuery['openSelectMember']['query']): Promise<Array<any>> {
    const webContentsId = options?.webContentsId || await this?.ipc.getCurrentWebContentId();
    let res = await this.invokeRenderer('openSelectMember', {
      webContentsId,
      query: options?.query || {},
    });
    try {
      res = JSON.parse(res);
    } catch (error) {
      this.log('openSelectMember', error);
    }
    return res as any;
  }

  /**
   * 打开加入数字平台组件
   * @param query
   * @returns
   */
  async openJoinDigitalPlatformDrawer(options?: MsgQuery['openJoinDigitalPlatformDrawer']['query']): Promise<Array<any>> {
    const webContentsId = options?.webContentsId || await this?.ipc.getCurrentWebContentId();
    let res = await this.invokeRenderer('openJoinDigitalPlatformDrawer', {
      webContentsId,
      query: options?.query || {},
    });
    try {
      res = JSON.parse(res);
    } catch (error) {
      this.log('openJoinDigitalPlatformDrawer', error);
    }
    return res as any;
  }

  /**
   * 打开组织认证组件
   * @param options
   * @returns
   */
  async openOrgAuthDrawer(options?: MsgQuery['openOrgAuthDrawer']['query']): Promise<Array<any>> {
    const webContentsId = options?.webContentsId || await this?.ipc.getCurrentWebContentId();
    let res = await this.invokeRenderer('openOrgAuthDrawer', {
      webContentsId,
      query: {
        region: options?.query?.region || '',
        teamId: options?.query?.teamId || '',
        orgType: options?.query?.orgType || '',
        showType: options?.query?.showType || 'detail',
      }
    });
    try {
      res = JSON.parse(res);
    } catch (error) {
      this.log('openOrgAuthDrawer', error);
    }
    return res as any;
  }

  /**
   * 打开添加联系人组件
   * @param options
   * @returns
   */
  async openAddContactsDialog(options?: MsgQuery['openAddContactsDialog']['query']): Promise<Array<any>> {
    const webContentsId = options?.webContentsId || await this?.ipc.getCurrentWebContentId();
    let res = await this.invokeRenderer('openAddContactsDialog', {
      webContentsId,
      query: options?.query || {},
    });
    try {
      res = JSON.parse(res);
    } catch (error) {
      this.log('openAddContactsDialog', error);
    }
    return res as any;
  }

  /**
   * 打开年费组件
   * @param options
   * @returns
   */
  async openAnnualFeeDrawer(options?: MsgQuery['openAnnualFeeDrawer']['query']) {
    const res = await this.invokeRenderer('openAnnualFeeDrawer', {
      webContentsId: options?.webContentsId || await this?.ipc.getCurrentWebContentId(),
      query: {
        teamId: options?.query?.teamId || '',
        title: options?.query?.title || '',
        open: options?.query?.open || false,
        inviteCode: options?.query?.inviteCode || '',
        upgrade: options?.query?.upgrade || false,
      },
    });
    this.log('openAnnualFeeDrawer', res);
    return res;
  }

  /**
   * 打开一个新窗口
   * @param options
   * @returns
   */
  async openNewWindow(options: MsgQuery['openNewWindow']['query']) {
    const res = await this.invokeRenderer('openNewWindow', options);
    this.log('openNewWindow', res);
    return res;
  }

  /**
   * 设置debug
   * @param debug
   */
  setDebug(debug: boolean) {
    this.debug = debug;
  }

  /**
   * 打印日志
   * @param type
   * @param args
   */
  log(type: string, ...args: any[]) {
    if (this.debug) {
      console.log(type, ...args);
    }
  }

  /** 获取用户信息 */
  async getUserInfo(): Promise<MsgQuery['getUserInfo']['data']> {
    const res = await this.invokeRenderer('getUserInfo', undefined);
    this.log('getUserInfo', res);
    return res;
  }

  /** 获取唯一ID */
  async getUniqueId(): Promise<MsgQuery['getUniqueId']['data']> {
    const res = await this.invokeRenderer('getUniqueId', undefined);
    this.log('getUniqueId', res);
    return res;
  }

  /** 打开外部应用 */
  async openExternalApp(query: MsgQuery['openExternalApp']['query']): Promise<MsgQuery['openExternalApp']['data']> {
    const res = await this.invokeRenderer('openExternalApp', query);
    this.log('openExternalApp', res);
    return res;
  }

  /** 设置环境 */
  async setEnv(env: MsgQuery['setEnv']['query']): Promise<void> {
    const res = await this.invokeRenderer('setEnv', env);
    this.log('setEnv', res);
    return res;
  }

  /** 检查是否为手动环境 */
  async checkIsManualEnv(): Promise<MsgQuery['checkIsManualEnv']['data']> {
    const res = await this.invokeRenderer('checkIsManualEnv', undefined);
    this.log('checkIsManualEnv', res);
    return res;
  }

  /** 打开外部窗口 */
  async openExternalWindow(query: MsgQuery['openExternalWindow']['query']): Promise<MsgQuery['openExternalWindow']['data']> {
    const res = await this.invokeRenderer('openExternalWindow', query);
    this.log('openExternalWindow', res);
    return res;
  }

  /** 打开设置窗口 */
  async openSettingWindow(query: MsgQuery['openSettingWindow']['query']): Promise<MsgQuery['openSettingWindow']['data']> {
    const res = await this.invokeRenderer('openSettingWindow', query);
    this.log('openSettingWindow', res);
    return res;
  }

  /** 打开设置组窗口 */
  async openSetGroupWindow(): Promise<MsgQuery['openSetGroupWindow']['data']> {
    const res = await this.invokeRenderer('openSetGroupWindow', undefined);
    this.log('openSetGroupWindow', res);
    return res;
  }

  /** 打开我的地址窗口 */
  async openMyAddressWindow(query: MsgQuery['openMyAddressWindow']['query']): Promise<MsgQuery['openMyAddressWindow']['data']> {
    const res = await this.invokeRenderer('openMyAddressWindow', query);
    this.log('openMyAddressWindow', res);
    return res;
  }

  /** 打开政策详情窗口 */
  async openPolicyDetailWindow(query: MsgQuery['openPolicyDetailWindow']['query']): Promise<MsgQuery['openPolicyDetailWindow']['data']> {
    const res = await this.invokeRenderer('openPolicyDetailWindow', query);
    this.log('openPolicyDetailWindow', res);
    return res;
  }

  /** 打开政策详情窗口2 */
  async openPolicyDetailWindow2(query: MsgQuery['openPolicyDetailWindow2']['query']): Promise<MsgQuery['openPolicyDetailWindow2']['data']> {
    const res = await this.invokeRenderer('openPolicyDetailWindow2', query);
    this.log('openPolicyDetailWindow2', res);
    return res;
  }

  /** 注销 */
  async logout(): Promise<void> {
    const res = await this.invokeRenderer('logout', undefined);
    this.log('logout', res);
    return res;
  }

  /** 打开调试工具 */
  async openDebugTools(): Promise<void> {
    const res = await this.invokeRenderer('openDebugTools', undefined);
    this.log('openDebugTools', res);
    return res;
  }

  /** 预览图像 */
  async previewImage(query: MsgQuery['previewImage']['query']): Promise<void> {
    const res = await this.invokeRenderer('previewImage', query);
    this.log('previewImage', res);
    return res;
  }

  /** 预览视频 */
  async previewVideo(query: MsgQuery['previewVideo']['query']): Promise<void> {
    const res = await this.invokeRenderer('previewVideo', query);
    this.log('previewVideo', res);
    return res;
  }

  /** 预览文件 */
  async previewFile(query: MsgQuery['previewFile']['query']): Promise<void> {
    const res = await this.invokeRenderer('previewFile', query);
    this.log('previewFile', res);
    return res;
  }

  /** 下载文件 */
  async downloadFile(query: MsgQuery['downloadFile']['query']): Promise<string> {
    const res = await this.invokeRenderer('downloadFile', query);
    this.log('downloadFile', res);
    return res;
  }

  /** 开始捕获 */
  async startCapture(): Promise<void> {
    const res = await this.invokeRenderer('startCapture', undefined);
    this.log('startCapture', res);
    return res;
  }

  /** 停止捕获 */
  async stopCapture(): Promise<void> {
    const res = await this.invokeRenderer('stopCapture', undefined);
    this.log('stopCapture', res);
    return res;
  }

  /** 显示加载中 */
  async showLoading(query: MsgQuery['showLoading']['query']): Promise<void> {
    const res = await this.invokeRenderer('showLoading', query);
    this.log('showLoading', res);
    return res;
  }

  /** 隐藏加载中 */
  async hideLoading(): Promise<void> {
    const res = await this.invokeRenderer('hideLoading', undefined);
    this.log('hideLoading', res);
    return res;
  }

  /** 主菜单打开消息 */
  async mainMenu_openMessage(): Promise<void> {
    const res = await this.invokeRenderer('mainMenu_openMessage', undefined);
    this.log('mainMenu_openMessage', res);
    return res;
  }

  /** 主菜单打开工作台 */
  async mainMenu_openWorkBench(query: MsgQuery['mainMenu_openWorkBench']['query']): Promise<MsgQuery['mainMenu_openWorkBench']['data']> {
    const res = await this.invokeRenderer('mainMenu_openWorkBench', query);
    this.log('mainMenu_openWorkBench', res);
    return res;
  }

  /** 主菜单打开广场 */
  async mainMenu_openSquare(): Promise<void> {
    const res = await this.invokeRenderer('mainMenu_openSquare', undefined);
    this.log('mainMenu_openSquare', res);
    return res;
  }

  /** 主菜单打开数字平台 */
  async mainMenu_openDigitalPlatform(query: MsgQuery['mainMenu_openDigitalPlatform']['query']): Promise<void> {
    const res = await this.invokeRenderer('mainMenu_openDigitalPlatform', query);
    this.log('mainMenu_openDigitalPlatform', res);
    return res;
  }

  /** 主菜单打开地址簿 */
  async mainMenu_openAddressBook(): Promise<void> {
    const res = await this.invokeRenderer('mainMenu_openAddressBook', undefined);
    this.log('mainMenu_openAddressBook', res);
    return res;
  }

  /** 主菜单打开活动 */
  async mainMenu_openActivities(): Promise<void> {
    const res = await this.invokeRenderer('mainMenu_openActivities', undefined);
    this.log('mainMenu_openActivities', res);
    return res;
  }

  /** 主菜单打开网盘 */
  async mainMenu_openDisk(): Promise<void> {
    const res = await this.invokeRenderer('mainMenu_openDisk', undefined);
    this.log('mainMenu_openDisk', res);
    return res;
  }

  /** 消息打开聊天 */
  async message_openChat(query: MsgQuery['message_openChat']['query']): Promise<MsgQuery['message_openChat']['data']> {
    const res = await this.invokeRenderer('message_openChat', query);
    this.log('message_openChat', res);
    return res;
  }

  /** 消息打开消息 */
  async message_openMessage(query: MsgQuery['message_openMessage']['query']): Promise<MsgQuery['message_openMessage']['data']> {
    const res = await this.invokeRenderer('message_openMessage', query);
    this.log('message_openMessage', res);
    return res;
  }

  /** 消息打开抽屉用于webview */
  async message_openDrawerForWebview(query: MsgQuery['message_openDrawerForWebview']['query']): Promise<MsgQuery['message_openDrawerForWebview']['data']> {
    const res = await this.invokeRenderer('message_openDrawerForWebview', query);
    this.log('message_openDrawerForWebview', res);
    return res;
  }

  /** 消息关闭抽屉用于webview */
  async message_closeDrawerForWebview(query: MsgQuery['message_closeDrawerForWebview']['query']): Promise<MsgQuery['message_closeDrawerForWebview']['data']> {
    const res = await this.invokeRenderer('message_closeDrawerForWebview', {
      ...query,
      // @ts-ignore
      id: query?.id || window['__Lynker_webview_id__'] || '',
    });
    this.log('message_closeDrawerForWebview', res);
    return res;
  }

  /** 工作台获取当前团队ID */
  async workBench_getActiveTeamId(): Promise<MsgQuery['workBench_getActiveTeamId']['data']> {
    const res = await this.invokeRenderer('workBench_getActiveTeamId', undefined);
    this.log('workBench_getActiveTeamId', res);
    return res;
  }

  /** 工作台打开标签 */
  async workBench_openTab(query: MsgQuery['workBench_openTab']['query']): Promise<MsgQuery['workBench_openTab']['data']> {
    const res = await this.invokeRenderer('workBench_openTab', query);
    this.log('workBench_openTab', res);
    return res;
  }

  /** 工作台获取标签列表 */
  async workBench_getTabList(): Promise<MsgQuery['workBench_getTabList']['data']> {
    const res = await this.invokeRenderer('workBench_getTabList', undefined);
    this.log('workBench_getTabList', res);
    return res;
  }

  /** 工作台关闭标签 */
  async workBench_closeTab(query: MsgQuery['workBench_closeTab']['query']): Promise<MsgQuery['workBench_closeTab']['data']> {
    const res = await this.invokeRenderer('workBench_closeTab', {
      ...query,
      // @ts-ignore
      id: query?.id || window['__Lynker_webview_id__'] || '',
    });
    this.log('workBench_closeTab', res);
    return res;
  }

  /** 工作台更新标签 */
  async workBench_updateTab(query: MsgQuery['workBench_updateTab']['query']): Promise<MsgQuery['workBench_updateTab']['data']> {
    const res = await this.invokeRenderer('workBench_updateTab', query);
    this.log('workBench_updateTab', res);
    return res;
  }

  /** 工作台打开标签用于iframe */
  async workBench_openTabForIframe(query: MsgQuery['workBench_openTabForIframe']['query']): Promise<MsgQuery['workBench_openTabForIframe']['data']> {
    const res = await this.invokeRenderer('workBench_openTabForIframe', query);
    this.log('workBench_openTabForIframe', res);
    return res;
  }

  /** 工作台打开标签用于webview */
  async workBench_openTabForWebview(query: MsgQuery['workBench_openTabForWebview']['query']): Promise<MsgQuery['workBench_openTabForWebview']['data']> {
    const res = await this.invokeRenderer('workBench_openTabForWebview', query);
    this.log('workBench_openTabForWebview', res);
    return res;
  }

  /**
   * 工作台打开抽屉用于webview
   * @param query
   * @returns
   */
  async workBench_openDrawerForWebview(query: MsgQuery['workBench_openDrawerForWebview']['query']): Promise<MsgQuery['workBench_openDrawerForWebview']['data']> {
    const res = await this.invokeRenderer('workBench_openDrawerForWebview', query);
    this.log('workBench_openDrawerForWebview', res);
    return res;
  }

  /** 工作台关闭抽屉用于webview */
  async workBench_closeDrawerForWebview(query: MsgQuery['workBench_closeDrawerForWebview']['query']): Promise<MsgQuery['workBench_closeDrawerForWebview']['data']> {
    const res = await this.invokeRenderer('workBench_closeDrawerForWebview', {
      ...query,
      // @ts-ignore
      id: query?.id || window['__Lynker_webview_id__'] || '',
    });
    this.log('workBench_closeDrawerForWebview', res);
    return res;
  }

  /** 工作台打开标签用于企业 */
  async workBench_openTabForEnterprise(query: MsgQuery['workBench_openTabForEnterprise']['query']): Promise<MsgQuery['workBench_openTabForEnterprise']['data']> {
    const res = await this.invokeRenderer('workBench_openTabForEnterprise', query);
    this.log('workBench_openTabForEnterprise', res);
    return res;
  }

  /** 工作台打开标签用于商户申请 */
  async workBench_openTabForMerchantApply(query: MsgQuery['workBench_openTabForMerchantApply']['query']): Promise<MsgQuery['workBench_openTabForMerchantApply']['data']> {
    const res = await this.invokeRenderer('workBench_openTabForMerchantApply', query);
    this.log('workBench_openTabForMerchantApply', res);
    return res;
  }

  /** 工作台打开标签用于接收详情 */
  async workBench_openTabForReceiveDetail(query: MsgQuery['workBench_openTabForReceiveDetail']['query']): Promise<MsgQuery['workBench_openTabForReceiveDetail']['data']> {
    const res = await this.invokeRenderer('workBench_openTabForReceiveDetail', query);
    this.log('workBench_openTabForReceiveDetail', res);
    return res;
  }

  /** 工作台打开标签用于退款详情 */
  async workBench_openTabForRefundDetail(query: MsgQuery['workBench_openTabForRefundDetail']['query']): Promise<MsgQuery['workBench_openTabForRefundDetail']['data']> {
    const res = await this.invokeRenderer('workBench_openTabForRefundDetail', query);
    this.log('workBench_openTabForRefundDetail', res);
    return res;
  }

  /** 工作台打开标签用于结算详情 */
  async workBench_openTabForSettlementDetail(query: MsgQuery['workBench_openTabForSettlementDetail']['query']): Promise<MsgQuery['workBench_openTabForSettlementDetail']['data']> {
    const res = await this.invokeRenderer('workBench_openTabForSettlementDetail', query);
    this.log('workBench_openTabForSettlementDetail', res);
    return res;
  }

  /** 工作台打开组织认证 */
  async workBench_openTeamCertification(query: MsgQuery['workBench_openTeamCertification']['query']): Promise<MsgQuery['workBench_openTeamCertification']['data']> {
    const res = await this.invokeRenderer('workBench_openTeamCertification', query);
    this.log('workBench_openTeamCertification', res);
    return res;
  }

  /** 工作台刷新 */
  async workBench_reload(): Promise<MsgQuery['workBench_reload']['data']> {
    const res = await this.invokeRenderer('workBench_reload', undefined);
    this.log('workBench_reload', res);
    return res;
  }

  /** 数字平台设置当前团队ID */
  async digitalPlatform_setActiveTeamId(query: MsgQuery['digitalPlatform_setActiveTeamId']['query']): Promise<MsgQuery['digitalPlatform_setActiveTeamId']['data']> {
    const res = await this.invokeRenderer('digitalPlatform_setActiveTeamId', query);
    this.log('digitalPlatform_setActiveTeamId', res);
    return res;
  }

  /** 数字平台获取当前团队ID */
  async digitalPlatform_getActiveTeamId(): Promise<MsgQuery['digitalPlatform_getActiveTeamId']['data']> {
    const res = await this.invokeRenderer('digitalPlatform_getActiveTeamId', undefined);
    this.log('digitalPlatform_getActiveTeamId', res);
    return res;
  }

  /** 数字平台刷新 */
  async digitalPlatform_reload(): Promise<MsgQuery['digitalPlatform_reload']['data']> {
    const res = await this.invokeRenderer('digitalPlatform_reload', undefined);
    this.log('digitalPlatform_reload', res);
    return res;
  }

  /** 数字平台打开标签 */
  async digitalPlatform_openTab(query: MsgQuery['digitalPlatform_openTab']['query']): Promise<MsgQuery['digitalPlatform_openTab']['data']> {
    const res = await this.invokeRenderer('digitalPlatform_openTab', query);
    this.log('digitalPlatform_openTab', res);
    return res;
  }

  /** 数字平台关闭标签 */
  async digitalPlatform_closeTab(query: MsgQuery['digitalPlatform_closeTab']['query']): Promise<MsgQuery['digitalPlatform_closeTab']['data']> {
    const res = await this.invokeRenderer('digitalPlatform_closeTab', query);
    this.log('digitalPlatform_closeTab', res);
    return res;
  }

  /** 数字平台更新标签 */
  async digitalPlatform_updateTab(query: MsgQuery['digitalPlatform_updateTab']['query']): Promise<MsgQuery['digitalPlatform_updateTab']['data']> {
    const res = await this.invokeRenderer('digitalPlatform_updateTab', query);
    this.log('digitalPlatform_updateTab', res);
    return res;
  }

  /** 数字平台打开标签用于webview */
  async digitalPlatform_openTabForWebview(query: MsgQuery['digitalPlatform_openTabForWebview']['query']): Promise<MsgQuery['digitalPlatform_openTabForWebview']['data']> {
    const res = await this.invokeRenderer('digitalPlatform_openTabForWebview', query);
    this.log('digitalPlatform_openTabForWebview', res);
    return res;
  }

  /** 数字平台获取标签列表 */
  async digitalPlatform_getTabList(): Promise<MsgQuery['digitalPlatform_getTabList']['data']> {
    const res = await this.invokeRenderer('digitalPlatform_getTabList', undefined);
    this.log('digitalPlatform_getTabList', res);
    return res;
  }

  /** 广场设置当前团队ID */
  async square_setActiveTeamId(query: MsgQuery['square_setActiveTeamId']['query']): Promise<MsgQuery['square_setActiveTeamId']['data']> {
    const res = await this.invokeRenderer('square_setActiveTeamId', query);
    this.log('square_setActiveTeamId', res);
    return res;
  }

  /** 广场获取当前团队ID */
  async square_getActiveTeamId(): Promise<MsgQuery['square_getActiveTeamId']['data']> {
    const res = await this.invokeRenderer('square_getActiveTeamId', undefined);
    this.log('square_getActiveTeamId', res);
    return res;
  }

  /** 广场刷新 */
  async square_reload(): Promise<MsgQuery['square_reload']['data']> {
    const res = await this.invokeRenderer('square_reload', undefined);
    this.log('square_reload', res);
    return res;
  }

  /** 广场打开标签 */
  async square_openTab(query: MsgQuery['square_openTab']['query']): Promise<MsgQuery['square_openTab']['data']> {
    const res = await this.invokeRenderer('square_openTab', query);
    this.log('square_openTab', res);
    return res;
  }

  /** 广场关闭标签 */
  async square_closeTab(query: MsgQuery['square_closeTab']['query']): Promise<MsgQuery['square_closeTab']['data']> {
    const res = await this.invokeRenderer('square_closeTab', query);
    this.log('square_closeTab', res);
    return res;
  }

  /** 广场更新标签 */
  async square_updateTab(query: MsgQuery['square_updateTab']['query']): Promise<MsgQuery['square_updateTab']['data']> {
    const res = await this.invokeRenderer('square_updateTab', query);
    this.log('square_updateTab', res);
    return res;
  }

  /** 广场打开标签用于webview */
  async square_openTabForWebview(query: MsgQuery['square_openTabForWebview']['query']): Promise<MsgQuery['square_openTabForWebview']['data']> {
    const res = await this.invokeRenderer('square_openTabForWebview', query);
    this.log('square_openTabForWebview', res);
    return res;
  }

  /**
   * 独立tab窗口打开标签
   * @param query 打开标签的参数
   * @returns
   */
  async windowsTabs_openTab(query: MsgQuery['windowsTabs_openTab']['query']): Promise<MsgQuery['windowsTabs_openTab']['data']> {
    let id = query.options?.id || this.getRandomUUID();
    let tabsId = query.tabsId;
    let url = query.options?.url;
    try {
      const windowsTabsOptions = await this.windowsTabs_getCurrentTab();
      if (windowsTabsOptions) {
        tabsId = tabsId || windowsTabsOptions.tabsId;
      }
      if (windowsTabsOptions) {
        url = url || windowsTabsOptions.options.url;
      }
    } catch (error) {
      console.error('windowsTabs_openTab', error);
    }
    if (!tabsId) {
      console.error('tabsId is required');
      return;
    }
    if (!id) {
      console.error('id is required');
      return;
    }
    if (!url) {
      console.error('url is required');
      return;
    }
    // 其他打开标签
    const res = await this.invokeRenderer('windowsTabs_openTab', {
      ...query,
      options: {
        ...query.options,
        url,
        // @ts-ignore
        id,
      },
      // @ts-ignore
      tabsId,
    });
    this.log('windowsTabs_openTab', res);
    return res;
  }

  /** 独立tab窗口关闭标签 */
  async windowsTabs_closeTab(query?: MsgQuery['windowsTabs_closeTab']['query']): Promise<MsgQuery['windowsTabs_closeTab']['data']> {
    let id = query?.id;
    let tabsId = query?.tabsId;
    try {
      // @ts-ignore
      const extraData = JSON.parse(window['__ELECTRON_WINDOW_MANAGER_EXTRA_DATA__'] || '{}');
      const windowsTabsOptions = extraData?.windowsTabsOptions;
      if (windowsTabsOptions) {
        id = id || windowsTabsOptions.tabOptions.id;
        tabsId = tabsId || windowsTabsOptions.tabsId;
      }
    } catch (error) {
      console.error('windowsTabs_openTab', error);
    }
    if (!tabsId) {
      console.error('tabsId is required');
      return;
    }
    if (!id) {
      console.error('id is required');
      return;
    }
    // 其他关闭标签
    const res = await this.invokeRenderer('windowsTabs_closeTab', {
      ...query,
      // @ts-ignore
      id,
      // @ts-ignore
      tabsId,
    });
    this.log('windowsTabs_closeTab', res);
    return res;
  }

  /**
   * 获取当前标签信息
   * @returns 当前标签信息
   */
  async windowsTabs_getCurrentTab(): Promise<MsgQuery['windowsTabs_openTab']['query'] | undefined> {
    try {
      // @ts-ignore
      const extraData = JSON.parse(window['__ELECTRON_WINDOW_MANAGER_EXTRA_DATA__'] || '{}');
      const windowsTabsOptions = extraData?.windowsTabsOptions;
      if (!windowsTabsOptions) {
        return undefined;
      }
      return {
        options: windowsTabsOptions?.tabOptions,
        isOnlyOneTabHideTabs: windowsTabsOptions?.isOnlyOneTabHideTabs,
        tabsId: windowsTabsOptions?.tabsId,
        tabsTitle: windowsTabsOptions?.tabsTitle,
      };
    } catch (error) {
      console.error('windowsTabs_getCurrentTab', error);
    }
    return undefined;
  }

  /** 打开新窗口 */
  async openWindow(query?: Omit<MsgQuery['windowsTabs_openTab']['query']['options'], ''>, tabsID?: string) {
    return await this.windowsTabs_openTab({
      options: {
        ...query,
      },
      tabsId: tabsID || this.getRandomUUID(),
      isOnlyOneTabHideTabs: true,
    } as any);
  }
  /** 关闭窗口 */
  async closeWindow(query?: MsgQuery['windowsTabs_closeTab']['query']) {
    return await this.windowsTabs_closeTab(query as any);
  }

  /**
   * 获取当前窗口信息
   * @returns 当前窗口信息
   */
  async getCurrentWindow(): Promise<MsgQuery['windowsTabs_openTab']['query'] | undefined> {
    try {
      // @ts-ignore
      const extraData = JSON.parse(window['__ELECTRON_WINDOW_MANAGER_EXTRA_DATA__'] || '{}');
      const windowsTabsOptions = extraData?.windowsTabsOptions;
      if (!windowsTabsOptions) {
        return undefined;
      }
      return {
        options: windowsTabsOptions?.tabOptions,
        isOnlyOneTabHideTabs: windowsTabsOptions?.isOnlyOneTabHideTabs,
        tabsId: windowsTabsOptions?.tabsId,
        tabsTitle: windowsTabsOptions?.tabsTitle,
      };
    } catch (error) {
      console.error('getCurrentWindow', error);
    }
    return undefined;
  }
}

export default new WebLynkerSDK();
