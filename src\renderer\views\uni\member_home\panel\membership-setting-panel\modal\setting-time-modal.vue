<template>
  <t-dialog
    v-model:visible="visible"
    :header="'编辑提醒时间'"
    attach="body"
    class="dialogSetLimitHeight"
    width="464px"
  >
    <template #body>
      <div class="toBody">
        <span class="toBody-item" v-for="(item, dayIndex) in days" :key="dayIndex">
          <!-- 会员到期时间前
          <t-input-number v-model="day"   theme="normal" />
          天提醒 -->
          <t-select v-replace-svg  v-model="item.day" :options="daySelectList"></t-select>
          <span class="inputItem-operate">
            <iconpark-icon   v-show="dayIndex === 0" name="iconaddpend"  class="cursor icon_addpend"  @click="onAdd"></iconpark-icon>
            <iconpark-icon name="iconclosecircle"  v-show="dayIndex !== 0"
              class="cursor icon_addpend"   @click="onDelete(dayIndex)"></iconpark-icon>

          </span>
        </span>
      </div>
    </template>
    <template #closeBtn>
      <!-- <svg class="iconpark-icon" style="width: 16px; height: 16px">
        <use href="#close" />
      </svg> -->
      <iconpark-icon name="iconerror" style="font-size: 24px"></iconpark-icon>
    </template>
    <template #footer>
      <div class="footer">
        <t-button
          theme="default"
          variant="outline"
          @click="onClose"
        >取消</t-button>
        <t-button theme="primary" @click="onSave">确定</t-button>
      </div>
    </template>
  </t-dialog>
</template>

<script setup lang="ts">
import { MessagePlugin } from "tdesign-vue-next";
import { ref, reactive } from "vue";
const emits = defineEmits(["onEmits"]);
const daySelectList = Array.from({length: 30}, (_, i)=> "到期前" + (i+1) + "天").map((v, vIndex)=> ({label: v, value: vIndex+1}));
const days = ref([{day: 3}]);

const visible = ref(false);

const onSave = () => {
  emits("onEmits", days.value);
  onClose();
};

const onAdd = () => {
  if(days.value.length > 2) {
    MessagePlugin.error('最多设置3次到期提醒')
  } else {
    days.value.push({day: 3})
  }
};
const onDelete = (rowIndex) => {
  days.value.splice(rowIndex, 1);
}

const onOpen = (val) => {
  // form.type = data.type;
  // form.members = data.members;
  days.value = val && val.length > 0 ? val.map(v=>({day: v})) : [{day: 3}];
  visible.value = true;
};


const onClose = () => {
  visible.value = false;
};

defineExpose({
  onOpen,
  onClose
});
</script>

<style scoped lang="less">
.toBody {
  // height: 26px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  &-item {
    font-size: 14px;

    font-weight: 400;
    text-align: left;
    color: #13161b;
    display: flex;
    align-items: center;
    gap: 12px;
    .icon_addpend {
      font-size: 24px;
    }
  }
}
:deep(.t-radio-group) {
  flex-direction: column;
  align-items: flex-start;
}

.selectRange {
  margin-top: 8px;
  display: flex;
  gap: 8px;
  &-item {
    padding: 0 8px;
    background: #f1f2f5;
    border-radius: 4px;

    font-size: 14px;

    font-weight: 400;
    color: #13161b;
    display: flex;
    align-items: center;
  }
  &-btn {
    font-size: 14px;

    font-weight: 400;
    color: #2069e3;
  }
}

.inputItem-operate {
  display: flex;
  align-items: center;
}
</style>
