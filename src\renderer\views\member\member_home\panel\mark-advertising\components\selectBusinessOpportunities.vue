<template>
  <div>
    <t-dialog v-model:visible="visible"  :header="t('ad.xzsj')"  @close="closeWin()"  :footer="false"  width="824">
      <!-- <template #header>
        <div style="display: flex; align-items: center; width: 100%; justify-content: space-between">
          <div>{{t('ad.xzsj')}}</div>
          <img
            style="width: 24px; cursor: pointer; height: 24px"
            src="@/assets/<EMAIL>"
            @click="closeWin()"
          />
        </div>
      </template> -->
      <div class="box">
        <div class="tips">
          <iconpark-icon name="iconinfo-b7dcijg4" style="font-size: 24px;"/>
          {{t('ad.xzsj_tips')}}
        </div>
        <div class="head-box"  @keyup.enter="fetchData()">
          <t-input
            v-model="searchValue"
            clearable
            :placeholder="t('clouddisk.searchKeywords')"
            style="padding-right: 8px"
            @bluer="fetchData()"
          >
            <template #prefix-icon>
              <img style="width: 20px; height: 20px" src="@/assets/svg/icon_search.svg" />
            </template>
          </t-input>
          <t-button style="min-width: 80px" theme="primary" @click="fetchData()">搜索</t-button>
        </div>
        <div class="flexbox" ref="scrollContainer">
          <div v-for="(item, index) in displayedItems" v-show="searchValue" @click="changeItem(item)" :key="index" class="sjbox">
            <!-- 重复 sjbox 内容 -->
            <div class="sjbox-left">
              <img :src="item.images[0]?.file_name" />
            </div>
            <div class="sjbox-info-box">
              <div class="sjbox-info" >
                <div :class="item.type === 1 ? '' : 'xq'" class="tags">{{ item.type === 1 ? "供应" : "需求" }}</div>
                <div class="sjbox-title" >{{ item.title }}</div>
              </div>
              <div class="sjbox-info">
                <div class="info-lable">{{t('ad.yxsj')}}</div>
                <div class="info-value">
                  {{ item.effective_unlimited ? "长期有效" : `${item.effective_begin} ~ ${item.effective_end}` }}
                </div>
              </div>
              <div class="sjbox-info">
                <div class="info-lable">{{t('ad.sjfl')}}</div>
                <div class="info-value">{{ item?.classify?.map((e) => e.alias).join("/") }}</div>
              </div>
              <div class="sjbox-info">

                <img v-if="item.apply_data?.avatar" class="sjbox-info-group" :src="item.apply_data?.avatar" />
                <img v-else class="sjbox-info-group" src="@/assets/svg/clouddisk/temaavatar.svg" />

                <div class="info-value">{{ item.apply_data?.name }}</div>
              </div>
            </div>
          </div>
          <img
            v-if="!searchValue"
            style="width: 250px;  margin: 190px auto 0"
            src="@/assets/member/Vector1.svg"
          />
          <div class="list-box empty" style="margin: 0 auto" v-if="searchValue && displayedItems.length === 0">
            <div>
              <img class="empty-img" src="@/assets/noApplyData.svg" alt="" />
            </div>
            <div class="font14">
              {{ t("zx.schedule.nosearch") }}
            </div>
          </div>
        </div>

        <!-- <div v-if="!items.length && !loading" class="no-results">
            没有找到匹配的结果。
          </div> -->
      </div>
    </t-dialog>
  </div>
</template>
<script setup lang="ts">
import { DialogPlugin, MessagePlugin } from "tdesign-vue-next";
import { useI18n } from "vue-i18n";
import { ref, onMounted, computed, watch, reactive, onUnmounted } from "vue";
import { businessListSQ } from "@renderer/api/business/index";
import { useRoute } from "vue-router";
import { useDigitalPlatformStore } from "@renderer/views/digital-platform/store/digital-platform-store";
import { platform } from "@renderer/views/digital-platform/utils/constant";
import { getMemberTeamID } from "@renderer/views/member/utils/auth";

const digitalPlatformStore = useDigitalPlatformStore();

const route = useRoute();

const searchValue = ref("");
const props = defineProps({
  platform: {
    type: String,
    default: "",
  },
});
const { t } = useI18n();

// 状态管理

const visible = ref(false);
const items = ref([]); // 存储所有搜索结果
const displayedItems = ref([]); // 当前显示的分页结果
const loading = ref(false); // 是否正在加载数据
const loadingMore = ref(false); // 是否正在加载更多数据
const currentPage = ref(1); // 当前页码
const pageSize = ref(10); // 每页显示的条目数量
const params = reactive({
  title: undefined,
  type: undefined,
  promotion_type: 1,
  promotion_related: "",
  is_top: undefined,
  page: 1,
  pageSize: 10,
});
const platformCpt = computed(() => {
  return props.platform || route.query?.platform;
});
const currentTeamId = computed(() => {
  if (platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount?.teamId;
  } else if (platformCpt.value === platform.digitalWorkbench) {
    return route.query?.teamId || 0;
  } else {
    if (!getMemberTeamID()) {
      return route.query?.teamId || 0;
    }
    return getMemberTeamID();
  }
});
const total = ref(0);
// 获取数据的函数
const fetchData = async () => {
  console.log(currentTeamId, "触发这里");
  businessListSQ({
    title: searchValue.value,
    page: currentPage.value,
    pageSize: pageSize.value,
    promotion_type: 2,
    promotion_related: currentTeamId.value,
    // promotion_related: "649990269132623872",
  }).then((res) => {
    displayedItems.value = res.data.data.list;
    total.value = res.data.data.total;
  });
};

// 加载更多的数据
const loadMore = async () => {
  if (displayedItems.value.length <= total.value) {
    currentPage.value += 1;
    businessListSQ({
      title: searchValue.value,
      page: currentPage.value,
      pageSize: pageSize.value,
      promotion_type: 2,
      promotion_related: currentTeamId.value,
    }).then((res) => {
      displayedItems.value.push(...res.data.data.list);
      total.value = res.data.data.total;
    });
  }
};

// 监听滚动事件
const scrollContainer = ref(null);
watch(scrollContainer, async (container) => {
  if (!container) return;
  container.addEventListener("scroll", handleScroll);
});

onMounted(() => {
  fetchData();
});
const closeWin = () => {
  visible.value = false;
  searchValue.value = "";
  currentPage.value = 1;
  displayedItems.value = [];
};
const emits = defineEmits(["changBusiness"]);

const changeItem = (item) => {
  visible.value = false;
  console.log(item, "itemitemchangbusiness");
  emits("changBusiness", item);
};
// 处理滚动事件
const handleScroll = () => {
  const container = scrollContainer.value;
  if (container.scrollHeight - container.scrollTop === container.clientHeight) {
    console.log("滚动到底部");
    loadMore();
  }
};

// 清理事件监听器
onUnmounted(() => {
  const container = scrollContainer.value;
  if (container) {
    container.removeEventListener("scroll", handleScroll);
  }
});

// 监听搜索值变化，重置分页
watch(searchValue, () => {
  currentPage.value = 1;
  displayedItems.value = [];
  fetchData();
});
const openWin = () => {
  searchValue.value=''

  visible.value = true;
  fetchData();
};
defineExpose({
  openWin,
});
const seachEnter = () => {};
</script>
<style lang="less" scoped>
  ::-webkit-scrollbar {
  width: 4px;
  background-color: transparent;
}
/*定义滚动条轨道 内阴影+圆角*/
::-webkit-scrollbar-track {
  background-color: transparent;
}
/*定义滑块 内阴影+圆角*/
::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background: var(--divider-kyy_color_divider_deep, #D5DBE4);
}
/* 保持原有样式 */
.loading-more {
  display: flex;
  justify-content: center;
  margin: 16px 0;
}
.no-results {
  text-align: center;
  color: #828da5;
  margin: 16px 0;
}
.sjbox {
  display: flex;
  padding: 12px;
  align-items: center;
  border: 1px solid transparent;
  width: 100%;

  gap: 12px;
  align-self: stretch;
  height: 145px;
  border-radius: 8px;
  background: var(--bg-kyy_color_bg_deep, #f5f8fe);
}
.head-box {
  display: flex;
  align-items: center;
  margin-top: 32px;
  margin-bottom: 12px;
}
.flexbox {
  display: flex;
    flex-wrap: wrap;
    gap: 12px;
    height: 436px;
    align-content: flex-start;
    overflow: auto;
}
.sjbox:hover {
  border: 1px solid var(--brand-kyy_color_brand_default, #4d5eff);
}
.sjbox-info-box {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
  .sjbox-info {
    display: flex;
    width: 100%;
    align-items: center;
    gap: 8px;
  }
  .xq {
    background: var(--kyy_color_tag_bg_warning, #ffe5d1);
    color: var(--kyy_color_tag_text_warning, #fc7c14);
  }
  .tags {
    display: flex;
    height: 20px;
    min-height: 20px;
    max-height: 20px;
    padding: 0px 4px;
    justify-content: center;
    align-items: center;
    gap: 4px;
    border-radius: var(--kyy_radius_tag_s, 4px);
    background: var(--kyy_color_tag_bg_cyan, #e6f9f8);
    color: var(--kyy_color_tag_text_cyan, #11bdb2);
    text-align: center;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px; /* 166.667% */
  }
  .sjbox-title {
    overflow: hidden;
    color: var(--text-kyy_color_text_1, #1a2139);
    text-overflow: ellipsis;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px; /* 157.143% */
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    word-break: break-all;
    max-width: 550px;

  }
  .info-lable {
    overflow: hidden;
    color: var(--text-kyy_color_text_3, #828da5);
    text-overflow: ellipsis;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
  .sjbox-info-group {
    width: 24px;
    height: 24px;
    border-radius: 50%;
  }
  .info-value {
    overflow: hidden;
    color: var(--text-kyy_color_text_1, #1a2139);
    text-overflow: ellipsis;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
}
.sjbox-left {
  img {
    width: 121px;
    height: 121px;
    border-radius: 8px;
  }
}
.tips{
  background: #EAECFF;
  display: flex;
  align-items: center;
  padding: 8px 24px;
  gap: 8px;
  border-radius: 8px;
  color: #1A2139;
  margin-bottom: 16px;
}
</style>
