import { useRoute, useRouter } from "vue-router";
import { i18n } from "@/i18n";
import { useUniStore } from "@renderer/views/uni/store/uni";

export default function useNavigate() {
  const route = useRoute();
  const router = useRouter();
  const store = useUniStore();
  // @ts-ignore
  const t = i18n.global.t;



  const goActivePage = (item) => {
    console.log(item)
    store.addTab(item,true);
    router.push(item.fullPath);
  }

  return {
    goActivePage,
  };

}
