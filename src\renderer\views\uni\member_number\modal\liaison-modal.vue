<template>
  <t-dialog v-model:visible="visible" :footer="null" :close-btn="true" width="560" attach="body"
    class="dialogSet dialogHeight560  dialogSet-noBodyPadding dialogSetHeader" :close-on-overlay-click="false" :close-on-esc-keydown="false">
    <template #header>
      <div class="header">
        <div class="tabs">
          <div class="tabs-item cursor" v-for="(tab, tabIndex) in tabs" :class="{active: currentTab === tab.value}" @click="onSwitchTab(tab?.value)" :key="tabIndex">
            {{tab?.label}}
          </div>
          <span class="trick">
            <Tricks :uuid="'数字高校-平台交流'" :size="'small'" :isDrag="false"/>
          </span>
        </div>
        <t-button class="set" v-show="props.isManage" theme="default" @click="onGoAdminContactS">设置</t-button>
      </div>
    </template>
    <template #closeBtn>
      <!-- <svg class="iconpark-icon" style="width: 16px; height: 16px">
        <use href="#close" />
      </svg> -->
      <iconpark-icon name="iconerror" style="font-size: 24px"></iconpark-icon>
    </template>
    <template #body>
      <div class="body" v-show="currentTab === TabType.PLATFORM">
        <div class="scroll">
          <groupsList v-if="groupListData.length>0" :list="groupListData"  :dataInfo="dataInfo"></groupsList>

          <div class="box" v-for="groups in members.group_list" :key="groups.id">
            <div class="box-title line-1">{{ groups.group_name }}</div>
            <div class="box-group g2">
              <div class="box-group-item2"
                v-if="groups.group_contact?.length > 0"
                v-for="member in  groups.group_contact.slice(0,groups.is_expand?groups.group_contact.length :2)" :key="member.card_id" @click="onGoMessage(member)">
                <span class="btnconnact">
                  <iconpark-icon name="icongroupmember" class="icon"></iconpark-icon>
                  <span class="btnconnact-text">联系TA</span>
                </span>
                <!-- <KyyAvatar class="avatar" avatar-size="44px" :image-url="member.avatar" :user-name="member.name"
                  shape="circle" /> -->
                <t-image class="pho" :src="getSrcThumbnail(member.photo)" fit="cover">
                  <template #loading>
                    <img class="pho" src="@renderer/assets/digital/icon/avatar_default.png" />
                  </template>
                  <template #error>
                    <img class="pho" src="@renderer/assets/digital/icon/avatar_default.png" />
                  </template>
                </t-image>
                <div class="toleft">
                  <span class="top line-1">
                    {{ member?.name }}
                  </span>
                  <div class="bottom">
                    <span class="tip line-1">
                      {{ $filters.isPeriodEmpty(member?.position?.departmentName) }}/{{ $filters.isPeriodEmpty(member?.position?.jobName) }}
                    </span>
                    <!-- <span class="btnconnact">
                      <iconpark-icon name="icongroupmember" class="icon"></iconpark-icon>
                      <span class="btnconnact-text">联系TA</span>
                    </span> -->
                  </div>
                  <t-tooltip
                    placement="top"
                    :content="member.describe"
                  >
                    <span class="describe line-2 mt-2px" v-show="member.describe">
                      <!-- <MyTooltipComp :text="member.describe"></MyTooltipComp> -->
                      {{member.describe}}
                    </span>
                  </t-tooltip>
                </div>
              </div>
              <div class="moreBox" v-if="onIsOkOpen(groups)">
                <div class="more cursor" @click="onClickExpand(groups)">
                  <img class="expand"  v-show="!groups.is_expand" v-if="onIsOkOpen(groups)" src="@renderer/assets/digital/expand/expand.svg">
                  <img class="expand_hover" v-show="!groups.is_expand" src="@renderer/assets/digital/expand/expand_hover.svg">
                  <img class="un_expand" v-show="groups.is_expand" v-if="onIsOkOpen(groups)" src="@renderer/assets/digital/expand/un_expand.svg">
                  <img class="un_expand_hover" v-show="groups.is_expand" src="@renderer/assets/digital/expand/un_expand_hover.svg">

                </div>
              </div>

            </div>
          </div>
          <div class="box" v-if="members.no_group_contact_list?.length > 0">
            <div class="box-title line-1">联络人</div>
            <div class="box-group">
              <div class="box-group-item2"
                v-for="member in members.no_group_contact_list" :key="member.card_id" @click="onGoMessage(member)">
                <!-- <KyyAvatar class="avatar" avatar-size="44px" :image-url="member.avatar" :user-name="member.name"
                  shape="circle" /> -->
                <span class="btnconnact">
                  <iconpark-icon name="icongroupmember" class="icon"></iconpark-icon>
                  <span class="btnconnact-text">联系TA</span>
                </span>
                <t-image class="pho" :src="getSrcThumbnail(member.photo)" fit="cover">
                  <template #loading>
                    <img class="pho" src="@renderer/assets/digital/icon/avatar_default.png" />
                  </template>
                  <template #error>
                    <img class="pho" src="@renderer/assets/digital/icon/avatar_default.png" />
                  </template>
                </t-image>
                <div class="toleft">
                  <span class="top line-1">
                    {{ member?.name }}
                  </span>
                  <div class="bottom">
                    <span class="tip line-1">
                      {{ $filters.isPeriodEmpty(member?.position?.departmentName) }}/{{ $filters.isPeriodEmpty(member?.position?.jobName) }}
                    </span>
                    <!-- <span class="btnconnact">
                      <iconpark-icon name="icongroupmember" class="icon"></iconpark-icon>
                      <span class="btnconnact-text">联系TA</span>
                    </span> -->
                  </div>
                  <t-tooltip
                    placement="top"
                    :content="member.describe"
                  >
                    <span class="describe line-2 mt-2px" v-show="member.describe">
                      <!-- <MyTooltipComp :text="member.describe"></MyTooltipComp> -->
                      {{member.describe}}
                    </span>
                  </t-tooltip>
                </div>
              </div>
            </div>
          </div>
          <div class="empty" v-if="!(members.no_group_contact_list?.length > 0 || members.group_list?.length > 0|| groupListData?.length > 0)">
            <Empty name="no-contact"/>
          </div>
        </div>
      </div>

      <div class="body" v-show="currentTab === TabType.CONTACT">
        <div class="scroll">
          <div class="rowTeam" v-if="contactInfo && contactInfoComputed">
            <div class="rowTeam-item" v-for="(ph, phIndex) in contactInfo?.phone_data" :key="phIndex">
              <div class="rowTeam-item-title">
                服务电话{{ contactInfo?.phone_data?.length > 1 ? phIndex + 1: '' }}
              </div>
              <div class="rowTeam-item-content">
                {{ph}}
              </div>
            </div>
            <div class="rowTeam-item" v-show="contactInfo?.address">
              <div class="rowTeam-item-title">
                联络地址
              </div>
              <div class="rowTeam-item-content">
                {{contactInfo?.address}}
              </div>
            </div>
            <div class="rowTeam-item" v-show="contactInfo?.zip_code">
              <div class="rowTeam-item-title">
                邮编
              </div>
              <div class="rowTeam-item-content">
                {{contactInfo?.zip_code}}
              </div>
            </div>
            <div class="rowTeam-item" v-show="contactInfo?.fax">
              <div class="rowTeam-item-title">
                传真
              </div>
              <div class="rowTeam-item-content">
                {{contactInfo?.fax}}
              </div>
            </div>
            <div class="rowTeam-item" v-show="contactInfo?.email">
              <div class="rowTeam-item-title">
                邮箱
              </div>
              <div class="rowTeam-item-content">
                {{contactInfo?.email}}
              </div>
            </div>
          </div>
          <Empty v-else name="no-contact" />
        </div>
      </div>
    </template>
    <!-- <template #footer>

    </template> -->
  </t-dialog>
</template>

<script setup lang="ts">
import KyyAvatar from "@/components/kyy-avatar/index.vue";
import { useDigitalPlatformStore } from '@renderer/views/digital-platform/store/digital-platform-store';
import { goToAdmin } from '@renderer/views/uni/utils/auth';
import groupsList from "@renderer/views/member/member_number/modal/groups-list.vue";

import { computed, reactive, ref } from 'vue';
import { useRouter } from 'vue-router';
import { onGetGovGroupCommunicationAxios } from "@renderer/api/uni/api/businessApi";
import { getImCardIds } from "@renderer/utils/auth";
import { getGroupChatListApi } from "@renderer/api/im/api";

import { getResponseResult } from "@renderer/utils/myUtils";
import { getGroupInfoApi } from "@renderer/api/digital-platform/api/businessApi";
import { openChat } from "@renderer/utils/share";
import ChatAvatar from '@renderer/views/message/components/ChatAvatar.vue'
import { MessagePlugin } from "tdesign-vue-next";
import Empty from "@renderer/components/common/Empty.vue";
import {
  getOpenid,
} from "@renderer/utils/auth";

import { getSrcThumbnail } from "@renderer/views/message/service/msgUtils";
import { useI18n } from "vue-i18n";
import {getContactInformationDetailAxios} from '@renderer/api/digital-platform/api/businessApi'
// import MyTooltipComp from "@renderer/components/engineer/components/MyTooltipComp.vue";

import { to } from 'await-to-js';
const { t } = useI18n();

const store = useDigitalPlatformStore();

const props = defineProps({
  teamId: {
    type: Boolean,
    default: false,
  },
  isManage: {
    type: Boolean,
    default: false
  }
});
const groupListData=ref([])
const members = reactive({ list: [], total: 0 });
const group = ref(null);

const router = useRouter();
const visible = ref(false);
// const emits = defineEmits(['toVerifyWeb']);


enum TabType {
  PLATFORM = "platform",
  CONTACT = "contact",
}
const tabs = [
  {
    label: t("member.fieldlock.e"),
    value: TabType.PLATFORM,
  },
  {
    label: "联络方式",
    value: TabType.CONTACT,
  },
];

const currentTab = ref(TabType.PLATFORM)

const contactInfo = ref(null);

const contactInfoComputed = computed(()=> {
  return (contactInfo.value?.phone_data &&  contactInfo.value?.phone_data?.length > 0) ||
    contactInfo.value?.address || contactInfo.value?.zip_code || contactInfo.value?.fax ||
    contactInfo.value?.email
})

const onGetSettingInfo = async () => new Promise(async (resolve, reject) => {
  contactInfo.value = null;
  const [err, res] = await to(getContactInformationDetailAxios({
    channel_type: 'uni'
  }, props.teamId));
  if (err) return reject();
  if (res) {
    const { data } = res;
    console.log(data);
    contactInfo.value = data?.data;
    resolve(data?.data);

  } else {
    reject();
  }
});


const onGoAdminContactS = () => {
  const params = {
    origin: 'message',
    redirect: 'ContactPersonPanel'
  }
  goToAdmin(props.teamId, params);
}



const dataInfo = ref(null);
const scrollDisabled = computed(() => members.list.length >= members.total);
const pagination = {
  page: 1,
  pageSize: 20
}

const onClose = () => {
  visible.value = false;
};
const getPerNum = async () => {
  try {

    const mycards = getImCardIds();
    let res = await getGroupChatListApi(["PT"+dataInfo.value.platformStaff], 0, 100, false, [20]);
    groupListData.value = res.data.array.groups.arr;

  } catch (error) {
    console.log(error);
  }
};
const onOpen = async (val) => {
  console.log(val)
  pagination.page = 1;
  members.list = [];
  members.total = 0;
  dataInfo.value = val;
  setTimeout(()=> {
    visible.value = true;
  }, 500)
  // if(val.im_group) {
  //   onGetPlatformGroup();
  // }
  onGetMemberGroup();
  getPerNum();
  onGetSettingInfo();

  // const result = await Promise.all([, ])
  // console.log(result)
};


const onSwitchTab = (val) => {
  currentTab.value = val;
  console.log(val);
};


const handleInfiniteOnLoad = () => {
  console.log('异步加载数据等逻辑',scrollDisabled.value)
  // 异步加载数据等逻辑
  if (scrollDisabled.value) {
    // 数据加载完毕
  } else {
    // 加载数据列表
    // pagination.page++;
    // onGetMemberGroup();
  }
};


// 获取平台群
const onGetPlatformGroup = () => {
  let result = null;
  return new Promise(async (resolve, reject) => {
    try {
      result = await getGroupInfoApi(dataInfo.value?.im_group);
      result = getResponseResult(result);

      if (!result) {
        reject();
        return;
      };
      // result = result;
      console.log(result)
      group.value = result;
      resolve(result)
    } catch (error) {
      const errMsg = error instanceof Error ? error.message : error;
      // MessagePlugin.error(errMsg);
      console.log(errMsg)
      reject();
    }
  })
};


// 获取平台交流列表
const onGetMemberGroup = () => {
  let result = null;
  return new Promise(async (resolve, reject) => {
    try {
      // result = await onGetGovGroupCommunicationAxios({ with_position: 1, ...pagination }, dataInfo.value?.teamId);
      result = await onGetGovGroupCommunicationAxios({ platform_id:  dataInfo.value?.platformStaff }, dataInfo.value?.teamId);
      result = getResponseResult(result);

      if (!result) {
        reject();
        return;
      };
      result = result.data;
      // if(result.list) result.list = result.list.filter(item => !item.deleted_at)
      members.group_list = result.group_list?.map((v)=> {
        v.is_expand = false;
        return v;
      }) || [];
      members.no_group_contact_list = result.no_group_contact_list || [];
      // members.total = result.total || 0;
      console.log(result)
      resolve(result)
    } catch (error) {
      const errMsg = error instanceof Error ? error.message : error;
      // MessagePlugin.error(errMsg);
      console.log(errMsg)
      reject();
    }
  })
};
// 跳消息体，拿平台身份
// 跳消息体，拿平台身份
const onGoMessage = (member) => {
  console.log(dataInfo.value, member.idStaff)
  // 要用openid来辨别身份
  if(getOpenid() === member.openid) return MessagePlugin.error('无法对自己发起聊天');
  // openChat({ main: 'PT' + dataInfo.value?.platformStaff, peer: '$' + member.idStaff });
  openChat({ main: 'PT' + dataInfo.value?.platformStaff, peer: member.card_id });
}

const onIsOkOpen = (groups) => {
    if(groups.group_contact?.length > 2) {
      return true;
    }
  return false;
}

const onClickExpand = (groups) => {
  groups.is_expand =!groups.is_expand;
}

const onGoGroupMessage = (groups) => {
  openChat({ main: 'PT' + dataInfo.value?.platformStaff, group: groups.group_im });
}

defineExpose({
  onOpen,
  onClose
});
</script>

<style lang="less" scoped>
@import "@renderer/views/engineer/less/common.less";
@import '@renderer/views/member/member_number/less/liaison-modal.less';
.empty {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.moreBox {
  width: 100%;
  display: flex;
  justify-content: center;
}

.more {
  margin: 0 auto;
  display: flex;

  .expand {
    display:block
  }
  .expand_hover {
    display:none
  }
  .un_expand {
    display:block
  }
  .un_expand_hover {
    display:none
  }
  &:hover {
    .expand {
      display:none
    }
    .un_expand {
      display:none
    }
    .expand_hover {
      display:block
    }
    .un_expand_hover {
      display:block;
    }
  }
}
</style>
