<template>
  <div class="box-flex">
    <div class="right-box" style="background: #fff;">
      <div class="right-box-head">
        <span class="addzj">{{ t("banch.ktszgcglyqx") }}</span>
        <t-button @click="addAdmin">
          <template #icon><add-icon /></template>
          {{ t("banch.tjgly") }}
        </t-button>
      </div>
      <div class="right-box-content">
        <!-- 受控用法，示例代码有效，勿删 -->
        <t-config-provider>
          <t-table row-key="index" height="100%" :columns="columns" :data="data">
            <template #name="{ row }">
              <div style="display: flex; align-items: center">
                <kyyAvatar style="padding: 0 4px" :image-url="row.avatar" avatar-size="32px" :user-name="row.name" />
                <div style="padding-left: 12px">{{ row.name }}</div>
                <div class="lzbox" style="margin-left: 4px" v-if="row?.deleted_at">{{ t("banch.ylz") }}</div>
              </div>
            </template>
            <template #type="{ row }">
              <div style="display: flex; align-items: center">
                {{ row.type === 1 ? t("banch.admin") : t("banch.supadmin") }}
              </div>
            </template>

            <template #operate="{ row }">
              <span>
                <!-- v-if="isAdmin(row)" -->
                <div class="btn" :style="{ color: row.type === 2 ? ' #4D5EFF' : 'red' }" @click="chengAdmin(row)">
                  {{
                    row.type === 2
                      ? activationGroupItem.staffId === row.id_staff || checkIsAdminData.isAdmin
                        ? t("clouddisk.transferSuperAdministrator")
                        : ""
                      : "移除"
                  }}
                </div>
              </span>
            </template>
            <template #empty>
              <div>
                <Empty
                  :tip="t('banch.zwsj')"
                  name="no-friend-list"
                />
              </div>
            </template>
          </t-table>
        </t-config-provider>
      </div>
    </div>
    <!-- 转移弹窗 -->
    <t-dialog v-model:visible="transferDialog" :close-btn="false" :header="true" :footer="true" width="505">
      <template #header>
        <div style="display: flex; align-items: center; width: 100%; justify-content: space-between">
          <div @click="transferDialog = true">{{ t("banch.changsupadmin") }}</div>
          <img
            style="width: 24px; cursor: pointer; height: 24px"
            src="@/assets/<EMAIL>"
            @click="(transferDialog = false), (listData = null), (radioFlag = false)"
          />
        </div>
      </template>
      <div class="content-box">
        <div class="item-box" style="width: 100%; margin-bottom: 8px">
          <div class="value-text">1、{{ t("banch.changsupadmin") }}</div>
          <div class="flexbox">
            <div v-if="!listData" class="add-box-btn" style="margin-top: 9px" @click="transferAdmin">
              <!-- <img class="add-icon" src="@/assets/<EMAIL>" /> -->
              <iconpark-icon name="iconadd" class="add-icon"></iconpark-icon>
            </div>
            <div v-else class="row-data-box" @click="transferAdmin">
              <kyyAvatar
                style="padding: 0 4px"
                :image-url="listData.avatar"
                avatar-size="32px"
                :user-name="listData.name"
              />
              <div class="avatar-name">
                {{ listData.name }}
              </div>
            </div>
          </div>
        </div>
        <div class="item-box" style="width: 100%">
          <div class="value-text" style="margin-bottom: 12px">2、{{ t("banch.yuansupadmin") }}：</div>
          <t-radio-group :default-value="false" v-model="radioFlag" style="margin-left: 20px" @change="onChange">
            <t-radio style="width: 100%" :value="false">{{ t("banch.jybcjgly") }}</t-radio>

            <t-radio :value="true">{{ t("banch.admindel") }} </t-radio>
          </t-radio-group>
        </div>
      </div>
      <template #footer>
        <div style="display: flex; align-items: center; width: 100%; justify-content: flex-end">
          <t-button
            class="min-80"
            theme="default"
            variant="outline"
            @click="(transferDialog = false), (listData = null), (radioFlag = false)"
          >
            取消
          </t-button>
          <t-button class="min-80" @click="confirmAdmin">
            {{ t("home.openPreloadWindowError.confirm") }}
          </t-button>
        </div>
      </template>
    </t-dialog>
    <select-personnel
      ref="selectPersonnelsRef"
      :max-num="5000"
      :disabled-array="disabledArray"
      :header="t('clouddisk.addMembers')"
      :options="addAdminData"
      @sub-form="onSelectItems"
      :tipes="'200'"
    />
    <select-personnel
      ref="selectPersonnelsTransferRef"
      :max-num="1"
      :tipes="'200'"
      :disabled-array="disabledTransferArray"
      :header="t('clouddisk.transferSuperAdministrator')"
      :options="addAdminData"
      @sub-form="onSelectItemsTransfer"
    />
  </div>
</template>
<script setup lang="jsx">
import { ref, onMounted, computed, watch, nextTick, onUnmounted } from "vue";
import { useI18n } from "vue-i18n";
import { CloudManager, DelManager, AddDiskManager, DiskUsers, managerChangeOwner } from "@renderer/api/cloud.ts";
import {
  adminWorkshop,
  adminAppStaff,
  workshopAdmin,
  delAdmin,
  changeAdmin,
  checkIsAdmin,
} from "@renderer/api/workBench/index.ts";
import { DialogPlugin, MessagePlugin } from "tdesign-vue-next";
import { CaretDownSmallIcon } from "tdesign-icons-vue-next";
import SelectPersonnel from "@renderer/views/engineer/components/select-personnel/index.vue";
import Empty from "@renderer/components/common/Empty.vue";

import kyyAvatar from "@renderer/components/kyy-avatar/index.vue";
import { AddIcon } from "tdesign-icons-vue-next";
const { t } = useI18n();
const columns = ref([
  { colKey: "name", title: t("contacts.userName"), ellipsis: true },
  { colKey: "type", title: t("clouddisk.type") },
  { colKey: "operate", title: "操作" },
]);
const disabledArray = ref([]);
const radioFlag = ref(false);
const addAdminData = ref([]);
const selectPersonnelsRef = ref(null);
const selectPersonnelsTransferRef = ref(null);
const props = defineProps({
  tabList: {
    type: Array,
    default: () => [],
  },
  activationGroupItem: {
    type: Object,
    default: () => {},
  },
});
const emits = defineEmits(["getManagerDiskUsersList", "getGroupListApi", "openRefreshs"]);
const data = ref([]);
let addnumberFlag = false;
const addAdmin = () => {
  adminAppStaff(props.activationGroupItem.teamId)
    .then((res) => {
      console.log(res, "resssssssssss");
      addnumberFlag = false;
      for (let index = 0; index < res.data.data.length; index++) {
        const element = res.data.data[index];
        element.staffId = element.idStaff;
      }
      addAdminData.value = res.data.data;

      selectPersonnelsRef.value.openWindow();
    })
    .catch((error) => {
      addnumberFlag = false;
      console.log(error, "errorerrorerror");
      // MessagePlugin.error(error.response.data.message);
    });
};

const selectedActorsList = ref([]);

const transferAdmin = async () => {
  try {
    const res = await adminAppStaff(props.activationGroupItem.teamId);
    addnumberFlag = false;
    for (let index = 0; index < res.data.data.length; index++) {
      const element = res.data.data[index];
      element.staffId = element.idStaff;
    }
    addAdminData.value = res.data.data;
    selectPersonnelsTransferRef.value.openWindow();
  } catch (error) {
    addnumberFlag = false;
    MessagePlugin.error(error.response.data.message);
  }
};
watch(
  () => props.activationGroupItem.teamId,
  (newValue) => {
    getList();
  },
);
onMounted(() => {
  if (props.activationGroupItem.teamId) {
    getList();
  }
});

let listData = ref(null);

const onChange = (val) => {
  radioFlag.value = val;
};
const confirmAdmin = async () => {
  if (!listData.value) {
    return;
  }
  try {
    const res = await changeAdmin(
      {
        id: rowData.value.id,
        idStaff: listData.value.idStaff,
        action: radioFlag.value ? 2 : 1,
      },
      props.activationGroupItem.teamId,
    );

    emits("getGroupListApi");

    if (res.status === 200) {
      MessagePlugin.success("操作成功");
      getList();
      listData.value = null;
      transferDialog.value = false;
    } else {
      emits("getManagerDiskUsersList");
      MessagePlugin.error(res.data.message);
    }
  } catch (err) {
    MessagePlugin.error(err.data.message);
    getList();
  }
};
const onSelectItemsTransfer = (val) => {
  listData.value = val[0];
};
const checkIsAdminData = ref(null);
const onSelectItems = async (val) => {
  if (val.length === 0) {
    return;
  }
  try {
    const res = await workshopAdmin(
      {
        idStaffs: val.map((e) => e.idStaff),
      },
      props.activationGroupItem.teamId,
    );

    if (res.status === 200) {
      MessagePlugin.success("操作成功");
      getList();
    } else {
      MessagePlugin.error(t("banch.opterror"));
    }
  } catch (err) {
    MessagePlugin.error(t("banch.opterror"));
  }
};
const getList = async () => {
  try {
    checkIsAdmin({ ...props.activationGroupItem.user_ids }, props.activationGroupItem.teamId).then((res) => {
      checkIsAdminData.value = res.data.data;
    });
    const res = await adminWorkshop(props.activationGroupItem.teamId);
    disabledArray.value = [];
    if (res.status === 200) {
      data.value = res.data.data.list;
      data.value.forEach((item) => {
        disabledArray.value.push(item.id_staff);
      });
    } else {
      emits("getManagerDiskUsersList");
      data.value = [];
      MessagePlugin.error(res.data.message);
    }
  } catch (err) {
    console.log(err, "呃呃呃呃呃呃呃呃呃呃呃呃呃呃呃");
    emits("getManagerDiskUsersList");
    data.value = [];
    if (err.response.data.code === 1010) {
      emits("openRefreshs");
    }
    MessagePlugin.error(err.response.data.message);
  }
};
const administratorOrenterprise = ref(false);
const transferDialog = ref(false);
const rowData = ref(null);
const disabledTransferArray = ref([]);
const chengAdmin = (row) => {
  rowData.value = row;
  disabledTransferArray.value = [row.id_staff];
  if (row.type === 2) {
    transferDialog.value = true;
  } else {
    const confirmDia = DialogPlugin({
      header: t("banch.delok"),
      theme: "info",
      class: "delmode",
      body: t("banch.delokal"),
      closeBtn: null,
      confirmBtn: t("identity.confirm"),
      cancelBtn: "取消",
      onClose: () => {
        confirmDia.hide();
      },
      onConfirm: async () => {
        const res = await delAdmin(row.id, props.activationGroupItem.teamId);
        if (res.status === 200) {
          MessagePlugin.success("操作成功");

          getList();
        } else {
          MessagePlugin.error(res.data.message);
        }
        confirmDia.hide();
      },
    });
  }
};
</script>

<style lang="less" scoped>
.row-data-box {
  cursor: pointer;

  text-align: center;
  margin-top: 10px;
}
.flexbox {
  display: flex;
}
.item-box {
  .add-icon {
    width: 16px;
    font-size: 16px;
    height: 16px;
  }
  .add-pre {
    height: 22px;
    font-size: 14px;

    font-weight: 400;
    text-align: left;
    color: #2069e3;
    line-height: 22px;
    padding-left: 4px;
  }
  .lab-text {
    height: 22px;
    font-size: 14px;

    font-weight: 400;
    text-align: left;
    margin-bottom: 8px;
    color: #a1a2a4;
    line-height: 22px;
  }
  .value-text {
    font-size: 14px;

    font-weight: 400;
    text-align: left;
    color: #13161b;
    line-height: 22px;
  }
}
.right-box-head {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}
.flex-a {
  display: flex;
  align-items: center;
}
.flex-j-s {
  display: flex;
  justify-content: space-between;
}
.btn {
  display: flex;
  align-items: center;
  gap: 16px;
  cursor: pointer;
  span {
    color: var(--color-button_text_brand-kyy_color_button_text_brand_font_default, #4d5eff);
    text-align: center;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
  span:hover {
    color: #707eff;
  }
}
.add-box-btn {
  width: 32px;
  height: 32px;
  border: 1px solid #e3e6eb;
  border-radius: 5px;
  margin-top: 12px;
  margin-left: 20px;
  text-align: center;
  line-height: 32px;
  cursor: pointer;
}
.lzbox {
  border-radius: var(--kyy_radius_tag_s, 4px);
  background: var(--kyy_color_tag_bg_gray, #eceff5);
  display: flex;
  height: 20px;
  min-height: 20px;
  max-height: 20px;
  padding: 0px 4px;
  color: var(--kyy_color_tag_text_gray, #516082);
  text-align: center;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px; /* 166.667% */
  justify-content: center;
  align-items: center;
  gap: 4px;
}
.add-icon {
  width: 16px;
  font-size: 16px;
  height: 16px;
}
.right-box-content {
  overflow: auto;
  .right-box-item {
    border-radius: 8px;
    background: var(--bg-kyy_color_bg_deep, #f5f8fe);
    display: flex;
    padding: 8px 12px;
    align-items: center;
    align-self: stretch;
    margin-bottom: 8px;
    img {
      width: 36px;
      height: 36px;
      margin-right: 12px;
    }
    .title-box {
      display: flex;
      align-items: center;
      .title {
        color: var(--text-kyy_color_text_1, #1a2139);
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 22px; /* 157.143% */
      }
      .title-tag {
        border-radius: var(--kyy_radius_tag_full, 999px);
        background: var(--kyy_color_tag_bg_success, #e0f2e5);
        display: flex;
        height: 20px;
        min-height: 20px;
        max-height: 20px;
        padding: 1px 8px;
        justify-content: center;
        align-items: center;
        gap: 10px;
        color: var(--kyy_color_tag_text_success, #499d60);
        text-align: right;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
      }
      .app-tips {
        color: var(--text-kyy_color_text_3, #828da5);
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px; /* 166.667% */
      }
    }
  }
}
.addzj {
  color: var(--text-kyy_color_text_2, #516082);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
}
.right-box {
  padding: 16px 16px 0;
  width: 100%;
  display: flex;
  flex-direction: column;
}
.items {
  cursor: pointer;
  height: 48px;
  background: #fff;
  border-radius: 4px;
  margin-bottom: 16px;
  padding: 5px 16px;
  display: flex;
  align-items: center;
  color: var(--lingke-black-90, #1a2139) !important;
  font-weight: 600;
  img {
    width: 20px;
    height: 20px;
    margin-right: 8px;
  }
}
.items:hover {
  background: var(--lingke-select, #e1eaff) !important;
}
.isactive {
  background: var(--bg-kyy-color-bg-list-foucs, #e1eaff) !important;

  color: #2069e3 !important;
}
.head-text {
  font-size: 16px;

  font-weight: 700;
  color: #13161b;
  padding: 20px 0 20px 16px;
  line-height: 24px;
}

.box-flex {
  display: flex;
  flex: 1;
  width: 100%;
  background: #fff;
  height: 100%;
}
</style>
