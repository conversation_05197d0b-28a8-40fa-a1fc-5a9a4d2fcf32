<svg width="756" height="138" viewBox="0 0 756 138" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_44_12049)">
<rect width="756" height="138" rx="8" fill="#12C2CC"/>
<g filter="url(#filter0_d_44_12049)">
<rect width="712" height="138" rx="8" fill="url(#paint0_linear_44_12049)" shape-rendering="crispEdges"/>
<rect width="712" height="138" rx="8" fill="url(#paint1_linear_44_12049)" shape-rendering="crispEdges"/>
<path d="M465.177 -22.5591C463.589 -27.7049 467.436 -32.918 472.822 -32.918L554.513 -32.918L650.727 -25.4554C658.216 -24.8745 660.848 -15.225 654.691 -10.922L510.742 89.6799C506.297 92.7865 500.114 90.6635 498.515 85.4814L465.177 -22.5591Z" fill="url(#paint2_linear_44_12049)" fill-opacity="0.16"/>
<path d="M439.177 -37.5591C437.589 -42.7049 441.436 -47.918 446.822 -47.918L528.513 -47.918L624.727 -40.4554C632.216 -39.8745 634.848 -30.225 628.691 -25.922L484.742 74.6799C480.297 77.7865 474.114 75.6635 472.515 70.4814L439.177 -37.5591Z" fill="url(#paint3_linear_44_12049)" fill-opacity="0.1"/>
<path d="M358.358 101.948C362.02 97.9997 368.459 98.7247 371.151 103.388L411.997 174.136L453.641 261.191C456.883 267.967 449.842 275.071 443.037 271.89L283.939 197.528C279.026 195.231 277.773 188.815 281.461 184.839L358.358 101.948Z" fill="url(#paint4_linear_44_12049)" fill-opacity="0.1"/>
<g filter="url(#filter1_f_44_12049)">
<ellipse cx="86" cy="99" rx="64" ry="59" fill="#3D74FF" fill-opacity="0.5"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_44_12049" x="-36" y="-83.918" width="784" height="392.591" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="18"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.32 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_44_12049"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_44_12049" result="shape"/>
</filter>
<filter id="filter1_f_44_12049" x="-98" y="-80" width="368" height="358" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="60" result="effect1_foregroundBlur_44_12049"/>
</filter>
<linearGradient id="paint0_linear_44_12049" x1="2.51023e-06" y1="59.4277" x2="711.794" y2="47.0241" gradientUnits="userSpaceOnUse">
<stop stop-color="#5D65E6"/>
<stop offset="1" stop-color="#5C56DC"/>
</linearGradient>
<linearGradient id="paint1_linear_44_12049" x1="248.5" y1="78.5" x2="859" y2="85" gradientUnits="userSpaceOnUse">
<stop stop-color="#0CCDEA"/>
<stop offset="1" stop-color="#28C7D2"/>
</linearGradient>
<linearGradient id="paint2_linear_44_12049" x1="511.5" y1="84" x2="593.5" y2="9.5" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint3_linear_44_12049" x1="485.5" y1="69" x2="567.5" y2="-5.5" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint4_linear_44_12049" x1="289.236" y1="195.344" x2="394.755" y2="229.108" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<clipPath id="clip0_44_12049">
<rect width="756" height="138" rx="8" fill="white"/>
</clipPath>
</defs>
</svg>
