<template>
	<t-drawer
		v-model:visible="visible"
		:header="true"
		size="472px"
		placement="right"
		:cancel-btn="null"
		confirm-btn="关闭"
		@confirm="handleClose"
	>
		<template #header>
			<div style="display: flex; align-items: center; width: 100%; justify-content: space-between">
				<div>保证金详情</div>
				<img style="width: 16px; cursor: pointer; height: 16px" src="@/assets/<EMAIL>" @click="handleClose" />
			</div>
		</template>
		<div class="detail-content">
			<t-loading :loading="isLoading" show-overlay :delay="300">
				<div v-if="detailData?.bond_status === 3" class="info-section">
					<div class="section-title">退款信息</div>
					<div class="info-grid">
						<div class="info-item">
							<span class="label">退款方式：</span>
							<span class="value">{{ detailData.refund?.refund_method || '--' }}</span>
						</div>
						<div class="info-item">
							<span class="label">开户行：</span>
							<span class="value">{{ detailData.refund?.opening_bank || '--' }}</span>
						</div>
						<div class="info-item">
							<span class="label">银行账户：</span>
							<span class="value">{{ detailData.refund?.bank_account || '--' }}</span>
						</div>
						<div class="info-item">
							<span class="label">退款时间：</span>
							<span class="value">{{ detailData.refund?.refund_time || '--' }}</span>
						</div>
						<div class="info-item">
							<span class="label">退款金额：</span>
							<span class="value">
								{{ detailData.refund?.currency === 'CNY' ? '¥' : 'MOP' }}
								{{ detailData.refund?.refund_amount || '--' }}
							</span>
						</div>
						<div class="info-item full-width">
							<span class="label">退款凭证：</span>
							<div class="proof-images">
								<t-image-viewer
									v-for="(image, index) in detailData.refund?.refund_voucher || []"
									:key="index"
									class="proof-image"
									:default-index="index"
									:images="detailData.refund?.refund_voucher || []"
								>
									<template #trigger="{ open }">
										<div class="tdesign-demo-image-viewer__ui-image tdesign-demo-image-viewer__base">
											<img
												:src="image"
												:alt="`退款凭证${index + 1}`"
												class="tdesign-demo-image-viewer__ui-image--img"
											/>
											<div class="tdesign-demo-image-viewer__ui-image--hover" @click="open">
												<span><browse-icon size="1.4em" /> 预览</span>
											</div>
										</div>
									</template>
								</t-image-viewer>
								<span v-if="!detailData.refund?.refund_voucher?.length" class="value">--</span>
							</div>
						</div>
					</div>
				</div>

				<!-- 应用信息 -->
				<div class="info-section">
					<div class="section-title">应用信息</div>
					<div class="info-grid">
						<div class="info-item">
							<span class="label">应用名称：</span>
							<span class="value">{{ detailData?.name || '--' }}</span>
						</div>
						<div class="info-item">
							<span class="label">应用ID: </span>
							<span class="value">{{ detailData?.app_id || '--' }}</span>
						</div>
						<div class="info-item">
							<span class="label">组织名称：</span>
							<span class="value">{{ detailData?.team_name || '--' }}</span>
						</div>
						<div class="info-item">
							<span class="label">保证金金额：</span>
							<span class="value">{{
								detailData?.bond_amount
									? `${detailData?.currency === 'CNY' ? '¥' : 'MOP'} ${detailData.bond_amount.toFixed(2)}`
									: '--'
							}}</span>
						</div>
					</div>
				</div>

				<!-- 支付信息 -->
				<div class="info-section">
					<div class="section-title">支付信息</div>
					<div class="info-grid">
						<div class="info-item">
							<span class="label">保证金编号：</span>
							<span class="value">{{ detailData?.order?.sn || '--' }}</span>
						</div>
						<div class="info-item">
							<span class="label">支付状态：</span>
							<span class="value">
								<div
									class="status-tag"
									:style="{
										background: getStatusStyle(detailData?.order?.status).background,
										color: getStatusStyle(detailData?.order?.status).color,
									}"
								>
									{{ getStatusText(detailData?.order?.status) }}
								</div>
							</span>
						</div>
						<div class="info-item">
							<span class="label">付款时间：</span>
							<span class="value">{{ detailData?.order?.payed_at || '--' }}</span>
						</div>
						<div class="info-item">
							<span class="label">付款人：</span>
							<span class="value">{{ detailData?.order?.pay_openid_name || '--' }}</span>
						</div>
						<div class="info-item">
							<span class="label">付款方式：</span>
							<span class="value"
								>{{ detailData?.order?.pay_method }}
								{{ detailData?.order?.pay_channel ? `（${detailData?.order?.pay_channel}）` : '--' }}</span
							>
						</div>
						<div class="info-item">
							<span class="label">收款方：</span>
							<span class="value">{{ detailData?.order?.receive_team_name || '--' }}</span>
						</div>
					</div>
				</div>

				<!-- 操作记录 - 仅已退款状态显示 -->
				<div v-if="detailData?.bond_status === 3" class="info-section">
					<div class="section-title">操作记录</div>
					<div>
						<div v-for="(record, index) in detailData.logs || []" :key="index" class="operation-item">
							<div class="info-item">
								<span class="label">操作内容：</span>
								<span class="value">{{ record.reason || '退款' }}</span>
							</div>
							<div class="info-item">
								<span class="label">操作人：</span>
								<span class="value">{{ record.openid_name || '--' }}</span>
							</div>
							<div class="info-item">
								<span class="label">操作时间：</span>
								<span class="value">{{ record.created_at || '--' }}</span>
							</div>
						</div>
						<div v-if="!detailData.logs?.length" class="no-records">暂无操作记录</div>
					</div>
				</div>
			</t-loading>
		</div>
	</t-drawer>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { externalAppDetail } from '@/api/advertisement/index';
import { MarginDetailData } from '@/api/advertisement/types';

const visible = ref(false);

const isLoading = ref(false);

const detailData = ref<MarginDetailData | null>(null);

// 关闭抽屉
const handleClose = () => {
	visible.value = false;
	detailData.value = null;
};

// 获取状态样式
const getStatusStyle = (status?: number) => {
	const statusMap = {
		1: { background: '#e3f9e9', color: '#1c8710' }, // 已支付
		2: { background: '#f0f8ff', color: '#2069e3' }, // 已退款
	};
	return statusMap[status as keyof typeof statusMap] || { background: '#f1f2f5', color: '#13161b' };
};

// 获取状态文本
const getStatusText = (status?: number) => {
	const statusMap = {
		0: '待支付',
		1: '已支付',
	};
	return statusMap[status as keyof typeof statusMap] || '--';
};

const getDetail = (app_id: number) => {
	externalAppDetail(app_id)
		.then((res) => {
			if (res.data.code === 0) {
				detailData.value = res.data.data;
			}
		})
		.finally(() => {
			isLoading.value = false;
		});
};

defineExpose({
	handleClose,
	open: (app_id: number) => {
		visible.value = true;
		isLoading.value = true;
		getDetail(app_id);
	},
});
</script>

<style lang="less" scoped>
.info-section {
	margin-bottom: 32px;

	&:last-child {
		margin-bottom: 0;
	}
}

.section-title {
	font-size: 16px;
	font-weight: 600;
	color: #13161b;
	margin-bottom: 16px;
	padding-bottom: 8px;
	border-bottom: 1px solid #e3e6eb;
}

.info-grid {
	display: flex;
	flex-direction: column;
	gap: 12px;
}

.info-item {
	display: flex;
	align-items: flex-start;
	gap: 8px;
	padding: 8px 0;
	border-bottom: 1px solid #f1f2f5;

	&:last-child {
		border-bottom: none;
	}

	&.full-width {
		flex-direction: column;
		align-items: flex-start;
		gap: 12px;
	}

	.label {
		font-size: 14px;
		color: #717376;
		flex-shrink: 0;
		min-width: 100px;
		font-weight: 500;
	}

	.value {
		font-size: 14px;
		color: #13161b;
		flex: 1;
		word-break: break-all;
		line-height: 1.5;
	}
}

.status-tag {
	display: inline-block;
	padding: 4px 8px;
	border-radius: 4px;
	font-size: 12px;
	line-height: 1;
}

.proof-images {
	display: flex;
	flex-wrap: wrap;
	gap: 8px;
}

.proof-image {
	width: 80px;
	height: 80px;
	border: 1px solid #e3e6eb;
	border-radius: 4px;
	overflow: hidden;
	cursor: pointer;
	transition: all 0.2s;

	&:hover {
		border-color: #0052d9;
		transform: scale(1.05);
	}

	img {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}
}

.operation-list {
	.operation-item {
		padding: 12px;
		border-radius: 6px;
		margin-bottom: 8px;

		&:last-child {
			margin-bottom: 0;
		}

		.operation-content {
			font-size: 14px;
			color: #13161b;
			margin-bottom: 8px;
			font-weight: 500;
		}

		.operation-info {
			display: flex;
			justify-content: space-between;
			align-items: center;
			font-size: 12px;
			color: #717376;

			.operator {
				font-weight: 500;
			}
		}
	}

	.no-records {
		text-align: center;
		color: #717376;
		font-size: 14px;
		padding: 24px;
	}
}

.tdesign-demo-image-viewer__ui-image {
	width: 100%;
	height: 100%;
	display: inline-flex;
	position: relative;
	justify-content: center;
	align-items: center;
	border-radius: var(--td-radius-small);
	overflow: hidden;
}

.tdesign-demo-image-viewer__ui-image--hover {
	width: 100%;
	height: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
	position: absolute;
	left: 0;
	top: 0;
	opacity: 0;
	background-color: rgba(0, 0, 0, 0.6);
	color: var(--td-text-color-anti);
	line-height: 22px;
	transition: 0.2s;
}

.tdesign-demo-image-viewer__ui-image:hover .tdesign-demo-image-viewer__ui-image--hover {
	opacity: 1;
	cursor: pointer;
}

.tdesign-demo-image-viewer__ui-image--img {
	width: auto;
	height: auto;
	max-width: 100%;
	max-height: 100%;
	cursor: pointer;
	position: absolute;
}

.tdesign-demo-image-viewer__ui-image--footer {
	padding: 0 16px;
	height: 56px;
	width: 100%;
	line-height: 56px;
	font-size: 16px;
	position: absolute;
	bottom: 0;
	color: var(--td-text-color-anti);
	background-image: linear-gradient(0deg, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0) 100%);
	display: flex;
	box-sizing: border-box;
}

.tdesign-demo-image-viewer__ui-image--title {
	flex: 1;
}

.tdesign-demo-popup__reference {
	margin-left: 16px;
}

.tdesign-demo-image-viewer__ui-image--icons .tdesign-demo-icon {
	cursor: pointer;
}

.tdesign-demo-image-viewer__base {
	width: 80px;
	height: 80px;
	border: 4px solid var(--td-bg-color-secondarycontainer);
	border-radius: var(--td-radius-medium);
}
</style>
