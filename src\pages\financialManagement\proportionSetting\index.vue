<template>
	<div class="payset">
		<h2>分账比例设置</h2>
		<div class="table">
			<t-table :row-key="'id'" :data="listData" cell-empty-content="--" height="100%" :columns="columns">
				<template #Actions="{ row }">
					<a @click="edit(row)">编辑</a>
				</template>
			</t-table>
		</div>
	</div>

	<t-dialog
		:visible="openvisible"
		:header="port"
		:on-confirm="onConfirmAnother"
		:on-cancel="close"
		:on-close="close"
		confirm-text="确定"
		:confirm-btn="{
			disabled: !val,
		}"
	>
		<div class="input-box" :class="val ? '' : 'proportionSetting'">
			<div class="custom-input">
				<t-input-number
					v-model="ratioTemp"
					:placeholder="editKey === 'tech_fee_rate' ? '限制只能输入0%-9.6%' : '限制只能输入0%-50%'"
					:min="0"
					:max="editKey === 'tech_fee_rate' ? 9.6 : 50"
					:allow-input-over-limit="false"
					theme="normal"
				/>
			</div>
			<span>%</span>
		</div>
	</t-dialog>
</template>

<script setup lang="ts">
import { MessagePlugin } from 'tdesign-vue-next';
import { onMounted, ref, computed } from 'vue';
import { businessBillConfig, businessBillConfigSave } from '@/api/businessOrder';

const openvisible = ref(false);
const ratio = ref(3.33);
const ratioTemp = ref(0);
const ratioInput = ref('3.33');
const ratioError = ref(false);
const editKey = ref(null);

const digital_platform_rate = ref(2);
const digitalRateInput = ref('2');
const digitalRateError = ref(false);

const val = computed(() => {
	if (ratioTemp.value === null || ratioTemp.value === undefined) {
		return false;
	}
	return true;
});
onMounted(() => {
	getData();
});
const configTemp = ref({});
const getData = () => {
	businessBillConfig().then((res) => {
		console.log(res);
		if (res.data.data) {
			const fdata = res.data.data;
			configTemp.value = fdata;
			listData.value[0] = {
				port: '平台技术服务费',
				key: 'tech_fee_rate',
				system: `${fdata.tech_fee_rate}%`,
				value: fdata.tech_fee_rate,
			};
			listData.value[1] = {
				port: '数字商协分佣比例',
				key: 'digital_platform_rate',
				system: `${fdata.digital_platform_rate}%`,
				value: fdata.digital_platform_rate,
			};
			listData.value[2] = {
				port: '数字政企分佣比例',
				key: 'digital_platform_government_rate',
				system: `${fdata.digital_platform_government_rate}%`,
				value: fdata.digital_platform_government_rate,
			};
			listData.value[3] = {
				port: '数字CBD分佣比例',
				key: 'digital_platform_cbd_rate',
				system: `${fdata.digital_platform_cbd_rate}%`,
				value: fdata.digital_platform_cbd_rate,
			};
			listData.value[4] = {
				port: '数字社群分佣比例',
				key: 'digital_platform_association_rate',
				system: `${fdata.digital_platform_association_rate}%`,
				value: fdata.digital_platform_association_rate,
			};
      listData.value[5] = {
				port: '数字高校分佣比例',
				key: 'digital_platform_uni_rate',
				system: `${fdata.digital_platform_uni_rate}%`,
				value: fdata.digital_platform_uni_rate,
			};
		}
	});
};

const applePayStatusPutReq = () => {
	const params = {
		...configTemp.value,
	};
	params[editKey.value] = ratioTemp.value;
	businessBillConfigSave(params).then((res) => {
		console.log(res);
		if (res.data) {
			getData();
			MessagePlugin.success('设置成功');
			openvisible.value = false;
		}
	});
};
const onConfirmAnother = () => {
	applePayStatusPutReq();
};
const close = () => {
	// ratio.value = ratioTemp.value;
	// ratioInput.value = ratioTemp.value.toString();
	openvisible.value = false;
	// 重置错误状态
	ratioError.value = false;
	// digitalRateError.value = false;
};
const port = ref(null);
const edit = (e) => {
	console.log(e);
	port.value = e.port;
	editKey.value = e.key;
	ratioTemp.value = e.value;
	ratioError.value = false;
	openvisible.value = true;
};

// const validateRatioInput = () => {
// 	// 只允许数字和一个小数点
// 	const regex = /^(\d*\.?\d{0,2})?$/;

// 	// 如果尝试输入负号，直接设置为0
// 	if (ratioInput.value.includes('-')) {
// 		ratioInput.value = '0';
// 		ratio.value = 0;
// 		return;
// 	}

// 	if (!regex.test(ratioInput.value)) {
// 		// 如果输入不符合格式，回退到上一个有效值
// 		ratioInput.value = ratio.value !== null && ratio.value !== undefined ? ratio.value.toString() : '0';
// 		return;
// 	}

// 	// 检查是否为空
// 	if (ratioInput.value === '') {
// 		ratio.value = null;
// 		return;
// 	}

// 	// 转换为数字并检查范围
// 	const numValue = parseFloat(ratioInput.value);
// 	if (!isNaN(numValue)) {
// 		if (numValue > 14.4) {
// 			ratioInput.value = '14.4';
// 			ratio.value = 14.4;
// 			ratioError.value = true;
// 		} else if (numValue < 0) {
// 			ratioInput.value = '0';
// 			ratio.value = 0;
// 			ratioError.value = true;
// 		} else {
// 			ratio.value = numValue;
// 			ratioError.value = false;
// 		}
// 	}
// };

// const validateDigitalRateInput = () => {
// 	// 只允许数字和一个小数点
// 	const regex = /^(\d*\.?\d{0,2})?$/;

// 	// 如果尝试输入负号，直接设置为0
// 	if (digitalRateInput.value.includes('-')) {
// 		digitalRateInput.value = '0';
// 		digital_platform_rate.value = 0;
// 		digitalRateError.value = true;
// 		return;
// 	}

// 	if (!regex.test(digitalRateInput.value)) {
// 		// 如果输入不符合格式，回退到上一个有效值
// 		digitalRateInput.value =
// 			digital_platform_rate.value !== null && digital_platform_rate.value !== undefined
// 				? digital_platform_rate.value.toString()
// 				: '0';
// 		return;
// 	}

// 	// 检查是否为空
// 	if (digitalRateInput.value === '') {
// 		digital_platform_rate.value = null;
// 		return;
// 	}

// 	// 转换为数字并检查范围
// 	const numValue = parseFloat(digitalRateInput.value);
// 	if (!isNaN(numValue)) {
// 		if (numValue > 5) {
// 			digitalRateInput.value = '5';
// 			digital_platform_rate.value = 5;
// 		} else if (numValue < 0) {
// 			digitalRateInput.value = '0';
// 			digital_platform_rate.value = 0;
// 		} else {
// 			digital_platform_rate.value = numValue;
// 		}
// 	}
// };

const columns = [
	{
		colKey: 'port',
		title: '分账渠道',
		width: '15%',
	},
	{
		colKey: 'system',
		title: '分账比例',
		width: '15%',
	},
	{
		colKey: 'Actions',
		title: '操作',
		width: '20%',
	},
];

const listData = ref([]);
</script>

<style lang="less" scoped>
.payset {
	background-color: #fff;
	height: 85vh;
	width: 100%;
	padding: 16px;
}

.table {
	margin-top: 16px;
}

.input-box {
	display: flex;
	gap: 16px;
	align-items: center;
}

.custom-input {
	position: relative;

	input {
		width: 240px;
		height: 32px;
		border: 1px solid #dcdcdc;
		border-radius: 3px;
		padding: 0 12px;
		font-size: 14px;
	}

	input:focus {
		border-color: #0052d9;
		outline: none;
	}
}
</style>

<style lang="less">
.proportionSetting .t-input {
	border-color: red !important;
}

.proportionSetting input {
	border-color: red !important;
}
</style>
