<template>
  <div :class="`windows-status-bar ${props.theme === 'dark' ? 'dark' : 'light'} ${isMac ? 'mac' : 'windows'}`">
    <div class="windows-status-bar-left">

    </div>
    <div class="windows-status-bar-center">
      <span class="windows-status-bar-center-title">
        {{ props.title || '' }}
      </span>
    </div>
    <div class="windows-status-bar-right">
      <template v-if="!isMac">
        <div class="window-action-buttons">
          <div class="window-action-button"
            @click="handleMinimize"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
              <path d="M4.375 10H16.0417" stroke="#516082" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
          <div class="window-action-button"
            @click="handleMaximize"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="21" height="20" viewBox="0 0 21 20" fill="none">
              <rect x="4.19922" y="5" width="12" height="10" rx="2" stroke="#828DA5" stroke-width="1.4" stroke-linecap="round"/>
            </svg>
          </div>
          <div class="window-action-button"
            @click="handleClose"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10" fill="none">
              <path d="M1.00391 1L9.00054 8.99936" stroke="#516082" stroke-width="1.4" stroke-linecap="round"/>
              <path d="M8.99609 1L0.999458 8.99936" stroke="#516082" stroke-width="1.4" stroke-linecap="round"/>
            </svg>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watchEffect } from 'vue';
const isMac = ref(checkIsMac());
const props = defineProps({
  theme: {
    type: String,
    default: 'light' // light, dark
  },
  visible: {
    type: Boolean,
    default: true
  },
  title: {
    type: String,
    default: ''
  },
});

watchEffect(() => {
  document.title = props.title || '';
});

// 检查是否是mac
function checkIsMac() {
  return navigator.userAgent.includes('Macintosh') || navigator.userAgent.includes('Mac OS');
}

const emit = defineEmits(['close', 'minimize', 'maximize']);

const handleClose = () => {
  emit('close');
}
const handleMinimize = () => {
  emit('minimize');
}
const handleMaximize = () => {
  emit('maximize');
}

onMounted(() => {

});

</script>
<style lang="less" scoped>
  .windows-status-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 44px;
    padding: 0 20px;
    background: #262739;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    * {
      -webkit-app-region: drag;
    }
    flex-shrink: 0;
    &.dark {
      background: #262739;
    }
    &.light {
      background: rgba(245, 248, 254, 1);
    }
    &.mac {

    }
    &.windows {

    }
  }
  .windows-status-bar-left, .windows-status-bar-right {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    min-width: 100px; // 防止太窄
  max-width: 200px; // 防止太宽
  }
  .windows-status-bar-center {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
  }
  .windows-status-bar-center-title {
    color: var(--text-kyy_color_text_1, #1A2139);
    text-align: center;

    /* kyy_fontSize_2/bold */
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px; /* 157.143% */
  }
  .window-action-buttons {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    gap: 16px;
    .window-action-button {
      -webkit-app-region: no-drag!important;
      display: flex;
      width: 20px;
      height: 20px;
      justify-content: center;
      align-items: center;
      flex-shrink: 0;
      cursor: pointer;
      &:hover {
        background: red
      }
      svg {
        -webkit-app-region: no-drag!important;
        display: block;
        // width: 20px;
        // height: 20px;
      }
    }
  }
</style>
