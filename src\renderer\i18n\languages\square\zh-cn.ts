// 在线繁体字转换器：http://www.aies.cn/
export default {
  route: {
    home: '主页',
    notification: '通知',
    myHome: '我的主页',
    publishRecord: '发布记录',
    promote: '广场号推广',
    fansManage: '粉丝',
    squareInfo: '广场号信息',
    album: '拾光相册',
    niche: '商机',
    timeLine: '时光轴',
    nodeDetail: '节点详情',
    albumDetail: '照片详情',
    recycleBin: '回收站',
  },
  tab: {
    editProfile: '编辑资料',
    privacySetting: '隐私设置',
    ringkolSetting: '另可圈设置',
  },
  errReason: {
    squareNotOpen: '广场号未开通',
    postCommentClosed: '这条动态已关闭评论',
    commentDeleted: '这条评论已删除',
    unfollowFriendIsNotAllowed: '好友不允许取消关注',
    auditReject: '审核不通过',
    auditPending: '需要等待审核完成',
    shareIsNotAllowed: '转发失败，原动态已关闭转发/分享功能',
    pendingOrderExist: '存在待支付订单',
    qrcodeInvalid: '二维码失效',
    postPublishVerifyExpired: '发布验证已失效',
    forbidden: '无权限',
    squareSilenced: '被禁言',
    postPublishVerifyInvalid: '预览内容已失效',
    annualFeeExpired: '广场号已到期，无法进行当前操作，请先续费',
    pendingReportExist: '存在待审核举报',
    pageNameDuplicated: '页面名称不可重复',
    annualDeeSquareSilencedExpired: '年费过期或者被禁言',
    circleRingkolExpired: '另可圈套餐过期',
    overCircleRingkolComboLimit: '超出套餐限制',
    squareDeleted: '广场号已注销',
    squareNotAccessible: '无权限',
    squareNameUnavailable: '名称不可用，请重新修改',
    circleRingkolBoothPoor: '可用另可圈占位数量为0',
    circleRingkolSquareBanned: '已被对方拉黑，无法添加',
    DUPLICATE_CONTENT: '文本内容相同，请勿频繁操作',
    checkImageError: '图片上传失败，请重新尝试',
  },
  square: {
    name: '广场',
    name1: '广场号',
    friendCircle: '好友圈',
    nearby: '附近',
    alwaysValid: '长期有效',
    squareId: '广场号ID',
    feeExpiredAt: '年费到期日期',
    qrcode: '二维码',
    qrcodeEntry: '二维码 (下载二维码，用于推广与分享广场号)',
    downloadQrcode: '下载二维码',
    orgProfile: '组织基本资料',
    orgName: '组织名称',
    orgType: '组织类型',
    orgCode: '商业登记编号/统一信用代码',
    business: '经营范围',
    industry: '所属行业',
    aboutOrg: '关于组织',
    org: '组织',
    businessCode: '商业登记编号/统一信用代码',
    settingPrivilege: '设置广场权限',
    hideMyPost: '不让TA看',
    hidePost: '不看TA',
    squareQRcodeTip: '扫一扫关注我的广场号',
    squareRenew: '广场号续费',
    packageRenew: '套餐续费',
    squareSearchPlaceholder: '输入广场号名称、广场号ID搜索',
    allPost: '全部动态',
    officeSquare: '官方广场号',
    authentication: '认证',
    liked: '获赞',
    officeWeb: '官网',
    getLikeCount: '累计获得 {0} 个赞',
    followedYou: '开始关注了你',
    lookMoreSquare: '查看更多相关广场号',
    lookMorePost: '查看更多相关动态',
    squareName: '广场号名称',
    squareInfoSetting: '设置',
    allFans: '全部粉丝',
    fans: '粉丝',
    noFollowedTip: '还没有人关注呢！',
    noFansFollow: '目前还没有粉丝哦',
    noFollowAny: '还没有关注任何人',
    accountDeleted: '已注销账号',
    certExpired: '组织认证已过期，需要管理员对本组织重新提交认证，以免影响广场号使用。',
    searchSquare: '搜索广场号',
    logoff: '已注销',
    usufruct: '广场号使用权',
    limitTip: '你的广场号被限制，无法进行当前操作',
    copyLink: '复制链接',
    copyLinkSuccess: '链接复制成功，快去分享给好友吧~',
    ringkolFriend: '另可好友',
    followed1: '关注了',
    followed: '已关',
    follow: '关注',
    followTo: '回关',
    followed2: '已关注',
    allFollowed: '互关',
    backFollow: '回关',
    unfollowTip: '你确定不再关注[{0}]？',
    followFail: '已注销，关注失败',
    followSuccess: '关注成功',
    individual: '个人',
    organization: '组织',
    privilege: '广场权限',
    post: '广场动态',
    navigateTip: '该用户已设置{0}列表不可见',
    inputTip: '请输入广场号名称',
    openTip1: '请选择开通广场号的组织',
    openTip2: '当前组织认证审核中，请审核通过后选择开通',
    openTip3: '当前组织未认证成功，请联系管理员进行认证',
    openTip4: '该组织已提交认证，请审核通过后选择并开通，并关闭该页面弹窗',
    closeTip: '确定关闭？',
    selectOpenTeam: '请选择购买另可年费套餐的组织',
    squareOpen: '广场号开通',
    noOpenTeam: '暂无可开通的组织',
    createTeam: '创建组织',
    viewPageNum: '主页访问数',
    unCertificate: '未认证',
    noRingkolTip1: '暂未设置另可圈内容，请前往设置',
    noRingkolTip2: '暂未有可见的另可圈内容',
    goAuthTip: '你的组织未认证，认证后即可获得广场号唯一简称',
    goAuth: '前往认证',
    video: '视频',
  },
  annualFee: {
    currentPackage: '当前套餐',
    validUntil: '有效期至',
    packageYear: '套餐年限',
    squareUsufruct: '广场号使用权',
    validDateTo: '订购后到期时间',
    upgradeValidDateTo: '升级后到期时间',
    tip1: '新订购套餐权益将在支付成功后立即生效',
    tip2: '已享有最高等级套餐，无需升级',
    tip3: '支付金额=（ 升级套餐日单价 - 旧套餐日单价）× 套餐剩余天数 × 当前折扣',
    tip4: '当前不可升级，可立即前往续费',
    tip5: '你选择的套餐权益小于当前套餐，无法购买',
    tip6: '邀请码不正确',
    tip7: '邀请码未生效',
    tip8: '已有未生效的续费套餐，请稍后重试',
    tip9: '原套餐已过期，当前不可升级，可立即前往续费',
    inviteCode: '邀请码',
    inputInviteCode: '请输入邀请码（选填）',
    openSquare: '开通广场号',
    buyPackage: '购买套餐',
    upgradePackage: '套餐升级',
    renewalNow: '立即续费',
    feature: '功能特权',
    packageBenefit: '套餐权益',
    pleaseInput: '请填写',
    aomen: '中国澳门',
    dalu: '中国大陆',
    other: '其他区域',
    areaInventCode: '地区邀请码',
    basicVersion: '基础版',
    upgrade: '升级',
  },
  post: {
    post: '动态',
    publish: '发布',
    published: '已发布',
    pending: '审核中',
    rejected: '未通过',
    publishTime: '发布时间',
    publishPost: '发布动态',
    publishStatus: '发布状态',
    success: '发布成功',
    text: '文本',
    imgText: '图文',
    video: '视频',
    article: '文章',
    deleted1: '已删除',
    posted: '已发布',
    draft: '草稿',
    block: '屏蔽',
    err2Draft: '有内容发送失败，已存入草稿箱',
    deleted: '这条动态已删除',
    invisible: '这条动态已设置不可见',
    extensionDynamics: '推广动态',
    cancelDynamics: '取消推广动态',
    cancelDynamics2: '取消理由',
    noPermission: '无权限查看',
    defaultMsg: '发布了动态',
    seeFull: '查看完整动态详情',
    previewContent: '预览内容',
    pageInvalid: '该页面已失效',
    rejectTip: '超时未审批已自动拒绝本次发布操作',
    invalid: '该申请已失效',
    pass: '已同意发布',
    reject: '已拒绝发布',
    applyContent: '申请发布内容',
    publishConfirm: '请确认是否允许，若30分钟未审批则默认拒绝本次操作',
    publishContent: '发布内容',
    previewPostContent: '预览发布内容',
    articleCoverTip: '请从正文插入的图片中选择封面',
    emptyTip: '还没发布动态',
    forwardTip: '说说转发心得',
    shortCut: '快捷发布',
    updateVisibility: '修改可见范围',
    postValid: '发布验证',
    qrcodeInvalid: '二维码已失效',
    refreshQrcode: '刷新二维码',
    seekManagerValid: '扫码后请联系管理员进行验证',
    scanSuccess: '扫描成功',
    closeAfter5s: '5秒后自动关闭',
    validTip1: '已取消此次操作',
    validTip2: '可重新扫描验证或关闭窗口',
    validTip3: '请勿刷新本页面，按手机提示操作',
    validTip4: '已发送操作申请',
    validTip5: '请等待管理员验证操作申请，验证通过后操作将立即进行此申请在30分钟后过期，请尽快联系管理员验证',
    validTip6: '管理员可直接扫码验证，非管理员扫码后需管理员验证通过。管理员可在广场管理后台关闭发布验证。',
    richTextContent: '富文本内容',
    noCommentTip: '还没有人评论呢！',
    notLikeTip: '还没有人点赞呢！',
    publishRegion: '发布于',
    writeComment: '写评论',
    author: '作者',
    topComment: '置顶评论',
    from: '来自',
    reply: '回复',
    replyComment: '回复评论',
    replyComment1: '回复了你的评论',
    commentPost: '评论了你的动态',
    comment: '评论',
    forwardShare: '转发/分享',
    commentSuccess: '回复成功',
    commentDeleted: '该评论已删除',
    postDeleted: '该帖子已删除',
    commentTip: '确定删除该评论？',
    commentTip1: '删除评论后，评论下所有的回复都会被删除',
    forward: '转发',
    toPOpenShare: '已设为公开可见，是否同步开启转发/分享？',
    toggleLike: '共同点赞',
    like: '点赞',
    lastQuery: '上次看到这里',
    invisibleBelow2Other: '以下内容对他人不可见',
    postPlaceholder: '分享你的新鲜事吧！',
    draftBox: '草稿箱',
    removeDraftTip: '确定删除该草稿？',
    removeDraftTip1: '删除后数据无法恢复',
    draftTip: '是否保存到草稿箱？',
    noDraftTip: '暂无草稿',
    partVisible: '部分可见',
    partInvisible: '不可谁看',
    previewTip: '临时链接，仅用于预览，将在短期内失效',
    confirmInvalid: '该发布确认已失效',
    agreed: '已同意',
    canceled: '已取消',
    likeEmpty: '目前还没有人点赞哦',
    commentEmpty: '目前还没有人评论哦',
    forwardEmpty: '目前还没有人转发哦',
    forwardEmpty1: '转发内容已开启转发隐藏',
    postArticle: '发布文章',
    selectPoster: '请选择封面',
    publishArticle: '发布了文章动态',
    shareForward: '转发/分享',
    topPost: '置顶动态',
    noTitle: '[无标题]',
    likeYou: '赞了你的',
    selectFromArticle: '从正文选择',
    visibleDay1: '最近1年',
    visibleDay2: '最近半年',
    visibleDay3: '最近三个月',
    visibleDay4: '最近一个月',
    visibleDay5: '最近7天',
    fansTip1: '更新粉丝组成功',
    fansTip2: '删除粉丝组成功',
    fansTip3: '新增粉丝组成功',
    tip1: '确定删除该动态？',
    tip2: '删除后数据无法恢复',
    tip3: '确定将此动态置顶，替换现有置顶动态？',
    tip4: '已取消置顶',
    tip5: '动态置顶成功!',
    tip6: '设置成功！',
    tip7: '举报正在处理中，请耐心等待！',
    tip8: '正在恢复发布失败数据...',
    tip9: '发布失败，网络异常',
    tip10: '你输入的信息包含敏感内容，请修改后重试',
    tip11: '这条动态已设置不可见，回复后对方不会收到通知',
    tip12: '移出后，TA将会看到你的动态更新。确定移出？',
    tip13: '移出后，你将会在“好友圈”中看到TA的动态更新。确定移出？',
    tip14: '发布内容审核不通过',
    tip15: '审核中',
    tip16: '待管理员审核',
    tip17: '管理员拒绝发布',
    idea: '想法',
    openSetting: '已{0}{1}',
    likeTogether: '位好友点赞',
    friendLike: '好友点赞',
    violation: '违规',
  },
  visibility: {
    public: '公开（所有人可见）',
    publicSimple: '公开',
    fansOnly: '粉丝可见（关注后可见）',
    fansOnlySimple: '粉丝可见',
    private: '私密（仅自己可见）',
    privateSimple: '私密',
    partiallyVisible: '部分可见',
    partiallyInVisible: '不给谁看',
    public1: '所有人可见',
    public2: '公开',
    fansOnly1: '粉丝可见',
  },
  appeal: {
    submitted: '申诉已提交',
    tip1: '我们会尽快核实，请耐心等待。',
    tip2: '经核实，内容合规，已正常显示',
    tip3: '经核实，内容不合规，故申诉未能通过',
    tip4: '已超过申诉时间，无法申诉',
    tip5: '禁言已结束，无法申诉',
    result: '申诉结果',
    status: '申诉状态',
    pending: '待审核',
    pass: '申诉通过',
    reject: '申诉未通过',
    pass1: '通过',
    reject1: '违规',
    auditedAt: '处置时间',
    record: '申诉记录',
    imgEvidence: '图片凭证',
    createdAt: '申诉时间',
    appealNum: '申诉编号',
    submit: '提交申诉',
    alreadySubmit: '已申诉',
    reason: '申诉描述',
    reasonPlaceholder: '请详细描述申诉理由',
  },
  report: {
    result: '举报结果',
    success: '举报成立',
    tip1: '举报定性',
    tip2: '您举报的评论违反了平台规则，我们已对该评论进行了相应处理，感谢您的监督和反馈。',
    tip3: '暂无足够证据证明您举报的动态违反了平台规则，我们将持续关注该动态情况。',
    reject: '举报不成立',
    content: '举报内容',
    reportNum: '举报编号',
    createdAt: '举报时间',
    reportType: '举报类型：',
    reportReason: '原因描述',
    reason: '原因描述',
    selectReportType: '请选择举报类型',
    reportDesc: '举报描述',
    descTip: '请详细描述举报理由',
    submitted: '举报已提交',
    thanksReport: '感谢你的举报，我们会尽快核实。',
    imageRecognition: '图像识别中',
  },
  reportType: {
    abuse: '辱骂攻击',
    ad: '垃圾广告',
    illegal: '违法违规',
    minor: '未成年相关',
    politics: '政治敏感',
    porn: '色情低俗',
    violence: '网络暴力',
    personalSecurity: '危害人身安全',
    other: '其它',
    marketing: '违规营销',
    falseInformation: '不实信息',
    irregularExpression: '不规范表达问题',
    infringement: '侵犯权益',
    inappropriateContent: '不当内容',
    copyrightInfringement: '侵犯版权',
    multipleViolations: '多次违规',
  },
  reportPostDesc: {
    porn: '动态可能含有展示或隐晦表达淫秽色情、诱惑引导性交友、渲染低级趣味的内容。',
    falseInformation: '动态可能含有虚假信息、包括但不限于对社会新闻事件或专业领域知识不实阐释、错误解读。',
    illegal: '动态可能含有管制枪械、刀具、毒品、等违禁品，或涉嫌诈骗、赌博、侵害野生动植物的相关内容。',
    infringement: '动态搬运抄袭，或侵犯肖像、隐私、名誉、商标、专利权。',
    irregularExpression: '动态可能含有使用表情、英文缩写、谐音、变体字等形式替代规范表述，影响内容理解。',
    marketing: '动态可能含有虚假营销、夸张宣传、售卖假冒商品等有关售卖及违规广告的内容。',
    minor: '动态可能含有未成年抽烟、喝酒等不文明行为，侵害未成年人身心健康以及未成年人低俗相关内容。',
    personalSecurity: '动态可能含有宣传自杀/自残场景、教唆他人自杀或其他容易造成人身伤害危险行为。',
    politics: '动态可能含有非权威媒体发布有关政治的争议，有关国防、外交政策等方面的重大分歧等相关内容。',
    other: '其他未提及的违规类型。',
  },
  reportCommentDesc: {
    abuse: '评论可能含有侮辱谩骂、讽刺调侃、引战对立。',
    ad: '评论可能含有虚假/夸大宣传、售卖假冒商品、违规卖货、兼职刷单。',
    illegal: '评论可能含有赌博、涉枪刀毒、涉嫌诈骗、其他违规。',
    minor: '评论可能含有涉低俗、诱导不良行为。',
    politics: '评论可能含有涉政不当言论、涉政不实信息。',
    porn: '评论可能含有色情信息、疑似招嫖、色情导流。',
    violence: '评论可能含有网暴我、网暴他人。',
    personalSecurity: '评论可能含有危险行为、自杀/自残。',
    other: '其他未提及的违规类型。',
  },
  albumReportCommentDesc: {
    illegal: '链接网页可能含有管制枪械、刀具、毒品、等违禁品，或涉嫌诈骗、赌博、侵害野生动植物的相关内容。',
    other: '其他未提及的违规类型。',
    inappropriateContent: '链接网页可能包含骚扰信息、广告信息及垃圾信息等影响用户体验的内容',
    falseInformation: '链接网页可能包含虚假信息、误导性信息或欺骗性内容的情况。',
    copyrightInfringement: '链接网页可能包含未经授权的版权材料，包括肖像、隐私、名誉、商标和专利权等',
    marketing: '链接网页可能含有虚假营销、夸张宣传、售卖假冒商品等有关售卖及违规广告的内容。',
    infringement: '链接网页搬运抄袭，或侵犯肖像、隐私、名誉、商标、专利权。',
    minor: '链接网页可能含有未成年抽烟、喝酒等不文明行为，侵害未成年人身心健康以及未成年人低俗相关内容。',
  },
  violation: {
    info: '违规详情',
    square: '违规广场号',
    violationType: '违规类型',
    actions: '哪些行为属于违规？',
    content: '违规内容',
    violationNum: '违规编号',
    effect: '处置影响',
    tip1: '已经屏蔽前端动态',
    tip2: '亲爱的用户，您的广场号近期涉嫌多次违规，平台已经做对应禁言处理，如反复违规将影响广场号使用；对本次违规如有疑问，请在7天内点击下方按钮进行申诉',
    cond: '违规情况',
  },
  ringkol: {
    intro: '另可圈介绍',
    boothSquare: '关联广场号',
    boothImg: '展位图片',
    boothTitle: '展位标题',
    boothIntro: '展位介绍',
    circle: '另可圈',
    circleSetting: '另可圈设置',
    circle1: '的另可圈',
    desktop: '桌面端',
    mobile: '移动端',
    desktopStyle: '桌面端展位样式',
    mobileStyle: '移动端端展位样式',
    boothCombo: '展位套餐',
    noBoothCombo: '暂无展位套餐',
    noBoothTip: '暂无展位',
    comboBuy: '套餐购买',
    comboRenew: '套餐续费',
    buyBoothNum: '当前已购买展位数',
    useBoothNum: '当前已使用展位数',
    boothNum: '展位数',
    boothContent: '展位内容',
    agreement: '另可圈展位协议',
    enableTip: '是否展示另可圈',
    boothAdventTip: '你的展位还有{0}天到期，请及时续费',
    boothExpiredTip: '你的展位已到期，请及时续费',
    freeGet: '0元免费领',
    renewNow: '立即续费',
    boothBuyTip: '展位套餐已过期，续费后重新展示',
    addGroup: '添加分组',
    boothTotal: '展位总数',
    boothUnuse: '可用数',
    groupName: '分组名称',
    groupNameTip: '请填写分组名称',
    removeGroupTip: '删除分组会将里面的展位内容同时删除，确定要删除吗？',
    boothSetting: '展位设置',
    boothSettingTip: '“待确认”、“已拒绝”的广场号不显示在另可圈中。',
    addBooth: '添加展位',
    addBoothContent: '添加展位内容',
    editBoothContent: '编辑展位内容',
    openAlliesTip: '开启后会在前端展示该分组信息，但黑名单标签的广场号不会显示',
    introTip: '仅另可圈展示（其他另可圈引用你的广场号时，则在其另可圈中展示你的广场号介绍）',
    setting: '另可圈设置',
    boothAllies: '友商关联',
    expiredAt: '到期时间',
    expiredAt1: '购买后到期时间',
    expiredAt2: '续费后到期时间',
    selectSquare: '请关联广场号',
    comboName: '套餐名称',
    amount: '总价',
    buyNum: '购买数量',
    getNow: '立即领取',
    saveAddPreview: '保存并预览',
    tip1: '套餐购买数量已达上限',
    tip2: '套餐购买年限已达上限',
    tip3: '至少选择一个套餐',
    tip4: '所续费的展位数需等于已购买的展位数，才可续费成功，请确认续费的展位数。',
    tip5: '展位已到期，只可删除已使用的展位，其他功能均不可操作',
    tip6: '你暂未购买展位，不可操作页面功能。',
    tip7: '可用展位数为0，请购买后再添加',
    tip8: '至少添加一个分组',
    tip9: '存在展位内容未添加',
    tip10: '请输入展位标题',
    tip11: '请输入展位介绍',
    tip12: '可用展位数为0，请购买展位',
    enableCircle: '开通另可圈',
    freeTip: '免费试用{0}天',
    lastStep: '上一步',
    nextStep: '下一步',
    know: '我知道了',
    step1Tip: '默认开启另可圈展示开关，关闭后将不展示另可圈。',
    step2Tip: '更多设置包括：另可圈简介、审核设置、黑名单。',
    step3Tip: '添加分组后，可将不同展位展示在不同分组内容。',
    skip: '跳过',
    auditSetting: '审核设置',
    blackList: '另可圈黑名单',
    buyTip: '你暂未购买展位，购买后可设置展位',
    buy: '购买套餐',
  },
  page: {
    assemble: '关联官网',
    moduleTitle: '模块标题',
    titleTip: '请输入模块标题',
    articleSetting: '文章设置',
    style: '样式',
    clickAdd: '点击添加',
    btn: '按钮',
    btnNum: '按钮数量',
    link: '链接',
    fontColor: '字体颜色',
    styleColor: '样式颜色',
    pageNameTip: '请输入页面名称',
    addComponent: '添加组件',
    addComponent2: '添加组件搭建官网',
    layoutManage: '布局管理',
    officeWeb: '官网',
    pageManage: '页面管理',
    clickAddImg: '点击添加图片',
    cropperImg: '裁剪图片',
    cropperSizeAdvise: '建议尺寸宽度{width}，高度{heightMin}-{heightMax}，建议1M左右，支持类型：jpg、png、gif',
    cropperSize: '裁剪尺寸',
    width: '宽',
    height: '高',
    cropperSizeAdvise2: '注意：支持裁剪固定宽度等于{width}px 最小高度{heightMin}px 最大高度{heightMax}px',
    uploadImg: '上传图片',
    imgTextSize: '建议尺寸宽度800px，高度320-1600px，建议1M左右，支持类型：jpg、png、gif',
    updateImg: '修改图片',
    content: '内容',
    enableTip: '是否启用官网',
    tplLib: '模板库',
    savePage: '保存页面',
    referPage: '引用页面',
    saveAddPreview: '保存并预览',
    removeTip: '是否删除该组件？',
    setLink: '设置链接地址',
    linkType: '链接类型',
    customLink: '自定义链接',
    pageLink: '页面链接',
    addPage: '添加页面',
    batchRemove: '批量删除',
    pageName: '页面名称',
    nav: '导航',
    navName: '导航名称',
    navNameTip: '请输入导航名称',
    createAt: '创建时间',
    query: '查询',
    reset: '重置',
    copy: '复制',
    look: '查看',
    removeTip2: '是否删除该页面？',
    textInputTip: '请输入文本内容',
    imageTextTip: '请输入文本内容...',
    emptyTpl: '暂无模板选择',
    tplBuy: '模板购买',
    tplName: '模板名称',
    tplCategory: '模板分类',
    needAmount: '应付总额',
    buyAsAgree: '购买即视为同意',
    tplBuyAgreement: '《模板购买协议》',
    confirmBuy: '确认并支付',
    searchPlace: '搜索地点',
    navItem: '导航项',
    addNavItem: '添加导航项',
    clickAddText: '点击添加文本内容',
    module: '模块',
    textContent: '文本内容',
    titleInputTip: '请输入标题',
    updateImgTip: '请上传图片',
    imgNum: '图片数量',
    uploadTip: '建议尺寸宽度800px，高度320-1600px，建议1M左右，支持类型：jpg、png、gif',
    uploadTip1:
      '建议尺寸宽度800px，高度320-1600px，建议1M左右，支持类型：jpg、png、gif。展示的所有图片尺寸需一致（即第一张上传的图片的尺寸）如需修改展示图片尺寸，需将当前图片都删除后才能进行重制。',
    uploadTip2: '图片支持jpg、png格式，大小不超过4M',
    uploadTip3: '请至少上传{0}张图片',
    uploadTip4: '至少上传{0}张或以上的图片',
    navContent: '导航内容',
    clickAddArticle: '点击添加文章',
    selectArticle: '选择文章',
    selectArticleTip: '请选择文章',
    articleInfo: '文章详情',
    articleTitle: '文章标题',
    searchArticleTitle: '搜索文章标题',
    selectTip: '请选择页面',
    tip: '请添加组件',
    tip1: '请输入页面名称',
    tip2: '请输入导航标题',
    tip3: '是否将当前内容另存为页面内容？',
    tip4: '内容必填项没有填写，保存不成功',
    tip5: '是否将模板导入到现有的官网首页中？（若导入则会将原本首页的信息直接替换；若不导入则将购买的模板数据保存在“页面管理”中）',
    tip6: '保存的数据存在页面名称相同，是否继续保存？（若继续保存则会将原本的数据进行覆盖；若不想覆盖，则需将互斥的页面名称修改即可。）',
    tip7: '相同页面名称',
    tip8: '请输入站外链接',
    tip9: '请选择站内链接',
    tip10: '导航项已添加上限',
    tip11: '请设置链接',
    tip12: '请填写内容',
    tip13: '至少上传1张或以上的图片',
    tip14: '请输入按钮文本',
    tip15: '请添加导航项',
    tip16: '至少选择一篇文章',
    externalLink: '站外链接',
    innerLink: '站内链接',
    colorRequiredTip: '请选择字体颜色',
    colorTip: '请输入正确的颜色值',
    imgDisplay: '图片展示',
    imgText: '图文',
    imgTextDisplay: '图文展示',
    text: '文本',
    articleManage: '文章管理',
    articleDisplay: '文章展示',
    imgMultiList: '列表多图',
    swiper: '轮播图',
    richText: '富文本',
    commonComp: '常用组件',
    baseComp: '基础组件',
    preview: '预览演示',
    promotionTips1: '为增加广场号曝光，你的广场号已展示在 “大市场” 。请设置用户访问后需要曝光的业务',
    promotionTips2: '请为“大市场”中的广场号增加[宣传图和商品/服务]展示，增加广场号的曝光的内容',
    introduction: '请输入商品/服务介绍',
  },
  article: {
    article: '文章',
    coverTip: '支持10MB以内的jpg、jpeg、',
    coverTip1: 'png、gif、bmp、webp图片',
    saveAsDraft: '保存为草稿',
  },
  privacy: {
    square: '广场号权限',
    postVisibleDays: '动态可见时间范围',
    visibleDaysTip: '在选择的时间范围之前发布的动态，将对其他人不可见',
    findMyWay: '找到我的方式',
    appearInOthersFf: '在他人关注和粉丝列表公开出现',
    appearTitle: '可以被陌生人搜索到',
    appearDesc: '关闭后，陌生人在广场内无法搜到你的广场号，你关注的人和你的粉丝除外，你收到的互动可能变少。',
    profile: '个人基本资料',
    followAndFans: '关注和粉丝列表',
    individuation: '个性化推荐',
    nearby: '附近动态',
    closeNearby: '关闭后，将无法看到“附近”页面，可能影响你的浏览体验',
    allowShowInNearby: '不允许我的动态在附近中显示',
    allowShowInNearbyTip: '开启后，你的动态在附近中不可见',
    public: '公开可见',
    private: '仅自己可见',
    private1: '私密',
    tip: '关闭弹窗后可在“广场-设置-隐私设置”再次调整',
    title: '隐私设置',
  },
  admin: {
    title: '管理后台',
    title2: '广场管理后台',
    administrator: '广场管理员',
    admin1: '超级管理员',
    admin2: '普通管理员',
    transfer: '转移超级管理员',
    tip: '开通广场应用的人为超级管理员，添加的广场管理员具有进入广场管理后台的权限。',
    tip1: '1、请选择新的广场超级管理员',
    tip2: '2、原广场超级管理员：',
    tip3: '请选择要转换的管理员',
    tip4: '确定关闭【发布动态】的安全保护吗？',
    tip5: '关闭后，广场号发布动态时不再进行二次验证',
    rmOpt1: '将原广场超级管理员调整为广场管理员',
    rmOpt2: '从广场管理后台中移除',
    add: '添加管理员',
    delConfirm: '确定删除操作？',
    safeSetting: '安全设置',
    safeVerify: '安全验证',
    publishSafe: '发布动态时，开启安全保护',
    publishSafeTip: '关闭后，广场号发布动态不在进行二次验证',
    transferSuccess: '转换成功',
    promoteAd: '投放另可广告',
  },
  qrcode: {
    width: '二维码边长(cm)',
    qrcodeSizeAdvice: '二维码尺寸请按照43像素的整数倍缩放，以保持最佳效果',
    qrcodeDownload: '二维码下载',
    adviceDistance: '建议扫描距离(米)',
  },
  action: {
    addSuccess: '添加成功',
    cancel: '取消',
    closeContinuse: '继续关闭',
    save: '保存',
    import: '导入',
    notImport: '不导入',
    download: '下载',
    remove: '删除',
    close: '关闭',
    enable: '启用',
    submit: '提交',
    confirm: '确定',
    renew: '续费',
    agree: '同意',
    buyNow: '立即购买',
    share: '分享',
    reject: '拒绝',
    fold: '收起',
    unfold: '展开',
    top: '置顶',
    like: '赞',
    confirmBuy: '确认并支付',
    agreeBuy: '同意并支付',
    preview: '预览',
    edit: '编辑',
    see: '查看',
    buy: '购买',
    use: '使用',
    cancelTop: '取消置顶',
    clickLoadMore: '点击加载更多',
    uploadImg: '上传图片',
    saveImg: '保存图片',
    report: '举报',
    sort: '排序',
    unFollow: '取消关注',
  },
  map: {
    name: '地图',
    address: '地址',
    addressName: '地址名称',
    noMore: '暂无更多地点信息',
    noMatchResult: '未匹配到结果',
    addressInputTip: '请输入地址名称',
    fixedAddress: '定位地址',
    fixedInputTip: '请输入定位地址',
    detailAddress: '详细地址',
    detailInputTip: '请输入详细地址',
    tel: '联系电话',
    telInputTip: '请输入联系电话',
  },
  editor: {
    tip: '请输入正文',
    imgSizeLimit: '请选择小于10M的图片',
    videoSizeLimit: '请选择小于300M的视频',
  },
  time: {
    just: '刚刚',
    today: '今天',
    yesterday: '昨天',
    beforeYesterday: '前天',
    month: '月',
    minuteBefore: '分钟前',
  },
  album: {
    title: '拾光相册',
    post: '相册动态',
  },
  partyBuild: '党建',
  rencentStatus: {
    unvisible: '好友动态不可见',
    sevenDay: '最近7天',
    oneMonth: '最近一个月',
    threeMonth: '最近三个月',
    halfYear: '最近半年',
    oneYear: '最近一年',
  },
  search: '搜索',
  searchPlaceholder: '搜索关键词',
  operator: '操作者名称',
  createdAt: '操作时间',
  setting: '设置',
  clickSee: '点击查看',
  name: '名称',
  name1: '姓名',
  enterPlease: '请输入内容',
  enterPlease2: '请输入',
  intro: '简介',
  expiredAt: '有效期',
  goSetting: '前往设置',
  csPhone: '客服电话',
  selected: '已选',
  imgSupport: '支持格式：JPG、GIF、PNG、BMP、WEBP',
  maxImgCount: '最多{0}张',
  piece: '篇',
  piece1: '条',
  piece2: '个',
  piece3: '张',
  piece4: '人',
  expiredTip: '到期，请及时',
  currentExpiredAt: '当前到期时间',
  orderedCombo: '订购套餐',
  comboContent: '套餐内容',
  expiredDate: '订购后到期时间',
  year: '年',
  renewYear: '续费年限',
  selectYear: '选择年限',
  orderedExpiredAt: '订购后到期时间',
  buyAgreement: '购买协议',
  agreePublish: '同意发布',
  operateAdvice: '操作建议',
  readyAppeal: '已申诉',
  syncOpen: '同步开启',
  of: '的',
  wait: '等',
  image: '图片',
  video: '视频',
  noImg: '暂无图片',
  return: '返回',
  amount: '总价',
  price: '单价',
  about: '约',
  comboAmount: '套餐总价',
  buyYearLimit: '购买年限',
  amount2: '应付总额',
  buyAsAgree: '购买即视为同意',
  day: '天',
  waitConfirm: '待确认',
  rejected: '已拒绝',
  topToggle: '{0}',
  tip: '提示',
  face: '表情',
  friend: '朋友',
  selectFriend: '选择朋友',
  selectPerson: '选择人员',
  yearOfAge: '岁',
  more: '更多',
  goLook: '去看看',
  know: '知道了',
  continueEdit: '继续编辑',
  nextStep: '下一步',
  reUpload: '重新上传',
  hide: '隐藏',
  fresh: '最新',
  ago: '以往',
  editImg: '编辑图片',
  selectImg: '选择图片',
  status: '状态',
  pleaseSelect: '请选择',
  emptyTip: '暂无相关结果',
  gender: '性别',
  notShow: '不展示',
  male: '男',
  female: '女',
  birthday: '生日',
  yearMonthDay: '年/月/日',
  region: '地区',
  select: '选择',
  type: '类型',
  operate: '操作',
  startDate: '开始日期',
  endDate: '结束日期',
  all: '全部',
  synthesize: '综合',
  not: '无',
  tel: '电话',
  loading: '正在加载...',
  loading1: '加载中...',
  searching: '搜索中...',
  noContent: '暂无内容',
  noData: '暂无数据',
  successTip: '操作成功',
  buySuccessTip: '购买成功',
  paySuccessTip: '购买成功',
  paySuccessTip2: '支付成功',
  paySuccessTip3: '待审核通过后才可使用',
  finish: '完成',
  settingRingkol: '设置另可圈',
  delSuccessTip: '删除成功',
  saveSuccessTip: '保存成功',
  updateSuccessTip: '修改成功',
  uploadFail: '上传失败',
  yet: '已',
  afterHours: '{0}小时后',
  afterDays: '{0}天后',
  afterMinutes: '{0}分钟后',
  readAndAgree: '请阅读并同意',
  requiredTip: '该项必填',
  fullContent: '全文',
  uploadLimit: '请选择{0}{1}以内的视频',
  uploadImgLimit: '请选择小于{0}MB的图片',
  orderTip: '你还有未完成的订单，请先完成后继续购买',
  seeOrder: '查看订单',
  upload: {
    imgAccept: '请选择{0}格式的图片',
    videoAccept: '请选择{0}格式{1}以内的视频',
    clickUpload: '点击上传',
    refuseUploadFormat: '不支持的视频编码格式',
    limitSize: '文件数量超出限制，仅上传未超出数量的文件',
    limitWidthAndHeight: '请上传大于{0}的图片',
  },
  switchAndRefresh: '确定切换并刷新',
  switchAndRefreshContent: '请确定你的应用数据已经被妥善保存。刷新后，已经打开的页面不再保留?',
};
