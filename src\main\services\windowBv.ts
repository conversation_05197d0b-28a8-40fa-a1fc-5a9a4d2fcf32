import { showDevTool } from '@main/env';
import { BrowserWindow } from 'electron';
import { debounce, throttle } from 'lodash';
import { windowManager } from './windowManager';
import { getRendererWindowEntry } from '../config/StaticPath';
import { apmReport } from '../../renderer/utils/apm';
import { getRandomUUID } from '@lynker-desktop/electron-sdk/main';

let handleResizeRef = null;

export const setPopBv = async (arg: {
  show: boolean;
  extype?: string;
  type?: string;
  data?: any;
  isCollapse?: boolean;
  leftMenuNum?: number
}) => {
  try {
    if (
      !windowManager.mainWindow
      || windowManager.mainWindow.isDestroyed()
    ) {
      return;
    }
    console.log('[poper]', windowManager.popView, windowManager.popView?.webContents?.isDestroyed() ? 'destroyed' : 'not destroyed');
    apmReport({
      name: 'windowBv popView.isDestroyed',
      message: windowManager.popView?.webContents?.isDestroyed() ? 'destroyed' : 'not destroyed',
    });
    // 添加 resize 事件处理
    const handleResize = debounce((type) => {
      windowManager.updatePopViewBounds(type, {
        isCollapse: arg.isCollapse,
        leftMenuNum: arg.leftMenuNum,
      });
    }, 200);
    windowManager.popView?.setAutoResize({ width: true, height: true });
    handleResizeRef && windowManager.mainWindow.removeListener('resize', handleResizeRef);
    handleResizeRef = () => handleResize(arg.type);
    // 隐藏
    if (!arg.show) {
      if (windowManager.popView) {
        const win = BrowserWindow.fromBrowserView(windowManager.popView);
        win && win.removeBrowserView(windowManager.popView);
        windowManager.popView.webContents.send('showtype', {
          type: '',
          data: arg.data,
        });
        windowManager.mainWindow.webContents.send('showAddHover', {
          type: '',
          data: arg.data,
        });
      }
      return;
    }

    const size = windowManager.mainWindow.getSize();
    const plat = process.platform;
    const boundsObj = {
      personal: { x: 18, y: 44, width: 226, height: 470 },
      add: {
        x: plat === 'darwin' ? size[0] - 16 - 194 : size[0] - 16 - 124 - 184,
        y: 44,
        width: 154,
        // height: 302,
        height: 300,
      },
      dialog: { x: 0, y: 48, width: size[0], height: size[1] - 48 },
      dialogCreateGroup: { x: 0, y: 48, width: size[0], height: size[1] - 48 },
      dialogScene: { x: 0, y: 48, width: size[0], height: size[1] - 48 },
      dialogContacts: { x: 0, y: 48, width: size[0], height: size[1] - 48 },
      dialogSearch: { x: 0, y: 48, width: size[0], height: size[1] - 48 },
      dialogOrg: { x: 0, y: 48, width: size[0], height: size[1] - 48 },
      // dialogActCode: { x: 0, y: 48, width: size[0], height: size[1] - 48 },

      accountAuthTip: { x: 0, y: 0, width: size[0], height: size[1] },
      packUpdate: { x: 0, y: 0, width: size[0], height: size[1] },
      appAuthUpdateTip: { x: 0, y: 0, width: size[0], height: size[1] },
      dialogModifyPw: { x: 0, y: 0, width: size[0], height: size[1] },
      dialogLogout: { x: 0, y: 0, width: size[0], height: size[1] },
      dialogKickOut: { x: 0, y: 0, width: size[0], height: size[1] },
      dialogViolationOut: { x: 0, y: 0, width: size[0], height: size[1] },
      dialogRemind: { x: 0, y: 0, width: size[0], height: size[1] },
      dialogActivityRemind: { x: 0, y: 0, width: size[0], height: size[1] },
      dialogShare: { x: 0, y: 0, width: size[0], height: size[1] },
      dialogVcard: { x: 0, y: 0, width: size[0], height: size[1] },
      dialogReport: { x: 0, y: 0, width: size[0], height: size[1] },
      dialogIdentity: { x: 0, y: 0, width: size[0], height: size[1] },
      moreMenu: {
        x: arg.isCollapse ? 152 : 88,
        y: 48 + (arg.isCollapse ? 50 * arg.leftMenuNum : 70 * arg.leftMenuNum),
        width: 164,
        height: 91,
      },
      moreMenuIcon: {
        x: (arg.isCollapse ? 135 : 70) - 16,
        y: size[1] * 0.5 - 31,
        width: 32,
        height: 62,
      },
    };

    const key = arg.extype || arg.type;
    const options = {
      url: getRendererWindowEntry('windows/popBv/index.html'),
      bounds: boundsObj[key],
      showDevtool: showDevTool,
      addBv: true,
    };
    if (windowManager.popView) {
      try {
        await windowManager.createPopView();
        await windowManager.setBv(windowManager.popView, options, undefined, `pop-bv-${getRandomUUID()}`);
        const type = arg.show ? arg.type : '';
        console.log('[poper type]', type);
        windowManager.popView.webContents.send('showtype', { type, data: arg.data });
        windowManager.popView.webContents.focus();
        windowManager.mainWindow.webContents.send('showAddHover', { type, data: arg.data });

        // resize 的时候更新 popview 的位置以及禁止自动 resize
        // 'personal',
        if (['add'].includes(type)) {
          windowManager.popView.setAutoResize({ width: false, height: false });
          windowManager.mainWindow.on('resize', handleResizeRef);
        }
      } catch (error) {
        console.log('[popView]', windowManager.popView, arg.type, error);
      }
    }
  } catch (error) {
    console.error('setPopBv error: ', error);
  }
};

export const setPopBvOther = async (arg: {
  show: boolean;
  type?: string;
  data?: any;
}) => {
  try {
    if (
      !windowManager.mainWindow
      || windowManager.mainWindow.isDestroyed()
    ) {
      return;
    }

    // 隐藏
    if (!arg.show) {
      if (windowManager.popView) {
        const win = BrowserWindow.fromBrowserView(windowManager.popView);
        win && win.removeBrowserView(windowManager.popView);
        windowManager.popView.webContents.send('showOtherType', {
          type: '',
          data: arg.data,
        });
      }
      return;
    }

    const size = windowManager.mainWindow.getSize();
    const plat = process.platform;
    const boundsObj = {
      changeAccount: { x: 0, y: 0, width: size[0], height: size[1] },
    };

    const key = arg.type;
    const options = {
      url: getRendererWindowEntry('windows/popBv/index.html'),
      bounds: boundsObj[key],
      showDevtool: showDevTool,
      addBv: true,
    };
    await windowManager.createPopView();
    await windowManager.setBv(windowManager.popView, options, undefined, `pop-bv-${getRandomUUID()}`);
    const type = arg.show ? arg.type : '';
    windowManager.popView.webContents.send('showOtherType', { type, data: arg.data });
    windowManager.popView.webContents.focus();
  } catch (error) {
    console.error('setPopBvOther error: ', error);
  }
};
