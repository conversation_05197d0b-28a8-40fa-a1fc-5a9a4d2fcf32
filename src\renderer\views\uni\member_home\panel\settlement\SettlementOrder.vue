<template>
  <div class="refund-container">
    <!-- 引入自定义的iframe组件 -->
    <iframe-component
      v-if="iframeUrl"
      id="settlement-platform-iframe"
      ref="iframeRef"
      :url="iframeUrl"
      :is-load-in-component="false"
      @load="handleLoad"
      @error="handleError"
      @message="handleMessage"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import LynkerSDK from '@renderer/_jssdk';
import IframeComponent from '@renderer/_jssdk/components/iframe/index.vue';

const iframeUrl = ref();
const loading = ref(true);
const loadError = ref(false);
const iframeRef = ref<{ postMessage:(data: any) => void }>(null);

const handleLoad = () => {
  loading.value = false;
};

const handleError = () => {
  loadError.value = true;
};

const handleMessage = (data: any) => {
  console.log('=====>data', data);
  if (data?.type === 'work-bench-open-tab') {
    const { url, title } = data.data;
    LynkerSDK.workBench.openTabForRefundDetail({
      title,
      url: encodeURIComponent(url),
    });
  }
};

const loadIframe = () => {
  loading.value = true;
  loadError.value = false;
  const token = LynkerSDK.config.token;
  const teamId = LynkerSDK.workBench.getActiveTeamId();
  const userInfo = LynkerSDK.getUserInfo();
  const env = LynkerSDK.config.env;
  if (token) {
    if (window.location.hostname === 'localhost') {
      iframeUrl.value = `http://localhost:8080/common/index.html#/settlement-platform/list?type=refund&env=${env}&token=${token}&teamId=${teamId}&openId=${userInfo.openid}`;
    } else {
      iframeUrl.value = LynkerSDK.getH5Url(`/common/index.html#/settlement-platform/list?type=refund&env=${env}&token=${token}&teamId=${teamId}&openId=${userInfo.openid}`);
    }
  }
};

onMounted(() => {
  loadIframe();
});
</script>

<style scoped>
.refund-container {
  width: 100%;
  height: 100%;
  position: absolute;
  overflow: hidden;
  border-radius: 8px;
  border: 1px solid var(--divider-kyy_color_divider_light, #ECEFF5);
}
</style>
