<template>
  <div class="sel-box">
    <t-dialog
      v-model:visible="adminVisible"
      header="选择成员"
      width="672px"
      top="100px"
      @cancel="onClickClose"
      @close="onClickClose"
      @confirm="onClickConfirm"
    >
      <div class="as-box">
        <div class="left">
          <div class="search">
            <t-input v-model="search_word">
              <template #label>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  style="width: 16px"
                  fill="none"
                  viewBox="0 0 48 48"
                >
                  <path
                    stroke-linejoin="round"
                    stroke-width="4"
                    stroke="#a1a2a4"
                    d="M21 38c9.389 0 17-7.611 17-17S30.389 4 21 4 4 11.611 4 21s7.611 17 17 17Z"
                    data-follow-stroke="#333"
                  />
                  <path
                    stroke-linejoin="round"
                    stroke-linecap="round"
                    stroke-width="4"
                    stroke="#a1a2a4"
                    d="M26.657 14.343A7.975 7.975 0 0 0 21 12c-2.209 0-4.209.895-5.657 2.343M33.222 33.222l8.485 8.485"
                    data-follow-stroke="#333"
                  />
                </svg>
              </template>
            </t-input>
          </div>
          <div class="tree">
            <div
              v-for="user in getAdminsData()"
              :key="user.staffId"
              class="user-item"
              @click="onUserClick(user)"
            >
              <div class="cb">
                <t-checkbox v-model="user.checked" @change="onClick" />
              </div>
              <div class="avb avbbb">
                <!-- <img v-if="user.staffAvatar" :src="user.staffAvatar" alt="">
                <div v-else class="mock-av">
                  {{ user.staffName }}
                </div> -->
                <avatar
                  avatar-size="24px"
                  :image-url="user.staffAvatar"
                  :user-name="user.staffName"
                  shape="circle"
                ></avatar>
              </div>
              <div class="name">
                {{ user.staffName }}
              </div>
            </div>
          </div>
        </div>
        <div class="right">
          <div class="title">已选：{{ selectTemp.length }}人</div>
          <div class="se-list">
            <div v-for="item in selectTemp" :key="item.value" class="app-item">
              <div class="avb">
                <!-- <img v-if="item.staffAvatar" :src="item.staffAvatar" alt="">
                <div v-else class="mock-av">
                  {{ item.staffName }}
                </div> -->
                <avatar
                  avatar-size="24px"
                  :image-url="item.staffAvatar"
                  :user-name="item.staffName"
                  shape="circle"
                ></avatar>
              </div>
              <div class="name-item">
                {{ item.staffName }}
              </div>
              <div class="close-item" @click="deleteEle(item)">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="#333"
                  viewBox="0 0 48 48"
                >
                  <path
                    stroke-linejoin="round"
                    stroke-linecap="round"
                    stroke-width="4"
                    stroke="#333"
                    d="m8 8 32 32M8 40 40 8"
                    data-follow-stroke="#333"
                  />
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import _ from "lodash";
import { getPlatformUser } from "@renderer/api/approval/api/approvalAdmin";
import { Staff } from "@renderer/api/approval/model/approvalAdmin";
import { AxiosResponse } from "axios";
import { MessagePlugin } from "tdesign-vue-next";
import avatar from "@/components/kyy-avatar/index.vue";

const props = defineProps({
  single: {
    type: Boolean,
    default: false,
  },
  teamId: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(["onSelectItem", "onClose"]);

const search_word = ref("");
const team_id = localStorage.getItem("approvalteamid");
const users: any = ref([]);
const selectTemp: any = ref([]);

const getAdminsData = () => {
  if (!search_word.value) {
    return users.value;
  }
  const res: any = [];
  for (const item of users.value) {
    if (item.name.includes(search_word.value)) {
      res.push(item);
    }
  }
  return res;
};

const dataHandle = (data: Array<any>, selectedData: Array<any>) => {
  selectTemp.value = [];
  const ids = selectedData.map((item) => item.staffId);
  console.log("ids", ids);
  if (ids.length === 0) {
    return;
  }
  data.forEach((item) => {
    if (ids.includes(item.staffId.toString())) {
      console.log("true");
      item.checked = true;
      selectTemp.value.push(item);
    } else {
      item.checked = false;
      console.log("false");
    }
  });
  console.log(data);
};

const getPlatformUserRun = (type, selectedData: Array<any>) => {
  getPlatformUser(type, props.teamId).then((res: AxiosResponse<any>) => {
    if (res.data) {
      console.log(res.data);
      users.value = res.data.data;
      console.log(' users.value', users.value);

      dataHandle(users.value, selectedData);
    }
  });
};

const onClick = () => {
  selectTemp.value = users.value.filter((item: any) => item.checked);
};

const onUserClick = (user) => {
  // 确保user对象和selectTemp.value是预期的数据类型
  // if (!user || typeof user !== 'object' || !Array.isArray(selectTemp.value)) {
  //   console.error('Invalid user object or selectTemp.value is not an array.');
  //   return;
  // }

  // 使用Map来优化查找性能
  const selectedUserMap = new Map(selectTemp.value.map((item) => [item.staffId, item]));

  // 判断用户是否已存在
  if (selectedUserMap.has(user.staffId)) {
    // 如果用户已存在，则移除该用户
    selectTemp.value = selectTemp.value.filter((item) => item.staffId !== user.staffId);
  } else {
    // 如果用户不存在，则添加用户
    // 使用扩展运算符来添加新元素
    selectTemp.value = [...selectTemp.value, user];
  }
  user.checked = !user.checked;
};


const deleteEle = (item) => {
  selectTemp.value = selectTemp.value.filter(
    (ele: any) => ele.staffId !== item.staffId
  );
  users.value.forEach((ele: any) => {
    if (ele.staffId === item.staffId) {
      ele.checked = false;
    }
  });
};

const adminVisible = ref(false);

const onClickConfirm = () => {
  if (props.single && selectTemp.value.length > 1) {
    MessagePlugin.error("只能选择一个人员！");
    return false;
  }
  emit("onSelectItem", selectTemp.value);
  adminVisible.value = false;
};
const onClickClose = () => {
  emit("onClose", true);
  adminVisible.value = false;
};

const onOpen = (type, selectedData) => {
  console.log(selectedData);
  adminVisible.value = true;
  getPlatformUserRun(type, selectedData);
};

defineExpose({
  onOpen,
});
</script>

<style lang="less" scoped>
.sel-box {
}
.as-box {
  width: 608px;
  height: 372px;
  border: 1px solid #e3e6eb;
  border-radius: 4px;
  display: flex;
  padding: 5px 0;
  .left {
    width: 303px;
    border-right: 1px solid #e1e1e1;
    padding: 12px 16px;
    .search {
    }
    .tree {
      margin-top: 12px;
      height: 300px;
      overflow-y: auto;
      .user-item {
        // width: 270px;
        height: 32px;
        border-radius: 4px;
        display: flex;
        align-items: center;
        padding: 0 8px;
        cursor: pointer;
        .cb {
          padding-top: 5px;
        }
        .avb {
          width: 24px;
          height: 24px;
          border-radius: 4px;
          margin-right: 4px;
          img {
            width: 100%;
            height: 100%;
            border-radius: 4px;
          }
          .mock-av {
            width: 100%;
            height: 100%;
            background: #488bf0;
            border-radius: 4px;
            font-size: 10px;

            font-weight: 400;
            text-align: center;
            color: #ffffff;
          }
        }
        .name {
          width: 180px;
          font-size: 14px;

          font-weight: 400;
          text-align: left;
          color: #13161b;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
      .user-item:hover {
        background: #f0f8ff;
      }
    }
    .tree::-webkit-scrollbar {
      width: 4px;
      // height: 2px;
      background-color: #f5f5f5;
    }
    /*定义滚动条轨道 内阴影+圆角*/
    .tree::-webkit-scrollbar-track {
      background-color: #e3e6eb;
    }
    /*定义滑块 内阴影+圆角*/
    .tree::-webkit-scrollbar-thumb {
      border-radius: 10px;
      box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
      -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
      background-color: #c8c8c8;
    }
  }

  .right {
    width: 303px;
    height: 100%;
    overflow-y: auto;
    padding: 0 16px;
    .title {
      font-size: 14px;

      font-weight: 400;
      text-align: left;
      color: #13161b;
      margin-bottom: 14px;
    }
    .se-list {
      width: 100%;
      .app-item {
        width: 100%;
        height: 32px;
        background: #ffffff;
        border-radius: 4px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .name-item {
          font-size: 14px;

          font-weight: 400;
          text-align: left;
          color: #13161b;
          width: 220px;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }
        .avb {
          width: 24px;
          height: 24px;
          border-radius: 4px;
          margin-right: 8px;
          img {
            width: 100%;
            height: 100%;
            border-radius: 4px;
          }
          .mock-av {
            width: 100%;
            height: 100%;
            background: #488bf0;
            border-radius: 4px;
            font-size: 10px;

            font-weight: 400;
            text-align: center;
            color: #ffffff;
          }
        }
        .close-item {
          cursor: pointer;
          svg {
            width: 14px;
            height: 14px;
          }
        }
      }
    }
  }
}
</style>
