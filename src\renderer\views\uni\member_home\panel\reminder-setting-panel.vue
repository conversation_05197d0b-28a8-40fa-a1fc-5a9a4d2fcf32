<template>
  <div class="container">
    <div class="header">
      <h1 class="header-title">{{ $t("member.remind.title") }}</h1>
      <!-- <ul class="header-buttons">
        <li>
          <t-button theme="primary" variant="base" @click="onSave">
            {{ $t("member.save") }}</t-button>
        </li>
      </ul> -->
    </div>
    <div class="setting">
      <div class="setting-form">
        <div class="setting-form-item">
          <!-- 会员到期 -->
          <span class="vip">{{ $t("member.remind.vip_tip") }}</span>
          <span class="value">
            <t-switch
              :value="formData.is_remind === 1 ? true : false"
              @change="onSetRemind"
            />
          </span>
        </div>
        <div class="setting-form-item">
          <!-- 提醒范围 -->
          <span class="title">{{ $t("member.remind.range") }}</span>
          <span class="value">
            {{ remindRangeTextCtd }}
          </span>
          <!-- <t-link
            theme="primary"
            class="ml-8"
            hover="color"
            @click="onReminderScope"
          >编辑</t-link> -->
        </div>
        <div class="setting-form-item">
          <!-- 提醒时间 -->
          <span class="title">
            {{ $t("member.remind.time") }}
          </span>
          <span class="value">
            {{ $t("member.remind.beforeText") }}
            <span class="countColor">{{ formData.day }}</span>
            {{ $t("member.remind.afterText") }}
            <t-link
              theme="primary"
              class="ml-8"
              hover="color"
              @click="onRemindTime"
            >编辑</t-link>
          </span>
        </div>
        <div class="setting-form-item">
          <!-- 提醒方式 -->
          <span class="title">{{ $t("member.remind.way") }}</span>
          <span class="value">
            {{ $t("member.remind.on_site_text") }}
          </span>
        </div>
      </div>
    </div>
  </div>
  <SettingRangeModal ref="settingRangeModalRef" @on-emits="onSettingRange" />
  <SettingTimeModal ref="settingTimeModalRef" @on-emits="onSettingTime" />
</template>

<script lang="ts" setup>
// import noData from "@renderer/components/approvalSatellite/approvalData/noData.vue";
import { useI18n } from "vue-i18n";
import { computed, reactive, ref, toRaw, watch } from "vue";
import { useRouter } from "vue-router";
import { useUniStore } from "@renderer/views/uni/store/uni";
import useRouterHelper from "@renderer/views/square/hooks/routerHelper";
import { SettingRemindInterface } from "@renderer/views/uni/member_home/panel/membership-setting-panel/interface";
import { useMemberFormDesignStore } from "@renderer/views/uni/store/formDesign";
import SettingRangeModal from "@renderer/views/uni/member_home/panel/membership-setting-panel/modal/setting-range-modal.vue";
import SettingTimeModal from "@renderer/views/uni/member_home/panel/membership-setting-panel/modal/setting-time-modal.vue";

import {
  getMemberSettingAxios,
  memberSettingRemindAxios
} from "@renderer/api/uni/api/businessApi";
import { getResponseResult } from "@renderer/utils/myUtils";
import { MessagePlugin } from "tdesign-vue-next";

const { t } = useI18n();
const router = useRouter();
const store = useUniStore();
const formDesignStore = useMemberFormDesignStore();
const { menuList, routeList, roleFilter } = useRouterHelper("uniIndex");

let formData: SettingRemindInterface = reactive({
  day: 0, // 提前提醒天数

  is_remind: 0,
  members: [1, 2], // [integer]部门提醒人员集合
  type: 1 // 提醒类型 1：全部，2：部门人员
});

const remindRangeTextCtd = computed(() => {
  let text = "";
  if (formData.type === 1) {
    // 全部
    text = "全部会员";
  } else if (formData.type === 2) {
    // formData.
    text = "等待设置";
  }
  return text;
});

watch(
  () => store.activeAccount,
  (val) => {
    if (val) {
      onGetMemberSetting();
    }
  },
  {
    deep: true
  }
);

const onSetRemind = (val) => {
  formData.is_remind = val ? 1 : 2;
  onSave();
};

const onGetMemberSetting = async () => {
  let result = null;
  try {
    result = await getMemberSettingAxios();
    result = getResponseResult(result);
    if (!result) return;
    // formData.pay_setting = result.data?.pay_setting;
    // formData.personal_apply = result.data?.personal_apply;
    // formData.personal_form = result.data?.personal_form;
    // formData.team_apply = result.data?.team_apply;
    // formData.team_form = result.data?.team_form;
    console.log(result);
    const data = result.data;
    formData.day = data.day;
    formData.is_remind = data.is_remind;
    formData.members = data.members;
    formData.type = data.type;
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    MessagePlugin.error(errMsg);
  }
};

// onActivated(() => {
//   if (store.activeAccount) {
//     onGetProjectList();
//   }
// });

// onMounted(() => {
// });
onGetMemberSetting();

const onSettingRange = (range) => {
  formData.type = range.type;
  formData.is_remind = range.is_remind;
  // onGetMemberSetting();
  onSave();
};

const onSettingTime = (time) => {
  console.log(time);
  formData.day = time;
  // onGetMemberSetting();
  onSave();
};

const settingRangeModalRef = ref(null);
const onReminderScope = () => {
  settingRangeModalRef.value.onOpen({
    type: formData.type,
    members: formData.members
  });
};

const settingTimeModalRef = ref(null);
const onRemindTime = () => {
  settingTimeModalRef.value.onOpen(formData.day);
};

const goFormDesignPage = (type) => {
  // router.push({
  //   path: "/memberIndex/member_manage",
  //   query: {
  //     // projectId: props.projectId
  //   }
  // });
  const searchMenu = routeList.find((v) => v.name === "member_manage");
  // store.addTab(routeList.filter((v) => v.affix).filter(roleFilter));
  router.push({ path: searchMenu.fullPath, query: { type } });
  store.addTab(toRaw(searchMenu));
};

// 保存表单
const onSave = async () => {
  const params = {
    ...toRaw(formData)
  };

  console.log(params);

  let result = null;
  try {
    result = await memberSettingRemindAxios(params);
    result = getResponseResult(result);
    if (!result) return;
    MessagePlugin.success(t("member.save") + t("member.success"));
    onGetMemberSetting();
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    MessagePlugin.error(errMsg);
  }
};

const onChangePay = (checkedValues) => {
  console.log(checkedValues);
  // console.log('checkedValues:', value.value, checkedValues);
};
</script>

<style lang="less" scoped>
@import "./public.less";

.setting {
  background-color: @kyy_color_bg_light;
  height: inherit;
  border-top: 1px solid #e3e6eb;

  &-form {
    display: flex;
    padding: 24px 20px;
    flex-direction: column;
    gap: 16px;
    &-item {
      display: flex;
      align-items: center;
      .title {
        font-size: 14px;

        font-weight: 400;
        color: #13161b;
      }
      .vip {
        font-size: 14px;

        font-weight: 700;
        // text-align: left;
        color: #13161b;
      }
      .value {
        display: flex;
        flex-direction: row;
        margin-left: 20px;
        // gap: 16px;

        font-size: 14px;

        font-weight: 400;
        color: #13161b;
        .countColor {
          font-size: 14px;

          font-weight: 400;
          color: #e66800;
          margin: 0 3px;
        }
        .team {
          display: flex;
          align-items: center;
          gap: 12px;
        }
      }
    }
  }
}
</style>
