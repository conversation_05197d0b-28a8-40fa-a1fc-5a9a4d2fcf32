<template>
  <t-dialog
    v-model:visible="visible"
    header="请选择组织"
    :z-index="2500"
    attach="body"
    width="432px"
    :footer="null"
  >
    <template #body>
      <div class="toBody">
        <div v-if="organizeDatas && organizeDatas.length > 0" class="data">
          <div
            v-for="(orItem, orIndex) in organizeDatas"
            :key="orIndex"
            :class="{
              'data-item': true,
              cursor: true,
              active: currentActive && currentActive.idTeam === orItem.idTeam
            }"
            @click="onSetCurrentActive(orItem)"
          >
            <img v-if="orItem?.teamLogo" class="img" :src="orItem.teamLogo" />
            <img
              v-else
              class="img"
              src="@/assets/img/<EMAIL>"
              alt=""
            />
            <span class="name line-1">{{ orItem.teamFullName }}</span>
            <svg
              v-show="currentActive && currentActive.idTeam === orItem.idTeam"
              class="iconpark-icon icon"
            >
              <use href="#check" />
            </svg>
          </div>
        </div>
        <div v-else class="noData">未加入任何组织，请先创建或加入组织</div>
      </div>
    </template>
    <template #closeBtn>
      <!-- <iconpark-icon name="close" size="16"></iconpark-icon> -->
      <!-- <svg class="iconpark-icon" style="width: 16px; height: 16px">
        <use href="#close" />
      </svg> -->
      <iconpark-icon name="iconerror" style="font-size: 24px"></iconpark-icon>
    </template>
  </t-dialog>
</template>

<script lang="ts" setup>
/**
 * @description 选择组织
 * <AUTHOR>
 */
import { debounce } from "lodash";
import { Ref, reactive, ref } from "vue";
import { MessagePlugin } from "tdesign-vue-next";

const emits = defineEmits(["onSelected"]);
const visible = ref(false);
const organizeDatas = ref([]);
const currentActive: Ref<any> = ref(null);
let originData = null;
const onSave = debounce(() => {}, 500);
const onSetCurrentActive = (item) => {
  currentActive.value = item;
  emits("onSelected", item, originData);
  onClose();
};

/**
 *
 * @param data 值不为空说明为编辑状态
 */
const onOpen = (data: any, origin: any) => {
  organizeDatas.value = data;
  originData = origin;
  visible.value = true;
};
const onClose = () => {
  visible.value = false;
};

defineExpose({
  onOpen,
  onClose
});
</script>

<style lang="less" scoped>
:deep(.t-dialog__header-content) {
  font-size: 16px;
  
  font-weight: 700;
  text-align: left;
  color: #13161b;
}

.toBody {
  //   max-height: 70vh;
  //   overflow: auto;
  // padding: 0 24px;
  // display: flex;
  // flex-direction: column;
  // align-items: center;
  .data {
    display: flex;
    flex-direction: column;
    .active {
      background: #daecff;
    }
    &-item {
      display: flex;
      align-items: center;
      padding: 8px;

      border-radius: 8px;
      margin-bottom: 4px;
      transition: all 0.15s linear;
      &:hover {
        background: #daecff;
      }
      .img {
        width: 40px;
        height: 40px;
      }
      .name {
        flex: 1;
        font-size: 14px;
        
        font-weight: 400;
        text-align: left;
        color: #13161b;
        margin-left: 8px;
        margin-right: 8px;
      }
      .icon {
        width: 24px;
        height: 24px;
        color: #2069e3;
      }
    }
  }
}

:deep(.t-form__item) {
  margin-bottom: 10px;
}

.t-dialog__ctx .t-dialog__position {
  padding: 0;
}
</style>
