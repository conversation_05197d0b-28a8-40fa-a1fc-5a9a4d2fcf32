import debounce from 'lodash/debounce';
import { SQUARE_CACHE } from '../constant';
import { useSquareStore } from '@/views/square/store/square';
import useNavigate from './navigate';
import { isSwitchAsking } from '@/views/square/hooks/squareAccount'; // 确保导出

export function useEventHandling(setActiveIndex, accountChange) {
  const store = useSquareStore();
  const { redirectFromOuter } = useNavigate();

  const { ipcRenderer } = require('electron');
  const DEBOUNCE_TIME = 800;

  // 更新未读消息计数 - 使用防抖，因为短时间内的多次更新只需要最后一次
  const updateUnreadCount = debounce((_) => {
    console.log('on-unread-post');
    store.getSquaresList(true, true);
  }, DEBOUNCE_TIME);

  // 处理通知消息 - 使用防抖，合并短时间内的多次通知
  const squareNotify = debounce(() => {
    console.log('square-notify');
    store.getSquaresList(true, true);
    store.fetchNewsStats();
  }, DEBOUNCE_TIME);

  // 清除缓存
  const clearCache = debounce(() => {
    Object.keys(SQUARE_CACHE).forEach((key) => localStorage.removeItem(SQUARE_CACHE[key]));
  }, DEBOUNCE_TIME);

  // 处理外部重定向
  const handleOuterRedirect = debounce((_, arg) => {
    if (isSwitchAsking.value) return; // 切换中不处理

    console.log('outer-redirect', arg);
    setActiveIndex({ path: arg?.redirect });
    redirectFromOuter(arg, accountChange);
  }, DEBOUNCE_TIME);

  // 监听事件键
  const listenerKey = {
    onUnreadPost: 'on-unread-post',
    squareNotify: 'square-notify',
    quitLogin: 'quit-login',
    childProcessGone: 'child-process-gone',
    outerRedirect: 'outer-redirect',
  };

  // 监听器映射
  const listeners = {
    [listenerKey.onUnreadPost]: updateUnreadCount,
    [listenerKey.squareNotify]: squareNotify,
    [listenerKey.quitLogin]: clearCache,
    [listenerKey.childProcessGone]: clearCache,
    [listenerKey.outerRedirect]: handleOuterRedirect,
  };

  // 移除所有监听器
  const removeAllListeners = () => {
    const list = Object.values(listenerKey);
    list.forEach((key) => {
      ipcRenderer.removeAllListeners(key);
    });
  };

  // 先移除所有监听器，避免重复触发
  removeAllListeners();

  // 添加监听器
  setTimeout(() => {
    Object.entries(listeners).forEach(([key, listener]) => {
      ipcRenderer.on(key, listener);
    });
  }, 100);

  // 返回一个函数，用于在组件卸载时移除监听器
  return () => {
    // 取消所有防抖和节流的待处理调用
    updateUnreadCount.cancel();
    squareNotify.cancel();
    clearCache.cancel();
    handleOuterRedirect.cancel();

    // 移除所有监听器
    removeAllListeners();
  };
}
