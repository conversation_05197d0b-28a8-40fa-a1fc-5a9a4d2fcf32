<template>
  <t-drawer
    v-model:visible="visible"
    class="drawerSetForm drawerSetBodyNoPadding"
    header="详情"
    :z-index="1500"
    :on-confirm="onClickConfirm"
    :close-btn="true"
    :size="'592px'"
  >
    <template #closeBtn>
      <!-- <svg class="iconpark-icon" style="width: 16px; height: 16px">
        <use href="#close" />
      </svg> -->
      <iconpark-icon name="iconerror" style="font-size: 24px" class="iconerror"></iconpark-icon>
    </template>
    <template #footer>
      <div v-if="data" class="operates">
        <t-button
          theme="default"
          variant="outline"
          class="operates-item"
          @click="onClose"
        >{{ $t("member.cancel") }}</t-button>
        <t-button
          v-show="data.activate_audit === 1"
          theme="danger"
          variant="outline"
          class="operates-item"
          @click="onReject"
        >{{ $t("member.active.reject_apply") }}</t-button>
        <t-button
          v-show="data.activate_audit === 1"
          theme="primary"
          class="operates-item"
          @click="onSuccess"
        >{{ $t("member.active.approved") }}</t-button>
      </div>
    </template>

    <!-- <span v-if="false" class="tabs">
      <span
        v-for="(tab, tabIndex) in tabs"
        v-show="tab.isShow"
        :key="tabIndex"
        :class="{ 'tabs-item': true, 'tabs-active': currentTab === tab.value }"
        @click="onSetTabItem(tab.value)"
      >{{ tab.label }}</span>
    </span> -->

    <div class="tabss">
      <div
        v-for="(tab, tabIndex) in tabs"
        v-show="tab.isShow"
        :key="tabIndex"
        class="item"
        :class="{
          active: currentTab === tab.value
        }"
        @click="onSetTabItem(tab.value)"
      >
        {{ tab.label }}
      </div>

      <!-- <div class="act-tag" :class="'act-' + currentTab"></div> -->
    </div>

    <div
      v-if="data && data.submit_data && data.data && visible"
      class="drawerSet-body"
    >
      <form-detail
        v-show="currentTab === 'active'"
        ref="runtimeRef"
        :widgets="data.submit_data.free_form"
        @release="releaseRun"
      />

      <form-detail
        v-show="currentTab === 'origin'"
        ref="runtimeDetailRef"
        :widgets="data.data.free_form"
        @release="releaseRun"
      />
      <div class="system">
        <div class="detail-control" style="width: 100%">
          <div class="lable">
            <span class="line" />{{ $t("member.active.system_info") }}
          </div>
        </div>
        <div class="detail-control" :style="{ width: '50%' }">
          <div class="subLable">
            <!-- {{ $t("member.active.membership_type") }} -->
            类型
          </div>
          <div class="value">
            {{
              data.type === 1
                ? '成员'
                : '个人'
            }}
          </div>
        </div>
        <div class="detail-control" :style="{ width: '50%' }">
          <div class="subLable">{{ $t("member.active.apply_status") }}</div>
          <div class="value">
            <div v-if="data.activate_audit === 1" class="wait">
              {{ $t("member.active.audit") }}
            </div>
            <div v-else-if="data.activate_audit === 2" class="success">
              {{ $t("member.active.passed") }}
            </div>
            <div v-else-if="data.activate_audit === 3" class="reject">
              {{ $t("member.active.rejected") }}
            </div>
            <div v-else-if="data.activate_audit === 4" class="default">
              {{ $t("member.bing.b") }}
            </div>
          </div>
        </div>
      </div>

      <div v-if="data.record && data.record.length > 0" class="sports">
        <span class="sports-title mb-8px block">动态</span>
        <t-steps
          layout="vertical"
          theme="dot"
          :current="data.record.length"
          readonly
        >
          <t-step-item
            v-for="(record, recordIndex) in data.record"
            :key="recordIndex"
          >
            <template #title>
              <div class="toTitle">{{ record.created_at }}</div>
            </template>
            <template #content>
              <div class="toContent">{{ record.content }}</div>
              <div v-show="record.operator_name" class="toContent">
                操作人：{{ record.operator_name }}
              </div>
            </template>
          </t-step-item>
        </t-steps>
      </div>
    </div>
  </t-drawer>
  <RejectModal ref="rejectModalRef" @on-send="onSaveReject" />
  <SuccessModal ref="successModalRef" @on-send="onSaveSuccess" />
</template>

<script setup lang="ts">
import { ref, Ref, reactive, watch, computed } from "vue";
import { DialogPlugin, MessagePlugin } from "tdesign-vue-next";
import formDetail from "@renderer/components/free-from/detail/index.vue";
import { formDiff } from "@renderer/components/free-from/utils";
import { updateAllCount } from "@renderer/views/uni/hooks/total";

import {
  postRejectMemberActivateAxios,
  postAgreeMemberActivateAxios
} from "@renderer/api/uni/api/businessApi";
import RejectModal from "@renderer/views/uni/member_home/panel/apply-member-panel/modal/reject-modal.vue";
import SuccessModal from "@renderer/views/uni/member_home/panel/apply-member-panel/modal/success-modal.vue";
import { useI18n } from "vue-i18n";
import { isArray } from "lodash";
import { allRoutersUuid } from "@renderer/constants/index";
import { getResponseResult } from "@/utils/myUtils";
import { useRoute } from "vue-router";
import { platform } from "@renderer/views/digital-platform/utils/constant";
import { useDigitalPlatformStore } from "@renderer/views/digital-platform/store/digital-platform-store";
import { useUniStore } from "@renderer/views/uni/store/uni";
import { getUniTeamID } from "@renderer/views/uni/utils/auth";
const { t } = useI18n();
const route = useRoute();
// import { ClientSide } from "@renderer/types/enumer";
const optionsOrganizeType = [
  { label: "企业", value: 1 },
  { label: "商协会", value: 2 },
  { label: "政府单位", value: 4 },
  { label: "个体户", value: 3 },
  { label: "其他", value: 0 }
];
const tabs = ref([
  { label: "激活信息", value: "active", isShow: true },
  { label: "原信息", value: "origin", isShow: true }
]);
const currentTab = ref("active");
// 运行时
const controls = ref([]);
const runtimeRef = ref(null);

const organizeSelectCompRef = ref(null);

const visible = ref(false);

const data: Ref<any> = ref(null);
const emits = defineEmits(["reload"]);
const digitalPlatformStore = useDigitalPlatformStore();
watch(
  () => visible.value,
  (cur) => {}
);

const props = defineProps({
  platform: {
    type: String,
    default: '',
  },
})
const store = useUniStore();

// 平台类型 目前只有digital-platform
const platformCpt: any = computed(() => {
  return props.platform || route.query?.platform
})

const activeAccount = computed(() => {
  if(platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount
  } else if(platformCpt.value === platform.digitalWorkbench)  {
    return route.query;
  } else {
    return store.activeAccount
  }
})


const currentTeamId = computed(() => {
  if(platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount?.teamId
  } else if(platformCpt.value === platform.digitalWorkbench){
    return route.query?.teamId || 0;
  } else {
    return  getUniTeamID()
  }
})

const releaseRun = (data) => {
  console.log(data);
};
const onInitFreeForm = (origins: any) => {
  if (origins && origins.submit_data && origins.submit_data.free_form) {
    const baseList = origins.submit_data.free_form.filter(
      (v: any) => v.type === "BaseInfoPolitics"
    );
    baseList.map((v: any) => {
      // 会员级别的设置
      let origin: any = null;
      origin = v.origin.find((or: any) => or.vModel === "memberLevel");
      if (origin) {
        const levelItem = origin.options.find(
          (v: any) => v.id === origin.value
        );
        origin.value = origins.level_name || levelItem.level_name;
      }

      // 组织名称
      origin = v.origin.find((or: any) => or.vModel === "organizeName");
      if (origin && origin.value) {
        // origin.value = origin.value.teamFullName;
      }

      // avatar头像
      origin = v.origin.find((or: any) => or.vModel === "logo");
      if (origin && origin.value && origin.value.length > 0) {
        // origin.value = origin.value[0].file_name;
      }

      // 组织logo
      //   origin = v.origin.find((or: any) => or.vModel === "organizeLogo");
      //   if (origin && origin.value && origin.value.length > 0) {
      //     origin.value = origin.value[0].file_name;
      //   }

      // 部门
      origin = v.origin.find((or: any) => or.vModel === "department");
      // if (origin) origin.value = origin_data.data.idDepartment;
      if (origin) origin.value = origins.departments;

      // 组织类型
      origin = v.origin.find((or: any) => or.vModel === "organizeType");
      if (origin) {
        const item = origin.options.find((v: any) => v.value === origin.value);
        origin.value = item ? item.label : "";
      }

      // 所在行业
      origin = v.origin.find((or: any) => or.vModel === "industryType");
      if (origin) {
        origin.value = origins.submit_data.industry_text;
      }

      // 组织规模
      origin = v.origin.find((or: any) => or.vModel === "organizeScale");
      if (origin) {
        origin.value = origins.submit_data.size_text;
      }
      // 组织地址
      origin = v.origin.find((or: any) => or.vModel === "organizeAddress");
      if (origin) {
        origin.value =
          origins.submit_data.country +
          origins.submit_data.province +
          origins.submit_data.city +
          origins.submit_data.district;
        origin.detail = origins.submit_data.address;
      }
      return v;
    });

    // controls.value = origins.submit_data.free_form;
  }
  //   else {
  //     controls.value = [];
  //   }
};

const onInitOriginFreeForm = (res: any) => {
  let detailInfo = res[0];
  let systemInfo = res[1];
  console.log(detailInfo);

  if (detailInfo.type === 2) {
    if (detailInfo.data.free_form) {
      detailInfo.data.free_form = formDiff(
        detailInfo.data.free_form,
        systemInfo.personal_form
      );
      onFillFormReShow(detailInfo.data.free_form, detailInfo);
    } else {
      onFillFormReShow(systemInfo.personal_form, detailInfo);
      detailInfo.data.free_form = systemInfo.personal_form;
    }
    // onFillFormReShow(systemInfo.personal_form, detailInfo);
  } else if (detailInfo.type === 1) {
    if (detailInfo.data.free_form && detailInfo.data.free_form.length > 0) {
      detailInfo.data.free_form = formDiff(
        detailInfo.data.free_form,
        systemInfo.team_form
      );

      // return;
      onFillFormReShow(detailInfo.data.free_form, detailInfo);
    } else {
      onFillFormReShow(systemInfo.team_form, detailInfo);
      detailInfo.data.free_form = systemInfo.team_form;
    }
    console.log(detailInfo.data.free_form);
    // onFillFormReShow(systemInfo.team_form, detailInfo);
  }

  // 把系统默认的全部显示出来
  detailInfo.data.free_form.map((v) => {
    v.show = true;
    if (v.type === "BaseInfoPolitics") {
      v.origin.map((or) => {
        or.isShow = true;
        or.disabled = false;
        initSystemDefault(or);
        return or;
      });
    }
    return v;
  });

  console.log(detailInfo);
};

const initSystemDefault = (or: any) => {
  const requireVModel = [
    "organizeName",
    "memberLevel",
    "name",
    "phone",
    "department",
    "joinTime",
    "expireTime"
  ];
  const noRequireVModel = [
    "logo",
    "email",
    "memberNum",
    "reference",
    "organizeLogo",
    "organizeAbbrName",
    "organizeType",
    "industryType",
    "organizeScale",
    "organizeAddress"
  ];

  if (requireVModel.includes(or.vModel)) {
    or.required = true;
  } else if (noRequireVModel.includes(or.vModel)) {
    or.required = false;
  }
};

// 回填详情里面的数据
/**
 *
 * @param result 系统表单
 * @param applyData 详情非表单数据，为了填充, 这个是用于处理编辑回填，需要修改成详情回填
 */
const onFillFormReShow = (result: Array<any>, origin_data: any) => {
  if (result && result.length > 0) {
    const baseList = result.filter((v: any) => v.type === "BaseInfoPolitics");
    baseList.map((v: any) => {
      // 会员级别的设置
      let origin: any = null;
      origin = v.origin.find((or: any) => or.vModel === "memberLevel");
      if (origin) {
        // const levelItem = origin.options.find(
        //   (v: any) => v.id === origin.value
        // );
        // origin.value = origin_data.data.level;
        console.log(origin, "hahah");
        origin.value = origin_data.data.level_name;
      }

      // 组织名称
      origin = v.origin.find((or: any) => or.vModel === "organizeName");
      if (origin) {
        // origin.value = [
        //   { teamFullName: origin_data.team_name, teamId: origin_data.teamId }
        // ];
        // origin.value = origin_data.team_name;
        if (origin.value) {
        } else {
          origin.value = origin_data.data.team_name;
        }
      }

      // 会员编号
      origin = v.origin.find((or: any) => or.vModel === "memberNum");
      if (origin) {
        origin.value = origin_data.data.no;
      }

      // 推荐人
      origin = v.origin.find((or: any) => or.vModel === "reference");
      if (origin) {
        origin.value = origin_data.data.referrer;
      }

      // avatar头像
      origin = v.origin.find((or: any) => or.vModel === "logo");
      if (origin && origin_data.avatar) {
        origin.value = [
          {
            file_name: origin_data.avatar,
            file_name_short: origin_data.avatar,
            original_name: origin_data.avatar
          }
        ];
      }

      // 手机
      origin = v.origin.find((or: any) => or.vModel === "phone");
      if (origin) {
        origin.code_value = Number(origin_data.telCode);
        origin.value = origin_data.telephone;
      }

      // 代表人姓名
      origin = v.origin.find((or: any) => or.vModel === "name");
      if (origin) origin.value = origin_data.name;

      // 到期时间
      origin = v.origin.find((or: any) => or.vModel === "expireTime");
      if (origin) {
        origin.value = origin_data.data.expire_time;
        origin.is_expire_value = !!origin_data.data.no_expire;
      }

      // 入会时间
      origin = v.origin.find((or: any) => or.vModel === "joinTime");
      if (origin) origin.value = origin_data.join_time;

      // 邮箱
      origin = v.origin.find((or: any) => or.vModel === "email");
      if (origin) origin.value = origin_data.data.email;

      // 部门
      origin = v.origin.find((or: any) => or.vModel === "department");
      // if (origin) origin.value = origin_data.data.idDepartment;
      if (origin) origin.value = origin_data.departments;

      // 组织logo
      //   origin = v.origin.find((or: any) => or.vModel === "organizeLogo");
      //   if (origin && origin.value && origin.value.length > 0) {
      //     origin.value = origin.value[0].file_name;
      //   }

      // 组织类型
      origin = v.origin.find((or: any) => or.vModel === "organizeType");
      if (origin) {
        const resultA = optionsOrganizeType.find(
          (v) => v.value === origin_data.data.team_type
        );
        origin.value = resultA ? resultA.label : "";
      }

      // 所在行业
      origin = v.origin.find((or: any) => or.vModel === "industryType");
      if (origin) {
        origin.value = origin_data.data.industry_text;
      }

      // 组织规模
      origin = v.origin.find((or: any) => or.vModel === "organizeScale");
      if (origin) {
        origin.value = origin_data.data.size_text;
      }
      // 组织地址
      origin = v.origin.find((or: any) => or.vModel === "organizeAddress");
      if (origin) {
        // origin.value =
        //   origins.submit_data.country +
        //   origins.submit_data.province +
        //   origins.submit_data.city +
        //   origins.submit_data.district;
        if (origin.value && isArray(origin.value) && origin.value.length < 1) {
          origin.value = undefined;
        } else {
          origin.value =
            origin_data.data.country +
            origin_data.data.province +
            origin_data.data.city +
            origin_data.data.district;
        }

        origin.detail = origin_data.data.address;
      }
      return v;
    });
  }
};

// 驳回
const rejectModalRef: Ref<any> = ref(null);
const onReject = () => {
  rejectModalRef.value.onOpen({ id: data.value.id });
};
const onSaveReject = async (val) => {
  let result = null;
  try {
    result = await postRejectMemberActivateAxios({
      id: val.id,
      content: val.area
    }, currentTeamId.value);
    result = getResponseResult(result);
    if (!result) return;
    MessagePlugin.success("驳回成功");
    // updateAllCount(); // 更新侧边栏数据
    rejectModalRef.value.onClose();
    onClose();
    emits("reload");
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    MessagePlugin.error(errMsg);
  }
};

// 通过

// const onGetMemberSetting = async () => {
//   let result = null;
//   // eslint-disable-next-line no-async-promise-executor
//   return new Promise(async (resolve, reject) => {
//     try {
//       result = await getMemberSettingAxios();
//       result = getResponseResult(result);
//       if (!result) {
//         reject();
//         return;
//       }
//       resolve(result.data);
//     } catch (error) {
//       const errMsg = error instanceof Error ? error.message : error;
//       MessagePlugin.error(errMsg);
//       reject();
//     }
//   });
// };

const successModalRef = ref(null);

const onSuccess = () => {
  // onGetMemberSetting().then((res) => {
  //   successModalRef.value.onOpen(data.value, res);
  // });

  const confirmDia = DialogPlugin({
    header: t("member.active.sure_to_passed"),
    theme: "info",
    body: '审核通过表示该组织激活成功，激活信息将覆盖原信息',
    closeBtn: null,
    confirmBtn: "确定",
    className: "delmode",
    onConfirm: async () => {
      // 删除字段操作
      confirmDia.hide();
      onSaveSuccess({ id: data.value.id });

      // ipcRenderer.send(
      //   "update-nume-index",
      //   allRoutersUuid.findIndex((v) => v === "government")
      // );
    },
    onClose: () => {
      confirmDia.hide();
    }
  });
};
const onSaveSuccess = async (val: any) => {
  let result = null;
  try {
    result = await postAgreeMemberActivateAxios(val, currentTeamId.value);
    result = getResponseResult(result);
    if (!result) return;
    MessagePlugin.success(t("member.active.audit_success"));
    // successModalRef.value.onClose();
    // updateAllCount(); // 更新侧边栏数据
    onClose();
    emits("reload");
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    MessagePlugin.error(errMsg);
  }
};
const onSetTabItem = (val) => {
  currentTab.value = val;
};
const onClickConfirm = async () => {};

const onOpen = (item?: any) => {
  currentTab.value = "active";
  controls.value = [];
  data.value = null;
  // 处理激活的数据
  onInitFreeForm(item[0]);
  // 处理原始数据
  onInitOriginFreeForm(item);
  data.value = item[0];
  visible.value = true;
  //   visible.value = true;
};
const onClose = () => {
  visible.value = false;
};

defineExpose({
  onOpen,
  onClose
});
</script>
<style lang="less" scoped>
@import url("@renderer/views/uni/member_home/panel/public.less");

.tabs {
  display: flex;
  gap: 32px;
  // margin-bottom: 10px;
  padding-left: 0;
  &-item {
    font-size: 14px;

    font-weight: 400;
    text-align: center;
    color: #13161b;
    cursor: pointer;
    user-select: none;
  }
  &-active {
    font-weight: 700 !important;
    color: #2069e3 !important;
  }
}
:deep(.t-steps--vertical).t-steps--dot-anchor
  .t-steps-item--finish
  .t-steps-item__icon {
  border-color: #c7c7c8;
  background: #c7c7c8;
}

:deep(.t-steps-item--finish) {
  &::before {
    border-right-color: #c7c7c8 !important;
    color: #c7c7c8 !important;
    left: 2.5px;
    top: 24px;
  }
}

.sports {
  &-title {
    font-size: 14px;

    font-weight: 400;

    color: #717376;
  }
}

:deep(.t-steps-item):not(:last-child)::before {
  left: 3.5px !important;
  top: 24px;
  border-right-width: 1px !important;
}
.toTitle {
  font-size: 14px;

  font-weight: 400;
  color: #1A2139;

  // color: #13161b;
}
.toContent {
  color: var(--text-kyy-color-text-1, #1a2139);

  /* kyy_fontSize_2/regular */
  font-family: PingFang SC;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
}
.operates {
  display: flex;
  justify-content: flex-end;
}
.drawerSet {
  // width: 720px;
  &-body {
  }
  //   .t-drawer__content-wrapper {
  //     width: 720px !important;
  //   }
}

:deep(.t-drawer__header) {
  border-bottom: 0;
  color: red;
}
:deep(.t-drawer__body) {
  padding-top: 10px !important;
}

.tabss {
  display: flex;
  position: relative;
  border-bottom: 1px solid var(--divider-kyy-color-divider-light, #eceff5);
  height: 40px;
  width: 100%;
  gap: 44px;
  align-self: stretch;
  margin-bottom: 16px;
  padding: 0 24px;
  .item {
    // margin-right: 44px;
    color: var(--text-kyy-color-text-1, #1a2139);
    text-align: center;

    /* kyy_fontSize_3/regular */
    font-family: PingFang SC;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px; /* 150% */
    cursor: pointer;
    :deep(.t-badge--circle) {
      right: -3px;
    }

    position: relative;


  }
  .active {
    color: var(--brand-kyy-color-brand-default, #4d5eff);

    /* kyy_fontSize_3/bold */
    font-family: PingFang SC;
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px; /* 150% */
    &::after {
      position: absolute;
      background-color:  var(--brand-kyy-color-brand-default, #4d5eff);
      content: " ";
      width: 16px;
      height: 3px;
      bottom: 0;
      left: 0;
      right: 0;
      margin: auto;
      border-radius: 1.5px;
    }
  }
}
.act-tag {
  width: 16px;
  height: 3px;
  flex-shrink: 0;
  border-radius: 1.5px;
  background: var(--brand-kyy-color-brand-default, #4d5eff);
}
.act-active {
  position: absolute;
  bottom: 0px;
  left: 23px;
}
.act-origin {
  position: absolute;
  bottom: 0px;
  left: 170px;
}

.drawerSet-body {
  padding-left: 24px;
  padding-right: 24px;
}
</style>
