export interface Log {
	/**
	 * 操作时间
	 */
	created_at: string;
	/**
	 * 操作人
	 */
	openid_name: string;
	/**
	 * 操作内容
	 */
	reason: string;
}

export interface Order {
	/**
	 * 支付渠道
	 */
	pay_channel: string;
	/**
	 * 支付方式
	 */
	pay_method: string;
	/**
	 * 付款人
	 */
	pay_openid_name: string;
	/**
	 * 付款时间
	 */
	payed_at: string;
	/**
	 * 收款方
	 */
	receive_team_name: string;
	/**
	 * 保证金编号
	 */
	sn: string;
	/**
	 * 支付状态 0待付款 1已付款
	 */
	status: number;
}

export interface Refund {
	/**
	 * 银行账户
	 */
	bank_account: string;
	/**
	 * 币种
	 */
	currency: string;
	/**
	 * 开户行
	 */
	opening_bank: string;
	/**
	 * 退款金额
	 */
	refund_amount: string;
	/**
	 * 退款方式
	 */
	refund_method: string;
	/**
	 * 退款时间
	 */
	refund_time: string;
	/**
	 * 退款凭证
	 */
	refund_voucher: string[];
}

export interface MarginDetailData {
	/**
	 * 应用id
	 */
	app_id: number;
	/**
	 * 文章链接
	 */
	article_link: string;
	/**
	 * 保证金金额
	 */
	bond_amount: number;
	/**
	 * 保证金编号
	 */
	bond_sn: string;
	/**
	 * 保证金状态（0：无需支付，1：待支付，2：已支付，3：已退款）
	 */
	bond_status: number;
	/**
	 * 展示渠道
	 */
	channels: {
		channel_type: string;
	}[];
	/**
	 * 创建时间
	 */
	created_at: string;
	/**
	 * 币种
	 */
	currency: string;
	/**
	 * 状态（1：正常，2：已删除）
	 */
	deleted_status: number;
	/**
	 * 桌面端链接
	 */
	desktop_link: string;
	/**
	 * 网页H5链接
	 */
	h5_link: string;
	/**
	 * 操作记录
	 */
	logs: Log[];
	/**
	 * 小程序原始ID
	 */
	mini_program_original_id: string;
	/**
	 * 小程序路径
	 */
	mini_program_path: string;
	/**
	 * 应用名称
	 */
	name: string;
	/**
	 * 支付信息
	 */
	order: null | Order;
	/**
	 * 付款时间
	 */
	payed_at: string;
	/**
	 * 应用图片链接
	 */
	picture_linking: string;
	refund: null | Refund;
	/**
	 * 分享链接
	 */
	share_link: string;
	/**
	 * 发布组织logo
	 */
	team_logo: string;
	/**
	 * 发布组织名称
	 */
	team_name: string;
	/**
	 * 应用类型（App跳转：app，网页H5跳转：h5，微信公众号：wechat_official，微信小程序：mini_program）
	 */
	type: string;
	/**
	 * 应用uuid
	 */
	uuid: string;
}

export interface RefundParams {
	/**
	 * 应用id
	 */
	app_id: number;
	/**
	 * 银行账户
	 */
	bank_account: string;
	/**
	 * 开户行
	 */
	opening_bank: string;
	/**
	 * 退款金额
	 */
	refund_amount: number;
	/**
	 * 退款方式：线下退款
	 */
	refund_method: string;
	/**
	 * 退款时间
	 */
	refund_time: string;
	/**
	 * 退款凭证
	 */
	refund_voucher: string[];
}
