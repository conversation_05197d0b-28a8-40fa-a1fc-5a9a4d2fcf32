// 扩展 Window 接口以包含 LynkerSDK
declare global {
  interface Window {
    LynkerSDK: typeof LynkerSDK;
    gc?: () => void;
  }
}

import core from '@lynker-desktop/electron-sdk/renderer';
import lodash from 'lodash';
import type { BWItem, ElectronWindowsManagerOptions } from '@lynker-desktop/electron-window-manager/common';
import qs from 'qs';
import md5 from 'md5';
import { ClientSide } from '@renderer/types/enumer';
import { i18nt } from '@/i18n';
import { CHANNEL, h5HostEnum, h5GetwayEnum, type LynkerSDKConfig, type MsgQuery, type UserInfo } from './packages/jssdk/src/common';
import createMessage from './components/message';

// 定义预览图片的接口
interface PreviewImageOptions {
  images: string[];
  index: number;
  url: string;
}
type PreviewImageOptions2 = {
  imgIndex: number,
  imgs: Array<{
    title: string,
    url: string,
    type: string,
    size: number,
    officeId: string|null,
    imgIndex: number,
  }>
}

core.log('log', '初始化SDK');
/** 手动设置环境key */
const MANUAL_KEY = '___MANUAL_ENV__';

const ENV = (window.localStorage.getItem(MANUAL_KEY) || `${__APP_ENV__.VITE_API_ENV || 'PROD'}`) as LynkerSDKConfig['env'];
const protocaol = window.location.protocol;

/** BASE域名 */
let WinUrlBase = `${window.location.protocol}//${window.location.host}`;
if (/^file/gi.test(protocaol)) {
  const decodedUrl = decodeURIComponent(window.location.href);
  /**
   * 注意如后期改打包资源目录要同步改这正则
   */
  const regex = /^.*?\/electron\/renderer\//i;
  const match = decodedUrl.match(regex);
  WinUrlBase = `${match[0]}`;
}

/** 获取本地url */
export function getAppUrl(path: string, html = '/index.html') {
  html = html.replace(/^\//, '');
  if (!/^file/gi.test(protocaol) && /^index\.html$/gi.test(html)) {
    html = '';
  }
  return `${WinUrlBase}/${html}#${path}`;
}
/** 获取H5 url */
export function getH5Url(path: string, clearCache = true) {
  path = path.replace(/^\//, '');
  const url = new URL(`${h5HostEnum[ENV]}/${path}`);
  /** 防止缓存html */
  if (clearCache) {
    url.searchParams.append('__RINGKO_DESKTIOP_TIME__', `${Date.now()}`);
  }
  return url.toString();
}

/** 获取H5 url 并携带环境参数 */
export const getH5UrlWithParams = (path: string, params: Record<string, string>, clearCache = false) => {
  const isPackage = core.remote.app.isPackaged;
  const query = {
    env: LynkerSDK.config.env,
    token: LynkerSDK.config.token,
    teamId: LynkerSDK.workBench.getActiveTeamId(),
    openId: LynkerSDK.getUserInfo().openid,
    ...params,
  };
  /** 防止缓存html */
  if (!isPackage && clearCache) {
    query['__RINGKO_DESKTIOP_TIME__'] = `${Date.now()}`;
  }
  const queryStr = qs.stringify({
    ...query,
  })
  const url = `${path}?${queryStr.toString()}`.replace(/^\//, '');

  return isPackage ? LynkerSDK.getH5Url(url, clearCache) : `http://localhost:8080/${url}`;
}

/** 默认窗口加载动画URL */
export const LOADING_URL = getAppUrl('', '/windows/loading/index.html');

/** 检查当前窗口是否是主窗口 */
export async function checkIsMainWindow() {
  try {
    const item = await core.windowManager.getCurrentWindow();
    const hash = window.location.hash;
    console.error('hash', hash);
    /** 判断主窗口 */
    if (item._name === 'mainWindow' && /^#\/main\/message\??$/gi.test(hash)) {
      return true;
    }
    return false;
  } catch (error) {
    return false;
  }
}

/** 获取preload路径 */
function getPreloadPath() {
  const path = core.remote.require('path');
  if (core.remote.app.isPackaged) {
    return path.join(core.remote.app.getAppPath(), 'dist/electron/main/preload.js');
  }
  return path.join(core.remote.app.getAppPath(), './preload.js');
}

/**
 * 业务SDK类
 * 提供应用程序核心功能的静态方法集合
 */
class LynkerSDK {
  static webFrame = require('electron').webFrame;

  /** 启用webContents */
  static enableWebContents = (webContentsId: number) => {
    return core?.enable?.(webContentsId);
  };

  /** H5 URL获取方法的引用 */
  static getH5Url = getH5Url;

  static getH5UrlWithParams = getH5UrlWithParams;

  /** 获取本地url */
  static getAppUrl = getAppUrl;

  static si = core.remote.require('systeminformation') as typeof import('systeminformation');

  static fs = window?.require('fs') as typeof import('node:fs');

  static os = window?.require('os') as typeof import('node:os');

  static path = window?.require('path') as typeof import('node:path');

  static child_process = core.remote.require('child_process') as typeof import('node:child_process');

  static remote = core.remote;

  static ipcRenderer = core.ipc.ipcRenderer;

  static ipc = core.ipc;

  static shell = core.remote.shell;

  static dialog = core.remote.dialog;

  static clipboard = core.remote.clipboard;

  static nativeImage = core.remote.nativeImage;

  static require = core.remote.require;

  static log = core.log;

  static app = core.remote.app;

  static tiff = core.remote.require('tiff.js') as typeof import('tiff.js');

  static eStore = core.remote.require('electron-store') as typeof import('electron-store');

  static eLog = core.remote.require('electron-log') as typeof import('electron-log');

  static downloader = core.remote.require('nodejs-file-downloader') as typeof import('nodejs-file-downloader');

  /** SDK配置 */
  private static _config: LynkerSDKConfig = {
    /** 当前开发环境 */
    env: ENV,
    /** 当前国际化 */
    i18n: 'zh-cn',
    /** 用户token */
    token: window.localStorage.getItem('main_token'),
    /** web端域名 */
    h5Host: h5HostEnum[ENV] || h5HostEnum['PROD'],
    /** h5获取方式 */
    h5Getway: h5GetwayEnum[ENV] || h5GetwayEnum['PROD'],
    /** 唤起参数 */
    launchOptions: '',
    preloadPath: getPreloadPath(),
    /** Vite配置 */
    viteConfig: {
      VITE_APP_TITLE: '',
      VITE_MAP_KEY: '',
      VITE_APP_REGION: '',
      VITE_API_ENV: '',
      VITE_IM_KEY: '',
      VITE_WPS_KEY: '',
      VITE_UMEN_PID: '',
      VITE_USER_NODE_ENV: '',
      VITE_APP_CONFIG_INFO: '',
      ...__APP_ENV__,
      VITE_APP_MAS: !!__APP_ENV__.VITE_APP_MAS,
    },
  };

  /**
   * 获取SDK配置
   * @returns {LynkerSDKConfig} 当前SDK配置
   */
  static get config(): LynkerSDKConfig {
    return { ...this._config }; // 返回配置的副本以防止直接修改
  }

  /**
   * 设置SDK配置
   * @param {Partial<LynkerSDKConfig>} v - 要更新的配置项
   */
  static set config(v: Omit<Partial<typeof this._config>, 'env'|'h5Host'>) {
    this._config = lodash.merge({}, this._config, v);
    core.ipc.invokeRenderer('__APP_CONFIG_CHANGE__', this._config);
  }

  /**
   * 获取随机UUID
   * @returns {string} 随机UUID
   */
  static getRandomUUID = () => {
    return core.getRandomUUID();
  }

  /**
   * 获取唯一ID
   * @returns {Promise<string>} 唯一ID
   */
  static getUniqueId = async () => {
    const [system, baseboard, bios, uuid] = await Promise.all([
      // uuid.os：操作系统安装时生成的 UUID，通常唯一。
      this.si.system(),
      // baseboard.serial：主板序列号。
      this.si.baseboard(),
      // bios.serial：BIOS 的序列号。
      this.si.bios(),
      // uuid.os：操作系统安装时生成的 UUID，通常唯一。
      this.si.uuid(),
    ]);

    const uniqueId = [
      system.manufacturer,
      system.model,
      baseboard.serial,
      bios.serial,
      uuid.os,
    ].join('-')
      .replace(/ /g, '_') // 先将空格替换为下划线
      .replace(/[^\w\-]/g, ''); // 再去除除字母、数字、下划线、- 以外的字符

    console.log('Unique ID:', uniqueId);
    return uniqueId;
  }

  /**
   * 打开外部应用
   * @param url 外部应用url
   * @param isInApp 是否在app内打开
   * @returns 外部应用url
   */
  static openExternalApp = (options: MsgQuery['openExternalApp']['query']): string => {
    const { url, title = '', isInApp = false } = options;
    const h5Path = "safeCheck";
    const openid = this.getUserInfo().openid;
    const h5Link = `${this.config.h5Getway}/${h5Path}?link=${encodeURIComponent(url)}&openid=${encodeURIComponent(
      openid,
    )}`;
    const md5Url = md5(h5Link);
    if (isInApp) {
      this.windowsTabs.openTab({
        tabsId: md5Url,
        tabsTitle: title || url,
        isOnlyOneTabHideTabs: true,
        options: {
          url: h5Link,
          title: '',
        },
      });
    } else {
      this.shell.openExternal(h5Link);
      console.error('openExternalApp', h5Link);
    }
    return h5Link;
  }

  /**
   * 获取用户信息
   * @returns {Promise<any>} 用户信息
   */
  static getUserInfo = (): UserInfo => {
    const string = window.localStorage.getItem('profile');
    try {
      if (!string) {
        throw new Error('获取用户信息失败');
      }
      return JSON.parse(string);
    } catch (error) {
      throw new Error('获取用户信息失败');
    }
  }

  static getWebContentsId(id: number) {
    return core.remote.webContents.fromId(id);
  }

  /**
   * 设置环境
   * @param {LynkerSDKConfig['env']} env - 环境
   */
  static setEnv(env: LynkerSDKConfig['env'] | 'RESET') {
    window.localStorage.clear();
    if (env === 'RESET') {
      const systemEnv = `${__APP_ENV__.VITE_API_ENV || 'PROD'}`;
      window.localStorage.removeItem(MANUAL_KEY);
      this._config = {
        ...this._config,
        env: systemEnv as LynkerSDKConfig['env'],
        h5Host: h5HostEnum[systemEnv as LynkerSDKConfig['env']] || h5HostEnum['PROD'],
      };
    } else {
      window.localStorage.setItem(MANUAL_KEY, env);
      this._config = {
        ...this._config,
        env,
        h5Host: h5HostEnum[env] || h5HostEnum['PROD'],
      };
    }
    core.ipc.invokeRenderer('__APP_CONFIG_CHANGE__', this._config);
    // 切换环境成功后，重启应用
    alert('切换环境成功, 应用将立即重新启动');
    core.remote.app.relaunch();
    setTimeout(() => {
      core.remote.app.exit();
    }, 0);
  }

  /**
   * 检查当前环境是否是手动设置的环境
   * @returns {boolean} 是否是手动设置的
   */
  static checkIsManualEnv() {
    return !!window.localStorage.getItem(MANUAL_KEY);
  }

  /**
   * 检查当前窗口是否是主窗口
   * @returns {Promise<boolean>} 是否是主窗口
   */
  static checkIsMainWindow = checkIsMainWindow;

  static merageUrl(url: string, query: Record<string, string>) {
    const urlObj = new URL(url);
    Object.keys(query).forEach((key) => {
      urlObj.searchParams.set(key, query[key]);
    });
    const qString = qs.stringify(lodash.pickBy(query));
    return `${url}${qString ? `?${qString}` : ''}`;
  }

  /**
   * 获取主窗口localStorage中的值
   * @param {string} key - 存储键名
   * @returns {string | null} 存储的值
   */
  static getLocalStorage(key: string): string | null {
    return window.localStorage.getItem(key);
  }

  /**
   * 获取当前窗口实例
   * @returns {Promise<any>} 当前窗口实例
   */
  static async getCurrentWindow() {
    return await core.windowManager.getCurrentWindow();
  }

  /**
   * 设置主窗口localStorage的值
   * @param {string} key - 存储键名
   * @param {string | null} value - 要存储的值
   */
  static setLocalStorage(key: string, value: string | null): void {
    window.localStorage.setItem(key, value);
  }

  /**
   * 获取启动/唤起参数
   * @returns {Promise<string>} 启动参数
   */
  static getLaunchOptions = async (): Promise<string> => {
    const options = await core.getLaunchOptions();
    this.config = {
      launchOptions: `${options}`,
    };
    return options;
  }

  /**
   * 清除启动/唤起参数
   * @returns {Promise<string>} 清除结果
   */
  static delLaunchOptions = async (): Promise<string> => {
    const options = await core.delLaunchOptions();
    this.config = {
      launchOptions: '',
    };
    return options;
  }

  /**
   * 退出登录
   * @returns {Promise<void>}
   */
  static logout = async (): Promise<void> => {
    try {
      localStorage.clear();
      core.remote.app.relaunch();
      setTimeout(() => {
        core.remote.app.exit();
      }, 0);
    } catch (error) {
      console.error(error);
    }
  }

  /**
   * 关闭所有窗口
   * @returns {Promise<void>}
   */
  static closeAllWindows = async (): Promise<void> => {
    try {
      // 历史遗留问题，需要关闭所有窗口
      await this.ipc.invokeMain('window-close-all');
    } catch (err) {
      console.error(err);
    }
    const allWindows = await core.windowManager.getAll();
    allWindows.forEach((window) => {
      core.windowManager.close(window.id);
    });
  }

  /**
   * 打开开发工具窗口
   * @returns {Promise<void>}
   */
  static openDebugTools = async (): Promise<void> => {
    const _url = getAppUrl('', '/_jssdk/components/debugtools/index.html');
    await core.windowManager.create({
      name: 'debugtools',
      url: _url,
      browserWindow: {
        show: true,
        minHeight: 100,
        minWidth: 100,
        width: 450,
        height: 600,
        alwaysOnTop: true,
      },
    });
  }

  /**
   * 打开web窗口
   * @deprecated 请使用 windowsTabs.openTab
   * @param {string} name - 窗口名称
   * @param {string} url - 窗口URL
   * @param {string} [extraData] - 额外数据
   * @param {boolean} [openDevTools=false] - 是否打开开发者工具
   * @returns {Promise<number>} 窗口ID
   */
  static async openWebWindow(name: string, url: string, extraData?: string, openDevTools = false): Promise<number> {
    const win = await core.windowManager.create({
      name: `${name}`,
      extraData: `${extraData}`,
      url: `${url}`,
      openDevTools: !!openDevTools,
      browserWindow: {
        titleBarStyle: 'hidden',
        width: 1296,
        minWidth: 1296,
        height: 720,
        useContentSize: true,
        autoHideMenuBar: true,
        frame: false,
        show: true,
        webPreferences: {
          preload: '',
          nodeIntegration: true,
          contextIsolation: false,
          webSecurity: false,
        },
      },
    });
    return win.id;
  }

  /**
   * 在默认浏览器中打开URL
   * @param {string} url - 要打开的URL
   * @returns {Promise<boolean>} 是否成功打开
   */
  static async openBrowser(url: string): Promise<boolean> {
    try {
      await core.remote.shell.openExternal(url);
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * 帮助中心
   * @param query
   * @returns
   */
  static async openMyHelpWindow(query?: MsgQuery['openMyHelpWindow']['query']) {
    const qString = qs.stringify(lodash.pickBy({ id: query?.id, module_id: query?.module_id, directory_id: query?.directory_id }));
    const webPath = getH5Url(`/common/index.html#/myhelp?${qString}`);
    // const localPath = getAppUrl('/myHelp');
    const win = await core.windowManager.create({
      name: '帮助中心',
      url: webPath,
      // loadingView: {
      //   url: LOADING_URL,
      // },
      openDevTools: false,
      browserWindow: {
        titleBarStyle: 'hidden',
        width: 1296,
        minWidth: 1296,
        height: 720,
        useContentSize: true,
        autoHideMenuBar: true,
        frame: false,
        show: true,
        webPreferences: {
          preload: '',
          nodeIntegration: true,
          contextIsolation: false,
          webSecurity: false,
        },
      },
    });
    return win;
  }

  /**
   * 我的订单
   * @param query
   * @returns
   */
  static async openMyOrderWindow(query?) {
    console.log(query);
    const url = LynkerSDK.getH5UrlWithParams('/common/index.html#/myorder', query);
    console.log('===>openMyOrderWindowurl', url);

    const win = await core.windowManager.create({
      name: '我的订单',
      url,

      openDevTools: false,
      browserWindow: {
        titleBarStyle: 'hidden',
        width: 1296,
        minWidth: 1296,
        height: 720,
        useContentSize: true,
        autoHideMenuBar: true,
        frame: false,
        show: true,
        webPreferences: {
          preload: '',
          nodeIntegration: true,
          contextIsolation: false,
          webSecurity: false,
        },
      },
    });
    win?.restore?.();
    return win;
  }

  /**
   * 地址管理
   * @param query
   * @returns
   */
  static async openMyAddressWindow(query?: string) {
    console.log(query);
    const webPath = getH5Url('/common/index.html#/myaddress');
    // const localPath = getAppUrl('/myAddress');
    const win = await core.windowManager.create({
      name: '地址管理',
      url: webPath,
      // loadingView: {
      //   url: LOADING_URL,
      // },
      openDevTools: false,
      browserWindow: {
        titleBarStyle: 'hidden',
        width: 1296,
        minWidth: 1296,
        height: 720,
        useContentSize: true,
        autoHideMenuBar: true,
        frame: false,
        show: true,
        webPreferences: {
          preload: '',
          nodeIntegration: true,
          contextIsolation: false,
          webSecurity: false,
        },
      },
    });
    win?.restore?.();
    return win;
  }

  /**
   * 发票中心
   * @param query
   * @returns
   */
  static async openMyInvoiceWindow(query?: string) {
    console.log(query);
    const webPath = getH5Url('/common/index.html#/myinvoice');
    // const localPath = getAppUrl('/myInvoice');
    const win = await core.windowManager.create({
      name: '发票中心',
      url: webPath,
      // loadingView: {
      //   url: LOADING_URL,
      // },
      openDevTools: false,
      browserWindow: {
        titleBarStyle: 'hidden',
        width: 1296,
        minWidth: 1296,
        height: 720,
        useContentSize: true,
        autoHideMenuBar: true,
        frame: false,
        show: true,
        webPreferences: {
          preload: '',
          nodeIntegration: true,
          contextIsolation: false,
          webSecurity: false,
        },
      },
    });
    win?.restore?.();
    return win;
  }

  /**
   * 打开设置窗口
   * @param query
   * @returns
   */
  static async openSettingWindow(query?: string) {
    console.log(query);
    const webPath = getAppUrl('/setting');
    const win = await core.windowManager.create({
      name: '设置',
      url: webPath,
      openDevTools: false,
      browserWindow: {
        titleBarStyle: 'hidden',
        height: 411,
        useContentSize: true,
        width: 784,
        autoHideMenuBar: true,
        minWidth: 784,
        maxWidth: 784,
        minimizable: false,
        maximizable: false,
        fullscreenable: false,
        frame: false,
        show: true,
        movable: true,
        trafficLightPosition: {
          x: 8,
          y: 14,
        },
        webPreferences: {
          contextIsolation: false,
          nodeIntegration: true,
          webSecurity: false,
          scrollBounce: true,
        },
      },
    });
    win?.restore?.();
    return win;
  }

  /**
   * 打开选人组件
   * @param webContentsId
   */
  static async openSelectMember(options?:  MsgQuery['openSelectMember']['query']) {
    // eslint-disable-next-line no-async-promise-executor
    return new Promise(async (resolve) => {
      let { webContentsId, query } = options || {};
      if (!webContentsId) {
        webContentsId = await core.ipc.getCurrentWebContentId();
      }
      const targetWin = await core.windowManager.getWindowForWebContentId(webContentsId);
      const callbackId = `select_member_callback_id_${webContentsId}_${targetWin?.id}_${core.getRandomUUID(`${webContentsId}`)}`;
      const info = encodeURIComponent(JSON.stringify({
        webContentsId,
        callbackId,
      }));
      const qString = encodeURIComponent(JSON.stringify(query || {}));
      const _url = getAppUrl('', `/_jssdk/components/SelectMember/index.html?data=${info}&options=${qString}`);
      const win = await core.windowManager.create({
        type: 'BV',
        name: `选人组件-for-${callbackId}`,
        extraData: '',
        // url: getAppUrl(`/sdk_view_select_member?data=${info}`),
        url: _url,
        browserWindow: {
          parent: targetWin?.id,
        },
      });
      if (targetWin) {
        const bounds = (targetWin as BWItem).getBounds();          // 整个窗口
        const contentBounds = (targetWin as BWItem).getContentBounds();  // 内容区域
        win.setBounds({
          x: 0,
          y: this.os.platform() === 'darwin' ? Math.max(bounds.height - contentBounds.height, 0) : 0,
          width: contentBounds.width,
          height: contentBounds.height,
        });
        // @ts-ignore
        win.setAutoResize({
          width: true,
          height: true,
        });
      }
      core.ipc.handleRenderer(callbackId, async (data) => {
        console.error(data);
        try {
          core.windowManager.close(win._id);
        } catch (err) {
          console.error(err);
        }
        resolve(data);
      });
      // win.on('close', () => {
      //   resolve(JSON.stringify([]));
      // });
    });
  }

  /**
   * 打开加入数字平台组件
   * @param options
   * @returns
   */
  static async openJoinDigitalPlatformDrawer(options?:  MsgQuery['openJoinDigitalPlatformDrawer']['query']) {
    return new Promise(async (resolve) => {
      let { webContentsId, query } = options || {};
      if (!webContentsId) {
        webContentsId = await core.ipc.getCurrentWebContentId();
      }
      const targetWin = await core.windowManager.getWindowForWebContentId(webContentsId);
      const callbackId = `open_join_digital_platform_drawer_callback_id_${webContentsId}_${targetWin?.id}_${core.getRandomUUID(`${webContentsId}`)}`;
      const info = encodeURIComponent(JSON.stringify({
        webContentsId,
        callbackId,
      }));
      const qString = encodeURIComponent(JSON.stringify(query || {}));
      const _url = getAppUrl('', `/_jssdk/components/join-digital-platform-drawer/index.html?data=${info}&options=${qString}`);
      const win = await core.windowManager.create({
        type: 'BV',
        name: `加入数字平台组件-for-${callbackId}`,
        extraData: "",
        // url: getAppUrl(`/sdk_view_select_member?data=${info}`),
        url: _url,
        browserWindow: {
          parent: targetWin?.id,
        }
      });
      if (targetWin) {
        const bounds = (targetWin as BWItem).getBounds();          // 整个窗口
        const contentBounds = (targetWin as BWItem).getContentBounds();  // 内容区域
        win.setBounds({
          x: 0,
          y: this.os.platform() === 'darwin' ? Math.max(bounds.height - contentBounds.height, 0) : 0,
          width: contentBounds.width,
          height: contentBounds.height,
        });
        // @ts-ignore
        win.setAutoResize({
          width: true,
          height: true,
        });
      }
      core.ipc.handleRenderer(callbackId, async (data) => {
        console.error(data);
        try {
          core.windowManager.close(win._id);
        } catch (err) {
          console.error(err);
        }
        resolve(data);
      });
      // win.on('close', () => {
      //   resolve(JSON.stringify([]));
      // });
    });
  }

  /**
   * 打开组织认证组件
   * @param options
   * @returns
   */
  static async openOrgAuthDrawer(options?:  MsgQuery['openOrgAuthDrawer']['query']) {
    return new Promise(async (resolve) => {
      let { webContentsId, query } = options || {};
      if (!webContentsId) {
        webContentsId = await core.ipc.getCurrentWebContentId();
      }
      const targetWin = await core.windowManager.getWindowForWebContentId(webContentsId);
      const callbackId = `open_org_auth_drawer_callback_id_${webContentsId}_${targetWin?.id}_${core.getRandomUUID(`${webContentsId}`)}`;
      const info = encodeURIComponent(JSON.stringify({
        webContentsId,
        callbackId,
        showType: options.query.showType === 'detail' ? 'detail' : 'edit',
        region: options.query.region,
        teamId: options.query.teamId,
        orgType: options.query.orgType,
      }));
      const qString = encodeURIComponent(JSON.stringify(query || {}));
      const _url = getAppUrl('', `/_jssdk/components/org-auth-drawer/index.html?data=${info}&options=${qString}`);
      const win = await core.windowManager.create({
        type: 'BV',
        name: `组织认证组件-for-${callbackId}`,
        extraData: "",
        // url: getAppUrl(`/sdk_view_select_member?data=${info}`),
        url: _url,
        browserWindow: {
          parent: targetWin?.id,
        }
      });
      if (targetWin) {
        const bounds = (targetWin as BWItem).getBounds();          // 整个窗口
        const contentBounds = (targetWin as BWItem).getContentBounds();  // 内容区域
        win.setBounds({
          x: 0,
          y: this.os.platform() === 'darwin' ? Math.max(bounds.height - contentBounds.height, 0) : 0,
          width: contentBounds.width,
          height: contentBounds.height,
        });
        // @ts-ignore
        win.setAutoResize({
          width: true,
          height: true,
        });
      }
      core.ipc.handleRenderer(callbackId, async (data) => {
        console.error(data);
        try {
          core.windowManager.close(win._id);
        } catch (err) {
          console.error(err);
        }
        resolve(data);
      });
      // win.on('close', () => {
      //   resolve(JSON.stringify([]));
      // });
    });
  }

  /**
   * 打开添加联系人组件
   * @param options
   * @returns
   */
  static async openAddContactsDialog(options?:  MsgQuery['openAddContactsDialog']['query']) {
    return new Promise(async (resolve) => {
      let { webContentsId, query } = options || {};
      if (!webContentsId) {
        webContentsId = await core.ipc.getCurrentWebContentId();
      }
      const targetWin = await core.windowManager.getWindowForWebContentId(webContentsId);
      const callbackId = `open_add_contacts_dialog_callback_id_${webContentsId}_${targetWin?.id}_${core.getRandomUUID(`${webContentsId}`)}`;
      const info = encodeURIComponent(JSON.stringify({
        webContentsId,
        callbackId,
        searchValue: options?.query?.searchValue || '',
      }));
      const qString = encodeURIComponent(JSON.stringify(query || {}));
      const _url = getAppUrl('', `/_jssdk/components/add-contacts-dialog/index.html?data=${info}&options=${qString}`);
      const win = await core.windowManager.create({
        type: 'BV',
        name: `添加联系人组件-for-${callbackId}`,
        extraData: "",
        // url: getAppUrl(`/sdk_view_select_member?data=${info}`),
        url: _url,
        browserWindow: {
          parent: targetWin?.id,
        }
      });
      if (targetWin) {
        const bounds = (targetWin as BWItem).getBounds();          // 整个窗口
        const contentBounds = (targetWin as BWItem).getContentBounds();  // 内容区域
        win.setBounds({
          x: 0,
          y: this.os.platform() === 'darwin' ? Math.max(bounds.height - contentBounds.height, 0) : 0,
          width: contentBounds.width,
          height: contentBounds.height,
        });
        // @ts-ignore
        win.setAutoResize({
          width: true,
          height: true,
        });
      }
      core.ipc.handleRenderer(callbackId, async (data) => {
        console.error(data);
        try {
          core.windowManager.close(win._id);
        } catch (err) {
          console.error(err);
        }
        resolve(data);
      });
      // win.on('close', () => {
      //   resolve(JSON.stringify([]));
      // });
    });
  }

  static async openAnnualFeeDrawer(options?:  MsgQuery['openAnnualFeeDrawer']['query']) {
    return new Promise(async (resolve) => {
      let { webContentsId, query } = options || {};
      if (!webContentsId) {
        webContentsId = await core.ipc.getCurrentWebContentId();
      }
      const targetWin = await core.windowManager.getWindowForWebContentId(webContentsId);
      const callbackId = `open_annual_fee_drawer_callback_id_${webContentsId}_${targetWin?.id}_${core.getRandomUUID(`${webContentsId}`)}`;
      const info = encodeURIComponent(JSON.stringify({
        webContentsId,
        callbackId,
        ...query,
      }));
      const qString = encodeURIComponent(JSON.stringify(query || {}));
      const _url = getAppUrl('', `/_jssdk/components/annual-fee-drawer/index.html?data=${info}&options=${qString}`);
      const win = await core.windowManager.create({
        type: 'BV',
        name: `年费组件-for-${callbackId}`,
        extraData: "",
        // url: getAppUrl(`/sdk_view_select_member?data=${info}`),
        url: _url,
        browserWindow: {
          parent: targetWin?.id,
        }
      });
      if (targetWin) {
        const bounds = (targetWin as BWItem).getBounds();          // 整个窗口
        const contentBounds = (targetWin as BWItem).getContentBounds();  // 内容区域
        win.setBounds({
          x: 0,
          y: this.os.platform() === 'darwin' ? Math.max(bounds.height - contentBounds.height, 0) : 0,
          width: contentBounds.width,
          height: contentBounds.height,
        });
        // @ts-ignore
        win.setAutoResize({
          width: true,
          height: true,
        });
      }
      core.ipc.handleRenderer(callbackId, async (data) => {
        console.error(data);
        try {
          core.windowManager.close(win._id);
        } catch (err) {
          console.error(err);
        }
        resolve(JSON.parse(data));
      });
    });
  }
  /**
   * 以独立窗口形式打开外部 HTTP 链接
   * @param url 链接
   * @param options 窗口配置选项
   * @returns 窗口实例
   */
  static async openExternalWindow(url: string, options?: ElectronWindowsManagerOptions) {
    return await core.windowManager.create({
      name: '外部链接',
      url,
      browserWindow: {
        width: 728,
        height: 676,
        autoHideMenuBar: true,
        ...options,
      },
    });
  }

  /**
   * 打开一个新窗口
   * @param {ElectronWindowsManagerOptions} options - 窗口配置选项
   * @returns {Promise<any>} 窗口实例
   */
  static async openNewWindow(options: ElectronWindowsManagerOptions) {
    return await core.windowManager.create(options);
  }

  /**
   * 打开分组设置窗口
   * @returns {Promise<any>} 窗口实例
   */
  static async openSetGroupWindow() {
    return await core.windowManager.create({
      name: '分组设置',
      url: getAppUrl('/setting/setGroup'),
      browserWindow: {
        alwaysOnTop: true,
        width: 520,
        height: 560,
        useContentSize: true,
        autoHideMenuBar: true,
        frame: false,
        show: true,
        movable: true,
        trafficLightPosition: {
          x: 8,
          y: 14,
        },
      },
    });
  }

  /**
   * 打开图片预览窗口
   * @param {PreviewImageOptions} options - 预览选项
   */
  static previewImage = (data: PreviewImageOptions | PreviewImageOptions2): void => {
    if ('images' in data || 'url' in data || 'index' in data) {
      // @ts-ignore
      const { images, index, url } = data;
      const imgList = images.length
        ? images.map((image) => ({
          url: image,
          type: 'jpg',
          imgIndex: index,
        }))
        : [{ url, type: 'jpg' }];

      this.ipcRenderer.invoke('preview-file', JSON.stringify(imgList));
    }
    if ('imgs' in data && 'imgIndex' in data) {
      const { imgs, imgIndex } = data;
      const temp = imgs.map((item) => ({
        title: item.title,
        url: item.url,
        type: item.type,
        size: item.size,
        officeId: item.officeId || null,
        imgIndex,
      }));
      this.ipcRenderer.invoke('view-img', JSON.stringify(temp));
    }
  }

  /**
   * 预览视频
   * @param {Object} data - 视频选项
   * @param {string} data.title - 视频标题
   * @param {string} data.url - 视频URL
   * @param {string} data.type - 视频类型
   * @param {number} data.size - 视频大小
   */
  static previewVideo = (data: { title: string, url: string, type: string, size: number }) => {
    this.ipcRenderer.invoke('view-video', JSON.stringify({
      title: data.title,
      url: data.url,
      type: data.type,
      size: data.size,
    }));
  }

  /**
   * 预览文件
   * @param {Object} data - 文件选项
   * @param {string} data.title - 文件标题
   * @param {string} data.url - 文件URL
   * @param {string} data.type - 文件类型
   * @param {number} data.size - 文件大小
   * @param {string} data.officeId - 文件ID
   */
  static previewFile = (data: { title: string, url: string, type: string, size: number, officeId?: string }) => {
    this.ipcRenderer.invoke("preview-file", JSON.stringify({
      title: data.title,
      url: data.url,
      type: data.type,
      size: data.size,
      officeId: data.officeId,
    }));
  }

  /**
   * 下载文件
   * @param {Object} data - 下载选项
   * @param {string} data.title - 文件标题
   * @param {string} data.url - 文件URL
   *
   */
  static downloadFile = async (data: { title: string, url: string }): Promise<string> => new Promise(async (resolve, reject) => {
    try {
      if (data.url.startsWith('base64:')) {
        const path = await this.ipcRenderer.invoke('save-base64', data.url);
        resolve(path);
      } else {
        const path = await this.ipcRenderer.invoke('download-file', {
          title: data.title,
          url: data.url,
        });
        resolve(path);
      }
    } catch (err) {
      reject(err);
    }
  })

  /**
   * 开始截图
   */
  static startCapture = () => {
    this.ipcRenderer.invoke('start-capture');
  }

  /**
   * 停止截图
   */
  static stopCapture = () => {
    this.ipcRenderer.invoke('stop-capture');
  }

  static showLoading = async (options: {
    id?: string,
    message: string,
    width: number,
  }) => {
    const win = await core.windowManager.getCurrentWindow();
    const bounds = win.getBounds();
    await this.hideLoading();
    const msgOptions = {
      message: options?.message || '加载中，请稍候...',
      width: options?.width || 200,
      height: 66,
    };
    const bvItem = await core.windowManager.create({
      name: 'global-bv-loading',
      type: 'BV',
      url: 'about:blank',
      browserWindow: {
        parent: win?.id,
      },
    });
    setTimeout(() => {
      try {
        bvItem.webContents.executeJavaScript(createMessage(msgOptions?.message));
        bvItem.setBounds({
          x: bounds.width / 2 - msgOptions.width / 2,
          y: 50,
          width: msgOptions.width,
          height: msgOptions.height,
        });
      } catch (err) {
        console.error(err);
      }
    }, 0);
  }

  static hideLoading = async () => {
    await core.windowManager.close('global-bv-loading');
  }

  static mainMenu = {
    openMessage: async () => new Promise((resolve, reject) => {
      this.ipcRenderer
        .invoke('click-menu-item', {
          url: '/main/message',
        })
        .then((res) => {
          if (res) {
            LynkerSDK.ipcRenderer.send('update-nume-index', 0);
          }
          resolve(res);
        }).catch((err) => {
          console.error(err);
          reject('err');
        });
    }),

    openSquare: async (options: any) => new Promise((resolve, reject) => {
      this.ipcRenderer
        .invoke('click-menu-item', {
          url: '/square/friend-circle',
          ...options,
          selected_path_uuid: 'square',
          click_path_uuid: 'square',
        })
        .then((res) => {
          if (res) {
            LynkerSDK.ipcRenderer.send('update-nume-index', 'square');
          }
          resolve(res);
        }).catch((err) => {
          console.error(err);
          reject('err');
        });
    }),

    openZhixing: async (options: any) => new Promise((resolve, reject) => {
      this.ipcRenderer
        .invoke('click-menu-item', {
          url: '/zhixing',
          query: 'to=todo',
          ...options,
          selected_path_uuid: 'zhixing',
          click_path_uuid: 'zhixing',
        })
        .then((res) => {
          if (res) {
            LynkerSDK.ipcRenderer.send('update-nume-index', 'zhixing');
          }
          resolve(res);
        }).catch((err) => {
          console.error(err);
          reject('err');
        });
    }),

    openBigMarket: async (options: any) => new Promise((resolve, reject) => {
      this.ipcRenderer
        .invoke('click-menu-item', {
          url: '/bigMarketIndex/home',
          ...options,
          selected_path_uuid: 'big-market',
          click_path_uuid: 'big-market',
        })
        .then((res) => {
          if (res) {
            LynkerSDK.ipcRenderer.send('update-nume-index', 'big-market');
          }
          resolve(res);
        }).catch((err) => {
          console.error(err);
          reject('err');
        });
    }),

    openWorkBench: async (options: any) => new Promise((resolve, reject) => {
      this.ipcRenderer.invoke('click-menu-item', {
        url: '/workBenchIndex/workBenchHome',
        ...options,
        selected_path_uuid: 'workBench',
        click_path_uuid: 'workBench',
      }).then((res) => {
        if (res) {
          this.ipcRenderer.send('update-nume-index', 'workBench');
        }
        resolve(res);
      }).catch((err) => {
        console.error(err);
        reject('err');
      });
    }),

    openDigitalPlatform: async (options: any) => new Promise((resolve, reject) => {
      this.ipcRenderer.invoke('click-menu-item', {
        url: '/digitalPlatformIndex/digital_platform_home',
        ...options,
        selected_path_uuid: 'digital_platform',
        click_path_uuid: 'digital_platform',
      }).then((res) => {
        if (res) {
          this.ipcRenderer.send('update-nume-index', 'digital_platform');
        }
        console.log('openDigitalPlatform =====>options', options);
        setTimeout(() => {
          if (options.teamId) {
            this.digitalPlatform.setActiveTeamId({ teamId: options.teamId });
          }
        }, 300);
        resolve(res);
      }).catch((err) => {
        console.error(err);
        reject('err');
      });
    }),

    openAddressBook: async (options: any) => new Promise((resolve, reject) => {
      this.ipcRenderer.send('update-nume-index', 'address_book');
      resolve(true);
    }),

    openActivities: async (options: any) => new Promise((resolve, reject) => {
      this.ipcRenderer.invoke('click-menu-item', {
        url: '/activity/activityList',
        ...options,
        selected_path_uuid: 'activities',
        click_path_uuid: 'activities',
      }).then((res) => {
        if (res) {
          this.ipcRenderer.send('update-nume-index', 'activities');
        }
        resolve(res);
      }).catch((err) => {
        console.error(err);
        reject('err');
      });
    }),

    openDisk: async (options: any) => new Promise((resolve, reject) => {
      this.ipcRenderer.invoke('click-menu-item', {
        url: '/clouddiskIndex/clouddiskhome',
        ...options,
        selected_path_uuid: 'disk',
        click_path_uuid: 'disk',
      }).then((res) => {
        if (res) {
          this.ipcRenderer.send('update-nume-index', 'disk');
        }
        resolve(res);
      }).catch((err) => {
        console.error(err);
        reject('err');
      });
    }),
  }

  static message = {
    checkIsInited: async (): Promise<boolean> => {
      const tryTimes = 3;
      const timeout = 1000;
      for (let i = 0; i < tryTimes; i++) {
        try {
          await Promise.race([
            this.ipc.invokeRenderer('message-is-inited'),
            new Promise((_, reject) => setTimeout(() => reject(new Error('timeout')), timeout))
          ]);
          return true;
        } catch (err) {
          if (i === tryTimes - 1) return false;
        }
      }
      return false;
    },

    openChat: async (options: {
      // 自己的身份卡
      main: string,
      // 单聊对方的身份卡
      peer?: string,
      // 群id
      group?: string,
      // 助手id
      assistant?: string,
      // 扩展信息
      extInfo?: {
        // 来源
        type: 'service_content',
        // 内容
        content: any
      },
      rela?: string,
    }) => new Promise(async (resolve, reject) => {
      await this.mainMenu.openMessage();
      await this.ipc.invokeRenderer('message-open-chat', options);
      resolve(true);
    }),

    openMessage: async (options: {
      messageId: string,
      type: 'GROUP' | 'SINGLE',
      cardId: string,
      fromId: string,
    }) => new Promise(async (resolve, reject) => {
      await this.mainMenu.openMessage();
      await this.ipc.invokeRenderer('message-open-message', options);
      resolve(true);
    }),

    openDrawerForWebview: async (options: {
      title: string,
      id: string,
      url: string,
      teamId?: string,
    }, openMessage = true) => {
      if (!options.id) {
        throw new Error('id');
      }
      if (openMessage) {
        await this.mainMenu.openMessage();
      }
      const id = options.id || this.getRandomUUID();
      core.ipc.invokeRenderer('message-open-drawer-for-webview', {
        id,
        title: options.title,
        url: options.url,
        teamId: options?.teamId,
      });
    },

    closeDrawerForWebview: async (options: {
      id: string,
    }) => {
      core.ipc.invokeRenderer('message-close-drawer-for-webview', options);
    },
  }

  static square = {
    checkIsInited: async (tryTimes = 3, timeout = 1000): Promise<boolean> => {
      for (let i = 0; i < tryTimes; i++) {
        try {
          await Promise.race([
            this.ipc.invokeRenderer('square-is-inited'),
            new Promise((_, reject) => setTimeout(() => reject(new Error('timeout')), timeout))
          ]);
          return true;
        } catch (err) {
          if (i === tryTimes - 1) return false;
        }
      }
      return false;
    },
    reload: async () => {
      return new Promise(async (resolve, reject) => {
        const isInited = await this.square.checkIsInited();
        if (!isInited) {
          return resolve(false);
        }
        this.ipc.invokeRenderer('dsquare-reload').then((res) => {
          resolve(res);
        }).catch((err) => {
          reject(err);
        });
      });
    },
    getActiveTeamId: async () => {
      const isInited = await this.square.checkIsInited();
      if (!isInited) {
        return undefined;
      }
      return this.ipc.invokeRenderer('square-get-active-team-id');
    },
    setActiveTeamId: async (query: { teamId: string }) => {
      const isInited = await this.square.checkIsInited();
      if (!isInited) {
        return false;
      }
      return this.ipc.invokeRenderer('square-set-active-team-id', query);
    },
    getTabList: async () => {
      return new Promise(async (resolve, reject) => {
        const isInited = await this.square.checkIsInited();
        if (!isInited) {
          return resolve([]);
        }
        this.ipc.invokeRenderer('get-square-tab-list').then((res) => {
          resolve(res);
        }).catch((err) => {
          reject(err);
        });
      });
    },
    openTab: async (options: MsgQuery['square_openTab']['query']['options'], openDigitalPlatform?:  MsgQuery['digitalPlatform_openTab']['query']['openDigitalPlatform']) => {
      try {
        if (openDigitalPlatform) {
          // @ts-ignore
          if (window.__ELECTRON_WINDOW_MANAGER_NAME__ !== 'square') {
            await this.mainMenu.openSquare({});
            const isInited = await this.square.checkIsInited();
            if (!isInited) {
              // 等待2秒，确保数智工场标签页已打开(后面需要优化，确保bv打包，页面正常己经打开)
              await new Promise((resolve) => setTimeout(resolve, 1000));
            }
          }
        }
        if (options.teamId) {
          const res = await this.square.setActiveTeamId({ teamId: options.teamId });
          if (res === true) {
            await new Promise((resolve) => setTimeout(resolve, 1000));
          }
        }
        console.log('=====>options', options);
        const updateKey = options?.path_uuid || md5(JSON.stringify(options));

        await this.ipc.invokeRenderer('open-square-tab-item', {
          ...options,
          query: {
            ...options.query,
            isClose: true,
          },
          path_uuid: updateKey,
        });
        return updateKey;
      } catch (error) {
        console.error(error);
      }
    },
    closeTab: async (query: { path_uuid: string }) => {
      return this.ipc.invokeRenderer('close-square-tab-item', query);
    },
    openTabForWebview: async (options: {
      title: string,
      path_uuid?: string,
      url?: string,
      teamId?: string,
      icon?: string,
      activeIcon?: string,
      query?: Record<string, string>,
      beforeCloseOptions?: {
        title?: string,
        content?: string,
      },
      beforeRefreshOptions?: {
        title?: string,
        content?: string,
      }
    }) => {
      if (!options.url) {
        throw new Error('url');
      }
      const id = options.path_uuid || this.getRandomUUID();
      return await this.square.openTab({
        fullPath: '/square/square_webview/' + id,
        label: options.title,
        icon: options.icon || 'workshop',
        activeIcon: options.activeIcon || 'workshop',
        path_uuid: options?.path_uuid,
        teamId: options.teamId,
        query: {
          url: options.url,
          teamId: options.teamId,
          ...options.query,
        },
        beforeCloseOptions: options.beforeCloseOptions,
        beforeRefreshOptions: options.beforeRefreshOptions,
      }, true);
    },
    updateTab: async (options: {
      path_uuid: string,
      title: string,
      beforeCloseOptions?: {
        title?: string,
        content?: string,
      },
      beforeRefreshOptions?: {
        title?: string,
        content?: string,
      }
    }) => {
      return this.ipc.invokeRenderer('update-square-tab-item', {
        path_uuid: options.path_uuid,
        label: options.title,
        beforeCloseOptions: options.beforeCloseOptions,
        beforeRefreshOptions: options.beforeRefreshOptions,
      });
    },
  }

  static zhixing = {
    checkIsInited: async (tryTimes = 3, timeout = 1000): Promise<boolean> => {
      for (let i = 0; i < tryTimes; i++) {
        try {
          await Promise.race([
            this.ipc.invokeRenderer('zhixing-is-inited'),
            new Promise((_, reject) => setTimeout(() => reject(new Error('timeout')), timeout))
          ]);
          return true;
        } catch (err) {
          if (i === tryTimes - 1) return false;
        }
      }
      return false;
    },
  }

  static bigMarket = {
    checkIsInited: async (tryTimes = 3, timeout = 1000): Promise<boolean> => {
      for (let i = 0; i < tryTimes; i++) {
        try {
          await Promise.race([
            this.ipc.invokeRenderer('big-market-is-inited'),
            new Promise((_, reject) => setTimeout(() => reject(new Error('timeout')), timeout))
          ]);
          return true;
        } catch (err) {
          if (i === tryTimes - 1) return false;
        }
      }
      return false;
    },
  }

  /**
   * 数智工场
   */
  static workBench = {
    /**
     * 获取数智工场当前组织团队ID
     * @returns {string} 团队ID
     */
    getActiveTeamId: () => localStorage.getItem('workBenchTeamid'),

    checkIsInited: async (tryTimes = 3, timeout = 1000): Promise<boolean> => {
      for (let i = 0; i < tryTimes; i++) {
        try {
          await Promise.race([
            this.ipc.invokeRenderer('work-bench-is-inited'),
            new Promise((_, reject) => setTimeout(() => reject(new Error('timeout')), timeout))
          ]);
          return true;
        } catch (err) {
          if (i === tryTimes - 1) return false;
        }
      }
      return false;
    },

    reload: async () => {
      return new Promise(async (resolve, reject) => {
        const isInited = await this.workBench.checkIsInited();
        if (!isInited) {
          return resolve(false);
        }
        this.ipc.invokeRenderer('work-bench-reload').then((res) => {
          resolve(res);
        }).catch((err) => {
          reject(err);
        });
      });
    },

    getTabList: async () => {
      return new Promise(async (resolve, reject) => {
        const isInited = await this.workBench.checkIsInited();
        if (!isInited) {
          return resolve([]);
        }
        this.ipc.invokeRenderer('get-work-bench-tab-list').then((res) => {
          resolve(res);
        }).catch((err) => {
          reject(err);
        });
      });
    },

    /**
     * 打开数智工场标签页 (临时只有在数字平台BV中使用)
     * @param {DigitalPlatformRouteItem} options - 标签页数据
     * @param {boolean} openWorkBench - 是否尝试打开数智工场BV
     */
    openTab: async (options: {
      fullPath: string,
      title: string,
      icon: string,
      teamId?: string,
      activeIcon?: string,
      pathName?: string,
      path_uuid?: string,
      type?: ClientSide,
      query?: Record<string, string>,
      activationGroupItemTeamId?: string,
      toAdmin?: boolean,
      beforeCloseOptions?: {
        title?: string,
        content?: string,
      },
      beforeRefreshOptions?: {
        title?: string,
        content?: string,
      }
    }, openWorkBench?: boolean) => {
      try {
        if (openWorkBench) {
          // @ts-ignore
          if (window.__ELECTRON_WINDOW_MANAGER_NAME__ !== 'workBench') {
            await this.mainMenu.openWorkBench({
              ...(options?.teamId ? {
                teamId: options?.teamId,
              } : {}),
            });
            const isInited = await this.workBench.checkIsInited();
            if (!isInited) {
              // 等待2秒，确保数智工场标签页已打开(后面需要优化，确保bv打包，页面正常己经打开)
              await new Promise((resolve) => setTimeout(resolve, 1000));
            }
          }
        }
        // options = {
        //   icon: "government",
        //   title: 'ssss',
        //   pathName: 'work_bench_iframe',
        //   fullPath: "/workBenchIndex/work_bench_iframe",
        //   query: {
        //     url: encodeURIComponent('https://baidu.com'),
        //   },
        // }
        console.log('=====>options', options);
        const updateKey = options?.path_uuid || md5(JSON.stringify(options));
        await this.ipc.invokeRenderer('open-work-bench-tab-item', {
          name: options?.pathName || options?.fullPath,
          path: options?.fullPath || '/workBenchIndex/merchant-settlement-apply',
          path_uuid: updateKey,
          title: options?.title || '',
          icon: options?.icon || '',
          activeIcon: options?.activeIcon || '',
          updateKey: options?.fullPath || 'merchant-settlement-apply',
          type: options?.type || ClientSide.BENCH,
          query: options?.query || {},
          gopath: true,
          beforeCloseOptions: options?.beforeCloseOptions,
          beforeRefreshOptions: options?.beforeRefreshOptions,
        });
        return updateKey;
      } catch (error) {
        console.error(error);
      }
    },

    /**
     * 关闭数智工场标签页
     * @param path_uuid 标签页path_uuid
     * @returns
     */
    closeTab: async (query: { path_uuid: string }) => {
      return this.ipc.invokeRenderer('close-work-bench-tab-item', query);
    },

    updateTab: async (query: { path_uuid: string, title?: string, icon?: string, activeIcon?: string,
      beforeCloseOptions?: {
        title?: string,
        content?: string,
      },
      beforeRefreshOptions?: {
        title?: string,
        content?: string,
      }
    }) => {
      return this.ipc.invokeRenderer('update-work-bench-tab-item', query);
    },

    /**
     * 打开数智工场iframe标签页
     * @param options
     * @param options.title - 标题
     * @param options.url - 链接
     * @param options.teamId - 团队ID
     * @param options.query - 查询参数
     */
    openTabForIframe: async (options: {
      title: string,
      path_uuid?: string,
      url?: string,
      teamId?: string,
      query?: Record<string, string>,
      beforeCloseOptions?: {
        title?: string,
        content?: string,
      },
      beforeRefreshOptions?: {
        title?: string,
        content?: string,
      }
    }) => {
      if (!options.url) {
        throw new Error('url');
      }
      const id = md5(`${options.url}`);
      return await this.workBench.openTab({
        fullPath: `/workBenchIndex/work_bench_iframe/${id}`,
        title: options.title,
        icon: 'workshop',
        activeIcon: 'workshop',
        pathName: 'work_bench_iframe',
        teamId: options?.teamId,
        path_uuid: options?.path_uuid,
        query: {
          url: options.url,
          teamId: options.teamId,
          ...options.query,
        },
        beforeCloseOptions: options.beforeCloseOptions,
        beforeRefreshOptions: options.beforeRefreshOptions,
      }, true);
    },

    /**
     * 打开数智工场webview标签页
     * @param options
     * @param options.title - 标题
     * @param options.url - 链接
     * @param options.teamId - 团队ID
     * @param options.query - 查询参数
     */
    openTabForWebview: async (options: {
      title: string,
      path_uuid?: string,
      icon?: string,
      activeIcon?: string,
      type?: ClientSide,
      url?: string,
      teamId?: string,
      query?: Record<string, string>,
      beforeCloseOptions?: {
        title?: string,
        content?: string,
      },
      beforeRefreshOptions?: {
        title?: string,
        content?: string,
      }
    }) => {
      if (!options.url) {
        throw new Error('url');
      }
      const id = options.path_uuid || this.getRandomUUID();
      return await this.workBench.openTab({
        fullPath: `/workBenchIndex/work_bench_webview/${id}`,
        title: options.title,
        icon: options.icon || 'workshop',
        activeIcon: options.activeIcon || 'workshop',
        type: options.type || ClientSide.BENCH,
        pathName: 'work_bench_webview',
        teamId: options?.teamId,
        path_uuid: options?.path_uuid,
        query: {
          url: options.url,
          teamId: options.teamId,
          ...options.query,
        },
        beforeCloseOptions: options.beforeCloseOptions,
        beforeRefreshOptions: options.beforeRefreshOptions,
      }, true);
    },

    openDrawerForWebview: async (options: {
      title: string,
      id: string,
      url: string,
      teamId?: string,
    }, openWorkBench = true) => {
      if (!options.id) {
        throw new Error('id');
      }
      if (openWorkBench) {
        // @ts-ignore
        if (window.__ELECTRON_WINDOW_MANAGER_NAME__ !== 'workBench') {
          await this.mainMenu.openWorkBench({
            ...(options?.teamId ? {
              teamId: options?.teamId,
            } : {}),
          });
          const isInited = await this.workBench.checkIsInited();
          if (!isInited) {
            // 等待2秒，确保数智工场标签页已打开(后面需要优化，确保bv打包，页面正常己经打开)
            await new Promise((resolve) => setTimeout(resolve, 1000));
          }
        }
      }
      const id = options.id || this.getRandomUUID();
      core.ipc.invokeRenderer('work-bench-open-drawer-for-webview', {
        id,
        title: options.title,
        url: options.url,
        teamId: options?.teamId,
      });
      return id;
    },

    closeDrawerForWebview: async (options: {
      id: string,
    }) => {
      core.ipc.invokeRenderer('work-bench-close-drawer-for-webview', options);
    },

    /**
     * 打开数智工场企业管理页面
     * @param {Record<string, string>} query - 查询参数
     * query.menuId 菜单ID 1. 文化墙; 2. 宣传广告; 3. 模块设置; 4. 管理员设置; 5. 入网分账; 6. 提现管理; 7. 收款订单; 8. 退款/售后; 9. 结算资金; 10. 结算订单
     */
    openTabForEnterprise: async (query?: Record<string, string>) => await this.workBench.openTab({
      fullPath: '/workBenchIndex/workBenchEnterprise',
      title: i18nt('banch.glht'),
      icon: 'workshop',
      activeIcon: 'workshop',
      pathName: 'workshop',
      teamId: query?.teamId,
      query,
    }, true),

    /**
     * 打开商户入网页面
     * @param {Record<string, string>} query - 查询参数
     */
    openTabForMerchantApply: async (query?: Record<string, string>) => await this.workBench.openTab({
      fullPath: '/workBenchIndex/merchant-settlement-apply',
      title: '商户入网',
      icon: 'workshop',
      activeIcon: 'workshop',
      type: ClientSide.SHOP,
      query,
    }, true),

    /**
     * 打开数智工场收款详情页面
     * @param {Object} options - 收款详情页面选项
     * @param {string} options.title - 收款详情页面标题
     * @param {string} options.url - 收款详情页面URL(如果有完整url可传，否则传teamId和sn)
     * @param {string} options.teamId - 团队ID
     * @param {string} options.sn - 收款单号
     */
    openTabForReceiveDetail: async (options: {
      title: string,
      url?: string,
      teamId?: string,
      sn?: string,
      query?: Record<string, string>,
    }) => {
      if (!options.url && !options.teamId && !options.sn) {
        throw new Error('url and teamId and sn are required');
      }
      if (!options.url) {
        const env = this.config.env;
        const token = this.config.token;
        const teamId = options.teamId || this.workBench.getActiveTeamId();
        const userInfo = this.getUserInfo();
        options.url = this.getH5Url(`/common/index.html#/organize_payments/details?type=receive&sn=${options.sn}&env=${env}&token=${token}&teamId=${teamId}&openId=${userInfo.openid}`);
      }
      const id = md5(`${options.url}`);
      return await this.workBench.openTab({
        fullPath: `/workBenchIndex/work_bench_iframe/${id}`,
        title: options.title,
        icon: 'workshop',
        activeIcon: 'workshop',
        pathName: 'work_bench_iframe',
        teamId: options?.teamId,
        query: {
          url: options.url,
          teamId: options.teamId,
          ...options.query,
        },
      }, true);
    },
    /**
     * 打开数智工场退款详情页面
     * @param {Object} options - 退款详情页面选项
     * @param {string} options.title - 退款详情页面标题
     * @param {string} options.url - 退款详情页面URL(如果有完整url可传，否则传teamId和sn)
     * @param {string} options.teamId - 团队ID
     * @param {string} options.sn - 退款单号
     */
    openTabForRefundDetail: async (options: {
      title: string,
      url?: string,
      teamId?: string,
      sn?: string,
      query?: Record<string, string>,
      openLeftMenu?: boolean,
    } = {
      title: '退款详情',
      openLeftMenu: true,
    }) => {
      if (!options.url && !options.teamId && !options.sn) {
        throw new Error('url and teamId and sn are required');
      }
      if (!options.url) {
        const env = this.config.env;
        const token = this.config.token;
        const teamId = options.teamId || this.workBench.getActiveTeamId();
        const userInfo = this.getUserInfo();
        options.url = this.getH5Url(`/common/index.html#/organize_payments/details?type=refund&sn=${options.sn}&env=${env}&token=${token}&teamId=${teamId}&openId=${userInfo.openid}`);
      }
      const id = md5(`${options.url}`);
      return await this.workBench.openTab({
        fullPath: `/workBenchIndex/work_bench_iframe/${id}`,
        title: options.title,
        icon: 'workshop',
        activeIcon: 'workshop',
        pathName: 'work_bench_iframe',
        teamId: options?.teamId,
        query: {
          url: options.url,
          ...options.query,
        },
      }, options.openLeftMenu);
    },
    /**
     * 打开数智工场结算详情页面
     * @param {Object} options - 结算详情页面选项
     * @param {string} options.title - 结算详情页面标题
     * @param {string} options.url - 结算详情页面URL(如果有完整url可传，否则传teamId和sn)
     * @param {string} options.teamId - 团队ID
     * @param {string} options.sn - 结算单号
     */
    openTabForSettlementDetail: async (options: {
      title: string,
      url?: string,
      teamId?: string,
      sn?: string,
    }) => {
      if (!options.url && !options.teamId && !options.sn) {
        throw new Error('url and teamId and sn are required');
      }
      if (!options.url) {
        const env = this.config.env;
        const token = this.config.token;
        const teamId = options.teamId || this.workBench.getActiveTeamId();
        const userInfo = this.getUserInfo();
        options.url = this.getH5Url(`/common/index.html#/settlement_order/details?type=receive&sn=${options.sn}&env=${env}&token=${token}&teamId=${teamId}&openId=${userInfo.openid}`);
      }
      const id = md5(`${options.url}`);
      return await this.workBench.openTab({
        fullPath: `/workBenchIndex/work_bench_iframe/${id}`,
        title: options.title,
        icon: 'workshop',
        activeIcon: 'workshop',
        pathName: 'work_bench_iframe',
        teamId: options?.teamId,
        query: {
          url: options.url,
        },
      }, true);
    },

    // 打开组织认证
    openTeamCertification: async (options?: {
      teamId?: string,
    }) => {
      const { teamId } = options || {};
      const _id = teamId || this.workBench.getActiveTeamId();
      await this.workBench.openTab({
        fullPath: '/workBenchIndex/teamSting',
        path_uuid: 'workBench-openTeamCertification',
        title: i18nt("banch.zzsz"),
        icon: 'workshop',
        activeIcon: 'workshop',
        pathName: 'workshop',
        teamId: _id,
        query: {
          activationGroupItemTeamId: _id,
          // @ts-ignore
          toAdmin: true,
          teamId: _id,
          openTeamCertificationTeamId: `${_id}`,
          openTeamCertification: `${Date.now()}`,
        },
      }, true);
      setTimeout(() => {
        // this.workBench.reload();
      }, 1000);
    },
  }

  static digitalPlatform = {
    checkIsInited: async (tryTimes = 3, timeout = 1000): Promise<boolean> => {
      for (let i = 0; i < tryTimes; i++) {
        try {
          await Promise.race([
            this.ipc.invokeRenderer('digital-platform-is-inited'),
            new Promise((_, reject) => setTimeout(() => reject(new Error('timeout')), timeout))
          ]);
          return true;
        } catch (err) {
          if (i === tryTimes - 1) return false;
        }
      }
      return false;
    },

    reload: async () => {
      return new Promise(async (resolve, reject) => {
        const isInited = await this.digitalPlatform.checkIsInited();
        if (!isInited) {
          return resolve(false);
        }
        this.ipc.invokeRenderer('digital-platform-reload').then((res) => {
          resolve(res);
        }).catch((err) => {
          reject(err);
        });
      });
    },
    getActiveTeamId: async () => {
      const isInited = await this.digitalPlatform.checkIsInited();
      if (!isInited) {
        return undefined;
      }
      return this.ipc.invokeRenderer('digital-platform-get-active-team-id');
    },
    setActiveTeamId: async (query: { teamId: string }) => {
      const isInited = await this.digitalPlatform.checkIsInited();
      if (!isInited) {
        return false;
      }
      return this.ipc.invokeRenderer('digital-platform-set-active-team-id', query);
    },
    getTabList: async () => {
      return new Promise(async (resolve, reject) => {
        const isInited = await this.digitalPlatform.checkIsInited();
        if (!isInited) {
          return resolve([]);
        }
        this.ipc.invokeRenderer('get-digital-platform-tab-list').then((res) => {
          resolve(res);
        }).catch((err) => {
          reject(err);
        });
      });
    },
    openTab: async (options: MsgQuery['digitalPlatform_openTab']['query']['options'], openDigitalPlatform?:  MsgQuery['digitalPlatform_openTab']['query']['openDigitalPlatform']) => {
      try {
        if (openDigitalPlatform) {
          // @ts-ignore
          if (window.__ELECTRON_WINDOW_MANAGER_NAME__ !== 'digitalPlatform') {
            await this.mainMenu.openDigitalPlatform({});
            const isInited = await this.digitalPlatform.checkIsInited();
            if (!isInited) {
              // 等待2秒，确保数智工场标签页已打开(后面需要优化，确保bv打包，页面正常己经打开)
              await new Promise((resolve) => setTimeout(resolve, 1000));
            }
          }
        }
        if (options.teamId) {
          const res = await this.digitalPlatform.setActiveTeamId({ teamId: options.teamId });
          if (res === true) {
            await new Promise((resolve) => setTimeout(resolve, 1000));
          }
        }
        console.log('=====>options', options);
        const updateKey = options?.path_uuid || md5(JSON.stringify(options));

        await this.ipc.invokeRenderer('open-digital-platform-tab-item', {
          ...options,
          query: {
            ...options.query,
            isClose: true,
          },
          path_uuid: updateKey,
        });
        return updateKey;
      } catch (error) {
        console.error(error);
      }
    },
    closeTab: async (query: { path_uuid: string }) => {
      return this.ipc.invokeRenderer('close-digital-platform-tab-item', query);
    },
    openTabForWebview: async (options: {
      title: string,
      path_uuid?: string,
      url?: string,
      teamId?: string,
      icon?: string,
      activeIcon?: string,
      query?: Record<string, string>,
      beforeCloseOptions?: {
        title?: string,
        content?: string,
      },
      beforeRefreshOptions?: {
        title?: string,
        content?: string,
      }
    }) => {
      if (!options.url) {
        throw new Error('url');
      }
      const id = options.path_uuid || this.getRandomUUID();
      return await this.digitalPlatform.openTab({
        path: '/digitalPlatformIndex/digital_platform_webview/' + id,
        fullPath: '/digitalPlatformIndex/digital_platform_webview/' + id,
        title: options.title,
        icon: options.icon || 'workshop',
        activeIcon: options.activeIcon || 'workshop',
        name: 'digital_platform_webview',
        path_uuid: options?.path_uuid,
        affix: false,
        teamId: options.teamId,
        query: {
          url: options.url,
          teamId: options.teamId,
          ...options.query,
        },
        beforeCloseOptions: options.beforeCloseOptions,
        beforeRefreshOptions: options.beforeRefreshOptions,
      }, true);
    },
    updateTab: async (options: {
      path_uuid: string,
      title: string,
      beforeCloseOptions?: {
        title?: string,
        content?: string,
      },
      beforeRefreshOptions?: {
        title?: string,
        content?: string,
      }
    }) => {
      return this.ipc.invokeRenderer('update-digital-platform-tab-item', options);
    },
  }

  static addressBook = {
    checkIsInited: async (): Promise<boolean> => new Promise((resolve) => {
      resolve(true);
    }),
  }

  static activities = {
    checkIsInited: async (tryTimes = 3, timeout = 1000): Promise<boolean> => {
      for (let i = 0; i < tryTimes; i++) {
        try {
          await Promise.race([
            this.ipc.invokeRenderer('activities-is-inited'),
            new Promise((_, reject) => setTimeout(() => reject(new Error('timeout')), timeout))
          ]);
          return true;
        } catch (err) {
          if (i === tryTimes - 1) return false;
        }
      }
      return false;
    },
  }

  static disk = {
    checkIsInited: async (tryTimes = 3, timeout = 1000): Promise<boolean> => {
      for (let i = 0; i < tryTimes; i++) {
        try {
          await Promise.race([
            this.ipc.invokeRenderer('disk-is-inited'),
            new Promise((_, reject) => setTimeout(() => reject(new Error('timeout')), timeout))
          ]);
          return true;
        } catch (err) {
          if (i === tryTimes - 1) return false;
        }
      }
      return false;
    },
  }

  /**
   * 打开政策直通车文章页面
   * @returns {Promise<any>} 窗口实例
   */
  static async openPolicyDetailWindow(params) {
    if (core.windowManager.get('直通车预览独立窗口')) {
      core.windowManager.close('直通车预览独立窗口');
    }
    return await core.windowManager.create({
      loadingView: null,
      name: '直通车预览独立窗口',
      url: getAppUrl(params.url),
      openDevTools: false,
      browserWindow: {
        titleBarStyle: 'hidden',
        width: 1296,
        minWidth: 1296,
        height: 720,
        useContentSize: true,
        autoHideMenuBar: true,
        frame: false,
        show: true,
        movable: true,
        webPreferences: {
          contextIsolation: false,
          nodeIntegration: true,
          webSecurity: false,
          scrollBounce: true,
        },
      },
    });
  }

  static async openPolicyDetailWindow2(params) {
    return await core.windowManager.create({
      loadingView: null,
      name: '政策详情',
      url: getAppUrl(params.url),
      openDevTools: false,
      browserWindow: {
        titleBarStyle: 'hidden',
        width: 1296,
        minWidth: 1296,
        height: 720,
        useContentSize: true,
        autoHideMenuBar: true,
        frame: false,
        show: true,
        movable: true,
        webPreferences: {
          contextIsolation: false,
          nodeIntegration: true,
          webSecurity: false,
          scrollBounce: true,
        },
      },
    });
  }

  static windowsTabs = {
    getTabId: (id: string) => {
      return `windows_tabs_${id}`;
    },
    /**
     * 检查指定 tab 是否初始化完成，支持重试和超时控制
     * @param id tab id
     * @param tryTimes 重试次数，默认3次
     * @param timeout 每次超时时间，默认1000ms
     */
    checkIsInited: async (id: string, tryTimes = 3, timeout = 1000): Promise<boolean> => {
      const tab_id = this.windowsTabs.getTabId(id);
      for (let i = 0; i < tryTimes; i++) {
        try {
          await Promise.race([
            this.ipc.invokeRenderer(`${tab_id}-is-inited`),
            new Promise((_, reject) => setTimeout(() => reject(new Error('timeout')), timeout))
          ]);
          return true;
        } catch (err) {
          if (i === tryTimes - 1) return false;
          // 否则自动重试
        }
      }
      return false;
    },
    createWindow: async (options: {
      id?: string,
      title?: string,
      isOnlyOneTabHideTabs?: boolean,
    }) => {
      const { title } = options;
      const id = options.id || core.getRandomUUID();
      if (!id) {
        console.error('id is required');
        return;
      }
      const tabs_id = this.windowsTabs.getTabId(id);
      let win = (await core.windowManager.get(tabs_id)) as BWItem;
      if (win) {
        win.focus();
        win?.restore?.();
        this.ipc.invokeRenderer(`${tabs_id}-window-update`, lodash.pickBy({
          ...options,
          id: undefined,
        }, value => value !== null && value !== undefined && value !== ''))
        return id;
      }
      win = await core.windowManager.create({
        name: tabs_id,
        url: getAppUrl('', `/_jssdk/components/windows-tabs/index.html?data=${encodeURIComponent(JSON.stringify({
          tabs_title: title,
          tabs_id: id,
          isOnlyOneTabHideTabs: !!options.isOnlyOneTabHideTabs,
        }))}`),
        openDevTools: false,
        extraData: JSON.stringify({
          tabs_title: title,
          tabs_id: id,
        }),
        browserWindow: {
          titleBarStyle: "hidden",
          width: 1296,
          minWidth: 1296,
          height: 720,
          useContentSize: true,
          autoHideMenuBar: true,
          frame: false,
          show: true,
          movable: true,
          webPreferences: {
            contextIsolation: false,
            nodeIntegration: true,
            webSecurity: false,
            scrollBounce: true,
          },
        },
      });
      return id;
    },

    closeWindow: async (id: string) => {
      if (!id) {
        console.error('id is required');
        return;
      }
      const tabs_id = this.windowsTabs.getTabId(id);
      let win = (await core.windowManager.get(tabs_id)) as BWItem;
      if (win) {
        win.close();
      }
      return id;
    },

    openTab: async (query: MsgQuery['windowsTabs_openTab']['query']) => {
      const { options, tabsTitle } = query;
      const tabsId = query.tabsId || core.getRandomUUID();
      const id = options?.id || core.getRandomUUID();
      if (!tabsId) {
        console.error('tabsId is required');
        return;
      }
      if (!id) {
        console.error('id is required');
        return;
      }
      console.log('=====>openTab query', query);
      // 广场打开标签
      if (query.tabsId === 'square') {
        const _options = lodash.pickBy({
          title: query.options?.title,
          icon: query.options?.icon,
          activeIcon: query.options?.activeIcon,
          path_uuid: query.options?.id,
          url: query.options?.url,
          query: query.options?.query,
          beforeCloseOptions: query.options?.beforeCloseOptions,
          beforeRefreshOptions: query.options?.beforeRefreshOptions,
        }, value => value !== null && value !== undefined && value !== '');
        (await this.square.openTabForWebview(_options as any)) as any;
        return {
          tabsId,
          id: id,
        }
      }
      // 数字平台打开标签
      if (query.tabsId === 'digitalPlatform') {
        const _options = lodash.pickBy({
          title: query.options?.title,
          icon: query.options?.icon,
          activeIcon: query.options?.activeIcon,
          path_uuid: query.options?.id,
          url: query.options?.url,
          query: query.options?.query,
          beforeCloseOptions: query.options?.beforeCloseOptions,
          beforeRefreshOptions: query.options?.beforeRefreshOptions,
        }, value => value !== null && value !== undefined && value !== '');
        await this.digitalPlatform.openTabForWebview(_options as any);
        return {
          tabsId,
          id: id,
        }
      }
      // 工作台打开标签
      if (query.tabsId === 'workBench') {
        const _options = lodash.pickBy({
          title: query.options?.title,
          icon: query.options?.icon,
          activeIcon: query.options?.activeIcon,
          path_uuid: query.options?.id,
          url: query.options?.url,
          query: query.options?.query,
          beforeCloseOptions: query.options?.beforeCloseOptions,
          beforeRefreshOptions: query.options?.beforeRefreshOptions,
        }, value => value !== null && value !== undefined && value !== '');
        (await this.workBench.openTabForWebview(_options as any)) as any;
        return {
          tabsId,
          id: id,
        }
      }
      const tabs_id = this.windowsTabs.getTabId(tabsId);
      await this.windowsTabs.createWindow({
        id: tabsId,
        title: tabsTitle,
        isOnlyOneTabHideTabs: !!query.isOnlyOneTabHideTabs,
      });
      const isOk = await this.windowsTabs.checkIsInited(tabsId);
      if (isOk) {
        this.ipc.invokeRenderer(`${tabs_id}-open`, lodash.pickBy({
          ...options,
          id: id,
          title: options.title,
          icon: options.icon,
          activeIcon: options.activeIcon,
          url: options.url,
          beforeCloseOptions: options.beforeCloseOptions,
          beforeRefreshOptions: options.beforeRefreshOptions,
          hideCloseButton: options?.hideCloseButton,
        }, value => value !== null && value !== undefined && value !== ''))
      }
      return {
        tabsId,
        id: id,
      }
    },

    closeTab: async (query?: MsgQuery['windowsTabs_closeTab']['query']) => {
      const { id, tabsId } = query || {};
      if (!tabsId) {
        throw new Error('tabsId is required');
      }
      if (!id) {
        throw new Error('id is required');
      }
      console.log('=====>closeTab query', query);
       // 广场关闭标签
      if (tabsId === 'square') {
        await this.square.closeTab({
          path_uuid: id,
        }) as any;
        return {
          tabsId,
          id,
        }
      }
      // 数字平台关闭标签
      if (tabsId === 'digitalPlatform') {
        await this.digitalPlatform.closeTab({
          path_uuid: id,
        }) as any;
        return {
          tabsId,
          id,
        }
      }
      // 数字工场关闭标签
      if (tabsId === 'workBench') {
        await this.workBench.closeTab({
          path_uuid: id,
        }) as any;
        return {
          tabsId,
          id,
        }
      }
      const tab_id = this.windowsTabs.getTabId(tabsId);
      let win = (await core.windowManager.get(tab_id)) as BWItem;

      if (win) {
        const isOk = await this.windowsTabs.checkIsInited(tabsId);
        if (isOk) {
          this.ipc.invokeRenderer(`${tab_id}-close`, {
            id: id,
          })
        }
      }
      return {
        tabsId,
        id,
      }
    },
  }
}

// 注册全局SDK实例
if (!window.LynkerSDK) {
  window.LynkerSDK = LynkerSDK;
}

export default LynkerSDK;

// 初始化函数
(async function init() {
  try {
    const isMain = await checkIsMainWindow();
    if (isMain) {
      // fix 截图未正确关闭
      LynkerSDK.stopCapture();
      // 监听启动参数变化
      core.onLaunchOptions((options) => {
        LynkerSDK.config = {
          launchOptions: `${options}`,
        };
      });

      core.log('log', '注册主窗口事件监听');

      // 处理窗口间通信
      const handle = async (type: keyof MsgQuery, query: any) => {
        switch (type) {
          case <const>'getConfig':
            return LynkerSDK.config;
          case <const>'getLocalStorage':
            return LynkerSDK.getLocalStorage(query as string);
          case <const>'setLocalStorage':
            const q = query as MsgQuery['setLocalStorage']['query'];
            return LynkerSDK.setLocalStorage(q?.key, q.value);
          case <const>'getLaunchOptions':
            return LynkerSDK.getLaunchOptions();
          case <const>'delLaunchOptions':
            return LynkerSDK.delLaunchOptions();
          case <const>'openWebWindow':
            const webQuery = query as MsgQuery['openWebWindow']['query'];
            return LynkerSDK.openWebWindow(webQuery.name, webQuery.url, webQuery.extraData, webQuery.openDevTools);
          case <const>'openBrowser':
            return LynkerSDK.openBrowser(query as string);
          case <const>'openMyHelpWindow':
            const helpQuery = query as MsgQuery['openMyHelpWindow']['query'];
            const helpWin = await LynkerSDK.openMyHelpWindow(helpQuery);
            return helpWin.id;
          case <const>'openMyOrderWindow':
            const orderQuery = query as MsgQuery['openMyOrderWindow']['query'];
            const orderWin = await LynkerSDK.openMyOrderWindow(orderQuery);
            return orderWin.id;
          case <const>'openMyInvoiceWindow':
            const invoiceQuery = query as MsgQuery['openMyInvoiceWindow']['query'];
            const invoiceWin = await LynkerSDK.openMyInvoiceWindow(invoiceQuery);
            return invoiceWin.id;
          case <const>'openSelectMember':
            const memberQuery = query as MsgQuery['openSelectMember']['query'];
            return await LynkerSDK.openSelectMember(memberQuery);
          case <'openJoinDigitalPlatformDrawer'>"openJoinDigitalPlatformDrawer":
            return await LynkerSDK.openJoinDigitalPlatformDrawer(query);
          case <'openOrgAuthDrawer'>"openOrgAuthDrawer":
            return await LynkerSDK.openOrgAuthDrawer(query);
          case <'openNewWindow'>"openNewWindow":
            const windowQuery = query as MsgQuery['openNewWindow']['query'];
            const newWin = await LynkerSDK.openNewWindow(windowQuery);
            return newWin.id;
          case <const>'getUserInfo':
            return LynkerSDK.getUserInfo();
          case <const>'getUniqueId':
            return LynkerSDK.getUniqueId();
          case <const>'openExternalApp':
            return LynkerSDK.openExternalApp(query as MsgQuery['openExternalApp']['query']);
          case <const>'setEnv':
            return LynkerSDK.setEnv(query as LynkerSDKConfig['env'] | 'RESET');
          case <const>'checkIsManualEnv':
            return LynkerSDK.checkIsManualEnv();
          case <const>'getCurrentWindow':
            return LynkerSDK.getCurrentWindow();
          case <const>'openExternalWindow':
            return LynkerSDK.openExternalWindow((query as any).url, (query as any).options);
          case <const>'openSettingWindow':
            return LynkerSDK.openSettingWindow(query as string);
          case <const>'openSetGroupWindow':
            return LynkerSDK.openSetGroupWindow();
          case <const>'openMyAddressWindow':
            return LynkerSDK.openMyAddressWindow(query as string);
          case <const>'openPolicyDetailWindow':
            return LynkerSDK.openPolicyDetailWindow(query);
          case <const>'openPolicyDetailWindow2':
            return LynkerSDK.openPolicyDetailWindow2(query);
          case <'openAddContactsDialog'>"openAddContactsDialog":
            return LynkerSDK.openAddContactsDialog(query);
          case <'openAnnualFeeDrawer'>"openAnnualFeeDrawer":
            return LynkerSDK.openAnnualFeeDrawer(query);
          case <'logout'>"logout":
            return LynkerSDK.logout();
          case <const>'openDebugTools':
            return LynkerSDK.openDebugTools();
          case <'previewImage'>"previewImage":
            return LynkerSDK.previewImage(query);
          case <const>'previewVideo':
            return LynkerSDK.previewVideo(query);
          case <const>'previewFile':
            return LynkerSDK.previewFile(query);
          case <const>'downloadFile':
            return LynkerSDK.downloadFile(query);
          case <const>'startCapture':
            return LynkerSDK.startCapture();
          case <const>'stopCapture':
            return LynkerSDK.stopCapture();
          case <const>'showLoading':
            return LynkerSDK.showLoading(query);
          case <const>'hideLoading':
            return LynkerSDK.hideLoading();
          case <const>'mainMenu_openMessage':
            return LynkerSDK.mainMenu.openMessage();
          case <const>'mainMenu_openWorkBench':
            return LynkerSDK.mainMenu.openWorkBench(query);
          case <const>'mainMenu_openSquare':
            return LynkerSDK.mainMenu.openSquare(query);
          case <const>'mainMenu_openDigitalPlatform':
            return LynkerSDK.mainMenu.openDigitalPlatform(query);
          case <const>'mainMenu_openAddressBook':
            return LynkerSDK.mainMenu.openAddressBook(query);
          case <const>'mainMenu_openActivities':
            return LynkerSDK.mainMenu.openActivities(query);
          case <const>'mainMenu_openDisk':
            return LynkerSDK.mainMenu.openDisk(query);
          case <const>'message_openChat':
            return LynkerSDK.message.openChat(query);
          case <const>'message_openMessage':
            return LynkerSDK.message.openMessage(query);
          case <const>'message_openDrawerForWebview':
            return LynkerSDK.message.openDrawerForWebview(query);
          case <const>'message_closeDrawerForWebview':
            return LynkerSDK.message.closeDrawerForWebview(query);
          case <'workBench_getActiveTeamId'>"workBench_getActiveTeamId":
            return LynkerSDK.workBench.getActiveTeamId();
          case <'workBench_openTab'>"workBench_openTab":
            return LynkerSDK.workBench.openTab((query as any).options, (query as any).openWorkBench);
          case <const>'workBench_closeTab':
            return LynkerSDK.workBench.closeTab(query);
          case <'workBench_updateTab'>"workBench_updateTab":
            return LynkerSDK.workBench.updateTab(query);
          case <'workBench_getTabList'>"workBench_getTabList":
            return LynkerSDK.workBench.getTabList();
          case <const>'workBench_openTabForIframe':
            return LynkerSDK.workBench.openTabForIframe(query);
          case <const>'workBench_openTabForWebview':
            return LynkerSDK.workBench.openTabForWebview(query);
          case <const>'workBench_openDrawerForWebview':
            return LynkerSDK.workBench.openDrawerForWebview(query);
          case <const>'workBench_closeDrawerForWebview':
            return LynkerSDK.workBench.closeDrawerForWebview(query);
          case <const>'workBench_openTabForEnterprise':
            return LynkerSDK.workBench.openTabForEnterprise(query);
          case <const>'workBench_openTabForMerchantApply':
            return LynkerSDK.workBench.openTabForMerchantApply(query);
          case <const>'workBench_openTabForReceiveDetail':
            return LynkerSDK.workBench.openTabForReceiveDetail(query);
          case <const>'workBench_openTabForRefundDetail':
            return LynkerSDK.workBench.openTabForRefundDetail(query);
          case <const>'workBench_openTabForSettlementDetail':
            return LynkerSDK.workBench.openTabForSettlementDetail(query);
          case <'workBench_openTeamCertification'>"workBench_openTeamCertification":
            return LynkerSDK.workBench.openTeamCertification(query);
          case <const>'workBench_reload':
            return LynkerSDK.workBench.reload();
          case <'digitalPlatform_setActiveTeamId'>"digitalPlatform_setActiveTeamId":
            return LynkerSDK.digitalPlatform.setActiveTeamId(query);
          case <'digitalPlatform_getActiveTeamId'>"digitalPlatform_getActiveTeamId":
            return LynkerSDK.digitalPlatform.getActiveTeamId();
          case <'digitalPlatform_openTab'>"digitalPlatform_openTab":
            return LynkerSDK.digitalPlatform.openTab(query);
          case <'digitalPlatform_closeTab'>"digitalPlatform_closeTab":
            return LynkerSDK.digitalPlatform.closeTab(query);
          case <'digitalPlatform_openTabForWebview'>"digitalPlatform_openTabForWebview":
            return LynkerSDK.digitalPlatform.openTabForWebview(query);
          case <'digitalPlatform_updateTab'>"digitalPlatform_updateTab":
            return LynkerSDK.digitalPlatform.updateTab(query);
          case <'digitalPlatform_reload'>"digitalPlatform_reload":
            return LynkerSDK.digitalPlatform.reload();
          case <'digitalPlatform_getTabList'>"digitalPlatform_getTabList":
            return LynkerSDK.digitalPlatform.getTabList();
          case <'square_setActiveTeamId'>"square_setActiveTeamId":
            return LynkerSDK.square.setActiveTeamId(query);
          case <'square_getActiveTeamId'>"square_getActiveTeamId":
            return LynkerSDK.square.getActiveTeamId();
          case <'square_openTab'>"square_openTab":
            return LynkerSDK.square.openTab(query);
          case <'square_closeTab'>"square_closeTab":
            return LynkerSDK.square.closeTab(query);
          case <'square_updateTab'>"square_updateTab":
            return LynkerSDK.square.updateTab(query);
          case <'square_getTabList'>"square_getTabList":
            return LynkerSDK.square.getTabList();
          case <'square_openTabForWebview'>"square_openTabForWebview":
            return LynkerSDK.square.openTabForWebview(query);
          case <'windowsTabs_openTab'>"windowsTabs_openTab":
            return LynkerSDK.windowsTabs.openTab(query);
          case <'windowsTabs_closeTab'>"windowsTabs_closeTab":
            return LynkerSDK.windowsTabs.closeTab(query);
          default:
            return 'OK';
        }
      };

      core.ipc.handleRenderer(CHANNEL, handle);

      // 定期执行垃圾回收
      timer(() => {
        try {
          const allWebContents = core.remote.webContents.getAllWebContents();
          allWebContents.forEach((webContents) => {
            webContents.executeJavaScript(`
              try {
                if (window.gc) {
                  console.log('执行垃圾回收');
                  window.gc();
                }
              } catch (error) {
                console.error(error);
              }
              try {
                // 设定内存阈值（单位：字节）
                const MEMORY_THRESHOLD = 80 * 1024 * 1024; // 80MB

                function getTotalLiveMemory(usage) {
                  return usage.images.liveSize +
                        usage.scripts.liveSize +
                        usage.cssStyleSheets.liveSize +
                        usage.xslStyleSheets.liveSize +
                        usage.fonts.liveSize +
                        usage.other.liveSize;
                }

                function checkAndClearCache() {
                  try {
                    let webFrame;
                    try {
                      webFrame = __ELECTRON_SDK__.webFrame || require('electron').webFrame;
                    } catch (error) {
                      webFrame = require('electron').webFrame;
                    }
                    const usage = webFrame.getResourceUsage();
                    const totalLiveMemory = getTotalLiveMemory(usage);

                    console.log('[资源监控] 当前内存占用: ' +(totalLiveMemory / 1024 / 1024).toFixed(2) + ' MB');

                    if (totalLiveMemory > MEMORY_THRESHOLD) {
                      console.warn('[资源监控] 超过内存阈值，正在清除缓存...');
                      webFrame.clearCache();
                    }
                  } catch (error) {
                    console.error('[资源监控] error: ', error);
                  }
                }
                checkAndClearCache();
              } catch (error) {
                console.error(error);
              }
            `);
          });
        } catch (error) {
          console.error(error);
        }
      }, 3);
    }
    try {
      const webContents = core.remote.getCurrentWebContents();
      webContents.setWindowOpenHandler(({ url }) => {
        webContents.executeJavaScript(`window.__ELECTRON_WINDOW_MANAGER_EXTRA_DATA__`).then((res) => {
          try {
            if (res) {
              const extraData = JSON.parse(res);
              if (extraData.windowsTabsOptions && extraData.windowsTabsOptions.tabOptions) {
                LynkerSDK.windowsTabs.openTab({
                  tabsId: extraData.windowsTabsOptions.tabsId,
                  tabsTitle: extraData.windowsTabsOptionstabsTitle,
                  options: {
                    title: '',
                    url: url,
                    icon: extraData.windowsTabsOptions.tabOptions.icon,
                    activeIcon: extraData.windowsTabsOptions.tabOptions.activeIcon,
                  }
                });
                return;
              }
            }
            throw new Error('extraData is not valid');
          } catch (error) {
            console.error('error', error);
            LynkerSDK.windowsTabs.openTab({
              tabsTitle: '',
              isOnlyOneTabHideTabs: true,
              options: {
                title: '',
                url: url,
              }
            });
          }
        });
        return { action: 'deny' }; // 拒绝默认行为，自己手动创建
      });
    } catch (error) {
      console.error('error', error);
    }

  } catch (error) {
    console.error(error);
  }
}());

/**
 * 定时器函数
 * @param {() => void} callback - 回调函数
 * @param {number} interval - 间隔时间(分钟)
 */
function timer(callback: () => void, interval: number): void {
  let lastTime = Date.now();
  const intervalMs = interval * 60 * 1000;

  function tick() {
    const now = Date.now();
    if (now - lastTime >= intervalMs) {
      callback();
      lastTime = now;
    }
    requestAnimationFrame(tick);
  }

  requestAnimationFrame(tick);
}
