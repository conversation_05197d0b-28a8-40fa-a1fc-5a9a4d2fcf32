<template>
  <div class="container">
    <div class="head">
      <div class="search-bar">
        <t-input
          v-model="bookName"
          :placeholder="t('member.culture.t')"
          style="width: 304px;"
          clearable
          @change="onSearch"
        >
          <!-- <template #suffixIcon>
            <iconpark-icon name="iconsearch" class="iconadd" @click="onSearch"></iconpark-icon>
          </template> -->
          <template #prefix-icon>
            <iconpark-icon name="iconsearch-a961a3le" class="iconsearch"></iconpark-icon>
          </template>
        </t-input>
      </div>

      <t-button theme="primary" variant="base" @click="onAdd">
        <template #icon>
          <iconpark-icon name="iconadd" class="iconadd"></iconpark-icon>
        </template>
        {{ $t('member.culture.o') }}</t-button>
    </div>

    <div class="container-bar" v-if="ebookSettingInfo">
      <div class="sBox">
        <div class="item">
          <div class="left">
            <iconpark-icon class="icon" name="iconcapacity"></iconpark-icon>
            <span class="bText"> {{ $t('member.culture.p') }}</span>
          </div>
          <div class="line"></div>
          <div class="right">
            <span class="count">{{ebookSettingInfo.use_size_txt}}/{{ebookSettingInfo.total_size_txt }}</span>
            <span class="text"> {{ $t('member.culture.q') }}</span>
          </div>
        </div>
      </div>
      <div class="sBox">
        <div class="item">
          <div class="left">
            <iconpark-icon class="icon" name="iconpage"></iconpark-icon>
            <span class="bText">{{ $t('member.culture.r') }}</span>
          </div>
          <div class="line"></div>
          <div class="right">
            <span class="count">{{ebookSettingInfo.use_page}}/{{ebookSettingInfo.total_page }}</span>
            <span class="text">{{ $t('member.culture.q') }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="container-bar" v-else>
      <div class="sBox">
        <div class="item"></div>
      </div>
      <div class="sBox">
        <div class="item"></div>
      </div>
    </div>

    <div class="body">
      <div class="body-content">
        <div class="table">
          <t-table
            row-key="id"
            :columns="memberColumns"
            :data="memberData"
            style="width: 100%;"
            :loading="loading"
            drag-sort="row-handler"
            @drag-sort="onDragSort"
          >
            <template #drag>
              <iconpark-icon name="icondrag" style="font-size: 20px; color: #828da5; margin-top: 5px"></iconpark-icon>
            </template>
            <template #role="{ row }">
              <div>
                {{ row.type_text }}
              </div>
            </template>
            <template #status="{ row }">
              <div class="statusT">
                <span :class="`status-tag${row.status}`">
                  {{ switchStatusOptions(row.status) }}
                </span>
                <t-tooltip placement="bottom">
                  <template #content>{{ row.reason }}</template>
                  <iconpark-icon v-show="row.status === 2 && row.reason" name="iconunusual" class="iconunusual"></iconpark-icon>
                </t-tooltip>
              </div>
            </template>
            <template #updated_at="{ row }">
              <div>
                {{ row.updated_at }}
              </div>
            </template>
            <template #size="{ row }">
              <div>
                {{ row.size_txt }}
              </div>
            </template>
            <template #page="{ row }">
              <div>
                {{ row.page_txt }}
              </div>
            </template>
            <template #channel_type="{ row }">
              <div>
                {{ switchChannelType(row.channel_type) }}
              </div>
            </template>

            <template #operate="{ row }">
              <div class="links">
                <template v-if="row.status === 1">
                  <t-link
                    theme="primary"
                    hover="color"
                    class="operates-item"
                    @click="updateThrough(row)"
                  >
                     {{$t('member.culture.s')}}
                  </t-link>
                </template>
                <t-link
                    theme="primary"
                    hover="color"
                    class="operates-item"
                    v-if="row.status === 2"
                    @click="onReCreate(row)"
                  >
                  {{$t('member.culture.u')}}
                </t-link>
                <t-link
                  v-if="row.status === 0"
                  hover="color"
                  class="operates-item"
                >
                  --
                </t-link>
                <t-popup
                  theme="default"
                  placement="bottom-right"
                  class="regular"
                  :cancel-btn="null"
                  :confirm-btn="null"
                  v-model:visible="row.popMoreconfirmVisible"
                >
                  <template #icon />
                  <template #content>
                    <div class="pop">

                      <span v-if="row.status === 1" class="pop-item"  @click="viewRun(row)">
                        <iconpark-icon
                          name="iconfolderSearch"
                          class="icondepartment1"
                        />
                        {{ t('ebook.view') }}
                      </span>
                      <span v-if="row.status === 1" class="pop-item"  @click="editItem(row)">
                        <iconpark-icon
                          name="iconedit"
                          class="icondepartment1"
                        />
                        {{ $t('member.impm.pos_5') }}
                      </span>
                      <span
                        v-if="row.status !== 0 "
                        class="pop-item"
                        @click="delItem(row)"
                      >
                        <iconpark-icon name="icondelete" class="icondepartment1"></iconpark-icon>
                        {{ $t('engineer.delete') }}
                      </span>
                      <span v-if="row.type === 1 && row.status === 1" class="pop-item"  @click="onReCreate(row)">
                        <iconpark-icon
                          name="iconactivation"
                          class="icondepartment1"
                        />
                        {{$t('member.culture.u')}}
                      </span>
                    </div>
                  </template>
                  <!-- @click="onShowMore(row)" -->
                  <div
                    v-if="row.status !== 0"
                    class="more-box"
                    :class="{moreActive: row.popMoreconfirmVisible}"
                  >
                    <iconpark-icon name="iconmore" class="iconmore" />
                  </div>
                </t-popup>
              </div>
            </template>
            <template #empty>
              <div class="empty">
                <noData :tip="$t('ebook.nodata')" :name="'no-friend-list'">
                </noData>
              </div>
            </template>
          </t-table>
        </div>
      </div>
    </div>
  </div>
  <t-upload
    ref="uploadRef"
    v-model="files"
    class="ossUpload"
    theme="custom"
    multiple
    :accept="'application/pdf'"
    :max="20"
    :action="null"
    :before-upload="beforeUpload"
    :request-method="onSelectChange"
  />
  <addEbook ref="adEbookRef" @reload="onSearch" :platform="platformCpt" :ebookSettingInfo="ebookSettingInfo"/>
  <updateThroughModal ref="updateThroughModalRef" @success="onSearch"/>
</template>

<script lang="ts" setup>

import noData from '@renderer/components/common/Empty.vue';
import { getResponseResult } from '@renderer/utils/myUtils';
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';
import { reactive, ref, watch, onMounted, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { usePoliticsStore } from '@renderer/views/politics/store/politics';
// import { deleteEbookAxios, getEbookDetail, getEbookListAxios, journalSort } from '@renderer/api/politics/api/ebookApi';
import { deleteEbookAxios,retryEbookAxios, getEbookDetail, getEbookListAxios, journalSort, getEbookSettingAxios } from '@renderer/api/politics/api/ebookApi';
import { getBaseUrl } from '@renderer/utils/apiRequest';
import addEbook from './add-ebook.vue';
import { useDigitalPlatformStore } from "@renderer/views/digital-platform/store/digital-platform-store";
import { useRoute } from "vue-router";
import { platform, platformText } from '@renderer/views/digital-platform/utils/constant';
import { getPoliticsTeamID } from "@renderer/views/politics/utils/auth";
import updateThroughModal from "@renderer/views/member/member_home/panel/ebook/update-through-modal.vue";
import { onMountedOrActivated } from '@renderer/hooks/onMountedOrActivated';
import { formatDate } from '@renderer/utils/date';
import { getStsToken } from "@renderer/api/cloud";
import md5 from "js-md5";
import { v4 as uuidv4 } from "uuid";
import { approveViewFiles } from '@renderer/api/approve';
import LynkerSDK from '@renderer/_jssdk';

const { ipcRenderer, shell } = LynkerSDK;

const files = ref([]);
const loading = ref(false);
const updateThroughModalRef = ref(null);
const adEbookRef = ref(null);
const uploadRef = ref(null);

const digitalPlatformStore = useDigitalPlatformStore();
const route = useRoute();

const store = usePoliticsStore();
const { t } = useI18n();
const statusOptions = [
  { label: '刊物生成中', value: 0 },
  { label: t('ebook.crok'), value: 1 },
  { label: t('ebook.crerr'), value: 2 },
];

const switchStatusOptions = (val) => {
  let res = statusOptions.find((v) => v.value === val);
  if (res) return res.label;

  return '--';
};

const memberColumns = ref([
  {
    colKey: 'drag', // 列拖拽排序必要参数
    title: '',
    width: '72px',
  },
  { colKey: 'name', title:  t('member.culture.z'), width: '324px' },
  { colKey: 'status', title: t('ebook.status'), width: '176px' },
  { colKey: 'channel_type', title: t('member.eb.a'), width: '176px' },
  { colKey: 'size', title: '大小', width: '120px' },
  { colKey: 'page', title: t('member.culture.v'), width: '120px' },
  { colKey: 'updated_at', title: t('ebook.upat'), width: '176px' },
  { colKey: 'operate', title: t('member.impm.drag_7'), width: '176px' },
]);
const bookName = ref('');
const memberData = ref([]);
const optionsMembers = ref([]);


const props = defineProps({
  platform: {
    type: String,
    default: '',
  },
})
// 平台类型 目前只有digital-platform
const platformCpt: any = computed(() => {
  return props.platform || route.query?.platform
})

const activeAccount = computed(() => {
  if(platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount
  } else if(platformCpt.value === platform.digitalWorkbench)  {
    return route.query;
  } else {
    return store.activeAccount
  }
})


const currentTeamId = computed(() => {
  if(platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount?.teamId
  } else if(platformCpt.value === platform.digitalWorkbench){
    return route.query?.teamId || 0;
  } else {
    return  getPoliticsTeamID()
  }
})


const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
  onChange: (pageInfo) => {
    console.log('pagination.onChange', pageInfo);
    pagination.current = pageInfo.current;
    pagination.pageSize = pageInfo.pageSize;
    getMemberList({});
  },
});

const switchChannelType = (val) => {
  if(val) {
    let arr = val.split(',');
    arr = arr.map(v=>platformText[v])
    return arr.join('、')
  } else {
    return '--'
  }
}

const client = ref(null);
const beforeUpload = (file) => {
  if(!file) return false;
  const isLt30M = file.size / 1024 / 1024 < 100;
  if (!isLt30M) {
    MessagePlugin.error(`PDF文件大小不得超过100MB`);
    return false;
  }
  return true;
};
const onSelectChange = async (file) => {
  // paddingUdpList.value = val.currentSelectedFiles;
  // if(!file) return false;
  // const isLt30M = file.size / 1024 / 1024 < 100;
  // if (!isLt30M) {
  //   MessagePlugin.error(`PDF文件大小不得超过100MB`);
  //   return false;
  // }

  console.log(file);
  // loadingModalRef.value.onOpen();
  // const fileItem = val.currentSelectedFiles[0];
  let paddingUdpList = file;
  loading.value = true;
  getStsToken().then((res) => {
    console.log(res);
    res = res.data;
    client.value = new OSS({
      // yourRegion填写Bucket所在地域。以华东1（杭州）为例，Region填写为oss-cn-hangzhou。
      region: "oss-cn-shenzhen",
      // 从STS服务获取的临时访问密钥（AccessKey ID和AccessKey Secret）。
      accessKeyId: res.data.AccessKeyId,
      accessKeySecret: res.data.AccessKeySecret,
      // 从STS服务获取的安全令牌（SecurityToken）。
      stsToken: res.data.SecurityToken,
      // 刷新临时访问凭证的时间间隔，单位为毫秒。
      refreshSTSTokenInterval: 300000,
      // 填写Bucket名称。
      bucket: "kuaiyouyi"
    });
    // eslint-disable-next-line array-callback-return
    paddingUdpList.map((e) => {
      const uuid = uuidv4();
      e.uuid = uuid;
      let fileName = e.name;
      let types = fileName.substring(fileName.lastIndexOf(".") + 1);
      const newName = `disk/${formatDate(new Date().getTime())}/${md5(
        fileName + new Date().getTime()
      )}.${types}`;
      client.value
        .multipartUpload(newName, e.raw, {
          progress: (p, cpt) => {}
        })
        .then(async (res) => {
          console.log(res, "qqqqqqqqqqqqq");

          const result = /\.([0-9a-z]+)(?:[\?#]|$)/i.exec(
            res.res.requestUrls[0]
          );
          const extension = result ? result[1] : "";
          let url = res.res.requestUrls[0].replace(/\?.*/, "");
          const resq = await approveViewFiles({
            url,
            name: res.name,
            size: e.size
          });
          let fileObj = {
            file_name: url,
            file_name_short: fileName,
            original_name: url,
            file_id: resq.data.file_id,
            size: e.size,
            type: extension,
            icon: "/assets/svg/pdf.svg"
          }
          console.log(fileObj, "props.attrs.value");
          onReCreateAxios(fileObj)

        })
        .catch((err) => {
          console.log(err, "errerrerr");
          loading.value = false;
        });
    });
  }).catch(()=> {
    loading.value = false;
  });
};


// 重新生成
const editRow = ref(null)
const onReCreate = (row) => {
  editRow.value = row;
  uploadRef.value.triggerUpload();
}

const onReCreateAxios = async (row) => {
  loading.value = true;
  try {
    let result = await retryEbookAxios({id: editRow.value.id, file: {url: row.file_name, ...row } }, currentTeamId.value);
    result = getResponseResult(result);
    if (!result) {
      loading.value = false;
      return;
    }
    addSucc();
    setTimeout(()=> {
      getMemberList({});
    }, 1000)
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    console.log(errMsg);

    MessagePlugin.error(errMsg);

  }
  loading.value = false;
}

// 修改渠道
const updateThrough = (row) => {
  updateThroughModalRef.value?.showDialog({teamId: currentTeamId.value, ...row});
}




const addSucc = () => {
  const confirmDia = DialogPlugin({
    header: '会刊生成中',
    theme: 'info',
    body: t('ebook.addsucc'),
    closeBtn: null,
    // confirmBtn: t('ebook.del'),
    className: 'delmode',
    onConfirm: async () => {
      confirmDia.hide();
    },
    onClose: () => {
      confirmDia.hide();
    },
  });
};

const onSearch = () => {
  getMemberList({});
  getEbookSettingInfo();
};
// 获取会员职务列表
const getMemberList =(params?) =>
   new Promise(async (resolve, reject) => {
    try {
      params.name = bookName.value;
      params.digital_type = 'government';
      let result = await getEbookListAxios(params, currentTeamId.value);
      console.log(result);
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      memberData.value = result.data.list;
      pagination.total = result.data.total;
      console.log(memberData.value);
      resolve(result.data);
    } catch (error) {
      const errMsg = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
      reject();
    }
  });
// watch(
//   () => store.activeAccount,
//   (val) => {
//     if (val) {
//       getMemberList({});
//     }
//   },
//   {
//     deep: true,
//   },
// );


onMountedOrActivated(()=> {
  getMemberList({});
  getEbookSettingInfo();
})

const onDragSort = (params) => {
  onSetSortAxios(params.newData.map((v) => v.id)).then(() => {
    memberData.value = params.newData;
  });
};

const ebookSettingInfo = ref(null)
const getEbookSettingInfo = async () => {
  try {
    let result = await getEbookSettingAxios({digital_type: 'government'}, currentTeamId.value);
    console.log(result);
    result = getResponseResult(result);
    if (!result) return;
    ebookSettingInfo.value = result.data;
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    MessagePlugin.error(errMsg);
  }
};


// 编辑
const editItem = async (row) => {
  try {
    let result = await getEbookDetail(row.id, currentTeamId.value);
    console.log(result);
    result = getResponseResult(result);
    if (!result) return;
    adEbookRef.value.onOpen(2, result.data);
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    MessagePlugin.error(errMsg);
  }
};



const delItem = (row) => {
  const confirmDia = DialogPlugin({
    header: '删除刊物',
    theme: 'info',
    body: t('ebook.deltip'),
    closeBtn: null,
    confirmBtn: t('ebook.del'),
    className: 'delmode',
    onConfirm: async () => {
      // 删除字段操作
      let res = null;
      try {
        res = await deleteEbookAxios(row.id, currentTeamId.value);
        res = getResponseResult(res);
        MessagePlugin.success(t('ebook.delsucc'));
        if (!res) return;
        getMemberList({});
        getEbookSettingInfo();
      } catch (error) {
        MessagePlugin.error(error.message);
      }
      confirmDia.hide();
    },
    onClose: () => {
      confirmDia.hide();
    },
  });
};

// 排序
const onSetSortAxios = (arr) => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await journalSort({ ids: arr, digital_type: 'government' }, currentTeamId.value);
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      resolve('success');
    } catch (error) {
      reject();
      const errMsg = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
    }
  });
};

const onAdd = () => {
  adEbookRef.value.onOpen(1);
};

const viewRun = (data: any) => {
  let url = `${getBaseUrl("h5")}/ebook/browse?id=${data.uuid}`;
  if (data.type === 2) {
    url = data?.file?.url || '';
  }
  openUrlByBrowser(url);
};


const openUrlByBrowser = (url: string) => {
  shell.openExternal(url);
};

</script>

<style lang="less" scoped>
.operates {
  &-item {
    // min-width: 64px;
    padding: 4px;
    border-radius: 4px;
    &:hover {

      background: var(--bg-kyy_color_bg_list_hover, #F3F6FA);

    }
  }
}

.statusT {
  display: flex;
  gap: 8px;
  .iconunusual {
    font-size: 20px;
    color: #828DA5;
  }
}
.pop {
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 144px;
  &-item {
    padding: 8px;

    border-radius: 4px;
    cursor: pointer;
    user-select: none;
    transition: all 0.2s linear;

    display: flex;
    align-items: center;
    gap: 12px;
    img {
      margin-right: 8px;
    }

    &:hover {
      background: var(--kyy_color_dropdown_bg_hover, #F3F6FA);
    }
  }
}
.more-box {
  display: flex;
  width: 28px;
  height: 28px;
  padding: 0px 4px;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  color: #828da5;
  cursor: pointer;
  font-size: 18px;
  transition: all 0.15s linear ;
  .iconmore {
    font-size: 20px;
  }

}
.more-box:hover {
  border-radius: 4px;
  background: var(--bg-kyy-color-bg-brand-foucs, #dbdfff);
  color: #3e4cd1;
  transition: all 0.15s linear ;
}

.icondepartment1 {
  font-size: 20px;
  width: 20px;
  height: 20px;
  color: #828da5;
}


.container-bar {
  padding: 0 16px;
  margin-top: 16px;
  margin-bottom: 24px;
  display: flex;
  gap: 16px;
  .sBox {
    border-radius: 8px;
    background: var(--bg-kyy_color_bg_deep, #F5F8FE);
    padding: 12px 16px;
    min-width: 304px;
    width:fit-content;
    .item {
      height: 50px;
      display:flex;
      // align-items: center;

      .left {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .icon {
          font-size: 24px;
          color: #828DA5;
        }
        .bText {
          color: var(--text-kyy_color_text_2, #516082);

          /* kyy_fontSize_2/bold */
          font-family: "PingFang SC";
          font-size: 14px;
          font-style: normal;
          font-weight: 600;
          line-height: 22px; /* 157.143% */
        }
      }
      .line {
        width: 1px;
        background: var(--divider-kyy_color_divider_deep, #D5DBE4);
        height: 16px;
        margin: auto 16px;
      }

      .right {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .count {
          color: var(--text-kyy_color_text_1, #1A2139);
          font-size: 18px;
          font-style: normal;
          font-weight: 600;
          line-height: 26px; /* 144.444% */
        }
        .text {
          color: var(--text-kyy_color_text_2, #516082);
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px; /* 157.143% */
        }
      }
    }
  }
}

.container {
    height: calc(100vh - 104px);
    width: 100%;
    max-width:none;
    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-color: @kyy_color_bg_light;
        padding: 0 16px;

        height: 64px;
        &-title {
            font-size: 16px;

            font-weight: 700;
            text-align: left;
            color: #13161b;
            user-select: none;
        }
        &-buttons {
          display: flex;
          gap: 8px;
        }

    }
    .body {
        // min-height:100%;

        // width: 100%;
        // padding: 12px;
        overflow-y: auto;
        height: calc(100vh - 202px);
        &-content {
            // height: max-content;
            background-color: @kyy_color_bg_light;
            height: fit-content;
            min-height: 100%;

        }
    }
    .body::-webkit-scrollbar {
      width: 4px;
    }
    .body::-webkit-scrollbar-thumb {
      background-color: #ccc;
    }
}
.table {
    padding: 0 16px;
    display:block;
}
:deep(.empty) {
  min-height: 70vh;
}
.iconadd {
  font-size: 20px;
}
.table {
  :deep(.t-table table) {
    width: 100%;
    // min-width: 100%;
  }
  :deep(.t-table--layout-fixed) {
    table-layout: auto !important;
  }
}

:deep(.t-dialog--default) {
  padding: 0 !important;
}

.links {
  display: flex;
  gap: 16px;
}

.head {
  background-color: #ffffff;
  padding: 0 16px;
  margin-top: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .tip {
    color: var(--text-kyy_color_text_3, #828DA5);
    text-align: center;


    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
    display: flex;
    align-items: center;
    gap: 4px;
    .icon {
      color: #828DA5;
      font-size: 20px;
    }
  }
}
.body-content {
  min-height: min-content !important;
}

.flex-column-center {
  display: flex;
  flex-direction: column;
}
.tag{
  display: flex;
  height: 24px;
  min-height: 24px;
  max-height: 24px;
  padding: 1px 8px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  text-align: right;
  font-family: "PingFang SC";
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 22px; /* 157.143% */
}
.status-tag0:extend(.tag){
  width: 86px;
  color: var(--kyy_color_tag_text_warning, #FC7C14);
  border-radius: var(--kyy_radius_tag_full, 999px);
background: var(--kyy_color_tag_bg_warning, #FFE5D1);
}
.status-tag1:extend(.tag){
  width: 58px;
  border-radius: var(--kyy_radius_tag_full, 999px);
  background: var(--kyy_color_tag_bg_success, #E0F2E5);
  color: var(--kyy_color_tag_text_success, #499D60);
}
.status-tag2:extend(.tag){
  width: 72px;
  color: var(--lingke-wrong, #D92F4D);
  border-radius: var(--kyy_radius_tag_full, 999px);
  background: #FBDDE3;
}




</style>
