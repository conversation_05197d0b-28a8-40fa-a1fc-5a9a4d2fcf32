<template>
  <div v-lkloading="{ show:loading, height:false, opacity:false }" class="containerk">
    <div class="header">
      <!-- 申请列表 -->
      <div class="h cursor" @click="onGoBack">
        <iconpark-icon
          name="iconarrowlift"
          style="font-size: 24px"
        ></iconpark-icon>

        <span class="text">返回</span>
      </div>

      <div class="tabs">

        <template
          v-for="(tab, tabIndex) in tabList"
          :key="tabIndex"
        >
          <!-- <t-badge :offset="[10, -2]" :count="tab.value === 'PApplyMemberPanel' ? applyCount : activeCount"> -->
            <span
              :class="{tab:true, cursor: true}"
              @click="onSetPanel(tab)"
            >
              {{ tab.label }}
              <span class="line"></span>
            </span>
          <!-- </t-badge> -->
        </template>
      </div>
    </div>
    <div class="body">

      <component
        :is="panels[currentPanel ? currentPanel : '']"
        @onBrush="onBrushCount"
        @getRedNum="getRedNum"
      />
    </div>

  </div>
</template>

<script setup lang="ts">
import { computed, reactive, ref, toRaw, watch } from "vue";
import { panels } from "@renderer/views/uni/member_home/panel/trends-panel/panel";

import { useUniStore } from "@renderer/views/uni/store/uni";
import { useRouter, useRoute } from "vue-router";
import { updateAllCount } from "@renderer/views/uni/hooks/total";
import { useI18n } from "vue-i18n";
import { useDigitalPlatformStore } from "@renderer/views/digital-platform/store/digital-platform-store";
import { platform } from "@renderer/views/digital-platform/utils/constant";
import { getUniTeamID } from "@renderer/views/uni/utils/auth";
import { onMountedOrActivated } from "@renderer/hooks/onMountedOrActivated";

const { t } = useI18n();
const digitalPlatformStore = useDigitalPlatformStore();
const store = useUniStore();
const route = useRoute();
const emits = defineEmits(["onSetCurrentPanel", "onSetCurrentRow", "getRedNum"]);
const currentPanel = ref("PApply");
const tabList = [
    {
        label: '申请列表',
        value: 'PApply'
    },
    // {
    //     // label: '激活申请',
    //     label: t('member.impm.set_7'),
    //     value: 'PActiveMemberPanel'
    // }
];

const props = defineProps({
  platform: {
    type: String,
    default: '',
  },
})
// 平台类型 目前只有digital-platform
const platformCpt = computed(() => {
  return props.platform || route.query?.platform
})

const activeAccount = computed(() => {
  if(platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount
  } else if(platformCpt.value === platform.digitalWorkbench)  {
    return route.query;
  } else {
    return store.activeAccount
  }
})


const currentTeamId = computed(() => {
  if(platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount?.teamId
  } else if(platformCpt.value === platform.digitalWorkbench){
    return route.query?.teamId || 0;
  } else {
    return  getUniTeamID()
  }
})


const loading = ref(false);
const onGoBack = () => {
  emits("onSetCurrentPanel", "PTrends");
};
const onLoadingShow = () => {
  loading.value = true;
  setTimeout(() => {
    loading.value = false;

  }, 900);
};

const applyCount = ref(0);
const activeCount = ref(0);
const onSetPanel = (val) => {
  currentPanel.value = val.value;
  onLoadingShow();
  onBrushCount();
};


const getRedNum = ()=> {
  emits("getRedNum");
}
const onBrushCount = ()=> {
  updateAllCount(currentTeamId.value).then((res)=> {
    if(res && res.length > 1) {
      activeCount.value = res[0].find((v) => v.teamId === currentTeamId.value)?.total || 0;
      applyCount.value= res[1].find((v) => v.teamId === currentTeamId.value)?.total || 0;
    }
  });
}

// 进入入会或激活
// 用于判定来自其他bv跳转的问题
const goApplyOrActive = () => {
    if (route.query && route.query.origin === 'message') {
      const { teamId, redirect } = route.query;
      if (teamId && redirect) {

        if (redirect.includes('PActiveMemberPanel')) {
          currentPanel.value = 'PActiveMemberPanel';
        } else if (redirect.includes('PApplyMemberPanel')) {
          currentPanel.value = 'PApplyMemberPanel';
        }

        route.query.from = '';
        onLoadingShow();
      }
    }
};
onMountedOrActivated(() => {
  goApplyOrActive();
  onBrushCount();
});


</script>

<style lang="less" scoped>
// .body {
//   height: calc(100vh - 56px);
// }
.containerk {
    display: flex;
    flex-direction: column;
    height: 100%;
    .header {
        height: 56px;
        display: flex;
        align-items: center;
        justify-content:center;
        position: relative;
        border-bottom: 1px solid var(--divider-kyy-color-divider-light, #ECEFF5);

        .h {
            position: absolute;
            left: 16px;
            display: flex;
            align-items: center;
            .text {
              color: var(--text-kyy_color_text_3, #828DA5);
            }
        }
        .tabs {
            display: flex;
            gap: 44px;
            .tab {
                // color: var(--text-kyy-color-text-1, #1A2139);
                // text-align: center;
                // font-size: 16px;
                // font-style: normal;
                // font-weight: 400;
                // line-height: 24px; /* 150% */
                position: relative;

                color: var(--text-kyy_color_text_1, #1A2139);
                text-align: center;

                /* kyy_fontSize_4/bold */
                font-family: "PingFang SC";
                font-size: 18px;
                font-style: normal;
                font-weight: 600;
                line-height: 26px; /* 144.444% */
                .line {
                    visibility: hidden;

                }
            }
            .active {
                color: var(--brand-kyy-color-brand-default, #4D5EFF);
                font-size: 16px;
                font-style: normal;
                font-weight: 600;
                line-height: 24px; /* 150% */

                .line {
                    visibility: visible;
                    position: absolute;
                    border-radius: 1.5px;
                    width: 16px;
                    height: 3px;
                    flex-shrink: 0;
                    background: var(--brand-kyy-color-brand-default, #4D5EFF);
                    bottom: -16px;
                    left: 0;
                    right: 0;
                    margin: 0 auto;
                }
            }
        }
    }
    .body {
      flex: 1;
      display: flex;
      flex-direction: column;
    }
}

:deep(.t-badge) {
  margin-right: 0 !important;
}
:deep(.t-badge--dot) {
  right: 3px;
  margin-top: 2px;

}
:deep(.t-badge--circle) {

  color: var(--kyy_color_tag_text_magenta, #ff4aa1);

  background: var(--kyy_color_badge_bg, #FF4AA1) !important;
}

</style>
