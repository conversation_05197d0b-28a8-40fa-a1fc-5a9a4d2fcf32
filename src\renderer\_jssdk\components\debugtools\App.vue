<template>
  <div class="debugtools-container">
    <t-collapse :default-value="[]" @change="handlePanelChange">
      <t-collapse-panel
        destroy-on-collapse
        :header="`窗口管理（${windowList.size}）`"
      >
        <template #headerRightContent>
          <t-space size="small">
            <t-button size="small" :on-click="openConsoleOld">控制台</t-button>
            <t-button size="small" :on-click="gc">垃圾回收</t-button>
            <t-button size="small" :on-click="getWindowList">刷新</t-button>
          </t-space>
        </template>
        <t-list>
          <t-list-item v-for="item in windowList?.entries?.()" :key="item[0]">
            <t-collapse style="width: 100%;" :default-value="[]">
              <t-collapse-panel
                destroy-on-collapse
                :header="`${item[1]._name}`"
              >
                <template #headerRightContent>
                  <t-space size="small">
                    <t-button size="small" :on-click="() => openConsole(item[1])">打开/关闭控制台</t-button>
                  </t-space>
                </template>
                <t-space style="width: 100%;" direction="vertical" size="small">
                  <div>title: {{ item[1]?.webContents?.getTitle?.() }}</div>
                  <div>url: {{ item[1]?.webContents?.getURL?.() }}</div>
                  <div><t-button size="small" :on-click="() => onCopy(item[1]?.webContents?.getURL?.())">复制 url</t-button></div>
                  <t-button v-if="item[1]?._type === 'BW'" size="small" :on-click="() => getWindowBv(item[1])">获取窗口BV</t-button>
                  <t-button size="small" :on-click="() => getWindowWebview(item[1])">获取窗口webview</t-button>
                  <t-button v-if="item[1]?._type === 'BW'" size="small" :on-click="() => closeWindow(item[1])">关闭窗口</t-button>
                  <t-button size="small" :on-click="() => focusWindow(item[1])">focus</t-button>
                  <template v-if="windowBvList?.get(item[1]?.id)">
                    <t-list v-for="bv in windowBvList?.get(item[1]?.id)" :key="bv[0]">
                      <t-list-item>
                        <t-space style="width: 100%;" direction="vertical" size="small">
                          <div>title: {{ bv?.webContents?.getTitle?.() }}</div>
                          <div>url: {{ bv?.webContents?.getURL?.() }}</div>
                          <div><t-button size="small" :on-click="() => onCopy(bv?.webContents?.getURL?.())">复制 url</t-button></div>
                          <t-button size="small" :on-click="() => openConsole(bv)">打开/关闭控制台</t-button>
                        </t-space>
                      </t-list-item>
                    </t-list>
                  </template>
                  <template v-if="windowWebviewList?.get(item[1]?.id)?.length">
                    <t-list>
                      <t-list-item v-for="wv in windowWebviewList.get(item[1]?.id)" :key="wv.webviewId">
                        <t-space style="width: 100%;" direction="vertical" size="small">
                          <div>webviewId: {{ wv.webviewId }}</div>
                          <div>href: {{ wv.href }}</div>
                          <div><t-button size="small" :on-click="() => onCopy(wv.href)">复制 href</t-button></div>
                          <div>
                            <t-button size="small" :on-click="() => openConsoleWebview(wv.webviewId)">打开/关闭控制台</t-button>
                          </div>
                        </t-space>
                      </t-list-item>
                    </t-list>
                  </template>
                </t-space>
              </t-collapse-panel>
            </t-collapse>

          </t-list-item>
        </t-list>
      </t-collapse-panel>
      <t-collapse-panel header="web开发工具">
        <div class="tdesign-demo-block-column" style="width: 100%">
          <t-form
            ref="form"
            :rules="FORM_RULES"
            :data="formData"
            :colon="true"
            :label-width="80"
            @submit="onSubmit"
            @reset="onReset"
          >
            <t-form-item label="打开页面" name="url">
              <t-input v-model="formData.url" placeholder="请输入网址" />
            </t-form-item>
            <t-form-item label="无边框模式" name="frame">
              <t-switch v-model="formData.frame" />
            </t-form-item>
            <t-form-item>
              <t-space size="small">
                <t-button style="width: 100px;" theme="primary" type="submit">确定</t-button>
                <t-button style="width: 100px;" theme="default" variant="base" type="reset">重置</t-button>
                <t-button style="width: 100px;" theme="default" variant="base" @click="closeH5Window">关闭开发窗口</t-button>
                <t-button style="width: 100px;" theme="default" variant="base" @click="openH5Console">打开控制台</t-button>
                <t-button style="width: 100px;" theme="default" variant="base" @click="openSdkDemo">打开sdk demo</t-button>
                <t-button style="width: 100px;" theme="default" variant="base" @click="openUnitPark">打开unitPark</t-button>
              </t-space>
            </t-form-item>
          </t-form>
        </div>
      </t-collapse-panel>
      <t-collapse-panel header="应用日志">
        <div class="tdesign-demo-block-column" style="width: 100%">
          <t-space style="width: 100%;" direction="vertical" size="small">
            <t-space style="width: 100%;" size="small">
              <span>渲染进程日志: </span><t-switch v-model="checked" @change="onChange" />
            </t-space>
            <t-space style="width: 100%;" size="small">
              <span>渲染进程日志保存本地: </span><t-switch v-model="checkedLogLocal" @change="onChangeLogLocal" />
            </t-space>
            <div>目录: {{ logPath }}</div>
            <t-button theme="primary" type="submit" :on-click="openLogPath">打开日志目录</t-button>
          </t-space>
        </div>
      </t-collapse-panel>
      <t-collapse-panel header="开发工具">
        <t-space style="width: 100%;" direction="vertical" size="small">
          <t-button theme="primary" type="submit" :on-click="openApmDevTool">AMP开发工具库</t-button>
        </t-space>
      </t-collapse-panel>
    </t-collapse>
  </div>
</template>
<script lang="tsx" setup>
import { onMounted, reactive, ref, watch } from 'vue';
import { useWindowFocus } from '@vueuse/core';
import { CollapseProps, FormProps, FormInstanceFunctions } from 'tdesign-vue-next';
import core from '@lynker-desktop/electron-sdk/renderer';
import LynkerSDK from "@renderer/_jssdk";
import type { BrowserWindow } from 'electron';
const checked = ref(false);
const checkedLogLocal = ref(false);
const currentItem = ref<number[]>([]);
const windowList = ref<Map<string, any>>(new Map());
const windowBvList = ref<Map<string, any>>(new Map());
const windowWebviewList = ref<Map<string, any[]>>(new Map());
const focused = useWindowFocus();
const logPath = ref('');
const form = ref<FormInstanceFunctions>(null);

const onChange = (val) => {
  console.log(val);
  localStorage.setItem(`__IS_LOG__`, (!!val).toString());
};

const onChangeLogLocal = (val) => {
  console.log(val);
  localStorage.setItem(`__IS_LOG_LOCAL__`, (!!val).toString());
};

const FORM_RULES: FormProps['rules'] = { url: [{ required: true, message: '网址必填' }] };

const formData: FormProps['data'] = reactive({
  url: '',
});

const { ipcRenderer } = LynkerSDK;
const onSubmit: FormProps['onSubmit'] = ({ validateResult, firstError }) => {
  if (validateResult === true) {
    console.log('submit', formData.url);
   if (formData.url && formData.url.startsWith('http')) {
      core.windowManager.create({
        name: `web端开发窗口`,
        url: `${formData.url}`,
        browserWindow: {
          width: 1296,
          height: 720,
          useContentSize: true,
          autoHideMenuBar: true,
          frame: !formData.frame,
          show: true,
          webPreferences: {
            preload: ``,
            nodeIntegration: true,
            contextIsolation: false,
            webSecurity: false,
          },
        },
      });
    }
  } else {
    console.log('Validate Errors: ', firstError, validateResult);
  }
};

const onReset: FormProps['onReset'] = () => {
  formData.url = '';
};

const closeH5Window = () => {
  core.windowManager.close(`web端开发窗口`);
};
const openH5Console = () => {
  core.windowManager.get(`web端开发窗口`).then((item) => {
    item.webContents?.toggleDevTools();
    item.webContents?.focus?.();
  });
};
const openSdkDemo = () => {
  formData.url = `http://localhost:8080/_sdk_demo_/index.html#/`;
  setTimeout(() => {
    form.value?.submit();
  }, 0);
};

const openUnitPark = () => {
  const env = LynkerSDK.config.env;
  const token = LynkerSDK.config.token;

  const baseUrl = 'http://localhost:8000/components';
  const url = `${baseUrl}?env=${env}&token=${token}`;
  LynkerSDK.openBrowser(url);
};


const handlePanelChange: CollapseProps['onChange'] = (val) => {
  currentItem.value = val.map((n) => Number(n));
};

const getWindowList = async () => {
  try {
    // windowList.value = new Map();
    const allWebContents = core.remote.webContents.getAllWebContents();
    console.log(allWebContents);
    const obj = await core.windowManager.getAll();
    // 将 Map<number, BWItem | BVItem> 转换为 Map<string, any>
    const convertedMap = new Map([...obj].map(([key, value]) => [key.toString(), value]));
    windowList.value = convertedMap;
  } catch (error) {
    console.error(error);
  }
};

const getWindowBv = async (item: any) => {
  console.log(item);
  const bv = await item.getBrowserViews();
  console.log(bv);
  windowBvList.value.set(item.id, bv);
};

const getWindowWebview = async (item: any) => {
  try {
    const webviews = await item.webContents.executeJavaScript(`(() => {
      try {
        var webviews = document.querySelectorAll('webview');
        return JSON.stringify(Array.from(webviews).map(item => {
          return {
            webviewId: item.getAttribute('data-webContentsId'),
            href: item.getAttribute('src'),
          };
        }));
      } catch (error) {
        return JSON.stringify([]);
      }
    })()`);
    const list = JSON.parse(webviews);
    windowWebviewList.value.set(item.id, list);
    // 触发响应式
    windowWebviewList.value = new Map(windowWebviewList.value);
    return list;
  } catch (error) {
    return [];
  }
};

const openConsole = (item: BrowserWindow) => {
  try {
    item.webContents.toggleDevTools();
    item.webContents?.focus?.();
  } catch (error) {
    console.error(error);
  }
};

const onCopy = (text: string) => {
  navigator.clipboard.writeText(text);
};

const openConsoleWebview = (webviewId: string) => {
  const webview = core.remote.webContents.fromId(Number(webviewId));
  webview.toggleDevTools();
  webview.focus();
};

const closeWindow = (item: any) => {
  try {
    item.close();
  } catch (error) {
    console.error(error);
  }
};

const focusWindow = (item: any) => {
  item.webContents?.focus?.();
};

const openLogPath = () => {
  core.remote.shell.openPath(logPath.value);
};

const openConsoleOld = () => {
  ipcRenderer.invoke("toggle-devtools", '打开控制台');
}
const init = async () => {
  getWindowList();
  logPath.value = await core.getLogPath();
  checked.value = localStorage.getItem('__IS_LOG__') === 'true';
  checkedLogLocal.value = localStorage.getItem('__IS_LOG_LOCAL__') === 'true';
};

const openApmDevTool = () => {
  LynkerSDK.windowsTabs.openTab({
    tabsTitle: 'AMP开发工具库',
    isOnlyOneTabHideTabs: true,
    options: {
      title: 'APM开发工具库',
      url: 'https://apm.lynker.cn/',
    },
  });
};
const gc = () => {
  const allWebContents = core.remote.webContents.getAllWebContents();
  allWebContents.forEach((webContents) => {
    webContents.executeJavaScript(`
      try {
        if (window.gc) {
          console.log('执行垃圾回收');
          window.gc();
        } else {
          console.log('window.gc不存在');
        }
      } catch (error) {
        console.error(error);
      }
    `);
  });
};

onMounted(() => {
  console.log(LynkerSDK);
  init();
});

/**
 * 监听窗口焦点变化
 */
watch(focused, (newVal) => {
  if (newVal) {
    init();
  }
});


</script>
<style lang="less">
.debugtools-container {
  width: 100%;
  min-height: 100vh;
  overflow-y: auto;
  & * {
    user-select: none;
  }
  .t-form {
    margin-bottom: 16px;
    .t-form-item {
      margin-bottom: 12px;
    }
    .t-space {
      flex-wrap: wrap;
      gap: 12px;
    }
  }
}
.window-name {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.button-area {
  margin-top: 20px;
  display: flex;
  align-items: center;
}
</style>
