import { i18nt } from '@/i18n';

/**
 * 公告场景类型枚举
 */
export enum SceneType {
  /**
   * 申请
   */
  APPLY = 5037,
  /**
   * 投放中取消
   */
  CANCEL = 5038,
  /**
   * 投放取消
   */
  REJECT = 5039,
}

export const SceneTypeArr = Object.values(SceneType).filter((value) => typeof value === 'number');

// export const typeText = ["", "通知", "通告", "通报", "公告", "公示"];
export const typeText = ['', '通知', '通告', i18nt('banch.tb'), '公告', '告示'];

/**  1商协 2政企 3CBD */
export const getTitle = (channel_type) => {
  if (channel_type === 'cbd') {
    return i18nt('application.digital_cbd');
  }
  if (channel_type === 'government') {
    return i18nt('application.government');
  }
  if (channel_type === 'member') {
    return i18nt('application.member');
  }
  if (channel_type === 'association') {
    return i18nt('niche.szsq');
  }
  if (channel_type === 'uni') {
    return i18nt('niche.szgx');
  }
  return i18nt('application.member');
};

export const getBtnTxt = (status) => {
  const titleMap = {
    1: i18nt('ebook.vtype3'), // 已通过
    2: i18nt('ebook.vrefuse'), // 已拒绝
    3: i18nt('approval.approval_data.repeal'), // 已撤销
  };
  return titleMap[status] || null;
};
