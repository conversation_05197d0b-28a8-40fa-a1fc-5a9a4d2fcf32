import { MessagePlugin } from 'tdesign-vue-next';
import { computed, nextTick } from 'vue';
import to from 'await-to-js';
import moment from 'moment';
import LynkerSDK from '@renderer/_jssdk';
import { SquareType } from '@/api/square/enums';
import { errReasonMap, PostType } from '@/views/square/constant';
import { ErrorResponseReason } from '@/views/square/enums';
import { i18n } from '@/i18n';
import { useSquareStore } from '@/views/square/store/square';
import router from '@/router/index';
import { getSquareInfo } from '@/api/square/home';
import { getErrorStatusMsg, quit } from '@/utils/apiRequest';
import { setAccountAuthRouters } from '@/utils/auth';
import { AgreementType } from '@/api/square/common';
import { useTabsStore } from '@/components/page-header/store';
import { navigateToSquare } from './ipcHelper';

const { shell } = LynkerSDK;
// @ts-ignore
const t = i18n.global.t;

// 获取组织广场号id
export const getOrgSquare = () => {
  try {
    const squareLocal = localStorage.getItem('square');
    if (!squareLocal) return;

    const squareCache = JSON.parse(squareLocal);
    const openid = localStorage.getItem('openid');
    const lastLoginInfo = squareCache.lastLoginInfo[openid];
    if (lastLoginInfo && lastLoginInfo.type !== SquareType.Individual) {
      return lastLoginInfo;
    }
  } catch (e) {
    console.log(e);
  }
};

// 消息管理器，用于避免重复显示相同的错误消息
const messageManager = {
  // 存储即将显示的消息，确保同一消息不会重复显示
  queue: new Set(),
  // 存储已经显示过的消息，用于冷却时间内的检查
  displayed: new Set(),
  // 冷却时间，用于控制消息显示的频率
  coolDown: 3000,

  // 添加消息到队列并显示消息
  addAndShow(message: string) {
    // 如果消息既不在已显示集合中，也不在队列中，则添加并显示
    if (!this.displayed.has(message) && !this.queue.has(message)) {
      this.queue.add(message);
      this.displayed.add(message);
      MessagePlugin.error(message);

      // 设置定时器，在冷却时间后从队列和已显示集合中删除消息
      setTimeout(() => {
        this.queue.delete(message);
        this.displayed.delete(message);
      }, this.coolDown);
    }
  },

  // 检查消息是否已经在队列中
  hasQueued(message: string) {
    return this.queue.has(message);
  },
};

// 接口请求错误消息处理
export const handleRequestError = (error, commonErrMsg?: string) => {
  const { data } = error.response;
  const msg = getErrorStatusMsg(error) || errReasonMap[data.reason] || data.message || commonErrMsg;
  const showMsgWhiteList = [ErrorResponseReason.SquareSilenced, ErrorResponseReason.ANNUAL_FEE_NOT_UPGRADEABLE, ErrorResponseReason.ANNUAL_FEE_PENDING_EXIST];
  const store = useSquareStore();

  switch (error?.response?.status) {
    case 401:
    case 403:
      quit();
      break;
    default:
      break;
  }

  // 使用中被禁言，接口返回被禁言错误时，设置广场禁言状态
  if (data.reason === ErrorResponseReason.SquareSilenced) {
    store.squareInfo.square.silenced = true;
  }

  // 能用错误提示处理
  if (
    msg
    && !error.config.hideMessage
    && !showMsgWhiteList.includes(data.reason)
  ) {
    // 如果消息已经在队列中，不再添加
    if (!messageManager.hasQueued(msg)) {
      messageManager.addAndShow(msg);
    }
  }

  // 处理未加 hideMessage 的特例
  if (!error.config.hideMessage && showMsgWhiteList.includes(data.reason)) {
    if (data.reason === ErrorResponseReason.SquareSilenced) {
      MessagePlugin.error(t('square.square.limitTip'));
    }
  }

  // 无组织广场号权限（企业管理后台-应用管理-广场-设置-可见权限）
  if (data.reason === ErrorResponseReason.SquareNotAccessible) {
    store.getSquaresList();
    toAccount();
    return;
  }

  return Promise.reject(error);
};

/**
 * 跳转广场号账号
 * @param item 从广场号列表选中的项，可选。不传时默认跳转个人广场号
 */
export const toAccount = async (item?) => {
  const store = useSquareStore();
  const tabStore = useTabsStore();

  let square = item;
  if (!square) {
    square = store.squareList.find((v) => v.square.squareType === SquareType.Individual);
  }
  const { squareId, squareType, originId } = square.square;
  await store.setLastLoginInfo({ squareId, type: squareType, teamId: originId });

  store.setSquareSelected(item);

  await nextTick();
  const [err, res] = await to(getSquareInfo({ square_id: square.square.squareId }));
  if (err) return;

  const { square: sq } = res.data.info;
  store.squareInfo = { ...res.data.info };
  store.squareInfo.square.silenced = sq.silenced;

  await nextTick();
  tabStore.removeAllTab();
  const isIndividual = squareType === SquareType.Individual;
  // const path = isIndividual ? '/square/friend-circle' : '/square/publish-records';
  const path = store.homePage;
  await router.replace(path);
  tabStore.tabs[0].fullPath = path;
  tabStore.activeIndex = 0;

  store.getSquareStats();
  store.fetchNewsStats();

  return {
    isIndividual,
  };
};

// 已过期名称增加提示
export const useExpiredNameTip = () => {
  const store = useSquareStore();
  const expiredNameTip = computed(() => {
    const expiredAt = store.squareInfo?.organizationProfile?.expiredAt;
    const MAX_DAYS = 30;
    if (!expiredAt) return '';

    const diff = moment().diff(moment(expiredAt));
    if (diff > 0) {
      const leftDays = Math.floor(moment.duration(diff).asDays());
      const leftHours = Math.floor(moment.duration(diff).asHours());
      if (leftDays > MAX_DAYS) return '该简称保留30天内未续费，已释放给他人使用';
      if (leftDays === MAX_DAYS) {
        const hours = leftHours - MAX_DAYS * 24;
        if (hours <= 0) return '该简称保留30天内未续费，已释放给他人使用';
        return `广场号已到期，该专有简称${hours}小时后将释放，释放后将允许他人使用。请尽快续费！`;
      }
      return `广场号已到期，该专有简称${MAX_DAYS - leftDays}天后将释放，释放后将允许他人使用。请尽快续费！`;
    }
    return '';
  });

  return {
    expiredNameTip,
  };
};

/**
 * TODO move to ipcHelper.ts
 * 跨bv跳转广场模块
 * @param query 跳转参数
 * @param toPersonal 默认先切换到个人广场
 * @param toOrg 切换到组织广场
 */
export const goSquareModule = async (query: Record<string, any> = {}, { toPersonal = true, toOrg = false, reload = false } = {}) => {
  await setAccountAuthRouters('click-menu-item');

  // 先切换到个人广场号
  const skipSwitchToPersonal = query.skipSwitchToPersonal === 'true';
  console.log(query, 'queryyyyyyyyyyy');

  if ((query.toOrg !== 'true' || !toOrg) && toPersonal && !skipSwitchToPersonal) {
    const squareStore = useSquareStore();
    await squareStore.getIndividualInfo();
  }

  navigateToSquare(query, { toOrg, reload });
};

// 打开隐私协议
export const openPrivacyAgreement = (type: AgreementType) => {
  const url = 'https://ringkol.com/privacyView?uuid=';
  shell.openExternal(`${url}${type}`);
};

export const needRedirectLastLogin = () => {
  try {
    const openid = localStorage.getItem('openid');
    const lastLoginInfo = JSON.parse(localStorage.getItem('square')).lastLoginInfo[openid];
    return lastLoginInfo && lastLoginInfo.type !== SquareType.Individual;
  } catch (e) {
    return false;
  }
};

export const isPostCard = (type: PostType) => [PostType.TeamHonorRoll, PostType.TeamIntro, PostType.TeamHistory, PostType.PartyBuilding, PostType.Fengcai].includes(type);
