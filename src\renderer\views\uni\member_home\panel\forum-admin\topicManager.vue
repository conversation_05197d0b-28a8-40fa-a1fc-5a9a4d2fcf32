<template>
  <div class="admin-container">
    <div class="su-header">
      <div class="more-search">
        <div class="in-box">
          <t-input
            v-model="searchParams.keyword"
            :placeholder="t('forum.keywordPlaceholder')"
            :maxlength="50"
            clearable
            style="width: 304px"
            @blur="handleSearch"
            @enter="handleSearch"
            @clear="handleSearch"
            class="inSearch"
          >
            <template #prefix-icon>
              <iconpark-icon name="iconsearch" class="iconsearch"></iconpark-icon>
            </template>
          </t-input>
        </div>
      </div>
      <div class="opt">
        <t-button theme="primary" variant="base" @click="topicAddDrawerRef?.openDrawer">
          <template #icon>
            <iconpark-icon name="iconadd" class="iconadd"></iconpark-icon>
          </template>
          {{ t("forum.addTopic") }}</t-button
        >
      </div>
    </div>

    <div class="table-container" @scroll="onTableScroll" :style="`height: ${tableMaxHeight}px`">
      <t-table
        row-key="topicID"
        :pagination="null"
        :columns="tableColumns"
        :data="tableData"
        dragSort="row-handler"
        @drag-sort="onDragSort"
        :loading="loading"
      >
        <template #empty>
          <div class="empty" :style="`height: ${tableMaxHeight - 70}px`">
            <Empty :name="searchEmpty ? 'no-search-contact': 'no-data-new'" :tip="searchEmpty ? t('forum.searchEmpty') : t('forum.topicEmpty')" />
          </div>
        </template>

        <template #drag>
          <div class="drag-cell">
            <img src="@/assets/icon_drag.png" alt="" />
          </div>
        </template>

        <template #name="{ row }">
          <div class="topic-info-cell">
            <div class="topic-name">
              <!-- <img src="@/assets/digital/icon/topic.png" alt="" /> -->
              <!-- <iconpark-icon name="icon24topic" class="topic-icon" /> -->
              <iconpark-icon name="icon24topic-f39f46n2" class="topic-icon text-[19px]!" />
              <div class="ellipsis-1">{{ row.name }}</div>
            </div>
            <div class="topic-des">
              <t-tooltip :content="`#${row.description}`">
                #{{ row.description }}
              </t-tooltip>
            </div>
          </div>
        </template>

        <template #creator="{ row }">
          <span>{{ row.card?.cardName }}</span>
        </template>

        <template #operate="{ row }">
          <div class="operate-cell">
            <a v-if="row.status === 'NORMAL'" @click.stop="toggleTopicStatus(row, 'SHIELD')">{{ t("forum.shield") }}</a>
            <a v-else @click.stop="toggleTopicStatus(row, 'NORMAL')">{{ t("forum.shieldCl") }}</a>
          </div>
        </template>
      </t-table>
    </div>

    <topic-add-drawer ref="topicAddDrawerRef" @add-success="refreshTableData" />
  </div>
</template>

<script setup lang="ts">
import { onBeforeUnmount, onMounted, reactive, ref } from "vue";
import { useI18n } from "vue-i18n";
import Empty from "@/components/common/Empty.vue";
import TopicAddDrawer from "./components/topic-add-drawer.vue";
import { getManagerTopicList, setTopicSort, setTopicStatus } from "@/api/uni/api/forumAdminApi";
import { Topic } from "@/api/forum/models/topic";
import { MessagePlugin } from "tdesign-vue-next";
import { debounce } from "lodash";

const { t } = useI18n();

const topicAddDrawerRef = ref(null);

const loading = ref(true);

// 搜索参数
const searchParams = reactive({
  keyword: null,
});

// 搜索结果为空
const searchEmpty = ref(false);

// 分页对象
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
});

// 表格宽度
const tableWidth = 944;

// 表格高度
const tableMaxHeight = ref(document.body.clientHeight - 128);

// 表格列
const tableColumns = [
  {
    colKey: "drag",
    title: t("forum.topic"),
    width: `${32 / tableWidth}%`,
  },
  {
    colKey: "name",
    className: "td-topic-name",
    width: `${400 / tableWidth}%`,
  },
  {
    colKey: "postCount",
    title: t("forum.postCount"),
    width: `${144 / tableWidth}%`,
  },
  {
    colKey: "viewCount",
    title: t("forum.viewCount"),
    width: `${144 / tableWidth}%`,
  },
  {
    colKey: "creator",
    title: t("forum.creator"),
    width: `${120 / tableWidth}%`,
  },
  {
    colKey: "operate",
    title: t("forum.operate"),
    width: `${104 / tableWidth}%`,
  },
];

// 表格数据
const tableData = ref<Topic[]>([]);

// 加载表格数据
const loadTableData = async (needRefreshList = false, isSearch = false) => {
  const res = await getManagerTopicList({
    page: { number: pagination.current, size: pagination.pageSize },
    search: searchParams.keyword,
  });

  if(loading.value){
    loading.value = false;
  }

  const {
    data: { topics, total },
  } = res.data;

  if (needRefreshList) {
    tableData.value = topics;
  } else {
    tableData.value = [...tableData.value, ...topics];
  }

  searchEmpty.value = isSearch ? total < 1 : false;

  pagination.total = Number(total);
};

// 刷新表格数据
const refreshTableData = () => {
  pagination.current = 1;
  loadTableData(true);
};

loadTableData();

// 搜索数据
const handleSearch = debounce(() => {
  pagination.current = 1;
  loadTableData(true, true);
}, 300);

// 切换话题屏蔽状态
const toggleTopicStatus = async (row, status) => {
  await setTopicStatus({ id: row.topicID,  status });
  await MessagePlugin.success(`${status === "SHIELD" ? t("forum.shield") : t("forum.shieldCl")}成功`);
  row.status = status;
};

// 拖拽排序
const onDragSort = async ({ current, target }) => {
  await setTopicSort({ id: current.topicID, sort: target.sort });
  refreshTableData();
};

// 监听表格滚动
const onTableScroll = (e) => {
  if (tableData.value.length === pagination.total) {
    return;
  }
  const { scrollTop, clientHeight, scrollHeight } = e.target;
  if (scrollTop + clientHeight === scrollHeight) {
    // 滚动到底加载下一页
    pagination.current++;
    loadTableData();
  }
};

// 重置表格最大高度
const resizeTableMaxHeight = () => {
  tableMaxHeight.value = document.body.clientHeight - 128;
};

onMounted(() => {
  window.addEventListener("resize", resizeTableMaxHeight);
});

onBeforeUnmount(() => {
  window.removeEventListener("resize", resizeTableMaxHeight);
});
</script>

<style lang="less" scoped>
@import "@renderer/views/member/member_home/panel/public.less";

.iconsearch {
  font-size: 20px;
}

.iconadd {
  color: #ffffff;
  font-size: 24px;
}

.admin-empty-box {
  margin: 0;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.admin-container {
  padding: 16px;

  .su-header {
    padding: 0;
  }

  .table-container {
    margin-top: 24px;
    overflow: auto;

    :deep(.t-table__handle-draggable) {
      text-align: left;
    }

    :deep(th.t-table__th-drag) {
      text-align: left;
      border-right: none;
      white-space: nowrap;

      .t-table__th-cell-inner {
        position: relative;
        z-index: 1;
      }
    }

    .drag-cell {
      display: flex;
      justify-content: center;
      align-items: center;

      img {
        width: 20px;
        height: 20px;
      }
    }

    :deep(.td-topic-name) {
      padding-left: 5px !important;
    }

    .topic-info-cell {
      .topic-name {
        display: flex;
        align-items: center;
        gap: 4px;

        img {
          width: 20px;
          height: 20px;
        }
        .topic-icon {
          font-size: 22px;
          color: #62BF7C;
        }
      }

      .topic-des {
        margin-top: 2px;
        color: var(--text-kyy_color_text_3, #828da5);
        display: -webkit-box;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-line-clamp: 2;
      }
    }

    .operate-cell {
      display: flex;
      gap: 8px;

      a {
        padding: 4px;
        color: var(--brand-kyy_color_brand_default, #4d5eff);
        line-height: 22px;
        border-radius: 4px;

        &:hover {
          background: var(--bg-kyy_color_bgBrand_hover, #eaecff);
        }
      }
    }
  }
}
</style>
