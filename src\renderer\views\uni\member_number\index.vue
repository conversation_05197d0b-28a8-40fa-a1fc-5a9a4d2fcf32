<template>
  <!-- v-lkloading="{ show:isLoading, height:false, opacity:true }" -->
  <div  class="main">
    <!-- {{ currentTab }}     {{ isLoading }} -->
    <!-- <div class="tab-box">
      <div class="logo">
        <span class="tabIcon">
          <img
            class="tabImg"
            src="/assets/member/icon_apply_notes.svg"
            alt=""
          />
        </span>
        <div class="text">数字城市</div>
      </div>
      <div class="tabs">
        <div
          v-for="(tab, tabIndex) in tabs"
          v-show="tab.isShow"
          :key="tabIndex"
          class="item"
          :class="{
            active: tab.value === currentTab.value
          }"
          @click="onSetTab(tab)"
        >
          <t-badge :count="tab.count">
            {{ tab.label }}
          </t-badge>
        </div>
        <div class="act-tag" :class="'act-' + currentTab.value"></div>
      </div>
      <div class="record" @click="onInputRecord">
        <span>入会申请记录</span>
        <iconpark-icon
          name="iconarrowright"
          class="iconarrowright"
        ></iconpark-icon>
      </div>
    </div> -->
    <div ref="containerFlets" class="body">

      <component :is="panels[currentTab ? currentTab.component : '']" @select-platform="selectPlatform" />
    </div>

    <div class="backTop cursor" v-show="scrolledDistance > 50" :class="{isOpacity: scrolledDistance > 70}" @click="scrollToTop">
      <!-- <iconpark-icon name="iconarrowup" class="iconarrowup"></iconpark-icon> -->
      <iconpark-icon class="iconarrowup" name="icontopinside"></iconpark-icon>

    </div>
    <!-- <ApplyRecordModal ref="applyRecordModalRef" /> -->

  </div>
</template>

<script setup lang="ts" name="bench_uni_number">
import { ref, toRaw, watch, onMounted, onUnmounted } from "vue";
import { useRouter, useRoute } from "vue-router";
// import useRouterHelper from "@renderer/views/square/hooks/routerHelper";
import { useUniStore } from "@renderer/views/uni/store/uni";
import { checkIsAdminAxios } from "@renderer/api/uni/api/businessApi";
// 申请记录
// import ApplyRecordModal from "@renderer/views/uni/association_number/modal/apply-record-modal.vue";

import { panels } from "@renderer/views/uni/member_number/panel";
import { getResponseResult } from "@renderer/utils/myUtils";
import { MessagePlugin } from "tdesign-vue-next";
import { updateAllCount } from "@renderer/views/uni/hooks/total";
import { allRoutersUuid } from "@renderer/constants/index";
import { getProfilesInfo } from '@renderer/utils/auth';
import { useI18n } from "vue-i18n";
import LynkerSDK from "@renderer/_jssdk";
const { ipcRenderer } = LynkerSDK;

const { t } = useI18n();
const router = useRouter();
const route = useRoute();
const store = useUniStore();
// const { menuList, routeList, roleFilter } = useRouterHelper("associationIndex");
const containerFlets = ref(null);
const tabs = ref([
  { label: "首页", value: "home", component: "PHome", isShow: true, count: 0 },
  {
    label: "会员中心",
    value: "center",
    component: "PMember",
    isShow: !!store.activeAccount,
    count: 0
  },
  {
    label: t('member.winter.member_manage'),
    value: "manage",
    component: "PManage",
    isShow: !!store.activeAccount,
    count: store.getAllTeamApplyCount + store.getAllTeamActiveCount
  },
  {
    label: t('member.winter.xuanchuan'),
    value: "leaflets",
    component: "PLeaflets",
    // isShow: !!store.activeAccount,
    count: 0
  },
  { // 选择组织端、管理端
    label: "选择端页面",
    value: "platform",
    component: "PPlatform",
    count: 0
  }
]);
const currentTab = ref(tabs.value[3]);
// const loading = ref(false);
const isLoading = ref(false);



// watch(() => route.fullPath, async () => {
//   setTimeout(() => {
//     if (route.query && route.query.from === 'message') {
//       const { teamId, redirect } = route.query;
//       if (teamId) {

//       }
//     }
//   });

// });

const selectPlatform = (type) => {
  const tab = store.tabs.find((v) => v.name === 'uni_number');
  console.log(tab)
  if (type === 'member') { // 组织端
    currentTab.value = tabs.value.find((v) => v.value === "center");
    console.log(store.tabs);

    console.log(tab);
    if (tab) {
      tab.title = '成员端';
    }
  } else if (type === 'manage') {
    currentTab.value = tabs.value.find((v) => v.value === "manage");
    // const tab = store.tabs.find((v) => v.name === 'uni_number');

    if (tab) {
      tab.title = t('member.winter.admin_platform');
    }
  }
  onLoading(1000);
};

const onLoading = (time?) => {
  isLoading.value = true;

  setTimeout(() => {
    isLoading.value = false;
  }, time | 3000);
};


// const onSetTab = (val) => {
//   if (val.value === "manage") {
//     const searchMenu = routeList.find((v) => v.name === "member_home");
//     // store.addTab(routeList.filter((v) => v.affix).filter(roleFilter));
//     // router.push({ path: searchMenu.fullPath });
//     router.push(searchMenu.fullPath);
//     console.log(searchMenu.fullPath);
//     store.addTab(toRaw(searchMenu));
//   } else {
//     currentTab.value = val;
//   }
// };
const isAdminValue = ref(null);

// 判断当前用户是否为管理员
const onCheckIsAdmin = async (idStaff) => {
  console.log('走这里了吗');
  
  let res: any = null;
  try {
    isLoading.value = true;
    res = await checkIsAdminAxios({ idStaff });
    isLoading.value = false;

    res = getResponseResult(res);
    if (!res) return;

    isAdminValue.value = res.data;
    console.log('isAdminValue')
    if (isAdminValue.value) {
      const center = tabs.value.find((v) => v.value === "center");
      if (center) {
        center.isShow = res.data.member;
      }
      console.log('isAdminValue.value')

      const manage = tabs.value.find((v) => v.value === "manage");
      manage.isShow =
        store.activeAccount &&
        (isAdminValue.value.super ||
          isAdminValue.value.isAdmin ||
          isAdminValue.value.superAdmin);

      console.log(center);
      console.log(manage);
      const tab = store.tabs.find((v) => v.name === 'uni_number');
      // 1、当用户未加入商协会组织，或切换到无关联会员和管理员身份的商协会下时，进入此宣传页
      if ((!center?.isShow) && (!manage?.isShow)) {
        // 两者没有的情况下
        console.log('两者没有的情况下');
        const leaflets = tabs.value.find((v) => v.value === "leaflets");
        currentTab.value = leaflets;

        // const tab = store.tabs.find((v) => v.name === 'uni_number');
        console.log(tab);
        if (tab) {
          tab.title = '数字城市';
        }
      } else if (center?.isShow && !manage.isShow) {
        // 只有关联会员的情况下
        console.log('只有关联会员的情况下');
        currentTab.value = center;


        console.log(tab);
        if (tab) {
          tab.title = '成员端';
        }

      } else if (!center?.isShow && manage.isShow) {
        // 只有管理员的情况下
        console.log('只有管理员的情况下');

        currentTab.value = manage;
        // const tab = store.tabs.find((v) => v.name === 'uni_number');
        console.log(tab);
        if (tab) {
          tab.title = t('member.winter.admin_platform');
        }
      } else {
        // 两者都有
        const arr = store.getStorePlatforms;


        if (route.query.from !== 'message') {
          const profile = getProfilesInfo();
          const result = arr.find((v) => v.openId === profile.openid && v.teamId === store.activeAccount?.teamId);
          if (result) {
            // 如果有记录值
            const tab = store.tabs.find((v) => v.name === 'uni_number');
            if (result.apply ==='manage') {
              const manage = tabs.value.find((v) => v.value === "manage");
              currentTab.value = manage;
              if (tab) {
                tab.title = t('member.winter.admin_platform');

              }
            } else {
              const center = tabs.value.find((v) => v.value === "center");
              currentTab.value = center;
              if (tab) {
                tab.title = '成员端';

              }
            }
          } else {
            currentTab.value = tabs.value.find((v) => v.value === "platform");
          }
        } else {
          goMemberAdmin(); // 用于其他地方跳转到这
        }
      }
      console.log(currentTab.value);
      // test
      // currentTab.value = tabs.value.find((v) => v.value === "leaflets");
    }
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    MessagePlugin.error(errMsg);
  }
  isLoading.value = false;

};

// 进入管理端的逻辑
const goMemberAdmin = () => {
      if (route.query && route.query.from === 'message') {
      const { teamId, redirect } = route.query;
      if (teamId && redirect) {
        const tab = store.tabs.find((v) => v.name === 'uni_number');
        if (redirect.includes('PManage')) {
          const manage = tabs.value.find((v) => v.value === "manage");
          currentTab.value = manage;
          if (tab) {
            tab.title = t('member.winter.admin_platform');
          }
        } else if(redirect.includes('PMember')) {
          const manage = tabs.value.find((v) => v.value === "center");
          currentTab.value = manage;
          if (tab) {
            tab.title = t('member.winter.member_platform');
          }
        }
      }
    }
};


watch(
  () => store.activeAccount,
  async (val) => {
    if (val) {
      isLoading.value = true;
      try {
        await updateAllCount();

      } catch (error) {
        console.log('err:activeAccountC');
      }

      // ipcRenderer.send(
      //   "update-nume-index",
      //   allRoutersUuid.findIndex((v) => v === "member")
      // );
      tabs.value[2].count =
        store.getAllTeamApplyCount + store.getAllTeamActiveCount;
      // 2023-12-5 修改默认tab
      // currentTab.value = tabs.value[0];

      setTimeout(() => {
        onCheckIsAdmin(val?.staffId);
      });
    }
  },
  {
    // deep: true,
    immediate: true
  }
);




const scrolledDistance = ref(0); // 滚动距离
// const containerFlets = ref(null);
const handleScroll = (event) => {
  console.log(event.target.scrollTop);
  scrolledDistance.value = event.target.scrollTop;
  // scrolledDistance.value = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop;
};
let animationId = null;
const scrollToTop = () => {
  // containerFlets.value.scrollTop = 0; // 滚动容器到顶部
  cancelAnimationFrame(animationId); // 取消之前的动画

      const scrollTop = containerFlets.value.scrollTop;
      const step = Math.ceil(scrollTop / 6); // 每帧滚动的步长
      console.log(step)
      const animate = () => {
        if (containerFlets.value.scrollTop > 0) {
          containerFlets.value.scrollTop -= step;
          animationId = requestAnimationFrame(animate); // 请求下一帧动画
        } else {
          cancelAnimationFrame(animationId); // 动画结束，取消请求
        }
      };

      animationId = requestAnimationFrame(animate); // 开始动画
};

onMounted(() => {
  containerFlets.value?.addEventListener('scroll', handleScroll); // 监听滚动事件
});
onUnmounted(() => {
  containerFlets.value?.removeEventListener('scroll', handleScroll); // 取消监听滚动事件
});

// const applyRecordModalRef = ref(null);
// const onInputRecord = () => {
//   applyRecordModalRef.value.onOpen();
// };
</script>

<style lang="less" scoped>

/*定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
::-webkit-scrollbar {
  width: 4px;
  // height: 2px;
  background-color: #f5f5f5;
}
/*定义滚动条轨道 内阴影+圆角*/
::-webkit-scrollbar-track {
  background-color: #e3e6eb;
}
/*定义滑块 内阴影+圆角*/
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
  background-color: #c8c8c8;
}
.main {
  .tab-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    align-self: stretch;
    width: 100%;
    height: 56px;
    padding: 0 24px;
    .logo {
      display: flex;
      align-items: center;
      gap: 12px;
      img {
        border-radius: 6.4px;
        width: 32px;
        height: 32px;
        margin-right: 12px;
      }
      .text {
        color: #000;
        text-align: center;

        /* kyy_fontSize_3/regular */
        font-family: PingFang SC;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 24px; /* 150% */
      }
    }
    .tabs {
      display: flex;
      position: relative;
      .item {
        margin-right: 44px;
        color: var(--text-kyy-color-text-1, #1a2139);
        text-align: center;

        /* kyy_fontSize_3/regular */
        font-family: PingFang SC;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 24px; /* 150% */
        cursor: pointer;
        :v-deep(.t-badge--circle) {
          right: -3px;
        }
      }
      .active {
        .t-badge {
          color: var(--brand-kyy-color-brand-default, #4d5eff);

          /* kyy_fontSize_3/bold */
          font-family: PingFang SC;
          font-size: 16px;
          font-style: normal;
          font-weight: 600;
          line-height: 24px; /* 150% */
        }
      }
    }

    .record {
      cursor: pointer;
      color: var(
        --color-button-text-brand-kyy-color-button-text-brand-font-default,
        #4d5eff
      );
      text-align: center;
      display: flex;
      align-items: center;
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
      .iconarrowright {
        font-size: 18px;
      }
    }
  }
  .act-tag {
    width: 16px;
    height: 3px;
    flex-shrink: 0;
    border-radius: 1.5px;
    background: var(--brand-kyy-color-brand-default, #4d5eff);
  }
  .act-home {
    position: absolute;
    bottom: -17px;
    left: 7px;
  }
  .act-center {
    position: absolute;
    bottom: -17px;
    left: 96px;
  }
  .act-manage {
  }
  // header {
  //   background: #fff;
  //   min-height: 30px;
  //   padding: 10px;
  //   font-size: 500;
  //   color: black;
  //   // box-shadow: 0 6px 8px -5px #dddddd;
  //   border-bottom: 1px solid #dddddd;
  //   display: flex;
  //   gap: 50px;
  //   align-items: center;
  //   .logo {
  //     margin-left: 200px;
  //   }
  //   .item {
  //     position: relative;
  //     text-align: center;
  //     width: 100px;
  //   }
  //   .active {
  //     color: @kyy_color_icon_blue;
  //     &::after {
  //       position: absolute;
  //       content: "";
  //       height: 2px;
  //       background: @kyy_color_icon_blue;
  //       border-radius: 20px;
  //       width: 100px;
  //       left: 0;
  //       bottom: -10px;
  //       right: 0;
  //       transition: all 0.15s linear;
  //     }
  //   }
  // }
  .body {
    display: flex;
    // align-items: center;
    justify-content: center;
    // overflow: auto;
    // height: calc(100% - 56px);
    height: 100%;
    // background: var(--kyy_color_tabbar_bg, #ebf1fc);
    background-size: cover;

    overflow-y: overlay;
    overflow-x: hidden;
    // background: #fff;
    background: var(--bg-kyy_color_bg_deep, #F5F8FE);
    // position: relative;

  }
}

.tabIcon {
  // margin-left: 7px !important;
  border-radius: 4px !important;
  width: 32px;
  height: 32px;
  // background: var(--cyan-kyy-color-cyan-default, #11bdb2) !important;
  background: #fd9d4f;
  display: flex;
  align-items: center;
  justify-content: center;
  .tabImg {
    width: 26px !important;
    height: 26px !important;
    margin-right: 0 !important;
  }
}


.isOpacity {
  opacity: 1 !important;
  transition:all 0.25s linear;
}
:deep(.pageTop) {
  height: 100% !important;
}

.backTop {
  position: fixed;
  right: 16px;
  bottom: 32px;
  opacity: 0;
  transition:all 0.25s linear;

  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  border: 1px solid var(--border-kyy_color_border_default, #D5DBE4);
  background: var(--divider-kyy_color_divider_deep, #D5DBE4);
  .iconarrowup {
    font-size: 20px;
    color: #1A2139;
  }
  .text {
    color: var(--text-kyy_color_text_1, #1A2139);
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px; /* 166.667% */
  }
}

</style>
