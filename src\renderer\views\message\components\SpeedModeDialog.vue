<template>
  <div @click="closeContextmenuVisible" id="speedModel" :class="props.defaultType">
    <div class="page-box" ref="offsetBox"
      :style="{ height: props.defaultType === 'APP_ACCOUNT_EMO' ? '320px' : isChecked ? '305px' : '271px', border: props.defaultType === 'APP_ACCOUNT_EMO' ? '1px solid #ECEFF5' : '0px',paddingTop:props.defaultType === 'APP_ACCOUNT_EMO' && isChecked ? '52px' : '0' }">
      <div class="header-box" v-if="!isChecked"
        :style="{ paddingTop: props.defaultType === 'APP_ACCOUNT_EMO' ? '16px' : '0', height: props.defaultType === 'APP_ACCOUNT_EMO' ? '52px' : '36px' }">
        <div class="flex-box">
          <div class="tab-box" :id="'tab-box_' + props.defaultType">
            <!--            <t-tabs v-model="tabActive" placement="top" @change="handlerChange">-->
            <!--              <t-tab-panel v-for="(item, index) in tabList" :key="index" :value="item.type" :label="item.typeName">-->
            <!--              </t-tab-panel>-->
            <!--            </t-tabs>-->
            <div class="personal_tabs" :id="'personal_tabs_' + props.defaultType">
              <div class="personal_tabs_width" :id="'personal_tabs_width' + props.defaultType">
                <div v-for="(item, index) in tabList" :key="index" :class="[
                  'personal_tabs_item_' + props.defaultType,
                  'default-tab-item',
                  tabActive === item.type ? 'active-tab-item' : ''
                ]">
                  <t-button theme="default" variant="text" @click="handlerChange(item)"
                    :style="{ fontSize: props.defaultType === 'APP_ACCOUNT_EMO' ? '16px' : '14px', linHeight: '24px' }">{{
                      item.typeName }}</t-button>
                  <div class="tab-item-border"></div>
                </div>
              </div>
            </div>
          </div>

          <div class="btn mb-3" v-if="showMoveTabBtn">
            <t-button shape="square" variant="text" @click="moveTabList('left')">
              <template #icon><img style="width: 20px;height: 20px;"
                  src="@renderer/assets/im/icon_arrow_left.svg"></template>
            </t-button>
            <t-button shape="square" variant="text" @click="moveTabList('right')">
              <template #icon><img style="width: 20px;height: 20px;"
                  src="@renderer/assets/im/icon_arrow_right.svg"></template>
            </t-button>
          </div>

          <div class="btn" v-if="tabList.length">
            <t-tooltip v-if="props.defaultType === 'APP_ACCOUNT_WORDS'" content="新建" :show-arrow="false"
              placement="bottom">
              <t-button shape="square" variant="text" @click="add" style="width: 28px;height: 28px;padding: 0 4px;">
                <template #icon><img style="width: 20px;height: 20px;"
                    src="@renderer/assets/im/im_setting_add.svg"></template>
              </t-button>
            </t-tooltip>

            <t-button v-else-if="props.defaultType === 'APP_ACCOUNT_ADDRESS'" shape="square" variant="text"
              style="display: none;">
              <template #icon><img style="width: 20px;height: 20px;"
                  src="@renderer/assets/im/im_setting_add.svg"></template>
            </t-button>
            <t-tooltip v-else-if="props.defaultType === 'APP_ACCOUNT_EMO' && ['DEFAULT', 'DEFAULT_COCO', 'SNAKE_EMOJI'].includes(tabActive)" content="新建"
              :show-arrow="false" placement="bottom">
              <t-button style="display: none;" shape="square" variant="text" @click="add">
                <template #icon><img style="width: 20px;height: 20px;"
                    src="@renderer/assets/im/im_setting_add.svg"></template>
              </t-button>
            </t-tooltip>

            <t-tooltip v-else content="新建" :show-arrow="false" placement="bottom">
              <upload ref="fileUploadRef" :total-number="dataList.length" @update-upload="getFiles"
                @show-loading="uploadLoading"
                :uploadfile-success-clear="props.defaultType === 'APP_ACCOUNT_FILE'"
                :type="props.defaultType === 'APP_ACCOUNT_FILE' ? 'file' : (props.defaultType === 'APP_ACCOUNT_EMO' ? 'icon' : 'image')" />
            </t-tooltip>

          </div>
          <div class="btn mb-3">
            <t-tooltip content="分组管理" :show-arrow="false" placement="bottom">
              <t-button shape="square" variant="text" @click="set" style="width: 28px;height: 28px;">
                <template #icon><img src="@renderer/assets/im/im_setting_speed.svg"></template>
              </t-button>
            </t-tooltip>
          </div>
        </div>
      </div>
      <div class="content-box">
        <!-- 表情 -->
        <div v-if="props.defaultType === 'APP_ACCOUNT_EMO'" class="overflow-auto flex-wrap emoji picture" style="align-items: flex-start;">
          <div class="emoji-root" v-show="tabActive === 'DEFAULT'"
            style="max-width: 100%;padding: 0;margin:0;padding-top: 10px">
            <div class="emoji-item" v-for="[key, src] in Array.from(EmojiData)" @click="onSelectEmoji(key, src)">
              <t-tooltip :content="key" :show-arrow="false" placement="top">
                <img :src="src" :alt="key" draggable="false">
              </t-tooltip>
            </div>
          </div>
          <div class="emoji-root" v-show="tabActive === 'DEFAULT_COCO'"
            style="max-width: 100%;padding: 0;margin:0;padding-top: 10px;gap:20px;">
            <div class="emoji-item-COCO" v-for="[key, src] in Array.from(EmojiGifData)" @click="onSelectEmojiCOCO('emoji_coco_image', key, src)">
              <t-tooltip :content="key" :show-arrow="false" placement="top">
                <img :src="src" :alt="key" draggable="false">
              </t-tooltip>
            </div>
          </div>
          <div class="emoji-root" v-show="tabActive === 'SNAKE_EMOJI'"
            style="max-width: 100%;padding: 0;margin:0;padding-top: 10px;gap:20px;">
            <div class="emoji-item-COCO" v-for="[key, src] in Array.from(snakeEmoji)" @click="onSelectEmojiCOCO('emoji_coco_image', key, src)">
              <t-tooltip :content="key" :show-arrow="false" placement="top">
                <img :src="src" :alt="key" draggable="false">
              </t-tooltip>
            </div>
          </div>
          <t-checkbox-group v-show="!['DEFAULT_COCO', 'DEFAULT', 'SNAKE_EMOJI'].includes(tabActive)" v-model="checkList" style="width: 100%;gap:20px;padding-top:8px;">
            <template v-if="dataList.length">
              <div v-for="(item, index) in dataList" :index="index" class="item" style="margin: 1px;"
                @contextmenu.prevent="openContextmenu($event, item, index)">
                <t-checkbox v-if="isChecked" :value="item.id"></t-checkbox>
                <t-image
                  :src="filterImgSrc(item.data.imgUrl,item.data.type)"
                  draggable="false"
                  shape="round"
                  :style="{ width: '80px', height: '80px' }"
                  @click="sendSingleMessage('APP_ACCOUNT_EMO', item)"
                />
                <t-dropdown
                  attach=".pictureList"
                  trigger="hover"
                  v-if="!isChecked">
                  <div class="more">
                    <iconpark-icon name="iconmore"></iconpark-icon>
                  </div>
                  <t-dropdown-menu>
                    <t-dropdown-item @click="operate('checkbox', item, index)">
                      <iconpark-icon class="icons" name="iconcheckcorrect"></iconpark-icon>
                      <span style="margin-left: 12px;">多选</span>
                    </t-dropdown-item>
                    <t-dropdown-item @click="operate('move', item, index)">
                      <iconpark-icon class="icons" name="iconmove"></iconpark-icon>
                      <span style="margin-left: 12px;">移动分组</span>
                    </t-dropdown-item>
                    <t-dropdown-item class="dropdown-item-delete" @click="operate('delete', item, index)">
                      <iconpark-icon class="icons" name="icondelete" style="color:#D54941"></iconpark-icon>
                      <span style="margin-left: 12px;">删除</span>
                    </t-dropdown-item>
                  </t-dropdown-menu>
                </t-dropdown>
              </div>
            </template>
            <template v-else>
              <div class="wh-full noMoreData">
                <img src="@renderer/assets/im/speed_noData.svg">
                <p>{{ t('approval.no_data') }}</p>
              </div>
            </template>
          </t-checkbox-group>
        </div>
        <!-- 常用语 -->
        <div v-else-if="props.defaultType === 'APP_ACCOUNT_WORDS'"
          :class="{ 'flex-wrap words': true, 'isChecked': isChecked }" style="overflow: scroll;">
          <t-checkbox-group v-model="checkList" v-if="dataList.length">
            <div v-for="(item, index) in dataList" :index="index" :class="{ 'item': true }">
              <!-- 右击 -->
              <t-checkbox v-if="isChecked" :value="item.id" id="agree-checkbox"></t-checkbox>
              <div class="wordItem APP_ACCOUNT_WORDS_CONTENT_BOX">
                <!-- <img class="img" src="@renderer/assets/im/im_content.png" alt="">-->
                <div style="width: 0;flex: 1;" @click="sendSingleMessage('APP_ACCOUNT_WORDS', item)">

                  <span :class="['APP_ACCOUNT_WORDS_CONTENT', item.isShowAllContent ? 'showAllContent' : 'content']">
                    <template v-if="item.type === 'richText'">
                      {{ item.content }}
                    </template>
                    <template v-else>
                      {{ item.content }}
                    </template>
                  </span>

                </div>
                <span class="wordicons" @click.capture="openContextmenu($event, item, index)">
                  <iconpark-icon name="iconedit" class="wordicon" @click="operate('edit',item, index)"></iconpark-icon>
                  <t-dropdown maxColumnWidth="136px" attach=".content-box" trigger="hover">
                    <iconpark-icon name="iconmore" class="wordicon"></iconpark-icon>
                    <t-dropdown-menu>
                      <t-dropdown-item value="1" @click="operate('checkbox')">
                        <div style="display: flex;flex-direction: row;align-items: center;">
                          <img src="@renderer/assets/im/word_check.svg"
                            style="margin-right:10px;vertical-align:center" />多选
                        </div>
                      </t-dropdown-item>
                      <t-dropdown-item value="1" @click="operate('move', item, index)">
                        <div style="display: flex;flex-direction: row;align-items: center;">
                          <img src="@renderer/assets/im/word_move.svg"
                            style="margin-right:10px;vertical-align:center" />移动分组
                        </div>
                      </t-dropdown-item>
                      <t-dropdown-item value="1" @click="copyword(item.content)">
                        <div style="display: flex;flex-direction: row;align-items: center;">
                          <img src="@renderer/assets/im/word_copy.svg"
                            style="margin-right:10px;vertical-align:center" />复制
                        </div>
                      </t-dropdown-item>
                      <t-dropdown-item value="1" v-if="item.isBeyond">
                        <div style="display: flex;flex-direction: row;align-items: center;"
                          @click="item.isShowAllContent = !item.isShowAllContent">
                          <img src="@renderer/assets/im/word_show.svg" style="margin-right:10px;vertical-align:center"
                            v-if="!item.isShowAllContent" />
                          <img src="@renderer/assets/im/word_unshow.svg" style="margin-right:10px;vertical-align:center"
                            v-else />
                          {{ item.isShowAllContent ? '收起内容' : '展开内容' }}
                        </div>
                      </t-dropdown-item>
                      <t-dropdown-item value="1" @click="operate('delete',item, index)">
                        <div style="display: flex;flex-direction: row;align-items: center;">
                          <img src="@renderer/assets/im/word_del.svg" style="margin-right:10px;vertical-align:center" />删除
                        </div>
                      </t-dropdown-item>
                    </t-dropdown-menu>
                  </t-dropdown>
                </span>
                <!-- <t-button theme="primary" variant="text" style="padding: 0 5px;" v-if="item.isBeyond"
                  @click.stop="item.isShowAllContent = !item.isShowAllContent">{{ item.isShowAllContent ? '收起' : '展开'
                  }}</t-button> -->
              </div>
            </div>
          </t-checkbox-group>
          <!-- 缺省页 -->
          <div v-else class="noData">
            <img src="@renderer/assets/im/speed_noData.svg">
            <p>{{ t('im.public.addCommonTip') }}</p>
            <button @click="add">{{ t('im.public.addCommon') }}</button>
          </div>
        </div>
        <!-- <div v-else-if="props.defaultType === 'APP_ACCOUNT_PICTURE'" class="overflow-auto flex-wrap emoji picture"> -->
        <!-- 图片 -->
        <div v-else-if="props.defaultType === 'APP_ACCOUNT_PICTURE'" class="picture">
          <t-checkbox-group v-model="checkList" v-if="dataList.length">
            <div v-for="(item, index) in dataList" :index="index" class="item">
              <!-- @click="sendSingleMessage('APP_ACCOUNT_PICTURE', item)"> -->
              <t-checkbox v-if="isChecked" :value="item.id" class="checkbox"></t-checkbox>
              <t-image
                :src="filterImgSrc(item.data?.type=== 'gif'? item.data.imgUrl : item.data.thumbnail,item.data.type)"
                shape="round"
                class="images"
                lazy
                overlay-trigger="hover"
                draggable="false"
                @click.stop="sendSingleMessage('APP_ACCOUNT_PICTURE', item)"></t-image>
              <t-dropdown attach=".pictureList" trigger="hover" v-if="!isChecked">
                <div class="more">
                  <iconpark-icon name="iconmore"></iconpark-icon>
                </div>
                <t-dropdown-menu>
                  <t-dropdown-item @click="operate('checkbox', item, index)">
                    <iconpark-icon class="icons" name="iconcheckcorrect"></iconpark-icon>
                    <span style="margin-left: 12px;">多选</span>
                  </t-dropdown-item>
                  <t-dropdown-item @click="operate('move', item, index)">
                    <iconpark-icon class="icons" name="iconmove"></iconpark-icon>
                    <span style="margin-left: 12px;">移动分组</span>
                  </t-dropdown-item>
                  <t-dropdown-item class="dropdown-item-delete" @click="operate('delete', item, index)">
                    <iconpark-icon class="icons" name="icondelete" style="color:#D54941"></iconpark-icon>
                    <span style="margin-left: 12px;">删除</span>
                  </t-dropdown-item>
                </t-dropdown-menu>
              </t-dropdown>
              <!-- <div class="content">{{ item.data?.name || item.content }}</div> -->
            </div>
          </t-checkbox-group>
          <!-- 缺省页 -->
          <div v-else class="noMoreData">
            <img src="@renderer/assets/im/speed_noData.svg">
            <p>{{ t('im.public.addImgTip') }}</p>
            <t-button variant="outline" shape="round" @click="add" theme="primary">{{ t('im.public.addImg') }}</t-button>
            <!-- <button @click="add">{{ t('im.public.addImg') }}</button> -->
          </div>
        </div>
        <!-- <div v-else-if="props.defaultType === 'APP_ACCOUNT_FILE'" class="overflow-auto flex-wrap words files"> -->
        <!-- 文件 -->
        <div v-else-if="props.defaultType === 'APP_ACCOUNT_FILE'" class="files">
          <t-checkbox-group v-model="checkList" v-if="dataList.length">
            <div v-for="(item, index) in dataList" :index="index" class="item">
              <!-- @click="sendSingleMessage('APP_ACCOUNT_FILE', item)"
               <t-checkbox v-if="isChecked" :value="item.id"></t-checkbox> -->
              <div class="wordItem" style="flex:1;" @click="sendSingleMessage('APP_ACCOUNT_FILE', item)">
                <t-checkbox v-if="isChecked" :value="item.id" class="checkbox"></t-checkbox>
                <img class="img" :src="formatAssetsPath(item)" alt="">
                <div class="content" style="width:100%;">{{ extractOriginalFileName(item.content) }}</div>
              </div>
              <div class="wordFiles">
                <t-dropdown attach=".filesList" trigger="hover" :zIndex="1900">
                  <iconpark-icon class="icons_item" name="iconmore"></iconpark-icon>
                  <t-dropdown-menu>
                    <t-dropdown-item @click="operate('checkbox', item, index)">
                      <iconpark-icon class="icons" name="iconcheckcorrect"></iconpark-icon>
                      <span style="margin-left: 12px;">多选</span>
                    </t-dropdown-item>
                    <t-dropdown-item @click="operate('move', item, index)">
                      <iconpark-icon class="icons" name="iconmove"></iconpark-icon>
                      <span style="margin-left: 12px;">移动分组</span>
                    </t-dropdown-item>
                    <t-dropdown-item @click="operate('delete', item, index)">
                      <!-- <iconpark-icon class="icons" name="icondelete" style="color:#D54941"></iconpark-icon> -->
                      <iconpark-icon class="icons" name="icondelete"></iconpark-icon>
                      <span style="margin-left: 12px;">删除</span>
                    </t-dropdown-item>
                  </t-dropdown-menu>
                </t-dropdown>
              </div>
            </div>
          </t-checkbox-group>
          <!-- 缺省页 -->
          <div v-else class="noMoreData">
            <img src="@renderer/assets/im/speed_noData.svg">
            <p>{{ t('im.public.addFileTip') }}</p>
            <t-button variant="outline" shape="round" @click="add" theme="primary">{{ t('im.public.addFile') }}</t-button>
            <!-- <button @click="add">{{ t('im.public.addFile') }}</button> -->
          </div>
        </div>
        <!-- 地址 -->
        <div v-else-if="props.defaultType === 'APP_ACCOUNT_ADDRESS'" class="map-position">
          <t-checkbox-group v-model="checkList" v-if="dataList.length">
            <div v-for="(item, index) in dataList" :index="index" class="item">
              <div class="wordItem" @click="sendSingleMessage('APP_ACCOUNT_ADDRESS', item)">
                <t-checkbox v-if="isChecked" :value="item.id" class="checkbox"></t-checkbox>
                <!-- <img class="img" src="@renderer/assets/im/im_position.png" alt=""> -->
                <iconpark-icon name="iconorientation" class="icons"></iconpark-icon>
                <div class="content">{{ item.content }}</div>
                <!-- <div style="width: 0;flex: 1;">
                  <span :class="['APP_ACCOUNT_ADDRESS_CONTENT', item.isShowAllContent ? 'showAllContent' : 'content']">{{
                    item.content }}</span>
                </div> -->
                <!-- <t-button theme="primary" variant="text" style="padding: 0 5px;" v-if="item.isBeyond"
                  @click.stop="item.isShowAllContent = !item.isShowAllContent">{{ item.isShowAllContent ? '收起' : '展开'
                  }}</t-button> -->
              </div>
              <div class="wordFiles">
                <!-- <div class="map-edit">
                  <iconpark-icon name="iconedit" class="icon-edit"></iconpark-icon>
                </div> -->
                <t-dropdown attach=".filesList" trigger="hover">
                  <iconpark-icon class="icons_item" name="iconmore"></iconpark-icon>
                  <t-dropdown-menu>
                    <t-dropdown-item @click="operate('checkbox', item, index)">
                      <iconpark-icon class="icons" name="iconcheckcorrect"></iconpark-icon>
                      <span style="margin-left: 12px;">多选</span>
                    </t-dropdown-item>
                    <t-dropdown-item @click="operate('move', item, index)">
                      <iconpark-icon class="icons" name="iconmove"></iconpark-icon>
                      <span style="margin-left: 12px;">移动分组</span>
                    </t-dropdown-item>
                    <t-dropdown-item @click="handlerLookMap(item)">
                      <iconpark-icon name="iconorientation" class="icons"></iconpark-icon>
                      <span style="margin-left: 12px;">查看地图</span>
                    </t-dropdown-item>
                    <t-dropdown-item @click="operate('delete', item, index)">
                      <!-- <iconpark-icon class="icons" name="icondelete" style="color:#D54941"></iconpark-icon> -->
                      <iconpark-icon class="icons" name="icondelete"></iconpark-icon>
                      <span style="margin-left: 12px;">删除</span>
                    </t-dropdown-item>
                  </t-dropdown-menu>
                </t-dropdown>
              </div>
            </div>
          </t-checkbox-group>
          <!-- 缺省页 -->
          <div v-else class="noMoreData">
            <img src="@renderer/assets/im/speed_noData.svg">
            <p>{{ t('im.public.localCommon') }}</p>
            <!-- <button @click="add">{{ t('im.public.addCommon') }}</button> -->
          </div>
        </div>
      </div>
      <!-- <MapSelector v-model="showMap" :loc="mapConfig" only-show /> -->
      <BaiduMapSelector v-model:visible="showMap" :loc="mapConfig" only-show />
      <!-- 多选操作栏 -->
      <div class="footer-box" v-if="isChecked">
        <div class="flex-box">
          <div class="btn" style="margin-left:-5px;">
            <t-checkbox :checked="checkList.length === dataList.length" @change="operateAll('selectAll')"
              :indeterminate="dataList.length > checkList.length && checkList.length"
              style='color:#1A2139;position:absolute;top:13px;left:16px;'>全选
              <!-- {{ checkList.length === dataList.length ? '取消全选' : '全选' }} -->
            </t-checkbox>
            <span class="checkBtn" variant="text" theme="primary" @click="operateAll('exitAll')" :style="checks">
              退出多选
            </span>
          </div>
          <div class="btn" style="margin-right:-12px;">
            <span class="m4 " variant="outline" theme="danger" @click="operateAll('deleteAll')" style="color:#D54941">
              删除
            </span>
            <span class="m4 " variant="outline" theme="primary" @click="operateAll('moveAll')" style="color:#4D5EFF">
              移动分组
            </span>
            <!-- <t-button v-if="showSendAll" class="m4 sendBtn" theme="primary" @click="operateAll('sendAll')">
              <template #content>发送</template>
            </t-button> -->
          </div>
        </div>
      </div>
      <!-- <div v-show="contextmenuObj.contextmenuVisible"
        :style="{ left: contextmenuObj.left + 'px', top: contextmenuObj.top + 'px' }" class="contextmenu">
        <template v-if="contextmenuObj.isGroup">
          <t-button variant="text" @click="operateGroup('edit')">
            <template #icon><img src="@renderer/assets/im/im_setting_edit.svg"></template>
            <template #content>编辑</template>
          </t-button>
          <t-button variant="text" theme="danger" @click="operateGroup('delete')">
            <template #icon><img src="@renderer/assets/im/delete_red.svg"></template>
            <template #content>删除</template>
          </t-button>
        </template>
        <template v-else>
          <t-button variant="text" @click="operate('checkbox')">
            <template #icon><img src="@renderer/assets/im/checkbox.svg"></template>
            <template #content>多选</template>
          </t-button>
          <t-button v-if="showEdit" variant="text" @click="operate('edit')">
            <template #icon><img src="@renderer/assets/im/im_setting_edit.svg"></template>
            <template #content>编辑</template>
          </t-button>
          <t-button variant="text" @click="operate('move')">
            <template #icon><img src="@renderer/assets/im/move.svg"></template>
            <template #content>移动</template>
          </t-button>
          <t-button variant="text" theme="danger" @click="operate('delete')">
            <template #icon><img src="@renderer/assets/im/delete_red.svg"></template>
            <template #content>删除</template>
          </t-button>
        </template>
      </div> -->
    </div>
    <!-- 分組管理彈框 -->
    <t-dialog v-model:visible="groupDrawer" :closeBtn="false" size="100%" top="80px" left="260px"
      :closeOnOverlayClick="false" :class="props.defaultType === 'APP_ACCOUNT_EMO'?'emoFen groupDrawer':'groupDrawer'">
      <template #header>
        <div class="header">
          <div class="title">分组管理</div>
          <div style="display: flex;align-items: center;justify-content: flex-end;gap: 8px;">
            <!-- <t-button @click="operateGroup('add')">
              <template #content>新建</template>
            </t-button> -->
            <img @click.stop="groupDrawer = false" style="cursor: pointer;" src="@renderer/assets/im/icon_error.svg"
              alt="">
          </div>
        </div>
      </template>
      <template #body>
        <div class="body">
          <draggable class="draggable" item-key="type" ghost-class="ghost" chosen-class="chosenClass" filter=".unmover"
            :list="tabList" @end="onDraggableEnd" :move="onMove">
            <template #item="{ element, index }">
              <div :class="['item', defaultList.includes(element.type) ? 'unmover' : '']">
                <t-button shape="square" variant="text" @click.capture="openContextmenu($event, element, index, true)">
                  <!--  原来的右击事件 -->
                  <template #icon><img src="@renderer/assets/im/group_drag.png"></template>
                  <template #content>{{ element.typeName }}
                    <span style="position:absolute;right:-2px;" class="itemset">
                      <iconpark-icon name="iconedit" class="wordicon" @click="operateGroup('edit')"></iconpark-icon>
                      <iconpark-icon name="icondelete" class="wordicon" @click="operateGroup('delete')"></iconpark-icon>
                      <!-- <img src="@renderer/assets/im/im_setting_edit.svg" @click="operateGroup('edit')">
                      <img src="@renderer/assets/im/speed_delete.svg" @click="operateGroup('delete')"> -->
                    </span>
                  </template>
                </t-button>
              </div>
            </template>
          </draggable>
        </div>
      </template>
      <template #footer>
        <div class="footer">
          <t-button variant="outline" theme="default" @click="groupDrawer = false">返回</t-button>
          <t-button @click="operateGroup('add')"><template #content>新建分组</template></t-button>
        </div>
      </template>
    </t-dialog>
    <t-dialog class="contentDrawer" v-model:visible="contentDrawer" :closeBtn="true" top="80px"
      :closeOnOverlayClick="false">
      <template #header>
        <div class="header">
          <div class="title">{{ contentDrawerType === 'add' ? '添加常用语' : '编辑常用语' }}</div>
        </div>
      </template>
      <template #body>
        <div class="body">
          <t-textarea v-model="form.content" :autosize="{ minRows: 5, maxRows: 5 }" :maxlength="200"
            placeholder="请输入内容" />
        </div>
      </template>
      <template #confirmBtn>
        <t-button @click="onClickContentDrawer" :disabled="!form.content">
          <template #content>保存</template>
        </t-button>
      </template>
    </t-dialog>
    <!-- 移动分组 -->
    <t-dialog v-model:visible="visibleDialog" top="80px" :close-btn="false" :attach="props.defaultType === 'APP_ACCOUNT_EMO'?'body':''"
    :class="props.defaultType === 'APP_ACCOUNT_EMO'?'emoFen visibleDialog':'visibleDialog'">
      <template #header>
        <span style='padding:24px 16px;width:516px'>{{ dialogType === 'move'
          ? '移动到'
          : dialogType === 'add' ? '新建分组' : '编辑分组'
        }}</span>
        <div @click="visibleDialog = false" class="iconhover" style="position:absolute;right:16px;top:24px;">
          <img style="height:16px;" src="@/assets/im/im_close.png">
        </div>
      </template>
      <template #body>
        <span :style="{padding:'0 16px',width:'516px',display:'flex',justifyContent:'center',height:props.defaultType === 'APP_ACCOUNT_EMO'?'190px':'120px'}">
          <t-form ref="formRef" :data="form" :label-width="0" statusIcon style="width:100%">
            <t-form-item label="" name="groupName" v-if="dialogType === 'add' || dialogType === 'edit'">
              <t-input v-model="form.groupName" :maxlength="8" show-limit-number clearable
                placeholder="请输入分组名称"></t-input>
            </t-form-item>
            <t-form-item label="" name="groupValue" v-if="dialogType === 'move'">
              <t-select v-replace-svg  v-model="form.groupValue" placeholder="选择分组">
                <t-option v-for="item in attachmentData" :key="item.type" :disabled="item.type == tabActive"
                  :label="item.typeName" :value="item.type" />
              </t-select>
            </t-form-item>
          </t-form>
        </span>
      </template>
      <template #confirmBtn>
        <span style="padding:50px 24px 0 16px;width:516px;">
          <t-button @click="onClickConfirm" :disabled="!form.groupName && !form.groupValue">
            <template #content>{{ dialogType === 'move' ? '移动到此' : '保存' }}</template>
          </t-button>
        </span>
      </template>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import draggable from "vuedraggable";
import { DialogPlugin, MessagePlugin, LoadingPlugin } from "tdesign-vue-next";
import to from "await-to-js";
import upload from '@renderer/views/message/components/upload.vue';
import { getUserConfig, putUserConfig } from "@renderer/api/im/api"
import { EmojiData, EmojiGifData, snakeEmoji } from '@/assets/im/emoji'
import { getProfilesInfo } from "@/utils/auth";
import { replaceUrlDomain } from "@renderer/utils/assist";
import _ from 'lodash';
import excelPng from "@renderer/assets/im/file-icon-excel.png";
import pdfPng from "@renderer/assets/im/file-icon-pdf.png";
import pptPng from "@renderer/assets/im/file-icon-ppt.png";
import wordPng from "@renderer/assets/im/file-icon-word.png";
import otherPng from "@renderer/assets/im/file-icon-other.png";
import { getFileType } from "@/views/message/service/utils";
import { getSrcThumbnail } from '../service/msgUtils';
// import MapSelector from "@/views/square/components/MapSelector.vue";
import BaiduMapSelector from '@renderer/components/common/map/BaiduMapSelector.vue';
import clipboard3 from "vue-clipboard3"//复制的插件
import { filterImgSrc } from "@renderer/views/clouddisk/clouddiskhome/fileType";
import { openExternalMap } from '@renderer/components/common/map/utils';
import { extractOriginalFileName } from "@/views/square/utils/upload"

const { t } = useI18n();
const props = defineProps({
  info: { type: Object, default: () => { } },
  type: { type: String, default: '' },
  defaultType: { type: String, default: '' },
  // APP_ACCOUNT_EMO // 表情设置
  // APP_ACCOUNT_PICTURE // 图片设置
  // APP_ACCOUNT_FILE // 文件设置
  // APP_ACCOUNT_ADDRESS // 地址设置
  // APP_ACCOUNT_WORDS // 文字设置
})
const emits = defineEmits(['sendMessage', 'onSelectEmoji']);

const defaultList = ['DEFAULT_COCO', 'DEFAULT', 'SNAKE_EMOJI', 'defaultEmojiGroup', 'defaultGroup'];
const offsetBox = ref(null)
const tabActive = ref('')
const dataList = ref([])
const tabList = ref([])
const attachmentData = ref([])
const contextmenuObj = reactive({//右擊彈窗內容
  isGroup: false,
  contextmenuVisible: false,
  left: 0,
  top: 0
})
const activeItem = ref({})
const checkList = ref([])
const isChecked = ref(false)
const groupDrawer = ref(false)
const dialogType = ref('')
const contentDrawerType = ref('')
const visibleDialog = ref(false)
const form = reactive({
  groupName: '',
  groupValue: '',
  content: ''
})
const formRef = ref(null)
const isMoveAll = ref(false)
const contentDrawer = ref(false)
const selfInfo = getProfilesInfo();
const loadingAttachInstance = ref('');
const showMoveTabBtn = ref(false);
// 是否展示地图
const showMap = ref(false);
interface IMapConfig {
  latLng: any,
  name: 'string'
}
// 地图参数
const mapConfig = ref<IMapConfig>();
const fileUploadRef = ref(null);

watch(() => props.type, (newVal, oldVal) => {
  if (newVal) {
    console.log(newVal, oldVal, props.defaultType)
    groupDrawer.value = false
    contentDrawer.value = false
    visibleDialog.value = false
    if (newVal === props.defaultType) {
      getAllData()
    }
  } else {
    init()
  }
})
watch(() => tabList.value, (val) => {
  if (val?.length) {
    nextTick(() => {
      const tab = document.getElementById('tab-box_' + props.defaultType);
      const personal = document.getElementById('personal_tabs_width' + props.defaultType);
      showMoveTabBtn.value = personal.clientWidth > tab.clientWidth;
    })
  }
})

const moveTabList = (type) => {
  let element = document.getElementById('personal_tabs_' + props.defaultType);
  let offsetWidth = document.querySelector('.personal_tabs_item_' + props.defaultType)?.offsetWidth;
  if (type === 'left') {
    if (element.scrollLeft === 0) return;
    element.scrollTo(element.scrollLeft - offsetWidth, 0)
  } else {
    element.scrollTo(element.scrollLeft + offsetWidth, 0)
  }
}

const formatAssetsPath = (item) => {
  let iconNameList = new Map([
    ['excel', { value: 'excel', label: excelPng }],
    ['pdf', { value: 'pdf', label: pdfPng }],
    ['word', { value: 'word', label: wordPng }],
    ['ppt', { value: 'ppt', label: pptPng }],
    ['other', { value: 'other', label: otherPng }],
  ])
  let type = ''
  if (['xlsx', 'xls', 'csv'].includes(item?.data?.type)) {
    type = 'excel'
  } else if (['pdf'].includes(item?.data?.type)) {
    type = 'pdf'
  } else if (['ppt', 'pptx', 'pps', 'ppsx'].includes(item?.data?.type)) {
    type = 'ppt'
  } else if (['doc', 'docx', 'csv'].includes(item?.data?.type)) {
    type = 'word'
  } else {
    type = 'other'
  }
  return iconNameList.get(type)?.label || otherPng
}
const onMove = (e) => {
  if (defaultList.includes(e.relatedContext.element.type)) return false;
}
const onDraggableEnd = async () => {
  let tabListTemp = [...tabList.value]
  if (props.defaultType === 'APP_ACCOUNT_EMO') tabListTemp?.shift()
  console.log('onDraggableEnd tabListTemp', tabListTemp);
  attachmentData.value = tabListTemp
  const [err] = await setAllData()
  if (err) return;
  MessagePlugin.success('操作成功!')
  getAllData()
}

const onSelectEmoji = (emojiText: string, src) => {
  emits('onSelectEmoji', emojiText, src);
  // close({}, false)
}
const onSelectEmojiCOCO = (type, emojiText: string, src) => {
  sendSingleMessage(type, {height: 120, width: 120, emojiText, src});
}
const uploadLoading = (v) => {
  loadingAttachInstance.value = LoadingPlugin({
    attach: () => offsetBox.value,
    showOverlay: true,
    size: '20px',
  });
}
const getFiles = async (v) => {
  loadingAttachInstance.value.hide();
  console.log('getFiles', v)
  let startId = new Date().getTime()
  let data = v?.map(item => {
    let type = getFileType(item.name)
    startId++;
    if (props.defaultType === 'APP_ACCOUNT_FILE') {
      return {
        id: startId.toString(),
        content: item.name,
        data: {
          name: item.name,
          fileId: item.fileId,
          fileName: item.name,
          fileUrl: item.url,
          type: type,
          size: item.size
        }
      }
    } else {
      return {
        id: startId.toString(),
        content: getSrcThumbnail(item.url),
        data: {
          name: item.name,
          type: type,
          imgUrl: item.url,
          localUrl: '',
          size: item.size,
          height: item.height || 0,
          width: item.width || 0,
          thumbnail: getSrcThumbnail(item.url),
        }
      }
    }
  })
  attachmentData.value?.forEach(items => {
    if (items.type === tabActive.value) {
      if (Array.isArray(items?.data)) {
        /**
         * fix: https://www.tapd.cn/********/bugtrace/bugs/view/11********001045688
         */
        items.data = _.uniqBy([
          ...data,
          ...items?.data
        ], 'id')
      } else {
        items.data = data
      }
    }
  })
  const [err] = await setAllData()
  if (err) return;
  getAllData()
}
// 删除弹框
const openDeleteDialog = (isDeleteGroup = false, isDeleteAll = false) => {
  //很奇怪confirm弹窗会把 activeItem.value, tabActive.value数据清空
  const activeIt = activeItem.value
  const tabAct = tabActive.value
  // let bodyText = showEdit ? '是否删除该常用语' : '确认删除吗?'
  let bodyText = '删除数据后不可恢复'
  const confirmDia = DialogPlugin.confirm({
    header: '确认删除?',
    body: isDeleteGroup ? '删除分组将导致清空分组内的数据，且不可恢复' : bodyText,
    theme: 'warning',
    // attach: `.${props.defaultType}`,
    confirmBtn: {
      content: '删除',
      theme: 'danger'
    },
    zIndex: 9999,
    top: '80px',
    attach: 'body',
    onConfirm: async () => {
      if (isDeleteGroup) {
        // attachmentData.value = null
        const index = attachmentData.value.findIndex(it => it.type === activeItem.value?.type);
        attachmentData.value?.splice(index, 1);
        if (activeItem.value?.type === tabAct) {
          tabActive.value = ''
        }
      } else {
        attachmentData.value?.forEach(items => {
          if (items?.type === tabAct) {
            if (isDeleteAll) {
              items.data = items.data?.filter(item => {
                return checkList.value.every(id => id !== item.id)
              })
            } else {
              items.data?.splice(activeIt?.activeItemIndex, 1)
            }
          }
        })
        checkList.value = []
      }
      confirmDia.destroy();
      // 保存消息实例并后续关闭
      // MessagePlugin.info('删除中...');
      const [err] = await setAllData()
      if (err) return;
      getAllData()
      // MessagePlugin.closeAll(); // 关闭加载提示
      await MessagePlugin.success('删除成功!');
    },
    onClose: () => {
      confirmDia.hide();
    },
  });
};
const onClickContentDrawer = async () => {
  if (contentDrawerType.value === 'add') {
    // 1.常用语、表情、位置：999个 2.文件、图片：99个
    if (dataList.value.length >= 999) return
    let data = {
      id: new Date().getTime().toString(),
      content: form.content,
      data: {
        text: form.content,
        atInfo: null
      }
    }
    attachmentData.value?.forEach(items => {
      if (items.type === tabActive.value) {
        if (Array.isArray(items?.data)) {
          items.data?.unshift(data)
        } else {
          items.data = [data]
        }
      }
    })
  } else if (contentDrawerType.value === 'edit') {
    let activeItemTemp = { ...activeItem.value }
    delete activeItemTemp.activeItemIndex
    attachmentData.value?.forEach(items => {
      if (items.type === tabActive.value) {
        items.data?.splice(activeItem.value?.activeItemIndex, 1, {
          ...activeItemTemp,
          content: form.content,
          data: {
            ...activeItemTemp.data,
            text: form.content,
          }
        })
      }
    })
  }
  const [err] = await setAllData()
  if (err) return;
  getAllData()
  MessagePlugin.success('操作成功!')
  form.content = ''
  contentDrawer.value = false
}
const onClickConfirm = async () => {
  if (dialogType.value === 'add') {
    attachmentData.value?.push({
      type: new Date().getTime().toString(),
      typeName: form.groupName,
      data: []
    })
  } else if (dialogType.value === 'edit') {
    let activeItemTemp = { ...activeItem.value }
    delete activeItemTemp.activeItemIndex
    attachmentData.value?.splice(activeItem.value?.activeItemIndex, 1, {
      ...activeItemTemp,
      typeName: form.groupName,
    })
  } else if (dialogType.value === 'move') {
    let moveData = [], filterData = []
    let dataItems = attachmentData.value?.find(item => item.type === tabActive.value)
    if (isMoveAll.value) {
      filterData = dataItems.data?.filter(item => {
        return checkList.value.every(id => id !== item.id)
      })
      moveData = _.differenceBy(dataItems.data, filterData, 'id')
    } else {
      filterData = dataItems.data?.filter(item => {
        return activeItem.value?.id !== item.id
      })
      let temp = { ...activeItem.value }
      delete temp.activeItemIndex
      moveData = [temp]
    }
    attachmentData.value?.forEach(items => {
      if (items?.type === tabActive.value) {
        items.data = filterData
      }
      if (items?.type === form.groupValue) {
        if (Array.isArray(items?.data)) {
          items.data = [
            ...moveData,
            ...items?.data
          ]
        } else {
          items.data = moveData
        }
      }
    })
    checkList.value = []
  }
  let [err, res] = await setAllData()
  if (!err) {
    getAllData()
    MessagePlugin.success('操作成功!')
    formRef.value.reset();
    visibleDialog.value = false
  }
}
const add = () => {
  if (tabList.value?.length === 0) {
    return MessagePlugin.info('请先创建分组!');
  }
  if (props.defaultType === 'APP_ACCOUNT_EMO' && ['DEFAULT', 'DEFAULT_COCO', 'SNAKE_EMOJI'].includes(tabActive.value)) {
    return MessagePlugin.info('默认分组不可编辑，请切换分组!');
  }
  if (props.defaultType === 'APP_ACCOUNT_WORDS') {
    contentDrawerType.value = 'add'
    form.content = ''
    contentDrawer.value = true
  }
  if (props.defaultType === 'APP_ACCOUNT_PICTURE' || props.defaultType === 'APP_ACCOUNT_FILE') {
    fileUploadRef.value.uploadFile();
  }
}
const set = () => {
  groupDrawer.value = true
}
// 分组操作
const operateGroup = (type) => {
  console.log('operateGroup', type, activeItem.value)
  if (type === 'edit') {
    if (props.defaultType === 'APP_ACCOUNT_EMO' && ['DEFAULT', 'DEFAULT_COCO', 'SNAKE_EMOJI'].includes(activeItem.value?.type)) {
      return MessagePlugin.info('默认分组不可操作!')
    }
    dialogType.value = 'edit'
    form.groupName = activeItem.value?.typeName
    visibleDialog.value = true
  } else if (type === 'delete') {
    if (props.defaultType === 'APP_ACCOUNT_EMO' && ['DEFAULT', 'DEFAULT_COCO', 'SNAKE_EMOJI'].includes(activeItem.value?.type)) {
      return MessagePlugin.info('默认分组不可操作!')
    }
    openDeleteDialog(true)
  } else if (type === 'add') {
    dialogType.value = 'add'
    form.groupName = ''
    form.groupValue = ''
    visibleDialog.value = true
  }
}
// 多选操作
const checks = ref({ marginLeft: '66px' })
// watch(()=>checkList.value.length,()=>{
//   checkList.value.length === dataList.value.length ?checks.value.marginLeft='94px':checks.value.marginLeft='66px'
// })
const operateAll = (type) => {
  console.log('operateAll', type, checkList.value)
  if (checkList.value?.length === 0 && type !== 'exitAll' && type !== 'selectAll') {
    return MessagePlugin.info('至少勾选一个!')
  }
  if (type === 'exitAll') {
    isChecked.value = false
    checkList.value = []
  } else if (type === 'selectAll') {
    checkList.value =
      checkList.value?.length === dataList.value?.length
        ? []
        : dataList.value?.map((item, index) => item.id)
  } else if (type === 'sendAll') {
    sendAllMessage()
  } else if (type === 'moveAll') {
    dialogType.value = 'move'
    isMoveAll.value = true
    visibleDialog.value = true
  } else if (type === 'deleteAll') {
    openDeleteDialog(false, true)
  }
}
// 分组内右键操作
const operate = (type, item?: any, index?: number, isGroup = false) => {
  if (item) {
    let activeItemIndex = (props.defaultType === 'APP_ACCOUNT_EMO' && isGroup)
      ? index - 1
      : index
    activeItem.value = { ...item, activeItemIndex };
  }
  console.log('operate', type, item, activeItem.value)

  if (type === 'checkbox') {
    isChecked.value = true;
    document.querySelector('.page-box').style.height = '302px';
    document.querySelector('.files').style.height = '302px';
    document.querySelector('.picture').style.height = '302px';
  } else if (type === 'edit') {
    contentDrawerType.value = 'edit'
    form.content = activeItem.value?.content
    contentDrawer.value = true
  } else if (type === 'move') {
    dialogType.value = 'move'
    isMoveAll.value = false
    visibleDialog.value = true
  } else if (type === 'delete') {
    openDeleteDialog(false, false)
  }
}

const { toClipboard } = clipboard3()
const copyword = async (link) => {
  console.log(link);
  try {
    await toClipboard(link)
    MessagePlugin.success('复制成功')
  } catch (error) {
    MessagePlugin.error('复制失败')
  }
}

const closeContextmenuVisible = () => {
  contextmenuObj.contextmenuVisible = false
}
//右擊打開彈窗
const openContextmenu = (e, item, index, isGroup = false) => {
  // emoji类型并且右键分组时添加了两个默认元素
  let activeItemIndex = attachmentData.value.findIndex(it=> it.type === item.type)
  activeItem.value = {
    ...item,
    activeItemIndex
  }
  contextmenuObj.left = e?.pageX - offsetBox.value.getBoundingClientRect().left + 10
  contextmenuObj.top = e?.pageY - offsetBox.value.getBoundingClientRect().top + 10
  contextmenuObj.isGroup = isGroup
  contextmenuObj.contextmenuVisible = true
}
//获取数据
const getDataList = () => {
  dataList.value = _.uniqBy(tabList.value?.find(item => item.type === tabActive.value)?.data || [], 'id')
  nextTick(() => {
    if (props.defaultType === 'APP_ACCOUNT_WORDS' || props.defaultType === 'APP_ACCOUNT_ADDRESS') {
      dataList.value?.forEach((item, index) => {
        let box = document.getElementsByClassName(props.defaultType + '_CONTENT_BOX')?.[index];
        let content = document.getElementsByClassName(props.defaultType + '_CONTENT')?.[index];
        // content很多时候都是undefined，所以需要判断一下
        //默认两行内超出部分省略号，所以不能以width为基准比较
        if (content && ((content.scrollHeight || content.clientHeight || content.offsetHeight) + 5 > box.clientHeight)) {
          // if ((content.scrollWidth || content.clientWidth || content.offsetWidth) + 10 > box.clientWidth) {
          item.isBeyond = true;
        } else {
          item.isBeyond = false;
        }
        item.isShowAllContent = false;
      });
    }
  });
}
const getAllData = () => {
  console.log('===>111111', 111111);
  
  getUserConfig(props.defaultType).then(res => {
    tabList.value = []
    if (props.defaultType === 'APP_ACCOUNT_EMO') {
      tabList.value = [
        {
          type: 'DEFAULT_COCO',
          typeName: 'COCO',
          data: []
        },
        {
          type: 'DEFAULT',
          typeName: '默认',
          data: []
        },
        {
          type: 'SNAKE_EMOJI',
          typeName: '蛇年贺岁',
          data: []
        },
      ]
    }
    attachmentData.value = res?.data?.attachment?.data.filter((item) => !['DEFAULT_COCO','DEFAULT','SNAKE_EMOJI'].includes(item.type)) || []
    attachmentData.value.map(item => {
      item.data?.map(data => {
        if (props.defaultType === 'APP_ACCOUNT_FILE') {
          let { fileUrl, type } = data.data;
          fileUrl = replaceUrlDomain(fileUrl, type)
          data.data.fileUrl = fileUrl
        } else if (['APP_ACCOUNT_PICTURE','APP_ACCOUNT_EMO'].includes(props.defaultType) ) {
          let { imgUrl, thumbnail, type } = data.data;
          imgUrl = replaceUrlDomain(imgUrl, type)
          thumbnail = replaceUrlDomain(thumbnail, type)
          data.data.imgUrl = imgUrl
          data.data.thumbnail = thumbnail
        }
        return data
      })
      return item
    })
    const list = [
      ...tabList.value,
      ...attachmentData.value
    ]
    const info = {}
    tabList.value = list.filter( v => {
      if (!info[v.type]) {
        info[v.type] = true
        return v;
      }
    })
    if (tabList.value?.length) tabActive.value = (tabActive.value || tabList.value[0]?.type)
    getDataList()
  })
}
const setAllData = async () => {
  const [err, res] = await to(putUserConfig({
    user_id: selfInfo.openid,
    typ: props.defaultType,
    attachment: {
      type: props.defaultType,
      data: attachmentData.value
    }
  }))
  return [err, res]
}
//发送消息
const sendSingleMessage = (type, item) => {
  console.log('sendSingleMessage', type, item)
  // 选中复选框
  if ((isChecked.value || contextmenuObj.contextmenuVisible) && ['APP_ACCOUNT_PICTURE'].includes(type) || (isChecked.value || contextmenuObj.contextmenuVisible) && ['APP_ACCOUNT_WORDS'].includes(type)) {
    const index = checkList.value.findIndex(v => v === item.id);
    if (index === -1) {
      checkList.value.push(item.id);
    } else {
      checkList.value.splice(index, 1);
    }
    return
  }
  if (isChecked.value || contextmenuObj.contextmenuVisible) return
  let sendData = {
    type,
    data: []
  }
  if (['APP_ACCOUNT_EMO', 'APP_ACCOUNT_PICTURE', 'APP_ACCOUNT_FILE'].includes(type)) {
    if (type === 'APP_ACCOUNT_FILE') {
      sendData.data = [{
        ...item.data,
        fileName: extractOriginalFileName(item.data?.fileName),
        name: extractOriginalFileName(item.data?.name || item.data?.fileName)
      }]
    } else {
      sendData.data = [item.data]
    }
  } else if (['APP_ACCOUNT_WORDS'].includes(type)) {
    sendData.data = [item.content]
  } else if (['APP_ACCOUNT_ADDRESS'].includes(type)) {
    sendData.data = [item.data]
  } else if (['emoji_coco_image'].includes(type)) {
    sendData.data = [item]
  }
  close(sendData, false)
}
const sendAllMessage = () => {
  let dataItems = attachmentData.value?.find(item => item.type === tabActive.value)
  let filterData = dataItems.data?.filter(item => {
    return checkList.value.some(id => id === item.id)
  }).map(item => {
    return item.data
  })
  let sendData = {
    type: props.defaultType,
    data: filterData
  }
  close(sendData, false)
}
const handlerChange = (item) => {
  console.log('tabChange', item)
  tabActive.value = item.type;
  init(false)
  getDataList()
};
const close = (sendData, isPageInit = true) => {
  init(isPageInit)
  emits('sendMessage', sendData);
};

const init = (isPageInit = true) => {
  contextmenuObj.isGroup = false
  contextmenuObj.contextmenuVisible = false
  contextmenuObj.left = 0
  contextmenuObj.top = 0
  activeItem.value = {}
  checkList.value = []
  isChecked.value = false
  if (isPageInit) {
    tabActive.value = ''
  }
}

// 查看地图
const handlerLookMap = (item) => {
  // console.log('item**',item);
  const { data } = item;
  if (!data) return;
  const { longitude, latitude } = data.latLng;
  // const location = `${data.latLng.longitude},${data.latLng.latitude}`;
  // const mapKey = __APP_ENV__.VITE_MAP_KEY;
  openExternalMap({ lng: longitude, lat: latitude, name: data.title });
  // const staticmaps = `https://uri.amap.com/marker?position=${longitude},${latitude}&name=${data.title}`;
  // shell.openExternal(staticmaps);
  // const data = item?.data || {};
  // mapConfig.value = {
  //   latLng: data.latLng,
  //   name: data.title,
  // };
  // showMap.value = true;
}
defineExpose({
  getDataList,
  getAllData,
})
onMounted(() => {
  console.log('===>11111', 2222);
  
  getAllData()
})
</script>
<style lang="less" scoped>
::-webkit-scrollbar {
  width: 2px;
  height: 3px;
}

/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  border-radius: 3px;
  background: rgba(0, 0, 0, 0.1);
  -webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.5);
}

.t-button {
  min-width: auto;
  border: none !important;
}

.page-box {
  box-sizing: border-box;
  //width: 448px;
  //height: 288px8
  width: 518px;
  height: 271px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  //background-image: url("@renderer/assets/im/speedModeBg.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  border-radius: 16px;
  box-shadow: 0px 8px 16px 0px #0000001F;
}

.header-box {
  padding: 0 8px;
  height: 36px;
  display: flex;
  background: #FFFFFF;
  border-radius: 16px;
  width: 517px;

  .flex-box {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #ECEFF5;

    .btn {
      margin: 4px 0;
    }
  }

  .m4 {
    margin: 0 4px;
  }

  .mb-3 {
    margin-bottom: 3px;
  }

  .btn .t-button {
    padding: 0 15px;

    img {
      width: 16px;
    }
  }

  :deep(.tab-box) {
    width: 0;
    flex: 1;
    color: #1A2139;

    .t-tabs__bar.t-is-top {
      //height: 0;
    }

    .t-tabs__nav-container.t-is-top::after {
      content: none;
    }

    .t-tabs__nav-wrap,
    .t-tabs__nav-item {
      height: 35px;
    }

    .t-tabs__operations {
      .t-tabs__btn--left {
        box-shadow: none;
        background-color: #FFFFFF;
        border: none;
      }

      .t-tabs__btn {
        height: 35px;
      }
    }

    .t-tabs__operations--right .t-tabs__btn:first-child {
      box-shadow: none;
      background-color: #FFFFFF;
      border: none;
    }
  }

  .personal_tabs_width {
    display: flex;
    align-items: center;
    gap: 5px;
  }

  .personal_tabs::-webkit-scrollbar {
    display: none;
  }

  .personal_tabs {
    // width: 100%;
    background-color: #FFFFFF;
    // border-bottom: 1px solid var(--divider-kyy_color_divider_light, #ECEFF5);
    display: flex;
    align-items: center;
    gap: 5px;
    overflow: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;

    .default-tab-item {
      display: flex;
      flex-direction: column;
      align-items: center;

      .t-button {
        padding: 0 10px;
        color: var(--kyy_color_tabbar_item_text, #1a2139);
        text-align: center;

        /* kyy_fontSize_2/regular */
        font-family: PingFang SC;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 24px;
        /* 157.143% */
        border: none !important;
      }

      .tab-item-border {
        width: 28px;
        height: 3px;
        border-radius: 1.5px;
        background: transparent;
      }
    }

    .active-tab-item {
      .t-button {
        font-weight: 600;
        color: var(--brand-kyy-color-brand-default, #4d5eff) !important;
      }

      .tab-item-border {
        width: 16px;
        height: 3px;
        border-radius: 1.5px;
        background: var(--brand-kyy-color-brand-default, #4d5eff);
      }
    }
  }
}

.footer-box {
  padding: 12px 24px 12px 24px;
  height: 48px;
  display: flex;
  width: 516px;
  background: var(--bg-kyy_color_bg_deep, #F5F8FE);
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 16px 16px 0 0;

  .flex-box {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .t-button {
      width: 28px;
      height: 28px;
      padding: 0 4px;
    }
  }

  .m4 {
    margin: 0 4px;
  }

  .btn .t-button {
    padding: 0 15px;

    img {
      width: 24px;
    }
  }

  .checkBtn {
    padding: 0 !important;
    color: var(--color-button_text_brand-kyy_color_button_text_brand_font_default, #4D5EFF);
    text-align: center;
    font-size: 14px;
    font-style: normal;
    line-height: 22px;
    margin-left: 16px;
    background: var(--bg-kyy_color_bg_deep, #F5F8FE);
    font-weight: normal;
    /* 157.143% */
  }

  .moveBtn {
    height: 32px;
    min-height: 32px;
    max-height: 32px;
    padding: 0px 16px;
    border-radius: var(--radius-kyy_radius_button_s, 4px) !important;
    border: 1px solid var(--color-button_secondaryBrand-kyy_color_button_secondaryBrand_border_dedault, #4D5EFF) !important;
    background: var(--color-button_secondaryBrand-kyy_color_button_secondrayBorder_bg_default, #EAECFF) !important;
    color: var(--color-button_secondaryBrand-kyy_color_button_secondrayBorder_text_default, #4D5EFF) !important;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    /* 157.143% */
  }

  .deleteBtn {
    height: 32px;
    min-height: 32px;
    max-height: 32px;
    padding: 0px 16px;
    border-radius: var(--radius-kyy_radius_button_s, 4px) !important;
    border: 1px solid var(--color-button_secondaryError-kyy_color_button_secondaryError_border_dedault, #D54941) !important;
    background: var(--color-button_secondaryError-kyy_color_button_secondrayError_bg_default, #FDF5F6) !important;
    color: var(--color-button_secondaryError-kyy_color_button_secondrayError_text_default, #D54941) !important;

    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    /* 157.143% */
  }

  .sendBtn {
    height: 32px;
    min-width: 80px;
    min-height: 32px;
    max-height: 32px;
    padding: 0px 16px;
    border-radius: var(--radius-kyy_radius_button_s, 4px) !important;
    background: var(--color-button_primary-kyy_color_button_primary_bg_default, #4D5EFF) !important;
    color: var(--color-button_primary-kyy_color_button_primary_text, #FFF) !important;
    text-align: center;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px;
    /* 157.143% */
  }

  :deep(.tab-box) {
    width: 0;
    flex: 1;

    .t-tabs__bar.t-is-bottom {
      height: 0;
    }

    .t-tabs__nav-container.t-is-bottom::after {
      content: none;
    }

    .t-tabs__nav-wrap,
    .t-tabs__nav-item {
      height: 35px;
    }

    .t-tabs__operations {
      .t-tabs__btn--left {
        box-shadow: none;
        background-color: #FFFFFF;
        border: none;
      }

      .t-tabs__btn {
        height: 35px;
      }
    }

    .t-tabs__operations--right .t-tabs__btn:first-child {
      box-shadow: none;
      background-color: #FFFFFF;
      border: none;
    }
  }
}

.content-box {
  padding: 0 5px 0 8px;
  // height: calc(100% - 45px);
  height: calc(100% - 35px);
  overflow: hidden;

  &>.emoji {
    :deep(.t-checkbox-group) {
      gap: 0;
      align-content: flex-start;

      .t-checkbox {
        position: absolute;
        top: 0;
        right: 0;
        z-index: 99;

        .t-checkbox__label {
          margin-left: 0;
        }
      }
    }
  }

  &>.words {
    :deep(.t-checkbox-group) {
      gap: 0;
      display: block;
    }
  }

  .overflow-auto {
    overflow: auto;
    height: 100%;
  }

  .flex-wrap {
    display: flex;
    flex-wrap: wrap;
  }

  .emoji {
    .item {
      margin: 2px;
      position: relative;

      :deep(.t-image__error) {
        .t-space-item {
          font-size: 8px;
        }
      }

      .content {
        width: 70px;
        font-size: 10px;

        font-weight: 400;
        text-align: left;
        color: #13161b;
        line-height: 22px;
        text-align: center;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }

  .words {
    width: 100%;
    height: 227px;

    .item {
      width: 493px;
      background: #ffffff;
      //margin-bottom: 8px;
      padding: 0px 8px;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      border-bottom: 1px solid #ECEFF5;
      height: auto;
      position: relative;

      .wordItem {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        width: 100%;

        // position:relative;
        .img {
          width: 20px;
          height: 20px;
          margin-right: 8px;
        }

        .content {
          max-width: 100%;
          display: inline-block;
          font-size: 14px;
          font-weight: 400;
          text-align: left;
          color: #13161b;
          line-height: 24px;
          margin: 12px 0;
          flex: 1;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
          -webkit-line-clamp: 2;
          /* 规定在两行以上时显示省略号 */
        }

        .showAllContent {
          display: inline-block;
          font-size: 14px;
          margin: 13px 0;
          font-weight: 400;
          text-align: left;
          color: #13161b;
          line-height: 22px;
          flex: 1;
        }
      }
    }

    .item:hover {
      border-radius: 8px;
      background: var(--bg-kyy_color_bg_list_foucs, #F3F6FA);

      .wordicons {
        width: 72px;
      }
    }

  }
}

.contextmenu {
  position: absolute;
  z-index: 9999;
  width: 128px;
  //min-height: 120px;
  background: #ffffff;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 8px;
  box-shadow: 0 0 15px 5px rgb(0 0 0 / 20%);

  .t-button:not(:first-child) {
    margin-top: 2px;
  }

  .t-button {
    justify-content: flex-start;
    padding: 0 16px;

    img {
      width: 20px;
      height: 20px;
      margin-right: 12px;
    }

    .t-button__text {
      font-size: 14px;

      font-weight: 400;
      text-align: left;
      color: var(--kyy_color_dropdown_text_default, #1A2139);
      line-height: 22px;
    }
  }

  :deep(.t-button:hover) {
    .t-button__text {
      color: var(--kyy_color_dropdown_text_active, #4D5EFF);
    }
  }
}

.groupDrawer {
  :global(.t-drawer__header) {
    border: none;
    height: 56px;
    min-height: 50px;
    padding: 16px 24px;
  }

  :global(.t-drawer__body) {
    padding: 0 24px 16px 24px;
  }

  .header {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;

    .title {
      font-size: 14px;

      font-weight: 700;
      text-align: left;
      color: #13161b;
      line-height: 22px;
    }

    .t-button {
      height: 24px;
      min-height: 24px;
      max-height: 24px;
      padding: 0px 12px;
      border-radius: var(--radius-kyy_radius_button_s, 4px);
      background: var(--color-button_primary-kyy_color_button_primary_bg_default, #4D5EFF);
      color: var(--color-button_primary-kyy_color_button_primary_text, #FFF);
      text-align: center;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      /* 157.143% */
    }
  }

  .body {
    width: 516px;
    padding: 0 6px;
    height: 200px;
    overflow: scroll;

    .item:hover {
      cursor: move;

      .wordicon {
        background-color: inherit !important;
      }

      // .wordicons {
      //   opacity: 1;
      // }
      .itemset {
        opacity: 1;

        .wordicon:hover {
          color: #707EFF;
          background: #EAECFF !important;
        }
      }
    }

    .item {
      border-radius: 8px;

      :deep(.t-button) {
        width: 100%;

        .t-button__text {
          flex: 1;
        }
      }

      img {
        width: 20px;
        height: 20px;
        margin-right: 8px;
      }
    }

    .item+.item {
      margin-top: 10px;
    }

    .unmover {
      display: none;
    }

    .unmover+.item {
      margin-top: 0;
    }

    .ghost {
      background-color: #f1f1f1;
    }

    .chosenClass {

      box-shadow: 0 0 5px 5px rgb(0 0 0 / 10%);
    }

    .item .t-button {
      height: 40px;
      padding: 9px 8px;
      border-radius: 8px;
    }

    .item .t-button:hover {
      background: var(--bg-kyy_color_bg_list_foucs, #F3F6FA);
      color: var(--text-kyy_color_text_1, #1A2139);

      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      /* 157.143% */
    }

    .item .t-button:active {
      background-color: #DBDFFF;
    }
  }

  .footer {
    width: 516px;
    height: 64px;
    padding: 12px 24px;

    .t-button {
      min-width: 80px;
      height: 32px;
    }

    .t-button:first-child {
      border: 1px solid #D5DBE4 !important;
    }

    .t-button:last-child:hover {
      background: #707EFF !important;
    }
  }

}
:deep(.groupDrawer) {
  border-radius: 16px;
}

:deep(.contentDrawer) {
  .t-dialog {
    width: 516px;
    height: 320px;
    padding: 24px;
  }

  .t-dialog__header {
    margin-bottom: 16px;
  }

  .t-dialog__footer {
    border: none;
    position: absolute;
    bottom: 24px;
    right: 24px;

    &>div {
      flex-direction: row-reverse;

      .t-button {
        margin-left: 10px;
      }
    }
  }

  .t-button:last-child:hover {
    background: #4D5EFF !important;
  }
}

.emoji-root {
  padding-left: 2px;
  padding-top: 16px !important;
  gap:0;
}

.emoji-item {
  width: 50px;
  height: 50px;
  margin: 0;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    width: 40px;
    height: 40px;
  }
}

.emoji-item-COCO{
  width: 80px;
  height: 80px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  img {
    width: 80px;
    height: 80px;
    border-radius: 8px;
  }
}

.emoji-item:hover {
  width: 50px;
  height: 50px;

  img {
    width: 48px;
    height: 48px;
  }
}

.noData {
  width: 532px;
  height: 223px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;

  img {
    margin-top: -20px;
  }

  p {
    font-size: 14px;
    color: #516082;
  }

  button {
    display: flex;
    min-width: 80px;
    align-items: center;
    justify-content: center;
    border-radius: 999px;
    border: 1px solid var(--color-button_border-kyy_color_buttonBorder_border_brand, #4D5EFF);
    color: #4D5EFF;
    background: #fff;
    width: 117px;
    height: 32px;
    font-size: 16px;
    margin-top: 12px;
  }
}

.iconhover {
  border-radius: 4px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;

  img {
    line-height: 16px;
  }
}

.iconhover:hover {
  background-color: #EAECFF;
}

.iconhover:active {
  background-color: #DBDFFF;
}

.itemset {
  display: flex;
  align-items: center;
  opacity: 0;

  img:nth-child(2) {
    width: 16px !important;
    height: 16px !important;
  }
}

.isChecked {
  height: 270px !important;
}

.wordicons {
  opacity: 0;
  width: 0px;
  display: flex;
  align-items: center;
}

.item:hover {
  border-radius: 8px;

  .wordicons {
    opacity: 1;
    width: 72px;
  }

  .wordicon {
    opacity: 1;
    width: 28px;
    background-color: inherit;
  }
}

.moretrue {
  border-radius: 4px;
  background: var(--bg-kyy_color_bg_list_hover, #F3F6FA) !important;

  .wordicon {
    opacity: 1;
    width: 28px;
    background-color: #EAECFF !important;
  }
}

.wordicon {
  width: 0px;
  flex-shrink: 0;
  height: 28px;
  font-size: 20px;
  color: #828DA5;
  margin: 0 4px;
  border-radius: 4px;
}

.wordicon:nth-child(1) {
  margin-left: 8px;
}

.wordicon:nth-child(2) {
  margin-right: 0px;
}

.wordicon:hover {
  color: #707EFF;
  background: #EAECFF !important;
}

.wordicon:active {
  border-radius: var(--radius-kyy_borderRadius_xs, 4px);
  color: #4D5EFF;
}

:deep(.visibleDialog) {
  .t-dialog {
    height: 320px;
  }

  .t-button:last-child:hover {
    background: #4D5EFF !important;
  }
}

:deep(.visibleDialog .t-dialog__footer) {
  position: absolute;
  bottom: 24px;
  right: -8px;
}

:global(.t-is-success) {
  //display: none;
  background-color: #fff !important;
}

// ui改版样式处理
@import './css/speedModeDialog.less';

.overflow-auto::-webkit-scrollbar {
  width: 3px;
  height: 40px;
}
/* 滚动条滑块 */
.overflow-auto::-webkit-scrollbar-thumb {
  border-radius: 12px;
  height: 14px; /* 滚动滑块的高度 */
  background: rgba(0, 0, 0, 0.1);
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
}

.t-button:hover {
  background-color: #F3F6FA !important;
}

.t-button:active {
  background-color: #EAECFF !important;
}

.t-dropdown__item {
  max-width: 136px !important;
}
:global(.emoFen .t-dialog__mask){
  background:rgba(0,0,0,0);
}
:global(.emoFen .t-dialog){
  width:517px;
  height:320px;
  margin-bottom: 0;
  padding:0;
}
:global(.emoFen .t-dialog__header){
  padding:0;
}
:global(.emoFen .t-dialog__footer){
  padding:0;
}
:global(.emoFen .t-dialog__body){
  padding:0 !important;
  .body{
    width:100%;
  }
}

</style>
