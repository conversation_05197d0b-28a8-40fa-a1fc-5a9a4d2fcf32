<script setup lang='ts'>
import { storeToRefs } from 'pinia';
import { useMerchantStore } from '@renderer/store/modules/merchant';
import { onMountedOrActivated } from '@renderer/hooks/onMountedOrActivated';
import { getOssImgNormal } from '@renderer/views/square/utils/OSSHelpler';
import LynkerSDK from '@renderer/_jssdk';
import { computed } from 'vue';
import { formatBankCard } from '@renderer/utils/format';

const props = defineProps<{
  teamId: string;
}>();

const merchantStore = useMerchantStore();
const { info: merchantInfo } = storeToRefs(merchantStore);

const isLongTerm = computed(() => merchantInfo.value?.legal_person_id_card_end_time === '9999-12-31');

onMountedOrActivated(() => {
  merchantStore.getDetail(props.teamId);
});

// 预览图片
const previewImg = (url: string, title: string) => {
  LynkerSDK.previewImage({
    imgs: [{
      title,
      url: getOssImgNormal(url),
      type: 'image',
      size: 0,
      officeId: null,
      imgIndex: 0,
    }],
    imgIndex: 0,
  });
};
</script>

<template>
  <div v-loading="merchantStore.detailLoading" class="apply-info">
    <div class="apply-info__title">商户信息</div>
    <t-descriptions layout="vertical" item-layout="vertical">
      <t-descriptions-item label="商户名称">{{ merchantInfo?.merchant_name }}</t-descriptions-item>
      <t-descriptions-item label="商户地址">{{ merchantInfo?.merchant_address }}</t-descriptions-item>
      <t-descriptions-item label="商户类别">{{ merchantInfo?.merchant_mcc_code_name }}</t-descriptions-item>
      <t-descriptions-item label="经营范围">{{ merchantInfo?.business_content_name }}</t-descriptions-item>
      <t-descriptions-item label="门脸图/环境图">
        <div class="flex gap-8">
          <div
            v-if="merchantInfo?.pay_merchant_store_img?.[0]"
            class="store-img-preview"
            @click="previewImg(merchantInfo.pay_merchant_store_img[0].file_url, '门脸图')"
          >
            <t-image
              class="img"
              :src="merchantInfo.pay_merchant_store_img[0].file_url"
              lazy
              fit="cover"
            />
          </div>
          <div class="divider-h" />

          <div
            v-if="merchantInfo?.pay_merchant_context_diagram_img?.[0]"
            class="store-img-preview"
            @click="previewImg(merchantInfo.pay_merchant_context_diagram_img[0].file_url, '环境图')"
          >
            <t-image
              class="img"
              :src="merchantInfo.pay_merchant_context_diagram_img[0].file_url"
              lazy
              fit="cover"
            />
          </div>
        </div>
      </t-descriptions-item>
    </t-descriptions>

    <t-divider class="my-16!" />

    <!-- // 组织信息相关字段（替换为真实字段）
  merchant_team_img: [], // 营业执照图片
  merchant_team_name: '', // 营业执照名称
  credit_code: '', // 统一社会信用代码/注册号
  start_time: '', // 营业执照有效期-开始
  end_time: '', // 营业执照有效期-结束 -->

    <!-- 组织信息，仅政府单位显示 -->
    <template v-if="merchantInfo?.is_government">
      <div class="apply-info__title">组织信息</div>
      <t-descriptions layout="vertical" item-layout="vertical">
        <t-descriptions-item label="营业执照图片">
          <div v-if="merchantInfo?.merchant_team_img?.[0]" class="id-card-img-preview" @click="previewImg(merchantInfo.merchant_team_img[0].file_url, '营业执照图片')">
            <t-image
              class="img"
              :src="merchantInfo.merchant_team_img[0].file_url"
              lazy
              fit="cover"
            />
          </div>
        </t-descriptions-item>
        <t-descriptions-item label="营业执照名称">{{ merchantInfo?.merchant_team_name }}</t-descriptions-item>
        <t-descriptions-item label="统一社会信用代码/注册号">{{ merchantInfo?.credit_code }}</t-descriptions-item>
        <t-descriptions-item label="营业执照有效期">
          <span v-if="merchantInfo?.end_time === '9999-12-31'">（长期有效）</span>
          <template v-else>
            {{ merchantInfo?.start_time }} ~ {{ merchantInfo?.end_time }}
          </template>
        </t-descriptions-item>
      </t-descriptions>
      <t-divider class="my-16!" />
    </template>

    <div class="apply-info__title">法人信息</div>
    <t-descriptions layout="vertical" item-layout="vertical">
      <t-descriptions-item label="法人身份证照片">
        <div class="flex gap-8">
          <div
            v-if="merchantInfo?.legal_person_id_card_img?.[0]"
            class="id-card-img-preview"
            @click="previewImg(merchantInfo.legal_person_id_card_img[0].file_url, '法人身份证照片')"
          >
            <t-image
              class="img"
              :src="merchantInfo.legal_person_id_card_img[0].file_url"
              lazy
              fit="cover"
            />
          </div>
          <div
            v-if="merchantInfo?.legal_person_id_card_img_tow?.[0]"
            class="id-card-img-preview"
            @click="previewImg(merchantInfo.legal_person_id_card_img_tow[0].file_url, '法人身份证照片')"
          >
            <t-image
              class="img"
              :src="merchantInfo.legal_person_id_card_img_tow[0].file_url"
              lazy
              fit="cover"
            />
          </div>
        </div>
      </t-descriptions-item>
      <t-descriptions-item label="法人姓名">{{ merchantInfo?.legal_person_name }}</t-descriptions-item>
      <t-descriptions-item label="法人身份证号码">{{ merchantInfo?.legal_person_id_card }}</t-descriptions-item>
      <t-descriptions-item label="法人身份有效期">
        <span v-if="isLongTerm">（长期有效）</span>
        <template v-else>
          {{ merchantInfo?.legal_person_id_card_start_time }} ~ {{ merchantInfo?.legal_person_id_card_end_time }}
        </template>
      </t-descriptions-item>
      <t-descriptions-item label="邮箱">{{ merchantInfo?.email }}</t-descriptions-item>
    </t-descriptions>

    <t-divider class="my-16!" />

    <div class="apply-info__title">结算信息</div>
    <t-descriptions layout="vertical" item-layout="vertical">
      <t-descriptions-item label="结算银行卡照片">
        <div
          v-if="merchantInfo?.pay_merchant_legal_person_bank_img?.[0]"
          class="id-card-img-preview"
          @click="previewImg(merchantInfo.pay_merchant_legal_person_bank_img[0].file_url, '法人银行卡证照片')"
        >
          <t-image class="img" :src="merchantInfo.pay_merchant_legal_person_bank_img[0].file_url" lazy />
        </div>
      </t-descriptions-item>
      <t-descriptions-item label="结算方式">对公</t-descriptions-item>
      <t-descriptions-item label="法人银行卡名称">{{ merchantInfo?.pay_merchant_legal_person_bank_card_bank_name }}</t-descriptions-item>
      <!-- <t-descriptions-item label="开户行号">{{ merchantInfo?.pay_merchant_legal_person_bank_card_open_bank_number }}</t-descriptions-item> -->
      <t-descriptions-item label="法人银行卡号">{{ formatBankCard(merchantInfo?.pay_merchant_legal_person_bank_card_number) }}</t-descriptions-item>
      <t-descriptions-item label="法人银行卡开户行">{{ merchantInfo?.pay_merchant_legal_person_bank_card_bank_name }}</t-descriptions-item>
      <t-descriptions-item label="法人手机号码">{{ merchantInfo?.pay_merchant_legal_person_bank_mobile }}</t-descriptions-item>
      <t-descriptions-item label="开户行名称">{{ merchantInfo?.pay_merchant_legal_person_bank_card_open_bank_name }}</t-descriptions-item>
    </t-descriptions>
  </div>
</template>

<style lang='less' scoped>
::-webkit-scrollbar {
  background-color: transparent;
}

::-webkit-scrollbar-thumb {
  box-shadow: none;
  -webkit-box-shadow: none;
}

::-webkit-scrollbar-track {
  background-color: transparent;
}

.apply-info {
  width: 100%;
  height: 100%;
  background-color: #fff;
  overflow-y: auto;
  .scrollbar2(24px, 8px);
}

.apply-info__title {
  color: var(--text-kyy_color_text_1, #1A2139);
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
  padding-left: 11px;
  position: relative;
  margin-bottom: 16px;

  &:before {
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    content: '';
    display: inline-block;
    width: 3px;
    height: 16px;
    border-radius: 8px;
    background: var(--brand-kyy_color_brand_default, #4D5EFF);
  }
}

:deep(.t-descriptions__label) {
  padding: 0 !important;
  color: var(--text-kyy_color_text_3, #828DA5);
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
}

:deep(.t-descriptions__body tr:last-child .t-descriptions__content) {
  padding-bottom: 0 !important;
}

:deep(.t-descriptions__content) {
  color: var(--text-kyy_color_text_1, #1A2139);
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  padding: 8px 0 16px 0 !important;
}

.divider-h {
  width: 1px;
  height: 78px;
  background: var(--divider-kyy_color_divider_light, #eceff5);
}

.store-img-preview {
  border-radius: 8px;

  .img {
    width: 78px;
    height: 78px;
    cursor: pointer;
    border-radius: 8px;
  }
}

.id-card-img-preview {
  display: flex;
  width: 140px;
  height: 88px;
  padding: 8px;
  justify-content: center;
  align-items: center;
  border-radius: 8px;
  border: 1px solid var(--divider-kyy_color_divider_deep, #D5DBE4);
  background: #FFF;
  cursor: pointer;
  .img {
    width: 124px;
    height: 72px;
    border-radius: 8px;
  }
}
</style>
