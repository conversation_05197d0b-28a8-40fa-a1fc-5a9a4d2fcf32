<script setup lang="ts">
import { ref, computed, onMounted} from 'vue';
import { useRoute } from 'vue-router';
import PictureList from './components/PictureList.vue'
import VideoList from '@/views/square/video/List/index.vue'
import { getIsVideoImage } from '@renderer/api/member/api/businessApi';

const route = useRoute();
// 平台类型
type PlatformType = 'number' | 'government' | 'cbd' | 'association';
const type : PlatformType = route.query.type as PlatformType || 'number';

const uuidCpt = computed(()=> {
  let msg = '';
  switch(type) {
    case 'number':
      msg = '数字商协-平台动态';
      break;
    case 'government':
      msg = '数字城市-平台动态';
      break;
    case 'cbd':
      msg = '数字CBD-平台动态';
      break;
    case 'association':
      msg = '数字社群-平台动态'
      break;
    case 'uni':
      msg = '数字高校-平台动态'
      break;
    default:;
  }
  return msg;
})

const isLoad = ref(false);
const current = ref(0);
const tab1 = {
  value: 1,
  label: '视频'
}
const tab2 = {
  value: 2,
  // label: $t("member.eb.n")
  label: '图文'
}
const tabChange = (newVal) => {
  current.value = newVal;
}
const getCheckData = () => {
  getIsVideoImage({
    channel_type: type === 'number' ? 'member' : type
  }).then(res => {
    const data = res.data?.data
    if (data) {
      current.value = data.is_video ? 1 : 2;
    }
    isLoad.value = true;
  }).catch(res => {
    isLoad.value = true;
  })
}
onMounted(async () => {
  await getCheckData();
})
</script>

<template>
  <div class="page-container">
    <div class="page-content" v-if="isLoad">
      <t-image
        src="https://kuaiyouyi.oss-cn-shenzhen.aliyuncs.com/square/172131330767814385018.png"
        class="logo"
      />
      <div class="title">
        <t-tabs :default-value="current" @change="tabChange">
          <t-tab-panel :value="tab1.value" :label="tab1.label"></t-tab-panel>
          <t-tab-panel :value="tab2.value" :label="tab2.label"></t-tab-panel>
        </t-tabs>
      </div>
      <VideoList v-show="current === 1" :isShow="current === 1" cols="5" :channel_type="type" :emptyType="1" :isFromOuter="true" />
      <PictureList v-show="current === 2" />
      <Tricks :offset="{ x: '-42', y: '-130' }" :uuid="uuidCpt" />
    </div>
    <!-- <div v-else-if="!isLoad" class="text-center w-full">
      <t-loading :text="$t('components.infiniteLoading.loading')" size="small" />
    </div> -->
  </div>
</template>

<style scoped lang="less">
.page-container {
  background: url('https://kuaiyouyi.oss-cn-shenzhen.aliyuncs.com/square/172230609542216724791.png') no-repeat center;
  background-size: cover;
  margin: 0 auto;
}

.scrollbar(6px);

.page-content {
  position: relative;
  padding: 72px 24px 24px;
  margin:56px 16px 24px 16px;
  gap: 16px;
  border-radius: 16px;
  background: var(--bg-kyy_color_bg_light, #FFF);
  min-height: -webkit-fill-available;
  .logo {
    position: absolute;
    top: -40px;
    width: 128px;
    height: 96px;
    flex-shrink: 0;
    border-radius: 8px;
  }
  .title {
    position: absolute;
    left: 168px;
    top: 0;
    color: #0BA18C;
    font-size: 18px;
    font-weight: 600;
    line-height: 26px;
    :deep(.t-tabs__nav-item-text-wrapper){
      color: var(--cyan-kyy_color_cyan_active, #0EA197);
      font-size: 18px;
      font-style: normal;
      font-weight: 600;
      line-height: 26px;
    }
    :deep(.t-tabs__bar){
      background-color: var(--cyan-kyy_color_cyan_active, #0EA197);
      width: 16px !important;
      transform: translateX(160%);
      border-radius: 1.5px;
    }
    :deep(.t-tabs__nav-container.t-is-top::after){
      display: none;
    }
  }
}

.post-list {
  width: 100%;
  .post-item {
    background-color: #F5FDFC;
    &::before {
      position: absolute;
      top: 0;
      right: 0;
      left: 0;
      content: ' ';
      background: url('https://kuaiyouyi.oss-cn-shenzhen.aliyuncs.com/square/172135602614950151009.png') no-repeat center;
      background-size: cover;
      height: 100px;
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
      margin: 1px 1px 0 1px;
      z-index: 1;
    }
    :deep(.trends-item ) {
      background-color: #F5FDFC;
      .right-content {
        z-index: 2;
      }
    }
  }
}

</style>
