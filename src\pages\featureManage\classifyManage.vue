<template>
	<div class="payset">
    <div class="head-title">特性管理</div>
		<!-- <t-tabs v-model="params.type" @change="tabChange">
			<t-tab-panel :value="1" label="移动端"> </t-tab-panel>
			<t-tab-panel :value="2" label="桌面端"> </t-tab-panel>
		</t-tabs> -->
    <div>
      <t-button theme="primary" @click="onAddFeature">添加特性</t-button>
    </div>
		<div class="search-bar" @keyup.enter="getDataSeach">

      <div class="sele">
        <div class="lab">特性名称</div>
        <t-input class="val" v-model="formData.features_name" clearable/>
      </div>
      <div class="sele">
        <div class="lab">特性等级</div>
        <t-select
          v-model="formData.features_level"
          :options="featureLevelOptions"
          placeholder="请选择"
          clearable
          class="val"
        />
      </div>
      <div class="sele">
        <div class="lab">状态</div>
        <t-select
          v-model="formData.status"
          :options="featureStatusOptions"
          placeholder="请选择"
          clearable
          class="val"
        />
      </div>
      <div class="sele">
        <div class="lab">特性类型</div>
        <t-select
          v-model="formData.features_type_id"
          :options="exclusiveTypeOptions"
          :keys="{label: 'features_type_name', value: 'features_type_id'}"
          placeholder="请选择"
          clearable

          class="val"
        />
      </div>
      <div class="btns" style="margin-left: 16px">
        <t-button theme="primary" @click="getDataSeach">查询</t-button>
        <t-button theme="default" variant="base" @click="onReset">重置</t-button>
      </div>

			<!-- <div>
				<t-button theme="primary" @click="add">添加分类</t-button>
			</div> -->
		</div>
		<!-- <div v-if="params.type === 1" class="tip">
			提示：排序相同则最后更新时间放在上方。移动端展示超过6个时，则只显示前4个，第5个收到全部分类里面
		</div>
		<div v-else class="tip">
			提示：排序相同则最后更新时间放在上方。桌面端展示超过9个时，则只显示前7个，第8个收到更多分类里面
		</div> -->
		<div class="table">
			<t-table
				:row-key="'features_id'"
				:data="listData"
				cell-empty-content="--"
				:pagination="pagination.total > 10 ? pagination : null"
				height="100%"
				:columns="columns"
			>
				<template #team_name="{ row }">
					<!-- <span v-for="(item, idx) in row.business_classify" :key="item.id"
						>{{ item.name }} <span v-if="idx !== row.business_classify?.length - 1">、</span>
					</span> -->
          <span class="teamLogo">
            <t-image class="img" :src="row.team_logo" alt="">
              <template #error>
                <img class="img" src="@/assets/<EMAIL>">
              </template>
            </t-image>
            <span class="teamName">{{ row.team_name }}</span>
          </span>
				</template>
				<template #digital_status="{ row }">
          {{ row.digital_status === 1? '已开启' : '已关闭'  }}
        </template>
        <template #digital_uuid="{ row }">
          {{ digitalUUIDText(row.digital_uuid) }}
        </template>
        <template #digital_open_time="{ row }">
          {{ row.digital_open_time || '--'}}
        </template>

        <template #exclusive_name="{ row }">
           <div v-if="row.exclusive_status === 0"  class="exclusive_name">
              <div class="tab tabDefault">
                未获取
              </div>
           </div>
           <div v-else class="exclusive_name">
             <div class="clas">
                <div class="lab">{{row?.exclusive_name}}</div>
                <div class="tab tabDefault" v-show="row.exclusive_status === 2">
                  已失效
                </div>
                <div class="tab tabPrimary" v-show="row.exclusive_status === 1">
                  生效中
                </div>
              </div>
           </div>
        </template>


				<template #status="{ row }">
					<t-switch :value="row.status ? true : false" @change="switchChange($event, row)" />
				</template>
				<template #img="{ row }">
					<img :src="row.icon" alt="" style="cursor: pointer" class="logo" @click="viewImg(row.icon)" />
				</template>
				<template #Actions="{ row }">
          <!-- <t-link theme="primary" @click="look(row)">查看</t-link> -->
					<t-link theme="primary" @click="edit(row)">编辑</t-link>
					<t-link theme="danger" style="margin-left: 8px" @click="del(row)">删除</t-link>
				</template>
			</t-table>
		</div>
	</div>
	<!-- <editModal ref="editRef" :etype="etype" :type="params.type" @succ="getData" /> -->
	<t-image-viewer v-model:visible="visible" :images="[img]"> </t-image-viewer>

	<editFeature ref="setFeatureRef"  @succ="onSaveFeature"/>
</template>

<script setup lang="ts">
import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next';
import { onMounted, ref, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { nicheTemplate, nicheTemplateClassify, nicheTemplatePut } from '@/api/businessOpportunity';
import { classTypeStatus, delExclusiveFeatureAxios, exclusiveListAxios, getExclusiveSettingAxios,  saveExclusiveSettingAxios, updateExclusiveFeatureAxios, exclusiveTypeListAxios, addExclusiveFeatureAxios, updateExclusiveFeaturePutAxios } from '@/api/businessOpportunity/niche-class';

import {featureStatusOptions, featureLevelOptions} from '@/constants/digital';

import editFeature from '@/pages/featureManage/editFeature.vue'
import to from 'await-to-js';
import router from '@/router';
const visible = ref(false);
const etype = ref(1);
const img = ref('');
const formData = reactive({
	features_name: '', // 名称（多个区域名称以\n换行）
  features_level: '', // 特性等级
  features_type_id: '', // 特性类型id
  status: '', // 状态
});


const exclusiveTypeOptions = ref([]);

const geExclusTypeList = () => {
  const params: any = {};
	params.page = 0;
	// params.pageSize = 10;
  // if (params.digital_time?.length) {
  //   params.digital_open_time_start = params.digital_time[0];
  //   params.digital_open_time_end = params.digital_time[1];
  //   delete params.digital_time;
  // }

	exclusiveTypeListAxios(params).then((res) => {
		console.log(res);
		if (res.data.data) {
			exclusiveTypeOptions.value = res.data.data.list || [];
		}
	});
};


onMounted(() => {
	getData();
  geExclusTypeList();
});
// const tabChange = () => {
// 	pagination.current = 1;
// 	pagination.pageSize = 10;
// 	if (params.value.type === 1) {
// 		columns.value.splice(3, 0, {
// 			colKey: 'img',
// 			title: '展示图标',
// 		});
// 	} else {
// 		columns.value = columns.value.filter((item) => item.colKey !== 'img');
// 	}
// 	getData();
// };

const viewImg = (imgUrl) => {
	img.value = imgUrl;
	visible.value = true;
};


const setFeatureRef = ref(null);
const onAddFeature = async () => {

  // const  [err, res]: any = await to(getExclusiveSettingAxios());
  // if (err) {
  //   MessagePlugin.error('获取失败');
  //   return;
  // }
  // if (res?.data?.data) {
  setFeatureRef.value.onOpen();
  // }
}

const onSaveFeature = async (d) => {
  const  [err, res]: any = await to(
    d?.features_id ? updateExclusiveFeaturePutAxios({...d, id: d?.features_id}) : addExclusiveFeatureAxios({...d})
    );
  if (err) {
    MessagePlugin.error(err?.message || '操作失败');
    return;
  }
  MessagePlugin.success('操作成功');
  setFeatureRef.value.onClose();
  getData();
}


const look = (row) => {
	router.push({
		path: '/platformManage/platformList/classifyManageDetail',
		query: {
			id: row.team_id,
		},
	});
}

const getData = () => {
  const params: any = {...formData};
	params.page = pagination.current;
	params.pageSize = pagination.pageSize;
  // if (params.digital_time?.length) {
  //   params.digital_open_time_start = params.digital_time[0];
  //   params.digital_open_time_end = params.digital_time[1];
  //   delete params.digital_time;
  // }

	exclusiveListAxios(params).then((res) => {
		console.log(res);
		if (res.data.data) {
			listData.value = res.data.data.list;
			pagination.total = res.data.data.total;
		}
	});
};
const pagination = reactive({
	current: 1,
	pageSize: 10,
	total: 0,
	showJumper: true,
	onCurrentChange: (current: number, pageInfo: any) => {
    console.log('特性1：',pageInfo.current, pageInfo.pageSize);
		pagination.current = current;
		// pagination.pageSize = pageInfo.pageSize;
		getData();
	},
  onPageSizeChange(pageSize: number, pageInfo: any) {
    console.log('特性2：',pageSize, pageInfo);
    pagination.pageSize = pageSize;
    // pagination.pageSize = pageSize;
    // pagination.current = 1;
    getData();
  }
});
const onReset = () => {
	formData.features_name = '';
	formData.features_level = '';
	formData.features_type_id = '';
	formData.status = '';
	pagination.current = 1;
	// pagination.pageSize = 10;
	getData();
};
const getDataSeach = () => {
	pagination.current = 1;
	// pagination.pageSize = 10;
	getData();
};

const editRef = ref(null);
const edit = (row) => {
	// etype.value = 2;
  setFeatureRef.value.onOpen({...row, etype: 2});
};


const digitalUUIDText = (uuid) => {
  //平台类型（数字商协：member，数字城市：government，数字cbd：cbd，数字社群：association）
	let msg ='';
  switch (uuid) {
    case 'member':
      msg = '数字商协';
      break;
    case'government':
      msg = '数字城市';
      break;
    case'cbd':
      msg = '数字cbd';
      break;
    case'association':
      msg = '数字社群';
      break;
        case'uni':
      msg = '数字高校';
      break;
    default:
      break;
  }
  return msg;
}

const columns = ref([
	// {
	// 	colKey: 'sort',
	// 	title: '排序',
	// },
	{
		colKey: 'features_name',
		title: '特性名称',

	},
	{
		colKey: 'features_level',
		title: '特性等级',
	},
	{
		colKey: 'features_type_name',
		title: '特性类型',
	},
	{
		colKey: 'status',
		title: '状态',
	},

	{
		colKey: 'Actions',
		title: '操作',
	},
]);

const listData = ref([]);

const classDeleteReq = (id) => {
	delExclusiveFeatureAxios(id).then((res) => {
		console.log(res);
		if (res.data) {
			MessagePlugin.success('删除成功');
			getData();
		}
	});
};

const del = (rowData) => {
	const myDialog = DialogPlugin({
		header: '确定删除该特性？',
		theme: 'info',
		body: `删除后获取专属名称时无法选择该特性`,
		className: 'dialog-classp32',
		onConfirm: () => {
			classDeleteReq(rowData.features_id);
			myDialog.hide();
		},
		onCancel: () => {
			myDialog.hide();
		},
	});
};

const classTypeStatusReq = (id, show) => {
	updateExclusiveFeatureAxios({features_id: id,  status: show }).then((res) => {
		console.log(res);
		if (res.data.data) {
			MessagePlugin.success('操作成功');
			getData();
		}
	}).catch((err) => {
		MessagePlugin.error('操作失败');
  });
};

// const onPick = (value, context) => console.log('onPick:', value, context);
// const onChange = (value, context) => {
// 	console.log('onChange:', value, context);
// 	console.log(
// 		'Timestamp:',
// 		context.dayjsValue.map((d) => d.valueOf()),
// 	);
// 	console.log(
// 		'YYYYMMDD:',
// 		context.dayjsValue.map((d) => d.format('YYYYMMDD')),
// 	);
// };

const switchChange = (e, rowData) => {
	const myDialog = DialogPlugin({
		header: '提示',
		theme: 'info',
		body: `确定${e ? '启用' : '禁用'}该特性？`,
		className: 'dialog-classp32',
		onConfirm: () => {
			classTypeStatusReq(rowData.features_id, e ? 1 : 0);
			myDialog.hide();
		},
		onCancel: () => {
			// rowData.status = e ? 0 : 1;
			myDialog.hide();
		},
	});
};
</script>

<style lang="less" scoped>
.head-title {
	height: 24px;
	font-size: 16px;
	font-family: Microsoft YaHei, Microsoft YaHei-Bold;
	font-weight: 700;
	color: #13161b;
	line-height: 24px;
	margin-bottom: 16px;
}
.exclusive_name {
  .tabDefault {
    display: inline-block;
    background-color: #f5f5f5;
    color: #97989a;
    padding: 0 8px;
    border-radius: 4px;
    font-size: 12px;
  }
  .tabPrimary {
    display: inline-block;
    background-color: #eeffe8;
    color: #1c8710;
    padding: 0 8px;
    border-radius: 4px;
    font-size: 12px;
  }
}
.teamLogo {
  display: flex;
  align-items: center;
  gap: 8px;
  .img {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    flex:none;
  }
  .teamName {

  }
}
.payset {
	background-color: #fff;
	// height: 85vh;
	width: 100%;
	padding: 16px;
}
.search-bar {
	display: flex;
	margin-bottom: 16px;
	margin-top: 16px;
  flex-wrap: wrap;
  gap: 16px;
	.sele {
		display: flex;
		gap: 8px;
		align-items: center;
    // max-width: 300px;
		.lab {
			width: 100px;
      text-align: right;
		}
    .val {
      width: 180px;
    }
	}
	.btns {
		display: flex;
		gap: 16px;
		.t-button {
			min-width: 80px;
		}
	}
}
.re-box {
	color: black;
	.clas {
		display: flex;
		margin-top: 12px;
		align-items: center;
	}
	.select {
		width: 630px;
	}
}
.logo {
	width: 100px;
	height: 100px;
}
.tip {
	padding: 12px;
	background-color: antiquewhite;
	width: 100%;
	margin-bottom: 12px;
}
</style>
