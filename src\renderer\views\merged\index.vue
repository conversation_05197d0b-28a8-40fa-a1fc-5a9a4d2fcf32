<template>
  <div class="root">
    <div class="title-bar">
      <div class="bar-title">聊天记录</div>
      <div v-if="isWin" class="win-close" @click="closeWindow">
        <img width="20" height="20" src="@/assets/svg/icon_close.svg">
      </div>
    </div>
    <div class="msg-list scrollbar">
      <template v-if="list.length">
        <div v-for="(item, index) in list" :key="item.index">
          <div class="message">
            <ChatAvatar :size="30" :src="item.avatar" :alt="item.name" />
            <div class="content">
              <div class="msg-info">
                <div>{{ item.name }}</div>
                <div>{{ item.sendTime ? moment(item.sendTime).format('YYYY-MM-DD HH:mm') : '' }}</div>
              </div>
              <div>
                <!-- TODO: 完善消息类型 -->
                <renderContent :item="item" />
              </div>
            </div>
          </div>
          <div v-if="index !== list.length - 1" class="divider" />
        </div>
      </template>
      <div v-if="err" class="error-status">
        <img src="@/assets/prompt-picture/emptyState_img_metadata备份********" />
        <div>出错啦</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="tsx">
import { onMounted, ref, defineComponent, h, PropType, computed } from 'vue';
import moment from 'moment';
import { CloseIcon, Icon } from 'tdesign-icons-vue-next';
import ChatAvatar from '@/views/message/components/ChatAvatar.vue';
import { MsgTextType, getImageStyle, getParsedText, getRichText, getSrcThumbnail, getReadableFileSize } from '@renderer/views/message/service/msgUtils';
import { fileImage } from "@renderer/utils/myUtils";
import {LkIMEditorPlayground} from "@rk/editor";

import {
  getDownloadedPath,
  isUrlCanPreview,
  isUrlDownloaded,
  removeFileDownloadedPath,
  setUrlDownloadedPath,
} from '@renderer/views/message/service/fileUtils';
import { EmojiObject } from '@renderer/assets/im/emoji';
const { ipcRenderer } = require('electron');


const list = ref([]);
const err = ref();
const isWin = process.platform === 'win32';
const fileUrl = ref('');
onMounted(() => {
  // 获取所有 URL 参数
  const hashParams = window.location.hash.split('?')[1] || '';
  const urlParams = new URLSearchParams(hashParams);
  const url = urlParams.get('fileUrl');
  console.log('merge onMounted', { url, allParams: Object.fromEntries(urlParams) });
  loadData(url);

  ipcRenderer.on('showMergedMessage', (e, val) => {
    fileUrl.value = val.fileUrl;
    loadData(val.fileUrl);
    console.log('showOtherType1', val);
  });
});

const loadData = (url) => {
  list.value = [];

  url &&
    fetch(url)
      .then((res) => res.json())
      .then((res) => {
        console.log(res);
        if (Array.isArray(res)) {
          list.value = res;
        } else {
          list.value = res.data || [];
        }
      })
      .catch((error) => {
        err.value = error;
      });
  // const filePath = getLocalFile(url);
  // if (filePath) {
  //     const fs = require('fs');
  //     fs.readFile(filePath, 'utf-8',  async (err, data)=> {
  //         if (!err) {
  //             list.value = JSON.parse(data);
  //         };
  //     });

  // } else {

  // }
};

// const getLocalFile = (url = '') => {
//     const Store = require('electron-store');
//     const md5 = require('js-md5');
//     const key = md5(url);
//     return new Store().get(key);
// }

const closeWindow = () => {
  ipcRenderer.invoke('window-close');
};

 // 图片视频预览更新为打开新窗口
 const previewFileAction = (data: PreviewFiles) => {
      // 图片data里面返回的type 可能会是image/png,从url获取格式
      const type = data.type || data.url.split('.').pop();
      const title = data.title || data.url.split('/').pop();
      console.log('预览', { ...data, type, title });
      ipcRenderer.invoke('preview-file', JSON.stringify({ ...data,size:data.size/1024, type, title }));
  };
type IMessageMergedContentType = IMessageMergedExtra['data'][0];

const renderContent = defineComponent({
  props: {
    item: {
      type: Object as PropType<IMessageMergedContentType>,
      required: true,
    },
  },
  setup(props) {
    const msgItme = computed<IMessageMergedContentType>(()=>props.item);


    //图片预览
    function previewImage() {
      const { imgUrl: url, size} = msgItme.value?.data;
      previewFileAction({ url, size });
    }
    //视频预览
    function previewVideo() {
      const { videoUrl: url, size, videoName: title } = msgItme.value?.data;
      previewFileAction({ url, size, title });
    }

    //文件预览
    function previewFile() {
      const { fileUrl: url, size, fileName: title, type, fileId: officeId } = msgItme.value?.data;
      previewFileAction({ url, size, title, type, officeId });
    }

    async function openFile() {
      const { shell } = require('electron');
      const { existsSync } = require('fs');
      const url = msgItme.value?.data?.fileUrl;
      const path = getDownloadedPath(url);

      if (existsSync(path)) {
        shell.openPath(path);
      } else {
        removeFileDownloadedPath(url);
        renderKey.value = moment().valueOf();
      }
    }

    async function showFileInFolder() {
      const { shell } = require('electron');
      const { existsSync } = require('fs');

      const url = msgItme.value?.data?.fileUrl;
      const path = getDownloadedPath(url);
      if (existsSync(path)) {
        shell.showItemInFolder(path);
      } else {
        removeFileDownloadedPath(url);
        renderKey.value = moment().valueOf();
      }
    }

    async function saveFile() {

      const { ipcRenderer } = require('electron');

      const url = msgItme.value?.data?.fileUrl;
      const filePath = await ipcRenderer.invoke('download-file', { title: msgItme.value?.data.fileName, url });
      setUrlDownloadedPath(url, filePath);
      renderKey.value = moment().valueOf();
      showFileInFolder();
    }

    async function saveAsFile() {
      // download-file
      const { ipcRenderer } = require('electron');
      const url = msgItme.value?.data?.fileUrl;
      const savePath = await ipcRenderer.invoke('download-file', { title: msgItme.value?.data.fileName, inquiry: true, url });
      setUrlDownloadedPath(url, savePath);
      renderKey.value = moment().valueOf();
    }

    function openUrlByBrowser(url: string) {
      const { shell } = require('electron');
      shell.openExternal(url);
    }

    // 用于强制刷新
    const renderKey = ref(0);

    /**
     * 渲染图片内容
     * @returns {JSX.Element} 图片内容
     */
    const renderContentForImage = () => {
      const isLongImg = msgItme.value?.data?.height > 320 && msgItme.value?.data?.height > msgItme.value?.data?.width;
      const imgStyle = getImageStyle(msgItme.value?.data?.width, msgItme.value?.data?.height);
      return (
        <div style={imgStyle} class='chat-img' data-long={isLongImg}>
          <img
            style={{width: imgStyle.width ?? imgStyle.maxWidth}}
            src={getSrcThumbnail(msgItme.value?.data?.imgUrl, msgItme.value?.data?.type)}
            onClick={() =>previewImage()}
          />
        </div>
      )
    };

    /**
     * 渲染视频内容
     * @returns {JSX.Element} 视频内容
     */
    const renderContentForVideo = () => (
      <div class="video-item" style={{ ...getImageStyle(msgItme.value?.data?.width, msgItme.value?.data?.height), position: 'relative' }}>
        <img
          style={getImageStyle(msgItme.value?.data?.width, msgItme.value?.data?.height)}
          src={msgItme.value?.data?.videoImageUrl}
          class="video-item-preview"
        />
        <div
          style="width: 40px; height: 40px; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); cursor: pointer;"
          onClick={() => previewVideo()}
        >
          <Icon color="#FFFFFF" name="play-circle" size="40px" />
        </div>
      </div>
    );

    /**
     * 渲染文件内容
     * @returns {JSX.Element} 文件内容
     */
    const renderContentForFile = () => {
      let result = null;
      const children = [];
      const canPreview = msgItme.value?.data?.fileId && isUrlCanPreview(msgItme.value?.data?.fileUrl);
      if (canPreview) {
        children.push(
          h('div', { class: 'file-action', onclick: () => previewFile() }, '预览'),
          h('div', { class: 'divider' }),
        );
      }

      const isDownload = msgItme.value?.data?.fileUrl ? isUrlDownloaded(msgItme.value?.data?.fileUrl) : false;
      if (isDownload) {
        children.push(
          h('div', { class: 'file-action', onclick: () => openFile() }, '打开'),
          h('div', { class: 'divider' }),
          h('div', { class: 'file-action', onclick: () => showFileInFolder() }, '打开文件夹'),
        );
      } else {
        children.push(
          h('div', { class: 'file-action', onclick: () => saveFile() }, '下载'),
          h('div', { class: 'divider' }),
          h('div', { class: 'file-action', onclick: () => saveAsFile() }, '另存为…'),
        );
      }
      result = h('div', { key: renderKey.value, class: 'send-success' }, children);
      return h('div', { class: 'chat-file', style: { 'margin-bottom': '20px' } }, [
        h('div', { class: 'file' }, [
          h('div', { class: 'file-icon' }, [
            h('img', { src: fileImage(msgItme.value?.data?.type), style: { width: '36px', height: '36px' } }),
          ]),
          h('div', { class: 'file-detail' }, [
            h('div', { class: 'file-name' }, msgItme.value?.data?.fileName || msgItme.value?.data?.name || msgItme.value?.content),
            h('div', { class: 'file-size' }, msgItme.value?.data?.size && getReadableFileSize(msgItme.value.data.size)),
          ]),
        ]),
        result,
      ]);
    };

    const renderContentForText = (contentType?: string) => {
      const splits = getParsedText(msgItme.value?.data.text, false, contentType);
      if (splits.length === 1 && splits[0].type === MsgTextType.Emoji) {
        const emoji = splits[0];
        return h('div', { class: 'chat-text' }, h('img', {  draggable: false,src: emoji.emojiSrc, style: `height:${emoji?.height || 44}px;width:${emoji?.width || 44}px;` }));
      }

      return h(
        'div',
        { class: 'chat-text' },
        splits.map((item) => {
          switch (item.type) {
            case MsgTextType.Text:
              return item.str;
            case MsgTextType.Emoji:
              return h('img', { src: item.emojiSrc, style: 'height:32px;width:32px;' });
            case MsgTextType.At:
              return item.str;
            case MsgTextType.Email:
              return item.str;
            case MsgTextType.File:
              return item.str;
            case MsgTextType.Url:
              return h('a', { class: 'text-link', onClick: () => openUrlByBrowser(item.str) }, item.str);
            default:
              return item.str;
          }
        }),
      );
    };

    const renderContentForRichText = (contentType?: string) => {
      let data = msgItme.value?.data.text
      try {
        data = JSON.parse(data)
      } catch (error) {
        console.error('richText', error)
      }

      return h(
        'div',
        { class: 'chat-text' },
        h(LkIMEditorPlayground, {
          content: data,
          emojiObject: EmojiObject,
          onOnMentionClick: (data: any) => {

          },
          onOnLinkClick: (data: string) => {
            console.log('linkElement', data)
            openUrlByBrowser(data)
          }
        })
      );
    };

    return () => {
      switch (msgItme.value?.type) {
        case 'text':
          return renderContentForText(msgItme.value?.type);
        case 'image':
          return renderContentForImage();
        case 'video':
          return  renderContentForVideo();
        case 'file':
          return  renderContentForFile();
        case 'APP_MEETING_MSG':
          return msgItme.value.data?.media_type === 'audio' ? '[语音通话]' : '[视频通话]';
        // 没相关需求，暂不扩展
        // case 'emoji_coco_image':
        //   return () => renderContentForText(item.type);
        // case 'emoji_image':
        //   return () => renderContentForImage();
        case 'richText':
          return renderContentForRichText();
        default:
          return <div style="word-break: break-word;">{msgItme.value?.content}</div>;
      }
    }
  },
});
</script>

<style lang="less">

.title-bar {
  -webkit-app-region: drag;
  flex: 0 0 48px;
  color: var(--text-kyy_color_text_1, #1A2139);
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  font-weight: 600;
  z-index: 10;
  background-color: var(--bg-kyy_color_bg_deep, #F5F8FE);
  position: relative;
}

.bar-title {
  flex: 1;
  text-align: center;
}

.title-close {
  -webkit-app-region: no-drag;
  padding: 10px;
}

.title-close:hover {
  opacity: 0.8;
}

.message {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
  padding: 0 16px;
}

.content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: flex-start;
  gap: 8px;
  margin-left: 8px;
  overflow: hidden;
  word-break: break-word;

  .chat-text {
    color: var(--text-kyy_color_text_1, #1A2139);
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    word-break: break-all;
    word-wrap: break-word;
    white-space: pre-line;
  }

  .chat-file {
    background-color: @kyy_white;
    width: 320px;
    padding: 8px 12px 0;
    border-radius: 4px;
    border: 1px solid @kyy_gray_3;
    overflow: hidden;
    user-select: none;

    & .file {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
    }

    & .file-icon {
      margin-right: 8px;
    }

    & .file-detail {
      flex: 1;
    }

    & .file-name {
      color: @kyy_font_1;
      font-size: 14px;
      line-height: 22px;
      margin-bottom: 4px;
      max-width: calc(100% - 50px);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      user-select: text;
    }

    & .file-size {
      color: @kyy_font_2;
      font-size: 12px;
      line-height: 220x;
    }

    & .send-failed {
      color: @kyy_red_6;
      border-top: 1px solid @kyy_gray_3;
      font-size: 14px;
      line-height: 38px;

      &:hover {
        color: @kyy_red_7;
      }
    }

    & .send-success {
      color: @kyy_brand_6;
      border-top: 1px solid @kyy_gray_3;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;

      & *:hover {
        color: @kyy_brand_7;
      }
    }

    & .divider {
      width: 1px;
      background-color: @kyy_gray_3;
      height: 22px;
      margin: 8px 0;
    }

    & .file-action {
      flex: 1;
      text-align: center;
      cursor: pointer;
      user-select: none;
    }
  }
  .text-link {
    word-break: break-all;
  }
}

.divider {
  width: calc(100% - 24px);
  height: 1px;
  margin: 16px 12px;
  background-color: #d6dbe3;
}

.error-status {
  display: block;
}

.error-status img {
  display: block;
  max-width: 200px;
  margin: auto;
}

.error-status div {
  display: block;
  text-align: center;
  color: #717376;
}

// 滚动条美化
.scrollbar {
  // 定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸
  ::-webkit-scrollbar {
    width: 2px;
    height: 2px;
    background-color: #f5f5f5;
  }

  // 定义滚动条轨道 内阴影+圆角
  ::-webkit-scrollbar-track {
    background-color: #e3e6eb;
  }

  // 定义滑块 内阴影+圆角
  ::-webkit-scrollbar-thumb {
    border-radius: 10px;
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
    background-color: #c8c8c8;
  }
}

.win-close {
  -webkit-app-region: no-drag;
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 48px;
  text-align: center;
  line-height: 48px;
  color:#516082;

  &:hover {
    background: var(--color-button_border-kyy_color_buttonBorder_bg_hover, #EAECFF);
  }
  &:active {
    background: var(--color-button_border-kyy_color_buttonBorder_bg_hover, #EAECFF);
  }
}

.video-item {
  overflow: hidden;
}

.video-item-preview {
  object-fit: fill;
}

.root {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: stretch;
  height: 100vh;
}

.msg-list {
  flex: 1;
  padding: 12px 0;
  display: flex;
  flex-direction: column;
  overflow: scroll;
}

.chat-img {
  overflow: hidden;
}

.msg-info {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  color: var(--text-kyy_color_text_3, #828DA5);
  font-size: 12px;
  line-height: 20px;
  font-weight: 400;
}
</style>
