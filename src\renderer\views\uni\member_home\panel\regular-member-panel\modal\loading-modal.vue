<template>
  <t-dialog
    v-model:visible="visible"
    header="正在导入"
    :z-index="2500"
    attach="body"
    width="480px"
    :footer="null"
    :close-btn="false"
  >
    <template #body>
      <div class="toBody">
        <t-progress :percentage="percentage" />
      </div>
    </template>
    <template #closeBtn>
      <!-- <iconpark-icon name="close" size="16"></iconpark-icon> -->
      <!-- <svg class="iconpark-icon" style="width: 16px; height: 16px">
        <use href="#close" />
      </svg> -->
      <iconpark-icon name="iconerror" style="font-size: 24px"></iconpark-icon>
    </template>
  </t-dialog>
</template>

<script lang="ts" setup>
/**
 * @description 选择组织
 * <AUTHOR>
 */
import { debounce } from "lodash";
import { Ref, reactive, ref } from "vue";
import { MessagePlugin } from "tdesign-vue-next";

const emits = defineEmits("reload");
const visible = ref(false);
const percentage = ref(0);
const progressInterval = ref(null);
const increaseProgress = () => {
  if (percentage.value >= 100) {
    console.log("进度已经达到100%");
    onClose();
    emits("reload");
    return;
  }

  percentage.value += 1;
  console.log(`当前进度: ${percentage.value}%`);
};
const onOpen = () => {
  percentage.value = 0;
  progressInterval.value = setInterval(increaseProgress, 100);
  visible.value = true;
};
const onClose = () => {
  clearInterval(progressInterval.value);
  visible.value = false;
};

defineExpose({
  onOpen,
  onClose
});
</script>

<style lang="less" scoped>
:deep(.t-dialog__header-content) {
  font-size: 16px;
  
  font-weight: 700;
  text-align: left;
  color: #13161b;
}

.toBody {
  //   max-height: 70vh;
  //   overflow: auto;
  // padding: 0 24px;
  // display: flex;
  // flex-direction: column;
  // align-items: center;
  .data {
    display: flex;
    flex-direction: column;
    .active {
      background: #daecff;
    }
    &-item {
      display: flex;
      align-items: center;
      padding: 8px;

      border-radius: 8px;
      margin-bottom: 4px;
      transition: all 0.15s linear;
      &:hover {
        background: #daecff;
      }
      .img {
        width: 40px;
        height: 40px;
      }
      .name {
        flex: 1;
        font-size: 14px;
        
        font-weight: 400;
        text-align: left;
        color: #13161b;
        margin-left: 8px;
        margin-right: 8px;
      }
      .icon {
        width: 24px;
        height: 24px;
        color: #2069e3;
      }
    }
  }
}

:deep(.t-form__item) {
  margin-bottom: 10px;
}

.t-dialog__ctx .t-dialog__position {
  padding: 0;
}
</style>
