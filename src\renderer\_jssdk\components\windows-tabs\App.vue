<template>
  <!-- :select-list="selectList" -->
  <div :data-tab-id="tabs_id" class="windows-tabs">
    <div class="windows-tabs-header">
      <StatusBar
        :title="title"
        @close="handleClose"
        @minimize="handleMinimize"
        @maximize="handleMaximize"
      />
    </div>
    <div :class="`windows-tabs-content ${isOnlyOneTabHideTabs && tabs.length <= 1 ? 'windows-tabs-content-only-one-tab-hide-tabs' : ''}`">
      <t-tabs v-model="value" :theme="theme" :scroll-position="scrollPosition"
        :class="`windows-tabs-content-tabs`"
        closable
        @remove="handleRemove"
      >
        <template #action>
          <div class="tabs-action">
            <div class="tabs-action-item">
              <span class="tabs-action-item-icon"
                v-if="tabsAction.refresh"
               @click="handleRefresh"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="17" height="14" viewBox="0 0 17 14" fill="none">
                  <path d="M2.00195 7C2.00195 3.68629 4.6299 1 7.87164 1C10.0774 1 11.9991 2.24373 13.002 4.08257" stroke="#516082" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M0.783178 4.81051L2.15887 7L4.34836 5.6243" stroke="#516082" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M14.002 7C14.002 10.3137 11.374 13 8.13226 13C5.92647 13 4.00486 11.7563 3.00195 9.91743" stroke="#516082" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M15.4043 9.18949L14.0286 7L11.8391 8.3757" stroke="#516082" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </span>
            </div>

            <div class="tabs-action-item"
              v-if="tabsAction.live"
              @click="handleLive"
            >
              <span class="tabs-action-item-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="14" viewBox="0 0 16 14" fill="none">
                  <path d="M6 3H1V13H13V8" stroke="#516082" stroke-width="1.4" stroke-linejoin="round"/>
                  <rect x="8" y="1" width="7" height="5" stroke="#516082" stroke-width="1.4" stroke-linejoin="round"/>
                </svg>
              </span>
            </div>
          </div>
        </template>
        <t-tab-panel v-for="item in tabs" :key="item.id" :value="item.id + ''" :label="`${item.title || '---'}`" :removable="item.hideCloseButton !== true">
          <template #label>
            <div class="tabs-label">
              <div v-if="item.icon || item.activeIcon || tabsPageInfo[item.id]?.icon" class="tabs-label-icon">
                <img
                  :src="getTabIcon(item) || tabsPageInfo[item.id]?.icon"
                  :class="{ 'is-grayscale': !isTabActive(item) }"
                  alt="icon"
                  @error="handleImageError"
                />
              </div>
              <span class="tabs-label-text">{{ item.title || tabsPageInfo[item.id]?.title || tabsPageInfo[item.id]?.url || item.url || '---' }}</span>
            </div>
          </template>
          <div class="windows-tabs-content-iframe">
            <iframe-component
              v-if="item.url && item.id && value === item.id"
              :key="item.id"
              :id="item.id"
              type="webview"
              :url="item.url"
              :is-load-in-component="false"
              :webview-extra-data="JSON.stringify({
                windowsTabsOptions: {
                  tabOptions: {
                    id: item.id,
                    title: item.title,
                    icon: item.icon,
                    activeIcon: item.activeIcon,
                    url: item.url,
                  },
                  tabsId: tabs_id,
                  tabsTitle: title,
                },
              })"
              @load="(e) => handleLoad(item.id, e)"
            />
          </div>
        </t-tab-panel>
      </t-tabs>
    </div>
    <div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';
import LynkerSDK from '@renderer/_jssdk';
import StatusBar from '@renderer/_jssdk/components/status-bar/index.vue';
import IframeComponent from '@renderer/_jssdk/components/iframe/index.vue';
import { DialogPlugin, Tabs as TTabs, TabPanel as TTabPanel } from 'tdesign-vue-next';
import { destroyNode, getShowNode, refreshNode } from '../iframe/iframePool';

const defaultIconSvg = `<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M10 18C14.4183 18 18 14.4183 18 10C18 5.58172 14.4183 2 10 2C5.58172 2 2 5.58172 2 10C2 14.4183 5.58172 18 10 18Z" stroke="#8A9199" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M2.5 10H17.5" stroke="#8A9199" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M10 2C12.7614 4.76142 12.7614 15.2386 10 18" stroke="#8A9199" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M10 2C7.23858 4.76142 7.23858 15.2386 10 18" stroke="#8A9199" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/></svg>`;
const defaultIconDataUri = `data:image/svg+xml;base64,${btoa(defaultIconSvg)}`;

// 1. 类型定义
type TabItem = {
  id: string;
  title?: string;
  icon?: string;
  activeIcon?: string;
  url?: string;
  beforeRefreshOptions?: {
    title?: string;
    content?: string;
  };
  beforeCloseOptions?: {
    title?: string;
    content?: string;
  };
  [key: string]: any;
};

const title = ref('');
const tabs_id = ref('');
const theme = ref('normal');
const scrollPosition = ref('auto');
const isOnlyOneTabHideTabs = ref(false);

const tabsAction = ref({
  refresh: true,
  live: false,
});

const tabsPageInfo = ref<Record<string, any>>({});

const handleLoad = (id: string, info: any) => {
  console.error('c', id, info);
  tabsPageInfo.value[id] = info;
}

const handleImageError = (event: Event) => {
  const target = event.target as HTMLImageElement;
  target.src = defaultIconDataUri;
  target.onerror = null;
};

// 2. 获取 sessionStorage key
const getTabsKey = () => `windows-tabs-list`;
const getTabsActiveKey = () => `windows-tabs-active-key`;

// 3. 加载 tabs
const loadTabs = (): TabItem[] => {
  try {
    const raw = sessionStorage.getItem(getTabsKey());
    return raw ? JSON.parse(raw) : [];
  } catch {
    return [];
  }
};

const loadValue = (): string => {
  try {
    const raw = sessionStorage.getItem(getTabsActiveKey());
    return raw ? raw : '';
  } catch {
    return '';
  }
};
// 4. tabs 响应式数据
const tabs = ref<TabItem[]>(loadTabs());

const value = ref<string>(loadValue());

const handleRemove = (item: any) => {
  removeTab({
    id: item.value,
  });
}

// 5. 监听 tabs 变化，写入 sessionStorage
watch(
  tabs,
  (val) => {
    sessionStorage.setItem(getTabsKey(), JSON.stringify(val));
  },
  { deep: true }
);

watch(
  value,
  () => {
    sessionStorage.setItem(getTabsActiveKey(), value.value);
  }
);
const handleBeforeClose = (title: string, content: string): Promise<boolean> => new Promise((resolve) => { const confirmDia = DialogPlugin.confirm({ theme: 'info', header: title, body: content, confirmBtn: '确认关闭', cancelBtn: { content: '取消', theme: 'default', variant: 'outline' }, onConfirm: async () => { confirmDia.hide(); resolve(true); }, onClose: () => { confirmDia.destroy(); resolve(false); }, }); });
const handleBeforeRefresh = (title: string, content: string): Promise<boolean> => new Promise((resolve) => { const confirmDia = DialogPlugin.confirm({ theme: 'info', header: title, body: content, confirmBtn: '确认刷新', cancelBtn: { content: '取消', theme: 'default', variant: 'outline' }, onConfirm: async () => { confirmDia.hide(); resolve(true); }, onClose: () => { confirmDia.destroy(); resolve(false); }, }); });

const addTab = (tab) => {
  const index = tabs.value.findIndex(t => t.id === tab.id);
  let url = tab.url;
  if (index !== -1) {
    // 已存在，更新
    tabs.value[index] = {
      ...tabs.value[index],
      ...tab,
      title: tab.title || tabs.value[index].title,
      icon: tab.icon || tabs.value[index].icon,
      activeIcon: tab.activeIcon || tabs.value[index].activeIcon,
      url: url || tabs.value[index].url || 'about:blank',
      beforeRefreshOptions: tab.beforeRefreshOptions || tabs.value[index].beforeRefreshOptions,
      beforeCloseOptions: tab.beforeCloseOptions || tabs.value[index].beforeCloseOptions,
    };
  } else {
    // 不存在，添加
    tabs.value.push({
      ...tab,
      url: url || 'about:blank',
    });
  }
  value.value = tab.id;
}

const removeTab = async (tab) => {
  const idx = tabs.value.findIndex(item => item.id === tab.id);
  const tabInfo = tabs.value[idx];
  if (tabInfo.beforeCloseOptions && (tabInfo.beforeCloseOptions.title || tabInfo.beforeCloseOptions.content)) {
    const isClose = await handleBeforeClose(tabInfo.beforeCloseOptions.title, tabInfo.beforeCloseOptions.content);
    if (!isClose) {
      return;
    }
  }
  tabs.value = tabs.value.filter(item => item.id !== tab.id);

  if (value.value === tab.id) {
    // 优先选前一个，没有就选后一个
    if (tabs.value.length > 0) {
      const newIdx = idx > 0 ? idx - 1 : 0;
      value.value = tabs.value[newIdx]?.id;
    } else {
      value.value = '';
    }
  }
  destroyNode(tab.id);
  if (tabs.value.length === 0) {
    window.close();
  }
}
const getTabKey = (id: string) => {
  return `windows_tabs_${id}`;
}
const init = () => {
  const search = new URLSearchParams(window.location.search);
  const data = search.get('data');
  const options = JSON.parse(decodeURIComponent(data));
  title.value = options.tabs_title;
  tabs_id.value = options.tabs_id;
  isOnlyOneTabHideTabs.value = options.isOnlyOneTabHideTabs;
  LynkerSDK.ipc.handleRenderer( `${getTabKey(tabs_id.value)}-window-update`, async (data: {title?: string, isOnlyOneTabHideTabs?: boolean}) => {
    title.value = data.title || title.value;
    isOnlyOneTabHideTabs.value = data.isOnlyOneTabHideTabs || isOnlyOneTabHideTabs.value;
  });
  LynkerSDK.ipc.handleRenderer( `${getTabKey(tabs_id.value)}-options`, async (data: {title?: string, isOnlyOneTabHideTabs?: boolean}) => {
    title.value = data.title;
    isOnlyOneTabHideTabs.value = data.isOnlyOneTabHideTabs;
  });
  LynkerSDK.ipc.handleRenderer( `${getTabKey(tabs_id.value)}-open`, async (data: TabItem) => {
    addTab(data)
  });
  LynkerSDK.ipc.handleRenderer( `${getTabKey(tabs_id.value)}-close`, async (data: TabItem) => {
    removeTab(data)
  });
  LynkerSDK.ipc.handleRenderer(`${getTabKey(tabs_id.value)}-is-inited`, async () => {
    return true;
  });
}

const handleClose = async () => {
  const currentTab = tabs.value.find(item => item.id === value.value);
  if (currentTab) {
    if (currentTab.beforeCloseOptions && (currentTab.beforeCloseOptions.title || currentTab.beforeCloseOptions.content)) {
      const isClose = await handleBeforeClose(currentTab.beforeCloseOptions.title, currentTab.beforeCloseOptions.content);
      if (!isClose) {
        return;
      }
    }
  }
  window.close();
}
const handleMinimize = async () => {
  // @ts-ignore
  (await LynkerSDK.getCurrentWindow())!?.minimize();
}
const handleMaximize = async () => {
  const currentWindow = (await LynkerSDK.getCurrentWindow()) as any;
  if (currentWindow.isMaximized()) {
    currentWindow.unmaximize();
  } else {
    currentWindow.maximize();
  }
}

const handleRefresh = async () => {
  const currentTab = tabs.value.find(item => item.id === value.value);
  if (currentTab) {
    if (currentTab.beforeRefreshOptions && (currentTab.beforeRefreshOptions.title || currentTab.beforeRefreshOptions.content)) {
      const isClose = await handleBeforeRefresh(currentTab.beforeRefreshOptions.title, currentTab.beforeRefreshOptions.content);
      if (!isClose) {
        return;
      }
    }
    const showNode = getShowNode();
    if (showNode.length > 0) {
      showNode.forEach((node) => {
        refreshNode(node);
      });
    }
  }

}

const handleLive = () => {
  // window.location.reload();
}

/**
 * 判断当前 tab 是否为激活状态
 * @param item TabItem
 * @returns boolean
 */
const isTabActive = (item: TabItem): boolean => value.value === item.id;

/**
 * 获取当前 tab 应显示的 icon 路径
 * - 选中时优先 activeIcon
 * - 未选中时 icon
 * @param item TabItem
 * @returns string
 */
const getTabIcon = (item: TabItem): string => {
  return isTabActive(item) ? (item.activeIcon || item.icon) : item.icon || '';
};

onMounted(() => {
  // fix routerView 有背景色
  let style = document.createElement('style');
  style.innerHTML = `
    .routerView {
      background: transparent!important;
      background-color: transparent!important;
    }
  `;
  // 将 style 标签添加到页面的 head 中
  document.head.appendChild(style);
  init();
});

</script>
<style lang="less" scoped>
  .windows-tabs {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    background-color: #fff;
    display: flex;
    flex-direction: column;
  }
  .windows-tabs-header {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    -webkit-app-region: drag;
    flex-shrink: 0;
  }
  .windows-tabs-content {
    flex: 1;
    &.windows-tabs-content-only-one-tab-hide-tabs {
      :deep(.t-tabs__nav-wrap) {
        display: none;
      }
    }
  }
  .windows-tabs-content-iframe {
    flex: 1;
    height: 100%;
  }
  .windows-tabs-content-tabs {
    display: flex;
    flex-direction: column;
    height: 100%;
    flex: 1;
    :deep(.t-tabs__content) {
      display: flex;
      flex-direction: column;
      height: 0;
      flex: 1;
    }
    :deep(.t-tab-panel) {
      flex: 1;
      height: 0;
    }
  }
  .windows-tabs-content-tabs {
    background-color: rgba(235, 241, 252, 1);
    .tabs-label {
      display: flex;
      align-items: center;
      flex-direction: row;
      gap: 8px;
      width: 100%;
      .tabs-label-icon {
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        border-radius: 4px;
        img {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
        img.is-grayscale {
          filter: grayscale(1);
          opacity: 0.6;
        }
      }
      .tabs-label-text {
        width: 0;
        flex: 1;
        overflow: hidden;
        color:  #1A2139;
        overflow: hidden;
        text-overflow: ellipsis;
        font-family: PingFang SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        text-shadow: none!important;
      }
    }
    :deep(.t-tabs__nav-wrap) {
      padding-left: 4px;
    }
    :deep(.t-tabs__operations) {
      height: 100%;
    }
    :deep(.t-tabs__btn) {
      height: 100%;
      background-color: rgba(235, 241, 252, 1);
    }
    :deep(.t-tabs__bar) {
      display: none;
    }
    :deep(.t-tabs__nav-item) {
      margin: 4px 0;
      display: flex;
      width: 168px;
      height: 32px;
      padding: 0px 12px;
      align-items: center;
      gap: 8px;
      flex-shrink: 0;
      border-radius: 4px;
      color: var(--kyy_color_tabbar_item_text_active, #516082);
      &:hover {
        .t-tabs__nav-item-wrapper {
          background-color: transparent;
        }
        background-color: rgba(255,255,255,0.5);
        &::after, & + .t-tabs__nav-item::after {
          display: none;
        }
      }
      & + .t-tabs__nav-item::after {
        content: '';
        width: 1px;
        height: 20px;
        position: absolute;
        top: 7px;
        left: 0px;
        background: #D5DBE4;
        pointer-events: none;
      }
    }
    :deep(.t-is-active) {
      background-color: var(--kyy_color_tabbar_item_bg_active, #FFF)!important;
      &::after {
        display: none;
      }
      & + .t-tabs__nav-item {
        &::after {
          display: none;
        }
      }
    }

    :deep(.t-tabs__nav-item-wrapper) {
      flex: 1;
      height: 100%;
      padding: 0;
      margin: 0;
      overflow: hidden;
    }
    :deep(.t-tabs__nav-item-text-wrapper) {
      display: flex;
      flex: 1;
      height: 100%;
    }
    :deep(.t-icon-close) {
      margin-left: 0;
    }
  }
  .tabs-action {
    display: flex;
    padding: 0 16px;
    min-width: 50px;
    flex-shrink: 0;
    align-items: center;
    justify-content: center;
    position: relative;
    right: 0;
    top: 0;
    z-index: 3;
    background-color: rgba(235, 241, 252, 1);
    gap: 16px;
    .tabs-action-item {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      cursor: pointer;
      .tabs-action-item-icon {
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        svg {
          display: block;
        }
      }
    }
  }
</style>
