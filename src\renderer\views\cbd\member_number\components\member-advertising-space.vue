<template>
  <div class="advertising-space" ref="elementRef">
    <div class="swiper-box" :class="placeType1[current]?.source_type===1?'guanggao':''">
      <t-swiper class="big-ad-swiper" 
      animation="fade" 
      loop :class="placeType1.length < 2 ? 'not-dian' : ''" :autoplay="autoplay"
        @change="onChanges"  v-if="placeType1.length > 0 && showFlag" v-model:current="current"
        trigger="click" :duration="600"
        :navigation="{ type: 'dots', showSlideBtn: placeType1.length < 2 ? 'never' : 'hover' }"  :interval="5000">
        <t-swiper-item style="border-radius: 8px;height: 308px;" @click="viewDetail(item,current)" v-for="item in placeType1"
          :key="item.id">
          <img class="swiper-ad-img" :src="item.image_url" />
        </t-swiper-item>
      </t-swiper>
      <!-- 默认图片 -->
      <img v-else class="swiper-ad-img" @click="toAdd(member)" style="border-radius: 8px;"  :src="curLang?jtptlb:ftptlb" />
    </div>
    <div class="big-ad-img-box">
      <div :class="placeType2[setBigMarkertRightTopIndex]?.source_type===1?'guanggao':''" style="width: 224px; height: 148px;">

        <img v-if="placeType2.length > 0&& setBigMarkertRightTopIndex !== null"
          @click="viewDetail(placeType2[setBigMarkertRightTopIndex],setBigMarkertRightTopIndex)"
          :src="placeType2[setBigMarkertRightTopIndex]?.image_url" />
        <img v-else :src="curLang?jtptys:ftptys" @click="toAdd(member)" />
      </div>
      <div :class="placeType3[setBigMarkertRightBottomIndex]?.source_type===1?'guanggao':''"
        style="width: 224px; height: 148px;">

        <img v-if="placeType3.length > 0&& setBigMarkertRightBottomIndex !== null"
          @click="viewDetail(placeType3[setBigMarkertRightBottomIndex],setBigMarkertRightBottomIndex)"
          :src="placeType3[setBigMarkertRightBottomIndex]?.image_url" />
        <img v-else :src="curLang?jtptyx:ftptyx" @click="toAdd(member)" />
      </div>
    </div>
  </div>
</template>
<script setup lang="ts" name="advertising-space">
  import { ref, watch, watchEffect, onMounted, onUnmounted, toRaw } from "vue";
  import { useRoute, useRouter } from "vue-router";
  import { adfrontadshow, adCommonAddEvent, adfrontclickshow } from "@/views/big-market/apis";
  import { getProfilesInfo } from "@renderer/utils/auth";
  import { toSquareHome } from "@renderer/views/square/utils/ipcHelper";
  import useNavigate from "@renderer/views/square/hooks/navigate";
  import { getOpenid } from "@renderer/utils/auth";
  import { getSquareByopenId } from "@/api/business/manage";
  import { ClientSide } from "@renderer/types/enumer";
  import { useDigitalPlatformStore } from "@renderer/views/digital-platform/store/digital-platform-store";
  import useRouterHelper from "@renderer/views/square/hooks/routerHelper";
  import { getNowDigitalType } from "@/views/digital-platform/marketplace/utils/index.ts";
  import { platform } from "@renderer/views/digital-platform/utils/constant";
  import LynkerSDK from "@renderer/_jssdk";
  const { ipcRenderer, shell } = LynkerSDK;
  import { configInfo, isNotMac } from '@renderer/views/setting/util';

  const ftptlb = new URL("@/assets/member/ptsc/ftptlb.png", import.meta.url);
  const jtptlb = new URL("@/assets/member/ptsc/jtptlb.png", import.meta.url);

  const jtptys = new URL("@/assets/member/ptsc/jtptys.png", import.meta.url);

  const ftptys = new URL("@/assets/member/ptsc/ftptys.png", import.meta.url);

  const jtptyx = new URL("@/assets/member/ptsc/jtptyx.png", import.meta.url);

  const ftptyx = new URL("@/assets/member/ptsc/ftptyx.png", import.meta.url);



  import to from "await-to-js";
  import { getLang } from "@renderer/utils/auth";
  let curLang = getLang() == "zh-cn" || !getLang();

  const digitalPlatformStore = useDigitalPlatformStore();
  const digitalRouter = useRouterHelper("digitalPlatformIndex");
  const { goHomePage } = useNavigate();

  const props = defineProps({

    teamId: {
      type: String,
      default: "",
    },
    currentMemberCard: {
      type: Object,
      default: () => { },
    },
    member: {
      type: Boolean,
      default: false,
    }

  });

  const route = useRoute();
  const autoplay = ref(true);
  const elementRef = ref(null);
  const isInViewport = ref(false);


  const profileInfo = getProfilesInfo();
  const setBigMarkertRightTopIndex = ref(null);
  const setBigMarkertRightBottomIndex = ref(null);
  const bigMarkertSwiper = ref(window.localStorage.getItem("CBDPTLBIndex")); //轮播索引
  const placeType1 = ref([]);
  const placeType2 = ref([]);
  const placeType3 = ref([]);
  const placeType1Length = ref([]);
  const current = ref(0);
  const placeType2Length = ref([]);
  const placeType3Length = ref([]);
  const onChanges = (val, val1) => {
    console.log("有触发吗");
    current.value = val;

    if (autoplay.value) {

      setBigMarkertSwiper();
      setViewNumSwiper(); //增加访问数
    }
  };
  const setBigMarkertSwiper = () => {
    window.localStorage.setItem("CBDPTLBIndex", current.value);
  };
  const setBigMarkertRightTop = () => {
    window.localStorage.setItem("CBDPYSIndex", setBigMarkertRightTopIndex.value);
  };
  const setBigMarkertRightBottom = () => {
    window.localStorage.setItem("CBDPYXIndex", setBigMarkertRightBottomIndex.value);
  };
  const setViewNumRightBottom = () => {
    if (placeType3Length.value.includes(setBigMarkertRightBottomIndex.value)) {
      if (placeType3.value[setBigMarkertRightBottomIndex.value]?.ad_id) {
        adfrontclickshow(
          {
            ad_id: placeType3.value[setBigMarkertRightBottomIndex.value].ad_id,
            add_show: true,
            add_click: false,
          },
          props.teamId,
        );
        adCommonAddEvent(
          {
            ad_id: placeType3.value[setBigMarkertRightBottomIndex.value].ad_id,
            version: configInfo.version,
            product_type: isNotMac ? 5 : 4,
            serial_num: setBigMarkertRightBottomIndex.value,
            event_type: 1,//2点击1展示
            occur_at: Math.round(new Date().getTime() / 1000),
            report_at: Math.round(new Date().getTime() / 1000),
            source_type: placeType3.value[setBigMarkertRightBottomIndex.value]?.source_type,
          },
          props.teamId,
        );
      }
    }
  };
  const setViewNumRightTop = () => {
    if (placeType2Length.value.includes(setBigMarkertRightTopIndex.value)) {
      adfrontclickshow(
        {
          ad_id: placeType2.value[setBigMarkertRightTopIndex.value].ad_id,
          add_show: true,
          add_click: false,
        },
        props.teamId,
      );
      adCommonAddEvent(
        {
          ad_id: placeType2.value[setBigMarkertRightTopIndex.value].ad_id,
          version: configInfo.version,
          serial_num: setBigMarkertRightTopIndex.value,
          product_type: isNotMac ? 5 : 4,
          event_type: 1,//1点击2展示
          occur_at: Math.round(new Date().getTime() / 1000),
          report_at: Math.round(new Date().getTime() / 1000),
          source_type: placeType2.value[setBigMarkertRightTopIndex.value]?.source_type,
        },
        props.teamId,
      );
    }
  };
  const setViewNumSwiper = (ad_id) => {
    if (placeType1Length.value.includes(current.value)) {
      adfrontclickshow(
        {
          ad_id: placeType1.value[current.value].ad_id,
          add_show: true,
          add_click: false,
        },
        props.teamId,
      );
    
      adCommonAddEvent(
        {
          ad_id: placeType1.value[current.value].ad_id,
          add_show: true,
          serial_num: current.value,
          version: configInfo.version,
          product_type: isNotMac ? 5 : 4,
          event_type: 1,//1点击2展示
          occur_at: Math.round(new Date().getTime() / 1000),
          report_at: Math.round(new Date().getTime() / 1000),
          source_type: placeType1.value[current.value]?.source_type,
        },
        props.teamId,
      );
      const index = placeType1Length.value.indexOf(current.value);
      if (index > -1) {
        placeType1Length.value.splice(index, 1);
      }
    }
  };
  const isSelfSquare = async () => {
    const [err, res] = await to(getSquareByopenId(getOpenid()));
    if (err) return false;
    const { data } = res;
    console.log("data", data);
    return data.opened && data.selfOpened;
  };
  const router = useRouter()
  const toAdd = (val) => {
    if (val) {//有平台身份
      // router.push({ path: "/bigMarketIndex/bigMarketAdd", query: { teamId: props.teamId } });
      router.push({
        path: `/digitalPlatformIndex/cbd_addAd?platform_type=3&teamId=${props.teamId}&currentMemberCard=${props.currentMemberCard?.id}`,
        query: {
          teamId: props.teamId,
          restData: true,
          platform_type: 3,
          currentMemberCard: props.currentMemberCard?.id,
        },
      });
      const searchMenu = digitalRouter.routeList.find((v) => v.name === "cbd_addAd");
      digitalPlatformStore.addTab(
        {
          ...searchMenu,
          query: {
            teamId: props.teamId,
            platform_type: 3,
            currentMemberCard: props.currentMemberCard?.id,
          },
        },
      );
    }
  }
  const viewDetail = async (row, itemIndex) => {
    console.log(row, "riwwww");
    if (row.skip_type === 5) {
      const url = LynkerSDK.getH5UrlWithParams(`shop/index.html#/product-detail/${row.skip_param}`, { teamId: props.teamId , platformTeamId:props.teamId});
      LynkerSDK.digitalPlatform.openTabForWebview({ title: `商品详情`, url: url, path_uuid: `productDetailTab-${row.skip_param}` })
    }
    if (row.skip_type === 1) {
      // console.log("viewDetail", row);
      // const path = "/bigMarketIndex/bigMarketDetailReadOnly";
      // const query = { uuid: row.skip_param.toString(), from: "big-market" };
      // ipcRenderer.invoke("set-big-market-tab-item", {
      //   path: `/bigMarketIndex/bigMarketDetailReadOnly`,
      //   path_uuid: "bigMarket",
      //   title: "商机详情",
      //   addNew: true,
      //   query,
      //   name: "bigMarketDetailReadOnly",
      //   type: ClientSide.BIGMARKET,
      // });
      // router.push({ path, query });
      const type = getNowDigitalType();
      const pageKey = `digital_platform_${type}_rich_detail`;
      const searchMenu = digitalRouter.routeList.find((v) => v.name.includes(pageKey));
      searchMenu.query = { uuid: row.skip_param, platform: platform.digitalPlatform, from: type };
      router.push({ path: searchMenu.fullPath, query: searchMenu.query });
      searchMenu.title = '商机详情';
      digitalPlatformStore.addTab(toRaw(searchMenu), true);
    }
    if (row.skip_type === 2) {
      const data = { ip_region: "" };
      const res = await isSelfSquare();
      if (res) {
        toSquareHome(row.skip_param);
      } else {
        goHomePage({ squareId: row.skip_param });
      }
    }
    if (row.skip_type === 3) {
      shell.openExternal(row.skip_param);
    }
    adfrontclickshow(
        {
          ad_id: row.ad_id,
          add_show: false,
          add_click: true,
        },
        props.teamId,
      );
    adCommonAddEvent(
      {
        ad_id: row.ad_id,
        add_show: true,
        version: configInfo.version,
        product_type: isNotMac ? 5 : 4,
        event_type: 2,//1点击2展示
        occur_at: Math.round(new Date().getTime() / 1000),
        serial_num: itemIndex,
        report_at: Math.round(new Date().getTime() / 1000),
        source_type: row.source_type,
      },
      props.teamId,
    );
  };
  onUnmounted(() => { });
  // ipcRenderer.on("setBigMarkertSwiperIndex-swiper", () => {
  //   setBigMarkertSwiper();
  //   console.log("setBigMarkertSwipersetBigMarkertSwiper");
  // });
  let showFlag = ref(false);
  onMounted(async () => {
    await getList();
    placeType1Length.value = [];
    placeType2Length.value = [];
    placeType3Length.value = [];
    for (let index = 0; index < placeType1.value.length; index++) {
      placeType1Length.value.push(index);
    }
    for (let index = 0; index < placeType2.value.length; index++) {
      placeType2Length.value.push(index);
    }
    for (let index = 0; index < placeType3.value.length; index++) {
      placeType3Length.value.push(index);
    }
    if (bigMarkertSwiper.value) {
      let num = bigMarkertSwiper.value - 0 + 1;
      if (num > placeType1.value.length - 1) {
        current.value = 0;
      } else {
        current.value = num||0;
      }
    } else {
      current.value = 0;
    }
    showFlag.value = true;
    setBigMarkertSwiper();
    setViewNumSwiper();

    let numys = window.localStorage.getItem("CBDPYSIndex");
    if (numys || numys == 0) {
      if (numys >= placeType2.value.length - 1) {
        setBigMarkertRightTopIndex.value = 0;
      } else {
        setBigMarkertRightTopIndex.value = numys - 0 + 1;
      }
    } else {
      setBigMarkertRightTopIndex.value = 0;
    }
    let numyx = window.localStorage.getItem("CBDPYXIndex");
    if (numyx || numyx == 0) {
      if (numyx >= placeType3.value.length - 1) {
        setBigMarkertRightBottomIndex.value = 0;
      } else {
        setBigMarkertRightBottomIndex.value = numyx - 0 + 1;
      }
    } else {
      setBigMarkertRightBottomIndex.value = 0;
    }
    console.log(placeType3.value, "asdasdas");
    if (setBigMarkertRightBottomIndex.value) {
      setBigMarkertRightBottomIndex.value = setBigMarkertRightBottomIndex.value - 0 + 1;
      if (setBigMarkertRightBottomIndex.value > placeType3.value.length - 1) {
        setBigMarkertRightBottomIndex.value = 0;
      }
    } else {
      setBigMarkertRightBottomIndex.value = 0;
    }
    setBigMarkertRightTop();
    setViewNumRightTop();
    setViewNumRightBottom();
    setBigMarkertRightBottom();
    const observer = new IntersectionObserver((entries) => {
      const entry = entries[0];
      isInViewport.value = entry.isIntersecting;
    });
    if (elementRef.value) {
      // 确保元素已经存在
      observer.observe(elementRef.value);
    }
  });
  const editAdSwiper = (val) => {
    console.log("开始或者暂停", val);
    if (isInViewport.value && val) {
      autoplay.value = true;
    } else {
      autoplay.value = false;
    }
  };

  const getList = async () => {
    console.log(props.addressData, "propspropsaddressDataaddressData");
    const res1 = await adfrontadshow(
      {
        platform_type: 3,
        place_type: 5,
      },
      props.teamId,
    );
    placeType1.value = res1.data.data.list;
    const res2 = await adfrontadshow(
      {
        platform_type: 3,
        place_type: 6,
      },
      props.teamId,
    );
    placeType2.value = res2.data.data.list;

    const res3 = await adfrontadshow(
      {
        platform_type: 3,
        place_type: 7,
      },
      props.teamId,
    );
    placeType3.value = res3.data.data.list;
  };
  // watch(
  //   () => route.path,
  //   () => {
  //     console.log(" route.path", route.path);
  //     if (route.path === "/bigMarketIndex/home" && route.query?.refresh) {
  //       console.log("homeRefresh");
  //     }
  //   },
  // );
  watch(
    () => props.addressData,
    () => {
      getList();
      console.log(props.addressData, "props.addressDataprops.addressData");
    },
  );
  watchEffect(() => {
    if (!isInViewport.value) {
      // 不在可视区时
      autoplay.value = false;
    } else {
      autoplay.value = true;
    }
    console.log(route, "路由");
    console.log(isInViewport.value, "再可视区吗");
  });
  defineExpose({
    editAdSwiper,
  });
</script>
<style lang="less" scoped>
   :global(.big-ad-swiper .t-swiper__navigation-item){
    scale: 2;
    margin: 0 8px;
  }
  .not-dian {
    :deep(.t-swiper__navigation) {
      display: none;
    }
  }

  .guanggao {
    position: relative;
  }

  .guanggao::after {
    content: "广告";
    position: absolute;
    width: 32px;
    height: 20px;
    z-index: 999;
    right: -1px;
    top: -1px;
    border-radius: 0px 8px;
    background: #F5F8FE;
    color: #ACB3C0;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    text-align: center;
    padding: 0 4px;
  }

  .advertising-space {
    display: flex;
    height: 308px;
    gap: 12px;
    -webkit-user-select: none;
    /* Chrome/Safari */
    -moz-user-select: none;
    /* Firefox */
    -ms-user-select: none;
    /* IE10+ */
    user-select: none;
    /* Standard */
  }

  .swiper-ad-img {
    width: 608px;
    height: 308px;
    transform: translateZ(0);
    object-fit: cover;
  }

  :deep(.t-swiper__content) {
    border-radius: 8px !important;
  }

  .big-ad-img-box {
    display: flex;
    cursor: pointer;
    flex-wrap: wrap;
    gap: 12px;

    img {
      width: 224px;
      height: 148px;
      border-radius: 8px;
      flex-shrink: 0;
    }
  }

  .big-ad-swiper {
    width: 608px;
    height: 308px;
  }

  .swiper-box {
    border-radius: 8px;
  }

  :deep(.t-swiper__arrow-left) {
    width: 32px;
    height: 32px;
    background: var(--icon-kyy_color_icon_transparent, rgba(26, 33, 57, 0.36));
    border-radius: 50%;
    color: #fff;
  }

  :deep(.t-swiper__arrow-right) {
    width: 32px;
    height: 32px;
    color: #fff;
    border-radius: 50%;
    background: var(--icon-kyy_color_icon_transparent, rgba(26, 33, 57, 0.36));
  }
</style>