<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import LynkerSDK from '@renderer/_jssdk';
import { RUploadImage } from '@rk/unitPark';
import { getSquarePromoteApi, updateSquarePromoteApi, getSquarePromoteRedirectTypesApi } from '@renderer/api/square/square';
import { MessagePlugin } from 'tdesign-vue-next';
import DemoDialog from './components/DemoDialog.vue';
import { useSquareStore } from '@/views/square/store/square';

const { t } = useI18n();
const squareStore = useSquareStore();

let squareId = '';
const redirectPage = ref('');
const options = ref([]);

const selectChange = (value: string) => {
  redirectPage.value = value;
  // savePromote(t('square.post.tip6'));

  savePromote(t('address.submit_success'));
};

const uploadedImages = ref<string[]>([]);
const handlePreview = (image) => {
  console.log('===>handlePreview', image, uploadedImages.value);
  LynkerSDK.previewImage(image);
};
const uploadChange = (images) => {
  console.log('===>imagesuploadChange', images, uploadedImages.value);
  if (images.length === uploadedImages.value.length) return;
  const tips = images.length < uploadedImages.value.length ? '删除成功' : '上传成功';
  savePromote(tips);
};
const sortChange = (images) => {
  console.log('===>sort', images, uploadedImages.value);
  savePromote();
};

const introduction = ref('');
let introductionText = '';
const showSave = ref(false);
const inputChange = (value) => {
  showSave.value = value !== introductionText;
};
const saveIntroduction = () => {
  // 接口
  savePromote();
  showSave.value = false;
};
const cancelSave = () => {
  introduction.value = introductionText;
  showSave.value = false;
};
let promoteData:any = {};
const getSquarePromote = async () => {
  const { data } = await getSquarePromoteRedirectTypesApi(squareId, squareStore.teamId);
  options.value = data.data?.redirectPageType?.map((item) => ({
    label: item.name,
    value: item.type === 'REDIRECT_PAGE_EXTERNAL_APP' ? item.appId : item.type,
    appId: item.appId,
  }));
  getSquarePromoteApi(squareId, squareStore.teamId).then((res) => {
    const { squarePromote } = res.data.data;
    if (!squarePromote) return;
    introduction.value = squarePromote.serviceTypeIntro;
    introductionText = squarePromote.serviceTypeIntro;
    uploadedImages.value = squarePromote.promotionalImages || [];
    if (squarePromote.redirectPageType === 'REDIRECT_PAGE_UNKNOWN') {
      redirectPage.value = '';
    } else if (squarePromote.redirectPageType === 'REDIRECT_PAGE_EXTERNAL_APP') {
      redirectPage.value = squarePromote.externalAppId;
    } else {
      redirectPage.value = squarePromote.redirectPageType;
    }
    promoteData = squarePromote;
  });
};
const savePromote = (tips?) => {
  setTimeout(() => {
    updateSquarePromoteApi({ squarePromote: {
      id: promoteData.id,
      squareId,
      serviceTypeIntro: introduction.value,
      promotionalImages: uploadedImages.value,
      redirectPageType: typeof redirectPage.value === 'number' ? 'REDIRECT_PAGE_EXTERNAL_APP' : redirectPage.value,
      externalAppId: typeof redirectPage.value === 'number' ? redirectPage.value : 0,
    } }, squareStore.teamId).then((res) => {
      console.log('===>res', res.data);
      MessagePlugin.success({
        content: tips || t('address.submit_success'),
      });
    });
  }, 100);
};
onMounted(() => {
  const squareLocal = localStorage.getItem('square');
  if (!squareLocal) return;
  const squareCache = JSON.parse(squareLocal);
  squareId = squareCache.annualFeeInfo?.square?.squareId;
  getSquarePromote();
});

const demoVisible = ref(false);
</script>

<template>
  <div class="promote-to-bigmarket">
    <div class="top-btn" @click="demoVisible = true">
      {{ t('square.page.preview') }}
    </div>
    <DemoDialog v-model:visible="demoVisible" />

    <div class="tips mt-16 mb-8">
      <t-text>
        {{ t('square.page.promotionTips1') }}
      </t-text>
    </div>
    <t-select
      v-model:value="redirectPage"
      :options="options"
      :placeholder="t('square.page.preview')"
      style="width: 435px"
      @change="selectChange"
    >
      <template #suffixIcon>
        <img src="@/assets/svg/icon_arrow_down.svg" alt="">
      </template>
    </t-select>
    <div class="tips mt-24 mb-8 flex items-center">
      <t-text class="mr-4">
        {{ t('square.page.promotionTips2') }}
      </t-text>
      <t-tooltip placement="bottom">
        <template #content>
          <div>温馨提示：（长按拖动）</div>
          <div>1、建议上传图片尺寸1080*640，大小不超2M</div>
          <div>2、支持格式：jpg/jpeg/png/gif/bmp/webp</div>
          <div>3、最多可上传3张图片。</div>
        </template>
        <iconpark-icon name="iconunusual" class="iconunusual" />
      </t-tooltip>
    </div>
    <div class="publicity">
      <RUploadImage
        v-model="uploadedImages"
        root-dir="square"
        :max-count="3"
        :enable-crop="true"
        :cropper-props="{
          title: '编辑图片',
          options: {
            aspectRatio: 1080 / 640,
            viewMode: 1,
          },
        }"
        :sortable="true"
        :size="80"
        :size-limit="{
          size: 2,
          unit: 'MB',
          message: '文件大小不能超过2MB',
        }"
        accept="image/jpg, image/jpeg, image/png, image/gif, image/bmp, image/webp"
        @click="handlePreview"
        @change="uploadChange"
        @sort="sortChange"
      />
      <t-textarea
        v-model="introduction"
        class="text-area"
        :autosize="{ minRows: 4, maxRows: 8 }"
        :maxlength="300"
        clearable
        :placeholder="t('square.page.introduction')"
        @change="inputChange"
      />
      <div v-if="showSave" class="flex justify-end mt-8">
        <t-button
          variant="outline"
          shape="round"
          @click="cancelSave"
        >
          {{ t('square.action.cancel') }}
        </t-button>
        <t-button
          theme="primary"
          variant="outline"
          shape="round"
          style="margin-left: 8px;"
          @click="saveIntroduction"
        >
          {{ t('square.action.save') }}
        </t-button>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.promote-to-bigmarket{
  background-color: #fff;
  padding:16px;
}
.publicity {
  padding: 16px;
  border-radius: 12px;
  border: 1px solid #BEE2FF;
  background: linear-gradient(180deg, rgba(227, 249, 254, 0.50) 0%, rgba(245, 250, 253, 0.50) 100%);
}
.text-area {
    margin-top: 12px !important;
    :deep(.t-textarea__limit) {
      display: inline !important;
      line-height: 20px;
      position: absolute;
      right: 7px;
      bottom: 7px;
      color: var(--text-kyy_color_text_5, #acb3c0);
      font-size: 12px;
    }
    :deep(.t-textarea__inner){
      padding-bottom: 24px;
    }
}
.top-btn {
  display: flex;
  align-items: center;
  width: fit-content;
  padding: 0px 12px;
  gap: 4px;
  border-radius: var(--radius-kyy_radius_button_s, 4px);
  border: 1px solid var(--color-button_secondaryBrand-kyy_color_button_secondaryBrand_border_dedault, #4d5eff);
  background: var(--color-button_secondaryBrand-kyy_color_button_secondrayBorder_bg_default, #eaecff);
  color: var(--color-button_secondaryBrand-kyy_color_button_secondrayBorder_text_default, #4d5eff);
  /* kyy_fontSize_2/regular */
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
  cursor: pointer;
}
</style>
