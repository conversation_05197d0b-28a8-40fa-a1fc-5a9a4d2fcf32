<script setup lang="ts">
import { useVModel } from '@vueuse/core';
import { ref } from 'vue';
import { OrganizationType, RingkolMarketMarketSquare } from '@renderer/views/big-market/types';
import SquareDetailCard from '@/views/big-market/components/SquareDetailCard.vue';
import SquareItemCard from '@/views/big-market/components/SquareItemCard.vue';

const props = defineProps<{
  modelValue?: boolean;
}>();

const visible = useVModel(props, 'modelValue');

const logo = 'https://kuaiyouyi.oss-cn-shenzhen.aliyuncs.com/square/e607e7c/logo.png';
const demoData = ref<RingkolMarketMarketSquare>({
  id: 1,
  isAd: true,
  imgs: [logo, logo, logo],
  avatar: logo,
  name: '示例广场号',
  publicId: 'd2qmikyp0ykg',
  followed: true,
  fansCount: 9999999,
  distance: 9999999,
  industry: '示例行业',
  intro: '组织广场号信息简介、发展历程、文化理念、竞争优势、战略规划等（组织广场号中设置）',
  serviceTypeIntro: '组织广场号主营的商品或服务介绍',
  squareOrganize: {
    name: '另可官方认证过的组织名称（组织广场号中设置）',
  },
  region: '组织所在地的详细信息（组织广场号中设置）',
  phoneNumbers: [{ countryCode: '', number: '组织广场号的客服电话（组织广场号中设置）' }],
  organizationType: 'ENTERPRISE' as OrganizationType,
  isFollow: true,
});
</script>

<template>
  <t-dialog
    v-model:visible="visible"
    width="655"
    attach="body"
    placement="center"
    prevent-scroll-through
    header="大市场广场号曝光演示"
    :cancel-btn="null"
    :footer="null"
    class="promote-demo-dialog"
    destroy-on-close
    v-bind="$attrs"
  >
    <template #closeBtn>
      <iconpark-icon
        name="iconerror-a961a3n0"
        class="text-24 cursor-pointer color-[#516082]"
        @click="visible = false"
      />
    </template>

    <div class="content">
      <div class="item-card">
        <SquareItemCard :data="demoData" />
        <div class="mt-8 text-14 color-text-2">
          <p>大市场中点击【卡片】会根据你设置的业务，进入到“广场对应的业务页签”下</p>
          <br>
          <p>鼠标悬浮卡片时出现右侧弹窗</p>
        </div>
      </div>
      <div class="detail-card">
        <SquareDetailCard :data="demoData" />
      </div>
    </div>
  </t-dialog>
</template>

<style lang="less">
.promote-demo-dialog {
  .t-dialog {
    padding: 12px !important;
    background-image: url('https://kuaiyouyi.oss-cn-shenzhen.aliyuncs.com/square/c7ec4b0/blue_bg.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
  }

  .t-dialog__header {
    padding: 4px 12px 16px !important;
  }

  .t-dialog__body {
    padding: 0 !important;
  }

  .content {
    height: 492px;
    display: flex;
    padding: 12px;
    align-items: flex-start;
    gap: 8px;
    flex: 1;
    border-radius: 12px;
    background: var(--bg-kyy_color_bg_light, #FFF);
  }

  .detail-card {
    display: flex;
    width: 320px;
    padding: 12px;
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    align-self: stretch;
    border-radius: 12px;
    border: 1px solid #BEE2FF;
    background: linear-gradient(180deg, rgba(227, 249, 254, 0.50) 0%, rgba(245, 250, 253, 0.50) 100%), #FFF;
    box-shadow: 0px 4px 8px 0px rgba(0, 7, 66, 0.16);
    .detail-image:hover {
      transform: none !important;
    }
  }

  .item-card {
    flex: 1;
  }
}
</style>
