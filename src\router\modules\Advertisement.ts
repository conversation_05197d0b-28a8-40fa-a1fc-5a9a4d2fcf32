import Layout from '@/layouts/index.vue';

export default [
	{
		path: '/advertisement',
		name: 'advertisement',
		component: Layout,
		redirect: '/advertisement/advertisementManage/index',
		meta: { title: '另可广告', icon: 'chart-graph', titleHK: '另可广告' },
		children: [
			{
				path: 'advertisement-advertisementManage',
				name: 'advertisement-advertisementManage',
				component: () => import('@/pages/advertisement/advertisementManage/index.vue'),
				meta: { title: '广告管理', titleHK: '广告管理' },
			},
			{
				path: 'advertisement-advertisementManage-detail',
				name: 'advertisement-advertisementManage-detail',
				component: () => import('@/pages/advertisement/advertisementManage/detail.vue'),
				meta: { title: '广告管理详情', titleHK: '广告管理详情', hidden: true },
			},
			{
				path: 'advertisement-platform',
				name: 'advertisement-platform',
				meta: { title: '数字平台广告', titleHK: '数字平台广告' },
				component: () => import('@/pages/advertisement/platform/index.vue'),
			},
			{
				path: 'advertisement-platform-whitelist',
				name: 'advertisement-platform-whitelist',
				component: () => import('@/pages/advertisement/platform/whitelist.vue'),
				meta: { title: '白名单', titleHK: '白名单', hidden: true },
			},
			{
				path: 'advertisement-bigMarket',
				name: 'advertisement-bigMarket',
				meta: { title: '大市场广告', titleHK: '大市场广告' },
				component: () => import('@/pages/advertisement/bigMarket/index.vue'),
			},
			{
				path: 'advertisement-bigMarket-detail',
				name: 'advertisement-bigMarket-detail',
				component: () => import('@/pages/advertisement/bigMarket/detail.vue'),
				meta: { title: '默认素材', titleHK: '默认素材', hidden: true },
			},
		],
	},
	{
		path: '/externalApplication',
		name: 'externalApplication',
		component: Layout,
		redirect: '/externalApplication/index',
		meta: { title: '外部应用', icon: 'chart-graph', titleHK: '外部应用' },
		children: [
			{
				path: 'externalApplication-list',
				name: 'externalApplication-list',
				component: () => import('@/pages/externalApplication/index.vue'),
				meta: { title: '应用管理', titleHK: '应用管理' },
			},
			{
				path: 'externalApplication-detail',
				name: 'externalApplication-detail',
				component: () => import('@/pages/externalApplication/detail.vue'),
				meta: { title: '应用管理详情', titleHK: '应用管理详情', hidden: true },
			},
			{
				path: 'margin-list',
				name: 'margin-list',
				component: () => import('@/pages/externalApplication/margin/index.vue'),
				meta: { title: '保证金', titleHK: '保证金' },
			},
		],
	},
];
