<template>
  <t-drawer
    v-model:visible="visible"
    class-name="drawerSet"
    header="详情"
    :z-index="1500"
    :on-confirm="onClickConfirm"
    :close-btn="true"
    :size="'592px'"
  >
    <template #closeBtn>
      <!-- <svg class="iconpark-icon" style="width: 16px; height: 16px">
        <use href="#close" />
      </svg> -->
      <iconpark-icon name="iconerror" style="font-size: 24px"></iconpark-icon>
    </template>
    <template #footer>
      <div v-if="data" class="operates">
        <t-button
          theme="default"
          variant="outline"
          class="operates-item"
          @click="onClose"
        >取消</t-button>
        <t-button
          theme="default"
          variant="outline"
          class="operates-item"
          @click="onDel"
        >{{ $t("member.delete") }}</t-button>
        <t-button theme="primary" class="operates-item" @click="onEdit">{{
          $t("member.edit")
        }}</t-button>
        <!-- <t-button
          v-show="data.status === 1"
          theme="danger"
          variant="outline"
          class="operates-item"
          @click="onReject"
        >驳回申请</t-button>
        -->
      </div>
    </template>
    <div v-if="data && visible" class="drawerSet-body">
      <div class="system">
        <div class="detail-control" style="width: 100%">
          <div class="lable">
            <span class="line" />{{ $t("member.svip.member_info") }}
          </div>
        </div>
        <div class="detail-control" :style="{ width: '50%' }">
          <div class="subLable">{{ $t("member.svip.fee_unit") }}</div>
          <div class="value">
            {{ $filters.isPeriodEmpty(data.name) }}
          </div>
        </div>
        <div class="detail-control" :style="{ width: '50%' }">
          <div class="subLable">{{ $t("member.svip.member_level") }}</div>
          <div class="value">
            {{ $filters.isPeriodEmpty(data.level_name) }}
          </div>
        </div>
        <div class="detail-control" :style="{ width: '50%' }">
          <div class="subLable">{{ $t("member.svip.in_date") }}</div>
          <div class="value">
            {{ $filters.isPeriodEmpty(data.join_time) }}
          </div>
        </div>
        <div class="detail-control" :style="{ width: '50%' }">
          <div class="subLable">{{ $t("member.svip.expire_date") }}</div>
          <div class="value">
            {{ $filters.isPeriodEmpty(data.expire_time) }}
          </div>
        </div>
      </div>
      <div class="system">
        <div class="detail-control" style="width: 100%">
          <div class="lable">
            <span class="line" />{{ $t("member.svip.fee_info") }}
          </div>
        </div>
        <div class="detail-control" :style="{ width: '100%' }">
          <div class="subLable">{{ $t("member.svip.fee_type") }}</div>
          <div class="value">
            {{ showTextType(data.type) }}
          </div>
        </div>
        <div class="detail-control" :style="{ width: '50%' }">
          <div class="subLable">{{ $t("member.svip.fee_date") }}</div>
          <div class="value">
            {{ $filters.isPeriodEmpty(data.pay_time) }}
          </div>
        </div>
        <div class="detail-control" :style="{ width: '50%' }">
          <div class="subLable">{{ $t("member.svip.fee_no") }}</div>
          <div class="value">
            {{ $filters.isPeriodEmpty(data.no) }}
          </div>
        </div>
        <div class="detail-control" :style="{ width: '50%' }">
          <div class="subLable">{{ $t("member.svip.fee_money") }}（元）</div>
          <div class="value">
            {{ priceDivisorShow(data.money, 100) }}
          </div>
        </div>
        <!-- <div class="detail-control" :style="{ width: '50%' }">
          <div class="subLable">{{ $t("member.svip.currency") }}</div>
          <div class="value">
            {{ $filters.isPeriodEmpty(data.currency) }}
          </div>
        </div> -->
        <DFileUpload
          :attrs="{ ...data.attachment, name: '缴费凭证', width: '50%' }"
        />
        <div class="detail-control" :style="{ width: '50%' }">
          <div class="subLable">{{ $t("member.svip.desc") }}</div>
          <div class="value">
            {{ $filters.isPeriodEmpty(data.remark) }}
          </div>
        </div>
      </div>
      <div class="system">
        <div class="detail-control" style="width: 100%">
          <div class="lable">
            <span class="line" />{{ $t("member.svip.system_info") }}
          </div>
        </div>
        <div class="detail-control" :style="{ width: '50%' }">
          <div class="subLable">{{ $t("member.svip.create_time") }}</div>
          <div class="value">
            {{ $filters.isPeriodEmpty(data.created_at) }}
          </div>
        </div>
        <div class="detail-control" :style="{ width: '50%' }">
          <div class="subLable">{{ $t("member.svip.creator") }}</div>
          <div class="value">
            {{ $filters.isPeriodEmpty(data.creator_name) }}
          </div>
        </div>
        <div class="detail-control" :style="{ width: '50%' }">
          <div class="subLable">{{ $t("member.svip.update_time") }}</div>
          <div class="value">
            {{ $filters.isPeriodEmpty(data.updated_at) }}
          </div>
        </div>
        <div class="detail-control" :style="{ width: '50%' }">
          <div class="subLable">{{ $t("member.svip.updator") }}</div>
          <div class="value">
            {{ $filters.isPeriodEmpty(data.operator_name) }}
          </div>
        </div>
      </div>
    </div>
  </t-drawer>
  <RejectModal ref="rejectModalRef" @on-send="onSaveReject" />
  <SuccessModal ref="successModalRef" @on-send="onSaveSuccess" />
</template>

<script setup lang="ts">
import { ref, Ref, reactive, watch, toRaw } from "vue";
import { DialogPlugin, MessagePlugin } from "tdesign-vue-next";
import {
  postMemberApplyRejectAxios,
  postMemberApplyAgreeAxios,
  delMemberOrderAxios,
} from "@renderer/api/uni/api/businessApi";
import RejectModal from "@renderer/views/uni/member_home/panel/apply-member-panel/modal/reject-modal.vue";
import SuccessModal from "@renderer/views/uni/member_home/panel/apply-member-panel/modal/success-modal.vue";
import DFileUpload from "@renderer/components/free-from/detail/controls/fileUpload.vue";
import lodash from "lodash";
import { getResponseResult, priceDivisorShow } from "@/utils/myUtils";
// src\renderer\components\free-from\detail\controls\fileUpload.vue
// import { ClientSide } from "@renderer/types/enumer";

// 运行时
const controls = ref([]);

const visible = ref(false);

const data = ref(null);
const emits = defineEmits(["reload", "onEdit"]);

watch(
  () => visible.value,
  (cur) => {}
);
const optionsFeeType = [
  // 会费类型
  { label: "会员入会", value: 1 },
  { label: "会员续期", value: 2 },
  { label: "其他", value: 3 },
];
// 会费类型
const showTextType = (val) => {
  const option = optionsFeeType.find((v: any) => v.value === val);
  if (option) {
    return option.label;
  }
  return "--";
};

// 驳回
// const rejectModalRef = ref(null);
// const onReject = () => {
//   rejectModalRef.value.onOpen({ id: data.value.id });
// };
const onDelMemberOrderAxios = (val) => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await delMemberOrderAxios(val.id);
      result = getResponseResult(result);
      if (!result) return;
      MessagePlugin.success("删除成功");
      resolve(result);
      // rejectModalRef.value.onClose();
    } catch (error) {
      const errMsg = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
      reject();
    }
  });
};

// 通过

// const onGetMemberSetting = async () => {
//   let result = null;
//   // eslint-disable-next-line no-async-promise-executor
//   return new Promise(async (resolve, reject) => {
//     try {
//       result = await getMemberSettingAxios();
//       result = getResponseResult(result);
//       if (!result) {
//         reject();
//         return;
//       }
//       resolve(result.data);
//     } catch (error) {
//       const errMsg = error instanceof Error ? error.message : error;
//       MessagePlugin.error(errMsg);
//       reject();
//     }
//   });
// };

// const successModalRef = ref(null);

const onDel = () => {
  // 删除

  const confirmDia = DialogPlugin({
    header: "提示",
    theme: "info",
    body: "您确定删除该记录吗？",
    closeBtn: null,
    confirmBtn: "确定",
    className: "delmode",
    onConfirm: async () => {
      onDelMemberOrderAxios(data.value).then(() => {
        // 删除字段操作
        confirmDia.hide();
        onClose();
        emits("reload");
      });
    },
    onClose: () => {
      confirmDia.hide();
    },
  });
};

const onEdit = () => {
  onClose();
  emits("onEdit", lodash.cloneDeep(toRaw(data.value)));
};
const onSaveSuccess = async (val: any) => {
  let result = null;
  try {
    result = await postMemberApplyAgreeAxios(val);
    result = getResponseResult(result);
    if (!result) return;
    MessagePlugin.success("审核成功");
    successModalRef.value.onClose();
    onClose();
    emits("reload");
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    MessagePlugin.error(errMsg);
  }
};

const onClickConfirm = async () => {};

const onOpen = (item?: any) => {
  data.value = item;
  visible.value = true;
  //   visible.value = true;
};
const onClose = () => {
  visible.value = false;
};

defineExpose({
  onOpen,
  onClose,
});
</script>
<style lang="less" scoped>
@import url("@renderer/views/uni/member_home/panel/public.less");
.sports {
  &-title {
    font-size: 14px;

    font-weight: 400;

    color: #717376;
  }
}
.toTitle {
  font-size: 14px;

  font-weight: 400;

  // color: #13161b;
}
.toContent {
  font-size: 14px;

  font-weight: 400;
}
.operates {
  display: flex;
  justify-content: flex-end;
  gap: 0px;
}
.drawerSet {
  // width: 720px;
  &-body {
  }
  //   .t-drawer__content-wrapper {
  //     width: 720px !important;
  //   }
}

:deep(.t-drawer__header) {
  border-bottom: 0;
  color: red;
}
:deep(.t-drawer__body) {
  padding-top: 10px !important;
}
</style>
