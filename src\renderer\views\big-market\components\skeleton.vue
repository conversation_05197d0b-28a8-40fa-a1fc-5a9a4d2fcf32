<script setup lang='ts'>
</script>

<template>
  <div class="sk-card">
    <div class="sk-card-item" v-for="item in 15" :key="item">
      <div class="sk-card-avatar">
        <div class="cv gradient"></div>
        <div class="btn gradient"></div>
      </div>
      <div class="sk-card-black">
        <div class="black1 gradient"></div>
        <div class="black1 gradient"></div>
        <div class="black2 gradient"></div>
        <div class="blackbox">
          <div class="blackbox3 gradient"></div>
          <div class="blackbox4 gradient"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang='less' scoped>
@keyframes t-skeleton--gradient {
    0% {
        transform: translate(-100%) skew(-15deg);
    }
    100% {
        transform: translate(100%) skew(-15deg);
    }
}
.gradient{
 position: relative;
        overflow: hidden;
}
      .gradient:after{
            content: " ";
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    top: 0;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0), var(--skeleton-animation-gradient), rgba(255, 255, 255, 0));
    animation: t-skeleton--gradient 1.5s linear 2s infinite;
      }
.sk-card {
  display: flex;
  align-items: flex-start;
  align-content: flex-start;
  gap: 12px;
  align-self: stretch;
  flex-wrap: wrap;

  .sk-card-item {
    display: flex;
    width: 279px;
    padding: 8px;
    justify-content: center;
    align-items: flex-start;
    gap: 16px;
    border-radius: 8px;
    border: 1px solid var(--divider-kyy_color_divider_light, #ECEFF5);

    .sk-card-avatar {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      gap: 8px;
      align-self: stretch;

      .cv {
        display: flex;
        width: 44px;
        height: 44px;
        justify-content: center;
        align-items: center;
        border-radius: var(--kyy_avatar_radius_full, 999px);
        background: var(--bg-kyy_color_bg_deepest, #ECEFF5);
      }

      .btn {
        display: flex;
        width: 54px;
        height: 24px;
        padding: 2px 8px 2px 4px;
        align-items: center;
        gap: 2px;
        border-radius: 99px;
        background: var(--bg-kyy_color_bg_deepest, #ECEFF5);
   
      }

    }

    .sk-card-black {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: flex-start;
      gap: 4px;
      flex: 1 0 0;

      .black1 {
        width: 100%;
        display: flex;
        height: 20px;
        align-items: center;
        gap: 2px;
        align-self: stretch;
        border-radius: 4px;
        background: var(--bg-kyy_color_bg_deepest, #ECEFF5);
      }

      .black2 {
        display: flex;
        width: 88px;
        height: 20px;
        justify-content: center;
        align-items: center;
        gap: 4px;
        border-radius: 4px;
        background: var(--bg-kyy_color_bg_deepest, #ECEFF5);
      }

      .blackbox {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        .blackbox3 {
          display: flex;
          width: 79px;
          height: 20px;
          justify-content: center;
          align-items: center;
          gap: 4px;
          border-radius: 4px;
          background: var(--bg-kyy_color_bg_deepest, #ECEFF5);

        }

        .blackbox4 {
          display: flex;
          width: 56px;
          height: 20px;
          justify-content: center;
          align-items: center;
          gap: 4px;
          border-radius: 4px;
          background: var(--bg-kyy_color_bg_deepest, #ECEFF5);
        }
      }
    }
  }
}
</style>
