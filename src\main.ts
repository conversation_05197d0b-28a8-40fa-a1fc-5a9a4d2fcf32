import { createApp } from 'vue';

import TDesign from 'tdesign-vue-next';
// import createPinia from 'pinia';
import 'tdesign-vue-next/es/style/index.css';
// import VueAMap, { initAMapApiLoader } from '@vuemap/vue-amap';
// import LkEditor from '@lk/editor';
import LkEditor from '@rk/editor';
import MyDirectives from './utils/myDirectives';
// import 'default-passive-events';

import { store } from './store';
import router from './router';
import 'virtual:uno.css';

import '@/style/index.less';
import './permission';
import App from './App.vue';

import filters from './utils/filters';
import vueI18n from './i18n/index';
import 'virtual:svg-icons-register';
// import '@lk/editor/index.css';
import '@rk/editor/index.css';

import 'cropperjs/dist/cropper.css';
import 'tippy.js/dist/tippy.css';
import '@rk/unitPark/dist/assets/style.css';
// 引入vue-amap
// import '@vuemap/vue-amap/dist/style.css';

// 设置带过期时间的storage
Storage.prototype.setCanExpireLocal = (key, value, expire) => {
	if (Number.isNaN(expire) || expire < 1) {
		throw new Error('有效期应为一个有效数值');
	}
	const time = expire * 86_400_000;
	const obj = {
		data: value, // 存储值
		time: Date.now(), // 存值时间戳
		expire: time, // 过期时间
	};
	localStorage.setItem(key, JSON.stringify(obj));
};
Storage.prototype.getCanExpireLocal = (key) => {
	let val = localStorage.getItem(key);
	if (!val) return val;
	val = JSON.parse(val);
	if (Date.now() > val.time + val.expire) {
		localStorage.removeItem(key);
		return '值已失效';
	}
	return val.data;
};
// window.localStorage.setCanExpireLocal('tokenss', 11111, 1);
console.log(window.localStorage.getCanExpireLocal('tokenss'), 'tokensstokensstokenss');

const app = createApp(App);

// 初始化vue-amap
// initAMapApiLoader({
// 	// 高德的key
// 	key: '667e6d41ce220bb7239b8623058f723c',
// 	securityJsCode: 'aa5fc425c15dcce14f033523f55ca0c9', // 新版key需要配合安全密钥使用
// 	// Loca:{
// 	//  version: '2.0.0'
// 	// } // 如果需要使用loca组件库，需要加载Loca
// });

// app.use(VueAMap);
// app.use(pinia);
app.use(MyDirectives);
app.use(TDesign);
app.use(store);
app.use(LkEditor);
// app.use(RkEditor);
app.use(router);
app.use(vueI18n);
filters(app);
app.mount('#app');
