import lodash from 'lodash';
import LynkerSDK from "@renderer/_jssdk";
/**
 * 节点类型，仅支持 'iframe' 或 'webview'
 */
type NodeType = 'iframe' | 'webview';
/**
 * 节点池，key 为唯一 id，value 为 HTMLIFrameElement 或 Electron.WebviewTag
 */
type NodeMap = Map<string, HTMLElement>;

const nodeMap: NodeMap = new Map();

/**
 * 获取或创建指定 id 的 iframe/webview 节点
 * @param id 节点唯一标识
 * @param type 节点类型 'iframe' | 'webview'
 * @param target 挂载目标容器，null 时挂载到 body
 * @param attrs 节点属性（如 src、allow 等）
 * @param openDevTools 是否自动打开开发者工具（仅 webview 有效）
 * @returns 节点实例
 *
 * 说明：
 * - 若节点已存在则直接返回，否则新建并挂载
 * - webview 节点自动配置 preload 路径与 webpreferences
 * - 节点样式初始为隐藏，需调用 showNode 显示
 */
export function getOrCreateNode(
  id: string,
  type: NodeType,
  target: HTMLElement | null,
  attrs: Record<string, string> = {},
  openDevTools: boolean = false,
  webviewExtraData: string = '',
  isOnlySetWebviewExtraData: boolean = false
) {
  let wrapper = nodeMap.get(id) as HTMLElement | undefined;

  if (!wrapper && isOnlySetWebviewExtraData) {
    return;
  }

  // 检查 wrapper 是否还在 DOM 中
  if (wrapper && !document.body.contains(wrapper)) {
    wrapper = undefined;
    nodeMap.delete(id);
  }

  if (!wrapper) {
    // 创建 wrapper
    wrapper = document.createElement('div');
    wrapper.className = 'lynker-iframe-wrapper';
    wrapper.id = `${id}`;
    wrapper.setAttribute('data-id', id);
    wrapper.setAttribute('data-type', type);
    wrapper.setAttribute('data-src', attrs?.src || '');
    wrapper.setAttribute('data-extraData', JSON.stringify(webviewExtraData));
    wrapper.setAttribute('data-attach-to', target?.id || 'body');
    wrapper.setAttribute('data-is-show', 'false');
    //
    // 业务数据都挂 wrapper 上
    // 样式
    Object.assign(wrapper.style, {
      zIndex: '9',
      pointerEvents: 'auto',
      display: 'none',
      background: 'transparent',
      border: 'none',
      position: target ? 'absolute' : 'fixed',
      left: '0px',
      top: '0px',
      width: '0px',
      height: '0px',
      userSelect: 'none',
    });

    // 注入 keyframes 动画（只需注入一次）
    if (!document.getElementById('lynker-iframe-loading-keyframes')) {
      const style = document.createElement('style');
      style.id = 'lynker-iframe-loading-keyframes';
      style.innerHTML = `
      @keyframes l3 { to { transform: rotate(1turn) } }
      `;
      document.head.appendChild(style);
    }

    // 创建 loading-container
    const loadingContainer = document.createElement('div');
    loadingContainer.className = 'loading-container';
    Object.assign(loadingContainer.style, {
      position: 'absolute',
      height: '100%',
      width: '100%',
      display: 'flex',
      flexDirection: 'column',
      gap: '8px',
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: 'rgba(255,255,255,0.7)',
      borderRadius: '8px',
      left: '0',
      top: '0',
      right: '0',
      bottom: '0',
      zIndex: '1',
      pointerEvents: 'none',
    });
    // loader 圆圈
    const loader = document.createElement('div');
    loader.className = 'loader';
    Object.assign(loader.style, {
      width: '28px',
      padding: '8px',
      aspectRatio: '1',
      borderRadius: '50%',
      background: '#4C5EFF',
      WebkitMask: 'conic-gradient(#0000 10%, #000), linear-gradient(#000 0 0) content-box',
      mask: 'conic-gradient(#0000 10%, #000), linear-gradient(#000 0 0) content-box',
      WebkitMaskComposite: 'source-out',
      maskComposite: 'subtract',
      animation: 'l3 1s infinite linear',
    });
    // loader-text
    const loaderText = document.createElement('div');
    loaderText.className = 'loader-text';
    loaderText.innerText = '加载中...';
    Object.assign(loaderText.style, {
      fontFamily: 'PingFang SC',
      fontSize: '14px',
      fontWeight: '400',
      lineHeight: '22px',
      textAlign: 'left',
      color: '#333',
    });
    loadingContainer.appendChild(loader);
    loadingContainer.appendChild(loaderText);

    // 创建 errorDiv
    const errorDiv = document.createElement('div');
    errorDiv.className = 'lynker-iframe-error';
    Object.assign(errorDiv.style, {
      position: 'absolute',
      left: '0',
      top: '0',
      right: '0',
      bottom: '0',
      display: 'none',
      alignItems: 'center',
      justifyContent: 'center',
      flexDirection: 'column',
      zIndex: '4',
      background: 'rgba(255,255,255,0.7)',
      color: 'red',
      fontSize: '14px',
      borderRadius: '8px',
    });
    // 错误提示文字
    const errorText = document.createElement('div');
    errorText.innerText = '加载失败';
    Object.assign(errorText.style, {
      marginBottom: '12px',
      fontSize: '15px',
      color: '#d32f2f',
      fontWeight: 'bold',
    });
    // 刷新重试按钮
    const retryBtn = document.createElement('button');
    retryBtn.innerText = '刷新重试';
    Object.assign(retryBtn.style, {
      padding: '6px 18px',
      borderRadius: '4px',
      border: 'none',
      background: '#4C5EFF',
      color: '#fff',
      fontSize: '14px',
      cursor: 'pointer',
      fontWeight: 'bold',
      boxShadow: '0 2px 8px rgba(76,94,255,0.08)',
      outline: 'none',
      transition: 'background 0.2s',
    });
    retryBtn.onmouseenter = () => { retryBtn.style.background = '#3a47c6'; };
    retryBtn.onmouseleave = () => { retryBtn.style.background = '#4C5EFF'; };
    // 重试逻辑
    retryBtn.onclick = () => {
      errorDiv.style.display = 'none';
      loadingContainer.style.display = 'flex';
      // 重新加载 iframe/webview
      if (type === 'webview') {
        const webview = wrapper.querySelector('webview') as Electron.WebviewTag | null;
        if (webview) webview.reload();
      } else {
        const iframe = wrapper.querySelector('iframe') as HTMLIFrameElement | null;
        if (iframe) {
          // 重新设置 src 触发 reload
          const src = wrapper.getAttribute('data-src') || iframe.getAttribute('src') || '';
          iframe.setAttribute('src', 'about:blank');
          setTimeout(() => {
            iframe.setAttribute('src', src);
          }, 100);
        }
      }
    };
    errorDiv.appendChild(errorText);
    errorDiv.appendChild(retryBtn);

    // 创建 iframe/webview
    const node = document.createElement(type) as HTMLIFrameElement | Electron.WebviewTag;
    node.setAttribute('id', `webview-${id}`);
    node.setAttribute('allowpopups', '');
    Object.entries(attrs).forEach(([k, v]) => node.setAttribute(k, v));
    Object.assign(node.style, {
      width: '100%',
      height: '100%',
      border: 'none',
      background: 'transparent',
      display: 'flex',
      position: 'relative',
      zIndex: '2',
      flex: '1',
    });
    if (type === 'webview') {
      (node as Electron.WebviewTag).preload = `file://${LynkerSDK.config.preloadPath}`;
      (node as Electron.WebviewTag).webpreferences = 'nodeIntegration=true,contextIsolation=false,webSecurity=false,nativeWindowOpen=true,plugins=true,webSecurity=false';
      node.addEventListener('ipc-message', (e) => {
        // @ts-ignore
        if (e.channel === 'webview-custom-event-mouseup') {
          LynkerSDK.ipcRenderer.invoke('set-popbv', { show: false });
          LynkerSDK.ipcRenderer.invoke('set-popbv-other', { show: false });
          const event = new MouseEvent('mousedown', {
            bubbles: true,
            cancelable: true,
            clientX: 10,
            clientY: 10,
            button: 0
          });
          document.dispatchEvent(event);
        }
      });
      node.addEventListener('dom-ready', () => {
        if (openDevTools) {
          (node as Electron.WebviewTag).openDevTools();
        }
        try {
          const webviewId = (node as Electron.WebviewTag).getWebContentsId();
          const webContents = LynkerSDK.getWebContentsId(webviewId);
          node.setAttribute('data-webContentsId', `${webviewId}`);
          // @ts-ignore
          const fatherWindowName = window?.__ELECTRON_WINDOW_MANAGER_NAME__;
          const initScript = `
            try {
              const data = {
                __Lynker_webview_id__: ${JSON.stringify(id)},
                __Lynker_webview_father_window_name__: ${JSON.stringify(fatherWindowName)},
                __ELECTRON_WINDOW_MANAGER_WEB_CONTENTS_ID__: ${JSON.stringify(webContents.id)},
                __ELECTRON_WINDOW_MANAGER_TYPE__: ${JSON.stringify('WEBVIEW')},
                __ELECTRON_WINDOW_MANAGER_NAME__: ${JSON.stringify(id)},
                __ELECTRON_WINDOW_MANAGER_PRELOAD__: ${JSON.stringify(LynkerSDK.config.preloadPath)},
              };
              Object.entries(data).forEach(([key, value]) => {
                Object.defineProperty(window, key, {
                  value: value,
                  writable: false,
                  configurable: false,
                  enumerable: true,
                });
              });
            } catch (error) {
              console.error('error', error);
            }
            try {
              window.__ELECTRON_WINDOW_MANAGER_EXTRA_DATA__ = ${wrapper.getAttribute('data-extraData')};
            } catch (error) {
              console.error('error', error);
            }
            try {
              document.addEventListener('mouseup', function() {
                __ELECTRON_IPC__.sendToHost('webview-custom-event-mouseup');
              });
            } catch (error) {
              console.error('error', error);
            }
          `;
          webContents.executeJavaScript(initScript);
          webContents.removeAllListeners('did-start-loading');
          webContents.removeAllListeners('did-fail-load');
          webContents.removeAllListeners('did-finish-load');
          loadingContainer.style.display = 'none';
          const lodashDebounce = lodash.debounce(() => {
            loadingContainer.style.display = 'flex';
            errorDiv.style.display = 'none';
          }, 500);
          const timeoutHideLoading = lodash.debounce(() => {
            loadingContainer.style.display = 'none';
          }, 1000 * 3);
          webContents.addListener('did-start-loading', () => {
            console.log('[iframePool] did-start-loading', { loadingDisplay: loadingContainer.style.display, errorDisplay: errorDiv.style.display });
            lodashDebounce();
            webContents.executeJavaScript(initScript);
          });
          webContents.addListener('did-stop-loading', () => {
            console.log('[iframePool] did-stop-loading', { loadingDisplay: loadingContainer.style.display, errorDisplay: errorDiv.style.display });
            lodashDebounce.cancel();
            timeoutHideLoading();
            loadingContainer.style.display = 'none';
            console.log('[iframePool] did-stop-loading after hide', { loadingDisplay: loadingContainer.style.display, errorDisplay: errorDiv.style.display });
          });
          webContents.addListener('did-fail-load', () => {
            console.log('[iframePool] did-fail-load', { loadingDisplay: loadingContainer.style.display, errorDisplay: errorDiv.style.display });
            lodashDebounce.cancel();
            timeoutHideLoading.cancel();
            loadingContainer.style.display = 'none';
            errorDiv.style.display = 'flex';
            console.log('[iframePool] did-fail-load after error', { loadingDisplay: loadingContainer.style.display, errorDisplay: errorDiv.style.display });
          });
          webContents.addListener('did-finish-load', () => {
            console.log('[iframePool] did-finish-load', { loadingDisplay: loadingContainer.style.display, errorDisplay: errorDiv.style.display });
            lodashDebounce.cancel();
            timeoutHideLoading.cancel();
            loadingContainer.style.display = 'none';
            webContents.executeJavaScript(initScript);
            console.log('[iframePool] did-finish-load after hide', { loadingDisplay: loadingContainer.style.display, errorDisplay: errorDiv.style.display });
          });
        } catch (error) {
          console.error('init-script error: ', error);
        }
        try {
          const webviewId = (node as Electron.WebviewTag).getWebContentsId();
          const webContents = LynkerSDK.getWebContentsId(webviewId);
          webContents.setWindowOpenHandler(({ url }) => {
            webContents.executeJavaScript(`window.__ELECTRON_WINDOW_MANAGER_EXTRA_DATA__`).then((res) => {
              try {
                if (res) {
                  const extraData = JSON.parse(res);
                  if (extraData.windowsTabsOptions && extraData.windowsTabsOptions.tabOptions) {
                    LynkerSDK.windowsTabs.openTab({
                      tabsId: extraData.windowsTabsOptions.tabsId,
                      tabsTitle: extraData.windowsTabsOptionstabsTitle,
                      options: {
                        title: '',
                        url: url,
                        icon: extraData.windowsTabsOptions.tabOptions.icon,
                        activeIcon: extraData.windowsTabsOptions.tabOptions.activeIcon,
                      }
                    });
                    return;
                  }
                }
                throw new Error('extraData is not valid');
              } catch (error) {
                LynkerSDK.windowsTabs.openTab({
                  tabsTitle: '',
                  isOnlyOneTabHideTabs: true,
                  options: {
                    title: '',
                    url: url,
                  }
                });
              }
            });
            return { action: 'deny' };
          });
        } catch (error) {
          console.error('did-create-window', error);
        }
      });
    } else {
      // 监听加载事件
      node.addEventListener('load', () => {
        loadingContainer.style.display = 'none';
        errorDiv.style.display = 'none';
      });
      node.addEventListener('error', () => {
        loadingContainer.style.display = 'none';
        errorDiv.style.display = 'flex';
      });
    }

    // 初始显示 loading
    loadingContainer.style.display = 'flex';

    // 组装
    wrapper.appendChild(node);
    wrapper.appendChild(loadingContainer);
    wrapper.appendChild(errorDiv);
    // 挂载
    if (target) {
      target.appendChild(wrapper);
    } else {
      document.body.appendChild(wrapper);
    }
    nodeMap.set(id, wrapper);
  } else {
    // 已有 wrapper，重新挂载
    const attachToId = wrapper.getAttribute('data-attach-to');
    const attachTarget = attachToId && attachToId !== 'body'
      ? document.getElementById(attachToId)
      : document.body;
    if (wrapper.parentNode !== attachTarget) {
      attachTarget.appendChild(wrapper);
    }
    if (type === 'webview') {
      const node = wrapper.querySelector('webview') as Electron.WebviewTag | null;
      if (node) {
        const extraData = wrapper.getAttribute('data-extraData');
        if (extraData && extraData !== JSON.stringify(webviewExtraData)) {
          wrapper.setAttribute('data-extraData', JSON.stringify(webviewExtraData));
          const webContentsId = node.getAttribute('data-webContentsId');
          const webContents = LynkerSDK.getWebContentsId(Number(webContentsId));
          webContents.executeJavaScript(`window.__ELECTRON_WINDOW_MANAGER_EXTRA_DATA__ = ${JSON.stringify(webviewExtraData)}`);
        }
      }
    }
  }
  return wrapper;
}

/**
 * 显示指定 id 的节点，并设置其位置、尺寸与额外样式
 * @param id 节点唯一标识
 * @param rect 目标区域（DOMRect）
 * @param extraStyle 额外样式（可选）
 * @returns 节点实例
 *
 * 说明：
 * - 会自动更新 data-options，便于后续恢复
 * - 样式变更后异步显示节点
 */
export function showNode(id: string, rect: DOMRect, extraStyle: Partial<CSSStyleDeclaration> = {}) {
  const wrapper = nodeMap.get(id) as HTMLElement | undefined;
  if (wrapper) {
    const attachToId = wrapper.getAttribute('data-attach-to');
    // 检查是否在 DOM
    if (!document.body.contains(wrapper)) {
      const attachTarget = attachToId && attachToId !== 'body'
        ? document.getElementById(attachToId)
        : document.body;
      attachTarget?.appendChild(wrapper);
    }
    const isBody = attachToId === 'body';
    wrapper.setAttribute('data-is-show', 'true');
    Object.assign(wrapper.style, {
      pointerEvents: 'auto',
      left: isBody ? rect.left + 'px' : '0px',
      top: isBody ? rect.top + 'px' : '0px',
      width: rect.width + 'px',
      height: rect.height + 'px',
      position: isBody ? 'fixed' : 'absolute',
      userSelect: wrapper.style.userSelect || 'none',
      display: 'flex',
      ...extraStyle,
    });
    wrapper.setAttribute('data-options', JSON.stringify({
      width: rect.width,
      height: rect.height,
      left: rect.left,
      top: rect.top,
      extraStyle,
    }));
    setTimeout(() => {
      handleFocus();
    }, 0);
  }
  return wrapper;
}

/**
 * 隐藏指定 id 的节点，并禁用交互
 * @param id 节点唯一标识
 * @returns 节点实例
 *
 * 说明：
 * - 仅隐藏 display，不销毁节点
 * - 可通过 showNode 恢复显示
 */
export function hideNode(id: string) {
  const wrapper = nodeMap.get(id) as HTMLElement | undefined;
  if (wrapper) {
    if (!document.body.contains(wrapper)) {
      const attachToId = wrapper.getAttribute('data-attach-to');
      const attachTarget = attachToId && attachToId !== 'body'
        ? document.getElementById(attachToId)
        : document.body;
      attachTarget?.appendChild(wrapper);
    }
    wrapper.setAttribute('data-is-show', 'false');
    wrapper.style.display = 'none';
    wrapper.style.pointerEvents = 'none';
  }
  return wrapper;
}

/**
 * 销毁指定 id 的节点，彻底移除并释放资源
 * @param id 节点唯一标识
 *
 * 说明：
 * - 节点会从 DOM 和 nodeMap 中移除
 * - 销毁后无法恢复，需重新创建
 */
export function destroyNode(id: string) {
  const wrapper = nodeMap.get(id) as HTMLElement | undefined;
  if (wrapper) {
    nodeMap.delete(id);
    setNodeSrc(id, 'about:blank');
    wrapper.remove();
    //
  }
}

/**
 * 清空所有节点
 */
export function clearAllNode() {
  nodeMap.forEach((node, id) => {
    if (node) {
      nodeMap.delete(id);
      setNodeSrc(id, 'about:blank');
      node.remove();
    }
  });
}

window.__clearAllNode__ = clearAllNode;

/**
 * 获取当前所有显示中的节点列表
 * @returns 显示中的节点数组
 *
 * 说明：
 * - 通过 data-is-show 属性判断
 */
export function getShowNode() {
  return Array.from(nodeMap.values())
    .filter((wrapper) => (wrapper as HTMLElement).getAttribute('data-is-show') === 'true');
}

/**
 * 刷新节点内容
 * @param node 目标节点（iframe 或 webview）
 *
 * 说明：
 * - iframe 通过 reload 刷新，并恢复样式
 * - webview 直接调用 reload
 * - 样式恢复有延迟，避免刷新时样式丢失
 */
export function refreshNode(wrapper: HTMLElement) {
  const iframe = wrapper.querySelector('iframe') as HTMLIFrameElement | null;
  const webview = wrapper.querySelector('webview') as Electron.WebviewTag | null;
  if (iframe) {
    const options = JSON.parse(wrapper.getAttribute('data-options') || '{}');
    const src = wrapper.getAttribute('data-src') || iframe.getAttribute('src') || '';
    iframe.setAttribute('src', 'about:blank');
    setTimeout(() => {
      iframe.setAttribute('src', src);
      Object.assign(iframe.style, { width: '100%', height: '100%', border: 'none' });
    }, 100);
  }
  if (webview) {
    webview.reload();
  }
}

/**
 * 设置节点 src
 * @param id 节点唯一标识
 * @param src 节点 src
 */
export function setNodeSrc(id: string, src: string) {
  try {
    const wrapper = nodeMap.get(id) as HTMLElement | undefined;
    if (!wrapper) return;
    const iframe = wrapper?.querySelector('iframe') as HTMLIFrameElement | null;
    const webview = wrapper?.querySelector('webview') as Electron.WebviewTag | null;
    if (wrapper) {
      const oldSrc = wrapper.getAttribute('data-src');
      if (src && oldSrc !== src) {
        wrapper.setAttribute('data-src', src);
        if (iframe) {
          iframe.setAttribute('src', src);
        }
        if (webview) {
          webview.setAttribute('src', src);
          webview.loadURL(src);
        }
      }
    }
  } catch (error) {
    console.error('setNodeSrc error', error);
  }
}

/**
 * 获取节点页面信息
 * @param id 节点唯一标识
 * @returns 节点页面信息
 */
export async function getNodePageInfo(id: string) {
  try {
    const wrapper = nodeMap.get(id) as HTMLElement | undefined;
    const iframe = wrapper?.querySelector('iframe') as HTMLIFrameElement | null;
    const webview = wrapper?.querySelector('webview') as Electron.WebviewTag | null;
    if (!iframe && !webview) return null;
    if (iframe instanceof HTMLIFrameElement) {
      if (!iframe.contentWindow) return null;
      const { document: iframeDoc } = iframe.contentWindow;
      const title = iframeDoc.title;
      let icon = '';
      const iconLink = iframeDoc.querySelector<HTMLLinkElement>('link[rel="icon"], link[rel="shortcut icon"]');
      if (iconLink) {
        icon = iconLink.href;
      }
      return { title, icon, url: iframe.getAttribute('src') };
    }
    if (webview) {
      const webContentsId = webview.getAttribute('data-webContentsId');
      const webContents = LynkerSDK.getWebContentsId(Number(webContentsId));
      const title = webContents?.getTitle?.() || '';
      const icon = await webContents?.executeJavaScript(`
        (() => {
          const iconLink = document.querySelector('link[rel="icon"], link[rel="shortcut icon"]');
          return iconLink ? (iconLink).href : '';
        })()
      `);
      return { title, icon: icon as string, url: webview.getURL() };
    }
    return null;
  } catch (error) {
    console.error('getNodePageInfo error', error);
    return null;
  }
}

const handleFocus = lodash.debounce(() => {
  handleBlur.cancel();
  nodeMap.forEach((node) => {
    if (node) {
      if (node.getAttribute('data-is-show') === 'true') {
        // console.error('focus', node.getAttribute('id'));
        const iframe = node.querySelector('iframe') as HTMLIFrameElement | null;
        const webview = node.querySelector('webview') as Electron.WebviewTag | null;
        if (iframe) {
          iframe.focus();
        }
        if (webview) {
          console.error('focus xxxxx', node.getAttribute('id'));
          const webContentsId = webview.getAttribute('data-webContentsId');
          const webContents = LynkerSDK.getWebContentsId(Number(webContentsId));
          if (webContents) {
            webContents.executeJavaScript(`
              try {
                const event = new Event('focus');
                window.dispatchEvent(event);
              } catch (error) {
                console.error('error', error);
              }
            `);
          }
        }
      } else {
        // console.error('blur', node.getAttribute('id'));
        const iframe = node.querySelector('iframe') as HTMLIFrameElement | null;
        const webview = node.querySelector('webview') as Electron.WebviewTag | null;
        if (iframe) {
          iframe.blur();
        }
        if (webview) {
          console.error('blur xxxxx', node.getAttribute('id'));
          const webContentsId = webview.getAttribute('data-webContentsId');
          const webContents = LynkerSDK.getWebContentsId(Number(webContentsId));
          if (webContents) {
            webContents.executeJavaScript(`
              try {
                const event = new Event('blur');
                window.dispatchEvent(event);
              } catch (error) {
                console.error('error', error);
              }
            `);
          }
        }
      }
    }
  });
}, 100);

const handleBlur = lodash.debounce(() => {
  nodeMap.forEach((node) => {
    if (node) {
      if (node.getAttribute('data-is-show') !== 'true') {
        // console.error('blur', node.getAttribute('id'));
        const iframe = node.querySelector('iframe') as HTMLIFrameElement | null;
        const webview = node.querySelector('webview') as Electron.WebviewTag | null;
        if (iframe) {
          iframe.blur();
        }
        if (webview) {
          console.error('blur xxxxx', webview.getAttribute('id'));
          const webContentsId = webview.getAttribute('data-webContentsId');
          const webContents = LynkerSDK.getWebContentsId(Number(webContentsId));
          if (webContents) {
            webContents.executeJavaScript(`
              try {
                const event = new Event('blur');
                window.dispatchEvent(event);
              } catch (error) {
                console.error('error', error);
              }
            `);
          }
        }
      }
    }
  });
}, 100);

window.addEventListener('focus', handleFocus);

// window.addEventListener('blur', handleBlur);
