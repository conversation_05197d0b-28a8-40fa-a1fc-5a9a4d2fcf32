<template>
  <div
    ref="pageContentRef"
    v-infinite-scroll="onLoading"
    class="page-content"
    :infinite-scroll-immediate-check="false"
    :infinite-scroll-distance="1800"
    :infinite-scroll-disabled="scrollDisabled"
    infinite-scroll-watch-disabled="scrollDisabled"
    @scroll="onScroll"
  >
    <div
      v-show="isShowTopbar"
      class="top-container"
      @click.stop
    >
      <div v-if="(!isSelf && !isOuter) || isSquareInfo" class="back-btn">
        <span class="back" @click="backIndex(route)">
          <iconpark-icon name="iconarrowlift" class="icon" />
          {{ $t("square.return") }}
        </span>
      </div>

      <div class="name" :class="{ followd: followed, 'pl-8!': isSelf && isSquareInfo }">
        <SquareAvatar
          class="avatar"
          size="24px"
          :square="squareData.square"
        />
        <Transition name="fade">
          <p v-if="followed || (isSelf && isSquareInfo)" class="name-title">{{ squareData.square?.name }}</p>
          <div v-else class="follow" @click="toggleFollow">
            <iconpark-icon size="20" name="iconadd" />
            <span class="follow-text">关注</span>
          </div>
        </Transition>
      </div>

      <ToolbarSetting
        :square-id="props.squareId"
        :square-data="squareData"
        :disable-list="disableList"
        @refresh="getSquareData(squareId); refreshId++"
      />
    </div>

    <template v-if="isShowHeader">
      <div v-if="!isSelf && !isOuter" class="top">
        <span class="back" @click="backIndex(route)">
          <iconpark-icon name="iconarrowlift" class="icon" />
          {{ $t("square.return") }}
        </span>
      </div>

      <HomeHeader
        v-if="squareData.square"
        :square-id="squareId"
        :stats="stats"
        :square-data="squareData"
        :profile="profile"
        :setting="setting"
        :disable-list="disableList"
        @navigate="navigate"
        @refresh="getSquareData(squareId); refreshId++"
      >
        <template #annual-fee>
          <UpgradeEntryPersonal
            v-if="profile?.teamId"
            v-show="showUpgradeEntry"
            ref="upgradeEntryRef"
            :square-id="profile.squareId"
            :team-id="profile.teamId"
            @success="loadFeeSuccess"
            @change="entryKey++"
          />
        </template>
      </HomeHeader>
    </template>

    <template v-if="squareData.square">
      <div v-if="!squareData.blacklisted && !squareData.beBlacklisted" class="body">
        <AppEntry
          v-if="squareData.square && isShowHeader"
          ref="appEntryRef"
          :key="entryKey"
          :square-data="squareData"
          :fee-info="feeInfo"
          @open-buy="openBuy"
          @kefu-loaded="kefuLoaded"
          @loaded="onEntriesLoaded"
        />

        <div v-if="isSelf" ref="datePickerPre" class="h-1 -mt-1">&nbsp;</div>
        <div
          v-if="isSelf"
          ref="dateWrap"
          class="date-wrap mb-16"
          :class="{ 'hidden!': hideDatePicker }"
        >
          <DatePicker ref="datePickerRef" @select="dateSelect" @loaded="dateLoaded" />
        </div>

        <div v-if="!isDeleted && stats.posts && isShowHeader" v-element-visibility="onAllVisibility" class="title">
          {{ $t("square.square.allPost") }}<span class="count">({{ isDeleted ? 0 : stats.posts }})</span>
        </div>
        <div class="post-container">
          <div v-if="!isShowHeader && (loadingUp || wheelLoading)" class="loading-up">
            <t-loading size="small" text="加载中..." />
          </div>
          <PostList
            v-if="props.squareId"
            ref="postListRef"
            :key="refreshId"
            class="trends-list"
            :api="getSquarePostList"
            :params="{ square_id: props.squareId, posted_at: postedAt }"
            :square="square"
            go-detail
            hide-more-nav
            post-privilege-tip
            show-visibility
            view-observer="HOME_PAGE"
            :cache-key="isSelf ? SQUARE_CACHE.homeTimeline : ''"
            @removed="() => refreshId++"
            @publish="postDialogVisible = true"
            @load-top="isShowHeader = true"
            @on-show="onPostShow"
          />
        </div>
      </div>

      <div v-else class="px-16 mb-16">
        <div class="empty-box">
          <Empty name="fail" :tip="squareData.blacklisted ? '你已经拉黑对方，无法查看TA的内容' : '由于对方设置，你无法执行该操作'" />
        </div>
      </div>
    </template>

    <Empty
      v-if="!squareData.square && !isOnline"
      name="offline"
      center
      tip="网络连接失败，请检查网络后重试"
    >
      <template #bottom>
        <t-button theme="primary" class="mt-12" @click="routeRefresh">点击重试</t-button>
      </template>
    </Empty>
    <PostPublishDialog v-model="postDialogVisible" :show-article="!store.isPersonal" @submit="submitPost" />

    <Tricks
      v-if="isSelf"
      :key="showKeyfu"
      :offset="{ x: '-383', y: showKeyfu ? '-90' : '-48' }"
      :uuid="store.isPersonal ? '个人广场-我的主页' : '组织广场-我的主页'"
    />
  </div>
</template>

<script setup lang="tsx">
import { computed, inject, ref, provide, defineExpose, onMounted, onUnmounted, nextTick } from 'vue';
import { useRoute } from 'vue-router';
import to from 'await-to-js';
import { vElementVisibility } from '@vueuse/components';
import { useElementVisibility, useNetwork } from '@vueuse/core';
import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next';
import { followToggle } from '@renderer/api/square/fans';
import { AxiosError, AxiosResponse } from 'axios';
import { useI18n } from 'vue-i18n';
import { getImCardIds } from '@renderer/utils/auth';
import { onMountedOrActivated } from '@renderer/hooks/onMountedOrActivated';
import { debounce } from 'lodash';
import { Year } from '@renderer/api/square/models/post';
import { useComponentEvent } from '@renderer/hooks/useComponentEvent';
import SquareAvatar from '@/views/square/components/SquareAvatar.vue';
import PostList from '@/views/square/components/post/PostList.vue';
import PostPublishDialog from '@/views/square/components/post/PostPublishDialog.vue';
import DatePicker from './components/DatePicker.vue';
import { getSquareInfo, getSquarePostList, getSquareStats } from '@/api/square/home';
import { useSquareStore } from '@/views/square/store/square';
import { getAge } from '@/views/square/utils/time';
import { SquareType, Status } from '@/api/square/enums';
import { getRegionName } from '@/components/common/region/utils';
import { IndividualProfile, Square, SquareData, SquareStatistics, teamAnnualFeeResponse, Entry } from '@/api/square/models/square';
import HomeHeader from '@/views/square/homepage/components/HomeHeader.vue';
import AppEntry from '@/views/square/homepage/components/AppEntry.vue';
import useNavigate from '@/views/square/hooks/navigate';
import { deepClone } from '@/views/square/components/page-designer/utils';
import Empty from '@/components/common/Empty.vue';
import { POST_REFRESH_KEY, ROUTE_REFRESH_INJECT_KEY, SQUARE_CACHE, errReasonMap } from '@/views/square/constant';
import UpgradeEntryPersonal from '@/views/square/components/annual-fee/UpgradeEntryPersonal.vue';
import { ErrorResponseReason } from '../enums';
import ToolbarSetting from '@/views/square/homepage/components/ToolbarSetting.vue';
import { useStateStore } from '../store/state';
import { usePreload } from './hooks';

const props = defineProps({
  squareId: {
    type: String,
    default: '',
  },
  scrollTop: {
    type: Number,
    default: 0,
  },
});

const emit = defineEmits(['stats-load', 'navigate']);

const route = useRoute();
const store = useSquareStore();
const stateStore = useStateStore();
const { backIndex } = useNavigate();
const pageContentRef = ref();

const isSelf = ref(false);
const isOuter = computed(() => route.path === '/square-single/info');
const isSquareInfo = computed(() => route.path === '/square/info');
const refreshId = ref(+new Date());
const isShowHeader = ref(true);
const routeRefresh = inject(ROUTE_REFRESH_INJECT_KEY);

const datePickerPre = ref(null);
const hideDatePicker = useElementVisibility(datePickerPre);
const upgradeEntryRef = ref(null);
const entryKey = ref(1);
const appEntryRef = ref(null);

const followed = computed({
  set: (val: boolean) => {
    stateStore.followedTarget[props.squareId] = val;
  },
  get() {
    return stateStore.followedTarget[props.squareId];
  },
});
const loading = ref(false);

const { t } = useI18n();

type Profile = IndividualProfile & {
  city?: string;
  age?: number | string;
  teamId?: string;
};
const profile = ref<Profile>({} as Profile);
const squareData = ref<SquareData>({} as SquareData);
const setting = ref({});
const orgCertInfo = ref({});
const isShowTopbar = ref(false);
// const isShowDatePicker = ref(false);
const disableList = ref([]);

const showUpgradeEntry = computed(() => {
  const teamId = profile.value.teamId;
  if (!teamId) return;
  const item = store.squareList.find((v) => v.square.originId === teamId);
  return !!item && item.isAdmin && teamId;
});

const postDialogVisible = ref(false);
const postSubmitKey = ref(1);
provide(POST_REFRESH_KEY, postSubmitKey);

const datePickerRef = ref();
const {
  loadingUp,
  load,
} = usePreload();

const loadPrevData = async (top) => {
  if (isShowHeader.value || top >= 1) return;
  await load(postListRef.value, 40);
};

/**
 * 当文章在页面顶部附近时触发
 */
const onPostShow = (item) => {
  if (item.stickOnTop) return;
  const post = item.post;
  const date = new Date(post.postedAt);
  datePickerRef.value.setCurrYear(date.getFullYear());
  datePickerRef.value.setCurrMonth(date.getMonth() + 1);
};

/**
 * 监听滚动条位置
 */
// watch(() => props.scrollTop, loadPrevData);

let wheelLoading = ref(false);
// 加载之前的动态
const onWheel = debounce(async (e) => {
  try {
    if (wheelLoading.value || loadingUp.value || e.deltaY >= 0) return;

    wheelLoading.value = true;
    await loadPrevData(e.deltaY);

    wheelLoading.value = false;
  } catch (err) {
    setTimeout(() => {
      wheelLoading.value = false;
    }, 1000);
  }
}, 400);

onMounted(() => {
  document.addEventListener('wheel', onWheel);
});

onUnmounted(() => {
  document.removeEventListener('wheel', onWheel);
});

const submitPost = () => {
  refreshId.value = Date.now();
  postDialogVisible.value = false;
  postSubmitKey.value++;
};

const onScroll = (e) => {
  const scrollTop = e.target.scrollTop;
  if (scrollTop) {
    isShowTopbar.value = scrollTop > 80;
    // isShowDatePicker.value = scrollTop <= 96;
  }
};

const postListRef = ref(null);
const scrollDisabled = computed(() => postListRef.value?.status === 'finished');
const onLoading = () => {
  postListRef.value?.loading();
};

const square = computed<Square>(
  () => {
    const result = deepClone(squareData.value?.square || ({} as Square));
    if (squareData.value?.remark) result.remark = squareData.value.remark;
    return result;
  },
);
const isDeleted = computed(() => square.value.status === Status.Deleted);

const { isOnline } = useNetwork();

// 获取广场信息
const getSquareData = async (square_id) => {
  try {
    const value = localStorage.getItem(SQUARE_CACHE.squareInfo);
    if (value) {
      squareData.value = JSON.parse(value)[square_id]?.data?.info || {};
    }
  } catch (e) {
    console.log(e);
  }

  let [err, res] = await to(getSquareInfo({ square_id }, {
    cache: isSelf.value,
    cacheKey: SQUARE_CACHE.squareInfo,
    uid: square_id,
    isOnline: isOnline.value,
  }));
  if (err) return;

  const { info } = res.data;
  const { organizationCertInfo, organizationProfile } = info;
  squareData.value = info;
  followed.value = squareData.value.square.followed;

  // 自己的广场号，更新 store
  if (info.square.squareId === store.squareId) {
    store.squareInfo = info;
  }

  if (info.square.squareType === SquareType.Individual) {
    const { individualProfile, individualSetting } = info;
    profile.value = {
      ...individualProfile,
      city: getRegionName(individualProfile.region),
      age: individualProfile.birthday ? getAge(individualProfile.birthday) : '',
    };
    setting.value = individualSetting;
  } else {
    orgCertInfo.value = organizationCertInfo;
    profile.value = { ...profile.value, ...organizationProfile };
  }
};

// 统计数据
const stats = ref<SquareStatistics>({});
const getStats = async (square_id: string) => {
  const [err, res] = await to(getSquareStats({ square_id }, {
    cache: isSelf.value,
    cacheKey: SQUARE_CACHE.statistics,
    uid: square_id,
    isOnline: isOnline.value,
  }));
  if (err) return;

  stats.value = res.data.stats;
  emit('stats-load', stats.value);
};

const navigate = (type: string) => {
  if (isDeleted.value) return;
  emit(
    'navigate',
    type,
    setting.value,
    squareData.value.square.squareType !== SquareType.Individual,
  );
};
// 切换关注状态
const toggleFollow = async () => {
  if (store.squareInfo.square.silenced) {
    await MessagePlugin.warning(t('square.square.limitTip'));
    return;
  }

  if (followed.value) {
    // 取消关注前确认
    const confirm = await new Promise((resolve) => {
      const confirmDia = DialogPlugin.confirm({
        className: 'square-unfollow-dialog',
        header: t('square.square.unfollowTip', [squareData.value.square.name]),
        theme: 'info',
        onConfirm: async () => {
          confirmDia.destroy();
          resolve(true);
        },
        onClose: () => {
          confirmDia.hide();
          resolve(false);
        },
      });
    });
    if (!confirm) return;
  }

  loading.value = true;
  const [err] = await to<AxiosResponse, AxiosError<{ reason: string }>>(followToggle({ square_id: squareData.value.square.squareId }));
  loading.value = false;
  if (err) {
    const errReason = err.response.data.reason;
    if (errReason === ErrorResponseReason.SquareSilenced) {
      await MessagePlugin.warning(t('square.square.limitTip'));
      return;
    }

    if (errReason === ErrorResponseReason.SquareDeleted) {
      await MessagePlugin.warning(t('square.square.followFail'));
      return;
    }

    await MessagePlugin.warning(errReasonMap[errReason]);
    return;
  }

  await MessagePlugin.success(t('square.square.followSuccess'));
  followed.value = !followed.value;
};

const newestDate = ref({ year: 0, month: 0 });
const dateLoaded = (years: Year[]) => {
  const { year, months } = years[0];
  newestDate.value.year = year;
  newestDate.value.month = months.slice(-1)[0].month;
};

const postedAt = ref('');
// 选择年月后定位到指定动态
const dateSelect = (year: number, month: number) => {
  let y = year;
  let m = month;

  if (m === 12) {
    y += 1;
    m = 0;
  }

  // 查找有指定年月的已加载的数据
  const pageContent = document.querySelector('.page-content');
  const dataList = postListRef.value.dataList;
  let hasDataForDate = false;
  let index = 0;

  for (const item of dataList) {
    if (item.stickOnTop) {
      continue;
    }

    const date = new Date(item.post.postedAt);
    if (date.getFullYear() === y && date.getMonth() + 1 === m) {
      hasDataForDate = true;
      break;
    }
    index++;
  }

  // 找到则滚动定位
  if (hasDataForDate) {
    const list = document.querySelector('.trends-list') as HTMLElement;
    const target = list?.children[index] as HTMLElement;

    let headerHeight = 46 + 12;
    if (y === newestDate.value.year && m === newestDate.value.month) {
      headerHeight += 46;
    }

    const top = target.offsetTop + list.offsetTop - headerHeight;
    pageContent.scrollTo({ top, behavior: 'smooth' });
  } else {
    isShowHeader.value = false;
    pageContent.scrollTo({ top: 0 });
    // 没找到即未加载过指定月份，直接加载对应年月的动态
    postedAt.value = new Date(`${y}/${m + 1}/1`).toISOString();
    refreshId.value++;
  }

  nextTick(() => {
    setTimeout(() => {
      hideDatePicker.value = false;
    }, 200);
  });
};

const isVisible = ref(false);
const onAllVisibility = (state) => {
  isVisible.value = state;
};

// 年费信息
const feeInfo = ref<teamAnnualFeeResponse>();
const loadFeeSuccess = (data) => {
  feeInfo.value = data;
};

const openBuy = () => {
  upgradeEntryRef.value?.openToBuy();
};

onMountedOrActivated(() => {
  const squareId = props.squareId;
  isSelf.value = store.squareId === squareId;
  disableList.value = getImCardIds();

  getSquareData(squareId);
  getStats(squareId);
});

// 当 entries 数据加载完成时的处理函数
const onEntriesLoaded = (_entries: Entry[]) => {
  // 标记 entries 已加载完成
  entriesLoaded.value = true;
  // 处理待处理的参数队列
  pendingParams.value.forEach(handleParamsUpdate);
  setTimeout(() => {
    pendingParams.value = []; // 清空待处理队列
  }, 0);
};

// 待处理的参数队列
const pendingParams = ref<Record<string, any>[]>([]);
const entriesLoaded = ref(false);

// 监听广场主页参数更新事件
useComponentEvent({
  handler: (data) => {
    // 只处理广场参数更新事件
    if (data.type === 'square:page-params-update' && data.payload.action === 'update_params') {
      const { squareId: eventSquareId, query } = data.payload;
      // 检查是否是当前页面的事件
      if (+eventSquareId === +props.squareId) {
        // 如果 entries 已经加载完成，直接处理参数
        if (entriesLoaded.value) {
          handleParamsUpdate(query);
        } else {
          // 否则将参数添加到待处理队列
          pendingParams.value.push(query);
        }
      }
    }
  },
  errorHandler: (error) => {
    console.error('广场主页事件处理错误:', error);
  },
});

// 处理参数更新的方法
const handleParamsUpdate = (query: Record<string, any>) => {
  if (query.refresh) {
    refreshId.value = Date.now();
  }
  // 如果有特定的entry类型需要导航
  if (query.entryType && appEntryRef.value) {
    const entryType = Array.isArray(query.entryType) ? query.entryType[0] : query.entryType;
    appEntryRef.value.navigateToEntry(entryType, query.externalAppId);
  }
};

const showKeyfu = ref(false);
const kefuLoaded = (show: boolean) => {
  showKeyfu.value = show;
};

defineExpose({
  postListRef,
});
</script>

<style lang="less">
.square-h-dialog {
  text-align: center;
  padding: 0;
  font-size: 14px;
  .t-dialog__body {
    text-align: center;
    padding: 0;
  }
  .name {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 16px;
  }
  .icon {
    font-size: 56px;
    margin-top: 12px;
    margin-bottom: 24px;
    color: #d4261a;
  }
}
</style>

<style lang="less" scoped>
.page-content {
  flex: 1;
  position: relative;
  overflow-y: auto;
}

.top-container {
  position: sticky;
  left: 0;
  top: 0;
  width: 100%;
  height: 56px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 8px;
  background: #F5F8FE;
  box-shadow: 0 1px 3px #ddd;
  z-index: 999;
  .back {
    display: flex;
    float: left;
    cursor: pointer;
  }
  .name {
    color: #4D5EFF;
    font-size: 18px;
    height: 28px;
    padding: 2px 8px 2px 2px;
    border-radius: 32px;
    background: #fff;
    display: flex;
    align-items: center;
    gap: 4px;
    width: auto;
    max-width: 300px;
    cursor: pointer;
    &.followd {
      cursor: auto;
    }
    &:hover {
      box-shadow: 0 0 5px #eee;
    }
    .avatar {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      // margin-right: 8px;
      overflow: hidden;
    }
    &-title {
      flex: 1;
      color: #516082;
      font-size: 14px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .follow {
    display: flex;
    align-items: center;
    &-text {
      font-size: 16px;
      transform: translate3d(-2px, 0px, 0);
    }
  }
}

.date-wrap {
  position: sticky;
  top: 56px;
  display: flex;
  width: 100%;
  padding: 8px 12px;
  align-items: center;
  gap: 4px;
  border-bottom: 1px solid var(--divider-kyy_color_divider_deep, #D5DBE4);
  background: var(--bg-kyy_color_bg_deep, #F5F8FE);
  z-index: 99;
}

.top {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 20px 16px;
  font-size: 14px;
  color: #fff;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 9;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0.25) 0%, rgba(0, 0, 0, 0.00) 90.18%);
  &::after {
    content: " ";
    clear: both;
  }

  .back {
    display: flex;
    float: left;
    cursor: pointer;
  }
}

.body {
  height: 100%;
  .title {
    padding: 0 16px;
    color: var(--lingke-Black-90, #1A2139);
    font-weight: 600;
    .count {
      margin-left: 16px;
      color: var(--lingke-Gray3, #828DA5);
      font-weight: 400;
    }
  }
}

.post-container {
  padding: 0 16px;
}

.empty-box {
  display: flex;
  width: 100%;
  padding: 24px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 12px;
  border-radius: 8px;
  border: 1px solid var(--divider-kyy-color-divider-light, #ECEFF5);
  background: var(--bg-kyy-color-bg-light, #FFF);
}

.btn-more {
  position: absolute;
  right: 0;
  bottom: 0;
  padding-left: 10px;
  background: #fff;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0), #fff 20%, #fff);
}

.album-title {
  color: #333333;
  font-size: 16px;
  margin: 0 16px 16px 16px;
  cursor: pointer;

  .album-title-icon {
    font-size: 24px;
    vertical-align: middle;
  }

  .album-title-span {
    margin-left: 8px;
    vertical-align: middle;
  }
}
:deep(.t-dropdown__item--theme-default) {
  height: initial !important;
}

.icon-wrap {
  display: flex;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 99px;
  border: 1px solid
    var(--color-button-border-kyy-color-button-border-border-dedault, #d5dbe4);
  background: var(
    --color-button-border-kyy-color-button-border-bg-default,
    #fff
  );
}

.fade-enter-from {
  opacity: 0;
  transform: scaleY(0);
}
.fade-enter-active {
  transition: all .5s;
}
.mx-6 {
  margin: 0 8px 0 6px;
}
.loading-up {
  text-align: center;
}
</style>
