<template>
  <div>
    <div class="logo-large">
      <img src="@renderer/assets/account/logo_img_logo.svg" alt="">
    </div>
    <div class="login-progress">
      <t-progress :percentage="loginPercent" :label="false" />
    </div>
    <div class="login-text">{{ t("account.logining") }}</div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';
import lodash from 'lodash';
import { getProfile } from '@renderer/api/account';
import { setProfilesInfo, setOpenid, removeOpenid, removeCards, removeStaff, removeAppTeamId, removeStore } from '../../utils/auth';
import { removeZhixingTab } from '@renderer/views/zhixing/storage';
import { loadAcountCards } from '@renderer/views/message/service/accountUtils';
import LynkerSDK from '@renderer/_jssdk';
const loginPercent = ref(0);
const { t } = useI18n();
import { removeProjectTeamID } from "@renderer/views/engineer/utils/auth";
import {
  removeMember,
} from "@renderer/views/member/utils/auth";

const props = defineProps({
  // 多账号使用
  isMoreAccount: {
    type: Boolean,
    default: false,
  },
});

// 登录超时
const loginTimeout = lodash.debounce(() => {
  console.error('loginTimeout');
  removeOpenid();
  emits('loginErr', '登录超时');
}, 1000 * 60 * 1);

const loadingPercent = () => {
  clearStorage();
  loginTimeout();
  getProfile().then((res) => {
    console.log(res, 'getProfile');
    if (res.status === 200) {
      setProfilesInfo(res.data);
      setOpenid(res.data.openid);
      loadAcountCards(false);
      const timer = setInterval(() => {
        loginPercent.value += 1;
        if (loginPercent.value >= 99) {
          loginSuc(res.data.openid);
          clearInterval(timer);
        }
      }, 20);
    } else {
      removeOpenid();
      emits('loginErr', res.data.message || 'error');
    }
  }).catch(err => {
    loginTimeout.cancel();
    removeOpenid();
    emits('loginErr', err?.response?.data?.message || 'error');
  });
};
const clearStorage = () => {
  removeZhixingTab();
  removeCards();
  removeStaff();
};
const loginSuc = (openid) => {
  console.log('loginSuc: ', 1);
  if (props.isMoreAccount) {
    console.log('loginSuc: ', 2);
    LynkerSDK.ipcRenderer.invoke('login-suc-moreAccount', { refreshMainWindow: true, openid: openid }).then(res => {
      console.log('loginSuc: ', 4);
      removeStore('secondInstanceParams');
      removeProjectTeamID();
      removeMember();
      removeAppTeamId();
      console.log(res)
      LynkerSDK.ipcRenderer.invoke('set-popbv-other', {show: false});
      LynkerSDK.ipcRenderer.invoke('set-popbv', {show: false});
    });
  } else {
    console.log('loginSuc: ', 3);
    // 登录之后上报用户ID
    // crashReporter.addExtraParameter('userId', openid);
    LynkerSDK.ipcRenderer.invoke('login-suc');
  }
  loginTimeout.cancel();
};
const emits = defineEmits(['loginErr']);
defineExpose({
  loadingPercent,
});
</script>

<style lang="less" scoped>
.logo-large {
  margin-top: 94px;
  text-align: center;
  img {
    vertical-align: bottom;
    width: 64px;
    height: 64px;
  }
}
.login-progress {
  width: 176px;
  margin: 48px auto 0;
}

.login-text{
  margin-top: 24px;
  text-align: center;
  color: #1A2139;
  font-size: 16px;
  line-height: 24px;
  font-weight: bold;
}
</style>
