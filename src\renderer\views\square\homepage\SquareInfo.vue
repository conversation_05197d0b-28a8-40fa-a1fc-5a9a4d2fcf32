<template>
  <div class="page-content flex">
    <template v-if="squareId">
      <Home :square-id="squareId" @navigate="onNavigate" />

      <FansAndFollow
        :key="refreshId"
        v-model="visible"
        :square-id="squareId"
        :type="activeType"
        :ff-visibility="ffVisibility"
      />
    </template>
  </div>
</template>

<script setup lang="ts" name="SquareInfo">
import { onMounted, ref } from 'vue';
import Home from '@renderer/views/square/homepage/Home.vue';
import { useRoute } from 'vue-router';
import { MessagePlugin } from 'tdesign-vue-next';
import { useI18n } from 'vue-i18n';
import FansAndFollow from '@/views/square/homepage/components/FansAndFollow.vue';
import { Visibility } from '@/views/square/constant';

const route = useRoute();
const { t } = useI18n();
const squareId = ref('');
const refreshId = ref('');

onMounted(() => {
  squareId.value = route.query.id as string;
});

const visible = ref(false);
const activeType = ref(0);
const ffVisibility = ref(true);

const onNavigate = (type, setting, isOrg) => {
  if (!isOrg) {
    // 关注和粉丝列表不可见
    ffVisibility.value = setting.ffVisibility === Visibility.PUBLIC;
    if (!ffVisibility.value && ['follows', 'fans'].includes(type)) {
      const text = { follows: t('square.square.follow'), fans: t('square.square.fans') }[type];
      MessagePlugin.warning(t('square.square.navigateTip', [text]));
      return;
    }
  } else {
    ffVisibility.value = true;
  }

  activeType.value = { commonFollows: 0, follows: 1, fans: 2 }[type] || 0;
  visible.value = true;
};
</script>
