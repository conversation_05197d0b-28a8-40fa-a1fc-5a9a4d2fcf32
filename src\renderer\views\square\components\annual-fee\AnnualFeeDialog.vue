<template>
  <t-drawer v-model:visible="visible" v-bind="$attrs" size="472px" :header="header" :z-index="2500" :footer="false"
    :close-btn="true" :class="['d-square-annual-fee', { single: annualFeePackages?.packages?.length <= 0 }]"
    @close="emit('close')"
  >
    <div v-if="loading" class="flex align-center justify-center h-full"><t-loading text="加载中..." /></div>
    <template v-else>
      <div v-if="teamInfo?.name" class="card !items-center">
        <SquareAvatar :square="teamInfo" size="44px" />
        <div class="title">{{ teamInfo.name }}</div>
      </div>
      <div v-else-if="upgradeInfo" class="card flex-items-start">
        <SquareAvatar :square="{
            name: upgradeInfo.team?.name,
            avatar: upgradeInfo.team?.logo,
            squareType: SquareType.Organization,
          }" size="44px" />
        <div class="right-content">
          <div class="title">{{ upgradeInfo.team?.name }}</div>
          <t-link v-if="isUpgrade" theme="primary" hover="color" @click="currPkgVisible = true">
            {{ $t('square.annualFee.currentPackage') }}
          </t-link>
          <div v-else class="time">{{ $t('square.currentExpiredAt') }}：{{ currExpiredAtFormat }}</div>

          <CurrentPackage v-if="currPkgVisible" v-model="currPkgVisible" :data="upgradeInfo" />
        </div>
      </div>

      <!--套餐-->
      <template v-if="annualFeePackages">
        <div v-if="annualFeePackages.packages?.length" class="card-content">
          <div class="package-wrap">
            <div v-for="item in annualFeePackages.packages" :key="item.id"
              :class="['package', { active: item.id === activePackageId, disable: pkgItemDisableMap[item.id] }]"
              @click="selectPackage(item)">
              <img src="@/assets/square/vip-package.svg" alt="" class="icon" />
              <span v-if="item.tag" class="tag"> <iconpark-icon name="iconhot" class="mr-2" /> {{ item.tag }} </span>
              <div class="name">{{ item.name }}</div>
              <div class="price">
                <template v-if="!isOrderByKefu(item) || upgrade">{{ currency?.symbol }}{{ Number(item.price.value)
                  }}</template>
                <template v-else>&nbsp;</template>
              </div>

              <div v-if="trialPackageId === item.id" class="trial-wrap">
                <div class="text">体验套餐</div>
                <img src="@/assets/square/trapezium2.svg" alt="" class="trial-mark" />
              </div>
            </div>
          </div>

          <!-- 升级套餐 -->
          <!-- <div v-if="upgrade" class="package-content mb-24">
            <div class="row">
              <div class="flex-col-center">{{ $t('square.annualFee.validUntil') }}</div>
              <div class="flex-1 text-right font-bold">{{ expiredAtFormat }}</div>
            </div>
          </div> -->

          <!--套餐年限-->
          <!-- <template> -->
          <div class="package-content mb-24">
            <div class="row">
              <div class="flex-col-center">{{ $t('square.annualFee.packageYear') }}</div>
              <div class="flex-1">
                <p class="text-right">
                  {{ activePackage?.years }} <strong>{{ $t('square.year') }}</strong>
                </p>
                <p class="desc">
                  {{ upgrade ? $t('square.annualFee.upgradeValidDateTo') : $t('square.annualFee.validDateTo') }}
                  {{ expiredAtFormat }}
                </p>
              </div>
            </div>
          </div>
          <!-- </template> -->

          <!--套餐内容-->
          <div class="title font-bold">{{ $t('square.comboContent') }}</div>
          <div v-if="!isOpen" class="mb-12 color-text-2">{{ $t('square.annualFee.tip1') }}</div>

          <t-table row-key="id" :data="selectItems" :columns="columns">
            <template #content="{ row }">
              <iconpark-icon v-if="row.content === '√'" name="iconcorrect" class="icon" />
              <template v-else>{{ row.content }}</template>
            </template>
          </t-table>
        </div>

        <!-- 无可用套餐。升级时，已是最高等级套餐时提示 -->
        <template v-else-if="isUpgrade && !hasHigherLevel">
          <div class="card-content">
            <div class="package-wrap no-level">
              <div class="package !w-full">
                <div class="name">{{ $t('square.annualFee.tip2') }}</div>
              </div>
            </div>
          </div>

          <!-- 当前套餐年费到期时间 -->
          <!-- <div class="package-content mx-24 mb-24 !-mt-24">
            <div class="row">
              <div class="flex-col-center">{{ $t('square.annualFee.validUntil') }}</div>
              <div class="flex-1 text-right font-bold">{{ expiredAtFormat }}</div>
            </div>
          </div> -->
        </template>
      </template>

      <!--默认套餐-->
      <div v-if="!activePackage && hasHigherLevel" class="card-content">
        <div class="package-content">
          <div class="row">
            <div class="flex-col-center">{{ isOpen ? $t('square.selectYear') : $t('square.renewYear') }}</div>
            <div class="flex-1">
              <p class="text-right years-input">
                <t-input-number v-model="years" :min="1" :max="3" :decimal-places="0" class="w-168!" />
              </p>
              <p class="desc">{{ $t('square.orderedExpiredAt') }} {{ defaultExpiredAtFormat }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 默认套餐内容 -->
      <div v-if="!hasPackageContent && hasHigherLevel" class="card-content">
        <div class="title font-bold">{{ $t('square.comboContent') }}</div>
        <t-table row-key="id" :data="selectItems" :columns="columns">
          <template #content="{ row }">
            <iconpark-icon v-if="row.content === '√'" name="iconcorrect" class="icon" />
            <template v-else>{{ row.content }}</template>
          </template>
        </t-table>
      </div>

      <!-- 开通广场-->
      <template v-if="isOpen && !notShowInviteCode">
        <!-- 邀请码-->
        <div class="card !items-center">
          <div class="color-text-1">{{ $t('square.annualFee.inviteCode') }}</div>
          <div class="right-content flex justify-end">
            <t-form ref="formRef" :data="openFormData" :rules="openFormRules">
              <t-form-item name="inviteCode">
                <t-input v-model="openFormData.inviteCode" class="w-168"
                  :placeholder="$t('square.annualFee.inputInviteCode')" />
              </t-form-item>
            </t-form>
          </div>
        </div>
      </template>

      <div class="card-footer">
        <div v-if="!(isUpgrade && !hasHigherLevel) && !isActivePkgOrderByKefu" class="flex-y-center">
          <t-radio v-model="agree" allow-uncheck>{{ $t('square.action.agree') }}</t-radio>
          <t-link theme="primary" hover="color" @click="agreementDialogRef.open(AgreementType.OrganizationSquare)">
            {{ $t('square.buyAgreement') }}
          </t-link>
        </div>

        <div class="bottom">
          <div v-if="isActivePkgOrderByKefu" class="price">&nbsp;</div>
          <template v-else>
            <template v-if="invalidPrice">
              <div class="price">--</div>
            </template>
            <template v-else>
              <div v-if="isUpgrade" class="flex">
                <span class="price">{{ currency?.symbol }}{{ upgradePrice?.price?.value }}</span>
                <t-tooltip :content="upgradePrice?.explain" placement="top">
                  <iconpark-icon name="iconhelp" class="icon ml-8" />
                </t-tooltip>
              </div>
              <template v-else>
                <template v-if="salePrice">
                  <div class="price">
                    {{ currency?.symbol }}{{ numberWithCommas(salePrice) }}
                    <span class="origin-price">{{ currency?.symbol }}{{ numberWithCommas(totalPrice) }}</span>
                  </div>
                </template>
                <div v-else class="price">{{ currency?.symbol }}{{ numberWithCommas(totalPrice) }}</div>
              </template>
            </template>
          </template>

          <!-- <t-button
            v-if="isActivePkgOrderByKefu"
            theme="primary"
            :loading="submitting"
            :disabled="submitting"
            @click="submitDebounce"
          >
            联系客服
          </t-button>-->

          <specialist v-if="isActivePkgOrderByKefu" ref="specialistRef" channel="package" :team-id="props.teamId"
            :fee-package="true" @album-package-imrun="imrun">
            <t-button theme="primary" :loading="submitting" :disabled="submitting"> 联系客服 </t-button>
          </specialist>

          <t-button v-else theme="primary" :loading="submitting" :disabled="submitting || invalidPrice"
            @click="submitDebounce">
            {{ $t('square.action.buyNow') }}
          </t-button>
        </div>
      </div>
    </template>

    <AllPay ref="allPayRef" v-model:all-pay-dialog="allPayDialog" :row-data="payParam"
      @payment-callback="paymentSuccess" />

    <AgreementDialog ref="agreementDialogRef" />
  </t-drawer>
</template>

<script setup lang="tsx">
  import { computed, ref } from 'vue';
  import to from 'await-to-js';
  import moment from 'moment';
  import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next';
  import { AxiosError, AxiosResponse } from 'axios';
  import { useI18n } from 'vue-i18n';
  import debounce from 'lodash/debounce';
  import specialist from '@renderer/views/customerService/specialist.vue';
  import { onMountedOrActivated } from '@renderer/hooks/onMountedOrActivated';
  import { getOfficialKeFuGroupList } from '@renderer/api/customerService';
  import { getOpenid, getProfilesInfo } from '@renderer/utils/auth';
  import { PackageType } from '@renderer/views/message/chat/common/constant';
  import { DATE_TIME_FORMAT } from '@/utils/date';
  import SquareAvatar from '@/views/square/components/SquareAvatar.vue';
  import { getAnnualFeePackages, getUpgradeAnnualFeePrice } from '@/api/square/home';
  import { orderTip } from '@/views/square/hooks/tip';
  import { AgreementType } from '@/api/square/common';
  import { currencyConfigs, getCurrencyConfig } from '@/views/square/utils/currency';
  import {
    AnnualFeePackagesResponse,
    ChannelType,
    FeePackage,
    OrderMethod,
    UpgradePriceResponse,
  } from '@/api/square/models/fee';
  import {
    AnnualFeeConsultResponse,
    AnnualFeeOrderRequest,
    OrderChannel,
    OrderType,
    teamAnnualFeeResponse,
  } from '@/api/square/models/square';
  import { OrganizationCertStatus, RegionCode, SquareType } from '@/api/square/enums';
  import { annualFeeConsult, createAnnualFeeOrder, getTeamAnnualFeeInfo } from '@/api/square/square';
  import { checkInviteCode } from '@/api/partner/api';
  import { getTeamsDetailAxios } from '@/api/member/api/businessApi';
  import { getResponseResult } from '@/utils/myUtils';
  import { numberWithCommas } from '@/views/square/utils/format';
  import { errReasonMap } from '@/views/square/constant';
  import CurrentPackage from '@/views/square/components/annual-fee/CurrentPackage.vue';
  import { getPackageItems } from '@/views/square/components/annual-fee/utils';
  import { ErrorResponseReason } from '@/views/square/enums';
  import AllPay from '@/components/paymentDialog/allPay.vue';
  import { MsgShareType, sendApplicationMsg } from '@/utils/share';
  import { isMacOS, isWindows } from '../../utils';
  import AgreementDialog from '@/views/square/components/AgreementDialog.vue';
  import LynkerSDK from '@renderer/_jssdk';

  const { ipcRenderer } = LynkerSDK;
  // 另可联盟-入驻广场：https://app.mockplus.cn/run/prototype/B_WPlIqhUx/-Q_NDbZsE/Jq_DT25jZe?allowShare=1&cps=expand&ha=1&isShare=false
  // 广场年费套餐（开通、续费、升级）https://app.mockplus.cn/run/prototype/aXv-mii6Ou/5DuxhvhRc/K6S9tvwk4ZT?allowShare=1&cps=expand&ha=1&isShare=false
  // 数字平台1.2（购买、续费、升级） https://app.mockplus.cn/run/prototype/84h2Mm5Ww_/dm2dLJp00/UywvzfIgV3F

  const props = defineProps < {
    modelValue: boolean;
    teamId: string;
    // 组织广场号id（开通时不用传）
    // squareId?: string;
    // 弹窗标题
    title?: string;

    // 是否为开通广场号
    open?: boolean;
    // 组织信息
    // team?: Partial<TeamItem>;
    // 邀请码
    inviteCode?: string;

    // 是否为升级套餐
    upgrade?: boolean;
  } > ();

  const visible = computed({
    get() {
      return props.modelValue;
    },
    set(value) {
      emit('update:modelValue', value);
    },
  });
  const emit = defineEmits(['update:modelValue', 'success', 'upgrade-loaded', 'close']);

  const isMas = __APP_ENV__.VITE_APP_MAS;
  const notShowInviteCode = ref(false);

  const { t } = useI18n();
  const isUpgrade = ref(props.upgrade);
  const currPkgVisible = ref(false);

  const isOpen = ref(props.open);

  const header = computed(() => {
    if (props.title) return props.title;
    if (isOpen.value) return t('square.annualFee.buyPackage');
    if (isUpgrade.value) return t('square.annualFee.upgradePackage');
    return t('square.square.packageRenew');
  });

  const getNotShowInviteCode = () => {
    const account_mobile = getProfilesInfo()?.account_mobile;
    notShowInviteCode.value = isMas && ['***********', '***********'].includes(account_mobile)
  }

  const loading = ref(false);
  onMountedOrActivated(async () => {
    loading.value = true;
    await fetchFeeInfo();
    await getList();
    isOpen.value && (await getTeamInfo());
    loading.value = false;
    getNotShowInviteCode();
  });

  // 年费套餐信息
  const upgradeInfo = ref < teamAnnualFeeResponse > ();
  const fetchFeeInfo = async () => {
    const [err, res] = await to < AxiosResponse, AxiosError< { reason: string } >> (
      getTeamAnnualFeeInfo({ team_id: props.teamId, annual_fee_detail: 'true' }),
  );
    if (err) return;
    upgradeInfo.value = res.data;
    emit('upgrade-loaded', res.data);
  };

  // 开通的组织信息
  const teamInfo = ref(
    {} as {
      teamId: string;
      avatar: string;
      name: string;
      squareType: SquareType;
      certStatus: OrganizationCertStatus;
      regionCode: string;
    },
  );

  const getTeamInfo = async () => {
    let result = await getTeamsDetailAxios({ teamId: props.teamId });
    result = getResponseResult(result);
    const { teamId, logo, fullname, type, region } = result.data;
    teamInfo.value = {
      teamId,
      avatar: logo,
      name: fullname,
      squareType:
        [
          SquareType.Other,
          SquareType.Enterprise,
          SquareType.BusinessAssociation,
          SquareType.IndividualBusiness,
          SquareType.Government,
        ][type] || SquareType.Other,
      certStatus: OrganizationCertStatus.Certified,
      // 所在地区，中国：1，澳门：2，香港：3
      // regionCode: ['', 'CN', 'MO', 'HK'][region],
      regionCode: region,
    };
  };

  const formRef = ref(null);
  const openFormData = ref({
    inviteCode: props.inviteCode || '',
  });
  const openFormRules = {
    inviteCode: [],
  };

  // 是否有更高等级的套餐
  const hasHigherLevel = computed(() => {
    if (!isUpgrade.value) return false;

    const level = upgradeInfo.value?.annualFeeDetail?.package?.level;
    if (!level) return true;

    const packages = annualFeePackages.value?.packages;
    return packages?.length ? packages?.some((v) => v.level > level) : false;
  });

  // 当前套餐过期时间
  const currExpiredAtFormat = computed(() => moment(upgradeInfo.value?.annualFeeExpiredAt).format(DATE_TIME_FORMAT));
  // 已选套餐年费到期时间
  const expiredAtFormat = computed(() => {
    // 开通/购买
    if (isOpen.value)
      return moment()
        .add(activePackage.value?.years || 1, 'years')
        .format(DATE_TIME_FORMAT);

    // 升级
    if (isUpgrade.value)
      return moment()
        .add(activePackage.value?.years || 1, 'years')
        .format(DATE_TIME_FORMAT);

    // 续费
    const expiredAt = upgradeInfo.value?.annualFeeExpiredAt;
    const isExpired = new Date(expiredAt).getTime() < new Date().getTime();

    // 已到期：当前时间+年限；未到期：广场号到期时间+年限
    const dateStart = isExpired ? new Date() : moment(expiredAt);
    return moment(dateStart)
      .add(activePackage.value?.years || 1, 'years')
      .format(DATE_TIME_FORMAT);
  });

  const annualFeePackages = ref < AnnualFeePackagesResponse > ();
  const trialPackageId = ref('');
  const activePackageId = ref('');
  const activePackage = computed(() => annualFeePackages.value?.packages.find((v) => v.id === activePackageId.value));

  const channelType = __APP_ENV__.VITE_APP_MAS ? ChannelType.MAC : ChannelType.WinMAC;
  // 指定套餐是否通过客服下单
  const isOrderByKefu = (pkg: FeePackage) =>
    pkg.displayChannels.filter((v) => v.channelType === channelType && v.orderMethod === OrderMethod.OrderThroughKefu)
      .length > 0;
  // 选中套餐的购买渠道
  const activeChannel = computed(() => activePackage.value?.displayChannels.find((v) => v.channelType === channelType));
  // 选中套餐是否通过客服下单
  const isActivePkgOrderByKefu = computed(
    () => !isUpgrade.value && activeChannel.value?.orderMethod === OrderMethod.OrderThroughKefu,
  );

  // 支付货币类型
  const currency = computed(() => {
    const currencyCode = activePackage.value
      ? activePackage.value?.price?.currencyCode
      : annualFeePackages.value?.defaultFee?.currencyCode;
    return currencyConfigs.find((v) => v.ISOCode === currencyCode) || getCurrencyConfig();
  });
  const pkgItemDisableMap = ref({});

  // 原价
  const totalPrice = computed(() => {
    if (!annualFeePackages?.value) return '';
    if (activePackage.value) return (+activePackage.value.fee.value).toFixed(2);
    // 默认套餐
    return (+annualFeePackages.value.defaultFee.value * years.value).toFixed(2);
  });
  // 销售价
  const salePrice = computed(() => {
    if (!annualFeePackages?.value) return '';
    if (activePackage.value) return (+activePackage.value.price.value).toFixed(2);
    return '';
  });
  // 结算金额
  const amount = computed(() => {
    if (isUpgrade.value) return +upgradePrice.value.price.value;
    return +(salePrice.value || totalPrice.value);
  });

  // 升级价
  const upgradePrice = ref < UpgradePriceResponse > ();

  // 如B1小于A1时，则支付金额显示为 “- -” ，立即购买不可点击（price 或立即购买接口返回错误码 ANNUAL_FEE_UPGRADE_PRICE_INVALID）
  const upgradePriceInvalid = ref(false);
  // 如C小于等于0天时，则弹窗提示：当前不可升级，请前往续费升级，点击立即续费，则关闭升级弹窗，显示续费弹窗（price 或立即购买接口返回错误码 ANNUAL_FEE_UPGRADE_INSUFFICIENT_DAYS）
  const upgradeInsufficientDays = ref(false);

  // 无效价格
  const invalidPrice = computed(() => isUpgrade.value && (upgradePriceInvalid.value || !hasHigherLevel.value));

  const handleAnnualFeeError = async (err: AxiosError<{ reason: ErrorResponseReason; message: string }>) => {
    const { reason, message } = err.response.data;
    if (reason === ErrorResponseReason.AnnualFeeUpgradePriceInvalid) {
      upgradePriceInvalid.value = true;
      return;
    }

    if (
      [ErrorResponseReason.AnnualFeeUpgradeInsufficientDays, ErrorResponseReason.ANNUAL_FEE_NOT_UPGRADEABLE].includes(
        reason,
      )
    ) {
      const tips = {
        [ErrorResponseReason.AnnualFeeUpgradeInsufficientDays]: t('square.annualFee.tip4'),
        [ErrorResponseReason.ANNUAL_FEE_NOT_UPGRADEABLE]: t('square.annualFee.tip9'),
      };
      upgradeInsufficientDays.value = true;
      const confirmDia = DialogPlugin.confirm({
        header: t('square.tip'),
        body: tips[reason],
        theme: 'info',
        confirmBtn: t('square.annualFee.renewalNow'),
        cancelBtn: { content: t('square.action.cancel'), theme: 'default', variant: 'outline' },
        closeBtn: null,
        closeOnOverlayClick: false,
        onConfirm: async () => {
          confirmDia.destroy();
          isUpgrade.value = false;
          await fetchFeeInfo();
          await getList();
        },
        onClose: () => {
          confirmDia.hide();
        },
      });
      return;
    }
    if ([ErrorResponseReason.ANNUAL_FEE_PENDING_EXIST].includes(reason)) {
      const confirmDia = DialogPlugin.confirm({
        header: t('square.tip'),
        body: t('square.annualFee.tip8'),
        theme: 'info',
        confirmBtn: t('square.action.confirm'),
        cancelBtn: null,
        closeBtn: null,
        closeOnOverlayClick: false,
        onConfirm: async () => {
          confirmDia.destroy();
        },
      });
      return;
    }
    if (
      [
        ErrorResponseReason.ANNUAL_FEE_PACKAGE_NOT_FOUND,
        ErrorResponseReason.ANNUAL_FEE_EFFECTIVE_PACKAGE_CHANGED,
      ].includes(reason)
    ) {
      await fetchFeeInfo();
      await getList();
    } else {
      MessagePlugin.warning(message || errReasonMap[reason]);
    }
  };

  // 选择套餐
  const selectPackage = async (item) => {
    if (pkgItemDisableMap.value[item.id]) return;
    activePackageId.value = item.id;

    // 升级套餐
    if (isUpgrade.value) {
      upgradePriceInvalid.value = false;
      const [err, res] = await to < AxiosResponse, AxiosError< { reason: ErrorResponseReason; message: string } >> (
        getUpgradeAnnualFeePrice({
          team_id: props.teamId,
          fee_package_id: item.id,
        }),
    );
      if (err) {
        handleAnnualFeeError(err);
        return;
      }

      upgradePrice.value = res.data;
    }
  };

  const columns = ref([
    { colKey: 'itemTypeText', title: t('square.annualFee.feature') },
    {
      colKey: 'content',
      title: () => <span>{t('square.annualFee.packageBenefit')}</span>,
      align: 'center',
      width: '123px',
    },
  ]);
  const hasPackageContent = computed(() => activePackageId.value && activePackage.value);
  // 已选套餐内容
  const selectItems = computed(() => {
    if (hasPackageContent.value) return getPackageItems(activePackage.value.items);
    return [{ itemTypeText: '广场号开通', content: '√' }];
  });

  const getOrderChannel = (): OrderChannel => {
    if (__APP_ENV__.VITE_APP_MAS) return OrderChannel.Mac;
    if (isMacOS) return OrderChannel.MacNonMarket;
    if (isWindows) return OrderChannel.Windows;
  };

  // 获取套餐列表
  const getList = async () => {
    const [err, res] = await to(
      getAnnualFeePackages({
        team_id: props.teamId,
        upgrade_from_level: isUpgrade.value ? upgradeInfo.value?.annualFeeDetail?.package?.level || 0 : undefined,
        order_channel: getOrderChannel(),
      }),
    );
    if (err) return;

    const { defaultFee, packages } = res.data;

    trialPackageId.value = res.data.trialPackageId;
    // 为体验套餐时，可购买套餐
    if (trialPackageId.value) {
      isOpen.value = true;
    }

    annualFeePackages.value = {
      defaultFee,
      packages: packages.map((pkg) => {
        const channel = pkg.displayChannels.find(
          (channel) => channel.channelType === channelType && channel.orderMethod === OrderMethod.OrderDirect,
        );
        return {
          ...pkg,
          // 应用市场内购时使用内购价格
          price: channel?.appStoreProduct?.price || pkg.price,
        };
      }),
    };
    if (res.data.packages?.length) {
      await selectPackage(annualFeePackages.value.packages[0]);
    }
  };

  const agree = ref(false);
  const agreementDialogRef = ref(null);

  const years = ref(1);
  // 订购到期时间
  const defaultExpiredAtFormat = computed(() => {
    const expired = upgradeInfo.value?.annualFeeExpiredAt ? moment(upgradeInfo.value.annualFeeExpiredAt) : moment();
    return expired.add(years.value, 'years').format(DATE_TIME_FORMAT);
  });
  const submitting = ref(false);

  // 支付
  const payParam = ref(null);
  const allPayDialog = ref(false);
  const allPayRef = ref();

  const paymentSuccess = () => {
    visible.value = false;
    // MessagePlugin.success(t('square.buySuccessTip'));
    emit('success');
  };
  const specialistRef = ref(null);
  const specialistParam = ref(null);
  // 下单
  const submit = async () => {
    console.log(submitting.value, 'submitting.valuesubmitting.value');

    if (submitting.value) return;

    // 套餐的每一项内容是否比当前套餐的都高，如果存在一项小于选择的套餐，则弹窗提示
    if (isUpgrade.value && upgradePrice.value?.benefitsCutBack) {
      console.log(11111111111111111111);

      const confirmDia = DialogPlugin.confirm({
        header: t('square.tip'),
        body: t('square.annualFee.tip5'),
        theme: 'info',
        confirmBtn: t('square.action.confirm'),
        cancelBtn: null,
        closeBtn: null,
        closeOnOverlayClick: false,
        onConfirm: async () => {
          confirmDia.destroy();
        },
        onClose: () => {
          confirmDia.hide();
        },
      });
      return;
    }

    // 未勾选协议则弹窗提示
    if (!agree.value && !isActivePkgOrderByKefu.value) {
      needAgree();
      return;
    }
    const param = await getOrderParams();
    // 检验失败
    if (param === false) return;

    // 联系客服 - 咨询单（只有非Mac(应用市场)的包能升级套餐）

    if (isActivePkgOrderByKefu.value) {

      // createConsultOrder(param as AnnualFeeOrderRequest);
      specialistParam.value = param;
      specialistRef.value.openDialog();
      return;
    }

    // 苹果内购校验
    if (channelType === ChannelType.MAC) {
      const productId = activeChannel.value?.appStoreProduct?.productId;
      if (productId) {
        const isCanPay = await allPayRef.value.checkPayInfo({ product_ids: [productId] });
        if (!isCanPay) return;
      }
    }

    // 创建普通订单
    let res: AxiosResponse;
    if (isOpen.value) {
      res = await submitOpenSquare();
    } else {
      res = await submitAnnualFeeOrder();
    }

    // 防止重复提交
    submitting.value = true;
    setTimeout(() => {
      submitting.value = false;
    }, 2000);

    // 零元订单
    if (amount.value === 0) {
      paymentSuccess();
      return;
    }

    const { data } = res as AxiosResponse;
    payParam.value = {
      sn: data.orderNum,
      region: data.regionCode || 0,
      amount: amount.value,
      teamId: props.teamId,
    };

    const isCanPay = await allPayRef.value.checkPayInfo(payParam.value);
    if (!isCanPay) return;

    allPayDialog.value = true;
  };
  const submitDebounce = debounce(submit, 400);
  const imrun = (cardid) => {
    createConsultOrderNew(cardid);
  };
  // 创建咨询单
  const createConsultOrder = async (param: AnnualFeeOrderRequest) => {
    console.log('1111111111111111111111');

    const groupRes = await getOfficialKeFuGroupList({ channel_type: 'package' });
    const group = groupRes.data.data.list[0];
    const main = getOpenid();

    submitting.value = true;
    const [err, res] = await to(
      annualFeeConsult({
        main,
        groupId: String(group.id),
        param,
      }),
    );
    submitting.value = false;
    if (err) return;

    createIMSession(main, group.id, res.data);
  };
  const createConsultOrderNew = async (cardid) => {
    const main = getOpenid();

    submitting.value = true;
    const param = await getOrderParams();
    const [err, res] = await to(annualFeeConsult({
      main,
      // groupId: String(group.id),
      cardId: String(cardid),
      // param: specialistParam.value as AnnualFeeOrderRequest,
      param
    }));
    submitting.value = false;
    if (err) return;

    createIMSession(main, cardid, res.data);
  };
  // 创建会话
  const createIMSession = async (main: string, groupId: number, data: AnnualFeeConsultResponse) => {
    const ids = {
      main: data?.kefuSession?.main,
      peer: data?.kefuSession?.peer,
    };
    const target = {
      conversationType: 1,
      openId: ids?.main,
      cardId: ids?.peer,
      targetId: ids?.peer,
      attachment: {
        creatorCardId: ids?.main,
        relation: 'CONSULT',
        member: [{ cardId: ids?.main }, { cardId: ids?.peer }],
      },
    };

    await ipcRenderer.invoke('im.chat.open', ids);
    ipcRenderer.send('update-nume-index', 0);
    sendApplicationMsg(MsgShareType.consult, { data, type: PackageType.Square }, [target]);
  };

  // 获取下单参数
  const getOrderParams = async () => {
    // 创建开通广场
    if (isOpen.value) {
      const code = openFormData.value.inviteCode;
      const region = teamInfo.value?.regionCode;

      // 校验邀请码
      if (code) {
        const res1 = await checkInviteCode({
          code,
          region,
        });
        const { result, type } = res1.data.data;
        if (!result) {
          // 0 验证码不存在 1 区域不匹配 2未生效
          if (type === 0) {
            formRef.value.setValidateMessage({ inviteCode: [{ type: 'error', message: t('square.annualFee.tip6') }] });
            return Promise.resolve(false);
          }
          if (type === 2) {
            formRef.value.setValidateMessage({ inviteCode: [{ type: 'error', message: t('square.annualFee.tip7') }] });
            return Promise.resolve(false);
          }
          if (type === 1) {
            formRef.value.setValidateMessage({
              inviteCode: [
                {
                  type: 'error',
                  message: `${t('square.annualFee.pleaseInput')}【${region === RegionCode.Mo ? t('square.annualFee.aomen') : t('square.annualFee.dalu')
                    }】${t('square.annualFee.areaInventCode')}`,
                },
              ],
            });
            return Promise.resolve(false);
          }
        }
      }
      formRef.value.clearValidate(['inviteCode']);

      const data: AnnualFeeOrderRequest = {
        amount: {
          currencyCode: currency.value.ISOCode,
          value: amount.value.toFixed(2),
        },
        inviteCode: openFormData.value.inviteCode,
        teamId: props.teamId,
        orderType: OrderType.OpenAnnualFee,
        orderChannel: getOrderChannel(),
      };
      if (activePackageId.value) data.feePackageId = activePackageId.value;
      else data.years = years.value;

      return data;
    }

    // 续费、升级年费套餐
    const data: AnnualFeeOrderRequest = {
      teamId: props.teamId,
      amount: {
        currencyCode: currency.value.ISOCode,
        value: isUpgrade.value ? upgradePrice.value?.price?.value : salePrice.value,
      },
      orderType: isUpgrade.value ? OrderType.UpgradeAnnualFee : OrderType.RenewalAnnualFee,
      orderChannel: getOrderChannel(),
      orderSn: upgradeInfo.value?.annualFeeDetail?.orderSn,
    };
    if (activePackageId.value) data.feePackageId = activePackageId.value;
    else data.years = years.value;

    return data;
  };

  // 续费、升级年费套餐
  const submitAnnualFeeOrder = async () => {
    const data = (await getOrderParams()) as AnnualFeeOrderRequest;
    console.log(data,'datadatadatadata1111111111');

    // 直接下单
    submitting.value = true;
    const [err, res] = await to < AxiosResponse, AxiosError< { reason: ErrorResponseReason; message: string } >> (
      createAnnualFeeOrder(data),
  );

    submitting.value = false;
    if (err) {
      handleAnnualFeeError(err);
      return;
    }
    console.log(res.data,'datadatadatadata1111111111222');

    const _data = props.teamId ? { teamId: props.teamId } : teamInfo.value;
    if (orderTip({ ...res.data, ..._data }, false)) throw new Error('error');
    return res;
  };

  // 创建开通广场
  const submitOpenSquare = async () => {
    const data = (await getOrderParams()) as AnnualFeeOrderRequest;

    submitting.value = true;
    const [err, res] = await to < AxiosResponse, AxiosError< { reason: ErrorResponseReason; message: string } >> (
      createAnnualFeeOrder(data),
  );

    submitting.value = false;
    if (err) {
      handleAnnualFeeError(err);
      return;
    }

    const _data = props.teamId ? { teamId: props.teamId } : teamInfo.value;
    if (orderTip({ ...res.data, ..._data }, false)) throw new Error('error');
    return res;
  };

  const needAgree = () => {
    const confirmDia = DialogPlugin.confirm({
      header: t('square.tip'),
      // @ts-ignore
      body: () => (
        <div>
          {t('square.readAndAgree')}
          <span
            class="t-link t-link--theme-primary t-link--hover-color"
            onClick={() => agreementDialogRef.value.open(AgreementType.OrganizationSquare)}
          >
            {t('square.buyAgreement')}
          </span>
        </div>
      ),
      confirmBtn: t('square.action.agree'),
      cancelBtn: { content: t('square.action.cancel'), theme: 'default', variant: 'outline' },
      closeBtn: null,
      closeOnOverlayClick: false,
      theme: 'info',
      onConfirm: async () => {
        confirmDia.destroy();
        agree.value = true;
        await submit();
      },
      onClose: () => {
        confirmDia.hide();
      },
    });
  };
</script>

<style lang="less">
  .d-square-annual-fee {
    .t-drawer__header {
      border-bottom: 0;
      padding: 0 24px;
    }

    .t-drawer__close-btn {
      right: 24px;
    }

    .t-drawer__body {
      margin-right: 0;
      padding: 0;
      border-radius: 0 0 8px 8px;
      margin-bottom: 110px;
    }

    &.single .t-drawer__body {
      display: flex;
      flex-direction: column;
      height: 100%;
    }
  }
</style>

<style lang="less" scoped>
  .card {
    margin: 0 24px 24px;
    display: flex;
    padding: 16px 24px;
    align-items: flex-start;
    gap: 12px;
    align-self: stretch;
    border-radius: 8px;
    background: var(--bg-kyy-color-bg-deep, #f5f8fe);

    .avatar {
      flex-shrink: 0;
      border-radius: 10px;

      :deep(.t-avatar) {
        border: 1px solid #fff;
        border-radius: 10px;
      }
    }

    .right-content {
      flex: 1;
      width: 0;
    }

    .title {
      font-size: 14px;
      font-weight: 600;
      color: var(--text-kyy-color-text-1, #1a2139);
      margin-bottom: 4px;
      .ellipsis();
    }

    .time {
      color: var(--text-kyy-color-text-3, #828da5);
    }
  }

  .card-content {
    padding: 0 20px 24px 24px;

    .title {
      font-size: 14px;
      //font-weight: 600;
      color: var(--text-kyy-color-text-1, #1a2139);
      margin-bottom: 4px;
    }
  }

  .package-wrap {
    // margin-top: 32px;
    display: flex;
    gap: 0 16px;
    flex-wrap: wrap;

    &::-webkit-scrollbar {
      width: 0;
      height: 0;
    }
  }

  .no-level .package {
    border-radius: var(--select-kyy_radius_select_option, 8px);
    box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.12);

    .name {
      font-size: 16px;
      font-weight: 600;
    }
  }

  .package {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;
    flex-shrink: 0;
    padding: 12px;
    width: 130px;
    height: 120px;
    cursor: pointer;
    margin-bottom: 20px;
    border-radius: 8px;
    border: 1px solid var(--divider-kyy_color_divider_light, #eceff5);

    .tag {
      display: flex;
      align-items: center;
      height: 24px;
      position: absolute;
      top: 0;
      right: 0;
      padding: 2px 4px;
      font-size: 12px;
      border-radius: 8px 8px 8px 0;
      background: var(--error-kyy-color-error-default, #d54941);
      color: #fff;
    }

    &:nth-child(3n + 1) {
      background: linear-gradient(180deg, #d6ebff 0%, rgba(255, 255, 255, 0) 100%);
    }

    &:nth-child(3n + 2) {
      background: linear-gradient(180deg, #c8eff8 0%, rgba(255, 255, 255, 0) 100%);
    }

    &:nth-child(3n + 3) {
      background: linear-gradient(180deg, #e5e8ff 0%, rgba(255, 255, 255, 0) 100%);
    }

    &.active,
    &:hover {
      box-shadow: 0 8px 16px 0 rgba(0, 0, 0, 0.12);
      border: 1px solid var(--brand-kyy-color-brand-default, #4d5eff);
    }

    &.disable {
      opacity: 0.6;
      border-color: rgba(var(--brand-kyy-color-brand-default, #4d5eff), 0.8);
    }

    .icon {
      width: 30px;
      height: 30px;
      margin-bottom: 12px;
    }

    .name {
      color: var(--text-kyy-color-text-1, #1a2139);
      font-size: 14px;
      width: 100%;
      text-align: center;
      line-height: 22px;
      .ellipsis();
    }

    .price {
      color: var(--error-kyy_color_error_default, #d54941);
      text-align: center;
      font-size: 18px;
      font-weight: 600;
      line-height: 26px;
      /* 144.444% */
      width: 100%;
      overflow: hidden;
      z-index: 3;
      // .ellipsis();
    }

    .trial-wrap {
      position: absolute;
      right: 0;
      bottom: 0;
      left: 0;
      height: 16px;

      .text {
        color: #544b49;
        font-size: 10px;
        font-weight: 400;
        line-height: 16px;
        /* 160% */
        z-index: 2;
        position: absolute;
        width: 100%;
        text-align: center;
      }

      .trial-mark {
        position: absolute;
        width: 126px;
        height: 18px;
        left: 50%;
        bottom: 0;
        transform: translateX(-50%);
        z-index: 1;
        border-radius: 8px;
      }
    }
  }

  .package-content {
    border-radius: 8px;
    padding: 0 16px;
    font-size: 14px;
    font-weight: 400;
    color: var(--text-kyy-color-text-1, #1a2139);
    background: var(--bg-kyy-color-bg-deep, #f5f8fe);

    .row {
      display: flex;
      padding: 16px 0;

      &:not(:last-child) {
        border-bottom: 1px solid #e3e6eb;
      }
    }

    .desc {
      font-size: 12px;
      font-weight: 400;
      color: var(--text-kyy-color-text-3, #828da5);
      text-align: right;
      margin-top: 4px;
    }

    // 输入框不可点击操作，只可通过+-操作
    .years-input {
      :deep(.t-input) {
        pointer-events: none;
      }

      :deep(.t-button--variant-outline) {
        padding: 0;
      }
    }
  }

  .card-footer {
    padding: 16px 24px;
    background-color: #fff;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    border-top: 1px solid var(--divider-kyy_color_divider_light, #eceff5);

    :deep(.t-radio__label) {
      vertical-align: text-top;
    }

    .bottom {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .price {
        display: flex;
        color: var(--error-kyy-color-error-default, #d54941);
        font-size: 18px;
        font-weight: 600;
      }

      .origin-price {
        color: var(--text-kyy-color-text-3, #828da5);
        font-size: 12px;
        text-decoration: line-through;
        margin-left: 12px;
      }

      .icon {
        font-size: 20px;
      }
    }
  }

  :deep(.t-table__content) {
    border-radius: var(--select-kyy_radius_select_option, 8px);
    border: 1px solid var(--divider-kyy_color_divider_light, #eceff5) !important;
  }

  :deep(.t-table td) {
    border: 0 !important;
  }

  :deep(.t-table__header th) {
    background: var(--bg-kyy_color_bg_deep, #f5f8fe) !important;
    font-size: 14px;
    line-height: 22px;

    /* 157.143% */
    &:first-child {
      color: var(--text-kyy_color_text_2, #516082) !important;
      font-weight: 400 !important;
    }

    &:last-child {
      color: var(--text-kyy_color_text_1, #1a2139) !important;
      font-weight: 600 !important;
    }
  }
</style>
