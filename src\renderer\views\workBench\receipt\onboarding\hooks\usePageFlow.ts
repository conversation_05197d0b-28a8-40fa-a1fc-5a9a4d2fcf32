import { nextTick } from 'vue';
import { DialogPlugin } from 'tdesign-vue-next';
import { useMerchantStore } from '@renderer/store/modules/merchant';
import LynkerSDK from '@renderer/_jssdk';
import type { StatusType } from '@renderer/api/workBench/merchant.model';

export interface PageFlowDependencies {
  /** 当前步骤 */
  step: any;
  /** 是否重新提交 */
  isResubmit: any;
  /** 流程控制状态数组 */
  flowControl: any;
  /** 是否为分佣模式 */
  isCommission: any;
  /** 设置流程状态方法 */
  setFlowStatus: (stepIdx: number, status: StatusType) => void;
  /** 更新流程状态方法 */
  updateFlowStatus: () => void;
}

/**
 * 页面流程控制 composable
 * 封装页面跳转、步骤控制、表单处理等流程控制逻辑
 */
export function usePageFlow(deps: PageFlowDependencies) {
  const merchantStore = useMerchantStore();

  const {
    step,
    isResubmit,
    flowControl,
    isCommission,
    setFlowStatus,
    updateFlowStatus,
  } = deps;

  /**
   * 步骤控制方法
   */

  // 到指定步骤
  const setStep = (stepIdx: number) => {
    step.value = stepIdx;
    if (flowControl.value[stepIdx]) {
      setFlowStatus(stepIdx, 'pending');
    }
  };

  // 到分账申请页面
  const toSplitApply = () => {
    step.value = 2;
    setFlowStatus(2, 'splitSuccess');
    refreshPage();
  };

  // 返回到表单编辑状态
  const backToFormEdit = (stepIdx: number) => {
    step.value = stepIdx;
    if (flowControl.value[stepIdx]) {
      setFlowStatus(stepIdx, 'startOpen');
    }
  };

  // 到商户实名页面
  const toRealName = () => {
    step.value = 1;
    setFlowStatus(step.value, 'realNamePending');
  };

  // 到商户分账页面
  const toSplitAccount = () => {
    if (isCommission.value) {
      step.value = 1;
      setFlowStatus(1, 'commissionPending');
      // 强制关闭分佣模式下第1步的验证状态显示
      if (flowControl.value[1]) {
        flowControl.value[1].isShowVerifyStatus = false;
      }
    } else {
      step.value = 2;
      setFlowStatus(2, 'pending');
    }
  };

  /**
   * 状态成功跳转方法
   */

  // 到商户入网成功页面
  const toJoinSuccess = () => {
    merchantStore.status.is_open_merchant = '2';
    merchantStore.status.apply_status = '2';

    step.value = 0;
    setFlowStatus(step.value, 'success');
  };

  // 到分佣成功页面
  const toCommissionSuccess = () => {
    step.value = 1;
    setFlowStatus(step.value, 'commissionSuccess');
    refreshPage();
  };

  /**
   * 表单和业务处理方法
   */

  // 处理表单提交
  const formSubmit = async (success: boolean) => {
    await merchantStore.checkExist();

    if (merchantStore.isExist) {
      await merchantStore.getStatus();
      await nextTick();
    }

    if (success) {
      setFlowStatus(step.value, 'needSign');
    } else {
      refreshPage();
    }
  };

  // 商户分账提交
  const splitSubmit = async (status: StatusType) => {
    await merchantStore.getStatus();
    setFlowStatus(step.value, status);
  };

  // 处理重新提交
  const handleResubmit = async () => {
    await merchantStore.getStatus();
    await nextTick();

    isResubmit.value = true;
    step.value = 0;
    setFlowStatus(step.value, 'startOpen');
  };

  /**
   * 页面刷新和状态更新
   */

  // 刷新页面
  const refreshPage = async () => {
    if (merchantStore.isExist) {
      await merchantStore.getStatus();
      await nextTick();
    }

    updateFlowStatus();

    LynkerSDK.workBench.reload();
  };

  /**
   * 交互方法
   */

  // 关闭页面弹确认窗提示
  const handleBeforeClose = (): Promise<boolean> => new Promise((resolve) => {
    // 仅表单页面关闭时提示
    if (step.value !== 0 || flowControl.value[step.value].isShowVerifyStatus) {
      resolve(true);
      return;
    }

    const confirmDia = DialogPlugin.confirm({
      theme: 'info',
      header: '提示',
      body: '确定关闭商户入网页面?',
      confirmBtn: '确定关闭',
      cancelBtn: { content: '取消', theme: 'default', variant: 'outline' },
      onConfirm: async () => {
        confirmDia.hide();
        resolve(true);
      },
      onClose: () => {
        confirmDia.destroy();
        resolve(false);
      },
    });
  });

  /**
   * 组合操作方法
   */

  // 获取上一步操作
  const getPrevStepAction = () => {
    if (isCommission.value) {
      step.value = 0;
      setFlowStatus(0, 'signSuccess');
      if (flowControl.value[0]) {
        flowControl.value[0].isShowVerifyStatus = true;
      }
      return;
    }
    setStep(1);
  };

  return {
    // 步骤控制
    setStep,
    toSplitApply,
    backToFormEdit,
    toRealName,
    toSplitAccount,

    // 状态成功跳转
    toJoinSuccess,
    toCommissionSuccess,

    // 表单和业务处理
    formSubmit,
    splitSubmit,
    handleResubmit,

    // 页面刷新和状态更新
    refreshPage,

    // 交互方法
    handleBeforeClose,

    // 组合操作
    getPrevStepAction,
  };
}
