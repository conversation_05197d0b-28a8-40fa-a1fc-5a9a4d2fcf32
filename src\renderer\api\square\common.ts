import { client_orgRequest as clientOrgReq, squareRequest as request } from '@renderer/utils/apiRequest';
import axios, { AxiosResponse } from 'axios';
import { getServerLang } from '@renderer/utils/auth';
import { SquareEntryResponse } from '@/api/square/models/common';
import { OrganizationCertStatus } from '@/api/square/enums';

const Api = {
  // 获取 ip 地址信息 https://ip.useragentinfo.com/api
  getIPInfo: 'https://ip.useragentinfo.com/json',
  getIDCardSquareInfo: '/v1/idcard_square_info', // 身份卡获取广场信息
  teamCertStatus: (teamId) => `/v1/shared/team/${teamId}/cert_status`,
  // 上报视频因格式问题上传失败的事件
  sharedUploadVideoReportEevent: '/v1/shared/report_event',
  // 上报浏览广告人数
  sharedReportSquareVisitor: '/v1/shared/report_square_visitor',
};

export const getIpInfo = (): Promise<AxiosResponse> => axios.get(Api.getIPInfo);

export enum AgreementType {
  // 另可广场平台规则
  ContentSpecification = 'lkgcCN',
  // 另可组织广场号服务购买协议
  OrganizationSquare = 'Package',
  // 广场展位销售协议
  circleRingkolBooth = 'Boothsales',
  // 官网模板购买协议
  portalTemplate = 'WebsiteTemplate',
  // 数字社群开启协议
  Association = 'LKSQ001',
    // 数字社群开启协议
  ExclusiveName = 'ExclusiveName',
  // 另可店铺入驻协议
  RingkolShop = 'DPFW',
}

// 获取协议
// export const getAgreement = (name: AgreementType) => `${getBaseUrl('square')}/v1/agreement/${name}`;
export const getAgreement = (uuid: AgreementType) => clientOrgReq.get(`/agreement/detail?uuid=${uuid}&lang=${getServerLang()}`);
// export const getAgreement = (uuid: AgreementType) => axios.get(`https://ringkol.com/client/api/agreement/detail?uuid=${uuid}&lang=${getServerLang()}`);

// @ts-ignore
export const getIDCardSquareInfo = (params): Promise<AxiosResponse<SquareEntryResponse>> => request.get(Api.getIDCardSquareInfo, { params, hideMessage: true });

// 获取组织认证状态
export const getTeamCertStatus = (teamId: string): Promise<AxiosResponse<{
  /**
   * 是否有权限发起认证
   */
  certifiable?: boolean;
  certStatus?: OrganizationCertStatus;
}>> => request.get(Api.teamCertStatus(teamId));

// 上报视频因格式问题上传失败的事件
export const sharedUploadVideoReportEevent = (
  name: string,
  labels: Record<string, any>,
) => request.post(Api.sharedUploadVideoReportEevent, { name, labels });

// 上报浏览广告人数
export const sharedReportSquareVisitorEvent = (squareId: string) => request.post(Api.sharedReportSquareVisitor, { squareId });
