import { squareRequest as request } from '@renderer/utils/apiRequest';
import { AxiosResponse } from 'axios';
import { NewsCommentResponse, NewsLikesResponse, NewsStatsResponse } from '@/api/square/models/news';
import { CursorPaginationRequest } from '@/api/square/models/common';

const Api = {
  unreadStats: '/v1/unread/stats',
  newsComments: '/v1/news/comments',
  newsLikes: '/v1/news/likes',
  newsFollows: '/v1/news/follows',
};

// GET 获取消息数量
export const getUnreadStats = (params = {}): Promise<AxiosResponse<NewsStatsResponse>> => request.get(Api.unreadStats, { params });

// 获取评论消息列表
export const getNewsCommentsList = (params: CursorPaginationRequest): Promise<AxiosResponse<NewsCommentResponse>> => request.get(Api.newsComments, { params });

// GET 获取新赞消息列表
// @ts-ignore
export const getNewsLikesList = (params = {}): Promise<AxiosResponse<NewsLikesResponse>> => request.get(Api.newsLikes, { params });

// GET 获取新关注消息列表
export const getNewsFollowsList = (params = {}): Promise<AxiosResponse> => request.get(Api.newsFollows, { params });
