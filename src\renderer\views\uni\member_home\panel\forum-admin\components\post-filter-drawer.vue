<template>
  <t-drawer
    v-model:visible="visible"
    :close-btn="true"
    class="drawerSetForm"
    size="472px"
    :header="t('forum.advancedFilter')"
    @close="closeDrawer"
  >
    <div class="form-boxxx">
      <div class="fitem">
        <div class="title">{{ t("forum.postedAt") }}</div>
        <div class="ctl">
          <t-date-range-picker
            v-model="filterFormData.dateTime"
            :placeholder="[t('forum.startTime'), t('forum.endTime')]"
            style="width: 422px"
            clearable
          />
        </div>
      </div>
    </div>

    <template #footer>
      <div class="foot">
        <div class="btn1" @click="closeDrawer">
          {{ t("forum.cl") }}
        </div>
        <div :class="filterFormDataNotEmpty ? 'btn3' : 'btn2'" @click="submit">
          {{ t("forum.search") }}
        </div>
      </div>
    </template>
  </t-drawer>
</template>

<script setup lang="ts">
import { computed, reactive, ref } from "vue";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

const props = defineProps(["filterList"]);

const emit = defineEmits(["update:filterList"]);

// 抽屉显示状态
const visible = ref(false);

// 高级筛选项表单
const filterFormData = reactive({
  dateTime: [],
});

// 高级筛选表单是否非空
const filterFormDataNotEmpty = computed(() => filterFormData.dateTime.length);

// 打开抽屉
const openDrawer = () => {
  visible.value = true;
  // 用高级筛选项列表回显高级筛选项表单
  for (const item of props.filterList) {
    filterFormData[item.key] = item.value;
  }
};

// 关闭抽屉
const closeDrawer = () => {
  visible.value = false;
  setTimeout(() => {
    filterFormData.dateTime = [];
  }, 200);
};

// 提交高级筛选项表单
const submit = () => {
  const list = Object.entries(filterFormData).reduce((list, [key, value]) => {
    if (key === "dateTime" && value.length) {
      list.push({
        key: "dateTime",
        label: t("square.post.publishTime"),
        value,
        text: `${value[0]}~${value[1]}`,
      });
    }
    return list;
  }, []);
  emit("update:filterList", list);
  closeDrawer();
};

defineExpose({
  openDrawer,
});
</script>

<style lang="less" scoped>
@import "@renderer/views/member/member_home/panel/public.less";

.foot {
  width: 100%;
  display: flex;
  justify-content: end;
  .btn1 {
    display: flex;
    cursor: pointer;
    height: 32px;
    min-width: 80px;
    min-height: 32px;
    max-height: 32px;
    padding: 0 16px;
    justify-content: center;
    align-items: center;
    gap: 4px;
    color: var(
      --color-button-border-kyy-color-button-border-text-default,
      #516082
    );
    text-align: center;
    border-radius: var(--radius-kyy-radius-button-s, 4px);
    border: 1px solid
    var(--color-button-border-kyy-color-button-border-border-dedault, #d5dbe4);
    background: var(
      --color-button-border-kyy-color-button-border-bg-default,
      #fff
    );
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px; /* 157.143% */
    margin-right: 8px;
  }
  .btn2 {
    display: flex;
    cursor: pointer;
    width: 88px;
    height: 32px;
    min-height: 32px;
    max-height: 32px;
    padding: 6px 16px;
    justify-content: center;
    align-items: center;
    gap: 4px;
    border-radius: var(--radius-kyy-radius-button-s, 4px);
    background: var(
      --color-button-primary-kyy-color-button-primary-bg-disabled,
      #c9cfff
    );
    color: var(--color-button-primary-kyy-color-button-primary-text, #fff);
    text-align: center;

    /* kyy_fontSize_2/bold */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px; /* 157.143% */
  }
  .btn3 {
    display: flex;
    cursor: pointer;
    width: 88px;
    height: 32px;
    min-height: 32px;
    max-height: 32px;
    padding: 6px 16px;
    justify-content: center;
    align-items: center;
    gap: 4px;
    border-radius: var(--radius-kyy-radius-button-s, 4px);
    background: var(
      --color-button-primary-kyy-color-button-primary-bg-default,
      #4d5eff
    );

    color: var(--color-button-primary-kyy-color-button-primary-text, #fff);
    text-align: center;
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px;
  }
}
</style>
