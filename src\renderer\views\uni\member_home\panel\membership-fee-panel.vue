<template>
  <div class="container">
    <div class="su-header">
      <div class="more-search">
        <div class="in-box">
          <t-input
            v-model="formData.name"
            :placeholder="'请输入缴费人/单位'"
            :maxlength="50"
            clearable
            style="width: 304px"
            @change="onSearch"
          />
        </div>
        <div v-if="paramsSuper" class="af-icon" @click="showFilter">
          <img
            src="@renderer/assets/approval/icons/fbutton.svg"
            style="width: 32px; height: 32px"
            alt=""
          />
        </div>
        <div v-else class="f-icon" @click="showFilter">
          <img
            src="@renderer/assets/approval/icons/icon_screen.svg"
            style="width: 20px; height: 20px"
            alt=""
          />
        </div>
      </div>
      <div class="opt">
        <t-button theme="primary" variant="base" @click="onAddRecord">
          <template #icon>
            <iconpark-icon name="iconadd" class="iconadd"></iconpark-icon>
          </template>
          添加
        </t-button>
      </div>
    </div>

    <div v-if="false" class="header">
      <h1 class="header-title">会费记录</h1>
      <ul class="header-buttons">
        <li>
          <t-button theme="primary" variant="base" @click="onAddRecord">
            添加</t-button>
        </li>
        <li />
      </ul>
    </div>
    <div class="body">
      <div v-if="paramsSuper" class="filter-res">
        <div class="tit">{{ t("approval.approval_data.sures") }}</div>
        <div v-if="formData.feeTime.length" class="kword te">
          <span>缴费时间：{{ formData.feeTime[0] }}~{{ formData.feeTime[1] }}</span>
          <span class="close2" @click="clearFeeTime">
            <img src="@renderer/assets/approval/icons/close2.svg" alt="" />
          </span>
        </div>
        <div v-if="formData.type" class="ov-time te">
          <span>会费类型： {{ formData.type_text }}</span>
          <span class="close2" @click="clearType">
            <img src="@renderer/assets/approval/icons/close2.svg" alt="" />
          </span>
        </div>

        <div v-if="formData.level" class="ov-time te">
          <span>{{ t("member.svip.member_level") }}：
            {{ formData.level_text }}</span>
          <span class="close2" @click="clearlevel">
            <img src="@renderer/assets/approval/icons/close2.svg" alt="" />
          </span>
        </div>
        <div v-if="formData.createdTime.length" class="kword te">
          <span>创建时间：{{ formData.createdTime[0] }}~{{
            formData.createdTime[1]
          }}</span>
          <span class="close2" @click="clearCreatedTime">
            <img src="@renderer/assets/approval/icons/close2.svg" alt="" />
          </span>
        </div>
        <div class="icon" @click="clearFilters">
          <img src="@renderer/assets/approval/icons/del8.svg" alt="" />
          <a>{{ t("approval.approval_data.clearFilters") }}</a>
        </div>
      </div>
      <div class="body-content">
        <t-form
          v-if="false"
          ref="form"
          :rules="FORM_RULES"
          class="searchForm"
          :data="formData"
          layout="inline"
          :label-align="'right'"
          :colon="false"
        >
          <t-form-item name="name" class="searchForm-item" label-width="84px">
            <template #label>
              <div>缴费人/单位</div>
            </template>
            <t-input
              v-model="formData.name"
              class="searchForm-item-input"
              :maxlength="50"
              clearable
              placeholder="请输入"
              @enter="onEnter"
            />
          </t-form-item>
          <t-form-item
            name="feeTime"
            class="searchForm-item"
            :label-align="'right'"
            label-width="64px"
          >
            <template #label>
              <div>缴费时间</div>
            </template>
            <t-date-range-picker
              v-model="formData.feeTime"
              :placeholder="[
                t('approval.approval_data.start_time'),
                t('approval.approval_data.end_time'),
              ]"
              style="width: 244px"
              class="!w-240"
              clearable
            />
          </t-form-item>
          <t-form-item name="type" class="searchForm-item" label-width="64px">
            <template #label>
              <div>会费类型</div>
            </template>
            <t-select
              v-model="formData.type"
              class="searchForm-item-input"
              :options="optionsFeeType"
              clearable
            ><template #suffixIcon>
              <img src="@/assets/svg/icon_arrow_down.svg" />
            </template>
          </t-select>
          </t-form-item>
          <t-form-item name="level" class="searchForm-item" label-width="84px">
            <template #label>
              <div>会员级别</div>
            </template>
            <t-select
              v-model="formData.level"
              class="searchForm-item-input"
              :options="levelOptions"
              :keys="{ label: 'level_name', value: 'id' }"
              clearable
            ><template #suffixIcon>
              <img src="@/assets/svg/icon_arrow_down.svg" />
            </template>
          </t-select>
          </t-form-item>

          <t-form-item
            name="createdTime"
            class="searchForm-item"
            :label-align="'right'"
            label-width="64px"
          >
            <template #label>
              <div>创建时间</div>
            </template>
            <t-date-range-picker
              v-model="formData.createdTime"
              :placeholder="[
                t('approval.approval_data.start_time'),
                t('approval.approval_data.end_time'),
              ]"
              style="width: 244px"
              class="!w-240"
              clearable
            />
          </t-form-item>

          <div class="searchForm-buttons">
            <t-button theme="primary" variant="base" @click="onSearch">
              搜索</t-button>
            <t-button theme="default" variant="outline" @click="onReset">
              重置</t-button>
          </div>
        </t-form>
        <!-- <div class="tabs">
              <span class="tabs-tab tabs-active cursor">单位会员</span>
              <span class="tabs-tab cursor">个人会员</span>
            </div> -->
        <div class="table">
          <t-table
            row-key="id"
            :columns="memberColumns"
            :pagination="pagination.total > 10 ? pagination : null"
            :data="memberData"
          >
            <!-- <template #main="{ row }">
              <div class="main_body">
                <span class="main_body-item">
                  {{ $filters.isPeriodEmpty(row.team_name) }}
                </span>
                <span class="main_body-item">
                  +{{ $filters.isPeriodEmpty(row.telCode) }}

                  {{ $filters.isPeriodEmpty(row.telephone) }}
                </span>
              </div>
            </template> -->
            <template #level_name="{ row }">
              <div>{{ $filters.isPeriodEmpty(row.level_name) }}</div>
            </template>
            <template #status="{ row }">
              <div :class="showClassStatus(row.status)">
                {{ showTextStatus(row.status) }}
              </div>
            </template>
            <template #type="{ row }">
              <div class="status">{{ showTextType(row.type) }}</div>
            </template>
            <template #pay_status="{ row }">
              <div class="status">{{ showTextFee(row.status) }}</div>
            </template>
            <template #money="{ row }">
              {{ priceDivisorShow(row.money, 100) }}
            </template>

            <!--  -->
            <template #operate="{ row }">
              <span class="operates">
                <t-link
                  theme="primary"
                  hover="color"
                  class="operates-item"
                  @click="onLookDetail(row)"
                >
                  详情
                </t-link>
              </span>
            </template>

            <template #empty>
              <div class="empty">
                <!-- <noData :text="$t('engineer.no_data')" /> -->
                <Empty />

              </div>
            </template>
          </t-table>
        </div>
      </div>
    </div>
  </div>

  <t-drawer
    v-model:visible="filterVisible"
    :close-btn="true"
    size="472px"
    :header="t('approval.approval_data.sur')"
  >
    <div class="form-boxxx">
      <div class="fitem">
        <div class="title">缴费时间</div>
        <div class="ctl">
          <t-date-range-picker
            v-model="paramsTemp.feeTime"
            :placeholder="[
              $t('approval.approval_data.start_time'),
              $t('approval.approval_data.end_time'),
            ]"
            style="width: 422px"
            clearable
          />
        </div>
      </div>
      <div class="fitem">
        <div class="title">会费类型</div>
        <div class="ctl">
          <t-select
            v-model="paramsTemp.type"
            :options="optionsFeeType"
            clearable
            style="width: 422px"
            @change="typeChange"
          ><template #suffixIcon>
            <img src="@/assets/svg/icon_arrow_down.svg" />
          </template>
        </t-select>
        </div>
      </div>
      <div class="fitem">
        <div class="title">{{ t("member.svip.member_level") }}</div>
        <div class="ctl">
          <t-select
            v-model="paramsTemp.level"
            :options="levelOptions"
            clearable
            :keys="{ label: 'level_name', value: 'id' }"
            style="width: 422px"
            @change="levelChange"
          ><template #suffixIcon>
            <img src="@/assets/svg/icon_arrow_down.svg" />
          </template>
        </t-select>
        </div>
      </div>
      <div class="fitem">
        <div class="title">创建时间</div>
        <div class="ctl">
          <t-date-range-picker
            v-model="paramsTemp.createdTime"
            :placeholder="[
              $t('approval.approval_data.start_time'),
              $t('approval.approval_data.end_time'),
            ]"
            style="width: 422px"
            clearable
          />
        </div>
      </div>
    </div>
    <template #footer>
      <div class="foot">
        <div class="btn1" @click="footerCla">
          {{ t("approval.approval_data.cl") }}
        </div>
        <div v-if="paramsSuperFoot" class="btn3" @click="getDataRunDr">
          {{ t("approval.approval_data.cm") }}
        </div>
        <div v-else class="btn2">
          {{ t("approval.approval_data.cm") }}
        </div>
      </div>
    </template>
  </t-drawer>

  <!-- <InviteQrcodeModal ref="inviteQrcodeModalRef" /> -->
  <SetFeeModal
    ref="feeModalRef"
    :level-options="levelOptions"
    @reload="onSearch"
  />
  <LookFeeModal ref="lookFeeModalRef" @reload="onSearch" @on-edit="onEdit" />
</template>

<script lang="ts" setup>
// import noData from "@renderer/components/approvalSatellite/approvalData/noData.vue";
import { ref, reactive, watch, computed } from "vue";
import { useI18n } from "vue-i18n";
import {
  getMemberJobsListAxios,
  getMemberOrderListAxios,
  getMemberApplyLinkAxios,
  getMemberOrderDetailAxios,
} from "@renderer/api/uni/api/businessApi";
import { getResponseResult, priceDivisorShow } from "@renderer/utils/myUtils";
import { MessagePlugin, FormRule } from "tdesign-vue-next";
import SetFeeModal from "@renderer/views/uni/member_home/panel/membership-fee-panel/modal/set-fee-modal.vue";
import LookFeeModal from "@renderer/views/uni/member_home/panel/membership-fee-panel/modal/look-fee-modal.vue";
import { useUniStore } from "@renderer/views/uni/store/uni";
import Empty from "@renderer/components/common/Empty.vue";

const store = useUniStore();
const { t } = useI18n();

const statusOptions = [
  // 申请状态
  //   { label: "全部", value: 0 },
  { label: "待审核", value: 1 },
  { label: "已入会", value: 2 },
  { label: "已驳回", value: 3 },
];
const levelOptions = ref([
  //   {
  //     label: "全部",
  //     value: 0
  //   }
]);
const optionsFeeType = [
  // 会费类型
  { label: "会员入会", value: 1 },
  { label: "会员续期", value: 2 },
  { label: "其他", value: 3 },
];

const FORM_RULES: Record<string, FormRule[]> = {
  // phone: [{ required: false, message: '内容超出', max: 10 }],
};
const formData = reactive({
  name: "", // 缴费人/单位
  // status: undefined, // 申请状态 1：待审核，2：已通过，3：已驳回
  feeTime: [], // 缴费时间开始、结束时间
  createdTime: [], // 创建时间开始、结束时间
  level: undefined, // 会员级别
  type: undefined, // 会费类型，1：会员入会，2：会员续期，3：其他
  level_text: undefined,
  type_text: undefined, // 会费类型，1：会员入会，2：会员续期，3：其他
});
const feeModalRef = ref(null);
const lookFeeModalRef = ref(null);
const form = ref(null);
const isMoreSearch = ref(false);
const memberColumns = ref([
  { colKey: "name", title: "缴费人/单位", width: "20%", ellipsis: true },
  { colKey: "level_name", title: "会员级别", width: "13%", ellipsis: true },
  { colKey: "type", title: "会费类型", width: "12%", ellipsis: true },

  { colKey: "pay_time", title: "缴费时间", width: "10%", ellipsis: false },
  // { colKey: "currency", title: "币种", width: "10%", ellipsis: true },
  { colKey: "money", title: "缴费金额（元）", width: "12%", ellipsis: true },
  //   { colKey: "pay_status", title: "会费状态", width: "10%", ellipsis: true },
  { colKey: "operate", title: "操作", width: "5%", ellipsis: true },
]);
const memberData = ref([]);
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
  onChange: (pageInfo) => {
    console.log("pagination.onChange", pageInfo);
    pagination.current = pageInfo.current;
    pagination.pageSize = pageInfo.pageSize;

    // getMemberList({});
    onSearch();
  },
});
// 会费状态
const showTextFee = (val) => {
  let msg = "";
  // 1：未缴费，2：已缴费，3：无需缴费
  switch (val) {
    case 1:
      msg = "未缴费";
      break;
    case 2:
      msg = "已缴费";
      break;
    case 3:
      msg = "无需缴费";
      break;
    default:
      msg = "--";
  }
  return msg;
};

const showClassStatus = (val) => {
  // 申请状态，1：待审核，2：已入会，3：已驳回
  let result = {};
  if (val === 1) {
    result = { wait: true };
  } else if (val === 2) {
    result = { success: true };
  } else if (val === 3) {
    result = { reject: true };
  }
  return result;
};
const showTextStatus = (val) => {
  const option = statusOptions.find((v: any) => v.value === val);
  if (option) {
    return option.label;
  }
  return "--";
};
// 会费类型
const showTextType = (val) => {
  const option = optionsFeeType.find((v: any) => v.value === val);
  if (option) {
    return option.label;
  }
  return "--";
};

watch(
  () => store.activeAccount,
  (val) => {
    if (val) {
      // updateAllCount();
      onSearch();
    }
  },
  {
    deep: true,
    // immediate: true
  }
);

const setMoreSearch = () => {
  isMoreSearch.value = !isMoreSearch.value;
};

const onReset = () => {
  form.value.reset();
  onSearch();
};
const onSearch = () => {
  const params = {
    ...formData,
    pay_time_start: formData.feeTime.length > 0 ? formData.feeTime[0] : "",
    pay_time_end: formData.feeTime.length > 1 ? formData.feeTime[1] : "",
    created_start:
      formData.createdTime.length > 0 ? formData.createdTime[0] : "",
    created_end: formData.createdTime.length > 1 ? formData.createdTime[1] : "",
  };
  getMemberList(params);
};

// 添加会费记录

const onAddRecord = () => {
  // getInviteLinkAxios().then((val) => {
  //   inviteQrcodeModalRef.value.onOpen(val);
  // });
  console.log(feeModalRef.value);
  feeModalRef.value?.onOpen();
};

// 编辑会费
const onEdit = (data) => {
  feeModalRef.value.onOpen(data);
};

// 获取邀请链接
const getInviteLinkAxios = () => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await getMemberApplyLinkAxios();
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      resolve(result.data.link);
    } catch (error) {
      reject();
      const errMsg = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
    }
  });
};

// 获取入会申请详情
const onGetMemberFeeDetailAxios = (res) => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await getMemberOrderDetailAxios(res.id);
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      resolve(result.data);
    } catch (error) {
      reject();
      const errMsg = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
    }
  });
};
// getMemberApplyDetailAxios

// 获取入会申请列表
const getMemberList = async (params) => {
  // teamId
  params.page = pagination.current;
  params.pageSize = pagination.pageSize;
  try {
    let result = await getMemberOrderListAxios(params);
    console.log(result);
    result = getResponseResult(result);
    if (!result) return;
    memberData.value = result.data.list;
    pagination.total = result.data.total;
    console.log(memberData.value);
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    MessagePlugin.error(errMsg);
  }
};

// 获取会员职务列表
const getMemberJobsList = async () => {
  // teamId
  // params.page = pagination.current;
  // params.pageSize = pagination.pageSize;
  try {
    let result: any = await getMemberJobsListAxios();
    console.log(result);
    result = getResponseResult(result);
    if (!result) return;

    // levelOptions.value = [
    //   ...result.data.map((v) => ({
    //     label: v.level_name,
    //     value: v.id
    //   }))
    // ];
    levelOptions.value = result.data
      ? result.data.map((v: any) => {
          v.money /= 100;
          return v;
        })
      : [];
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    MessagePlugin.error(errMsg);
  }
};

const initData = () => {
  getMemberJobsList();
  getMemberList({});
};
initData();

const onLookDetail = (row: any) => {
  onGetMemberFeeDetailAxios(row).then((res: any) => {
    lookFeeModalRef.value.onOpen(res);
  });
};

const onEnter = () => {};
const filterVisible = ref(false);
const paramsSuper = computed(
  () =>
    formData.feeTime.length ||
    formData.createdTime.length ||
    formData.level ||
    formData.type
);
const paramsSuperFoot = computed(
  () =>
    paramsTemp.value.level ||
    paramsTemp.value.type ||
    paramsTemp.value.feeTime.length ||
    paramsTemp.value.createdTime.length
);
const showFilter = () => {
  filterVisible.value = true;
};
const paramsTemp = ref({
  level: undefined,
  level_text: undefined,
  type: undefined,
  type_text: undefined,
  feeTime: [],
  createdTime: [],
});
const typeChange = (e, ctx) => {
  console.log(e, ctx);
  paramsTemp.value.type_text = ctx.option.label;
};
const levelChange = (e, ctx) => {
  console.log(e, ctx);
  paramsTemp.value.level_text = ctx.option.label;
};
const clearFilters = () => {
  formData.level = undefined;
  formData.type = undefined;
  formData.name = undefined;
  formData.feeTime = [];
  formData.createdTime = [];
  paramsTemp.value.feeTime = [];
  paramsTemp.value.createdTime = [];
  paramsTemp.value.type = undefined;
  paramsTemp.value.level = undefined;
  onSearch();
};

const clearType = () => {
  formData.type = undefined;
  paramsTemp.value.type = undefined;
  onSearch();
};
const clearlevel = () => {
  formData.level = undefined;
  paramsTemp.value.level = undefined;
  onSearch();
};
const clearFeeTime = () => {
  formData.feeTime = [];
  paramsTemp.value.feeTime = [];
  onSearch();
};
const clearCreatedTime = () => {
  formData.createdTime = [];
  paramsTemp.value.createdTime = [];
  onSearch();
};
const getDataRunDr = () => {
  filterVisible.value = false;
  formData.level = paramsTemp.value.level;
  formData.type = paramsTemp.value.type;
  formData.createdTime = paramsTemp.value.createdTime;
  formData.feeTime = paramsTemp.value.feeTime;
  formData.feeTime = paramsTemp.value.feeTime;
  formData.level_text = paramsTemp.value.level_text;
  formData.type_text = paramsTemp.value.type_text;
  onSearch();
};
const footerCla = () => {
  filterVisible.value = false;
  paramsTemp.value.level = undefined;
  paramsTemp.value.createdTime = [];
  paramsTemp.value.type = undefined;
  paramsTemp.value.feeTime = [];
};
</script>

<style lang="less" scoped>
@import "@renderer/views/uni/member_home/panel/public.less";


.iconadd {
  color: #ffffff;
  font-size: 24px;
}
.main_body {
  display: flex;
  flex-direction: column;
}
</style>
