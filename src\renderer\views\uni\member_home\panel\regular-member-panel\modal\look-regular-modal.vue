<template>
  <t-drawer
    v-model:visible="visible"
    class="drawerSet"
    header="详情"
    :z-index="1500"
    :on-confirm="onClickConfirm"
    :close-btn="true"
    :size="'376px'"
  >
    <template #closeBtn>

      <iconpark-icon name="iconerror" style="font-size: 24px" class="iconerror"></iconpark-icon>
    </template>
    <template #footer>
      <div v-if="data && data.status !== 3" class="operates">
        <!-- <t-button
          v-show="data.activate === 2"
          theme="default"
          variant="outline"
          class="operates-item"
          @click="onInviteJobClub(data)"
        >邀请激活</t-button> -->
        <!-- <t-button
          v-show="data.no_expire !== 1"
          theme="default"
          variant="outline"
          class="operates-item"
          @click="onMemberRenewal(data)"
        >会员续期</t-button> -->

        <t-button
          theme="primary"
          class="operates-item"
          @click="onEditMember"
        >编辑</t-button>
      </div>
    </template>

    <div v-if="data && data.submit_data && visible" class="drawerSet-body">
      <div v-if="data.status === 3" class="system">
        <div class="detail-control" style="width: 100%">
          <div class="lable"><span class="line" /> 退出信息 </div>
        </div>
        <div class="detail-control" :style="{ width: '50%' }">
          <div class="subLable">退出时间</div>
          <div class="value">
            {{ $filters.isPeriodEmpty(data.exit_time? dayjs(data.exit_time).format('YYYY-MM-DD') : '') }}
          </div>
        </div>
        <div class="detail-control" :style="{ width: '50%' }">
          <div class="subLable">退出原因</div>
          <div class="value">
            {{ $filters.isPeriodEmpty(data.content) }}
          </div>
        </div>
      </div>
      <form-detail
        ref="runtimeRef"
        :widgets="data.submit_data.free_form"
        @release="releaseRun"
      />

      <div class="system">
        <div class="detail-control" style="width: 100%">
          <div class="lable"><span class="line" />{{$t('member.bing.f')}}</div>
        </div>
        <div class="detail-control" :style="{ width: '50%' }">
          <div class="subLable">{{$t('member.bing.g')}}</div>
          <div class="value">
            {{ data.activate === 1 ? "已激活" : "未激活" }}
          </div>
        </div>
        <div class="detail-control" :style="{ width: '50%' }">
          <div class="subLable">组织状态</div>
          <div class="value">
            <div v-if="data.status === 1" class="success">正常</div>
            <div v-else-if="data.status === 2" class="reject">已到期</div>
            <div v-else-if="data.status === 3" class="reject">已退出</div>
          </div>
        </div>
        <div class="detail-control" :style="{ width: '50%' }" v-show="data?.type === 1">
          <div class="subLable">连接状态</div>
          <div class="value">
            <div v-if="data?.is_connect" class="connected">已连接</div>
            <div v-else class="unconnected">未连接</div>
          </div>
        </div>
        <div class="detail-control" :style="{ width: '50%' }">
          <div class="subLable">创建时间</div>
          <div class="value">
            {{ $filters.isPeriodEmpty(data.created_at) }}
          </div>
        </div>
        <div class="detail-control" :style="{ width: '50%' }">
          <div class="subLable">更新时间</div>
          <div class="value">
            {{ $filters.isPeriodEmpty(data.updated_at) }}
          </div>
        </div>
        <div class="detail-control" :style="{ width: '50%' }">
          <div class="subLable">更新人</div>
          <div class="value">
            {{ $filters.isPeriodEmpty(data.operator_name) }}
          </div>
        </div>
      </div>
    </div>
  </t-drawer>
  <InviteQrcodeModal
    ref="inviteQrcodeModalRef"
    :header-text="$t('member.regular.invite_active')"
    :down-load-text="$t('member.regular.click_down_qrcode')"
    :tip-text="$t('member.regular.share_qrcode_to_join')"
    :copy-link-text="'点击复制激活链接'"
  />
  <MembershipRenewalModal
    ref="membershipRenewalModalRef"
    @on-send="onSaveMemberRenewal"
  />

  <AddMemberModal
    ref="addMemberModalRef"
    :is-hidden-arr="isHiddenArr"
    @reload="onSearch"
    @on-show-member-flow="onShowMemberFlow"
  />
  <AddInMemberModal ref="addInMemberModalRef" />

  <!-- <RejectModal ref="rejectModalRef" @on-send="onSaveReject" />
  <SuccessModal ref="successModalRef" @on-send="onSaveSuccess" /> -->
</template>

<script setup lang="ts">
import { ref, Ref, reactive, watch, toRaw, computed } from "vue";
import { MessagePlugin } from "tdesign-vue-next";
import formDetail from "@renderer/components/free-from/detail/index.vue";
import {
  getMemberSettingAxios,
  postMemberApplyRejectAxios,
  postMemberApplyAgreeAxios,
  getRegularLinkAxios,
  getRegularDetailAxios,
  renewalRegularAxios
} from "@renderer/api/uni/api/businessApi";
import { formDiff } from "@renderer/components/free-from/utils";
import memberConst from "@renderer/components/free-from/design/constants/associationConst";
import lodash from "lodash";
import InviteQrcodeModal from "@renderer/views/uni/member_home/panel/apply-member-panel/modal/invite-qrcode-modal.vue";
import MembershipRenewalModal from "@renderer/views/uni/member_home/panel/regular-member-panel/modal/membership-renewal-modal.vue";
import AddMemberModal from "@renderer/views/uni/member_home/panel/regular-member-panel/modal/add-member-modal.vue";
import AddInMemberModal from "@renderer/views/uni/member_home/panel/regular-member-panel/modal/add-in-member-modal.vue";
import { getResponseResult } from "@/utils/myUtils";
import dayjs from "dayjs";
import { getUniTeamID } from "@renderer/views/uni/utils/auth";
import { useRoute } from "vue-router";
import { platform } from "@renderer/views/digital-platform/utils/constant";
import { useDigitalPlatformStore } from "@renderer/views/digital-platform/store/digital-platform-store";
import { useUniStore } from "@renderer/views/uni/store/uni";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
// import RejectModal from "./reject-modal.vue";
// import SuccessModal from "./success-modal.vue";

// import { ClientSide } from "@renderer/types/enumer";

// 运行时
const controls = ref([]);
const runtimeRef = ref(null);

const organizeSelectCompRef = ref(null);
const digitalPlatformStore = useDigitalPlatformStore();

const props = defineProps({
  platform: {
    type: String,
    default: '',
  },
})
const store = useUniStore();
const route = useRoute();
// 平台类型 目前只有digital-platform
const platformCpt = computed(() => {
  return props.platform || route.query?.platform
})

const activeAccount = computed(() => {
  if(platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount
  } else if(platformCpt.value === platform.digitalWorkbench)  {
    return route.query;
  } else {
    return store.activeAccount
  }
})


const currentTeamId = computed(() => {
  if(platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount?.teamId
  } else if(platformCpt.value === platform.digitalWorkbench){
    return route.query?.teamId || 0;
  } else {
    return  getUniTeamID()
  }
})



const visible = ref(false);
let origin_data = null; // 用于内部全局
const data = ref(null);
const emits = defineEmits(["reload", "updateDetail"]);

watch(
  () => visible.value,
  (cur) => {}
);

const releaseRun = (data) => {
  console.log(data);
};

// 会员续期
const membershipRenewalModalRef = ref(null);
const onMemberRenewal = (row) => {
  onGetRegularDetailAxios(row).then((res) => {
    console.log(res);
    membershipRenewalModalRef.value.onOpen(res);
  });
  row.popMoreconfirmVisible = false;
};

const onGetRegularDetailAxios = async (row) => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await getRegularDetailAxios(row.id, {}, currentTeamId.value);
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      resolve(result.data);
    } catch (error) {
      const errMsg = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
      reject();
    }
  });
};

const onSaveMemberRenewal = async (params) => {
  console.log(params);
  let result = null;
  // eslint-disable-next-line no-async-promise-executor

  try {
    result = await renewalRegularAxios(params);
    result = getResponseResult(result);
    if (!result) {
      return;
    }
    MessagePlugin.success("操作成功");
    membershipRenewalModalRef.value.onClose();
    // onSearch();
    console.log(deepItemData);
    // emits("updateDetail", { id: data.value.id });
    onClose();
    emits("reload");
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    MessagePlugin.error(errMsg);
  }
};

const isHiddenArr = ref([]);
const addMemberModalRef = ref(null);

// 编辑会员
const onEditMember = () => {
  isHiddenArr.value = ["relateRespector"];
  // onGetRegularDetailAxios(row).then((res) => {
  //   addMemberModalRef.value.onOpen(res);
  // });
  // addMemberModalRef.value.onOpen(toRaw(data.value));
  onGetRegularDetailAxios(toRaw(data.value)).then((res) => {
    addMemberModalRef.value.onOpen(res);
  });
};


const addInMemberModalRef = ref(null);
const onShowMemberFlow = () => {
  console.log('点击了吗');
  addInMemberModalRef.value?.onOpen();
};


// 自动构造自定义表单
const onInitConstructFreeForm = (origins: any) => {
  if (origins && origins.submit_data && origins.submit_data.free_form) {
    // show: true
    origins.submit_data.free_form.map((fr) => (fr.show = true));
    const baseList = origins.submit_data.free_form.filter(
      (v: any) => v.type === "BaseInfoPolitics"
    );
    console.log(baseList);
    baseList.map((v: any) => {
      v.origin.map((vo) => {
        vo.isShow = true;
      });
      // 会员级别的设置
      let origin: any = null;
      origin = v.origin.find((or: any) => or.vModel === "memberLevel");
      if (origin) {
        // const levelItem = origin.options.find(
        //   (v: any) => v.id === origin.value
        // );
        origin.value = origin_data.level_name;
      }

      // 组织名称
      origin = v.origin.find((or: any) => or.vModel === "organizeName");
      if (origin) {
        origin.value = origin_data.data?.team_name;
      }

      // avatar头像
      origin = v.origin.find((or: any) => or.vModel === "logo");
      if (origin) origin.value = origin_data.avatar;

      // 手机
      origin = v.origin.find((or: any) => or.vModel === "phone");
      if (origin) {
        origin.code_value = origin.code_value
          ? Number(origin.code_value)
          : Number(origin_data.telCode);
        origin.value = origin_data.telephone;
      }

      // 会员编号
      origin = v.origin.find((or: any) => or.vModel === "memberNum");
      if (origin) {
        origin.value = origin_data.data?.no;
      }

      // 推荐人
      origin = v.origin.find((or: any) => or.vModel === "reference");
      if (origin) {
        origin.value = origin_data.data?.referrer;
      }

      origin = v.origin.find((or: any) => or.vModel === "referenceUnit");
      if (origin) {
        origin.value = origin_data.data?.referrer_unit;
      }

      // 代表人姓名
      origin = v.origin.find((or: any) => or.vModel === "name");
      if (origin) origin.value = origin_data.name;

      // 到期时间
      origin = v.origin.find((or: any) => or.vModel === "expireTime");
      if (origin) {
        origin.value = origin_data.expire_time;
        origin.is_expire_value = !!origin_data.no_expire;
      }

      // 入会时间
      origin = v.origin.find((or: any) => or.vModel === "joinTime");
      if (origin) origin.value = origin_data.join_time;

      // 邮箱
      origin = v.origin.find((or: any) => or.vModel === "email");
      if (origin) origin.value = origins.submit_data.email;



      // 兴趣爱好
      origin = v.origin.find((or: any) => or.vModel === "interest");
      if (origin) {
        origin.value = origins?.hobby || origins?.submit_data?.hobby;
      }


      // 部门
      origin = v.origin.find((or: any) => or.vModel === "department");
      if (origin) origin.value = origin_data.departments;

      // 组织logo
      //   origin = v.origin.find((or: any) => or.vModel === "organizeLogo");
      //   if (origin && origin.value && origin.value.length > 0) {
      //     origin.value = origin.value[0].file_name;
      //   }

      // 组织类型
      origin = v.origin.find((or: any) => or.vModel === "organizeType");
      if (origin) {
      }

      // 所在行业
      origin = v.origin.find((or: any) => or.vModel === "industryType");
      if (origin) {
        // origin.value = origins.submit_data.industry_text;
      }

      // 组织规模
      origin = v.origin.find((or: any) => or.vModel === "organizeScale");
      if (origin) {
        // origin.value = origins.submit_data.size_text;
      }
      // 组织地址
      origin = v.origin.find((or: any) => or.vModel === "organizeAddress");
      if (origin) {
        // origin.value =
        //   origins.submit_data.country +
        //   origins.submit_data.province +
        //   origins.submit_data.city +
        //   origins.submit_data.district;
      }
      return v;
    });
  }
};

// 通过自定义表单添加的时候，使用该方法
const onInitFreeForm = (origins: any) => {
  if (origins && origins.submit_data && origins.submit_data.free_form) {
    origins.submit_data.free_form.map((fr) => (fr.show = true));
    const baseList = origins.submit_data.free_form.filter(
      (v: any) => v.type === "BaseInfoPolitics"
    );
    console.log(baseList);
    baseList.map((v: any) => {
      v.origin.map((vo) => {
        vo.isShow = true;
        return vo;
      });
      // 会员级别的设置
      let origin: any = null;
      origin = v.origin.find((or: any) => or.vModel === "memberLevel");
      if (origin) {
        // const levelItem = origin.options?.find(
        //   (v: any) => v.id === origin.value
        // );
        // origin.value = levelItem ? levelItem.level_name : "";
        origin.value = origins?.level_name || origins.submit_data?.level_name;
      }
      console.log(origin);
      // 组织名称
      origin = v.origin.find((or: any) => or.vModel === "organizeName");
      if (origin && origin.value) {
        // origin.value = origin.value.teamFullName;
        origin.value = origins?.team_name  || origins?.submit_data?.team_name  || origin.value  ;
      }

      // 组织简称
      origin = v.origin.find((or: any) => or.vModel === "organizeAbbrName");
      if (origin) {
        // origin.options = optionsOrganizeType;
        origin.value = origins?.team_short_name  || origins?.submit_data?.team_short_name  || origin.value  ;
      }


      // 组织LOGO
      origin = v.origin.find((or: any) => or.vModel === "organizeLogo");
      if (origin) {

        const organizeLogoSrc = origins?.team_logo  || origins?.submit_data?.team_logo ;

        if (organizeLogoSrc) {
          const logoRes: any = /\.([0-9a-z]+)(?:[\?#]|$)/i.exec(organizeLogoSrc);
          const origin_name = organizeLogoSrc ? organizeLogoSrc.substring(organizeLogoSrc.lastIndexOf('/') + 1) : '';
          const ck = [
            {
              file_name: organizeLogoSrc,
              file_name_short: origin_name,
              original_name: origin_name,
              size: 0,
              type: logoRes?.length > 1 ? logoRes[1] : '',
            },
          ];
          console.log(ck);
          origin.value = ck;
        } else {
          origin.value = origin.value ? origin.value : [];
        }
      }

      // 邮箱
      origin = v.origin.find((or: any) => or.vModel === "email");
      if (origin) {
        origin.value = origins?.email;
      }


      // 兴趣爱好
      origin = v.origin.find((or: any) => or.vModel === "interest");
      if (origin) {
        origin.value = origins?.hobby || origins?.submit_data?.hobby;
      }

      // 手机号
      origin = v.origin.find((or: any) => or.vModel === "phone");
      if (origin) {
        origin.value = origins?.telephone || origins?.submit_data?.telephone;
      }

      origin = v.origin.find((or: any) => or.vModel === "department");
      if (origin) {
        // origin.value = origins.submit_data.department_name;
        console.log(origins?.departments, "感动东");
        origin.name = "部门";
        origin.value = origins?.departments;
      }

      // 姓名/代表人
      origin = v.origin.find((or: any) => or.vModel === "name");
      if (origin) {
        // origin.disabled = false;
        // origin.value = applyData.value?.name || origin.value;
        origin.value = origins?.name;
      }


      // 所在岗位
      origin = v.origin.find((or: any) => or.vModel === "unitJob");
      if (origin) {
        origin.value = origins?.job || origins?.submit_data?.job  ;
      }

      // console.log(origins.telCode)

      // 姓名/代表人
      origin = v.origin.find((or: any) => or.vModel === "logo");
      if (origin) {
        // origin.disabled = false;
        // origin.value = applyData.value?.name || origin.value;
        origin.value = origins?.avatar ? [{
          file_name: origins?.avatar
        }] : [];
      }

      // 入会时间
      origin = v.origin.find((or: any) => or.vModel === "joinTime");
      if (origin) origin.value = origin_data.join_time;

      // 到期时间
      origin = v.origin.find((or: any) => or.vModel === "expireTime");
      if (origin) {
        origin.value = origin_data.expire_time;
        origin.is_expire_value = !!origin_data.no_expire;
      }



      // 业务范围，
      origin = v.origin.find((or: any) => or.vModel === "business");
      if (origin) {
        origin.value = origins?.business || origins?.submit_data.business || origin.value  ;
      }

      // avatar头像
      origin = v.origin.find((or: any) => or.vModel === "logo");
      if (origin && origin.value && origin.value.length > 0) {
        // origin.value = origin.value[0].file_name;
      }

      // 组织logo
      //   origin = v.origin.find((or: any) => or.vModel === "organizeLogo");
      //   if (origin && origin.value && origin.value.length > 0) {
      //     origin.value = origin.value[0].file_name;
      //   }

      // 组织类型
      origin = v.origin.find((or: any) => or.vModel === "organizeType");
      if (origin) {
        const typeKey = origins?.team_type  || origins?.submit_data.team_type  || origin.value  ;

        const item = origin.options?.find((v: any) => v.value === typeKey);
        origin.value = item ? item.label : "";
      }

      // 所在行业
      origin = v.origin.find((or: any) => or.vModel === "industryType");
      if (origin) {
        origin.value = origins?.industry_text || origins?.submit_data?.industry_text || origin.value  ;
      }

      // 组织规模
      origin = v.origin.find((or: any) => or.vModel === "organizeScale");
      if (origin) {
        origin.value = origins.size_text || origins.submit_data.size_text || origin.value;
      }
      // 组织地址
      origin = v.origin.find((or: any) => or.vModel === "organizeAddress");
      if (origin) {
        origin.value =
          origins.submit_data.country +
          origins.submit_data.province +
          origins.submit_data.city +
          origins.submit_data.district;
        origin.detail = origins.submit_data.address;
      }
      return v;
    });

    // controls.value = origins.submit_data.free_form;
  }
  //   else {
  //     controls.value = [];
  //   }
};

// 邀请激活
const inviteQrcodeModalRef = ref(null);
const onInviteJobClub = (row: any) => {
  getRegularLinkAxios({ id: row.id }, currentTeamId.value).then((val) => {
    console.log(val);
    inviteQrcodeModalRef.value.onOpen(
      val.data.data.link,
      "/account/jump?to=associationActiveInvite"
    );
  });
  row.popMoreconfirmVisible = false;
};

// 驳回
const rejectModalRef = ref(null);
const onReject = () => {
  rejectModalRef.value.onOpen({ id: data.value.id });
};
const onSaveReject = async (val) => {
  let result = null;
  try {
    result = await postMemberApplyRejectAxios({
      id: val.id,
      content: val.area
    });
    result = getResponseResult(result);
    if (!result) return;
    MessagePlugin.success("拒绝成功");
    rejectModalRef.value.onClose();
    onClose();
    emits("reload");
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    MessagePlugin.error(errMsg);
  }
};

const onSearch = () => {
  onClose();
  emits("reload");
};
// 通过

const onGetMemberSetting = async () => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await getMemberSettingAxios();
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      resolve(result.data);
    } catch (error) {
      const errMsg = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
      reject();
    }
  });
};

const successModalRef = ref(null);

const onSuccess = () => {
  onGetMemberSetting().then((res) => {
    successModalRef.value.onOpen(data.value, res);
  });
};
const onSaveSuccess = async (val: any) => {
  let result = null;
  try {
    result = await postMemberApplyAgreeAxios(val);
    result = getResponseResult(result);
    if (!result) return;
    MessagePlugin.success("审核成功");
    successModalRef.value.onClose();
    onClose();
    emits("reload");
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    MessagePlugin.error(errMsg);
  }
};

const onClickConfirm = async () => {};

// 2023-8-3 lss 这里做一下处理，如果用户未设置自由表单，则读取默认的
const initFreeForm = (res) => {
  console.log(res);
  // eslint-disable-next-line no-empty
  if (res.personal_form && res.personal_form.length > 0) {
  } else {
    const menuList = memberConst.filter((v) => v.fromType === "person");
    // 给系统默认
    res.personal_form = lodash.cloneDeep(menuList);
  }
  // eslint-disable-next-line no-empty
  if (res.team_form && res.team_form.length > 0) {
  } else {
    const menuList = memberConst.filter((v) => v.fromType === "unit");
    // 给系统默认
    res.team_form = lodash.cloneDeep(menuList);
  }
};
let deepItemData = [];
const onOpen = (item?: any) => {
  console.log(item)
  deepItemData = lodash.cloneDeep(item);
  controls.value = [];
  // data.value = null;
  // 0 是详情信息， 1是系统设置信息
  // onInitFreeForm(item);
  origin_data = item[0];
  // 这里做一下处理，如果用户未设置自由表单，则读取默认的

  initFreeForm(item[1]);

  let subData = null;
  if (item[0].type === 1) {
    // 单位
    subData = item[1].team_form;
  } else {
    subData = item[1].personal_form;
  }
  // 以系统表单设计为主
  console.log('晕死')

  if (item[0].submit_data && item[0].submit_data.free_form) {
    item[0].submit_data.free_form = formDiff(
      item[0].submit_data.free_form,
      subData
    );
    console.log(item[0]);
    console.log('到这里了吗')
    // return;
    // return;
    onInitFreeForm(item[0]);
    console.log(item[0]);
  } else if (item[0].submit_data) {
    item[0].submit_data = { ...item[0].submit_data, free_form: subData };
    onInitConstructFreeForm(item[0]);
  }
  data.value = item[0];


  // 特殊处理一下名录照片赋值问题2024-09-12 start-------------------------------
  // if(  data.value?.submit_data?.free_form?.length > 0) {
  //   const freeForm =   data.value?.submit_data?.free_form;
  //   const baseList = freeForm.filter((v:any)=> {
  //     return ['BaseInfoPolitics'].includes(v.type)
  //   })
  //   console.log(baseList)
  //   baseList.map(async (v: any) => {
  //     // 会员级别的设置
  //     let origin = null;
  //     const origins_arr = v.origin;
  //     origin = origins_arr.find((or: any) => or.vModel === "nameLogo");
  //     if (origin) {
  //       console.log('拉拉', origin.value)
  //       origin.value =    data.value?.submit_data?.directory_image_values  ||  origin.value  || [];
  //       // console.log(detailItem?.submit_data?.directory_image_values)
  //     }
  //     // console.log(origin);
  //   })
  // }



  console.log(data.value);
  visible.value = true;
  //   visible.value = true;
};
const onClose = () => {
  visible.value = false;
};

defineExpose({
  onOpen,
  onClose
});
</script>
<style lang="less" scoped>
@import url("@renderer/views/uni/member_home/panel/public.less");

:deep(.t-steps--vertical).t-steps--dot-anchor
  .t-steps-item--finish
  .t-steps-item__icon {
  border-color: #c7c7c8;
  background: #c7c7c8;
}

:deep(.t-steps-item--finish) {
  &::before {
    border-right-color: #c7c7c8 !important;
    color: #c7c7c8 !important;
    left: 2.5px;
    top: 24px;
  }
}

.sports {
  &-title {
    font-size: 14px;

    font-weight: 400;

    color: #717376;
  }
}
.toTitle {
  font-size: 14px;

  font-weight: 400;

  // color: #13161b;
}
.toContent {
  font-size: 14px;

  font-weight: 400;
}
.operates {
  display: flex;
  justify-content: flex-end;
  &-item {
    height: 32px;
    min-width: 80px;
  }
}
.drawerSet {
  // width: 720px;
  &-body {
  }
  //   .t-drawer__content-wrapper {
  //     width: 720px !important;
  //   }
}

:deep(.t-drawer__header) {
  border-bottom: 0;
  color: red;
}
:deep(.t-drawer__body) {
  padding-top: 10px !important;
}
</style>
