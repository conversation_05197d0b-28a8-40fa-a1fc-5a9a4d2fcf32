<template>
  <div class="container">
    <div class="su-header">
      <div class="more-search">
        <div class="in-box">
          <t-input
            v-model="formData.team_name"
            :placeholder="$t('member.winter.search_organize_name')"
            :maxlength="50"
            clearable
            style="width: 304px"
            @change="onSearch"
          />
        </div>
        <div v-if="paramsSuper" class="af-icon" @click="showFilter">
          <img
            src="@renderer/assets/approval/icons/fbutton.svg"
            style="width: 32px; height: 32px"
            alt=""
          />
        </div>
        <div v-else class="f-icon" @click="showFilter">
          <img
            src="@renderer/assets/approval/icons/icon_screen.svg"
            style="width: 20px; height: 20px"
            alt=""
          />
        </div>
      </div>
    </div>
    <div class="body">
      <div v-if="paramsSuper" class="filter-res">
        <div class="tit">{{ t("approval.approval_data.sures") }}</div>
        <div v-if="formData.status" class="stat te">
          <span> {{ $t('member.winter.apply_status') }}： {{ formData.status_text }}</span>
          <span class="close2" @click="clearStatus">
            <iconpark-icon class="closeIcon" name="iconerror"></iconpark-icon>
          </span>
        </div>
        <div v-if="formData.dateTime.length" class="kword te">
          <span>{{ $t('member.winter.apply_time') }}：{{ formData.dateTime[0] }}~{{
            formData.dateTime[1]
          }}</span>
          <span class="close2" @click="clearDateTime">
            <iconpark-icon class="closeIcon" name="iconerror"></iconpark-icon>
          </span>
        </div>
        <div v-if="formData.level" class="ov-time te">
          <span>{{ t("member.svip.member_level") }}：
            {{ formData.level_text }}</span>
          <span class="close2" @click="clearlevel">
            <iconpark-icon class="closeIcon" name="iconerror"></iconpark-icon>
          </span>
        </div>
        <div v-if="formData.telephone" class="ov-time te">
          <span>{{ t("member.regular.phone") }}： {{ formData.telephone }}</span>
          <span class="close2" @click="clearFiltertelephone">
            <iconpark-icon class="closeIcon" name="iconerror"></iconpark-icon>
          </span>
        </div>
        <div v-if="formData.type" class="ov-time te">
          <span>入会类型： {{ showTextType(formData.type) }}</span>
          <span class="close2" @click="clearType">
            <iconpark-icon class="closeIcon" name="iconerror"></iconpark-icon>
          </span>
        </div>
        <div v-if="formData.name" class="ov-time te">
          <span>{{ t("member.regular.respect") }}： {{ formData.name }}</span>
          <span class="close2" @click="clearName">
            <iconpark-icon class="closeIcon" name="iconerror"></iconpark-icon>
          </span>
        </div>
        <div class="icon" @click="clearFilters">
          <img src="@renderer/assets/approval/icons/del8.svg" alt="" />
          <a>{{ t("approval.approval_data.clearFilters") }}</a>
        </div>
      </div>
      <div class="body-content">
        <t-form
          v-if="false"
          ref="form"
          :rules="FORM_RULES"
          class="searchForm"
          :data="formData"
          layout="inline"
          :label-align="'right'"
          :colon="false"
        >
          <t-form-item
            name="team_name"
            class="searchForm-item"
            label-width="64px"
          >
            <template #label>
              <div>会员名称</div>
            </template>
            <t-input
              v-model="formData.team_name"
              class="searchForm-item-input"
              :maxlength="50"
              clearable
              placeholder="请输入姓名"
              @enter="onEnter"
            />
          </t-form-item>
          <t-form-item name="status" class="searchForm-item" label-width="64px">
            <template #label>
              <div>申请状态</div>
            </template>
            <t-select
              v-model="formData.status"
              class="searchForm-item-input"
              :options="statusOptions"
              clearable
            ><template #suffixIcon>
              <img src="@/assets/svg/icon_arrow_down.svg" />
            </template>
          </t-select>
          </t-form-item>
          <t-form-item
            name="dateTime"
            class="searchForm-item"
            :label-align="'right'"
            label-width="64px"
          >
            <template #label>
              <div>申请时间</div>
            </template>
            <t-date-range-picker
              v-model="formData.dateTime"
              :placeholder="[
                t('approval.approval_data.start_time'),
                t('approval.approval_data.end_time'),
              ]"
              style="width: 244px"
              class="!w-240"
              clearable
            />
          </t-form-item>
          <t-form-item name="level" class="searchForm-item" label-width="64px">
            <template #label>
              <div>会员级别</div>
            </template>
            <t-select
              v-model="formData.level"
              class="searchForm-item-input"
              :options="levelOptions"
              clearable
            ><template #suffixIcon>
              <img src="@/assets/svg/icon_arrow_down.svg" />
            </template>
          </t-select>
          </t-form-item>

          <t-form-item name="type" class="searchForm-item" label-width="64px">
            <template #label>
              <div>入会类型</div>
            </template>
            <t-select
              v-model="formData.type"
              class="searchForm-item-input"
              :options="typeOptions"
              clearable
            ><template #suffixIcon>
              <img src="@/assets/svg/icon_arrow_down.svg" />
            </template>
          </t-select>
          </t-form-item>

          <t-form-item
            v-show="isMoreSearch"
            name="telephone"
            class="searchForm-item"
            label-width="64px"
          >
            <template #label>
              <div>手机号</div>
            </template>
            <t-input
              v-model="formData.telephone"
              class="searchForm-item-input"
              :maxlength="50"
              clearable
              placeholder="请输入"
              @enter="onEnter"
            />
          </t-form-item>
          <t-form-item
            v-show="isMoreSearch"
            name="name"
            class="searchForm-item"
            label-width="74px"
          >
            <template #label>
              <div>代表人姓名</div>
            </template>
            <t-input
              v-model="formData.name"
              class="searchForm-item-input"
              :maxlength="50"
              clearable
              placeholder="请输入"
              style="width: 190px"
              @enter="onEnter"
            />
          </t-form-item>

          <div class="searchForm-buttons">
            <t-button theme="primary" variant="base" @click="onSearch">
              搜索</t-button>
            <t-button theme="default" variant="outline" @click="onReset">
              重置</t-button>
          </div>
          <div class="searchForm-buttons">
            <t-link
              theme="primary"
              class="ml-16"
              hover="color"
              @click="setMoreSearch"
            >
              {{ isMoreSearch ? "关闭高级搜索" : "高级搜索" }}
            </t-link>
          </div>
        </t-form>
        <!-- <div class="tabs">
            <span class="tabs-tab tabs-active cursor">单位会员</span>
            <span class="tabs-tab cursor">个人会员</span>
          </div> -->
        <div class="table">
          <t-table
            row-key="id"
            :columns="memberColumns"
            :pagination="pagination.total > 10 ? pagination : null"
            :data="memberData"
          >
            <template #main="{ row }">
              <div class="main_body">
                <span
                  v-show="row.type === 1"
                  class="main_body-item"
                  style="text-overflow: ellipsis; overflow: hidden"
                >
                  {{ $filters.isPeriodEmpty(row.team_name) }}
                </span>
                <span
                  class="main_body-item"
                  style="text-overflow: ellipsis; overflow: hidden"
                >
                  {{ $filters.isPeriodEmpty(row.name) }}
                </span>

                <span class="main_body-item phone">
                  +{{ $filters.isPeriodEmpty(row.telCode) }}

                  {{ $filters.isPeriodEmpty(row.telephone) }}
                </span>
              </div>
            </template>
            <template #level_name="{ row }">
              {{ $filters.isPeriodEmpty(row.level_name) }}
            </template>
            <template #status="{ row }">
              <div :class="showClassStatus(row.activate_audit)">
                {{ showTextStatus(row.activate_audit) }}
              </div>
            </template>
            <template #type="{ row }">
              <div class="status">{{ showTextType(row.type) }}</div>
            </template>
            <template #pay_status="{ row }">
              <div class="status">{{ showTextFee(row.status) }}</div>
            </template>

            <!--  -->
            <template #operate="{ row }">
              <span class="operates">
                <t-link
                  theme="primary"
                  hover="color"
                  class="operates-item"
                  @click="onLookDetail(row)"
                >
                  详情
                </t-link>
              </span>
            </template>

            <template #empty>
              <div class="empty">
                <noData :text="$t('engineer.no_data')" />
              </div>
            </template>
          </t-table>
        </div>
      </div>
    </div>
  </div>
  <t-drawer
    v-model:visible="filterVisible"
    :close-btn="true"
    size="472px"
    :header="t('approval.approval_data.sur')"
  >
    <div class="form-boxxx">
      <div class="fitem">
        <div class="title">申请状态</div>
        <div class="ctl">
          <t-select
            v-model="paramsTemp.status"
            clearable
            :options="statusOptions"
            style="width: 422px"
            @change="statusChange"
          ><template #suffixIcon>
            <img src="@/assets/svg/icon_arrow_down.svg" />
          </template>
        </t-select>
        </div>
      </div>
      <div class="fitem">
        <div class="title">申请时间</div>
        <div class="ctl">
          <t-date-range-picker
            v-model="paramsTemp.dateTime"
            :placeholder="[
              $t('approval.approval_data.start_time'),
              $t('approval.approval_data.end_time'),
            ]"
            style="width: 422px"
            clearable
          />
        </div>
      </div>
      <!-- <div class="fitem">
        <div class="title">{{ t("member.svip.member_level") }}</div>
        <div class="ctl">
          <t-select v-replace-svg
            v-model="paramsTemp.level"
            :options="levelOptions"
            clearable
            style="width: 422px"
            @change="levelChange"
          />
        </div>
      </div> -->
      <div class="fitem">
        <div class="title">{{ t("member.regular.phone") }}</div>
        <div class="ctl">
          <t-input
            v-model="paramsTemp.telephone"
            class="searchForm-item-input"
            :maxlength="50"
            clearable
            :placeholder="$t('member.regular.please_input')"
            style="width: 422px"
          />
        </div>
      </div>
      <div class="fitem">
        <div class="title">入会类型</div>
        <div class="ctl">
          <t-select
            v-model="paramsTemp.type"
            clearable
            :options="typeOptions"
            style="width: 422px"
          ><template #suffixIcon>
            <img src="@/assets/svg/icon_arrow_down.svg" />
          </template>
        </t-select>
        </div>
      </div>
      <div class="fitem">
        <div class="title">{{ t("member.regular.respect") }}</div>
        <div class="ctl">
          <t-input
            v-model="paramsTemp.name"
            class="searchForm-item-input"
            :maxlength="50"
            clearable
            :placeholder="$t('member.regular.please_input_name')"
            style="width: 422px"
          />
        </div>
      </div>
    </div>
    <template #footer>
      <div class="foot">
        <div class="btn1" @click="footerCla">
          {{ t("approval.approval_data.cl") }}
        </div>
        <div v-if="paramsSuperFoot" class="btn3" @click="getDataRunDr">
          {{ t("approval.approval_data.cm") }}
        </div>
        <div v-else class="btn2">
          {{ t("approval.approval_data.cm") }}
        </div>
      </div>
    </template>
  </t-drawer>
  <InviteQrcodeModal ref="inviteQrcodeModalRef" />
  <LookActiveModal ref="lookActiveModalRef" @reload="onSearch" :platform="platformCpt"/>
</template>

<script lang="ts" setup>
import noData from "@renderer/components/common/Empty.vue";
import { ref, reactive, watch, computed } from "vue";
import { useI18n } from "vue-i18n";
import {
  getMemberJobsListAxios,
  getMemberActivatesAxios,
  getMemberApplyLinkAxios,
  getMemberActivatesDetailAxios,
  getMemberSettingAxios,
} from "@renderer/api/uni/api/businessApi";
import { getResponseResult } from "@renderer/utils/myUtils";
import { MessagePlugin, FormRule } from "tdesign-vue-next";
import InviteQrcodeModal from "@renderer/views/uni/member_home/panel/apply-member-panel/modal/invite-qrcode-modal.vue";
import LookActiveModal from "@renderer/views/uni/member_home/panel/active-member-panel/modal/look-active-modal.vue";
import { useUniStore } from "@renderer/views/uni/store/uni";
import memberConst from "@renderer/components/free-from/design/constants/associationConst";
import lodash from "lodash";
import { useDigitalPlatformStore } from "@renderer/views/digital-platform/store/digital-platform-store";
import { useRoute } from "vue-router";
import { platform } from "@renderer/views/digital-platform/utils/constant";
import { getUniTeamID } from "@renderer/views/uni/utils/auth";
import { onMountedOrActivated } from "@renderer/hooks/onMountedOrActivated";

const { t } = useI18n();
const store = useUniStore();

const digitalPlatformStore = useDigitalPlatformStore();
const route = useRoute();

const statusOptions = [
  // 申请状态
  //   { label: "全部", value: 0 },
  { label: "待审核", value: 1 },
  { label: "已通过", value: 2 },
  { label: "已驳回", value: 3 },
  { label: "已失效", value: 4 },
];
const levelOptions = ref([
  //   {
  //     label: "全部",
  //     value: 0
  //   }
]);


const props = defineProps({
  platform: {
    type: String,
    default: '',
  },
})
// 平台类型 目前只有digital-platform
const platformCpt: any = computed(() => {
  return props.platform || route.query?.platform
})

const activeAccount = computed(() => {
  if(platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount
  } else if(platformCpt.value === platform.digitalWorkbench)  {
    return route.query;
  } else {
    return store.activeAccount
  }
})


const currentTeamId = computed(() => {
  if(platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount?.teamId
  } else if(platformCpt.value === platform.digitalWorkbench){
    return route.query?.teamId || 0;
  } else {
    return  getUniTeamID()
  }
})


const typeOptions = [
  // 入会类型
  //   { label: "全部", value: 0 },
  { label: "成员", value: 1 },
  { label: "个人", value: 2 },
];
const emits = defineEmits(["onBrush"]);

const FORM_RULES: Record<string, FormRule[]> = {
  // phone: [{ required: false, message: '内容超出', max: 10 }],
};
const formData = reactive({
  team_name: "", // 会员名称
  status: undefined, // 申请状态 1：待审核，2：已通过，3：已驳回
  dateTime: [], // 申请开始、结束时间
  level: undefined, // 会员级别
  type: undefined, // 入会类型
  level_text: undefined,
  status_text: undefined,
  name: "", // 代表人姓名
  telephone: "", // 手机号
});
const lookActiveModalRef = ref(null);
const form = ref(null);
const isMoreSearch = ref(false);
const memberColumns = ref([
  { colKey: "main", title: t('member.impm.app_1'), width: "20%", ellipsis: true },
  { colKey: "type", title: t('member.digital.j'), width: "10%", ellipsis: true },
  // { colKey: "level_name", title: t('member.impm.app_3'), width: "10%", ellipsis: true },
  { colKey: "apply_time", title: t('member.impm.app_4'), width: "13%", ellipsis: true },
  { colKey: "status", title: t('member.impm.app_5'), width: "10%", ellipsis: true },
  //   { colKey: "pay_status", title: "会费状态", width: "10%", ellipsis: true },
  { colKey: "operate", title: t('member.impm.app_7'), width: "10%", ellipsis: true },
]);
const memberData = ref([]);
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
  onChange: (pageInfo) => {
    console.log("pagination.onChange", pageInfo);
    pagination.current = pageInfo.current;
    pagination.pageSize = pageInfo.pageSize;

    // getMemberList({});
    onSearch();
  },
});
// 会费状态
const showTextFee = (val) => {
  let msg = "";
  // 1：未缴费，2：已缴费，3：无需缴费
  switch (val) {
    case 1:
      msg = t('member.winter.no_fee1');
      break;
    case 2:
      // msg = "已缴费";
      msg = t('member.winter.no_fee2');

      break;
    case 3:
      // msg = "无需缴费";
      msg = t('member.winter.no_fee3');

      break;
    default:
      msg = "--";
  }
  return msg;
};

const showClassStatus = (val) => {
  // 申请状态，1：待审核，2：已入会，3：已驳回
  let result = {};
  if (val === 1) {
    result = { wait: true };
  } else if (val === 2) {
    result = { success: true };
  } else if (val === 3) {
    result = { reject: true };
  } else if (val === 4) {
    result = { default: true };
  }
  return result;
};
const showTextStatus = (val) => {
  const option = statusOptions.find((v: any) => v.value === val);
  if (option) {
    return option.label;
  }
  return "--";
};
// 入会类型
const showTextType = (val) => {
  const option = typeOptions.find((v: any) => v.value === val);
  if (option) {
    return option.label;
  }
  return "--";
};

// watch(
//   () => store.activeAccount,
//   (val) => {
//     if (val) {
//       // updateAllCount();
//       onSearch();
//     }
//   },
//   {
//     deep: true,
//     // immediate: true
//   }
// );

onMountedOrActivated(() => {
  onSearch();
});

const setMoreSearch = () => {
  isMoreSearch.value = !isMoreSearch.value;
};

const onReset = () => {
  form.value.reset();
  onSearch();
};
const onSearch = () => {
  const params = {
    ...formData,
    apply_time_start: formData.dateTime.length > 0 ? formData.dateTime[0] : "",
    apply_time_end: formData.dateTime.length > 1 ? formData.dateTime[1] : "",
  };
  getMemberList(params);
  emits('onBrush')
};

// 邀请入会
const inviteQrcodeModalRef = ref(null);
const onInviteJobClub = () => {
  getInviteLinkAxios().then((val) => {
    inviteQrcodeModalRef.value.onOpen(val);
  });
};

// 获取邀请链接
const getInviteLinkAxios = () => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await getMemberApplyLinkAxios();
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      resolve(result.data.link);
    } catch (error) {
      reject();
      const errMsg = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
    }
  });
};

// 获取设置
const onGetMemberSetting = async () => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await getMemberSettingAxios({}, currentTeamId.value);
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      const subData = result.data;
      let menuList = memberConst.filter((v) => v.fromType === "person");
      if (!(subData.personal_form && subData.personal_form.length > 0)) {
        subData.personal_form = lodash.cloneDeep(menuList);
      }
      menuList = memberConst.filter((v) => v.fromType === "unit");
      if (!(subData.team_form && subData.team_form.length > 0)) {
        subData.team_form = lodash.cloneDeep(menuList);
      }
      resolve(result.data);
    } catch (error) {
      const errMsg = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
      reject();
    }
  });
};

// 获取入会申请详情
const onGetMemberApplyDetailAxios = (res) => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      // result = await getMemberApplyDetailAxios(res.id);
      result = await getMemberActivatesDetailAxios(res.id);
      // getMemberActivatesDetailAxios;
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      resolve(result.data);
    } catch (error) {
      reject();
      const errMsg = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
    }
  });
};
// getMemberApplyDetailAxios

// 获取入会申请列表
const getMemberList = async (params) => {
  // teamId
  params.page = pagination.current;
  params.pageSize = pagination.pageSize;

  if (params.team_name) {
    params.keyword = params.team_name;
    delete params.team_name;
  }
  try {
    let result = await getMemberActivatesAxios(params, currentTeamId.value);
    console.log(result);
    result = getResponseResult(result);
    if (!result) return;
    memberData.value = result.data.list;
    pagination.total = result.data.total;
    console.log(memberData.value);
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    MessagePlugin.error(errMsg);
  }
};

// 获取会员职务列表
const getMemberJobsList = async () => {
  // teamId
  // params.page = pagination.current;
  // params.pageSize = pagination.pageSize;
  try {
    let result: any = await getMemberJobsListAxios();
    console.log(result);
    result = getResponseResult(result);
    if (!result) return;

    levelOptions.value = [
      ...result.data.map((v) => ({
        label: v.level_name,
        value: v.id,
      })),
    ];
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    MessagePlugin.error(errMsg);
  }
};

const initData = () => {
  // getMemberJobsList();
  getMemberList({});
};
initData();

const onLookDetail = (row: any) => {
  // 拿详情，和系统设置的信息
  Promise.all([
    onGetMemberApplyDetailAxios(row), // 详情
    onGetMemberSetting(), // 组织配置信息
  ]).then((res: any) => {
    lookActiveModalRef.value.onOpen(res);
  });
};

const onEnter = () => {};

const filterVisible = ref(false);
const paramsSuper = computed(
  () =>
    formData.status ||
    formData.dateTime.length ||
    formData.level ||
    formData.name ||
    formData.type ||
    formData.telephone
);
const paramsSuperFoot = computed(
  () =>
    paramsTemp.value.status ||
    paramsTemp.value.level ||
    paramsTemp.value.type ||
    paramsTemp.value.name ||
    paramsTemp.value.dateTime.length ||
    paramsTemp.value.telephone
);
const showFilter = () => {
  filterVisible.value = true;
};
const paramsTemp = ref({
  level: undefined,
  level_text: undefined,
  status: undefined,
  type: undefined,
  status_text: undefined,
  name: undefined,
  telephone: undefined,
  dateTime: [],
});
const statusChange = (e, ctx) => {
  console.log(e, ctx);
  paramsTemp.value.status_text = ctx.option.label;
};
const levelChange = (e, ctx) => {
  console.log(e, ctx);
  paramsTemp.value.level_text = ctx.option.label;
};
const clearFilters = () => {
  formData.level = undefined;
  formData.status = undefined;
  formData.telephone = undefined;
  formData.type = undefined;
  formData.name = undefined;
  formData.dateTime = [];
  paramsTemp.value.dateTime = [];
  paramsTemp.value.type = undefined;
  paramsTemp.value.name = undefined;
  paramsTemp.value.level = undefined;
  paramsTemp.value.status = undefined;
  paramsTemp.value.telephone = undefined;
  onSearch();
};
const clearStatus = () => {
  formData.status = undefined;
  paramsTemp.value.status = undefined;
  onSearch();
};
const clearFiltertelephone = () => {
  formData.telephone = undefined;
  paramsTemp.value.telephone = undefined;
  onSearch();
};
const clearName = () => {
  formData.name = undefined;
  paramsTemp.value.name = undefined;
  onSearch();
};
const clearType = () => {
  formData.type = undefined;
  paramsTemp.value.type = undefined;
  onSearch();
};
const clearlevel = () => {
  formData.level = undefined;
  paramsTemp.value.level = undefined;
  onSearch();
};
const clearDateTime = () => {
  formData.dateTime = undefined;
  paramsTemp.value.dateTime = undefined;
  onSearch();
};
const getDataRunDr = () => {
  filterVisible.value = false;
  formData.level = paramsTemp.value.level;
  formData.type = paramsTemp.value.type;
  formData.status = paramsTemp.value.status;
  formData.telephone = paramsTemp.value.telephone;
  formData.name = paramsTemp.value.name;
  formData.dateTime = paramsTemp.value.dateTime;
  formData.status_text = paramsTemp.value.status_text;
  formData.level_text = paramsTemp.value.level_text;
  onSearch();
};
const footerCla = () => {
  filterVisible.value = false;
  paramsTemp.value.level = undefined;
  paramsTemp.value.status = undefined;
  paramsTemp.value.telephone = undefined;
  paramsTemp.value.name = undefined;
  paramsTemp.value.type = undefined;
  paramsTemp.value.dateTime = [];
};
</script>

<style lang="less" scoped>
@import "./public.less";

.main_body {
  display: flex;
  flex-direction: column;
}
.t-table {
  color: var(--kyy_color_table_text, #1a2139);

  /* kyy_fontSize_2/regular */
  font-family: PingFang SC;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
}

.body {
  height: calc(100vh - 184px) !important;
}
</style>
