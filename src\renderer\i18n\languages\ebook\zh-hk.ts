export const ebook_HK = {
  load: '加載中...',
  ss: '搜索會刊名稱',
  sskw: '搜索刊物名稱',
  loadmore: '加載更多',
  neterr: '網絡鏈接失敗，請檢查網絡後重試',
  again: '點擊重試',
  nodata: '暫無刊物',
  addebook: '添加會刊',
  view: '查看',
  cr: '會刊生成中',
  crok: '已發布',
  crerr: '生成失敗',
  ebookname: '會刊名稱',
  status: '狀態',
  upat: '更新時間',
  del: '刪除',
  delebook: '刪除會刊',
  deltip: '刪除後不可恢復，是否繼續刪除？',
  delsucc: '刪除成功！',
  uptip: '上傳格式為pdf，大小不超過100MB！',
  edsucc: '編輯成功',
  edsucc2: '設置成功',
  addsucc: '正在為你生成電子會刊，生成後將自動發布，請稍後查看',
  namerq: '請輸入會刊名稱',
  namerq2: '請輸入刊物名稱',
  up: '點擊上傳',
  ebfile: '會刊文件',
  cover: '會刊封面',
  ebdesc: '會刊描述',
  input: '請輸入會刊名稱',
  covtip: '上傳格式為png，jpeg，jpg，大小不超過5MB',
  editeb: '編輯會刊',
  addeb: '添加會刊',
  addse: '添加成功',
  validatorTip: '請上傳會刊封面',
  validatorTipkw: '請上傳刊物封面',
  validatorTip2: '請上傳會刊文件',
  validatorTip23: '請上傳刊物文件',
  plin: '請輸入內容',
  noreg: '未找到匹配會刊',

  gri: '分組信息',
  gai: '添加分組',
  gdi: '編輯分組',
  grn: '分組名稱',
  grni: '請輸入分組名稱',
  llr: '聯絡人',
  llri: '請選擇聯絡人',
  llri2: '選擇聯絡人',
  ad: '添加',
  grq: '分組群',
  gre: '解散該群',
  qn: '群名稱',
  qno: '群主',
  qrs: '群人數',
  gr2q: '當成員數2名後，會自動創建群聊',
  zqw: '暫無群聊',
  cjq: '創建群聊',
  cjqz: '創建分組群',
  auc: '創建一個關聯此分組的群，如果有新成員進入分組會自動加入該群',
  tip1: '1、當分組中分配了聯絡人，並且的成員數有2名後才會創建此群聊。',
  tip2: '2、新進入分組的成員會自動加入到群聊。',
  tip3: '3、如果成員被移除了當前分組，群聊中也會移除此成員。',
  tip4: '4、創建群聊時會把聯絡人作為群主。群主可在群管理中轉移群主身份。',
  es: '編輯成功',
  exs: '解散成功',
  ex: '解散',
  ext: '解散該群，群聊中的聊天記錄，圖片和文件將同時被刪除',
  sxz: '選擇群主',
  sxzt: '提示：群主僅能從聯絡人中進行選擇',
  cs: '創建成功',
  cst: '確定要創建群聊嗎？',
  changet: '該分組群的群主將由【{name}】變更為【{name2}】，是否確定？',
  grs: '設置分組',
  sgt: '請選擇分組',
  grsu: '設置成功',
  lrr: '聯絡人',
  fz: '分組',
  fz2: '分組：',
  ssllr: '搜索聯絡人',
  nlllr: '暫無聯絡人',
  alrr: '添加聯絡人',
  dllr: '刪除聯絡人',
  dllrt: '是否刪除該聯絡人？',
  asu: '添加成功',
  zug: '暫無分組',
  cys: '成員數',
  de1: '確定要刪除該分組？如果該分組有分組群，則分組群的聊天信息和文件都會刪除。',
  de2: '請先移除分組內除聯絡人以外的所有平臺成員再進行刪除分組',
  fzck: '分組查看設置',
  bc: '保存',
  fzfw: '分組查看範圍',
  fw1: '1、可查看全部分組：平臺成員和聯絡人可在名錄、通訊錄中查看所有分組下的成員。',
  fw12: '2、僅可查看所在分組：平臺成員和聯絡人在名錄、通訊錄中僅可查看到自己所在分組下的成員。',
  all: '可查看全部分組',
  only: '僅可查看所在分組',
  liw: '以下人員例外',
  dgx: '當勾選了「僅可查看所在分組」後，可單獨設置成員依然可查看全部分組',
  tianj: '添加',
  ttipss1: '成員數2名後才可以創建群聊',
  wfz: '未分組',
  setsu: '設置成功',
  leader: '請選擇群主',
  grsu2: '设置分組成功',
  seterr: '设置分組失敗',
  qd: '確定',
  lz: '已離職',
  zug1: '暫無聯絡人',
  mset1: '確定關閉【平臺動態審核】？',
  mset2: '關閉後，申請推廣的平臺動態無需審核直接通過',
  mset3: '確定',
  mset4: '取消',
  mset5: '平臺動態設置',
  mset6: '動態審核',
  mset7: '開啟後，申請推廣的平臺動態均要管理員審核',

  visback: '返回',
  vist: '訪客申請',
  visp: '請選擇',
  vispass: '通過',
  visrefu: '拒絕',
  viyt: '已通過',
  vrefuse: '已拒絕',
  sx: '已失效',
  nos: '搜索無結果',
  nod: '暫無數據',
  vzc: '正常',
  vjr: '未加入',
  vtype1: '全部',
  vtype2: '待審核',
  vtype3: '已通過',
  vtype4: '已拒絕',
  vtype5: '自動通過',
  vtype6: '已失效',
  vname: '姓名',
  vphone: '手機號碼',
  voperatorer: '邀請人',
  vtime: '申請時間',
  voperate: '操作',
  vsc: '操作成功',
  ver: '操作失敗',
  vssxm: '搜索姓名',
  vfk: '訪客申請列表',
  vyq: '邀請訪客',
  vst: '狀態：',
  vcy: '成員類型：',
  vph: '手機號：',
  vxq: '詳情',
  vst2: '狀態',
  vcy2: '成員類型',
  vph2: '手機號',
  vzs: '正式',
  vfk2: '訪客',
  vat: '加入時間',
  vemail: '郵箱',
  vb: '基礎信息',
  vzty: '關聯主體',
  delf: '刪除訪客',
  vfz: '負責人',
  vxx: '聯系人',
  vdb: '代表人',
  vdelt: '確定刪除當前訪客？',
  vdelc: '刪除後，該訪客不可再訪問數字平臺，已產生的數據不可恢復',
  vdsu: '刪除成功',
  vfk3: '訪客狀態異常',
  vipu: '請輸入',
};
