<script setup lang="ts">
import { useVModel } from '@vueuse/core';
import { computed } from 'vue';

const props = defineProps<{
  modelValue?: number; // 依然是数字
  canClick?: boolean;
  isCommission?: boolean;
}>();

const emit = defineEmits(['update:modelValue']);

const activeStep = useVModel(props, 'modelValue', emit, {
  defaultValue: props.modelValue ?? 0,
});

// 全量步骤
const allSteps = [
  {
    index: '01',
    title: '商户入网',
    desc: '线上支付入网申请',
  },
  {
    index: '02',
    title: '商户实名',
    desc: '微信与支付宝实名认证',
  },
  {
    index: '03',
    title: '商户分账',
    desc: '开通分账业务',
  },
];

// 当前 steps
const steps = computed(() => {
  if (props.isCommission) {
    return [
      allSteps[0],
      {
        ...allSteps[2],
        index: '02',
        title: '商户分佣开通',
        desc: '开通分佣收款业务',
      },
    ];
  }
  return allSteps;
});

// 全量下标到当前 steps 下标的映射
const stepIndexMap = computed(() => {
  const map = new Map<number, number>();
  if (props.isCommission) {
    // 0 -> 0, 1 -> 1 (分佣模式下，step 1 对应商户分佣开通)
    map.set(0, 0);
    map.set(1, 1);
  } else {
    // 0 -> 0, 1 -> 1, 2 -> 2
    steps.value.forEach((_, idx) => {
      map.set(idx, idx);
    });
  }
  return map;
});

// 当前高亮的 steps 下标
const activeStepIndex = computed(() => stepIndexMap.value.get(activeStep.value) ?? -1);

// 点击时反向映射
const handleClick = (idx: number) => {
  if (!props.canClick) return;
  if (props.isCommission) {
    // 0 -> 0, 1 -> 1 (分佣模式下直接映射)
    activeStep.value = idx;
  } else {
    activeStep.value = idx;
  }
};
</script>

<template>
  <div :class="['step-container', { 'is-commission': isCommission }]">
    <div
      v-for="(item, idx) in steps"
      :key="item.index"
      :class="['step-item', { 'active': activeStepIndex === idx }]"
      :data-step-type="idx === 0 ? 'start' : idx === steps.length - 1 ? 'end' : 'middle'"
      @click="handleClick(idx)"
    >
      <div class="step-circle">{{ item.index }}</div>
      <div class="step-content">
        <h3 class="step-content__title">{{ item.title }}</h3>
        <p class="step-content__desc">{{ item.desc }}</p>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.step-container {
  display: flex;
  width: 824px;
  justify-content: space-between;

  &.is-commission {
    justify-content: center;
    .step-item {
      width: 420px;

      &[data-step-type="start"] {
        background-image: url('../../assets/step-start-lg.svg');
        &.active {
          background-image: url('../../assets/step-start-active-lg.svg');
        }
      }

      &[data-step-type="end"] {
        background-image: url('../../assets/step-end-lg.svg');
        &.active {
          background-image: url('../../assets/step-end-active-lg.svg');
        }
      }
    }
  }

  .step-item {
    --step-text-color: var(--text-kyy_color_text_3, #828DA5);
    --step-border-color: var(--border-kyy_color_border_default, #D5DBE4);
    --step-active-color: #fff;

    position: relative;
    display: flex;
    align-items: center;
    width: 280px;
    height: 64px;
    color: var(--step-text-color);
    font: 400 14px/22px sans-serif;
    padding: 0 20px;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    cursor: pointer;
    z-index: calc(100 - var(--step-index));

    &:not(:first-child) {
      padding-left: 48px;
      margin-left: -16px;
    }

    &[data-step-type="start"] {
      --step-index: 1;
      background-image: url('../../assets/step-start.svg');
      &.active {
        background-image: url('../../assets/step-start-active.svg');
      }
    }

    &[data-step-type="middle"] {
      --step-index: 2;
      background-image: url('../../assets/step-middle.svg');
      &.active {
        background-image: url('../../assets/step-middle-active.svg');
      }
    }

    &[data-step-type="end"] {
      --step-index: 3;
      background-image: url('../../assets/step-end.svg');
      &.active {
        background-image: url('../../assets/step-end-active.svg');
      }
    }

    .step-circle {
      margin-right: 10px;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      border: 2px solid var(--step-border-color);
      color: var(--step-text-color);
      font: 600 16px/24px sans-serif;
    }

    .step-content {
      &__title {
        color: var(--step-text-color);
        font: 600 16px/24px sans-serif;
        margin-bottom: 2px;
      }

      &__desc {
        color: var(--step-text-color);
        font: 400 14px/22px sans-serif;
      }
    }

    &.active {
      color: var(--step-active-color);

      .step-circle {
        border-color: var(--step-active-color);
        color: var(--step-active-color);
      }

      .step-content__title,
      .step-content__desc {
        color: var(--step-active-color);
      }
    }
  }
}
</style>
