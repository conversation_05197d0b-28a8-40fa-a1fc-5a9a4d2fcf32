<script setup lang="ts">
import icon_error from '@renderer/assets/pb/icon_error.svg';
import icon_success from '@renderer/assets/pb/icon_success.svg';
import icon_warning from '@renderer/assets/pb/icon_warning.svg';
import icon_info from '@renderer/assets/pb/icon_info.svg';
import icon_revoked from '@renderer/assets/pb/revoked.svg';
import { CheckIcon, CloseIcon } from 'tdesign-icons-vue-next';

import { ref, reactive, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { MessagePlugin } from 'tdesign-vue-next';
import LynkerSDK from '@renderer/_jssdk';
import { reviewCheck } from '@renderer/api/curtureTourism';
import { useChatActionStore } from '@renderer/views/message/service/actionStore';

const { t } = useI18n();

enum DetailResultEnum {
  pending = 'StatusReviewing', // 待审核
  refuse = 'StatusRejected', // 拒绝
  agree = 'StatusApproved', // 已同意
  noAuth = 'StatusNoAuth', // 无权限
  delete = 'StatusDeleted', // 已删除
}

const reviewResultMap = {
  [DetailResultEnum.refuse]: {
    icon: icon_error,
    title: t('activity.announcement.releaseSuccessTips6'),
    showLine: true,
  },
  [DetailResultEnum.agree]: {
    icon: icon_success,
    title: t('activity.announcement.releaseSuccessTips3'),
  },
    [DetailResultEnum.noAuth]: {
    icon: icon_warning,
    title: t('activity.announcement.releaseSuccessTips9'),
  },
  [DetailResultEnum.delete]: {
    icon: icon_error,
    title: t('djMan.management.contentDeleted'),
  },
  [DetailResultEnum.revocation]: {
    icon: icon_revoked,
    title: t('activity.announcement.reset2'),
  },
};

const props = defineProps({
  dialogInfo: {
    type: Object,
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    default: () => {},
  },
});
const resultStatus = ref('');
const refuseRmark = ref('');

const emits = defineEmits(['onClose']);

const loading = ref(false);
const approvalVisibleRef = ref(true);

const rules = {
  remark: [
    { required: true, message: t('member.active.please_input_rejecto_reason'), type: 'error' },
  ],
};

const formValidatorStatus = ref(null);
const formData = reactive({
  remark: '',
});
const refuseVisible = ref(false);

// 同意发布
const onConfirm = async () => {
  loading.value = true;
  const { informationId, reviewerCardId, publisherCardId, teamId } = props.dialogInfo.msg?.extend || {};
  const res = await reviewCheck({ id:informationId, me: { cardId: reviewerCardId || publisherCardId, teamId, openId: props.dialogInfo.msg?.openId }, approvedFlag: true });
  if (res.data.code === 0) {
    resultStatus.value = DetailResultEnum.agree;
  }
};

const onRefuseClose = () => {
  refuseVisible.value = false;
};
const onRefuseConfirm = () => {
  formValidatorStatus.value.validate().then(async (validateResult) => {
    if (validateResult === true) {
      console.log(formData);
      const { informationId, reviewerCardId, teamId } = props.dialogInfo.msg?.extend || {};
      const res = await reviewCheck({
        id: informationId, me: { cardId: reviewerCardId, teamId, openId: props.dialogInfo.msg?.openId }, reviewRemark: formData.remark,
        approvedFlag: false
      });
      onRefuseClose();
      if (res.data.code === 0) {
        MessagePlugin.success(t('activity.announcement.releaseSuccessTips6'));
        resultStatus.value = DetailResultEnum.refuse;
        refuseRmark.value = formData.remark;
      }
    }
  });
};
const previewDetail = () => {
    // 打开预览文章窗口
    const { informationId, reviewerCardId, publisherCardId, teamId } = props.dialogInfo.msg?.extend || {};
      const url = LynkerSDK.getH5UrlWithParams('/culture/index.html#/preview', {
        id: informationId,
        teamId,
        cardId: reviewerCardId || publisherCardId,
      });
      useChatActionStore().showDialogIframe(true, {
        url,
        id: informationId,
        name: '文化旅游',
      });

};
const onClose = () => {
  emits('onClose');
};
onMounted(() => {
  const { msg } = props.dialogInfo;
  const { status, remark } = msg;
  resultStatus.value = status;
  refuseRmark.value = remark;
});
</script>
<template>
  <div>
    <t-drawer
      v-model:visible="approvalVisibleRef"
      size="376"
      v-bind="$attrs"
      :footer="resultStatus === DetailResultEnum.pending"
      @close="onClose"
    >
      <template #header>
        <div class="f-align jc-sb">
          <div class="title">{{ t('activity.announcement.securityVerification') }}</div>
          <iconpark-icon class="iconhover" name="iconerror" @click="onClose" />
        </div>
      </template>
      <template #body>
        <div v-if="resultStatus === DetailResultEnum.pending" class="f-align fd-c release-box" style="gap: 16px;">
          <div class="release-icon"><img :src="icon_info" alt=""></div>
          <div class="release-tip">{{ t('policy.release_verification_tip') }}</div>
          <div class="release-line" />
          <div class="release-info f-align fd-c">
            <div v-for="item in props.dialogInfo.msg?.content?.body" :key="item" class="f-align ai-c">
              <div class="lable">{{ item.key }}</div>
              <div class="value">{{ item.value }}</div>
            </div>
            <div class="f-align ai-c">
              <div class="lable">{{ t('activity.announcement.releaseContent') }}</div>
              <div class="view-value" @click="previewDetail">{{ t('activity.announcement.previewReleaseContent') }}</div>
            </div>
          </div>
        </div>
        <div v-else class="f-align fd-c release-box" style="gap: 16px;">
          <div class="release-icon"><img :src="reviewResultMap[resultStatus]?.icon" alt=""></div>
          <div class="release-tip">{{ reviewResultMap[resultStatus]?.title }}</div>
          <div v-if="reviewResultMap[resultStatus]?.showLine" class="release-line" />
          <div v-if="resultStatus === DetailResultEnum.refuse && refuseRmark" class="release-info f-align fd-c">
            <div class="f-align ai-s">
              <div class="lable">{{ t('activity.announcement.honorReason') }}</div>
              <div class="value">{{ refuseRmark }}</div>
            </div>
          </div>
        </div>
      </template>
      <template #footer>
        <div class="footer-box">
          <t-button
            class="btn cancel pl-12!"
            variant="outline"
            theme="danger"
            @click="refuseVisible = true"
          >
            <template #icon><CloseIcon /></template>
            {{ t('activity.activity.Denied') }}
          </t-button>
          <t-button
            class="btn confirm pl-12!"
            theme="primary"
            variant="outline"
            :loading="loading"
            @click="onConfirm()"
          >
            <template #icon><CheckIcon /></template>
            {{ t("activity.announcement.honorContentConfirm1") }}
          </t-button>
        </div>
      </template>
    </t-drawer>
    <t-dialog
      :visible="refuseVisible"
      class="refuseVisible"
      :header="true"
      :footer="true"
      :close-btn="false"
      :close-on-esc-keydown="false"
      :close-on-overlay-click="false"
      width="560px"
      destroy-on-close
      :confirm-btn="{ disabled: !formData.remark }"
      @close="onRefuseClose"
      @confirm="onRefuseConfirm"
    >
      <template #header>
        <div class="f-align jc-sb">
          <div class="title">{{ t("activity.announcement.honorContentRefuse") }}</div>
          <iconpark-icon class="iconhover" name="iconerror" @click="onRefuseClose" />
        </div>
      </template>
      <template #body>
        <t-form
          ref="formValidatorStatus"
          :data="formData"
          :rules="rules"
          label-align="left"
        >
          <t-form-item :label="t('activity.announcement.honorContentRefuseReason')" name="remark">
            <t-textarea
              v-model="formData.remark"
              class="form-item-textarea"
              :placeholder="t('member.active.please_input_rejecto_reason')"
              :maxlength="200"
              :autosize="{ minRows: 2, maxRows: 3 }"
              :autofocus="true"
            />
          </t-form-item>
        </t-form>
      </template>
    </t-dialog>
  </div>
</template>
<style lang="less" scoped>
:deep(.t-drawer) {
  position: absolute;
  .t-drawer__header{
    padding: 0 24px;
  }
  .t-drawer__body {
    padding: 0;
  }
}
:deep(.refuseVisible) {
  .t-dialog--default {
    padding: 24px !important;
  }
  .t-dialog {
    overflow: hidden;
   }
   .t-dialog__body{
    margin:24px -4px 12px;
    padding:4px;
    border-radius: 8px;
    background-color: #fff;
   }
   .t-dialog__footer {
    background-color: #fff;
    width: 560px;
    margin:0 -24px -24px;
    padding:24px;
   }
  .t-form__item{
    display: flex;
    flex-direction: column;
    gap: 8px;
    .t-form__controls{
      margin-left: 0 !important;
    }
  }
  .t-dialog__cancel, .t-dialog__confirm {
    font-weight: 600;
  }
  .t-form__label {
    line-height: 22px;
  }
}
.ai-c {
  align-items: center;
}
.fd-c{
  flex-direction: column;
}
.jc-sb{
  width: 100%;
  justify-content: space-between;
}
.f-align{
  display: flex;
}
.release-box{
  padding: 12px 24px;
}
.release-icon{
  text-align: center;
  img {
    width: 64px;
    height: 64px;
  }
}
.release-tip{
  text-align: center;
  color: var(--text-kyy_color_text_1, #1A2139);

  /* kyy_fontSize_2/bold */
  font-family: "PingFang SC";
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 22px; /* 157.143% */
  padding-bottom: 12px;
}
.release-line{
  width: 100%;
  height: 1px;
  background-color: #ECEFF5;
}
.release-info{
  gap: 12px;
}
.lable{
  width: 88px;
  color: var(--text-kyy_color_text_3, #828DA5);

  /* kyy_fontSize_2/regular */
  font-family: "PingFang SC";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
}
.value{
  flex: 1;
  color: var(--text-kyy_color_text_1, #1A2139);

  /* kyy_fontSize_2/regular */
  font-family: "PingFang SC";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
  word-break: break-word;
}
.view-value{
  cursor: pointer;
  display: flex;
  height: 32px;
  justify-content: center;
  align-items: center;
  border-radius: var(--radius-kyy_radius_button_full, 999px);
  color: var(--color-button_text_brand-kyy_color_button_text_brand_font_default, #4D5EFF);

  /* kyy_fontSize_2/regular */
  font-family: "PingFang SC";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
}
.add-value {
  width: 0;
  flex: 1;
  .add{
    display: flex;
    height: 28px;
    min-width: 72px;
    min-height: 28px;
    max-height: 28px;
    padding: 0px 12px 0px 8px;
    justify-content: center;
    align-items: center;
    gap: 4px;
    border-radius: var(--radius-kyy_radius_button_full, 999px);
    border: 1px solid var(--color-button_border-kyy_color_buttonBorder_border_brand, #4D5EFF) !important;
    color: var(--color-button_border-kyy_color_buttonBorder_text_brand, #4D5EFF);
    text-align: center;

    /* kyy_fontSize_2/regular */
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
}
.name{
  max-width: calc(100% - 70px);
  color: var(--text-kyy_color_text_1, #1A2139);
  text-align: center;

  /* kyy_fontSize_2/regular */
  font-family: "PingFang SC";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
}
.change{
  cursor: pointer;
  margin-left: 4px;
  color: var(--color-button_text_brand-kyy_color_button_text_brand_font_default, #4D5EFF);
  text-align: center;

  /* kyy_fontSize_2/regular */
  font-family: "PingFang SC";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
}
.iconhover{
  color: var(--text-kyy_color_text_2, #516082);
  border-radius: 4px;
  width:24px;
  height:24px;
  font-size: 24px;
}

.iconhover:hover{
  background: #F3F6FA;
  cursor: pointer;
}
.footer-box{
  text-align: right;
  .cancel{
    border-radius: var(--radius-kyy_radius_button_s, 4px) !important;
    border: 1px solid var(--color-button_secondaryError-kyy_color_button_secondaryError_border_dedault, #D54941) !important;
    background: var(--color-button_secondaryError-kyy_color_button_secondrayError_bg_default, #FDF5F6) !important;
    color: var(--color-button_secondaryError-kyy_color_button_secondrayError_text_default, #D54941);
  }
  .cancel:hover {
    color: var(--color-button_secondaryError-kyy_color_button_secondrayError_text_default, #D54941) !important;
  }
  .confirm {
    border-radius: var(--radius-kyy_radius_button_s, 4px) !important;
    border: 1px solid var(--color-button_secondaryBrand-kyy_color_button_secondaryBrand_border_dedault, #4D5EFF);
    background: var(--color-button_secondaryBrand-kyy_color_button_secondrayBorder_bg_default, #EAECFF);
  }
  .confirm:hover {
    color: var(--color-button_secondaryError-kyy_color_button_secondrayError_text_default, #4D5EFF) !important;
  }
}

:deep(.t-textarea) {

  .t-textarea__inner {
    padding-bottom: 20px;
  }

  &::after {
    content: '';
    position: absolute;
    bottom: 1px;
    left: 1px;
    right: 1px;
    height: 20px;
    border-radius: 4px;
    background-color: #fff;
  }
}

.form-item-textarea {
  position: relative;

  :deep(.t-textarea__info_wrapper) {
    display: flex;
    position: absolute;
    right: 4px;
    bottom: 4px;
    z-index: 99;
  }
}
</style>
