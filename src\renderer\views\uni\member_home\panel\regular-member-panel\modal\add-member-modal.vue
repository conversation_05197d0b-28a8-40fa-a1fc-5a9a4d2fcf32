<template>
  <!-- <div v-if="visible" v-lkloading="{show: true, height: true, opacity:true }"></div> -->

  <t-drawer v-model:visible="visible" class="drawerSetForm" :close-on-overlay-click="false" :z-index="1500"
    :close-btn="true" :size="'472px'">
    <template #header>
      <div class="header">
        <span class="title">{{ applyData ? '编辑资料' : t("member.kaxi.c") }} </span>
        <!-- <span v-show="!props.isMember" class="tip cursor" @click="onShowMemberFlow">
          <iconpark-icon name="iconhelp" class="iconhelp"></iconpark-icon>{{ $t('member.winter_column.know_add_flow_1') }} </span> -->
      </div>
    </template>
    <template #closeBtn>
      <!-- <svg class="iconpark-icon" style="width: 16px; height: 16px">
        <use href="#close" />
      </svg> -->
      <iconpark-icon name="iconerror" style="font-size: 24px; color: #516082"></iconpark-icon>
    </template>
    <template #footer>
      <div class="toFooter">
        <!-- <div v-show="!applyData" class="check">
          <t-checkbox v-model="isChecked" />
          <span class="tap">
            保存后短信通知组织激活
            <t-tooltip placement="top">
              <template #content>
                保存成功后通知组织确认资料，激活身份；7天内仅能发送1次
              </template>
<iconpark-icon name="iconhelp" class="iconhelp"></iconpark-icon>


</t-tooltip> </span>

</div>
<div v-show="applyData" class="check"></div> -->
        <div></div>
        <div class="operates">
          <t-button theme="default" variant="outline" class="operates-item min-w-80px" @click="onClose">{{
            $t("member.impm.input_8")
          }}</t-button>

          <t-button theme="primary" class="operates-item min-w-80px" :loading="loading" @click="onSubmit">{{
            $t("member.impm.input_9")
          }}</t-button>
        </div>
      </div>
    </template>
    <div class="drawerSet-body">
      <template v-if="!(props.isMember && applyData)">
        <div class="tabTitle mb-6px">{{ $t("member.digital.j") }}</div>
        <t-radio-group v-model="currentTab" :options="tabsOptions" :disabled="Boolean(applyData)" class="mb-8px" />
      </template>
      <template v-if="isRefreshNeeded && visible">
        <form-runtime v-show="currentTab === 'person'" ref="runtimePersonRef" :widgets="controls_person"
          @release="releaseRun_person" @controls-event="controlsEventRun_person" />
        <form-runtime v-show="currentTab === 'unit'" ref="runtimeUnitRef" :widgets="controls_unit"
          @release="releaseRun_unit" @controls-event="controlsEventRun_unit" />
      </template>
    </div>
  </t-drawer>
  <SelectOrganizeModal ref="selectOrganizeModalRef" @on-selected="onSelectedOrganize" />
  <SelectMemberModal ref="selectMemberModalRef" :options="optionsMembers" :header="$t('member.second.f')"
    :is-only="true" @sub-form="onListenMembers" />
</template>

<script setup lang="ts">
import { ref, Ref, reactive, watch, computed } from "vue";
import { MessagePlugin } from "tdesign-vue-next";
// import formDetail from "@renderer/components/free-from/detail/index.vue";
import formRuntime from "@renderer/components/free-from/runtime/index.vue";
import memberConst from "@renderer/components/free-from/design/constants/associationConst";
import {
  getCommonTeamsAxios,
  getIndustryListAxios,
  getMemberJobsListAxios,
  getMemberSettingAxios,
  getTeamsDetailAxios,
  getTeamsSizeListAxios,
  createRegularAxios,
  patchRegularAxios,
  getOrganizeDepartmentListAxios,
  getOrganizeDepartmentJobsListAxios,
  getMemberRelationAxios2,
} from "@renderer/api/uni/api/businessApi";

import { formDiff } from "@renderer/components/free-from/utils";
import lodash, { isArray } from "lodash";
import dayjs from "dayjs";
import SelectMemberModal from "@renderer/views/uni/member_home/panel/membership-setting-panel/modal/select-member-modal.vue";
import { v4 as uuidv4 } from "uuid";
import { useUniStore } from "@renderer/views/uni/store/uni";
import SelectOrganizeModal from "./select-organize-modal.vue";
import { getChidlren, getResponseResult } from "@/utils/myUtils";
import { useI18n } from "vue-i18n";
import { useRoute } from "vue-router";
import { useDigitalPlatformStore } from "@renderer/views/digital-platform/store/digital-platform-store";
import { platform as platformCon } from "@renderer/views/digital-platform/utils/constant";
import { getUniTeamID } from "@renderer/views/uni/utils/auth";
import { debounce } from "lodash";
const { t } = useI18n();

const memberStore = useUniStore();

const isRefreshNeeded = ref(false);
const isChecked = ref(false);
// 运行时
// 运行时
const controls_person: any = ref([]);
const controls_unit: any = ref([]);
const runtimePersonRef: Ref<any> = ref(null);
const runtimeUnitRef: Ref<any> = ref(null);

const tabsOptions = [
  { label: t("member.kaxi.d"), value: "unit", icon: "#building-one-9890od0d" },
  { label: t("member.kaxi.e"), value: "person", icon: "#avatar-9i556n12" },
];

const optionsCodeList = [
  { label: "+86", value: 86 },
  { label: "+853", value: 853 },
  { label: "+852", value: 852 },
];

const optionsOrganizeType = [
  // { label: "企业", value: 1 },
  // { label: "商协会", value: 2 },
  // { label: "个体户", value: 3 },
  // { label: "其他", value: 0 }
  { label: t("member.second.g"), value: 1 },
  { label: t("member.second.h"), value: 2 },
  { label: "政府单位", value: 4 },
  { label: t("member.second.i"), value: 3 },
  { label: t("member.second.j"), value: 0 },
];

const currentTab = ref("unit");

const visible = ref(false);

// const data = ref(null);
const emits = defineEmits(["reload", "onShowMemberFlow"]);

const applyData: Ref<any> = ref(null); // 入会表单设置数据,详情数据
const industryListData: Ref<any> = ref([]); // 行业列表
const sizeListData: Ref<any> = ref([]); // 规模列表
const departmentListData: Ref<any> = ref([]); // 部门列表
watch(
  () => visible.value,
  (cur) => { },
);

const props = defineProps({
  disabledArr: {
    type: Array,
    default: () => [],
  },
  isHiddenArr: {
    type: Array,
    default: () => [],
  },
  isMember: {
    type: Number,
    default: 0,
  },
  platform: {
    type: String,
    default: "",
  },
});

const route = useRoute();
const digitalPlatformStore = useDigitalPlatformStore();
// 平台类型 目前只有digital-platform
const platformCpt = computed(() => {
  return props.platform || route.query?.platform;
});

const activeAccount = computed(() => {
  if (platformCpt.value === platformCon.digitalPlatform) {
    return digitalPlatformStore.activeAccount;
  } else if (platformCpt.value === platformCon.digitalWorkbench) {
    return route.query;
  } else {
    return memberStore.activeAccount;
  }
});

const currentTeamId = computed(() => {
  if (platformCpt.value === platformCon.digitalPlatform) {
    return digitalPlatformStore.activeAccount?.teamId;
  } else if (platformCpt.value === platformCon.digitalWorkbench) {
    return route.query?.teamId || 0;
  } else {
    return getUniTeamID();
  }
});

const onShowMemberFlow = () => {
  emits("onShowMemberFlow");
};
// 获取设置
const onGetMemberSetting = async () => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await getMemberSettingAxios({}, currentTeamId.value);
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      console.log(result, "resultresultresult");

      resolve(result.data);
    } catch (error) {
      const errMsg = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
      reject();
    }
  });
};

// 获取组织列表
const onGetOrganizeList = () => {
  // getMemberJobsListAxios
  let result = null;

  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await getCommonTeamsAxios({ owner: 0 });
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      //   organizeData.value = result.data;
      // 单位入会、个人入会
      // applyData.value = result.data;
      resolve(result.data);
    } catch (error) {
      const errMsg = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
      reject();
    }
  });
};

// 获取会员职务列表
const onGetMemberLevelList = () => {
  // getMemberJobsListAxios
  let result = null;

  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await getMemberJobsListAxios({ status: 2, show_status: 0 });
      console.log(result, "23232");
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      //   organizeData.value = result.data;
      // 单位入会、个人入会
      // applyData.value = result.data;
      resolve(result.data);
    } catch (error) {
      const errMsg: any = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
      // eslint-disable-next-line prefer-promise-reject-errors
      reject();
    }
  });
};

// 获取部门列表
const onGetOrganizeDepartmentList = () => {
  // getMemberJobsListAxios
  let result = null;

  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await getOrganizeDepartmentListAxios({ parent: 0 }, currentTeamId.value);
      console.log(result, "請問");
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      //   organizeData.value = result.data;
      // 单位入会、个人入会
      // applyData.value = result.data;
      resolve(result.data);
    } catch (error) {
      const errMsg: any = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
      // eslint-disable-next-line prefer-promise-reject-errors
      reject();
    }
  });
};
// 获取行业列表
const onGetIndustryList = () => {
  // getMemberJobsListAxios
  let result = null;

  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      console.log(activeAccount.value.teamRegion, "memberStorememberStore");

      result = await getIndustryListAxios(activeAccount.value.teamRegion, currentTeamId.value);
      console.log(result, "行业11");
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      //   organizeData.value = result.data;
      // 单位入会、个人入会
      // applyData.value = result.data;
      resolve(result.data);
    } catch (error) {
      const errMsg: any = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
      // eslint-disable-next-line prefer-promise-reject-errors
      reject();
    }
  });
};

// 获取组织规模列表
const onGetTeamsSizeList = () => {
  // getMemberJobsListAxios
  let result = null;

  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await getTeamsSizeListAxios({}, currentTeamId.value);
      console.log(result);
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      //   organizeData.value = result.data;
      // 单位入会、个人入会
      // applyData.value = result.data;
      resolve(result.data);
    } catch (error) {
      const errMsg: any = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
      // eslint-disable-next-line prefer-promise-reject-errors
      reject();
    }
  });
};

const initFreeForm = (res) => {
  console.log(res);
  // eslint-disable-next-line no-empty
  if (res.personal_form && res.personal_form.length > 0) {
  } else {
    const menuList = memberConst.filter((v) => v.fromType === "person");
    // 给系统默认
    res.personal_form = lodash.cloneDeep(menuList);
  }
  // eslint-disable-next-line no-empty
  if (res.team_form && res.team_form.length > 0) {
  } else {
    const menuList = memberConst.filter((v) => v.fromType === "unit");
    // 给系统默认
    res.team_form = lodash.cloneDeep(menuList);
  }
  res.personal_form.map((v) => {
    if (v.type === "BaseInfoPolitics") {
      v.origin.map((or) => {
        or.isShow = true;
        // if (or.vModel !== "phone"  ) {

        // }
        // 杨珂 1.6需求
        if (or.type == "InputUnborder" && or.vModel == "memberNum") {
          or.isShow = false;
        }
        if (props.isHiddenArr.includes(or.vModel)) {
          or.isShow = false;
        }
        or.disabled = false;
        initSystemDefault(or);
        return or;
      });
    }
    // if(v.)
    // v.origin.map((or) => {
    //   or.isShow = true;
    //   return or;
    // });
    return v;
  });
  res.team_form.map((v) => {
    if (v.type === "BaseInfoPolitics") {
      v.origin.map((or) => {
        or.isShow = true;
        // 杨珂 1.6需求
        if (or.type == "InputUnborder" && or.vModel == "memberNum") {
          or.isShow = false;
        }
        if (props.isHiddenArr.includes(or.vModel)) {
          or.isShow = false;
        }
        // if (or.vModel !== "phone") {
        // }
        or.disabled = false;
        initSystemDefault(or);
        return or;
      });
    }
    return v;
  });
};

const initSystemDefault = (or: any) => {
  const requireVModel = ["organizeName", "memberLevel", "name", "phone", "department", "joinTime", "expireTime"];
  const noRequireVModel = [
    "logo",
    "email",
    "memberNum",
    "reference",
    "organizeLogo",
    "organizeAbbrName",
    "organizeType",
    "industryType",
    "organizeScale",
    "organizeAddress",
  ];

  if (requireVModel.includes(or.vModel)) {
    or.required = true;
  } else if (noRequireVModel.includes(or.vModel)) {
    or.required = false;
  }
};

// 获取表单、options等配置信息
/**
 * 组织配置信息、组织会员级别、组织行业列表、组织规模
 */
// eslint-disable-next-line no-async-promise-executor
const onGetSettings = () =>
  // eslint-disable-next-line no-async-promise-executor
  new Promise(async (resolve, reject) => {
    try {
      isRefreshNeeded.value = false;

      const result = await Promise.all([
        onGetMemberSetting(), // 组织配置信息
        // onGetMemberLevelList(), // 组织会员级别
        onGetIndustryList(), // 组织行业列表
        onGetTeamsSizeList(), // 组织规模
        onGetOrganizeDepartmentList(), // 部门列表
        // onGetOrganizeList() // 获取组织列表
      ]);
      console.log(result, "啊實打實");

      if (result) {
        // const apply: any = isDetail ? applyData.value : result[0];
        // 会员职务
        const apply: any = result[0];

        initFreeForm(apply); // 这里，如果用户未设置过自由表单，则拿默认的

        if (applyData.value) {
          // 编辑的时候
          initFreeDetail(apply); // 回显填充
        }

        onSetPeronForm(result, apply);
        // 出现过期时间故障问题
        onSetUnitForm(result, apply);
        // return;
        // console.log(apply)
        // if(!isDetail) {
        //     applyData.value = apply;
        // }
        console.log(result);
        isRefreshNeeded.value = true;
        resolve(apply);
      }
      reject();
    } catch (error) {
      reject();
    }
  });

const initFreeDetail = (apply: any) => {
  // 做一下回显处理
  if (applyData.value && applyData.value.type === 2) {
    console.log("个人");
    if (applyData.value.submit_data.free_form) {
      apply.personal_form = formDiff(applyData.value.submit_data.free_form, apply.personal_form);
    } else {
      // apply.personal_form =
      onFillFormReShow(apply.personal_form);
      console.log(apply.personal_form);
    }
  } else if (applyData.value && applyData.value.type === 1) {
    // apply.team_form = applyData.value.submit_data.free_form;
    // onFillFormReShow(apply.team_form);

    if (applyData.value.submit_data.free_form) {
      console.log(applyData.value.submit_data.free_form);
      apply.team_form = formDiff(applyData.value.submit_data.free_form, apply.team_form);
      console.log(apply.team_form);
    } else {
      // apply.personal_form =
      onFillFormReShow(apply.team_form);
      console.log(apply.team_form);
    }
  }
};

// 回填详情里面的数据
const onFillFormReShow = (result: Array<any>) => {
  if (result && result.length > 0) {
    const baseList = result.filter((v: any) => v.type === "BaseInfoPolitics");
    const origin_data = applyData.value;
    console.log(origin_data);
    console.log(baseList);
    baseList.map((v: any) => {
      // 会员级别的设置
      let origin: any = null;
      origin = v.origin.find((or: any) => or.vModel === "memberLevel");
      if (origin) {
        // const levelItem = origin.options.find(
        //   (v: any) => v.id === origin.value
        // );
        origin.value = origin_data.level || origin_data.submit_data.level;
      }

      // 组织名称
      origin = v.origin.find((or: any) => or.vModel === "organizeName");
      if (origin) {
        // origin.value = [
        //   { teamFullName: origin_data.team_name, teamId: origin_data.teamId }
        // ];
        origin.value = origin_data.team_name;
      }

      // 会员编号
      origin = v.origin.find((or: any) => or.vModel === "memberNum");
      if (origin) {
        origin.value = origin_data.submit_data.no;
      }

      // 推荐人
      origin = v.origin.find((or: any) => or.vModel === "reference");
      if (origin) {
        origin.value = origin_data.submit_data.referrer;
      }

      origin = v.origin.find((or: any) => or.vModel === "referenceUnit");
      if (origin) {
        origin.value = origin_data.submit_data.referrer_unit;
      }

      // avatar头像
      origin = v.origin.find((or: any) => or.vModel === "logo");
      if (origin && origin_data.avatar) {
        const origin_name = origin_data.avatar
          ? origin_data.avatar.substring(origin_data.avatar.lastIndexOf("/") + 1)
          : "";
        origin.value = [
          {
            file_name: origin_data.avatar,
            file_name_short: origin_name,
            original_name: origin_name,
          },
        ];
      }

      // 手机
      origin = v.origin.find((or: any) => or.vModel === "phone");
      if (origin) {
        origin.code_value = Number(origin_data.telCode);
        origin.value = origin_data.telephone;
        // origin.disabled = true;
      }

      // 代表人姓名
      origin = v.origin.find((or: any) => or.vModel === "name");
      if (origin) origin.value = origin_data.name;

      // 到期时间
      origin = v.origin.find((or: any) => or.vModel === "expireTime");
      if (origin) {
        origin.value = origin_data.expire_time || origin_data.submit_data.expire_time;
        // origin.is_expire_value = !!origin_data.submit_data.no_expire;
        origin.is_expire_value = !!origin_data.no_expire;
      }

      // 入会时间
      origin = v.origin.find((or: any) => or.vModel === "joinTime");
      if (origin) origin.value = origin_data.join_time;

      // 邮箱
      origin = v.origin.find((or: any) => or.vModel === "email");
      if (origin) {
        origin.value = origin_data.submit_data.email;
      }

      // 部门
      origin = v.origin.find((or: any) => or.vModel === "department");
      if (origin) {
        origin.value = origin_data.idDepartment || origin_data.submit_data.idDepartment;
      }

      // 组织logo
      //   origin = v.origin.find((or: any) => or.vModel === "organizeLogo");
      //   if (origin && origin.value && origin.value.length > 0) {
      //     origin.value = origin.value[0].file_name;
      //   }

      // 组织类型
      origin = v.origin.find((or: any) => or.vModel === "organizeType");
      if (origin) {
        origin.value = origin_data.submit_data.team_type;
      }

      // 所在行业
      origin = v.origin.find((or: any) => or.vModel === "industryType");
      if (origin) {
        origin.value = origin_data.submit_data.industry || undefined;
      }

      // 组织规模
      origin = v.origin.find((or: any) => or.vModel === "organizeScale");
      if (origin) {
        origin.value = origin_data.submit_data.size || undefined;
      }
      // 组织地址
      origin = v.origin.find((or: any) => or.vModel === "organizeAddress");
      if (origin) {
        // origin.value =
        //   origins.submit_data.country +
        //   origins.submit_data.province +
        //   origins.submit_data.city +
        //   origins.submit_data.district;
      }
      return v;
    });
  }
};

const onSetPeronForm = (result: Array<any>, apply: any) => {
  // const memberLevelOptions = result[1];
  console.log(apply.personal_form, "gggg");
  if (apply.personal_form && apply.personal_form.length > 0) {
    const baseList = apply.personal_form.filter((v: any) => v.type === "BaseInfoPolitics");
    baseList.map(async (v: any) => {
      // 会员级别的设置
      let origin: any = null;
      // origin = v.origin.find((or: any) => or.vModel === "memberLevel");
      // if (origin) {
      //   origin.options = memberLevelOptions;
      //   // 如果会员级别被隐藏或者停用了，那么就会屏蔽掉
      //   if (!origin.options.some((v) => v.id === origin.value)) {
      //     origin.value = null;
      //   }
      // }

      // 手机
      origin = v.origin.find((or: any) => or.vModel === "phone");
      if (origin) {
        // origin.code = 86;
        origin.code_value = origin.code_value ? origin.code_value : 86;
        origin.options = optionsCodeList;
        if (applyData.value) {
          origin.disabled = true;
        }
      }
      console.log(origin);

      // 姓名
      origin = v.origin.find((or: any) => or.vModel === "name");
      if (origin) {
        origin.disabled = false;
        origin.value = applyData.value?.name;
      }

      // 邮箱 2023-11-8 商协会1.2
      origin = v.origin.find((or: any) => or.vModel === "email");
      if (origin) {
        if (applyData.value) {
          origin.value = applyData.value.email;
          // origin.disabled = false;
        }
      }

      // 入会时间
      origin = v.origin.find((or: any) => or.vModel === "joinTime");
      if (origin) origin.value = applyData.value?.join_time;

      // 过期时间
      origin = v.origin.find((or: any) => or.vModel === "expireTime");
      if (origin) {
        origin.value = applyData.value?.submit_data.expire_time;
        // origin.is_expire_value = origin.is_expire_value
        //   ? origin.is_expire_value
        //   : false;
        origin.disabled = false;

        origin.is_expire_value = !!applyData.value?.no_expire || false;
        // origin.is_expire_value = !!applyData.value?.no_expire || false;
      }

      // 部门选择
      // origin = v.origin.find((or: any) => or.vModel === "department");
      // if (origin) {
      //   // origin.code = 86;
      //   origin.options = result[4];
      //   origin.disabled = false;
      //   departmentListData.value = result[4];
      // }
      // 部门选择
      origin = v.origin.find((or: any) => or.vModel === "department");
      if (origin) {
        // origin.code = 86;
        origin.options = result[3];
        origin.position =
          applyData.value && applyData.value.departments.length > 0
            ? await returnConstructDeparts(
              applyData.value.departments.map((v) => ({
                departmentId: v.id_department,
                jobId: v.id_job,
                uuid: uuidv4(),
                jobDatas: [],
              })),
            )
            : [
              {
                departmentId: undefined,
                jobId: undefined,
                uuid: uuidv4(),
                jobDatas: [],
              },
            ]; // 部门/岗位信息
        origin.disabled = false;
        origin.name = "部门";
        departmentListData.value = result[3];
      }

      // 头像 2023-11-8 商协会1.2
      origin = v.origin.find((or: any) => or.vModel === "logo");
      if (origin) {
        const origin_name =
          applyData.value && applyData.value.avatar
            ? applyData.value.avatar.substring(applyData.value.avatar.lastIndexOf("/") + 1)
            : "";
        origin.value =
          applyData.value && applyData.value.avatar
            ? [
              {
                file_name: applyData.value.avatar,
                file_name_short: origin_name,
                original_name: origin_name,
              },
            ]
            : [];
      }

      // 名录照片 2024-07-05 1.4.2
      origin = v.origin.find((or: any) => or.vModel === "nameLogo");
      if (origin) {
        console.log(applyData.value)
        // origin.value = origin.value ? origin.value : [];
        // if(!(origin.value && origin.value?.length > 0)) {
        //   origin.value = applyData.value?.submit_data?.directory_image_values || [];
        // }
        // if (applyData.value?.submit_data?.directory_image_values?.length > 0) {
        //   origin.value = applyData.value?.submit_data?.directory_image_values
        // } else
        if (applyData.value?.directory_image) {
          const nameLogoSrc = applyData.value?.directory_image;
          const namelogoRes: any = /\.([0-9a-z]+)(?:[\?#]|$)/i.exec(nameLogoSrc);
          const origin_name = nameLogoSrc ? nameLogoSrc.substring(nameLogoSrc.lastIndexOf('/') + 1) : '';
          const ck = [
            {
              file_name: nameLogoSrc,
              file_name_short: origin_name,
              original_name: origin_name,
              size: 0,
              type: namelogoRes?.length > 1 ? namelogoRes[1] : '',
            },
          ];
          console.log(ck);
          origin.value = ck;
        } else {
          origin.value = [];
        }

        // 名录照片 2.4.0强制非必填
        origin.required = false;
      }

      console.log(origin);

      // 兴趣爱好， 2.4.0设置为非必填
      origin = v.origin.find((or: any) => or.vModel === "interest");
      if (origin) {
        origin.required = false;
        origin.value = applyData.value?.hobby || applyData.value?.submit_data.hobby;
      }

      return v;
    });
  }
};

// 设置单位
const onSetUnitForm = (result: Array<any>, apply: any) => {
  // const memberLevelOptions = result[1];
  if (apply.team_form && apply.team_form.length > 0) {
    const baseList = apply.team_form.filter((v: any) => v.type === "BaseInfoPolitics");
    baseList.map(async (v: any) => {
      // 会员级别的设置
      let origin = null;
      const origins_arr = lodash.cloneDeep(v.origin);

      // origin = origins_arr.find((or: any) => or.vModel === "memberLevel");
      // if (origin) {
      //   origin.options = memberLevelOptions;
      //   // 如果会员级别被隐藏或者停用了，那么就会屏蔽掉
      //   if (!memberLevelOptions.some((v) => v.id === origin.value)) {
      //     // origin = Object.assign(origin, { value: null }); // 设为undefined无法正常回显
      //     origin.value = null;
      //   }
      //   console.log(memberLevelOptions.some((v) => v.id === origin.value));
      //   console.log(result[1]);

      //   console.log(origin);
      //   console.log(origin.value);
      // }
      // console.log(origin)

      // 组织名称
      origin = origins_arr.find((or: any) => or.vModel === "organizeName");
      if (origin) {
        // origin.value = {行业
        //     teamLogo: memberStore.linkInfo?.logo,
        //     teamId: memberStore.teamId,
        //     teamFullName: memberStore.linkInfo?.team
        // }
        // origin.placeholder = '请输入单位会员的组织名称';
        origin.value = applyData.value?.team_name || (lodash.isObject(origin.value) ? origin.value.teamFullName : origin.value);
      }

      // 所在单位岗位
      origin = origins_arr.find((or: any) => or.vModel === "unitJob");
      if (origin) {
        // origin.placeholder = '请输入代表人在该单位会员的岗位';
        origin.placeholder = "请输入";
        origin.value = applyData.value?.job || applyData.value?.submit_data?.job;
      }


      // 兴趣爱好， 2.4.0设置为非必填
      origin = origins_arr.find((or: any) => or.vModel === "interest");
      if (origin) {
        origin.required = false;
        origin.value = applyData.value?.hobby || applyData.value?.submit_data.hobby;
      }
      // 业务范围， 2.4.0设置为非必填
      origin = origins_arr.find((or: any) => or.vModel === "business");
      if (origin) {
        origin.required = false;
        origin.value = applyData.value?.business || applyData.value?.submit_data.business;
      }


      // 代表人姓名
      origin = origins_arr.find((or: any) => or.vModel === "name");
      if (origin) {
        if (applyData.value) {
          origin.isShow = true;
        } else {
          origin.isShow = false;
        }

        origin.disabled = false;
        origin.value = applyData.value?.name;
      }

      // origin = origins_arr.find((or: any) => or.vModel === "memberLevel");
      // if (origin) {
      //   // origin.disabled = false;
      //   origin.value = applyData.value?.level || origin.value;
      // }

      // 手机
      origin = origins_arr.find((or: any) => or.vModel === "phone");
      if (origin) {
        origin.code_value = origin.code_value ? origin.code_value : 86;
        origin.options = optionsCodeList;
        if (applyData.value) {
          origin.disabled = true;
        }
        origin.value = applyData.value?.telephone || applyData.value?.submit_data.telephone;
      }

      // 关联代表人
      origin = origins_arr.find((or: any) => or.vModel === "relateRespector");
      if (origin) {
        origin.value = origin.value ? origin.value : 1;
        // origin.code_name = 'ff';
        origin.name = "代表人姓名";
      }

      // 邮箱 2023-11-8 商协会1.2
      origin = origins_arr.find((or: any) => or.vModel === "email");
      if (origin) {
        if (applyData.value) {
          origin.value = applyData.value.email;
          origin.disabled = false;
        }
      }

      // 过期时间
      origin = origins_arr.find((or: any) => or.vModel === "expireTime");
      if (origin) {
        // origin.is_expire_value = origin.is_expire_value
        //   ? origin.is_expire_value
        //   : false;
        origin.value = applyData.value?.expire_time || origin.value;
        origin.disabled = false;
        origin.is_expire_value = !!applyData.value?.no_expire || false;
      }

      origin = origins_arr.find((or: any) => or.vModel === "joinTime");
      if (origin) origin.value = applyData.value?.join_time || origin.value;

      // console.log(origin)
      // 行业选择
      origin = origins_arr.find((or: any) => or.vModel === "industryType");
      if (origin) {
        // origin.code = 86;
        origin.options = result[1];
        industryListData.value = result[1];
        origin.value = applyData.value?.industry || applyData.value?.submit_data.industry;
      }

      // 规模选择
      origin = origins_arr.find((or: any) => or.vModel === "organizeScale");
      if (origin) {
        origin.options = result[2];
        sizeListData.value = result[2];
        origin.value = applyData.value?.size || applyData.value?.submit_data.size;
      }

      // 部门选择 2023-11-8 商协会1.2
      origin = origins_arr.find((or: any) => or.vModel === "department");
      if (origin) {
        // origin.code = 86;
        origin.options = result[3];
        origin.position =
          applyData.value && applyData.value.departments.length > 0
            ? await returnConstructDeparts(
              applyData.value.departments.map((v) => ({
                departmentId: v.id_department,
                jobId: v.id_job,
                uuid: uuidv4(),
                jobDatas: [],
              })),
            )
            : [
              {
                departmentId: undefined,
                jobId: undefined,
                uuid: uuidv4(),
                jobDatas: [],
              },
            ]; // 部门/岗位信息
        origin.disabled = false;
        origin.name = "部门";
        departmentListData.value = result[3];

        console.log(origin.position, "departmentss");
      }

      // 组织类型选择
      origin = origins_arr.find((or: any) => or.vModel === "organizeType");
      if (origin) {
        origin.options = optionsOrganizeType;
        origin.value = applyData.value?.team_type || applyData.value?.submit_data.team_type;
      }

      // 组织简称
      origin = origins_arr.find((or: any) => or.vModel === "organizeAbbrName");
      if (origin) {
        origin.options = optionsOrganizeType;
        origin.value = applyData.value?.team_short_name || applyData.value?.submit_data.team_short_name;
      }

      // 地址
      origin = origins_arr.find((or: any) => or.vModel === "organizeAddress");
      if (origin) {
        // origin.address_value = ;
        console.log(origin);
        origin.value = applyData.value?.address_code || applyData.value?.submit_data.address_code;
        origin.address_value = applyData.value?.address || applyData.value?.submit_data.address;
      }

      // 组织头像
      // origin = v.origin.find((or: any) =>
      //   ["logo", "organizeLogo"].includes(or.vModel));
      origin = origins_arr.find((or: any) => or.vModel === "organizeLogo");
      if (origin) {

        const organizeLogoSrc = applyData.value?.team_logo || applyData.value?.submit_data?.team_logo;

        if (organizeLogoSrc) {
          const logoRes: any = /\.([0-9a-z]+)(?:[\?#]|$)/i.exec(organizeLogoSrc);
          const origin_name = organizeLogoSrc ? organizeLogoSrc.substring(organizeLogoSrc.lastIndexOf('/') + 1) : '';
          const ck = [
            {
              file_name: organizeLogoSrc,
              file_name_short: origin_name,
              original_name: origin_name,
              size: 0,
              type: logoRes?.length > 1 ? logoRes[1] : '',
            },
          ];
          console.log(ck);
          origin.value = ck;
        } else {
          origin.value = [];
        }
      }

      // 头像 2023-11-8 商协会1.2
      origin = origins_arr.find((or: any) => or.vModel === "logo");
      if (origin) {
        const origin_name =
          applyData.value && applyData.value.avatar
            ? applyData.value.avatar.substring(applyData.value.avatar.lastIndexOf("/") + 1)
            : "";

        origin.value =
          applyData.value && applyData.value.avatar
            ? [
              {
                file_name: applyData.value.avatar,
                file_name_short: origin_name,
                original_name: origin_name,
              },
            ]
            : [];
        console.log(origin);
      }

      // 名录照片 2024-07-05 1.4.2
      // 名录照片回显 2024-09-12
      origin = origins_arr.find((or: any) => or.vModel === "nameLogo");
      if (origin) {
        console.log(applyData.value)
        // origin.value = origin.value ? origin.value : [];
        // if(!(origin.value && origin.value?.length > 0)) {
        //   origin.value = applyData.value?.submit_data?.directory_image_values || [];
        // }
        if (applyData.value?.submit_data?.directory_image_values?.length > 0) {
          origin.value = applyData.value?.submit_data?.directory_image_values
        } else if (applyData.value?.directory_image) {
          const nameLogoSrc = applyData.value?.directory_image;
          const namelogoRes: any = /\.([0-9a-z]+)(?:[\?#]|$)/i.exec(nameLogoSrc);
          const origin_name = nameLogoSrc ? nameLogoSrc.substring(nameLogoSrc.lastIndexOf('/') + 1) : '';
          const ck = [
            {
              file_name: nameLogoSrc,
              file_name_short: origin_name,
              original_name: origin_name,
              size: 0,
              type: namelogoRes?.length > 1 ? namelogoRes[1] : '',
            },
          ];
          console.log(ck);
          origin.value = ck;
        } else {
          origin.value = [];
        }

        // 名录照片 2.4.0强制非必填
        origin.required = false;
      }

      // console.log(origin);
      v.origin = origins_arr;
      return v;
    });
  }
};

const returnConstructDeparts = async (positions) => {
  if (positions && positions.length > 0) {
    const arr = [];
    positions.forEach((v) => {
      // v.jobDatas = v.jobId ? [{ title: v.jobName, id: v.jobId }] : [];
      // 直接上级对象
      // v.jobDatas数据，则需要根据部门id去请求数据填充
      const getJobs = getJobListData(v.departmentId, v);

      // return {
      // 	departmentId: v.departmentId,
      // 	jobDatas: v.jobDatas,
      // 	uuid: uuidv4(),
      // 	jobId: v.jobId ? v.jobId : undefined,
      // };
      // arr.push({
      // 	departmentId: v.departmentId,
      // 	jobDatas: v.jobDatas,
      // 	uuid: uuidv4(),
      // 	jobId: v.jobId ? v.jobId : undefined,
      // });
      arr.push(getJobs);
    });
    const result = await Promise.all(arr);
    console.log(result);
    positions = positions.map((vs, index) => ({
      departmentId: vs.departmentId,
      jobDatas: vs.jobDatas,
      uuid: uuidv4(),
      jobId: vs.jobId ? vs.jobId : undefined,
    }));
  }
  console.log(positions);
  return positions;
};

// 获取岗位列表
// const jobDatas = ref([]);
const getJobListData = (departmentID, position) => {
  const params = {
    departmentId: departmentID,
  };
  let res = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      res = await getOrganizeDepartmentJobsListAxios(params);
      res = getResponseResult(res);
      if (!res) {
        reject();
        return;
      }
      console.log(res);
      // position.jobDatas = res.data;
      const { jobs } = res.data[0];

      position.jobDatas = jobs && jobs.length > 0 ? jobs.filter((v) => v.status !== 0) : [];
      resolve(res.data);
    } catch (error) {
      MessagePlugin.error(error.message);
      reject();
    }
  });
};

const selectOrganizeModalRef: Ref<any> = ref(null);
const currentE = ref(null);
const controlsEventRun_unit = (e: any) => {
  console.log(e, "ff");
  currentE.value = e;
  // if (e && e.vModel === "organizeName") {
  //   // 组织名称
  //   onGetOrganizeList().then((res) => {
  //     selectOrganizeModalRef.value.onOpen(res, e);
  //   });
  // }

  // 入会时间
  if (isArray(e) && e[0].vModel === "joinTime") {
    console.log("joinTime");
    // 搜索过期时间
    const expireTime = e[1].find((v) => v.vModel === "expireTime");
    if (expireTime && !expireTime.is_expire_value && expireTime.value) {
      const endDay = dayjs(expireTime.value);
      const startDay = dayjs(e[0].value);
      const diffInDays = endDay.diff(startDay, "day");
      console.log(diffInDays);
      if (diffInDays < 0 || diffInDays === 0) {
        MessagePlugin.error("入会时间不能小于过期时间");
        e[0].value = "";
      }
    }
    runtimeUnitRef.value.clearValidate(e[0].id);
  } else if (isArray(e) && e[0].vModel === "expireTime") {
    if (!e[0].is_expire_value) {
      // 过期时间不能小于入会时间
      const joinTime = e[1].find((v) => v.vModel === "joinTime");
      if (joinTime && joinTime.value) {
        const endDay = dayjs(e[0].value);
        const startDay = dayjs(joinTime.value);
        const diffInDays = endDay.diff(startDay, "day");
        if (diffInDays < 0 || diffInDays === 0) {
          MessagePlugin.error("入会时间不能小于过期时间");
          e[0].value = "";
        }
      }
    }
    runtimeUnitRef.value.clearValidate(e[0].id);
  } else if (!isArray(e) && e.vModel === "relateRespector") {
    // 关联代表人的特殊处理
    console.log("代表人选人", e.value);
    onAddManage();
  } else if (!isArray(e) && e) {
    // if(e)
    runtimeUnitRef.value.clearValidate(e.id);
  }
};

const controlsEventRun_person = (e: any) => {
  console.log(e);

  // 入会时间
  if (isArray(e) && e[0].vModel === "joinTime") {
    console.log("joinTime");
    // 搜索过期时间
    const expireTime = e[1].find((v) => v.vModel === "expireTime");
    if (expireTime && !expireTime.is_expire_value && expireTime.value) {
      const endDay = dayjs(expireTime.value);
      const startDay = dayjs(e[0].value);
      const diffInDays = endDay.diff(startDay, "day");
      console.log(diffInDays);
      if (diffInDays < 0 || diffInDays === 0) {
        MessagePlugin.error("入会时间不能小于过期时间");
        e[0].value = "";
      }
    }
  } else if (isArray(e) && e[0].vModel === "expireTime" && !e[0].is_expire_value) {
    // 过期时间不能小于入会时间
    const joinTime = e[1].find((v) => v.vModel === "joinTime");
    if (joinTime && joinTime.value) {
      const endDay = dayjs(e[0].value);
      const startDay = dayjs(joinTime.value);
      const diffInDays = endDay.diff(startDay, "day");
      if (diffInDays < 0 || diffInDays === 0) {
        MessagePlugin.error("入会时间不能小于过期时间");
        e[0].value = "";
      }
    }
  }
};

const loading = ref(false);
// 单位申请入会
const releaseRun_unit = async (data: any) => {
  console.log(data);
  if (data && data.free_form && data.free_form.length > 0) {
    const params = onConstructorParams(lodash.cloneDeep(data.free_form));
    let result = null;
    try {
      loading.value = true;
      if (applyData.value) {
        result = await patchRegularAxios(applyData.value.id, params, currentTeamId.value);
      } else {
        result = await createRegularAxios(params, currentTeamId.value);
      }

      console.log(result);
      result = getResponseResult(result);
      onClose();
      MessagePlugin.success("操作成功");
      emits("reload");
    } catch (error) {
      // resetViewOptions(data.free_form); // 回显数据
      const errMsg: any = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
      // if (errMsg && errMsg.response?.status) {
      //   errorHandlerFilter(errMsg.response?.status);
      // }
    }
    loading.value = false;
  }
};

const releaseRun_person = async (data: any) => {
  console.log(data);
  if (data && data.free_form && data.free_form.length > 0) {
    const params: any = onConstructorParams(lodash.cloneDeep(data.free_form));
    params.submit_data.type = 2;
    let result = null;
    loading.value = true;

    try {
      if (applyData.value) {
        result = await patchRegularAxios(applyData.value.id, params, currentTeamId.value);
      } else {
        result = await createRegularAxios(params, currentTeamId.value);
      }
      console.log(result);
      console.log(result);
      result = getResponseResult(result);
      onClose();
      MessagePlugin.success("操作成功");

      emits("reload");
    } catch (error) {
      // resetViewOptions(data.free_form); // 回显数据
      const errMsg: any = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
    }
    loading.value = false;
  }
};

// 构造单位申请的参数
const onConstructorParams = (freeForm: Array<any>) => {
  const params = {
    type: 1, //  1:单位会员 2：个人会员
    team_name: "", // 组织名称
    teamId: 0,
    level: undefined, // 会员级别
    level_name: "", // 会员级别名称

    name: "", // 代表人姓名/姓名
    avatar: "", // 代表人头像/头像
    telCode: 86, // 手机区号
    telephone: "", //  手机号
    email: "", // 邮箱
    idDepartment: "", // 部门
    department_name: "", // 部门名称

    join_time: "", // 入会时间
    expire_time: "", // 到期时间
    no_expire: "", // 长期有效： 1:长期有效 0：过期

    no: "", // 会员编号：
    referrer: "", // 推荐人：
    team_logo: "", // 组织logo：
    team_short_name: "", // 组织简称：
    team_type: null, // 组织类型： 0：其他，1：企业，2：商协会，3：个体户
    industry: undefined, // 所在行业：
    industry_text: "", // 所在行业文本：
    size: undefined, // 组织规模：
    size_text: "", // 组织规模文本：
    // 组织地址：自行定义，推荐国家：country，省：province，市：city，区：district，详细地址：address
    country: "",
    province: "",
    city: "",
    district: "",
    address: "", // 详细地址
    address_code: null as any,
    // 商协会1.1新增字段
    relation_type: 1, // 关联代表人（新）
    relation_id: 0, // 选择代表人ID（新）
    job: "", // 所在单位岗位（新）
    referrer_unit: "", // 推荐人单位（新）
    departments: [], // （2023.11.7新增）array（object） {"id_department": 1, "id_job": 0}
    // 会员状态：状态，1：正常，2：已到期，3：已退会
    // 激活状态：激活状态，1：已激活，2：未激活
    // 创建时间：created_at
    // 更新时间：updated_at
    // 更新人/操作人名称: operator_name
    // 申请时间：apply_time
    // 申请状态:status 1：待审核，2：已入会，3：已驳回
    // pay_status 1：未缴费，2：已缴费，3：无需缴费
    directory_image: '', // 名录照片
    hobby: '',
    business: '',
    directory_image_values: [],
    free_form: freeForm
  };
  const baseList = freeForm.filter((v: any) => v.type === "BaseInfoPolitics");
  // eslint-disable-next-line complexity
  baseList.map((v: any) => {
    // 会员级别的设置
    let origin: any = null;
    origin = v.origin.find((or: any) => or.vModel === "memberLevel");

    if (origin) {
      console.log(origin.value);
      params.level = origin.value;
      const levelItem = origin.options.find((v: any) => v.id === origin.value);
      params.level_name = levelItem ? levelItem.level_name : "";
    }

    // 组织名称
    origin = v.origin.find((or: any) => or.vModel === "organizeName");
    if (origin && origin.value) {
      // params.team_name = origin.value.teamFullName;

      params.team_name = origin.value;
      // params.teamId = origin.value.teamId;
    }

    // 入会时间
    origin = v.origin.find((or: any) => or.vModel === "joinTime");
    if (origin) {
      params.join_time = origin.value;
    }

    // 到期时间
    origin = v.origin.find((or: any) => or.vModel === "expireTime");
    if (origin) {
      params.expire_time = origin.value;
      params.no_expire = origin.is_expire_value ? 1 : 0;
    }

    // avatar头像
    origin = v.origin.find((or: any) => or.vModel === "logo");
    if (origin && origin.value && origin.value.length > 0) {
      params.avatar = origin.value[0].file_name;
    }

    // 名录照片
    origin = v.origin.find((or: any) => or.vModel === "nameLogo");
    if (origin && origin.value && origin.value.length > 0) {
      params.directory_image = origin.value[0].file_name;
      params.directory_image_values = origin.value;
    }
    // 兴趣爱好
    origin = v.origin.find((or: any) => or.vModel === "interest");
    if (origin) {
      params.hobby = origin.value;
    }

    // 业务范围
    origin = v.origin.find((or: any) => or.vModel === "business");
    if (origin) {
      params.business = origin.value;
    }

    // 手机号、手机区号
    origin = v.origin.find((or: any) => or.vModel === "phone");
    if (origin) {
      params.telCode = origin.code_value;
      params.telephone = origin.value;
    }

    // 邮箱
    origin = v.origin.find((or: any) => or.vModel === "email");
    if (origin) {
      params.email = origin.value;
    }

    // 会员编号
    origin = v.origin.find((or: any) => or.vModel === "memberNum");
    if (origin) {
      params.no = origin.value;
    }

    // 推荐人姓名
    origin = v.origin.find((or: any) => or.vModel === "reference");
    if (origin) {
      params.referrer = origin.value;
    }
    // 推荐人单位 - 新
    origin = v.origin.find((or: any) => or.vModel === "referenceUnit");
    if (origin) {
      params.referrer_unit = origin.value;
    }

    // 关联代表人 - 新
    origin = v.origin.find((or: any) => or.vModel === "relateRespector");
    if (origin) {
      params.relation_type = origin.value;
      params.relation_id = origin.code_value ? origin.code_value.idStaff : 0; // 待完善
    }

    // 代表人姓名，
    origin = v.origin.find((or: any) => or.vModel === "name");
    if (origin) {
      params.name = origin.value;
    }

    // 所在单位岗位 - 新
    origin = v.origin.find((or: any) => or.vModel === "unitJob");
    if (origin) {
      params.job = origin.value;
    }

    // 组织logo
    origin = v.origin.find((or: any) => or.vModel === "organizeLogo");
    if (origin && origin.value && origin.value.length > 0) {
      params.team_logo = origin.value[0].file_name;
    }

    // 组织简称
    origin = v.origin.find((or: any) => or.vModel === "organizeAbbrName");
    if (origin) {
      params.team_short_name = origin.value;
    }

    // 组织类型
    origin = v.origin.find((or: any) => or.vModel === "organizeType");
    if (origin) {
      params.team_type = origin.value || undefined;
    }

    // 所在行业
    origin = v.origin.find((or: any) => or.vModel === "industryType");
    if (origin) {
      params.industry = origin.value || undefined;
      const industryItem: any = getChidlren(origin.value, origin.options);
      params.industry_text = industryItem?.name;
      origin.options = [];
    }

    // 所在部门
    origin = v.origin.find((or: any) => or.vModel === "department");
    console.log(origin);
    if (origin) {
      // params.idDepartment = origin.value || applyData.value?.idDepartment;
      // console.log("lala: ", params.idDepartment);

      // const departmentItem: any = getChidlren(origin.value, origin.options);
      // params.department_name = departmentItem?.name;
      origin.options = [];

      if (props.isMember) {
        params.departments = undefined;
      } else {
        params.departments = origin.position
          .filter((v) => v.departmentId)
          .map((v) => ({
            id_department: v.departmentId,
            id_job: v.jobId,
          }));
      }
    }

    // 组织规模
    origin = v.origin.find((or: any) => or.vModel === "organizeScale");
    if (origin) {
      params.size = origin.value;
      const sizeItem: any = origin.options.find((v: any) => v.id === origin.value);
      params.size_text = sizeItem?.name;
      origin.options = [];
    }

    // 组织地址
    origin = v.origin.find((or: any) => or.vModel === "organizeAddress");
    if (origin) {
      params.address_code = origin.value;
      // address_code 拆分出国家省市区
      if (params.address_code && params.address_code.select) {
        params.address_code.select.forEach((item: any, itemIndex: number) => {
          if (itemIndex === 0) {
            params.country = item.name;
          } else if (itemIndex === 1) {
            params.province = item.name;
          } else if (itemIndex === 2) {
            params.city = item.name;
          } else if (itemIndex === 3) {
            params.district = item.name;
          }
        });
      }
      params.address = origin.address_value;
    }

    return v;
  });
  console.log(params);
  const resultParams = {
    type: currentTab.value === "unit" ? 1 : 2,
    submit_data: params,
    is_member: props.isMember, // 是否是会员中心
    is_remind: props.isMember === 1 ? 0 : applyData.value ? 0 : Number(isChecked.value), // 是否短信通知
  };
  console.log(resultParams);
  return resultParams;
};

// 回显行业、规模
const resetViewOptions = (freeForm: Array<any>) => {
  console.log(industryListData.value, departmentListData.value, sizeListData.value);
  const baseList = freeForm.filter((v: any) => v.type === "BaseInfoPolitics");
  // eslint-disable-next-line complexity
  baseList.map((v: any) => {
    // 会员级别的设置
    let origin: any = null;
    // 所在行业
    origin = v.origin.find((or: any) => or.vModel === "industryType");
    if (origin) {
      origin.options = industryListData.value;
    }

    // 所在部门
    origin = v.origin.find((or: any) => or.vModel === "department");
    if (origin) {
      origin.options = departmentListData.value;
    }

    // 组织规模
    origin = v.origin.find((or: any) => or.vModel === "organizeScale");
    if (origin) {
      origin.options = sizeListData.value;
    }
    return v;
  });
};

const onSubmit = debounce(() => {
  if (currentTab.value === "person") {
    runtimePersonRef.value.submitRun();
  } else if (currentTab.value === "unit") {
    runtimeUnitRef.value.submitRun();
  }
}, 200);

// 选择组织
const onSelectedOrganize = (e: any, origin: any) => {
  if (e && origin) {
    origin.value = e;
    onGetOrganizeDetail(e.teamId).then((res) => {
      onSetTeamsDetail(res); // 设置组织详细信息
    });
    // emits('reload')
  }
};

const optionsMembers = ref([]);
const getMemberRelationsList = () => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await getMemberRelationAxios2({}, currentTeamId.value);
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      optionsMembers.value = result.data?.list
        ? result.data.list.map((v) => {
          v.idStaff = v.id;
          return v;
        })
        : [];
      resolve("success");
    } catch (error) {
      reject();
      const errMsg = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
    }
  });
};
const selectMemberModalRef = ref(null);
const onAddManage = () => {
  getMemberRelationsList().then(() => {
    selectMemberModalRef.value.onOpen();
  });
};

// 添加成员
const onListenMembers = async (arr) => {
  console.log(arr);
  console.log(currentE.value);
  if (arr && arr.length > 0) {
    const result = optionsMembers.value.find((v) => v.idStaff === arr[0]);
    if (currentE.value) currentE.value.code_value = result;
    onSetRespectorInfo(result);
    runtimeUnitRef.value.clearValidate(currentE.value.id);
  }
};

// 设置关联代表人相关信息
// 姓名、头像、手机号、邮箱、部门
const onSetRespectorInfo = (info: any) => {
  console.log(info);
  if (controls_unit.value && info) {
    const baseList = controls_unit.value.filter((v: any) => v.vModel === "baseInfoPolitics");
    baseList.map((v: any) => {
      // 会员级别的设置
      let origin: any = null;
      origin = v.origin.find((or: any) => or.vModel === "name");
      if (origin) origin.value = info.name;
      origin = v.origin.find((or: any) => or.vModel === "logo");
      if (origin) {
        if (info.avatar) {
          const origin_name = info.avatar
            ? info.avatar.substring(info.avatar.lastIndexOf("/") + 1)
            : "";

          origin.value = [
            {
              file_name: info.avatar,
              file_name_short: origin_name,
              original_name: origin_name
            }
          ];
        } else {
          origin.value = [];
        }
      }
      // 手机
      origin = v.origin.find((or: any) => or.vModel === "phone");
      if (origin) {
        origin.code_value = Number(info.telCode);
        origin.value = info.telephone;
        // origin.disabled = true;
      }

      // 邮箱
      origin = v.origin.find((or: any) => or.vModel === "email");
      if (origin) origin.value = info.email;

      // 部门
      origin = v.origin.find((or: any) => or.vModel === "department");
      if (origin && info.departments) {
        origin.position = info.departments.map((v) => ({
          departmentId: v.id_department,
          jobId: v.id_job,
          uuid: uuidv4(),
          jobDatas: [],
        }));
      }

      return v;
    });
  }
};

// 获取组织详情
const onGetOrganizeDetail = (teamId: any) => {
  // getMemberJobsListAxios
  let result = null;

  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await getTeamsDetailAxios({ teamId });
      console.log(result);
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }

      resolve(result.data);
    } catch (error) {
      const errMsg: any = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
      // eslint-disable-next-line prefer-promise-reject-errors
      reject();
    }
  });
};

const onSetTeamsDetail = (info: any) => {
  if (controls_unit.value) {
    const baseList = controls_unit.value.filter((v: any) => v.vModel === "organizeInfoMember");
    console.log(baseList);
    console.log(info);
    if (!(baseList && baseList.length > 0)) return;
    baseList.map((v: any) => {
      // 组织logo
      let origin = null;
      console.log(v.origin);
      origin = v.origin.find((or: any) => or.vModel === "organizeLogo" && or.isShow);
      if (origin && info.logo) {
        const logoRes: any = /\.([0-9a-z]+)(?:[\?#]|$)/i.exec(info.logo);

        const origin_name = info.logo ? info.logo.substring(info.logo.lastIndexOf("/") + 1) : "";

        origin.value = [
          {
            file_name: info.logo,
            file_name_short: origin_name,
            original_name: origin_name,
            size: 0,
            type: logoRes?.length > 1 ? logoRes[1] : "",
          },
        ];
      } else {
        origin.value = [];
      }

      // 组织简称
      origin = v.origin.find((or: any) => or.vModel === "organizeAbbrName" && or.isShow);
      console.log(origin);
      if (origin) {
        origin.value = info.title;
      }

      // 组织类型
      origin = v.origin.find((or: any) => or.vModel === "organizeType" && or.isShow);
      if (origin) {
        origin.value = info.type;
      }

      // 行业类型
      origin = v.origin.find((or: any) => or.vModel === "industryType" && or.isShow);
      if (origin) {
        origin.value = info.industry;
      }

      // 组织规模
      origin = v.origin.find((or: any) => or.vModel === "organizeScale" && or.isShow);
      if (origin) {
        origin.value = info.size;
      }

      // 组织地址
      origin = v.origin.find((or: any) => or.vModel === "organizeAddress" && or.isShow);
      if (origin) {
        origin.value = info.contact.address_code;
        origin.address_value = info.contact.address;
      }

      return v;
    });
  }
};

const initData = () => {
  controls_person.value = [];
  controls_unit.value = [];

  if (applyData.value && applyData.value.type === 2) {
    currentTab.value = "person";
  } else {
    currentTab.value = "unit";
  }

  industryListData.value = [];
  sizeListData.value = [];
  departmentListData.value = [];
};

// 编辑
const initEditForm = (data: any) => {
  // 这里要座一层逻辑
  let detailItem: any = data;
  // 激活状态，1：已激活，2：未激活
  if (detailItem.activate === 2) {
    // 未激活，读取data数据
    detailItem.submit_data = detailItem.data;
  }
};
const disabledVModels = new Set(["name", "unitJob", "logo", "nameLogo", "phone", "email", "interest"]);

const processControls = (controls, setDisabled) => {
  controls.forEach((dataVal) => {
    dataVal?.origin.forEach((e) => {
      if (setDisabled && disabledVModels.has(e?.vModel)) {
        e.disabled = true;
      } else if (!setDisabled && e?.vModel === "logo") {
        e.disabled = false;
      }
    });
  });
};
const onOpen = (data: any, currentMemberCard: any) => {
  // onGetMemberSetting().then((res: any) => {
  //   initFreeForm(res);
  //   controls_person.value = res.personal_form;
  //   controls_unit.value = res.team_form;
  //   visible.value = true;
  // });
  // 编辑的时候回显数据初步处理
  console.log(data);
  console.log(currentTeamId.value);
  isChecked.value = true;
  if (data) {
    initEditForm(data);
    applyData.value = data;
  } else {
    applyData.value = null;
  }
  initData();
  onGetSettings().then((res: any) => {
    console.log(data, "data啊啊啊啊啊啊啊啊啊啊啊");
    console.log(currentMemberCard, "currentMemberCard啊啊啊啊啊啊啊啊啊啊啊");
    controls_person.value = res.personal_form;
    controls_unit.value = res.team_form;
    // 自由表单表单数据
    const disabledVModels = new Set(["name", "unitJob", "logo", "nameLogo", "phone", "email", "interest"]);
    if (!route.path.includes("digitalPlatformIndex") && data?.activate === 2) {
      console.log("进入这里1号");

      controls_person.value.forEach((dataVal) => {
        if (dataVal?.origin) {

          dataVal?.origin.forEach((e) => {
            if (e?.vModel === "logo") {
              e.disabled = false;
            }
          });
        }
      });
      controls_unit.value.forEach((dataVal) => {
        if (dataVal?.origin) {

          dataVal?.origin.forEach((e) => {
            if (e?.vModel === "logo") {
              e.disabled = false;
            }
          });
        }
      });
    }
    if (!route.path.includes("digitalPlatformIndex") && data && [1, 2].includes(data?.status) && data?.activate === 1) {
      console.log("进入这里2号");
      console.log(controls_unit.value, "进入这里2号controls_person");
      console.log(controls_person.value, "进入这里2号controls_person");
      controls_unit.value.forEach((dataVal) => {
        console.log("邹334343");
        if (dataVal?.origin) {
          dataVal?.origin.forEach((e) => {
            console.log("435324邹334343");

            if (disabledVModels.has(e?.vModel)) {
              e.disabled = true;
            }
          });
        }
      });
      controls_person.value.forEach((dataVal) => {
        console.log("3222221435324邹334343");
        if (dataVal?.origin) {
          dataVal?.origin.forEach((e) => {
            if (disabledVModels.has(e?.vModel)) {
              e.disabled = true;
            }
          });
        }
      });
    }
    if (route.path.includes("digitalPlatformIndex")) {
      if (currentMemberCard.type == 1 && currentMemberCard.is_contact === 1) {
        controls_unit.value.forEach((dataVal) => {
          if (dataVal?.origin) {
            dataVal?.origin.forEach((e) => {
              if (disabledVModels.has(e?.vModel)) {
                e.disabled = true;
              }
            });
          }
        });
        controls_person.value.forEach((dataVal) => {
          if (dataVal?.origin) {
            dataVal?.origin.forEach((e) => {
              if (disabledVModels.has(e?.vModel)) {
                e.disabled = true;
              }
            });
          }
        });
      } else {
        controls_unit.value.forEach((dataVal) => {
          if (dataVal?.origin) {
            dataVal?.origin.forEach((e) => {
              if (disabledVModels.has(e?.vModel)) {
                if (e?.vModel === "phone") {
                  e.disabled = true;
                } else {
                  e.disabled = false;
                }
              }
            });
          }
        });
        controls_person.value.forEach((dataVal) => {
          if (dataVal?.origin) {

            dataVal?.origin.forEach((e) => {
              if (disabledVModels.has(e?.vModel)) {
                if (e?.vModel === "phone") {
                  e.disabled = true;
                } else {
                  e.disabled = false;
                }
              }
            });
          }

        });
      }
      console.log("进入这里3号");
    }
    console.log(controls_person.value, "过滤啊啊");
    console.log(controls_unit.value, "过滤123");

    visible.value = true;
  });
};
const onClose = () => {
  visible.value = false;
};

defineExpose({
  onOpen,
  onClose,
});
</script>
<style lang="less" scoped>
// @import "@renderer/views/engineer/less/common.less";

:deep(.t-steps--vertical).t-steps--dot-anchor .t-steps-item--finish .t-steps-item__icon {
  border-color: #c7c7c8;
  background: #c7c7c8;
}

:deep(.t-steps-item--finish) {
  &::before {
    border-right-color: #c7c7c8 !important;
    color: #c7c7c8 !important;
    left: 2.5px;
    top: 24px;
  }
}

.sports {
  &-title {
    font-size: 14px;

    font-weight: 400;

    color: #717376;
  }
}

.toTitle {
  font-size: 14px;

  font-weight: 400;

  // color: #13161b;
}

.toContent {
  font-size: 14px;

  font-weight: 400;
}

.toFooter {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .check {
    color: var(--checkbox-kyy-color-checkbox-text-default, #1a2139);

    font-size: 14px;
    font-style: normal;
    font-weight: 400;

    display: flex;

    align-items: center;

    .tap {
      // line-height: 25px; /* 157.143% */
      display: flex;

      align-items: center;
      gap: 4px;

      .iconhelp {
        font-size: 20px;
        color: #828da5;
      }
    }
  }
}

.operates {
  display: flex;
  justify-content: flex-end;
}

.drawerSet {

  // width: 720px;
  &-body {}

  //   .t-drawer__content-wrapper {
  //     width: 720px !important;
  //   }

  :deep(.t-drawer__header) {
    border-bottom: 0 !important;
  }

  // :deep(.t-drawer__footer) {
  //   padding: 0px!important;
  // }
}

// :deep(.t-drawer__header) {
//   border-bottom: 0;
//   color: red;
// }
:deep(.t-drawer__body) {
  padding-top: 10px !important;
}

.tabTitle {
  font-size: 14px;

  font-weight: 700;
  text-align: left;
  color: #13161b;
  position: relative;
  padding-left: 12px;

  &::before {
    content: "";
    width: 2px;
    height: 14px;
    background: #2069e3;
    border-radius: 2px;
    position: absolute;
    top: 4px;
    left: 0;
  }
}

:deep(.detail-control) {
  margin-bottom: 16px !important;
}

.header {
  display: flex;
  align-items: center;
  gap: 16px;

  .title {
    color: var(--kyy_color_modal_title, #1a2139);
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
    /* 150% */
  }

  .tip {
    color: var(--brand-kyy-color-brand-default, #4d5eff);

    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    /* 157.143% */

    display: flex;
    align-items: center;

    .iconhelp {
      font-size: 20px;
    }
  }
}
</style>
