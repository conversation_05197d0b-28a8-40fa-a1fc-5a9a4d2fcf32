<template>
  <div style="height: calc(100% - 48px)">
    <PageHeader
      tab-icon="square-fill"
      :account-list="accountList"
      :account-value="store.squareInfo?.square?.squareId"
      :account-count="accountCount"
      alone-win-name="square"
      @tab-change="tabChange"
      @tab-remove="tabRemove"
      @account-change="accountChange"
      @refresh="refresh()"
    />

    <div class="page-container">
      <div class="sidebar">
        <div class="title">{{ $t('square.admin.title2') }}</div>
        <div class="menu">
          <div
            v-for="(item, index) in menuList"
            :key="item.name"
            :class="['menu-item', { active: activeMenuIdx === index }]"
            @click="onMenuItemClick(item, index)"
          >
            <iconpark-icon v-if="item.icon" :class="['menu-icon', item.name]" :name="item.icon" />
            <div class="menu-title">{{ item.name === 'SquareManager' ? '广场管理员' : item.title }}</div>
          </div>
        </div>
      </div>

      <router-view v-slot="{ Component }" :key="refreshKey" class="square-content">
        <component :is="Component" />
      </router-view>
    </div>
  </div>
</template>

<script setup lang="ts" name="SquareAdmin">
import { onMounted, onUnmounted, provide, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useTabsStore } from '@renderer/components/page-header/store';
import { TabItem } from '@renderer/components/page-header/type';
import PageHeader from '@/components/page-header/index.vue';
import useRouterHelper from '@/views/square/hooks/routerHelper';
import { RouteItem } from '@/views/square/types';
import { useSquareAccount } from '../hooks/squareAccount';
import { ROUTE_REFRESH_INJECT_KEY } from '../constant';
import { useSquareStore } from '../store/square';
import useNavigate from '../hooks/navigate';
import { useEventHandling } from '../hooks/eventHandling';

const tabStore = useTabsStore();
const route = useRoute();
const router = useRouter();
const store = useSquareStore();
const { redirectFromOuter } = useNavigate();
const { menuList, routeList } = useRouterHelper('squareAdmin');

const refreshKey = ref(1);
const refresh = () => {
  refreshKey.value++;
};

// 刷新页面
provide(ROUTE_REFRESH_INJECT_KEY, refresh);

const tabChange = (tab: TabItem) => {
  let idx = menuList.value.findIndex((v) => v.fullPath === tab.fullPath);
  if (idx > -1) store.activeMenuIdx = idx;
  router.replace(tabStore.getActiveTab().fullPath);
};

const tabRemove = () => {
  router.replace(tabStore.getActiveTab().fullPath);
  // router.back();
  store.isRemoveTag = true;
};

const { accountList, accountCount, accountChange } = useSquareAccount(refresh);

// 左侧菜单
const activeMenuIdx = ref(0);

const onMenuItemClick = (item: RouteItem, index: number) => {
  activeMenuIdx.value = index;
  router.push(item.fullPath);
};

// electron 事件监听器处理
const removeEventListeners = useEventHandling(({ path }) => {
  console.log(path);
}, accountChange);

onUnmounted(() => {
  // 在组件卸载时移除监听器
  removeEventListeners();
});

onMounted(() => {
  // 刷新页面后，保持菜单选中
  activeMenuIdx.value = menuList.value.findIndex((v) => v.fullPath === router.currentRoute.value.fullPath) || 0;
  // 添加到 tab 选项卡
  const item = routeList[0];
  tabStore.addTab({
    label: item.title,
    fullPath: item.fullPath,
  });

  // HACK 用闭包避免 setTimeout 内获取不到 route.query
  ((query) => {
    setTimeout(() => {
      redirectFromOuter(query, accountChange);
    }, 500);
  })(route.query);
});
</script>

<style scoped lang="less">
.page-container {
  display: flex;
  height: 101%;
  background-color: #fff;
}

.page-content {
  position: relative;
  overflow: hidden;
}

.sidebar {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  width: 200px;
  height: 100%;
  padding-top: 16px;
  border-right: 1px solid #e3e6eb;

 .title {
   margin: 4px 16px 20px;
   font-size: 16px;
   font-weight: 700;
   color: #13161b;
 }

  .footer {
    position: relative;
    padding: 8px 8px;
    height: 48px;
    color: #13161b;
    cursor: pointer;
    .setting-wrap {
      display: flex;
      align-items: center;
      height: 100%;
      padding: 8px;
      border-radius: 4px;

      &.active {
        background: #daecff;
        color: #2069e3;
        .title {
          color: #2069e3;
        }
      }
    }
    &::before {
      content: ' ';
      position: absolute;
      top: 0;
      left: 0;
      width: 200px;
      height: 1px;
      background: #e3e6eb;
    }

    .icon {
      font-size: 20px;
      width: 20px;
      height: 20px;
      margin-right: 12px;
    }

    .title {
      font-size: 14px;
      font-weight: 700;
      color: #13161b;
    }
  }
}

.menu {
  flex: 1;
  padding: 0 8px;

  &-item {
    display: flex;
    align-items: center;
    padding: 12px 8px 12px 12px;
    margin-bottom: 9px;
    height: 40px;
    border-radius: 4px;
    font-size: 14px;
    color: #13161b;
    cursor: pointer;

    &.active {
      font-weight: 700;
      color: #2069e3;
      background: var(--bg-kyy-color-bg-list-foucs, #E1EAFF);
    }
  }

  &-icon {
    font-size: 16px;
    margin-right: 8px;
    color: #2069e3;
    &.SquareManager {
      color: var(--blue-kyy-color-brand-default, #4D5EFF);
    }
    &.SafetySetting {
      color: var(--blue-kyy-color-blue-default, #4093E0);
    }
  }
}

.square-content {
  flex: 1;
  height: 100%;
  position: relative;
  overflow-y: auto;
  border-right: 1px solid #e3e6eb;
}
</style>
