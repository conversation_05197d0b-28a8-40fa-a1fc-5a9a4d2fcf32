.af-icon {
  margin-left: 8px;
  height: 32px;
  cursor: pointer;
}
.form-boxxx {
  padding: 0 8px;
  .fitem {
    margin-bottom: 24px;
    .title {
      color: var(--text-kyy-color-text-3, #828da5);

      /* kyy_fontSize_2/regular */
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
    }
    .ctl {
      margin-top: 8px;
    }
  }
}
.foot {
  width: 100%;
  display: flex;
  justify-content: end;
  .btn1 {
    display: flex;
    cursor: pointer;
    height: 32px;
    min-width: 80px;
    min-height: 32px;
    max-height: 32px;
    padding: 0px 16px;
    justify-content: center;
    align-items: center;
    gap: 4px;
    color: var(--color-button-border-kyy-color-button-border-text-default, #516082);
    text-align: center;
    border-radius: var(--radius-kyy-radius-button-s, 4px);
    border: 1px solid var(--color-button-border-kyy-color-button-border-border-dedault, #d5dbe4);
    background: var(--color-button-border-kyy-color-button-border-bg-default, #fff);
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px; /* 157.143% */
    margin-right: 8px;
  }
  .btn2 {
    display: flex;
    cursor: pointer;
    width: 88px;
    height: 32px;
    min-height: 32px;
    max-height: 32px;
    padding: 6px 16px;
    justify-content: center;
    align-items: center;
    gap: 4px;
    border-radius: var(--radius-kyy-radius-button-s, 4px);
    background: var(--color-button-primary-kyy-color-button-primary-bg-disabled, #c9cfff);
    color: var(--color-button-primary-kyy-color-button-primary-text, #fff);
    text-align: center;

    /* kyy_fontSize_2/bold */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px; /* 157.143% */
  }
  .btn3 {
    display: flex;
    cursor: pointer;
    width: 88px;
    height: 32px;
    min-height: 32px;
    max-height: 32px;
    padding: 6px 16px;
    justify-content: center;
    align-items: center;
    gap: 4px;
    border-radius: var(--radius-kyy-radius-button-s, 4px);
    background: var(--color-button-primary-kyy-color-button-primary-bg-default, #4d5eff);

    color: var(--color-button-primary-kyy-color-button-primary-text, #fff);
    text-align: center;
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px;
  }
}
.t-table {
  color: var(--kyy-color-table-text, #1a2139);
}

:deep(.filterDrawer .t-drawer__body) {
  padding-top: 12px;
}

:deep(.filterDrawer .t-drawer__header) {
  border-bottom: none;
  padding: 0 24px;
}

:deep(.filterDrawer .t-drawer__close-btn .t-icon) {
  font-size: 20px;
  color: #516082;
}

.iconorientation {
  font-size: 20px;
  color: #828da5;
  cursor: pointer;
}

.iconscreen {
  font-size: 20px;
  color: #828da5;
  cursor: pointer;
}
.iconscreen:hover {
  color: #4d5eff;
}
.name-icon {
  font-size: 20px;
  color: #828da5;
}

.filter-res {
  display: flex;
  flex-wrap: wrap;
  row-gap: 8px;
  .tit {
    color: var(--text-kyy-color-text-2, #516082);

    /* kyy_fontSize_2/bold */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px; /* 157.143% */
  }
  .ov-time {
    display: flex;
    min-width: 290px;
    height: 24px;
    min-height: 24px;
    max-height: 24px;
    padding: 2px 8px;
    align-items: center;
    gap: 8px;
  }
  .close2 {
    display: flex;
    align-items: center;
    color: #828da5;
    img {
      width: 10px;
      height: 10px;
    }
  }
  .te {
    color: var(--kyy-color-tag-text-black, #1a2139);
    cursor: pointer;
    /* kyy_fontSize_2/regular */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
    border-radius: 4px;
    background: var(--kyy-color-tag-bg-gray, #eceff5);
    margin-right: 8px;
  }
  .stat {
    display: flex;
    height: 24px;
    min-height: 24px;
    max-height: 24px;
    padding: 2px 8px;
    align-items: center;
    gap: 8px;
  }
  .kword {
    display: flex;
    height: 24px;
    min-height: 24px;
    max-height: 24px;
    padding: 2px 8px;
    align-items: center;
    gap: 8px;
  }
  .icon {
    display: flex;
    margin-left: 4px;
    cursor: pointer;
    img {
      width: 14px;
      height: 14px;
      margin-top: 4px;
      margin-right: 4px;
    }
  }
}
:deep(.data-box-con .t-table__empty) {
  display: none;
}
:deep(.table-box .t-table__empty) {
  display: none;
}

//待生效
.st-tag5,
.st-tag0 {
  display: flex;
  height: 20px;
  min-height: 20px;
  max-height: 20px;
  padding: 0px 8px;
  justify-content: center;
  align-items: center;
  gap: 4px;
  border-radius: var(--kyy_radius_tag_s, 4px);
  background: var(--kyy_color_tag_bg_kyyBlue, #e4f5fe);
  color: var(--kyy_color_tag_text_kyyBlue, #21acfa);
  text-align: center;
  font-family: "PingFang SC";
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
}
//进行中
.st-tag1 {
  display: flex;
  height: 20px;
  min-height: 20px;
  max-height: 20px;
  padding: 0px 8px;
  justify-content: center;
  align-items: center;
  gap: 4px;
  border-radius: var(--kyy_radius_tag_s, 4px);
  background: var(--kyy_color_tag_bg_warning, #ffe5d1);
  color: var(--kyy_color_tag_text_warning, #fc7c14);
  text-align: center;
  font-family: "PingFang SC";
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
}
//已删除 已拒绝 已失效 已下架
.st-tag2,
.st-tag6,
.st-tag4 {
  display: flex;
  height: 20px;
  min-height: 20px;
  max-height: 20px;
  padding: 0px 8px;
  justify-content: center;
  align-items: center;
  gap: 4px;
  border-radius: var(--kyy_radius_tag_s, 4px);
  background: var(--kyy_color_tag_bg_error, #f7d5db);
  color: var(--kyy_color_tag_text_error, #d54941);
  text-align: center;
  font-family: "PingFang SC";
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
}
.st-tag00 {
  display: flex;
  height: 20px;
  min-height: 20px;
  max-height: 20px;
  padding: 0px 8px;
  justify-content: center;
  align-items: center;
  gap: 4px;
  border-radius: var(--kyy_radius_tag_s, 4px);
  background: var(--kyy_color_tag_bg_gray, #eceff5);
  color: var(--kyy_color_tag_text_gray, #516082);
  text-align: center;
  font-family: "PingFang SC";
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px; /* 166.667% */
}
.st-tag3 {
  display: flex;
  height: 20px;
  min-height: 20px;
  max-height: 20px;
  padding: 0px 8px;
  justify-content: center;
  align-items: center;
  gap: 4px;
  border-radius: var(--kyy_radius_tag_s, 4px);
  background: var(--kyy_color_tag_bg_gray, #eceff5);
  color: var(--kyy_color_tag_text_gray, #516082);
  text-align: center;
  font-family: "PingFang SC";
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px; /* 166.667% */
}
.type-tag {
  display: flex;
  width: 76px;
  height: 22px;
  padding: 0px 8px;
  align-items: center;
  gap: var(--checkbox-kyy_radius_checkbox, 2px);
  flex-shrink: 0;
  border-radius: 4px;
  background: var(--bg-kyy_color_bg_deep, #f5f8fe);
  font-family: "PingFang SC";
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  .icon {
    width: 16px;
    height: 16px;
  }
}
.type-tag1 {
  color: #ed565c;
}
.type-tag2 {
  color: #ea8330;
}
.type-tag3 {
  color: #4d5eff;
}
.type-tag4 {
  color: #ED565C;
}
.actions-boxs {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  width: 136px;
  min-height: 40px;
  .item {
    display: flex;
    height: 32px;
    min-width: 136px;
    min-height: 32px;
    max-height: 32px;
    padding: 0px 16px;
    align-items: center;
    gap: 12px;
    align-self: stretch;
    cursor: pointer;
    .icon {
      width: 20px;
      height: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      img {
        width: 16px;
        height: 16px;
      }
    }
    .text {
      color: var(--kyy_color_dropdown_text_default, #1a2139);

      /* kyy_fontSize_2/regular */
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
      // margin-left: 4px;
    }
  }
  .item:hover {
    border-radius: var(--kyy_radius_dropdown_s, 4px);
    background: var(--kyy_color_dropdown_bg_hover, #f3f6fa);
  }
  .item-view {
    border-top: 1px solid var(--divider-kyy-color-divider-light, #eceff5);
  }
}
:deep(.data-box-con .t-table__empty) {
  display: none;
}
:deep(.data-box-con tr):hover {
  background-color: #f3f6fa;
  cursor: pointer;
}
.pagination {
  margin-top: 15px;
}
:deep(.data-box-con .t-table__empty-row):hover {
  background-color: #fff;
}
.actions {
  display: flex;
  gap: 8px;
}
.mbtna {
  display: flex;
  padding: 4px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.mbtna:hover {
  background: var(--bg-kyy_color_bgBrand_hover, #eaecff);
  border-radius: 4px;
}
.family .t-button--theme-default {
  font-weight: 600 !important;
}
