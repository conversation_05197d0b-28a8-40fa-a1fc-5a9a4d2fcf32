<template>
  <div class="sel-box">
    <t-dialog
      v-model:visible="adminVisible"
      :header="props.header"
      width="672px"
      @cancel="onClickClose"
      @close="onClickClose"
      @confirm="onClickConfirm"
    >
      <div class="as-box">
        <div class="left">
          <div class="search">
            <t-input v-model="search_word" :placeholder="t('notice.search')">
              <template #prefix-icon>
                <iconpark-icon name="iconsearch" class="name-icon" />
              </template>
            </t-input>
          </div>
          <div class="tree">
            <div v-for="user in getAdminsData()" :key="user.idStaff" class="user-item" >
              <div class="cb">
                <t-checkbox v-model="user.checked" :disabled="idDisabled(user)" @change="onClick($event, user)" />
              </div>
              <div class="avb avbbb" @click="onClickItem(user)">
                <kyy-avatar round-radius avatar-size="24px" :image-url="user.avatar" :user-name="user.name" />
              </div>
              <div class="name" @click="onClickItem(user)">
                {{ user.name }}
              </div>
            </div>
          </div>
        </div>
        <div class="right">
          <div class="title">{{ t("notice.sed") }}{{ selectTemp.length }}人</div>
          <div class="se-list">
            <div v-for="item in selectTemp" :key="item.idStaff" class="app-item">
              <div class="avb">
                <kyy-avatar round-radius avatar-size="24px" :image-url="item.avatar" :user-name="item.name" />
              </div>
              <div class="name-item">
                {{ item.name }}
              </div>
              <div class="close-item" @click="deleteEle(item)">
                <svg xmlns="http://www.w3.org/2000/svg" fill="#333" viewBox="0 0 48 48">
                  <path
                    stroke-linejoin="round"
                    stroke-linecap="round"
                    stroke-width="4"
                    stroke="#333"
                    d="m8 8 32 32M8 40 40 8"
                    data-follow-stroke="#333"
                  />
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="tfoot">
          <div class="left">{{ props.tip }}</div>
          <div class="rigth">
            <t-button theme="default" style="min-width: 80px" @click="onClickClose"> 取消 </t-button>
            <t-button style="min-width: 80px" :disabled="!selectTemp.length" @click="onClickConfirm">
              {{ t("notice.qd") }}
            </t-button>
          </div>
        </div>
      </template>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import _ from "lodash";
import { MessagePlugin } from "tdesign-vue-next";
import kyyAvatar from "@renderer/components/kyy-avatar/index.vue";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const props = defineProps({
  single: {
    type: Boolean,
    default: true,
  },
  tip: {
    type: String,
    default: "",
  },
  header: {
    type: String,
    default: "",
  },
  disData: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(["sendUserData", "onClose"]);

const search_word = ref("");
const users: any = ref([]);
const selectTemp: any = ref([]);

const getAdminsData = () => {
  if (!search_word.value) {
    return users.value;
  }
  const res: any = [];
  for (const item of users.value) {
    if (item.name.includes(search_word.value)) {
      res.push(item);
    }
  }
  return res;
};

const dataHandle = (data: Array<any>, ids: Array<any>) => {
  selectTemp.value = [];
  // const ids = selectedData.map((item) => item.idStaff);
  console.log("ids", ids);

  data.forEach((item) => {
    if (ids.includes(item.idStaff)) {
      console.log("true");
      item.checked = true;
      selectTemp.value.push(item);
    } else {
      item.checked = false;
      console.log("false");
    }
  });
  console.log(data);
};

const idDisabled = (user) => {
  if (props.disData.includes(user.idStaff)) {
    return true;
  }
  if (props.single && selectTemp.value.length && !user.checked) {
    return true;
  }
  return false;
};

const onClickItem = (user) => {
  console.log(`onClickItem`);
  console.log(user);

  if (idDisabled(user)) {
    return;
  }
  user.checked = !user.checked;
  if (!user.checked) {
    selectTemp.value = users.value.filter((item: any) => item.checked);
  } else {
    selectTemp.value.push(user);
  }
};

const onClick = (e, user) => {
  console.log(e);

  // selectTemp.value = users.value.filter((item: any) => item.checked);
  if (!e) {
    selectTemp.value = selectTemp.value.filter((item: any) => item.idStaff !== user.idStaff);
  } else {
    selectTemp.value.push(user);
  }
};

const deleteEle = (item) => {
  if (idDisabled(item)) {
    return;
  }
  selectTemp.value = selectTemp.value.filter((ele: any) => ele.idStaff !== item.idStaff);
  users.value.forEach((ele: any) => {
    if (ele.idStaff === item.idStaff) {
      ele.checked = false;
    }
  });
};

const adminVisible = ref(false);

const onClickConfirm = () => {
  adminVisible.value = false;
  if (props.single && selectTemp.value.length > 1) {
    MessagePlugin.error(t("notice.one"));
    return false;
  }
  emit("sendUserData", selectTemp.value);
};
const onClickClose = () => {
  emit("onClose", true);
  adminVisible.value = false;
};

const ownerOpen = (treeData, selectedData) => {
  console.log(treeData, selectedData);

  users.value = treeData;
  dataHandle(users.value, selectedData || []);
  adminVisible.value = true;
};

defineExpose({
  ownerOpen,
});
</script>

<style lang="less" scoped>
.sel-box {
}
.as-box {
  height: 372px;
  border: 1px solid #e3e6eb;
  border-radius: 4px;
  display: flex;
  padding: 5px 0;
  margin-top: 24px;
  margin-bottom: 16px;
  .left {
    width: 316px;
    border-right: 1px solid #e1e1e1;
    padding: 12px 16px;
    .search {
    }
    .tree {
      margin-top: 12px;
      height: 300px;
      overflow-y: auto;
      .user-item {
        width: 270px;
        height: 32px;
        border-radius: 4px;
        display: flex;
        align-items: center;
        padding: 0 8px;
        cursor: pointer;
        .cb {
          padding-top: 5px;
        }
        .avb {
          width: 24px;
          height: 24px;
          border-radius: 4px;
          margin-right: 4px;
          img {
            width: 100%;
            height: 100%;
            border-radius: 4px;
          }
          .mock-av {
            width: 100%;
            height: 100%;
            background: #488bf0;
            border-radius: 4px;
            font-size: 10px;

            font-weight: 400;
            text-align: center;
            color: #ffffff;
          }
        }
        .name {
          width: 205px;
    font-size: 14px;
    font-weight: 400;
    text-align: left;
    color: #13161b;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    height: 100%;
    line-height: 32px;
        }
      }
      .user-item:hover {
        background: #f0f8ff;
      }
    }
  }

  .right {
    width: 316px;
    height: 100%;
    overflow-y: auto;
    padding: 0 16px;
    .title {
      font-size: 14px;

      font-weight: 400;
      text-align: left;
      color: #13161b;
      margin-bottom: 14px;
    }
    .se-list {
      width: 100%;
      .app-item {
        width: 100%;
        height: 32px;
        background: #ffffff;
        border-radius: 4px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .name-item {
          font-size: 14px;

          font-weight: 400;
          text-align: left;
          color: #13161b;
          width: 220px;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }
        .avb {
          width: 24px;
          height: 24px;
          border-radius: 4px;
          margin-right: 8px;
          img {
            width: 100%;
            height: 100%;
            border-radius: 4px;
          }
          .mock-av {
            width: 100%;
            height: 100%;
            background: #488bf0;
            border-radius: 4px;
            font-size: 10px;

            font-weight: 400;
            text-align: center;
            color: #ffffff;
          }
        }
        .close-item {
          cursor: pointer;
          svg {
            width: 14px;
            height: 14px;
          }
        }
      }
    }
  }
}
.tfoot {
  display: flex;
  justify-content: space-between;
  .left {
    color: var(--text-kyy_color_text_3, #828da5);
    text-align: center;

    /* kyy_fontSize_2/regular */
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
  .rigth {
    display: flex;
  }
}
</style>
