export const platform = {
  digitalPlatform: 'digital_platform',
  digitalWorkbench: 'digital_workbench',
  square: 'square',
  message: 'message'
}

// 数字平台类型，DP_BUSINESS：数字商协，DP_GOVERNMENT：数字城市，DP_CBD：数字 CBD，DP_COMMUNITY：数字社群
export const DigitalPlatformTypeSquare = {
  Member: 'DP_BUSINESS',
  Government: 'DP_GOVERNMENT',
  CBD: 'DP_CBD',
  Association: 'DP_COMMUNITY'
}


export const originType = {
  Member: 'member',  // 商协
  Politics: 'politics', // 城市
  Government:'government', // 城市
  CBD: 'cbd', // cbd
  Association: "association", // 社群
  Uni: "uni", // 社群
  Connect:'connect'
}



export const platformText = {
  digital_platform: '数字平台',
  digital_work: '数智工场',
  square: '广场号'
}


export const  FENGCAIImageDefault = 'https://kuaiyouyi.oss-cn-shenzhen.aliyuncs.com/square/172232087009759148525.svg';
import dynicImageDefault from '@renderer/assets/member/svg/dynamic.svg';
export const DYNAMICImageDefault = dynicImageDefault;

// export const ImageDefault = {
//   FENGCAI: 'https://kuaiyouyi.oss-cn-shenzhen.aliyuncs.com/square/172232087009759148525.svg', // 平台风采
//   PARTY_BUILDING: 'https://kuaiyouyi.oss-cn-shenzhen.aliyuncs.com/squ…72376125.png'// 党建
//   FENGCAI: 'https://kuaiyouyi.oss-cn-shenzhen.aliyuncs.com/square/172232087009759148525.svg', // 平台风采

// }

// TEXT // 文字PICTURE // 图片VIDEO // 视频ARTICLE // 文章ALBUM_NODE
// 相册节PARTY_BUILDING // 党建 TEAM_INTRO // 组织介绍 TEAM_HONOR_ROLL
// 组织荣誉榜 TEAM_HISTORY // 组织历程 FENGCAI // 平台风采
export enum SquareContentType {
  TEXT = 'TEXT',
  PICTURE = 'PICTURE',
  VIDEO = 'VIDEO',
  ARTICLE = 'ARTICLE',
  ALBUM_NODE = 'ALBUM_NODE',
  PARTY_BUILDING = 'PARTY_BUILDING',
  TEAM_INTRO = 'TEAM_INTRO', // 组织介绍
  TEAM_HONOR_ROLL = 'TEAM_HONOR_ROLL', // 组织荣誉榜
  TEAM_HISTORY = 'TEAM_HISTORY', // 组织历程
  FENGCAI = 'FENGCAI' // 平台风采
}

// 1同意、2拒绝，3已取消、4已失效 5待审核 写一个ts的枚举
export enum ApprovalStatus {
  All = 0,
  Agreed = 1,
  Rejected = 2,
  Cancelled = 3,
  Invalidated = 4,
  Pending = 5
}

export const ApprovalStatusList = [
  {
    value: ApprovalStatus.All,
    label: '全部'
  },
  {
    value: ApprovalStatus.Pending,
    label: '待审核'
  },
  {
    value: ApprovalStatus.Agreed,
    label: '已同意'
  },
  {
    value: ApprovalStatus.Rejected,
    label: '已拒绝'
  },
  {
    value: ApprovalStatus.Cancelled,
    label: '已取消'
  },
  {
    value: ApprovalStatus.Invalidated,
    label: '已失效'
  }
]

/**
 * 标签颜色
 */
export const TagColors = [
  { // 红色
    bgColor: '#F7D5DB',
    color: '#D54941',
    intColor: Number.parseInt('0XFFD54941', 16),
    class: 'TagColorsRed'

  },
  { // 橙色
    bgColor: '#FFE5D1',
    color: '#FC7C14',
    intColor: Number.parseInt('0XFFFC7C14', 16),
    class: 'TagColorsOrange'
  },
  { // 绿色
    bgColor: '#E0F2E5',
    color: '#499D60',
    intColor: Number.parseInt('0XFF499D60', 16),
    class: 'TagColorsGreen'
  },

  { // 洋红
    bgColor: '#FFE3F1',
    color: '#FF4AA1',
    intColor: Number.parseInt('0XFFFF4AA1', 16),
    class: 'TagColorsMagenta'
  },
  { // 紫色
    bgColor: '#FAEDFD',
    color: '#CA48EB',
    intColor: Number.parseInt('0XFFCA48EB', 16),
    class: 'TagColorsPurple'
  },
  { // 黄色
    bgColor: '#FFF9E8',
    color: '#FFBE16',
    intColor: Number.parseInt('0XFFFFBE16', 16),
    class: 'TagColorsYellow'
  },
  { // 青色
    bgColor: '#E6F9F8',
    color: '#11BDB2',
    intColor: Number.parseInt('0XFF11BDB2', 16),
    class: 'TagColorsCyan'
  },
  { // 蓝
    bgColor: '#EAECFF',
    color: '#4D5EFF',
    intColor: Number.parseInt('0XFF4D5EFF', 16),
    class: 'TagColorsBlue'
  },
  { // 中蓝
    bgColor: '#E8F0FB',
    color: '#4093E0',
    intColor: Number.parseInt('0XFF4093E0', 16),
    class: 'TagColorsMediumBlue'
  },
  { // 淡蓝
    bgColor: '#E4F5FE',
    color: '#21ACFA',
    intColor: Number.parseInt('0XFF21ACFA', 16),
    class: 'TagColorsLightBlue'
  },

];


export const TagType = {
  Search: 'search',
  Create: 'create',
  Edit: 'edit',
  Delete: 'delete'
}
