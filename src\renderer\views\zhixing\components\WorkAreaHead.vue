<template>
  <div class="head-box">
    <icon class="icon-specials" :url="iconUrl" name="iconSpecial-graphics" />

    <div class="tabs-list">
      <div
        v-for="(item, index) in tabList"
        :key="index"
        :class="['tabs-item', { 'bgc-fff': activeIndex === index }]"
        @click.stop="switchTab(item, index)"
      >
        <div style="display: flex; align-items: center">
          <img class="tab-icon" :src="item.icon" alt="">
          <span style="font-size:14px;color:var(--kyy_color_tabbar_item_text, #1A2139);">{{ item.title }}</span>
        </div>
        <div class="red-point" v-if="item.count">
          <!-- {{ item.count > 99 ? '99+' : item.count }} -->
          {{ item.count > 99 ? '...' : item.count}}
        </div>
        <!-- <img
          v-if="route.path !== item.path && !item.affix"
          style="width: 12px; height: 12px"
          src="@renderer/assets/img/<EMAIL>"
          @click.stop="removeTab(item)"
        > -->
      </div>
    </div>

    <!-- <t-popup placement="bottom-right" overlay-inner-class-name="square-account-list-popup" :disabled="accountList.length < 2">
      <t-badge dot :count="accountNoticeCount" class="mr-16">
        <div class="account">
          <div class="text">
            {{ store.squareInfo?.profile?.name }}
          </div>
          <img
            v-if="accountList.length > 1"
            class="arrow"
            src="@renderer/assets/img/icon_arrow_down.svg"
            alt=""
          >
        </div>
      </t-badge>
      <template #content>
        <div class="account-wrap">
          <div
            v-for="item in accountList"
            :key="item.square.squareId"
            :class="['account-item', { active: item.square.squareId === store.squareInfo?.profile?.squareId }]"
            @click="accountClick(item)"
          >
            <img :src="item.square.avatar" alt="" class="avatar">
            <div class="name line-1">{{ item.square.name }}</div>
            <t-badge :count="item.notices" size="small" class="mr-5" />
          </div>
        </div>
      </template>
    </t-popup> -->
    <icon
      class="org-img-window"
      name="iconrefresh"
      :url="iconUrl"
      @click="refresh"
    />
    <icon
      v-if="newWinFlag"
      class="org-img-window"

      name="iconwindow"
      :url="iconUrl"
      @click="openStandAloneWin"
    />

  </div>
</template>

<script setup lang="ts">
import manifesticon from '@renderer/assets/zhixing/icon_apply_manifest.svg';
import scheduleicon from '@renderer/assets/zhixing/schedule.svg';
import noteicon from '@renderer/assets/img/im_note.svg';
import todoicon from '@renderer/assets/zhixing/all.svg';
import icon_remind from '@renderer/assets/zhixing/remind.svg';
import { useRoute, useRouter } from 'vue-router';
import { ref, onMounted, watch } from 'vue';
import { onMountedOrActivated } from "@/hooks/onMountedOrActivated";
import to from "await-to-js";
// import { useSquareStore } from '../store/square';
import { iconUrl } from "@renderer/plugins/KyyComponents";
import { Icon } from "tdesign-icons-vue-next";
import { getSquareInfo, getSquaresList } from '@/api/square/home';
import {getZhixingTab, setZhixingTab} from '../storage';
import { useZhixingStore } from '../store';
import { zhixingRedPoint } from '../util';
//国际化
import { useI18n } from "vue-i18n";
import LynkerSDK from '@renderer/_jssdk';

const { ipcRenderer, shell } = LynkerSDK;
const { t } = useI18n();
const zhixingStore = useZhixingStore();
const route = useRoute();
const router = useRouter();
const activeIndex = ref(0);
// const store = useSquareStore();
const tabList = ref([
  { title: t('zx.zhixing.todos'), path: 'todo', icon: todoicon, count: 0 },
  { title: t('zx.zhixing.schedule'), path: 'schedule', icon: scheduleicon, count: 0 },
  { title: t('zx.zhixing.note'), path: 'note', icon: noteicon, count: 0 },
  { title: t('zx.zhixing.remind'), path: 'remind', icon: icon_remind, count: 0 },
  // { title: '清单', path: 'manifest', icon: manifesticon, count: 0 }
]);

const newWinFlag = ref(true);
const emits = defineEmits(['onrefresh']);

// const accountList = ref([]);
const accountNoticeCount = ref(0);

onMountedOrActivated(async () => {
  const countObj = await zhixingRedPoint();
  zhixingStore.setToDoCount(countObj.manifestAll + countObj.approval + countObj.later + countObj.notice + countObj.activity);
  zhixingStore.setManifestCount(countObj.manifest);
  zhixingStore.setRemindCount(countObj.notice);
  zhixingStore.setActivityCount(countObj.activity);
  const remind = tabList.value.find(v => v.path === 'remind');
  remind.count = zhixingStore.remindCount;
  console.log(countObj);

  const todo = tabList.value.find(v => v.path === 'todo');
  todo.count = zhixingStore.toDoCount;
})

onMounted(async () => {

  // const res = await getSquaresList();

  // accountList.value = res.data.squares;
  // store.squareList = accountList.value;
  // accountNoticeCount.value = res.data.notices;

  // setRestConfig(store.squareInfo?.profile?.squareId);
});

// const setRestConfig = (squareId) => {
//   if (!squareId) return;
//   const selected = store.squareList.find((v) => v.square.squareId === squareId);
//   if (selected) {
//     store.squareInfo.profile.isAdmin = selected.isAdmin;
//   }
// };

// const accountClick = async (item) => {
//   console.log(item);
//   let [err, res] = await to(getSquareInfo({ square_id: item.square.squareId }));
//   if (err) return;

//   const { organizationProfile, individualProfile, individualSetting, square } = res.data.info;

//   const isIndividual = item.square.squareType === 'INDIVIDUAL';
//   store.squareInfo.square = square;
//   if (isIndividual) {
//     store.squareInfo.profile = individualProfile;
//     store.squareInfo.setting = individualSetting;
//   } else {
//     store.squareInfo.profile = organizationProfile;
//     setRestConfig(organizationProfile.squareId);
//   }

//   await router.replace(isIndividual ? '/square/friend-circle' : '/square/publish-records');
//   refresh();
// };

const switchTab = (item, index: number) => {
  if(getZhixingTab() === item.path){
    return;
  }
  activeIndex.value = index;
  console.log(item.path);
  router.push(`/zhixing/${item.path}`);
  setZhixingTab(item.path);
};

const refresh = () => {
  zhixingStore.deleteStatus = 'none';
  emits('onrefresh');
};

const openStandAloneWin = () => {
  newWinFlag.value = false;
  ipcRenderer.invoke('click-standalone-window', {
    url: 'zhixingLayout',
    flag: route.fullPath.split('/')[1],
  });
};

const setActive = (path:string) => {
  activeIndex.value = tabList.value.findIndex(v => v.path === path);
};
defineExpose({
  setActive,
})

watch(() => zhixingStore.toDoCount, (newValue) => {
  console.log('toDoCount', newValue);
  const tabItem = tabList.value.find(v => v.path === 'todo');
  tabItem.count = newValue;
})
watch(() => zhixingStore.remindCount, (newValue) => {
  console.log('remindCount', newValue);
  const remind = tabList.value.find(v => v.path === 'remind');
  remind.count = newValue;
})
// watch(() => zhixingStore.manifestCount, (newValue) => {
//   const tabItem = tabList.value.find(v => v.path === 'manifest');
//   tabItem.count = newValue;
// })
</script>

<style lang="less" scoped>
//@import '../common';

.bgc-fff {
  background: #fff !important;
//   .tab-icon {
//     margin-left: 7px !important;
//   }
}

.btn-op {
  width: 20px;
  height: 20px;
  margin-right: 16px;
  cursor: pointer;
}

.account {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  //max-width: 200px;
  .arrow {
    width: 16px;
    height: 16px;
    //margin-right: 16px;
  }
  .text {
    max-width: 94px;
    font-size: 12px;
    font-weight: 400;
    margin: 0 4px;
    color: #13161b;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.head-box {
  height: 40px;
  display: flex;
  align-items: center;
  overflow: hidden;
  background: #e3e6eb;
  .tabs-item:hover {
    background: #fff;
  }

  .tabs-list {
    &:hover {
      //overflow-x: overlay !important;
    }
    display: flex;
    flex: 1;
    width: 0;
    overflow: hidden;
    .tabs-item {
      cursor: pointer;
      min-width: 140px;
      justify-content: space-between;
      height: 32px;
      // margin: 4px;
      display: flex;
      align-items: center;
      // background: #f1f2f5;
      border-radius: 4px;
      box-shadow: 0 1px 4px 0 rgba(19, 22, 27, 0.24);
      .left-head-color {
        width: 5px;
        border-radius: 4px 0 0 4px;
        height: 32px;
        background: #2069e3;
      }
      img {
        width: 20px;
        height: 20px;
        margin-right: 8px;
        margin-left: 12px;
      }
      span {
        height: 20px;
        font-size: 12px;
        font-weight: 400;
        color: #13161b;
        line-height: 20px;
      }
    }
  }
}
</style>

<style lang="less">
.square-account-list-popup {
  //width: 311px;
    max-height: 276px;
  .account-wrap {
    overflow-y: auto;
  }
  .account-item {
    display: flex;
    align-items: center;
    height: 40px;
    border-radius: 4px;
    margin-bottom: 4px;
    padding: 0 8px;
    cursor: pointer;
    &.active, &:hover {
      background: #daecff;
    }
  }
  .avatar {
    width: 24px;
    height: 24px;
    border-radius: 5px;
    margin-right: 8px;
  }
  .name {
    max-width: 220px;
    margin-right: 20px;
  }
  .t-size-s {
    color: #fff;
    background-color: #da2d19;
  }
}
@import '@/style/workAreaHead.less';
.red-point {
  // border-radius: var(--kyy_radius_badge_full, 999px);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  // padding: 0 4px;
  height: 16px;
  width: 16px;
  background: var(--kyy_color_badge_bg, #FF4AA1);
  color: var(--kyy_color_badge_text, #FFF);
  text-align: center;
  font-family: PingFang SC;
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
  line-height: 16px; /* 133.333% */
  margin-right: 12px;
}

</style>
