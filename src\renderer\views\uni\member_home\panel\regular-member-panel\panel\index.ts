import { defineAsyncComponent } from "vue";

const PRegular = defineAsyncComponent(() => import("./regular.vue"));
const PContact = defineAsyncComponent(() => import("./contact.vue"));
const PApply = defineAsyncComponent(() => import("./apply.vue"));
const PActiveMemberPanel = defineAsyncComponent(
  () => import("@renderer/views/uni/member_home/panel/active-member-panel.vue")
);
const PApplyMemberPanel = defineAsyncComponent(
  () => import("@renderer/views/uni/member_home/panel/apply-member-panel.vue")
);


export const panels = {
  PRegular, // 正式会员
  PContact, // 联系人管理
  PApply, // 申请列表
  PActiveMemberPanel, // 激活会员
  PApplyMemberPanel, // 入会申请
};
