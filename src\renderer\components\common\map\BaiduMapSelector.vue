<script setup lang="ts">
import { computed, nextTick, onBeforeMount, onMounted, ref, watch } from 'vue';
import debounce from 'lodash/debounce';
import { useVModels } from '@vueuse/core';
import { useIpLocation, usePointGeocoder } from 'vue3-baidu-map-gl';
import { baiduMapSuggestion } from '@renderer/api/global/index';
import to from 'await-to-js';
import { mapOptions, markerIcon, covertData, openExternalMap, BAIDU_API_URL } from './utils';
import Empty from '@/components/common/Empty.vue';
import { highlight } from '@/views/square/utils';

// HACK: md5 is not defined
// https://github.com/yue1123/vue3-baidu-map-gl/issues/27#issuecomment-2219696323
let moduleObject;
onBeforeMount(() => {
  moduleObject = module;
  // eslint-disable-next-line no-global-assign
  (module as unknown) = undefined;
});
const onBMapInitdHandler = () => {
  // eslint-disable-next-line no-global-assign
  (module as unknown) = moduleObject;
};

const props = withDefaults(defineProps<{
  visible: boolean,
  // 显示
  loc?: Record<string, any>,
  // 仅显示
  onlyShow?: boolean,
}>(), {
  loc: () => ({}),
});
const emit = defineEmits(['update:visible', 'confirm', 'activity-confirm']);
const { visible } = useVModels(props, emit);

const mapVisible = ref(false);
onMounted(() => {
  mapVisible.value = true;
});

// 搜索信息
const keyword = ref('');
const loading = ref(false);
const listLoaded = ref(false);
const listLoading = ref(false);
// 搜索结果列表
const posList = ref([]);
// 搜索结果选中项
const selectItem = ref(null);

// 地图中心点
const center = ref();
// 当前城市
const city = ref('');
// 地图缩放级别
const zoom = ref(10);
// 拖拽或点击地图得到的marker
const markerInfo = ref(null);
// 标记点坐标
const markerPoint = ref<{ lng: number; lat: number; }>(center.value);

const locPoint = computed(() => {
  const { latLng } = props.loc || {};
  if (!latLng) return null;

  const { longitude, latitude } = latLng;
  return { lat: latitude, lng: longitude };
});

// 定位到指定经纬度
const handleSelectedData = (loc) => {
  if (!loc) return false;

  center.value = loc;
  markerPoint.value = loc;

  selectPOI(loc);
  getPointGeocoder(loc);
  return true;
};

// 由坐标点解析地址信息（逆向地理编码）
const { get: getPointGeocoder, isEmpty } = usePointGeocoder({}, (res) => {
  if (isEmpty.value) return;

  console.log('pointGeocoder', res.value);
  const data = covertData(res.value);
  if (selectItem.value) {
    const { name, address } = selectItem.value;
    markerInfo.value = {
      ...data,
      name,
      address: address || name,
      location: selectItem.value.location,
    };
  } else {
    markerInfo.value = data;
  }
  console.log('markerInfo.value:', markerInfo.value);
});

// IP 定位（用于获取用户所在的城市位置信息，根据用户 IP 自动定位到城市）
const { get: getIPLocation, location } = useIpLocation(() => {
  if (!location.value) return;
  if (handleSelectedData(locPoint.value)) return;

  const { point, name } = location.value;
  center.value = point;
  city.value = name;
  zoom.value = 18;

  console.log('location.value:', location.value);
  markerPoint.value = point;
  getPointGeocoder(point);
});

// 地图初始化
const mapInit = () => {
  onBMapInitdHandler();

  if (handleSelectedData(locPoint.value)) return;

  // 不指定目标地址时，定位到当前城市中心
  getIPLocation();
};

// 点击地图设置新的标记点，并获取其详情地址
const mapClick = ({ latlng }) => {
  posList.value = [];

  if (props.onlyShow) {
    const { latLng, name } = props.loc || {};
    const { lng, lat } = latLng;
    if (!lng) return;

    openExternalMap({ lng, lat, name });
    return;
  }

  selectPOI(latlng);
  getPointGeocoder(latlng);
};

// 搜索结果列表
const getPlaceList = async () => {
  mapVisible.value = false;
  listLoading.value = true;
  listLoaded.value = false;

  const [err, res] = await to(baiduMapSuggestion({
    q: keyword.value,
    region: city.value || '全国',
  }));

  listLoading.value = false;
  listLoaded.value = true;
  if (err || res.data.status !== 0) return;

  const { result } = res.data;
  posList.value = result.filter((v) => v.location);
};

// 搜索关键字变化时，重新发起请求
const getPlaceListDebounce = debounce(() => {
  if (!keyword.value) {
    mapVisible.value = true;
    return;
  }

  posList.value = [];
  getPlaceList();
}, 400);

// 取消搜索
const cancelSearch = () => {
  keyword.value = '';
  mapVisible.value = true;
};

// 关闭弹窗事件
const onclose = () => {
  keyword.value = '';
};
// 手动关闭弹窗
const oncancel = () => {
  onclose();
  visible.value = false;
};

// 清空搜索条件后，重置为初始值
watch(keyword, (val) => {
  if (!val && markerPoint.value && !posList.value.length) {
    getPointGeocoder(markerPoint.value);
  }
});

// 标记并定位选中的点
const selectPOI = (item) => {
  markerPoint.value = item;
  center.value = item;
  zoom.value = 18;
  mapVisible.value = true;
};

// 获取搜索结果项实例（用于自动定位到所选项）
let itemRefs = ref<{ [key: string]: HTMLInputElement }>({});
const setItemRef = (el: HTMLInputElement, uid: string) => {
  if (el) itemRefs.value[uid] = el;
};

// 搜索结果项点击
const itemClick = async (item) => {
  selectItem.value = item;
  selectPOI(item.location);
  getPointGeocoder(item.location);

  await nextTick();
  itemRefs.value[item.uid]?.scrollIntoView({ behavior: 'smooth', block: 'start' });
};

// 回显地址
watch(() => props.loc, (val) => {
  if (!val || !val.latLng) return;

  handleSelectedData(locPoint.value);
}, { immediate: true, deep: true });

// 提交地址
const submit = () => {
  emit('confirm', markerInfo.value);

  oncancel();
};
</script>

<template>
  <t-dialog
    v-model:visible="visible"
    v-bind="$attrs"
    width="672"
    attach="body"
    :footer="false"
    prevent-scroll-through
    :close-on-overlay-click="false"
    :header="$t('square.map.fixedAddress')"
    class="b-map-selector-dialog"
    :z-index="9999"
    @close="onclose"
  >
    <div v-if="visible && !onlyShow" class="input-wrap">
      <t-input
        v-model="keyword"
        :placeholder="$t('square.page.searchPlace')"
        clearable
        autofocus
        @clear="cancelSearch"
        @input="getPlaceListDebounce"
      >
        <template #prefix-icon>
          <iconpark-icon name="iconsearch" class="icon-search" />
        </template>
      </t-input>

      <t-link
        v-if="keyword"
        theme="default"
        hover="color"
        class="ml-12"
        @click="cancelSearch"
      >
        {{ $t('square.action.cancel') }}
      </t-link>
    </div>

    <div v-if="visible" class="main-content">
      <t-loading
        v-if="loading"
        class="my-20"
        :text="$t('square.searching')"
        size="small"
      />

      <div v-show="!loading && mapVisible" class="map-container">
        <BMap
          :api-url="BAIDU_API_URL"
          :center="center"
          :zoom="zoom"
          :height="214"
          enable-scroll-wheel-zoom
          v-bind="mapOptions"
          @initd="mapInit"
          @click="mapClick"
        >
          <BZoom />
          <BMarker
            v-if="markerPoint"
            :position="markerPoint"
            :enable-clicking="false"
            :offset="{ x: -14, y: -48 }"
            :icon="markerIcon"
          />
        </BMap>
      </div>

      <!-- 搜索的列表 -->
      <div v-if="posList.length" class="search-result">
        <div
          v-for="item in posList"
          :ref="(el) => setItemRef(el, item.uid)"
          :key="item.uid"
          :class="['pos-item', { active: selectItem?.uid === item.uid }]"
          @click="itemClick(item)"
        >
          <iconpark-icon name="iconorientation" class="icon" />
          <div class="flex-1 w-0">
            <div class="line-1 mb-2" v-html="highlight(item.name, keyword)" />
            <div class="address line-1" v-html="highlight(item.address, keyword)" />
          </div>
        </div>

        <Empty v-if="!posList.length && listLoaded && !mapVisible" name="no-result" center />
        <t-loading v-else-if="listLoading" :text="$t('square.loading')" size="small" />
      </div>

      <!-- 地图上选点 -->
      <div v-else-if="markerInfo" class="search-result">
        <div class="pos-item active">
          <iconpark-icon name="iconorientation" class="icon" />
          <div class="flex-1 w-0">
            <div v-if="markerInfo.name" class="line-1 mb-2">{{ markerInfo.name }}</div>
            <div class="address line-1">{{ markerInfo.address }}</div>
          </div>
        </div>
      </div>
    </div>

    <div v-if="!onlyShow && (posList.length || markerInfo)" class="text-right mt-24">
      <t-button variant="outline" class="mr-8 min-w-80" @click="oncancel">{{ $t('square.action.cancel') }}</t-button>
      <t-button
        theme="primary"
        :disabled="!(selectItem || markerInfo) || !mapVisible"
        class="min-w-80"
        @click="submit"
      >
        {{ $t('square.action.confirm') }}
      </t-button>
    </div>
  </t-dialog>
</template>

<style lang="less">
.b-map-selector-dialog {
  .t-dialog {
    padding-bottom: 24px;
  }

  .t-dialog__close {
    color: var(--icon-kyy-color-icon-deep, #516082);
    width: 24px;
    height: 24px;
  }

  .t-dialog__body {
    display: flex;
    flex-direction: column;
    height: 488px;
    padding-top: 24px;
    padding-bottom: 0;
  }

  .main-content {
    flex: 1;
    // height: 0;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .input-wrap {
    margin-bottom: 16px;
    display: flex;
    .t-input__wrap {
      flex: 1;
    }
    input::placeholder {
      color: var(--text-kyy-color-text-5, #ACB3C0) !important;
    }
    .icon-search {
      font-size: 20px;
      color: var(--icon-kyy-color-icon-default, #828DA5);
    }
    .btn-cancel {
      display: flex;
      align-items: center;
      padding-left: 12px;
      color: var(--color-button_text_secondray-kyy_color_button_text_secondray_font_default, #516082);
      text-align: center;
      font-size: 14px;
      line-height: 22px; /* 157.143% */
      cursor: pointer;
    }
  }

  .map-container {
    width: 100%;
    // height: 216px;
    // margin: 8px 0;
    border-radius: 8px;
    border: 1px solid var(--border-kyy_color_border_default, #D5DBE4);
    flex-shrink: 0;
    overflow: hidden;
    :deep(.amap-container) {
      border-radius: 8px;
    }
  }

  .search-result {
    overflow-y: auto;
    .pos-item {
      display: flex;
      align-items: center;
      padding: 12px;
      font-size: 16px;
      // height: 72px;
      gap: 12px;
      color: var(--text-kyy_color_text_1, #1A2139);
      border-bottom: 1px solid var(--divider-kyy_color_divider_light, #ECEFF5);
      cursor: pointer;
      &.active {
        background: var(--bg-kyy_color_bg_list_foucs, #E1EAFF);
      }
      &:hover {
        background: var(--bg-kyy_color_bg_list_hover, #F3F6FA);
      }
    }

    .icon {
      font-size: 20px;
      color: var(--warning-kyy_color_warning_default, #FC7C14);
      flex-shrink: 0;
      align-self: flex-start;
      margin-top: 3px
    }

    .address {
      font-size: 14px;
      color: var(--text-kyy_color_text_2, #516082);
    }

    .load-more {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 60px;
      color: var(--text-kyy-color-text-2, #516082);
      font-size: 14px;
      cursor: pointer;
    }
  }

  .t-loading {
    width: 100%;
    color: #2069E3;
    .t-loading__gradient-conic {
      background: conic-gradient(from 90deg at 50% 50%, rgba(161, 162, 164, 0) 0deg, rgb(4 85 248) 360deg)
    }
  }

  .scrollbar();
}
</style>
