<script setup lang='ts'>
import { useVModel } from '@vueuse/core';
import { MessagePlugin } from 'tdesign-vue-next';
import { computed, ref, withDefaults, defineExpose } from 'vue';
import { useMerchantStore } from '@renderer/store/modules/merchant';
import { onMountedOrActivated } from '@renderer/hooks/onMountedOrActivated';
import { getPlatformAgreement } from '@renderer/api/workBench/merchant';
import { useUploadAgreement } from '@renderer/hooks/useUploadAgreement';
import LynkerSDK from '@renderer/_jssdk';
import { multipartUpload } from '../../../upload';
import { useMerchantRequest } from '../hooks/useMerchant';
import { useWorkBenchNavigate } from '../../hooks/useNavigate';

// 常量定义
const CONSTANTS = {
  // SIZE_LIMIT: 2 * 1024 * 1024,
  ALLOWED_FILE_TYPES: ['image/jpeg', 'image/png', 'application/pdf'],
  AGREEMENT_TITLE: '另可平台合作协议',
} as const;

// 类型定义
interface Props {
  visible?: boolean;
  isResubmit?: boolean;
  isCommission?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  isResubmit: false,
});

const emit = defineEmits(['update:visible', 'upload-success']);
const visible = useVModel(props, 'visible', emit);
const isResubmit = ref(props.isResubmit);

const merchantStore = useMerchantStore();
const { submitSplitAccountApply, resubmitSeparateAccounts, splitAccountLoading } = useMerchantRequest();
const { toMerchantSplit } = useWorkBenchNavigate();

const agreementUrl = ref('');
const uploadRef = ref<HTMLElement | null>(null);

const {
  fileList,
  loading,
  isDragging,
  setHover,
  validateFile,
  uploadFile,
  uploadSuccess,
  onSelectChange,
  handleDelete,
} = useUploadAgreement({
  allowedFileTypes: CONSTANTS.ALLOWED_FILE_TYPES.slice(),
  multipartUpload,
});

// 打开弹窗，供外部调用
const open = (options?: { isResubmit?: boolean }) => {
  visible.value = true;
  if (options?.isResubmit !== undefined) {
    isResubmit.value = options.isResubmit;
  }
  fileList.value = [];
};
defineExpose({ open });

// 获取平台协议
const getDownloadUrl = async () => {
  const res = await getPlatformAgreement(merchantStore.teamId);
  agreementUrl.value = res.data.data.url;
};

onMountedOrActivated(() => {
  getDownloadUrl();
});

// 上传协议
const applyBindImg = computed(() => {
  const file = fileList.value[0] || {};
  const suffix = file.name.split('.').pop();
  return {
    file_name: file.name,
    file_suffix: suffix,
    file_url: file.url,
  };
});

const submit = async () => {
  await merchantStore.getStatus();

  // 判断商户分账是否为待审核、审核通过
  if (['1', '2'].includes(merchantStore.status.is_separate_accounts)) {
    MessagePlugin.warning('商户分账状态已变更，请重新确认');
    // 刷新页面
    LynkerSDK.workBench.reload();
    return;
  }

  if (isResubmit.value) {
    // 重新提交分账
    await resubmitSeparateAccounts(merchantStore.info.merchant_id, merchantStore.teamId);
  } else {
    // 首次分账申请
    await submitSplitAccountApply(applyBindImg.value, merchantStore.teamId);
  }

  // 跳转到分账申请成功页面
  toMerchantSplit({ status: 'splitSuccess', teamId: merchantStore.teamId });

  emit('upload-success');

  setTimeout(() => {
    visible.value = false;
  });
};

// 导出 handleDownloadAgreement 供模板使用
const handleDownloadAgreement = () => {
  if (!agreementUrl.value) return;
  const suffix = agreementUrl.value.split('.').pop();
  window.LynkerSDK.downloadFile({
    title: `${CONSTANTS.AGREEMENT_TITLE}.${suffix}`,
    url: agreementUrl.value,
  });
};
</script>

<template>
  <t-dialog
    v-model:visible="visible"
    header="上传协议"
    width="448"
    attach="body"
    confirm-btn="开通"
    prevent-scroll-through
    class="merchant-upload-agreement-dialog"
    :close-btn="true"
    :confirm-loading="splitAccountLoading"
    @confirm="submit"
  >
    <template #closeBtn>
      <iconpark-icon name="iconerror" class="text-24 cursor-pointer" />
    </template>

    <div class="content">
      <t-alert theme="info" :title="`请先下载《${CONSTANTS.AGREEMENT_TITLE}》，然后盖章后上传`" />
      <div class="link-wrap">
        <img src="@/assets/zhixing/word.svg" class="w-20 h-20" alt="">
        <t-link
          theme="primary"
          hover="color"
          class="cursor-pointer"
          @click="handleDownloadAgreement"
        >
          下载《{{ CONSTANTS.AGREEMENT_TITLE }}》
        </t-link>
      </div>

      <t-upload
        v-if="fileList.length === 0 || loading"
        ref="uploadRef"
        v-loading="{ loading, size: 'small' }"
        draggable
        :value="fileList"
        accept="image/png,image/jpeg,image/jpg,application/pdf"
        :abridge-name="[6, 6]"
        :before-all-files-upload="validateFile"
        :request-method="uploadFile"
        v-bind="$attrs"
        theme="custom"
        allow-upload-duplicate-file
        :class="{ 'is-dragging': isDragging }"
        @success="uploadSuccess"
        @select-change="onSelectChange"
        @dragenter="setHover(true)"
        @dragleave="setHover(false)"
      >
        <div class="btn-upload">
          <iconpark-icon name="iconadd" class="icon" />
        </div>

        <p class="text-14 color-text-1">点击或将文件拖拽至此区域上传</p>
        <p class="text-14 color-text-3">仅支持jpg、png、pdf格式上传</p>
      </t-upload>

      <div v-else class="uploader has-file">
        <div class="file-item">
          <div class="name">{{ fileList[0].name }}</div>
          <div class="btn-del" @click="handleDelete">删除</div>
        </div>
      </div>
    </div>
  </t-dialog>
</template>

<style lang='less'>
.merchant-upload-agreement-dialog {
  .t-dialog {
    padding: 0;
    background-image: url('@/assets/square/gradient-bg.png');
    background-size: cover;
    background-position: center;
    border: 1px solid var(--divider-kyy_color_divider_light, #eceff5);
    box-shadow: 0px 12px 24px 0px rgba(0, 0, 0, 0.12);
    height: 362px;
    display: flex;
    flex-direction: column;
  }

  .t-dialog__header {
    padding: 16px 16px 16px 24px;
  }

  .t-dialog__body {
    flex: 1;
    border-radius: 12px;
    padding: 0px 12px 0 12px;
  }

  // TODO 样式背景色需调整
  .t-dialog__footer {
    display: flex;
    padding: 24px;
    justify-content: flex-end;
    align-items: center;
    background: var(--bg-kyy_color_bg_light, #fff);
    border-bottom-left-radius: 16px;
    border-bottom-right-radius: 16px;
  }

  .content {
    height: 100%;
    display: flex;
    padding: 12px 12px 0px 12px;
    flex-direction: column;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    background: var(--bg-kyy_color_bg_light, #FFF);
    .link-wrap {
      margin: 8px 0px 16px;
      display: flex;
      height: 32px;
      padding: 0px 4px 0px 8px;
      align-items: center;
      gap: 4px;
      border-radius: 4px;
      background: var(--bg-kyy_color_bg_default, #FFF);
      cursor: pointer;
      &:hover {
        background: var(--bg-kyy_color_bg_list_hover, #F3F6FA);
      }
    }
  }

  .t-alert {
    background: var(--kyy_color_alert_bg_bule, #EAECFF);
    .t-alert__icon {
      display: none;
    }
    .t-alert__title {
      color: var(--text-kyy_color_text_1, #1A2139);
      font-size: 14px;
      font-weight: 400;
      line-height: 22px;
    }
    .t-alert__message {
      display: none;
    }
  }

  .t-upload {
    width: 100%;
    height: 226px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border-radius: 8px;
    background: var(--bg-kyy_color_bg_light, #FFF);
    border: 1px dashed var(--divider-kyy_color_divider_light, #ECEFF5);
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover,
    &.is-dragging {
      background: var(--bg-kyy_color_bg_list_hover, #F3F6FA);
      border-color: var(--kyy_color_primary, #0052D9);
    }

    .t-upload__dragger {
      width: 100%;
      height: 100%;
      border: none;
      padding: 0;
    }

    .t-upload__trigger {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }
  }

  .btn-upload {
    display: flex;
    width: 44px;
    height: 44px;
    justify-content: center;
    align-items: center;
    border-radius: 5.5px;
    border: 1.375px solid var(--kyy_color_upload_border_default, #D5DBE4);
    background: var(--kyy_color_upload_bg, #FFF);
    margin-bottom: 8px;
    .icon {
      font-size: 27.5px;
      color: #828DA5;
    }
  }

  .uploader {
    width: 100%;
    height: 226px;
    padding: 12px;
    display: flex;
    flex-direction: column;
    border-radius: 8px;
    background: var(--bg-kyy_color_bg_light, #FFF);
    border: 1px dashed var(--divider-kyy_color_divider_light, #ECEFF5);
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: var(--bg-kyy_color_bg_list_hover, #F3F6FA);
    }

    &.has-file:hover {
      background: initial;
    }

    .file-item {
      width: 100%;
      display: flex;
      padding: 12px;
      align-items: center;
      gap: 12px;
      border-radius: 8px;
      border: 1px solid var(--divider-kyy_color_divider_light, #ECEFF5);
      background: var(--bg-kyy_color_bg_light, #FFF);
      transition: all 0.3s ease;

      .name {
        flex: 1;
        color: var(--kyy_color_upload_text_success, #1A2139);
        font-size: 14px;
        line-height: 22px;
        .multi-ellipsis(2);
      }

      .btn-del {
        color: var(--color-button_text_error-kyy_color_button_text_error_font_default, #D54941);
        text-align: center;
        font-size: 14px;
        font-weight: 400;
        line-height: 22px;
        cursor: pointer;
      }
    }
  }
}
</style>
