import type { ElectronWindowsManagerOptions } from '@lynker-desktop/electron-window-manager/common';
import type { BrowserWindow } from 'electron';

/** 通信channel */
export const CHANNEL = '__ON_MAIN_WINDOW_CHANNEL__';

export enum h5HostEnum {
  'TEST' = 'https://web-qa.ringkol.com',
  'DEV' = 'https://web-dev.ringkol.com',
  'PRE' = 'https://web-pre.ringkol.com',
  'PROD' = 'https://web.ringkol.com',
}

export enum h5GetwayEnum {
  'TEST' = 'https://h5-qa.ringkol.com',
  'DEV' = 'https://h5-dev.ringkol.com',
  'PRE' = 'https://h5-pre.ringkol.com',
  'PROD' = 'https://h5.ringkol.com',
}

/** sdk 配置 */
export type LynkerSDKConfig = {
  /**
   * 运行环境
   * DEV: 开发环境
   * TEST: 测试环境
   * PRE: 预发布环境
   * PROD: 生产环境
   */
  readonly env: 'DEV' | 'TEST' | 'PRE' | 'PROD';
  /**
   * web端域名
   * h5HostEnum
  */
  readonly h5Host: h5HostEnum;
  /**
   * h5获取方式
   * h5GetwayEnum
  */
  readonly h5Getway: h5GetwayEnum;
  /** 国际化 */
  i18n: string;
  /** 用户token */
  token: string;
  /** 唤起参数 */
  launchOptions: string;
  /** 预加载路径 */
  preloadPath: string;
  /** vite启动配置 */
  viteConfig: {
    VITE_APP_TITLE: string;
    VITE_MAP_KEY: string;
    VITE_APP_REGION: string;
    VITE_API_ENV: string;
    VITE_IM_KEY: string;
    VITE_WPS_KEY: string;
    VITE_UMEN_PID: string;
    VITE_USER_NODE_ENV: string;
    VITE_APP_CONFIG_INFO: string;
    VITE_APP_MAS: boolean;
  }
}

// 预览图片参数类型
export interface PreviewImageOptions {
  images: string[];
  index: number;
  url: string;
}
export type PreviewImageOptions2 = {
  imgIndex: number;
  imgs: Array<{
    title: string;
    url: string;
    type: string;
    size: number;
    officeId: string | null;
    imgIndex: number;
  }>;
};

export interface PreviewVideoOptions {
  title: string;
  url: string;
  type: string;
  size: number;
}
export interface PreviewFileOptions {
  title: string;
  url: string;
  type: string;
  size: number;
  officeId?: string;
}
export interface DownloadFileOptions {
  title: string;
  url: string;
}

// workBench/mainMenu 相关类型
export interface WorkBenchTabOptions {
  fullPath: string;
  title: string;
  icon: string;
  teamId?: string;
  activeIcon?: string;
  pathName?: string;
  updateKey?: string;
  type?: number; // ClientSide 枚举类型，建议 import
  query?: Record<string, string>;
  beforeCloseOptions?: {
    title?: string;
    content?: string;
  };
  beforeRefreshOptions?: {
    title?: string,
    content?: string,
  }
}
export interface WorkBenchOpenTabQuery {
  options: WorkBenchTabOptions;
  openWorkBench?: boolean;
}
export type WorkBenchOpenTabData = boolean | undefined;

export interface WorkBenchOpenTabForIframeQuery {
  title: string;
  url: string;
  path_uuid?: string;
  teamId?: string;
  query?: Record<string, string>;
  beforeCloseOptions?: WorkBenchTabOptions['beforeCloseOptions'];
  beforeRefreshOptions?: WorkBenchTabOptions['beforeRefreshOptions'];
}
export type WorkBenchOpenTabForIframeData = boolean | undefined;

export interface WorkBenchOpenTabForWebviewQuery {
  title: string;
  url: string;
  path_uuid?: string;
  teamId?: string;
  icon?: string;
  activeIcon?: string;
  query?: Record<string, string>;
  beforeCloseOptions?: WorkBenchTabOptions['beforeCloseOptions'];
  beforeRefreshOptions?: WorkBenchTabOptions['beforeRefreshOptions'];
}
export type WorkBenchOpenTabForWebviewData = boolean | undefined;

export interface WorkBenchOpenTabForEnterpriseQuery {
  teamId?: string;
  menuId?: string;
  [key: string]: any;
}
export type WorkBenchOpenTabForEnterpriseData = boolean | undefined;

export interface WorkBenchOpenTabForMerchantApplyQuery {
  teamId?: string;
  [key: string]: any;
}
export type WorkBenchOpenTabForMerchantApplyData = boolean | undefined;

export interface WorkBenchOpenTabForReceiveDetailQuery {
  title: string;
  url?: string;
  teamId?: string;
  sn?: string;
  query?: Record<string, string>;
}
export type WorkBenchOpenTabForReceiveDetailData = boolean | undefined;

export interface WorkBenchOpenTabForRefundDetailQuery {
  title: string;
  url?: string;
  teamId?: string;
  sn?: string;
  query?: Record<string, string>;
  openLeftMenu?: boolean;
}
export type WorkBenchOpenTabForRefundDetailData = boolean | undefined;

export interface WorkBenchOpenTabForSettlementDetailQuery {
  title: string;
  url?: string;
  teamId?: string;
  sn?: string;
}
export type WorkBenchOpenTabForSettlementDetailData = boolean | undefined;

export interface MainMenuOpenWorkBenchQuery {
  url?: string;
  selected_path_uuid?: string;
  click_path_uuid?: string;
  teamId?: string;
  [key: string]: any;
}
export type MainMenuOpenWorkBenchData = boolean | undefined;

export interface DigitalPlatformTabOptions {
  teamId?: string;
  path_uuid?: string;
  // 路由名
  name: string;
  // 路径
  path: string;
  // 图标
  icon: string;
  // 激活图标
  activeIcon: string;
  // 路由标题
  title: string;
  // 路由完整路径
  fullPath: string;
  // 是否固定在顶部 tab 栏，无法移除（首页）
  affix?: boolean;
  // 是否在菜单中不显示
  hidden?: boolean;
  // 角色，personal：个人, office: 企业
  role?: string;
  // 排序，小的排前面
  sort?: number;
  // 父级路径
  parentPath?: string;
  query?: Record<string, string>,
  beforeCloseOptions?: {
    title?: string,
    content?: string,
  },
  beforeRefreshOptions?: {
    title?: string,
    content?: string,
  }
}

export interface DigitalPlatformOpenTabQuery {
  options: DigitalPlatformTabOptions;
  openDigitalPlatform?: boolean;
}
export type DigitalPlatformOpenTabData = boolean | undefined;

export interface DigitalPlatformOpenTabForWebviewQuery {
  title: string;
  url: string;
  path_uuid?: string;
  teamId?: string;
  icon?: string;
  activeIcon?: string;
  query?: Record<string, string>;
  beforeCloseOptions?: WorkBenchTabOptions['beforeCloseOptions'];
  beforeRefreshOptions?: WorkBenchTabOptions['beforeRefreshOptions'];
}
export type DigitalPlatformOpenTabForWebviewData = boolean | undefined;


export interface SquareTabOptions {
  path_uuid?: string;
  value?: string;
  // 标签名称
  label: string;
  // 标签图标
  icon?: string;
  // 标签激活图标
  activeIcon?: string;
  // 完整路径
  fullPath: string;
  // 是否显示未读数
  badgeCount?: number;
  // 标签是否可关闭
  closable?: boolean;
  teamId?: string;
  query?: Record<string, string>,
  beforeCloseOptions?: {
    title?: string,
    content?: string,
  },
  beforeRefreshOptions?: {
    title?: string,
    content?: string,
  }
}
export interface SquareOpenTabQuery {
  options: SquareTabOptions;
  openSquare?: boolean;
}

/**
 * 方法调用数据类型
 * key 为 调用类型字段
 * query 为 调用传参
 * data 为 调用返回数据
 */
export interface MsgQuery {
  /** 获取config */
  getConfig: {
    /** 调用传参 */
    query: undefined,
    /** 返回数据 */
    data: LynkerSDKConfig,
  };
  /** 获取主窗口localStorage */
  getLocalStorage: {
    /** 调用传参 */
    query: string,
    /** 获取主窗口localStorage */
    data: string | null,
  };
  /** 获取主窗口localStorage */
  setLocalStorage: {
    /** 调用传参 */
    query: {
      key: string;
      value: string | null;
    },
    /** 获取主窗口localStorage */
    data: string | null,
  };
  /** 获取启动/唤起参数 */
  getLaunchOptions: {
    /** 调用传参 */
    query: undefined,
    /** string */
    data: string | null,
  };
  /** 清除启动/唤起参数 */
  delLaunchOptions: {
    /** 调用传参 */
    query: undefined,
    /** string */
    data: string | null,
  };
  /** 打开web窗口 */
  openWebWindow: {
    /** 调用传参 */
    query: {
      name: string;
      url: string;
      extraData?: string;
      openDevTools?: boolean;
    },
    /** 获取主窗口localStorage */
    data: number,
  };
  /** 打开浏览器 */
  openBrowser: {
    /** url地址 */
    query: string,
    /** 获取主窗口localStorage */
    data: boolean,
  };
  /** 打开帮助中心 */
  openMyHelpWindow: {
    query: undefined | {
      id: number;
      module_id: number;
      directory_id: number;
    },
    /** 窗口id */
    data: number | undefined,
  };
  /** 打开我的订单 */
  openMyOrderWindow: {
    query: any,
    /** 窗口id */
    data: number | undefined,
  };
  /** 打开发票中心 */
  openMyInvoiceWindow: {
    query: undefined,
    /** 窗口id */
    data: number | undefined,
  };
  /** 打开选人组件 */
  openSelectMember: {
    query: {
      webContentsId?: number,
      query?: {
        /** 菜单类型 recent,follow,friend,orgcontacts,organize,groups */
        menu?: string[];
        /** 最大可选人数 */
        max?: number;
        /** 禁用选项列表 */
        disableList?: any[];
        /** 已选成员列表 */
        selectList?: any[];
        /** 是否允许切换菜单 */
        changeMenus?: boolean;
        /** 是否禁用身份卡切换 */
        disabledCard?: boolean;
        /** 默认激活的身份卡ID数组 */
        activeCardId?: string[];
        /** 是否显示"我的群组"菜单，仅IM转发用 */
        showMyGroupMenu?: boolean;
        /** 是否显示身份切换下拉菜单 */
        showDropdownMenu?: boolean;
        /** IM转发场景下，未选人时确认按钮置灰 */
        isImMember?: boolean;
        /** 群ID，语音通话增加成员时用 */
        groupID?: string;
        /** 自定义数据 */
        diyData?: any[];
        /** 扩展参数 */
        extend?: Record<string, any>;
        /** 业务模块场景扩展，如 ['activity']、['share'] */
        extendFrom?: string[];
        /** 平台需要不受分组权限影响，传true */
        emptyCardId?: boolean;
        /** 只使用activeCardId的组织 */
        useSelectedGroup?: boolean;
      },
    },
    /** callbackId */
    data: string,
  },
  openJoinDigitalPlatformDrawer: {
    query: {
      webContentsId?: number,
      query?: Record<string, any>,
    },
    data: string,
  },
  openOrgAuthDrawer: {
    query: {
      webContentsId?: number,
      query: {
        region: string,
        teamId: string,
        orgType: string,
        showType: 'detail' | 'edit',
      },
    },
    data: string,
  },
  openAddContactsDialog: {
    query: {
      webContentsId?: number,
      query?: {
        searchValue?: string,
      },
    },
    data: string,
  },
  openAnnualFeeDrawer: {
    query: {
      webContentsId?: number,
      query?: {
        teamId: string;
        // 组织广场号id（开通时不用传）
        squareId?: string;
        // 弹窗标题
        title?: string;

        // 是否为开通广场号
        open?: boolean;
        // 组织信息
        // team?: Partial<TeamItem>;
        // 邀请码
        inviteCode?: string;

        // 是否为升级套餐
        upgrade?: boolean;
      },
    },
    data: {
      type: 'success' | 'upgrade-loaded' | 'close';
      data?: any;
    },
  },
  /** 打开一个新窗口 */
  openNewWindow: {
    query: ElectronWindowsManagerOptions,
    /** 窗口id */
    data: number | undefined,
  },
  /** 获取用户信息 */
  getUserInfo: {
    query: undefined;
    data: UserInfo;
  };
  /** 获取唯一ID */
  getUniqueId: {
    query: undefined;
    data: string;
  };
  /** 打开外部应用 */
  openExternalApp: {
    query: { url: string; title?: string; isInApp?: boolean },
    data: string;
  };
  /** 设置环境 */
  setEnv: {
    query: LynkerSDKConfig['env'] | 'RESET';
    data: void;
  };
  /** 检查是否为手动环境 */
  checkIsManualEnv: {
    query: undefined;
    data: boolean;
  };
  /** 获取当前窗口 */
  getCurrentWindow: {
    query: undefined;
    data: BrowserWindow;
  };
  /** 打开外部窗口 */
  openExternalWindow: {
    query: { url: string; options?: ElectronWindowsManagerOptions },
    data: BrowserWindow;
  };
  /** 打开设置窗口 */
  openSettingWindow: {
    query: string;
    data: BrowserWindow;
  };
  /** 打开设置组窗口 */
  openSetGroupWindow: {
    query: undefined;
    data: BrowserWindow;
  };
  /** 打开我的地址窗口 */
  openMyAddressWindow: {
    query: string;
    data: BrowserWindow;
  };
  /** 打开政策详情窗口 */
  openPolicyDetailWindow: {
    query: { url: string },
    data: BrowserWindow;
  };
  /** 打开政策详情窗口2 */
  openPolicyDetailWindow2: {
    query: { url: string },
    data: BrowserWindow;
  };
  /** 注销 */
  logout: {
    query: undefined;
    data: void;
  };
  /** 打开调试工具 */
  openDebugTools: {
    query: undefined;
    data: void;
  };
  /** 预览图像 */
  previewImage: {
    query: PreviewImageOptions | PreviewImageOptions2,
    data: void;
  };
  /** 预览视频 */
  previewVideo: {
    query: PreviewVideoOptions,
    data: void;
  };
  /** 预览文件 */
  previewFile: {
    query: PreviewFileOptions,
    data: void;
  };
  /** 下载文件 */
  downloadFile: {
    query: DownloadFileOptions,
    data: string;
  };
  /** 开始捕获 */
  startCapture: {
    query: undefined;
    data: void;
  };
  /** 停止捕获 */
  stopCapture: {
    query: undefined;
    data: void;
  };
  /** 显示加载中 */
  showLoading: {
    query: {
      id?: string;
      message?: string;
      width?: number;
    };
    data: void;
  };
  /** 隐藏加载中 */
  hideLoading: {
    query: undefined;
    data: void;
  };
  /** 主菜单打开消息 */
  mainMenu_openMessage: {
    query: undefined;
    data: void;
  };
  /** 主菜单打开工作台 */
  mainMenu_openWorkBench: {
    query: MainMenuOpenWorkBenchQuery;
    data: MainMenuOpenWorkBenchData;
  };
  /** 主菜单打开广场 */
  mainMenu_openSquare: {
    query?: undefined;
    data: void;
  };
  /** 主菜单打开数字平台 */
  mainMenu_openDigitalPlatform: {
    query?: {
      teamId?: string;
      query?: Record<string, string>;
    };
    data: void;
  };
  /** 主菜单打开地址簿 */
  mainMenu_openAddressBook: {
    query?: undefined;
    data: void;
  };
  /** 主菜单打开活动 */
  mainMenu_openActivities: {
    query?: undefined;
    data: void;
  };
  /** 主菜单打开网盘 */
  mainMenu_openDisk: {
    query?: undefined;
    data: void;
  };
  /** 消息打开聊天 */
  message_openChat: {
    query: {
      // 自己的身份卡
      main: string,
      // 单聊对方的身份卡
      peer?: string,
      // 群id
      group?: string,
      // 助手id
      assistant?: string,
      // 扩展信息
      extInfo?: {
        // 来源
        type: 'service_content',
        // 内容
        content: any
      },
      rela?: string,
    };
    data: void;
  };
  message_openMessage: {
    query: {
      messageId: string,
      type: 'GROUP' | 'SINGLE',
      cardId: string,
      fromId: string,
    };
    data: void;
  };
  /** 消息打开抽屉用于webview */
  message_openDrawerForWebview: {
    query: {
      id: string;
      title: string;
      url: string;
    };
    data: void;
  };
  /** 消息关闭抽屉用于webview */
  message_closeDrawerForWebview: {
    query: {
      id: string;
    };
    data: void;
  };
  /** 工作台获取当前团队ID */
  workBench_getActiveTeamId: {
    query?: undefined;
    data: string | undefined;
  };
  /** 工作台获取标签列表 */
  workBench_getTabList: {
    query: undefined;
    data: WorkBenchTabOptions[];
  };
  /** 工作台打开标签 */
  workBench_openTab: {
    query: WorkBenchOpenTabQuery;
    data: WorkBenchOpenTabData;
  };
  /** 工作台关闭标签 */
  workBench_closeTab: {
    query: {
      path_uuid: string;
    };
    data: void;
  };
  /** 工作台更新标签 */
  workBench_updateTab: {
    query: {
      path_uuid: string;
      title?: string;
      icon?: string;
      activeIcon?: string;
      beforeCloseOptions?: WorkBenchTabOptions['beforeCloseOptions'];
      beforeRefreshOptions?: WorkBenchTabOptions['beforeRefreshOptions'];
      query?: Record<string, string>,
    };
    data: void;
  };
  /** 工作台打开标签用于iframe */
  workBench_openTabForIframe: {
    query: WorkBenchOpenTabForIframeQuery;
    data: WorkBenchOpenTabForIframeData;
  };
  /** 工作台打开标签用于webview */
  workBench_openTabForWebview: {
    query: WorkBenchOpenTabForWebviewQuery;
    data: WorkBenchOpenTabForWebviewData;
  };
  /** 工作台打开抽屉用于webview */
  workBench_openDrawerForWebview: {
    query: {
      id: string;
      title: string;
      url: string;
      teamId?: string;
    };
    data: void;
  };
  /** 工作台关闭抽屉用于webview */
  workBench_closeDrawerForWebview: {
    query: {
      id: string;
    };
    data: void;
  };
  /** 工作台打开标签用于企业 */
  workBench_openTabForEnterprise: {
    query: WorkBenchOpenTabForEnterpriseQuery;
    data: WorkBenchOpenTabForEnterpriseData;
  };
  /** 工作台打开标签用于商户申请 */
  workBench_openTabForMerchantApply: {
    query: WorkBenchOpenTabForMerchantApplyQuery;
    data: WorkBenchOpenTabForMerchantApplyData;
  };
  /** 工作台打开标签用于接收详情 */
  workBench_openTabForReceiveDetail: {
    query: WorkBenchOpenTabForReceiveDetailQuery;
    data: WorkBenchOpenTabForReceiveDetailData;
  };
  /** 工作台打开标签用于退款详情 */
  workBench_openTabForRefundDetail: {
    query: WorkBenchOpenTabForRefundDetailQuery;
    data: WorkBenchOpenTabForRefundDetailData;
  };
  /** 工作台打开标签用于结算详情 */
  workBench_openTabForSettlementDetail: {
    query: WorkBenchOpenTabForSettlementDetailQuery;
    data: WorkBenchOpenTabForSettlementDetailData;
  };
  /** 工作台打开组织认证 */
  workBench_openTeamCertification: {
    query: {
      teamId?: string;
    };
    data: void;
  };
  /** 工作台刷新 */
  workBench_reload: {
    query: undefined;
    data: boolean;
  };
  /** 数字平台设置当前团队ID */
  digitalPlatform_setActiveTeamId: {
    query: {
      teamId: string;
    };
    data: boolean;
  };
  /** 数字平台获取当前团队ID */
  digitalPlatform_getActiveTeamId: {
    query: undefined;
    data: string | undefined;
  };
  /** 数字平台刷新 */
  digitalPlatform_reload: {
    query: undefined;
    data: boolean;
  };
  /** 数字平台获取标签列表 */
  digitalPlatform_getTabList: {
    query: undefined;
    data: DigitalPlatformTabOptions[];
  };
  /** 数字平台打开标签 */
  digitalPlatform_openTab: {
    query: DigitalPlatformOpenTabQuery;
    data: DigitalPlatformOpenTabData;
  };
  /** 数字平台关闭标签 */
  digitalPlatform_closeTab: {
    query: {
      path_uuid: string;
    };
    data: void;
  };
  /** 数字平台更新标签 */
  digitalPlatform_updateTab: {
    query: {
      path_uuid: string;
      title?: string;
      icon?: string;
      activeIcon?: string;
      beforeCloseOptions?: {
        title?: string,
        content?: string,
      },
      beforeRefreshOptions?: {
        title?: string,
        content?: string,
      },
      query?: Record<string, string>,
    };
    data: void;
  };
  /** 数字平台打开标签用于webview */
  digitalPlatform_openTabForWebview: {
    query: DigitalPlatformOpenTabForWebviewQuery;
    data: DigitalPlatformOpenTabForWebviewData;
  };
  /** 广场获取当前团队ID */
  square_openTab: {
    query: SquareOpenTabQuery;
    data: boolean;
  };
  /** 广场设置当前团队ID */
  square_setActiveTeamId: {
    query: {
      teamId: string;
    };
    data: boolean;
  };
  /** 广场获取当前团队ID */
  square_getActiveTeamId: {
    query: undefined;
    data: string | undefined;
  };
  /** 广场刷新 */
  square_reload: {
    query: undefined;
    data: boolean;
  };
  /** 广场获取标签列表 */
  square_getTabList: {
    query: undefined;
    data: SquareTabOptions[];
  };
  /** 广场关闭标签 */
  square_closeTab: {
    query: {
      path_uuid: string;
      title?: string;
      beforeCloseOptions?: {
        title?: string,
        content?: string,
      },
      beforeRefreshOptions?: {
        title?: string,
        content?: string,
      },
    };
    data: void;
  };
  /** 广场更新标签 */
  square_updateTab: {
    query: {
      path_uuid: string;
      title?: string;
      icon?: string;
      activeIcon?: string;
      beforeCloseOptions?: {
        title?: string,
        content?: string,
      },
      beforeRefreshOptions?: {
        title?: string,
        content?: string,
      },
      query?: Record<string, string>,
    };
    data: void;
  };
  /** 广场打开标签用于webview */
  square_openTabForWebview: {
    query: WorkBenchOpenTabForWebviewQuery;
    data: WorkBenchOpenTabForWebviewData;
  };
  /** 打开新窗口标签 */
  windowsTabs_openTab: {
    query: {
      options: {
        id?: string,
        title?: string,
        url?: string,
        icon?: string,
        activeIcon?: string,
        hideCloseButton?: boolean,
        beforeCloseOptions?: {
          title: string,
          content?: string,
        },
        beforeRefreshOptions?: {
          title?: string,
          content?: string,
        },
        query?: Record<string, string>,
      },
      // 只有一个tab不显示tabs标签
      isOnlyOneTabHideTabs?: boolean,
      tabsId?: string | 'square' | 'digitalPlatform' | 'workBench',
      tabsTitle?: string,
    };
    data: void;
  };
  /** 关闭新窗口标签 */
  windowsTabs_closeTab: {
    query: {
      id?: string;
      tabsId?: string | 'square' | 'digitalPlatform' | 'workBench';
    };
    data: void;
  };
}

/**
 * 电话号码信息
 */
export interface PhoneNumber {
  /** 国家/地区代码 */
  code: string;
  /** 电话号码 */
  phone: string;
}

/**
 * 附件选项配置
 */
export interface AttachmentOption {
  /** 显示方式：1-显示，2-隐藏 */
  display: number;
  /** 是否可编辑：1-可编辑，2-不可编辑 */
  editable: number;
  /** 选项名称 */
  name: string;
  /** 选项类型：手机号、邮箱、性别、生日、地址 */
  type: 'phone' | 'email' | 'gender' | 'birthday' | 'address';
  /** 选项值：可能是电话号码数组、字符串或数字 */
  value: PhoneNumber[] | string | number;
  /** 是否可见：1-可见，2-不可见 */
  visible: number;
  /** 可见类型：1-所有人可见，2-指定用户可见 */
  visible_type: number;
  /** 可见用户列表 */
  visible_users: Record<string, any>;
}

/**
 * 账户登录信息
 */
export interface AccountLoginInfo {
  /** 登录类型：1-邮箱登录，2-手机号登录 */
  loginType: number;
  /** 登录账号 */
  account: string;
}

/**
 * 用户信息
 */
export interface UserInfo {
  /** 用户唯一标识 */
  openid: string;
  /** 用户昵称 */
  title: string;
  /** 用户头像 */
  avatar: string;
  /** 性别：0-未知，1-男，2-女 */
  gender: number;
  /** 邮箱地址 */
  email: string;
  /** 手机号码 */
  cellphone: string;
  /** 固定电话 */
  telephone: string;
  /** 生日时间戳 */
  birthday: number;
  /** 地区代码 */
  region: string;
  /** 是否隐藏 */
  hidden: null;
  /** 个性签名 */
  slogan: string;
  /** 附件信息 */
  attachments: {
    /** 附件选项列表 */
    options: AttachmentOption[];
  };
  /** 地区代码 */
  area: string;
  /** 链接ID */
  link_id: string;
  /** 账户邮箱 */
  account_email: string;
  /** 账户手机号 */
  account_mobile: string;
  /** 账户手机号地区代码 */
  account_mobile_region: string;
  /** 是否已删除 */
  removed: boolean;
  /** 地址信息 */
  address: string;
  /** 是否申请删除 */
  apply_removed: boolean;
  /** 是否已认证 */
  psn_auth: boolean;
  /** 渠道来源 */
  channel: string;
  /** 渠道参数 */
  channel_params: string;
  /** 账户登录信息 */
  account_login_info: AccountLoginInfo;
}
