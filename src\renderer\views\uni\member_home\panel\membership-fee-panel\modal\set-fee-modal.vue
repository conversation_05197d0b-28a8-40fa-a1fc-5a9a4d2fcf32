<template>
  <t-drawer
    v-model:visible="visible"
    :header="autoHeaderText"
    class="drawerSetForm drawerSetBodyNoBottomPadding"
    attach="body"
    :z-index="2500"
    :size="'472px'"
  >
    <div class="toBody">
      <!-- <t-alert theme="info">
					<template #message>
						<span>批量调整后原来的角色都会被替换为新的角色 </span>
					</template>
				</t-alert> -->
      <div class="form">
        <t-form
          ref="form"
          :data="formData"
          label-align="top"
          :rules="rules"
        >
          <t-form-item
            :label="$t('member.svip.fee_unit')"
            name="member_id"
            class="searchForm-item"
          >
            <!-- <t-input
                v-model="formData.member_id"
                :maxlength="50"
                :placeholder="'请输入'"
              /> -->

            <t-select v-replace-svg
              v-model="formData.member_id"
              :keys="{ label: 'combine_name', value: 'id' }"
              :options="regularsOptions"
              filterable
              class="searchForm-item"
              :placeholder="$t('member.impm.select_1')"
              @change="onSelectMember"
            />
          </t-form-item>
          <t-form-item :label="$t('member.svip.member_level')" name="level">
            <t-input
              v-model="formData.level"
              :maxlength="50"
              class="searchForm-item"
              :placeholder="$t('member.svip.auto_outer')"
              disabled
            />
          </t-form-item>
          <t-form-item :label="$t('member.svip.join_time')" name="join_time">
            <t-date-picker
              v-model="formData.join_time"
              :placeholder="$t('member.svip.auto_outer')"
              disabled
              style="width: 100%"
            />
          </t-form-item>
          <t-form-item :label="$t('member.impm.select_2')" name="expire_time">
            <t-date-picker
              v-model="formData.expire_time"
              :maxlength="50"
              :placeholder="$t('member.impm.select_3')"
              style="width: 100%"
              disabled
            />
          </t-form-item>
          <t-form-item :label="$t('member.impm.select_4')" name="type">
            <t-select v-replace-svg
              v-model="formData.type"
              :options="optionsFeeType"
              class="searchForm-item"
              :placeholder="$t('member.impm.select_1')"
            />
          </t-form-item>

          <t-form-item :label="$t('member.impm.select_5')" name="money" class="searchForm-item">
            <t-input-number
              v-model="formData.money"
              class="searchForm-item"
              :decimal-places="2"
              theme="normal"
              :min="0"
              :placeholder="$t('member.impm.select_6')"
              type="number"
            />
          </t-form-item>

          <!-- <t-form-item :label="'币种'" name="currency" class="searchForm-item">
            <t-select v-replace-svg
              v-model="formData.currency"
              :options="optionsCurrency"
              placeholder="请选择"
            />
          </t-form-item> -->
          <t-form-item :label="$t('member.impm.select_7')" name="pay_time">
            <t-date-picker
              v-model="formData.pay_time"
              :format="'YYYY-MM-DD HH:mm'"
              enable-time-picker
              style="width: 100%"
              :placeholder="$t('member.impm.select_1')"
            />
          </t-form-item>
          <!-- <t-form-item
              :label="'缴费凭证'"
              name="attachment"
              class="searchForm-item"
            >
              {{ formData.attachment }}
            </t-form-item> -->
          <!-- <div style="flex: 1; margin-right: 30px">

            </div> -->
          <FFileUpload
            :attrs="formData.attachment"
            :upload-format="uploadFormat"
            :is-onepoint-sixe="true"
            size="20"
          />

          <t-form-item :label="$t('member.impm.select_10')" name="remark" class="searchForm-item">
            <t-textarea
              v-model="formData.remark"
              :maxlength="500"
              :placeholder="$t('member.impm.select_9')"
            />
          </t-form-item>
        </t-form>
      </div>
    </div>
    <template #footer>
      <div class="footer" style="display: flex; justify-content: flex-end">
        <t-button
          theme="default"
          variant="outline"
          @click="onClose"
        >{{ $t('member.impm.select_11') }}</t-button>
        <t-button theme="primary" @click="onSave">{{ $t('member.impm.select_12') }}</t-button>
      </div>
    </template>
  </t-drawer>


</template>

<script lang="ts" setup>
/**
 * @description 批量调整角色弹层
 * <AUTHOR>
 */
import lodash, { debounce } from "lodash";

import { ref, reactive, Ref, computed, toRaw } from "vue";
import { MessagePlugin } from "tdesign-vue-next";
import {
  createMemberOrderAxios,
  getRegularListAxios,
  patchMemberOrderAxios
} from "@renderer/api/uni/api/businessApi";
import { getResponseResult, priceRecovery } from "@renderer/utils/myUtils";
// src\renderer\components\free-from\runtime\controls\FFileUpload.vue
import FFileUpload from "@renderer/components/free-from/runtime/controls/FFileUpload.vue";
import dayjs from "dayjs";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const type = ref(0); // 0创建 1编辑

const form = ref(null);
const rules = {
  member_id: [
    {
      required: true,
      message: t('member.impm.fee_1'),
      type: "error",
      trigger: "change"
    }
  ],
  type: [
    {
      required: true,
      message: t('member.impm.fee_2'),
      type: "error",
      trigger: "change"
    }
  ],

  currency: [
    {
      required: true,
      message: t('member.impm.select_1'),
      type: "error",
      trigger: "change"
    }
  ],
  money: [
    {
      required: true,
      message: t('member.impm.fee_3'),
      type: "error",
      trigger: "change"
    },
    { min: 0, message: t('member.impm.fee_4'), type: "error", trigger: "blur" }

    // { max: 999, message: '输入字数应在0到999之间', type: 'error', trigger: 'blur' },
  ],
  pay_time: [
    {
      required: true,
      message: t('member.impm.select_1'),
      type: "error",
      trigger: "blur"
    }
  ]
  // remark: [
  //   {
  //     required: false,
  //     message: "请输入",
  //     type: "error",
  //     trigger: "blur"
  //   }
  // ]
};
const visible = ref(false);
const optionsFeeType = [
  // 会费类型
  // { label: "会员入会", value: 1 },
  // { label: "会员续期", value: 2 },
  // { label: "其他", value: 3 }
  { label: t('member.impm.hui_1'), value: 1 },
  { label: t('member.impm.hui_2'), value: 2 },
  { label: t('member.impm.hui_3'), value: 3 }
];

const optionsCurrency = [
  { label: "CNY", value: "CNY" },
  { label: "MOP", value: "MOP" },
  { label: "HKD", value: "HKD" }
];

const showStatusOptions = [
  { label: "展示", value: 1 },
  { label: "隐藏", value: 2 }
];
const statusOptions = [
  { label: "停用", value: 1 },
  { label: "启用", value: 2 }
];

const uploadFormat = ['doc', 'docx', 'pdf', 'jpg', 'png', 'jpeg', 'xlsx', 'xls'];

interface Request {
  /**
   * 附件
   */
  attachment: any;
  /**
   * 币种
   */
  currency: string;
  /**
   * 缴费人/单位的会员ID
   */
  member_id: number;
  /**
   * 缴费金额，单位：元
   */
  money: number;
  /**
   * 缴费时间
   */
  pay_time: string;
  /**
   * 备注
   */
  remark: string;
  /**
   * 会费类型， 1：会员入会，2：会员续期，3：其他
   */
  type: number;
}
const attachment_const = {
  required: false,
  id: "upload",
  value: [],
  // name: "缴费凭证",
  name: t('member.impm.hui_4'),
  max: 12,

  editable: true
};
let formData = reactive({
  member_id: undefined, // 缴费人/单位的会员ID
  type: "", // 会费类型， 1：会员入会，2：会员续期，3：其他
  money: "", // 缴费金额，单位：元
  currency: "", // 币种
  pay_time: dayjs(), // 缴费时间
  attachment: lodash.cloneDeep(attachment_const),
  remark: "", // 备注
  level: "", // 会员级别
  join_time: "", // 入会时间
  expire_time: "" // 到期时间,
});

const initForm = () => {
  formData = Object.assign(formData, {
    member_id: undefined, // 缴费人/单位的会员ID
    type: "", // 会费类型， 1：会员入会，2：会员续期，3：其他
    money: "", // 缴费金额，单位：元
    currency: "", // 币种
    pay_time: dayjs(), // 缴费时间
    attachment: lodash.cloneDeep(attachment_const),
    remark: "", // 备注
    level: "", // 会员级别
    join_time: "", // 入会时间
    expire_time: "" // 到期时间,
  });
};

const autoHeaderText = computed(() => (type.value ? t('member.impm.edit_1') : t('member.impm.edit_2')));

const props = defineProps({
  levelOptions: {
    // 会员职务列表
    type: Array,
    default: () => []
  } // 0创建 1编辑
});

const emits = defineEmits(["reload"]);

const onSave = debounce(() => {
  form.value
    .validate({ showErrorMessage: true })
    .then(async (validateResult) => {
      if (validateResult && Object.keys(validateResult).length) {
        console.log(formData);
      } else {
        const params: Request = {
          attachment: formData.attachment,
          currency: formData.currency,
          member_id: formData.member_id,
          money: priceRecovery(formData.money),
          pay_time: `${formData.pay_time}`,
          remark: formData.remark,
          type: Number(formData.type)
        };
        // ...toRaw(formData)
        // params.money = priceRecovery(params.money);
        let res = null;
        try {
          if (!type.value) {
            res = await createMemberOrderAxios(params);
          } else {
            res = await patchMemberOrderAxios(edit_data.value?.id, params);
          }
          res = getResponseResult(res);
          if (!res) return;
          MessagePlugin.success("操作成功");
          setTimeout(() => {
            onClose();
            emits("reload");
          }, 500);
        } catch (error) {
          MessagePlugin.error(error.message);
        }
      }
    });
}, 500);

const regularsOptions = ref([]);
// 获取正式会员列表
const onGetRegularList = async () => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await getRegularListAxios();
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      regularsOptions.value = result.data.list.map((v: any) => {
        delete v.content;
        v.combine_name = v.team_name || v.name;
        console.log(v.combine_name);
        return v;
      });
      resolve(result.data.list);
    } catch (error) {
      const errMsg = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
      reject();
    }
  });
};

/**
 *
 * @param data 值不为空说明为编辑状态
 */
const edit_data = ref(null);
const onOpen = (data?: any) => {
  edit_data.value = null;
  console.log(data);
  if (data) {
    edit_data.value = data;
    type.value = 1;
    // formData.name = data.name;
    // formData.short_name = data.short_name;
    // formData.id = data.id;
    form.value.reset();

    formData.money = Number(data.money / 100);
    formData.level = data.level_name;
    formData.attachment = data.attachment
      ? data.attachment
      : lodash.cloneDeep(attachment_const);
    formData.member_id = data.member_id;
    formData.type = data.type;
    formData.pay_time = data.pay_time;
    formData.remark = data.remark;
    formData.join_time = data.join_time;
    formData.expire_time = data.expire_time;
    formData.currency = data.currency;
    // formData.id = data.id;
  } else {
    type.value = 0;
    // form.value.reset();
    initForm();
  }
  form.value.clearValidate();
  onGetRegularList().then(() => {
    visible.value = true;
  });
};

const onSelectMember = (val) => {
  console.log(val);
  if (regularsOptions.value) {
    const regularItem = regularsOptions.value.find((v) => v.id === val);
    if (regularItem) {
      formData.expire_time = regularItem.expire_time;
      formData.level = regularItem.level_name;
      formData.join_time = regularItem.join_time;
      console.log(props.levelOptions);
      if (props.levelOptions && props.levelOptions.length > 0) {
        const levelItem: any = props.levelOptions.find(
          (v: any) => v.id === regularItem.level
        );
        if (levelItem) {
          formData.currency = levelItem.currency;
          formData.money = levelItem.money;
        }
      }
    }
  }
};

const onClose = () => {
  visible.value = false;
};

defineExpose({
  onOpen,
  onClose
});
</script>

<style lang="less" scoped>
@import "@renderer/views/engineer/less/common.less";
// :deep(.t-form__item) {
//   margin-right: 0;
//   margin-bottom: 0 !important;
// }

:deep(.t-form__label) {
  white-space: wrap !important;
}
:deep(.t-popup) {
  z-index: 2501;
}
.searchForm {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  // gap: 24px;
  &-item {
    min-width: 324px;
    width: 100%;
  }
}
.t-alert--info {
  padding: 8px 16px;
}
.form {
  // margin-top: 10px;
}

// :deep(.t-dialog__body) {
//   overflow: hidden !important;
//   padding-bottom: 0;
// }

.inputFlex {
  display: flex;
  flex-direction: column;
  width: 100%;
  .tips {
    font-size: 14px;

    font-weight: 400;
    color: #717376;
  }
}
.toBody {
  //   max-height: 70vh;
  //   overflow: auto;
  // padding: 0 24px;
}

.t-dialog__ctx .t-dialog__position {
  padding: 0;
}

// :deep(.t-dialog--default) {
//   padding: 0 !important;
// }
</style>
<style lang="less" scoped>
@import "@renderer/views/engineer/less/common.less";

.createUpdate {
  .toBody {
    // height: 68vh;
    // overflow: auto;
    // padding-bottom: 120px;
  }
  .t-dialog__header {
    padding: 0 24px;
  }
  .t-dialog__footer {
    padding: 0 24px;
  }

  .t-dialog--default {
    padding-left: 0;
    padding-right: 0;
  }
}

</style>
