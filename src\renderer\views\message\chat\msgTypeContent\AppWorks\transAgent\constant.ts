import { i18nt } from '@renderer/i18n';

/**
 * 场景类型枚举
 */
export enum SceneType {
  /**
   * 访客申请拒绝
   */
  DELEGATE_TRANSFER_BY_ADMIN_MEMBER = 5082,
  /**
   * 访客申请拒绝
   */
  DELEGATE_TRANSFER_BY_ADMIN_POLITICS = 14042,
  /**
   * 访客申请拒绝
   */
  DELEGATE_TRANSFER_BY_ADMIN_CBD = 16042,
  /**
   * 访客申请拒绝
   */
  DELEGATE_TRANSFER_BY_ADMIN_ASSOCIATION = 19042,
   /**
   * 访客申请拒绝
   */
  DELEGATE_TRANSFER_BY_ADMIN_UNI = 51042,
}

export const getTitle = (type: string) :string => {
  const titleMap = {
    cbd: i18nt('application.digital_cbd'),
    government: i18nt('im.public.government'),
    member: i18nt('im.public.biz'),
    association: i18nt('niche.szsq'),
    uni: i18nt('niche.szgx'),
  };
  return titleMap[type] || '';
};

export const TransAgentSceneTypeArr = Object.values(SceneType).filter((value) => typeof value === 'number');
