<script setup lang='ts'>
import { ref, computed, watch } from 'vue';
import to from 'await-to-js';
import { getApplyStatus } from '@renderer/api/workBench/merchant';
import { DialogPlugin } from 'tdesign-vue-next';
import { ElectronicContractStatus, StatusType } from '@renderer/api/workBench/merchant.model';
import { useVModel } from '@vueuse/core';
import LynkerSDK from '@renderer/_jssdk';
import { onMountedOrActivated } from '@renderer/hooks/onMountedOrActivated';
import { useMerchantStore } from '@renderer/store/modules/merchant';
import { storeToRefs } from 'pinia';
import { useComponentComm } from '../hooks/useComponentComm';
import ApplyInfo from './ApplyInfo.vue';
import UploadAgreement from './UploadAgreement.vue';

type StatusMap = {
  icon: string;
  bgCls: string;
  title: string;
  desc?: string[];
  btnText?: string;
  loading?: boolean;
  handleBtnClick?: (status: StatusType) => void;
};

interface Props {
  /** 组织ID（默认不传，获取当前组织状态） */
  teamId?: string;
  /** 审核状态 */
  modelValue: StatusType;
  /** 是否从IM中调用 */
  from?: 'im' | 'merchant';
  /** IM中调用时，传入的扩展参数 */
  extend?: Record<string, any>;
  /** 是否为分佣入网 */
  isCommission?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  teamId: '',
  modelValue: 'pending',
  from: 'merchant',
  isCommission: false,
});

const emit = defineEmits(['update:modelValue', 'resubmit', 'close', 'to-real-name', 'to-split-account', 'refresh-status']);

const status = useVModel(props, 'modelValue', emit);
const merchantStore = useMerchantStore();

const { status: merchantStatus } = storeToRefs(merchantStore);

// 使用统一组件通信机制
const { createComponentInterface } = useComponentComm({
  step: computed(() => 0), // VerifyStatus可能在任何步骤
  flowControl: computed(() => ({})),
  isCommission: computed(() => props.isCommission || false),
  setFlowStatus: () => { /* 空实现 */ },
  updateFlowStatus: () => { /* 空实现 */ },
});

// 创建组件接口
createComponentInterface('VerifyStatus');
const applyStatus = ref<ElectronicContractStatus>();
const uploadAgreementRef = ref<InstanceType<typeof UploadAgreement>>();
const uploadAgreementVisible = ref(false);

// 是否来自 IM
const isFromIm = computed(() => props.from === 'im');

// 处理 IM 场景下的外跳
const handleOuterRedirect = () => {
  if (isFromIm.value) {
    LynkerSDK.workBench.openTabForMerchantApply({
      teamId: props.teamId,
      menuId: '5',
    });
    return false;
  }
  return true;
};

// 状态映射
const statusMap = ref<Partial<Record<StatusType, StatusMap>>>({
  // 商户入网-审核中
  pending: {
    icon: 'icontime-g4lj3dbh',
    bgCls: 'bg-info',
    title: '审核中',
    desc: ['审核人员会在 1~3 个工作日内完成审核，审核结', '果会通过 <span class="font-600">"小秘书"</span> 进行通知，请注意查收'],
  },
  // 商户入网-审核失败
  failed: {
    icon: 'iconerror-g4lj3da4',
    bgCls: 'bg-error',
    title: '审核失败',
    desc: [],
    btnText: isFromIm.value ? '前往修改' : '去修改',
    handleBtnClick: () => {
      if (!handleOuterRedirect()) return;

      emit('resubmit');
    },
  },
  // 商户入网-签约中
  needSign: {
    icon: 'icontime-g4lj3dbh',
    bgCls: 'bg-info',
    title: '',
    btnText: '确认签约',
  },
  // 商户入网-签约成功
  signSuccess: {
    icon: 'iconsuccess',
    bgCls: 'bg-success',
    title: '商户入网审核已通过',
    desc: ['恭喜您商户入网已审核通过了，请点击下一步', ...(props.isCommission ? ['进行商户分佣'] : ['进行商户的实名认证'])],
    btnText: props.isCommission ? '下一步 商户分佣' : '下一步 商户实名',
    handleBtnClick: () => {
      if (props.teamId) {
        emit('close');
        return;
      }

      if (props.isCommission) {
        emit('to-split-account');
      } else {
        emit('to-real-name');
      }
    },
  },
  // 商户分账-审核中
  splitPending: {
    icon: 'icontime-g4lj3dbh',
    bgCls: 'bg-info',
    title: '提交成功，正审核中',
    desc: ['平台会在1-3个工作日进行审核，请耐心等待'],
  },
  // 商户分账-审核通过
  splitSuccess: {
    icon: 'iconsuccess',
    bgCls: 'bg-success',
    title: '审核通过',
    desc: [props.isCommission ? '恭喜你，审核通过，可以进行分佣' : '恭喜你，审核通过，可进行交易'],
    btnText: '知道了',
    handleBtnClick: () => {
      emit('close');
    },
  },
  // 商户分账-审核失败
  splitFailed: {
    icon: 'iconerror-g4lj3da4',
    bgCls: 'bg-error',
    title: '审核失败',
    desc: [],
    btnText: '重新提交',
    handleBtnClick: () => {
      if (!handleOuterRedirect()) return;

      uploadAgreementRef.value?.open({ isResubmit: true });
    },
  },
  // 审核通过（所有流程都成功）
  success: {
    icon: 'iconsuccess',
    bgCls: 'bg-success',
    title: '审核通过',
    desc: [props.isCommission ? '恭喜您，审核通过，可以进行分佣' : '恭喜您，审核通过，可进行交易'],
    btnText: '知道了',
    handleBtnClick: () => {
      emit('close');
    },
  },
  // 分佣-审核中 TODO 没有这个状态
  commissionPending: {
    icon: 'icontime-g4lj3dbh',
    bgCls: 'bg-info',
    title: '分佣申请审核中',
    desc: ['分佣申请已提交，平台会在1-3个工作日进行审核，请耐心等待'],
  },
  // 分佣-审核通过
  commissionSuccess: {
    icon: 'iconsuccess',
    bgCls: 'bg-success',
    title: '分佣申请审核通过',
    desc: ['恭喜您，分佣申请已审核通过，可以进行分佣操作'],
    btnText: '知道了',
    handleBtnClick: () => {
      emit('close');
    },
  },
});

// IM 场景下的独立状态
const imStatus = ref<StatusType>('failed');

// 统一对外的状态
const realStatus = computed({
  get() {
    if (props.from === 'im' && props.extend) {
      return imStatus.value;
    }
    return status.value;
  },
  set(val: StatusType) {
    if (props.from === 'im' && props.extend) {
      imStatus.value = val;
    } else {
      status.value = val;
    }
  },
});

// 获取 IM 场景下的状态和描述
const getImStatusAndDesc = (extend: Record<string, any>) => {
  const isSplitFailed = extend.is_separate_accounts === 3;
  const statusKey = isSplitFailed ? 'splitFailed' : 'failed';
  const reason = extend.fail_message || extend.message || '';

  const btnText = statusKey === 'splitFailed' ? '前往申请' : '前往修改';
  statusMap.value[statusKey].btnText = btnText;

  return {
    statusKey,
    desc: [`审核不通过：${reason}`],
  };
};

// IM 场景下，强制设置状态
watch(
  () => [props.from, props.extend],
  ([from, extend]) => {
    if (from === 'im' && extend && typeof extend === 'object') {
      const { statusKey, desc } = getImStatusAndDesc(extend);
      imStatus.value = statusKey as StatusType;
      statusMap.value[statusKey].desc = desc;
    }
  },
  { immediate: true, deep: true },
);

const fetchStatus = async () => {
  // 非 IM 场景才自动推导
  if (props.from === 'im' && props.extend) return;

  // FIXME 由于流程状态推导的逻辑在 JoinApply 组件中，这里不需要重复判断
  // 只处理失败状态的描述更新和签约状态的特殊逻辑
  if (['failed', 'splitFailed'].includes(status.value)) {
    statusMap.value[status.value].desc = [`审核不通过：${merchantStatus.value?.message ?? ''}`];
  }

  if (status.value === 'needSign') {
    fetchApplyStatus();
  }

  // 不再自动推导状态，完全依赖父组件传递的 modelValue
};

onMountedOrActivated(fetchStatus);

// 获取签约地址
const fetchApplyStatus = async () => {
  const [err, res] = await to(getApplyStatus(merchantStore.teamId));
  if (err) {
    console.error(err);
    return;
  }

  applyStatus.value = res.data.data;
};

// 处理协议点击事件
const handleAgreementClick = () => {
  if (!applyStatus.value?.sign_h5_url) return;
  LynkerSDK.openExternalWindow(applyStatus.value.sign_h5_url);
};

// 确认签约
const handleConfirmSign = async () => {
  await fetchApplyStatus();

  // 未完成
  if (applyStatus.value?.apply_status === 1) {
    const dialog = DialogPlugin.confirm({
      header: '提示',
      theme: 'info',
      body: '特约商户支付服务合作协议暂未完成签署，请重新确认',
      confirmBtn: '知道了',
      cancelBtn: null,
      onConfirm: () => {
        dialog.hide();
      },
    });
    return;
  }

  await merchantStore.getStatus();
  emit('refresh-status');
};
</script>

<template>
  <div class="status-container">
    <div class="content-wrapper">
      <div v-if="statusMap[realStatus]" :class="['verify-status', statusMap[realStatus].bgCls]">
        <iconpark-icon :name="statusMap[realStatus].icon" class="text-64 mb-16" />
        <div class="verify-status__title">{{ statusMap[realStatus].title }}</div>

        <!-- 状态 -->
        <div class="verify-status__desc">
          <!-- 签约中 -->
          <template v-if="realStatus === 'needSign'">
            <p>需要签署<t-link theme="primary" hover="color" @click="handleAgreementClick">《特约商户支付服务合作协议》</t-link>后才可</p>
            <p>申请入网，请点击协议进行签约</p>
            <p>&nbsp;</p>
            <p>签约成功后再点击【确认签约】进行下一步</p>
          </template>

          <!-- 其他状态 -->
          <template v-else>
            <p v-for="item in statusMap[realStatus].desc" :key="item" v-html="item" />
          </template>
        </div>

        <!-- 按钮 -->
        <div v-if="statusMap[realStatus].btnText" class="verify-status__btn">
          <!-- 签约中 -->
          <t-button
            v-if="realStatus === 'needSign'"
            theme="primary"
            class="min-w-160"
            @click="handleConfirmSign"
          >
            {{ statusMap[realStatus].btnText }}
          </t-button>

          <!-- 其他按钮 -->
          <t-button
            v-else-if="statusMap[realStatus].btnText"
            theme="primary"
            class="min-w-160"
            :loading="statusMap[realStatus].loading"
            @click="statusMap[realStatus].handleBtnClick"
          >
            {{ statusMap[realStatus].btnText }}
          </t-button>
        </div>
      </div>

      <!-- 商户入网（签约）成功 -->
      <template v-if="realStatus === 'signSuccess'">
        <t-divider class="my-16!" />
        <ApplyInfo :team-id="merchantStore.teamId" />
      </template>

      <!-- 商户分账-上传协议 -->
      <UploadAgreement
        ref="uploadAgreementRef"
        v-model:visible="uploadAgreementVisible"
        is-resubmit
        @upload-success="fetchStatus"
      />
    </div>
  </div>
</template>

<style lang='less' scoped>
.status-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  flex: 1;
  border-bottom-right-radius: 8px;
  border-bottom-left-radius: 8px;
  position: relative;
  overflow: hidden;
  margin-bottom: -16px;
}

.content-wrapper {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24px;
  padding-bottom: 0;
  .scrollbar2(0);
}

.verify-status {
  width: 416px;
  min-height: 334px;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 16px;
  padding: 0 32px;
  border-radius: 8px;
  background: var(--bg-kyy_color_bg_light, #FFF);

  &.bg-info {
    background: url('@/assets/bg/bg_one.png') center no-repeat;
    background-size: cover;
  }

  &.bg-error {
    background: url('@/assets/bg/bg_two.png') center no-repeat;
    background-size: cover;
  }

  &.bg-success {
    background: url('@/assets/bg/bg_three.png') center no-repeat;
    background-size: cover;
  }

  &__title {
    width: 100%;
    color: var(--text-kyy_color_text_1, #1A2139);
    text-align: center;
    font-size: 14px;
    font-weight: 600;
    line-height: 22px;
    margin-bottom: 4px;
  }

  &__desc {
    width: 100%;
    color: var(--text-kyy_color_text_2, #516082);
    font-size: 14px;
    line-height: 22px;
    text-align: center;
  }

  .verify-status__btn {
    margin-top: 32px;
  }
}

:deep(.apply-info) {
  overflow-y: initial;
}
</style>
