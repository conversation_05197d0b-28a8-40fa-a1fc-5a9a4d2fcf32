import { businessRequest } from '@/api/apiRequest';
import { MarginDetailData, RefundParams } from './types';

export function advertiseList(params: any) {
	return businessRequest({
		method: 'get',
		url: '/ad-lk/list',
		params,
	});
}

export function advertiseDetail(id) {
	return businessRequest({
		method: 'get',
		url: `/ad-lk/detail/${id}`,
	});
}

export function advertiseReview(params) {
	return businessRequest({
		method: 'post',
		url: '/ad-lk/review',
		data: { ...params },
	});
}

export function spaceList(params) {
	return businessRequest({
		method: 'get',
		url: '/ad-lk/space/list',
		params,
	});
}

export function spacePriceList(params) {
	return businessRequest({
		method: 'get',
		url: '/ad-lk/space/price-list',
		params,
	});
}

export function spacePriceSet(params) {
	return businessRequest({
		method: 'post',
		url: '/ad-lk/space/price-set',
		data: { ...params },
	});
}

export function materialList(params) {
	return businessRequest({
		method: 'get',
		url: '/ad-lk/material/list',
		params,
	});
}

export function materialPublish(params) {
	return businessRequest({
		method: 'post',
		url: '/ad-lk/material/publish',
		data: { ...params },
	});
}

export function materialDetail(id) {
	return businessRequest({
		method: 'get',
		url: `/ad-lk/material/detail/${id}`,
	});
}

export function materialEdit(params) {
	return businessRequest({
		method: 'post',
		url: '/ad-lk/material/edit',
		data: { ...params },
	});
}

export function materialSort(params) {
	return businessRequest({
		method: 'post',
		url: '/ad-lk/material/sort',
		data: { ...params },
	});
}

export function materialDelete(id) {
	return businessRequest({
		method: 'delete',
		url: `/ad-lk/material/${id}`,
	});
}

export function whiteList(params) {
	return businessRequest({
		method: 'get',
		url: '/ad-lk/space/white-list',
		params,
	});
}

export function whiteAdd(params) {
	return businessRequest({
		method: 'post',
		url: '/ad-lk/space/white-add',
		data: { ...params },
	});
}

export function whiteDel(params) {
	return businessRequest({
		method: 'post',
		url: '/ad-lk/space/white-del',
		data: { ...params },
	});
}

export function logList(params) {
	return businessRequest({
		method: 'get',
		url: '/ad-lk/log-list',
		params,
	});
}

export function priceList(params) {
	return businessRequest({
		method: 'get',
		url: '/ad-lk/price-list',
		params,
	});
}

export function externalApp(params) {
	return businessRequest({
		method: 'get',
		url: '/external_app/app',
		params,
	});
}

export function externalAppDetail(app_id: number) {
	return businessRequest<{
		code: number;
		message: string;
		data: MarginDetailData;
	}>({
		method: 'get',
		url: `/external_app/app/${app_id}`,
	});
}

export function marketClassifyTree() {
	return businessRequest({
		method: 'get',
		url: `/market-classify/tree`,
	});
}

export interface BondListParams {
	page: number;
	pageSize: number;
	bond_sn?: string;
	team_name?: string;
	name?: string;
	bond_status?: number;
	payed_at: string;
}

export interface BondItem {
	app_id: number;
	bond_sn: string;
	name: string;
	team_name: string;
	team_logo: string;
	picture_linking: string;
	bond_status: number;
	payed_at: string;
}

// 获取保证金列表
export function getBondList(params: BondListParams) {
	return businessRequest<{
		data: {
			total: number;
			items: BondItem[];
		};
	}>({
		method: 'get',
		url: '/external_app/bond',
		params,
	});
}

// 设置保证金
export function setBond(params) {
	return businessRequest({
		method: 'put',
		url: '/external_app/bond/modify',
		data: { ...params },
	});
}

// 获取保证金设置详情
export function getBondDetail() {
	return businessRequest({
		method: 'get',
		url: '/external_app/bond/detail',
	});
}

// 保证金退款
export function bondRefund(params: RefundParams) {
	return businessRequest({
		method: 'post',
		url: '/external_app/app/refund',
		data: { ...params },
	});
}
