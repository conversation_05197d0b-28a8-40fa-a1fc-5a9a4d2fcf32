<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted, watchEffect, nextTick } from "vue";
import Spin from "./Spin.vue";
import _ from "lodash";

interface Image {
  src: string; // 图像地址
}
interface Props {
  src: string | Image[]; // 图像地址 | 图像地址数组
  width?: string | number; // 图像宽度
  height?: string | number; // 图像高度
  fit?: "contain" | "fill" | "cover"; // 图像如何适应容器高度和宽度
  zoomRatio?: number; // 每次缩放比率
  minZoomScale?: number; // 最小缩放比例
  maxZoomScale?: number; // 最大缩放比例
  resetOnDbclick?: boolean;
  loop?: boolean;
}
const props = withDefaults(defineProps<Props>(), {
  src: "",
  width: 200,
  height: 200,
  bordered: true,
  fit: "contain", // 可选 fill(填充) | contain(等比缩放包含) | cover(等比缩放覆盖)
  preview: "预览",
  zoomRatio: 0.1,
  minZoomScale: 0.5,
  maxZoomScale: 12,
  resetOnDbclick: true,
  loop: false,
});

const images = ref<Image[]>([]);
const emits = defineEmits(["onUpdIndex", "fist", "last"]);

const getImages = () => {
  if (Array.isArray(props.src)) {
    return props.src;
  }
  return [props.src];
};
watchEffect(() => {
  images.value = getImages();
});
const imageCount = computed(() => images.value.length);

onMounted(() => {
  // 监听键盘切换事件
  document.addEventListener("keydown", keyboardSwitch);
});
onUnmounted(() => {
  // 移除键盘切换事件
  document.removeEventListener("keydown", keyboardSwitch);
});
const complete = ref(Array(imageCount.value).fill(false)); // 图片是否加载完成
const loaded = ref(Array(imageCount.value).fill(false)); // 预览图片是否加载完成
const previewIndex = ref(0); // 当前预览的图片索引
const showPreview = ref(true); // 是否显示预览
const rotate = ref(0); // 预览图片旋转角度
const scale = ref(1); // 缩放比例
const swapX = ref(1); // 水平镜像数值符号
const swapY = ref(1); // 垂直镜像数值符号
const sourceX = ref(0); // 拖动开始时位置
const sourceY = ref(0); // 拖动开始时位置
const dragX = ref(0); // 拖动横向距离
const dragY = ref(0); // 拖动纵向距离
const imgRef = ref(null);
const onWheel = _.throttle((e) => {
  // e.preventDefault() // 禁止浏览器捕获滑动事件
  console.log('走进滚动了');
  if (!isMove.value) return;
  const delta = Math.sign(e.deltaY);
  const newScale = scale.value * (1 + (delta > 0 ? -0.1 : 0.1));
  
  scale.value = Math.max(props.minZoomScale, Math.min(props.maxZoomScale, newScale));
}, 100, {leading:true,trailing:false});
const isMove = ref(true);
const onMouseDown = (e) => {
  isMove.value = false;
  e.preventDefault(); // 消除拖动元素时的阴影
  const el = e.target; // 当前点击的元素
  const imageRect = el.getBoundingClientRect();
  const top = imageRect.top; // 图片上边缘距浏览器窗口上边界的距离
  const bottom = imageRect.bottom; // 图片下边缘距浏览器窗口上边界的距离
  const right = imageRect.right; // 图片右边缘距浏览器窗口左边界的距离
  const left = imageRect.left; // 图片左边缘距浏览器窗口左边界的距离
  const imgW = imageRect.width; //获取图片w  * scale.value;
  const imgH = imageRect.height; //获取图片h * scale.value
  sourceX.value = e.clientX; // 鼠标按下时相对于视口左边缘的X坐标
  sourceY.value = e.clientY; // 鼠标按下时相对于视口上边缘的Y坐标
  const boxW = document.documentElement.clientWidth;
  const boxH = document.documentElement.clientHeight - 56;
  let startX = e.clientX;
  let startY = e.clientY;
  el.onmousemove = (e) => {
    e.preventDefault();
    const el = e.target; // 当前点击的元素
    let currentX = e.clientX;
    let currentY = e.clientY;
    let disX = currentX - startX; // 鼠标移动的像素 往右 往上是正数
    let disY = currentY - startY;
    const imageRect = el.getBoundingClientRect();
    const tops = imageRect.top; // 图片上边缘距浏览器窗口上边界的距离
    if (imgW > boxW) {
      if (disX > 0 && (imgW - boxW) / 2 - dragX.value > 0) {
        // 往右走
        dragX.value += disX;
        if ((imgW - boxW) / 2 - dragX.value < 0) {
          dragX.value = (imgW - boxW) / 2;
        }
      } else if (disX < 0 && (imgW - boxW) / 2 + dragX.value > 0) {
        dragX.value += disX;
        if ((imgW - boxW) / 2 + dragX.value < 0) {
          dragX.value = ((imgW - boxW) / 2) * -1;
        }
      }
    }
    if (imgH > boxH) {
      if (disY > 0 && (imgH - boxH) / 2 - dragY.value > 0) {
        dragY.value += disY;
        if ((imgH - boxH) / 2 - dragY.value < 0) {
          dragY.value = (imgH - boxH) / 2;
        }
      } else if (disY < 0 && (imgH - boxH) / 2 + dragY.value > 0) {
        // 往上走
        dragY.value += disY;
        if ((imgH - boxH) / 2 + dragY.value < 0) {
          dragY.value = ((imgH - boxH) / 2) * -1;
        }
      }
    }
    startX = currentX;
    startY = currentY;
  };
  el.onmouseup = () => {
    el.onmousemove = null;
    el.onmouseup = null;
    isMove.value = true;
  };
  el.onmouseleave = () => {
    el.onmousemove = null;
    el.onmouseup = null;
    isMove.value = true;
  };
};
const keyboardSwitch = (e) => {
  if (showPreview.value && imageCount.value > 1) {
    if (e.key === "ArrowLeft" || e.key === "ArrowRight") {
      e.preventDefault(); // 只在处理箭头键时阻止默认行为

      if (e.key === "ArrowLeft") {
        onSwitchLeft();
      }
      if (e.key === "ArrowRight") {
        onSwitchRight();
      }
    }
  }
};
const onLoaded = (index: number) => {
  // 预览图片加载完成
  loaded.value[index] = true;

  // 添加强制重绘逻辑
  nextTick(() => {
    const img = imgRef.value as HTMLImageElement;
    img?.style && (img.style.transform = img.style.transform + ' ');
  });
};

const onPreview = (n: number) => {
  scale.value = 1;
  rotate.value = 0;
  dragX.value = 0;
  dragY.value = 0;
  showPreview.value = true;
  previewIndex.value = n;
};

// 消除js加减精度问题的加法函数
const add = (num1: number, num2: number) => {
  const num1DeciStr = String(num1).split(".")[1];
  const num2DeciStr = String(num2).split(".")[1];
  let maxLen = Math.max(num1DeciStr?.length || 0, num2DeciStr?.length || 0); // 两数中最长的小数位长度
  let num1Str = num1.toFixed(maxLen); // 补零，返回字符串
  let num2Str = num2.toFixed(maxLen);
  const result = +num1Str.replace(".", "") + +num2Str.replace(".", ""); // 转换为整数相加
  return result / 10 ** maxLen;
};
const onClose = () => {
  // 关闭
  showPreview.value = false;
};
const onZoomin = () => {
  scale.value = Math.trunc(scale.value) >= 10 ? 10 : scale.value + 0.2;
};
const onZoomout = () => {
  // 缩小
  scale.value = scale.value - 0.2 <= 0.2 ? 0.2 : scale.value - 0.2; // 缩放最小0.1倍
  let n = Math.round((scale.value - 1) / 1);
  if (dragX.value !== 0) {
    dragX.value -= dragX.value / n;
    if (dragX.value===Infinity||dragX.value===-Infinity) {
      dragX.value=0
    }
  }
  if (dragY.value !== 0) {
    dragY.value -= dragY.value / n;
    if (dragY.value===Infinity||dragY.value===-Infinity) {
      dragY.value=0
    }
  }
};

const onResetOrigin = () => {
  scale.value = 1;
  swapX.value = 1;
  swapY.value = 1;
  rotate.value = 0;

  sourceX.value = 0;
  sourceY.value = 0;
  dragX.value = 0;
  dragY.value = 0;
};
const onClockwiseRotate = () => {
  // 顺时针旋转
  rotate.value += 90;
};
const onAnticlockwiseRotate = () => {
  // 逆时针旋转
  rotate.value -= 90;
};

const onSwitchLeft = () => {
  if (previewIndex.value === 0) {
    emits("fist");
  }
  if (props.loop) {
    previewIndex.value = (previewIndex.value - 1 + imageCount.value) % imageCount.value;
  } else if (previewIndex.value > 0) {
    previewIndex.value--;
  }

  onResetOrigin();
  emits("onUpdIndex", previewIndex.value);
};

const onSwitchRight = () => {
  if (previewIndex.value === imageCount.value - 1) {
    emits("last");
  }
  if (props.loop) {
    previewIndex.value = (previewIndex.value + 1) % imageCount.value;
  } else if (previewIndex.value < imageCount.value - 1) {
    previewIndex.value++;
  }

  onResetOrigin();
  emits("onUpdIndex", previewIndex.value);
};
const editPreviewIndex = (value) => {
  previewIndex.value = value;
};
defineExpose({
  onPreview,
  onResetOrigin,
  onZoomout,
  onZoomin,
  editPreviewIndex,
  previewIndex: previewIndex.value,
  onClockwiseRotate,
  onAnticlockwiseRotate,
  onSwitchLeft,
  onSwitchRight,
});
</script>
<template>
  <div class="m-image-wrap">
    <div class="m-preview-wrap" @click.self="onClose" @wheel="onWheel">
      <div class="m-preview-body">
        <div class="m-preview-image">
          <Spin
            v-for="(image, index) in images"
            v-show="previewIndex === index"
            :key="index"
            :spinning="false"
            indicator="dynamic-circle"
          >
            <!--             :spinning="!loaded[index]"-->
            <img
              ref="imgRef"
              draggable="true"
              class="u-preview-image"
              :style="`transform:translate3d(${dragX}px, ${dragY}px, 0) scale(${scale}, ${scale}) rotate(${rotate}deg);
               image-rendering: crisp-edges;
               backface-visibility: hidden;
               will-change: transform;`" 
              :src="images.length === 0 ? null : image.thumb"
              @dragstart="onMouseDown($event)"
              @load="onLoaded(index)"
              @dblclick="resetOnDbclick ? onResetOrigin() : () => false"
            />
            <!-- @mousedown="onMouseDown($event)" -->
          </Spin>
        </div>
        <template v-if="imageCount > 1">
          <div
            class="m-switch-left"
            v-if="previewIndex !== 0"
            :class="{ 'u-switch-disabled': previewIndex === 0 && !loop }"
            @click="onSwitchLeft"
          >
            <iconpark-icon style="font-size: 48px" name="iconlefe"></iconpark-icon>
          </div>
          <div
            class="m-switch-right"
            v-if="previewIndex !== imageCount - 1"
            :class="{
              'u-switch-disabled': previewIndex === imageCount - 1 && !loop,
            }"
            @click="onSwitchRight"
          >
            <iconpark-icon style="font-size: 48px" name="iconright-b762inpn"></iconpark-icon>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>
<style lang="less" scoped>
.mask-enter-active,
.mask-leave-active {
  transition: opacity 0.25s ease-in-out;
}
.mask-enter-from,
.mask-leave-to {
  opacity: 0;
}
.preview-enter-active,
.preview-leave-active {
  transition: all 0.25s ease-in-out;
}
.preview-enter-from,
.preview-leave-to {
  opacity: 0;
  transform: scale(0.01);
}
.view-img-box:hover {
  .m-switch-right {
    display: flex !important;
  }
  .m-switch-left {
    display: flex !important;
  }
}
.m-image-wrap {
  display: inline-block;
  .m-preview-wrap {
    height: 100%;
    text-align: center;
    .m-preview-body {
      position: absolute;
      inset: 0;
      height: calc(100% - 56px);
      margin-top: 56px;
      overflow: hidden;
      pointer-events: none;
      .m-preview-operations {
        position: fixed;
        width: 100%;
        z-index: 9;
        bottom: 200px;
        display: flex;
        flex-direction: row-reverse;
        align-items: center;
        background: rgba(0, 0, 0, 0.1);
        height: 42px;
        pointer-events: auto;
        .u-name {
          position: absolute;
          left: 12px;
          font-size: 14px;
          color: rgb(255, 255, 255);
          line-height: 1.57;
          max-width: calc(50% - 60px);
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          transition: color 0.3s;
          &:hover {
            // color: @themeColor;
          }
        }
        .u-preview-progress {
          position: absolute;
          left: 50%;
          transform: translateX(-50%);
          font-size: 14px;
          color: rgb(255, 255, 255);
          line-height: 1.57;
        }
        .u-preview-operation {
          line-height: 1;
          padding: 12px;
          cursor: pointer;
          transition: all 0.3s;
          &:not(:last-child) {
            margin-left: 12px;
          }
          &:hover {
            background: rgba(0, 0, 0, 0.25);
          }
          .u-icon {
            display: inline-block;
            width: 18px;
            height: 18px;
            vertical-align: bottom;
            fill: #fff;
          }
        }
        .u-operation-disabled {
          color: rgba(255, 255, 255, 0.25);
          pointer-events: none;
          .u-icon {
            fill: rgba(255, 255, 255, 0.25);
          }
        }
      }

      .m-preview-image {
        position: absolute;
        z-index: 3;
        inset: 0;
        transition: transform 0.3s cubic-bezier(0.215, 0.61, 0.355, 1) 0s;
        display: flex;
        justify-content: center;
        align-items: center;
        .u-preview-image {
          display: inline-block;
          vertical-align: middle;
          max-width: 100%;
          max-height: calc(100vh - 56px);
          cursor: grab;
          transition: transform 0.3s cubic-bezier(0.215, 0.61, 0.355, 1) 0s;
          user-select: none;
          pointer-events: auto;
          transform-style: preserve-3d; // 启用3D变换优化
          backface-visibility: hidden;
          perspective: 1000px;
          transform: translateZ(0); // 强制开启GPU加速
          image-rendering: -moz-crisp-edges;
          image-rendering: crisp-edges;
          object-fit: contain; // 确保图片完整显示
          overflow: hidden; // 隐藏溢出部分
            outline: 1px solid transparent; /* 修复Chrome渲染bug */
        }
      }
      .m-switch-left {
        display: none;
        inset-inline-start: 36px;
        position: fixed;
        inset-block-start: 50%;
        z-index: 1081;
        // display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        transform: translateY(-50%);
        cursor: pointer;
        transition: all 0.3s;
        pointer-events: auto;
      }
      .m-switch-right {
        display: none;
        inset-inline-end: 36px;
        position: fixed;
        inset-block-start: 50%;
        z-index: 1081;
        // display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        transform: translateY(-50%);
        cursor: pointer;
        transition: all 0.3s;
        pointer-events: auto;
      }
      .u-switch-disabled {
        display: none;
        // color: rgba(255, 255, 255, .25);
        // background: transparent;
        // cursor: not-allowed;
        // &:hover {
        //   background: transparent;
        // }
        // .u-switch {
        //   fill: rgba(255, 255, 255, .25);
        // }
      }
    }
  }
}
img {
    -webkit-user-drag:auto  !important;
}
</style>
