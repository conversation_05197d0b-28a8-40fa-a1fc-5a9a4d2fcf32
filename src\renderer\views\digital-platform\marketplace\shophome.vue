<template>
  <!-- {{ route.query }}
  {{ route.path }} -->
  <shopSearch v-if="searching" ref="desiredCompoentRef" @editIsMapPageFlag="editIsMapPageFlag" type-key="null"
    :position="addressData" :home="true" @homeinit="homeinit" />
  <div v-else class="big" :style="{height: isSquare ? 'calc(100vh - 40px)': '100vh'}" @scroll="onScroll" @click.stop="closeLat" :key="pageKey">
    <div class="big-box">
      <shop-header ref="shopHeaderRef" :keyword="keyword" @editIsMapPageFlag="editIsMapPageFlag"
        :fixdHeaderShow="fixdHeaderShow" @homeinit="homeinit" @toSaerch="toSeach" @change-type="changeType" :type="type"
        :addressText="addressText" />
      <div class="banner-conetnt">
        <div class="banner banner">
          <div class="top" style="padding-bottom: 16px" v-show="false">
            <menuWidget :acvtive-key="'index'" @path-click="pathClickRun" />
          </div>
          <div class="bottom">
            <div class="ad-box">
              <member-advertising-space-shop ref="advertisingSpaceRef"
                :teamId="isSquare ? route?.query?.team_id : digitalPlatformStore.activeAccount.teamId"></member-advertising-space-shop>
            </div>
          </div>
        </div>
        <div class="conetnt">
          <div class="list-box" v-infinite-scroll="handleInfiniteOnLoad" :infinite-scroll-immediate-check="false"
            :infinite-scroll-distance="20">
            <template v-for="item in supplyData" :key="item.id">
              <goodsCard :card-data="item" @card-click="viewDetail" />
            </template>
            <!-- <loading v-if="loadingStatus" /> -->
            <nores v-if="!supplyData.length && !loadingStatus" :type="2" />
            <t-divider v-if="supplyData.length && scrollDisabled && !loadingStatus">已经到底啦~</t-divider>
          </div>
        </div>
      </div>
    </div>
    <totop ref="totopRef" />
    <BMap :height="0" @initd="onMapInit" />
  </div>
</template>

<script setup lang="ts" name="bigMarketHome">
import { onActivated, onMounted, ref, toRaw, watch, onBeforeUnmount, computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
import { getProfilesInfo } from "@renderer/utils/auth";
import memberAdvertisingSpaceShop from "@renderer/views/digital-platform/marketplace/components/member-advertising-space-shop.vue"
import { useNicheStore } from "@renderer/store/modules/niche";
import useRouterHelper from "@renderer/views/square/hooks/routerHelper";
import locationWidget from "@/views/big-market/components/location-widget.vue";
import menuWidget from "@/views/digital-platform/marketplace/components/menu-widget.vue";
import "@amap/amap-jsapi-types";
import { marketListGet, searchDigitalPlatformProducts } from "@/views/digital-platform/marketplace/apis";
import shopSearch from "@/views/digital-platform/marketplace/components/shopSearch.vue";
import shopSelect from "@/views/digital-platform/marketplace/components/shop-select.vue";
import goodsCard from "@/views/digital-platform/marketplace/components/goods-card.vue";
import { useDigitalPlatformStore } from "../store/digital-platform-store";
import { platform } from "../utils/constant";
import { getNowDigitalType } from "./utils";
import loading from "@/views/big-market/components/loading.vue";
import nores from "@/views/big-market/components/nores.vue";
import mockSelect from "@/views/big-market/components/mock-select.vue";
import totop from "@/views/big-market/components/totop.vue";
import { useBaiduMap } from "@renderer/components/common/map/hooks";
import shopHeader from "@/views/digital-platform/marketplace/components/shop-header.vue";
import { useTabsStore } from "@renderer/components/page-header/store";
// import { getMemberTeamID } from "@renderer/views/member/utils/auth";

const router = useRouter();
const route = useRoute();
const latWidgetRef = ref(null);
const { t, locale } = useI18n();
const pageNumber = ref(1)

const supplyData = ref([]);
const desiredData = ref([]);
const digitalPlatformStore = useDigitalPlatformStore();

const type = ref(1);
const searching = ref(false);
const loadingStatus = ref(true);
const keyword = ref("");
const pageKey = ref(digitalPlatformStore.richPagekey);

const isSquare = route?.query?.platform === 'square';
const tabStore = useTabsStore(); // 广场那边用到
onMounted(() => {
  nicheStore.positionTextDK = '';
});
onActivated(() => {
  fixdHeaderShow.value = false;
  if (pageKey.value !== digitalPlatformStore.richPagekey) {
    // homeinit();
    // pageKey.value = digitalPlatformStore.richPagekey;
  }
});
const addressData = ref(null);
const addressText = ref(null);
const initStatus = ref(false);
const nicheStore = useNicheStore();

// 地图 IP 定位
const { location, markerInfo, onMapInit } = useBaiduMap({
  onIPLocation() {
    const { point } = location.value;
    console.log('shophomeonIPLocation',point);
    if (!initStatus.value) {
      addressData.value = [point.lng, point.lat];
      nicheStore.positionDK = addressData.value
    }
  },
  onPointGeocoder() {
    console.log('shophomeonIPLocationonPointGeocoder',markerInfo.value.address);
    if (!initStatus.value) {
      addressText.value = markerInfo.value.address;
      nicheStore.positionTextDK = markerInfo.value.address;
      supplyData.value = [];
      pageNumber.value = 1;
      homeDataReq();
    }
  },
})

const editAddressText = (text) => {
  addressText.value = text;
  nicheStore.positionTextDK = text;
};

const shopHeaderRef = ref(null);
const closeLat = () => {
  shopHeaderRef.value?.latClose();
};


const supplyTotal = ref(0);
const desiredTotal = ref(0);
const advertisingSpaceRef = ref(null);
const { ipcRenderer } = require("electron");
ipcRenderer.on("edit-ad-swiper", (event, val) => {
  advertisingSpaceRef.value.editAdSwiper(val);
  // 把轮播暂停edit-ad-swiper
  console.log(val, "把组件暂停");
});
onBeforeUnmount(() => {
  console.log('onBeforeUnmount');

  ipcRenderer.removeAllListeners("edit-ad-swiper");
});
const homeDataReq = async () => {
  loadingStatus.value = true;
  // const teamId = '620659391103373312';
  const teamId = isSquare ? route?.query?.team_id : digitalPlatformStore.activeAccount.teamId;
  const listParams = {
    'page.number': pageNumber.value,
    'page.size': 50,
    'latLng.longitude': addressData.value[0],
    'latLng.latitude': addressData.value[1],
    'page.nextPageToken': '',
    'page.prevPageToken': '',
    teamId: teamId,
    // teamId: digitalPlatformStore.activeAccount.teamId,
  };
  console.log(listParams, "listParams");
  const res = await searchDigitalPlatformProducts(listParams, teamId);
  console.log(res, "listParamsres");
  const data = res.data.data
  loadingStatus.value = false;
  supplyData.value = supplyData.value.concat(data.products);
  supplyTotal.value = data.total;
  console.log(supplyData.value, "supplyData.value");
};

const editAddressData = (data) => {
  addressData.value = data;
  nicheStore.positionDK = addressData.value;
};
const editIsMapPageFlag = (val, name?) => {
  if (val) {
    console.log(val, name, 'shop-header editIsMapPageFlag');
    if (name) {
      addressText.value = name;
      nicheStore.positionTextDK = addressText.value;
    }
    addressData.value = [val.location.lng, val.location.lat];
    nicheStore.positionDK = addressData.value;
    supplyData.value = [];
    pageNumber.value = 1;
    homeDataReq();
  }
};

const fixdHeaderShow = ref(false);
const onScroll = (e) => {
  if (e.target.scrollTop >= 72) {
    fixdHeaderShow.value = true;
  } else {
    fixdHeaderShow.value = false;
  }
  closeLat();
};

const viewDetail = (row) => {
  console.log("viewDetail", row);
  onOpenRich(row);
};
const digitalRouter = useRouterHelper("digitalPlatformIndex");
const onOpenRich = (row) => {
  const type = getNowDigitalType();

  if(isSquare) {
    const pageKey = `square_digital_platform_${type}_rich_detail`;
    const fullPath = `/square-alone/${pageKey}?platform=square&team_id=${route?.query?.team_id}`;
    console.log(fullPath)
    tabStore.addTab({ label: `${row.title}`, fullPath, icon: 'marketvip' });
    router.push(fullPath);
    return;
  } 

  const pageKey = `digital_platform_${type}_rich_detail`;
  const searchMenu = digitalRouter.routeList.find((v) => v.name.includes(pageKey));
  searchMenu.query = { uuid: row.uuid, platform: platform.digitalPlatform, from: type };
  router.push({ path: searchMenu.fullPath, query: { ...searchMenu.query } });
  searchMenu.title = row.title;
  digitalPlatformStore.addTab(toRaw({ ...searchMenu }), true);
};

const getUUID = computed(() => {
  const type = getNowDigitalType();
  const uuids = {
    member: '数字商协-市场',
    cbd: '数字CBD-市场',
    association: '数字社群-市场',
    politics: '数字城市-市场',
  }
  return uuids[type]
})

const toPage = (key) => {
  const type = getNowDigitalType();
  const pathMap = {
    index: `digital_platform_${type}_rich`,
    supply: `digital_platform_${type}_supply`,
    desired: `digital_platform_${type}_desired`,
  };
  const titleMap = {
    index: t("market.index"),
    supply: t("market.sc"),
    desired: t("market.xq"),
  };
  const searchMenu = digitalRouter.routeList.find((v) => v.name.includes(pathMap[key]));
  searchMenu.query = { platform: platform.digitalPlatform, from: type };
  router.push({ path: searchMenu.fullPath, query: searchMenu.query });
  searchMenu.title = titleMap[key];
  digitalPlatformStore.addTab(toRaw(searchMenu), false);
};

const desiredCompoentRef = ref(null);
const toSeach = (params) => {
  if (!params.keyword?.length) {
    return;
  }
  keyword.value = params.keyword;
  searching.value = true;
  console.log(desiredCompoentRef.value);
  setTimeout(() => {
    console.log(desiredCompoentRef.value);
    desiredCompoentRef.value.homeSearch({
      keyword: keyword.value,
      addressText: addressText.value,
      position: addressData.value,
      type: type.value,
    }, addressText.value);
  }, 1);
};

const homeinit = () => {
  initStatus.value = true;
  searching.value = false;
  keyword.value = "";
  fixdHeaderShow.value = false;
  digitalPlatformStore.richPagekey += 1;
  pageKey.value = digitalPlatformStore.richPagekey;
  supplyData.value = [];
  pageNumber.value = 1;
  homeDataReq();
  setTimeout(() => {
    initStatus.value = false;
  }, 2000);
};
const emits = defineEmits(["openRefresh"]);
const homeRefresh = () => {
  homeinit();
};

watch(
  () => route.path,
  () => {
    console.log(" route.path", route.path);
    console.log("route.query?.refresh", route.query?.refresh);
  },
);
const some = () => {
  console.log("方法有用，请勿删除");
};
const typeSelectEvent = (e) => {
  type.value = e;
};
const pathClickRun = (key) => {
  console.log("ley", key);
  if (key === "index") {
    homeRefresh();
  }
};

const changeType = (val) => {
  type.value = val;
};
const scrollDisabled = computed(() => supplyData.value.length >= supplyTotal.value);
const handleInfiniteOnLoad = () => {
  console.log("触发了");
  if (!scrollDisabled.value) {
    pageNumber.value += 1;
    homeDataReq();
  }
};



</script>

<style lang="less" scoped>
.big::-webkit-scrollbar {
  width: 0px;
}

.big {
  display: flex;
  width: 100%;
  flex-direction: column;
  align-items: center;
  height: 100vh;
  background-image: url(@/assets/big-market/mbg.png);
  overflow-y: auto;
  background-position: center;
  background-size: 100% 100%;
  background-repeat: no-repeat;

  .big-box {
    width: 1216px;
  }

  .banner-conetnt {
    width: 1216px;
    padding: 16px 24px;
    border-radius: 16px 16px 0px 0px;
    background: linear-gradient(180deg, #fff 5.63%, #f5f8fe 18.66%);
  }

  .index-header {
    display: flex;
    // justify-content: space-between;
    width: 100%;
    align-items: center;
    padding: 16px 24px;
    gap: 16px;

    .logo-box {
      display: flex;
      align-items: center;
      gap: 12px;
      cursor: pointer;

      .logo {
        width: 32px;
        height: 32px;
      }

      .font_cn {
        width: 60px;
        height: 40px;
      }

      .name {
        color: var(--text-kyy_color_text_2, #516082);
        font-family: YouSheBiaoTiHei;
        font-size: 32px;
        font-style: normal;
        font-weight: 400;
        line-height: 40px;
        /* 125% */
      }
    }

    .search-bar {
      display: flex;
      width: 520px;
      height: 32px;
      padding: 0px 2px 0px 4px;
      align-items: center;
      gap: 12px;
      flex-shrink: 0;
      border-radius: 4px;
      border: 2px solid var(--brand-kyy_color_brand_hover, #707eff);
      background: var(--input-kyy_color_input_bg_default, #fff);

      .search-btn {
        width: 48px;
        height: 24px;
        flex-shrink: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        border-radius: var(--radius-kyy_radius_button_s, 4px);
        background: var(--brand-kyy_color_brand_hover, #707eff);

        .iconsearch {
          font-size: 20px;
          color: #fff;
        }
      }
    }
  }

  .banner {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    align-self: stretch;
    border-radius: 8px;
    /* background: #fff; */

    .top {
      width: 100%;
    }

    .bottom {
      display: flex;
      gap: 12px;
      width: 100%;

      .ad-box {
        width: 100%;
        height: 308px;
      }
    }
  }

  .conetnt {
    width: 100%;
    margin-top: 24px;

    .title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      align-self: stretch;
      margin-bottom: 16px;

      .title-name {
        position: relative;

        .text {
          font-family: "PingFang SC";
          font-size: 24px;
          font-style: normal;
          font-weight: 600;
          line-height: 32px;
          /* 133.333% */
          z-index: 2;
          position: relative;
          text-indent: 12px;
        }

        .text-supply {
          color: var(--cyan-kyy_color_cyan_default, #11bdb2);
        }

        .text-desired {
          color: var(--warning-kyy_color_warning_default, #fc7c14);
        }

        .text-tag {
          position: absolute;
          top: 0px;
          left: 0px;
          width: 19px;
          height: 15px;
        }

        .tag {
          position: absolute;
          bottom: 4px;
          width: 144px;
          height: 8px;
          flex-shrink: 0;
          background: linear-gradient(270deg, rgba(247, 255, 254, 0.3) 4%, rgba(49, 228, 217, 0.3) 100%);
          z-index: 1;
        }

        .tag2 {
          position: absolute;
          bottom: 0px;
          width: 144px;
          height: 8px;
          flex-shrink: 0;
          z-index: 1;
          background: linear-gradient(270deg, rgba(141, 250, 243, 0) 4%, rgba(133, 231, 221, 0.7) 100%);
        }

        .tag3 {
          position: absolute;
          bottom: 4px;
          width: 144px;
          height: 8px;
          flex-shrink: 0;
          background: linear-gradient(270deg, rgba(255, 239, 226, 0.3) 4%, rgba(253, 146, 86, 0.3) 100%);
          z-index: 1;
        }

        .tag4 {
          position: absolute;
          bottom: 0px;
          width: 144px;
          height: 8px;
          flex-shrink: 0;
          z-index: 1;
          background: linear-gradient(270deg, rgba(252, 124, 20, 0) 4%, rgba(255, 168, 98, 0.56) 100%);
        }
      }

      .more {
        display: flex;
        height: 24px;
        min-height: 24px;
        max-height: 24px;
        padding: 0px 4px 0px 12px;
        justify-content: center;
        align-items: center;
        gap: 4px;
        border-radius: 123px;
        background: var(--color-button_border-kyy_color_buttonBorder_bg_default, #fff);
        color: var(--color-button_border-kyy_color_buttonBorder_text_default, #516082);
        text-align: center;
        cursor: pointer;
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;

        /* 157.143% */
        .iconmore {
          font-size: 20px;
        }
      }

      .more:hover {
        background: var(--bg-kyy_color_bgBrand_hover, #eaecff);
        color: var(--brand-kyy_color_brand_hover, #707eff);
      }
    }

    .list-box {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
      column-gap: 12px;
    }
  }
}

.fixd-header-box2 {
  width: 100%;
  margin: 0 auto;
  position: fixed;
  top: 40px;
  background: #fff;
  box-shadow: 0px 3px 8px 0px rgba(0, 0, 0, 0.12);
  z-index: 1000;
}

.fixd-header-box {
  width: 100%;
  margin: 0 auto;
  position: fixed;
  top: 40px;
  background: #fff;
  box-shadow: 0px 3px 8px 0px rgba(0, 0, 0, 0.12);
  z-index: 1000;

  .fixd-header {
    display: flex;
    width: 1216px;
    gap: 16px;
    background: #fff;
    padding: 16px 24px;
    align-items: center;
    margin: 0 auto;

    .logo-box {
      display: flex;
      align-items: center;
      gap: 12px;
      min-width: 125px;
      cursor: pointer;

      .logo {
        width: 32px;
        height: 32px;
      }

      .font_cn {
        width: 60px;
        height: 40px;
      }

      .name {
        color: var(--text-kyy_color_text_2, #516082);
        font-family: YouSheBiaoTiHei;
        font-size: 32px;
        font-style: normal;
        font-weight: 400;
        line-height: 40px;
        /* 125% */
      }
    }

    .line {
      display: flex;
      height: 24px;
      min-width: 1px;
      max-width: 1px;
      align-items: flex-start;
      gap: 4px;
      background: var(--divider-kyy_color_divider_deep, #d5dbe4);
    }

    .search-bar {
      display: flex;
      width: 424px;
      height: 32px;
      padding: 0px 2px 0px 4px;
      align-items: center;
      gap: 12px;
      flex-shrink: 0;
      border-radius: 4px;
      border: 2px solid var(--brand-kyy_color_brand_hover, #707eff);
      background: var(--input-kyy_color_input_bg_default, #fff);

      .search-btn {
        width: 48px;
        height: 24px;
        flex-shrink: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        border-radius: var(--radius-kyy_radius_button_s, 4px);
        background: var(--brand-kyy_color_brand_hover, #707eff);

        .iconsearch {
          font-size: 20px;
          color: #fff;
        }
      }
    }
  }
}

:deep(.t-back-top) {
  background: var(--bg-kyy_color_bg_mask, rgba(0, 0, 0, 0.5));
  border: none;
  box-shadow: none;
}

.tag-mu {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-right: 24px;

  .tag-box {
    display: flex;
    width: 64px;
    padding: 4px 0px;
    text-align: center;
    justify-content: center;
    align-items: center;
    gap: 10px;
    cursor: pointer;
  }

  .tag-box-act {
    border-radius: 4px;
    background: #707EFF;
    color: var(--text-kyy_color_text_white, #FFF);
    text-align: center;
    font-family: "PingFang SC";
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
    /* 150% */
  }

  .tag-box-shop {
    border-radius: 4px;
    color: var(--warning-kyy_color_warning_active, #BE5A00);
    text-align: center;
    font-family: "PingFang SC";
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
    /* 150% */
  }

  .tag-box-niche {
    border-radius: 4px;
    color: var(--cyan-kyy_color_cyan_default, #11BDB2);
    text-align: center;
    font-family: "PingFang SC";
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
    /* 150% */
  }
}

.line-geduan {
  width: 1px;
  height: 24px;
  background: var(--divider-kyy_color_divider_deep, #D5DBE4);
  margin: 0px 16px;
}
:deep(.t-divider__inner-text){
  color: #828da5;
}
</style>
