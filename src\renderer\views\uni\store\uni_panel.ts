import { defineStore } from "pinia";

export const useAssociationPanelStore = defineStore("uni_panel", {
  state: () => ({
    params: null,
  }),
  actions: {
    setParamsKey(key: string, value) {
      if(this.params) {
        this.params[key] = value;
      } else {
        this.params = {
          [key]: value,
        }
      }

    },
    setParams(val: any) {
      this.params = val;
    },
  },
})
