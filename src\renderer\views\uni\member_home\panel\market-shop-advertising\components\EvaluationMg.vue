<template>
  <div>
    <t-dialog :closeOnOverlayClick="false" :closeBtn="false" :closeOnEscKeydown="false" v-model:visible="visible"
      header="评价" class="adSelectShopWin" @close="closeWin()" width="876">
      <template #header>
        <div style="
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;">
          <span>评价</span>
          <iconpark-icon class="iconhover" size="24" name="iconerror" @click="closeWin()" />
        </div>
        <!-- @click="visible = false" iconerror-->
      </template>
      <div class="box">
        <div class="head-box">
          <div class="head-box-flex">
            <img class="head-box-img" src="@/assets/<EMAIL>">
            <div class="head-box-flex-item">
              <div class="head-box-title">
                啊实打实大师啊实打实大师啊实打实大师啊实打实大师啊实打实大师啊实打实大师
              </div>
              <div class="head-box-dis">
                111111啊实打实大师啊实打实大师啊实打实大师啊实打实大师啊实打实大师啊实打实大师
              </div>
            </div>
          </div>
          <div class="evaluation-box">
            <div class="evaluation-box-flex">
              <star-rating :readonly="isView" v-model="rating" />
            </div>
            <div v-if="isView" class="evaluation-box-flex-dis">
              啊实打实大师啊实打实大师啊实打实大师啊实打实大师啊实打实大师啊实打实大师啊实打实大师啊实打实大师啊实打实大师啊实打实大师啊实打实大师啊实打实大师啊实打实大师啊实打实大师啊实打实大师啊实打实大师啊实打实大师啊实打实大师啊实打实大师啊实打实大师啊实打实大师啊实打实大师啊实打实大师啊实打实大师啊实打实大师啊实打实大师啊实打实大师啊实打实大师啊实打实大师啊实打实大师
            </div>
            <div class="input-box">

              <t-textarea class="textarea" :autosize="{minRows: 4, maxRows: 4}" maxlength="200"
                placeholder="你的建议可能会帮到更多人！欢迎畅所欲言~" />
            </div>
            <div v-if="isView" class="view-box">
              <img class="head-box-img" src="@/assets/<EMAIL>">
              <img class="head-box-img" src="@/assets/<EMAIL>">
              <img class="head-box-img" src="@/assets/<EMAIL>">
            </div>
            <div v-else class="view-box">
              <div class="uploadRef-box">
                <t-upload style="display: none" ref="uploadRef" theme="image"
                  :before-all-files-upload="(file) => uploadCheck(file, 3)" v-model="formData.voucher" class="none-box"
                  multiple accept="image/jpg,image/jpeg,image/png" :request-method="onSelectChange" :max="5"
                  :action="null" />
                <div v-if=" formData.voucher.length < 5" class="upd-box" @click="updFileFn">
                  <iconpark-icon class="updimg" name="iconimg" style="font-size: 32px; color: #828da5"></iconpark-icon>
                  <span style="color: #516082; cursor: pointer">{{ t('order.clickpd') }}</span>
                </div>
                <div>
                  <div class="el-box">
                    <div v-for="(item, index) in formData.voucher" :key="index" class="a-item">
                      <img class="updend-img" :src="item" @click="viewimgFn(item)" />
                      <img class="close-img" src="@/assets/img/image7.png" @click="delImg(index)" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="foot-shop-box">
          <div class="ckdNum">
            <t-checkbox :disabled='isView'><span :class="isView?'no-click':'is-tip'">匿名评价</span><span
                :class="isView?'no-click':'is-span'">（隐藏你的头像和昵称）</span></t-checkbox>
          </div>
          <div class="foot-shop-btn">
            <t-button theme="primary">
              发布
            </t-button>
          </div>
        </div>
      </template>
    </t-dialog>
  </div>
</template>
<script setup lang="ts">
  import { DialogPlugin, MessagePlugin } from "tdesign-vue-next";
  import { useI18n } from "vue-i18n";
  import sdk from '@lynker-desktop/web';
  import { getStsToken } from '@renderer/api/cloud';
  import { formatDate } from '@/utils/date';
  import { imgType } from '@/views/zhixing/constant';
  import { getFileType } from "@/views/message/service/utils";
  import to from 'await-to-js';
  import { ref, onMounted, computed, watch, reactive, onUnmounted } from "vue";
  import { businessListSQ } from "@renderer/api/business/index";
  import upload from '@/views/square/utils/upload';
  import { useRoute } from "vue-router";
  import { useDigitalPlatformStore } from "@renderer/views/digital-platform/store/digital-platform-store";
  import { platform } from "@renderer/views/digital-platform/utils/constant";
  import { getMemberTeamID } from "@renderer/views/member/utils/auth";
  import OSS from 'ali-oss';

  import StarRating from "./StarRating.vue";
  const rating = ref(4); // 初始评分为4星
  const digitalPlatformStore = useDigitalPlatformStore();
  const uploadRef = ref(null);
  const client = ref(null);
  const paddingUdpList = ref([]);

  const shopType = ref(null);
  const shopStats = ref(null);
  const formData = ref({
    voucher: []

  })
  const isView = ref(false);
  const updFileFn = () => {
    uploadRef.value.triggerUpload();
  };
  const delImg = (index) => {
    formData.value.voucher.splice(index, 1);
  };
  const route = useRoute();
  const tableData = ref([]);
  const selectedRowKeys = ref([2]);
  const selectOnRowClick = ref(true);

  const viewimgFn = (item) => {
    sdk.ipcRenderer.invoke(
      'preview-file',
      JSON.stringify({
        url: item,
        type: 'jpg',
      }),
    );
  };


  const searchValue = ref("");
  const props = defineProps({
    platform: {
      type: String,
      default: "",
    },
    changeType: {
      type: String,
      default: "single", //single/multiple
    }
  });

  const { t } = useI18n();
  const onSelectChange = async (file) => {
    console.log(file, "onSelectChange");
    let paddingUdpListTemp = file;

    getStsToken().then((res) => {
      client.value = new OSS({
        // yourRegion填写Bucket所在地域。以华东1（杭州）为例，Region填写为oss-cn-hangzhou。
        region: "oss-cn-shenzhen",
        // 从STS服务获取的临时访问密钥（AccessKey ID和AccessKey Secret）。
        accessKeyId: res.data.data.AccessKeyId,
        accessKeySecret: res.data.data.AccessKeySecret,
        // 从STS服务获取的安全令牌（SecurityToken）。
        stsToken: res.data.data.SecurityToken,
        // 刷新临时访问凭证的时间间隔，单位为毫秒。
        refreshSTSTokenInterval: 300000,
        // 填写Bucket名称。
        bucket: "kuaiyouyi",
      });
      paddingUdpListTemp.map((e) => {
        let fileName = e.name;
        let types = fileName.substring(fileName.lastIndexOf(" .") + 1);
        e.name = `disk/${formatDate(new Date().getTime())}/${types}`;


        client.value.multipartUpload(e.name, e.raw, {})
          .then((resq) => {
            console.log(resq, '啊实打实打撒resqqqq');
            formData.value.voucher.push(resq.res.requestUrls[0]);
            console.log(formData.value.voucher, '啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊');

            // resolve(res);
          })
          .catch((err) => {
            // resolve(err);

            // reject(err, 'errerrerr');
            console.log(err, "errerrerr");
          });
      });
    });
  };
  const uploadCheck = (files, limitNumber = 20) => {
    if (files?.[0]?.size > limitNumber * 1024 * 1024) {
      MessagePlugin.error(`${t("activity.activity.uploadTip0")}${limitNumber}M`);
      return false;
    }
    if (!imgType.includes(getFileType(files?.[0]?.name).toLowerCase())) {
      MessagePlugin.error(t("activity.activity.uploadTip11"));
      return false;
    }
  };
  // 文件上传方法
  const requestMethod = async (files) => {

    const [uploadFile] = files;
    const file = uploadFile.raw;
    const allowFileTypes = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg', '.webp', '.svg']
    // 格式校验
    if (!allowFileTypes.some((type) => file.name.toLowerCase().endsWith(type))) {
      await MessagePlugin.warning(t('activity.activity.uploadTip3'));
      return Promise.resolve({ status: 'fail' });
    }

    // 文件大小校验
    if (file.size > 30 * 1024 * 1024) {
      const confirmDia = DialogPlugin.alert({
        header: t('activity.activity.tip'),
        theme: 'info',
        body: `上传的文件大小不能超过30M`,
        closeBtn: null,
        confirmBtn: t('activity.activity.confirm'),
        onConfirm: async () => {
          confirmDia.hide();
        },
        onClose: () => {
          confirmDia.hide();
        },
      });

      return Promise.resolve({ status: 'fail' });
    }

    const [error, res] = await to(
      upload(file, {
        rename: true,
        rootDir: 'activity',
      }),
    );

    if (error) {
      await MessagePlugin.warning(t('activity.activity.uploadFail'));
      return Promise.resolve({ status: 'fail' });
    }

    // 图片文件则上传完成
    if (imgType.some((type) => file.name.endsWith(type))) {
      return Promise.resolve({ status: 'success', response: { url: res.url } });
    }

    // 非图片文件获取文件id用于预览
    const { file_id } = await getFilePreviewId(res.url, file.name, file.size);
    return Promise.resolve({ status: 'success', response: { url: res.url, file_id } });
  };
  // 状态管理
  const visible = ref(false);
  const items = ref([]); // 存储所有搜索结果
  const displayedItems = ref([]); // 当前显示的分页结果
  const loading = ref(false); // 是否正在加载数据
  const loadingMore = ref(false); // 是否正在加载更多数据
  const currentPage = ref(1); // 当前页码
  const pageSize = ref(10); // 每页显示的条目数量

  const platformCpt = computed(() => {
    return props.platform || route.query?.platform;
  });
  const currentTeamId = computed(() => {
    if (platformCpt.value === platform.digitalPlatform) {
      return digitalPlatformStore.activeAccount?.teamId;
    } else if (platformCpt.value === platform.digitalWorkbench) {
      return route.query?.teamId || 0;
    } else {
      if (!getMemberTeamID()) {
        return route.query?.teamId || 0;
      }
      return getMemberTeamID();
    }
  });
  const total = ref(0);
  // 获取数据的函数
  const fetchData = async () => {
    console.log(currentTeamId, "触发这里");
    businessListSQ({
      title: searchValue.value,
      page: currentPage.value,
      pageSize: pageSize.value,
      promotion_type: 2,
      promotion_related: currentTeamId.value,
      // promotion_related: "649990269132623872",
    }).then((res) => {
      displayedItems.value = res.data.data.list;
      total.value = res.data.data.total;
    });
  };

  // 加载更多的数据
  const loadMore = async () => {
    if (displayedItems.value.length <= total.value) {
      currentPage.value += 1;
      businessListSQ({
        title: searchValue.value,
        page: currentPage.value,
        pageSize: pageSize.value,
        promotion_type: 2,
        promotion_related: currentTeamId.value,
      }).then((res) => {
        displayedItems.value.push(...res.data.data.list);
        total.value = res.data.data.total;
      });
    }
  };

  // 监听滚动事件
  const scrollContainer = ref(null);
  watch(scrollContainer, async (container) => {
    if (!container) return;
    container.addEventListener("scroll", handleScroll);
  });

  onMounted(() => {
    fetchData();
  });
  const closeWin = () => {
    const confirmDia = DialogPlugin.confirm({
      header: '确认关闭吗?',
      theme: 'info',
      body: `关闭后，当前评价内容不做保存`,
      cancelBtn: '取消',
      closeBtn: null,
      confirmBtn: t('activity.activity.confirm'),
      onConfirm: () => {
        visible.value = false;
        confirmDia.hide();
        searchValue.value = "";
        currentPage.value = 1;
        displayedItems.value = [];
      },
      onClose: () => {
        confirmDia.hide();
      },
    });

  };
  const emits = defineEmits(["changBusiness"]);

  const changeItem = (item) => {
    visible.value = false;
    console.log(item, "itemitemchangbusiness");
    emits("changBusiness", item);
  };
  // 处理滚动事件
  const handleScroll = () => {
    const container = scrollContainer.value;
    if (container.scrollHeight - container.scrollTop === container.clientHeight) {
      console.log("滚动到底部");
      loadMore();
    }
  };

  // 清理事件监听器
  onUnmounted(() => {
    const container = scrollContainer.value;
    if (container) {
      container.removeEventListener("scroll", handleScroll);
    }
  });

  // 监听搜索值变化，重置分页
  watch(searchValue, () => {
    currentPage.value = 1;
    displayedItems.value = [];
    fetchData();
  });
  const openWin = () => {
    searchValue.value = ''

    visible.value = true;
    fetchData();
  };
  defineExpose({
    openWin,
  });
  const seachEnter = () => { };
</script>
<style lang="less" scoped>
  .name-item {
    display: flex;
    align-items: start;
    gap: 12px;
  }

  .no-type-item-status {
    width: 8px;
    height: 8px;
    background: #62BF7C;
    border-radius: 50%;
    margin-right: 10px;
  }

  .type-item-status {
    border-radius: 50%;
    width: 8px;
    margin-right: 10px;
    height: 8px;
    background: #4D5EFF;
  }

  .type-item {
    display: flex;
    align-items: center;
  }

  .name-item-name {
    color: #1A2139;
    text-overflow: ellipsis;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;

  }

  .name-item-img {
    width: 64px;
    height: 64px;
    border-radius: 4px;
  }

  .ckdNumText {
    color: #1A2139 !important;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
  }

  .ckdNumNum {
    color: #4D5EFF !important;

    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
  }

  .shopSearch {
    display: flex;
    align-items: center;
    gap: 8px;

    .shopSearchTitle {
      color: #828DA5;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      width: 56px;
    }
  }

  :global(.adSelectShopWin .foot-shop-box) {
    background: #FFF;
    height: 80px;
    padding: 24px;
    border-radius: 0 0 16px 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;

  }

  :global(.adSelectShopWin .t-dialog--default) {
    padding: 0;
  }

  :global(.adSelectShopWin .t-dialog__header) {
    padding: 24px 24px 0 !important;

  }

  .adSelectShopWin {
    .box {
      padding-left: 12px;
    }

  }

  :global(.adSelectShopWin .t-dialog__body) {
    padding-bottom: 0;
  }

  :global(.adSelectShopWin .t-dialog) {
    background-image: url(@/assets/workbench/xzshop.png) !important;
    background-repeat: no-repeat !important;
    background-size: 100% 100% !important;
  }

  .head-box-flex {
    display: flex;
    align-items: start;
    gap: 12px;
    border-bottom: 1px solid #ECEFF5;
    padding-bottom: 8px;
    margin-bottom: 8px;
  }

  .evaluation-box {}

  .head-box-title {
    color: #1A2139;
    width: 712px;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    word-break: break-all;
    margin-bottom: 6px;
  }

  ::-webkit-scrollbar {
    width: 4px;
    background-color: transparent;
  }

  /*定义滚动条轨道 内阴影+圆角*/
  ::-webkit-scrollbar-track {
    background-color: transparent;
  }

  /*定义滑块 内阴影+圆角*/
  ::-webkit-scrollbar-thumb {
    border-radius: 4px;
    background: var(--divider-kyy_color_divider_deep, #D5DBE4);
  }

  /* 保持原有样式 */
  .loading-more {
    display: flex;
    justify-content: center;
    margin: 16px 0;
  }

  .no-results {
    text-align: center;
    color: #828da5;
    margin: 16px 0;
  }

  .none-box {
    display: none;
  }

  .close-img {
    position: absolute;
    top: 0;
    z-index: 99;
    right: 0px;
    cursor: pointer;
  }

  .a-item {
    width: 78px;
    height: 78px;
    position: relative;
  }

  .uploadRef-box {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .updend-img {
    width: 78px;
    height: 78px;
    border-radius: 8px;
  }

  .sjbox {
    display: flex;
    padding: 12px;
    align-items: center;
    border: 1px solid transparent;
    width: 100%;

    gap: 12px;
    align-self: stretch;
    height: 145px;
    border-radius: 8px;
    background: var(--bg-kyy_color_bg_deep, #f5f8fe);
  }

  .evaluation-box-flex-dis {
    color: #1A2139;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
  }

  .no-click {
    color: #ACB3C0;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    /* 157.143% */
  }

  .is-tip {
    color: #1A2139;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    /* 157.143% */
  }

  .is-span {
    color: #828DA5;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
  }

  .view-box {
    height: 78px;
    margin-top: 12px;
    display: flex;
    gap: 8px;
  }

  .head-box-img {
    width: 78px;
    height: 78px;
    border-radius: 8px;
  }

  .evaluation-box-flex {
    display: flex;
    gap: 8px;
    align-items: center;
    padding-bottom: 4px;
  }

  .head-box {
    display: flex;
    flex-direction: column;
    align-items: baseline;
    margin-top: 8px;
    margin-bottom: 12px;
    border-radius: 8px;
    margin-right: 12px;
    background: #FFF;
    padding: 12px;
  }

  .table-box-shop {
    padding: 12px;
    background: #fff;
    border-radius: 8px;
  }

  .flexbox {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    height: 356px;
    align-content: flex-start;
    overflow-y: overlay;
    padding-right: 12px;
    border-radius: 8px;
  }

  .sjbox:hover {
    border: 1px solid var(--brand-kyy_color_brand_default, #4d5eff);
  }

  .sjbox-info-box {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;

    .sjbox-info {
      display: flex;
      width: 100%;
      align-items: center;
      gap: 8px;
    }

    .xq {
      background: var(--kyy_color_tag_bg_warning, #ffe5d1);
      color: var(--kyy_color_tag_text_warning, #fc7c14);
    }

    .tags {
      display: flex;
      height: 20px;
      min-height: 20px;
      max-height: 20px;
      padding: 0px 4px;
      justify-content: center;
      align-items: center;
      gap: 4px;
      border-radius: var(--kyy_radius_tag_s, 4px);
      background: var(--kyy_color_tag_bg_cyan, #e6f9f8);
      color: var(--kyy_color_tag_text_cyan, #11bdb2);
      text-align: center;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      /* 166.667% */
    }

    .sjbox-title {
      overflow: hidden;
      color: var(--text-kyy_color_text_1, #1a2139);
      text-overflow: ellipsis;
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: 22px;
      /* 157.143% */
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      word-break: break-all;
      max-width: 550px;

    }

    .info-lable {
      overflow: hidden;
      color: var(--text-kyy_color_text_3, #828da5);
      text-overflow: ellipsis;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      /* 157.143% */
    }

    .sjbox-info-group {
      width: 24px;
      height: 24px;
      border-radius: 50%;
    }

    .info-value {
      overflow: hidden;
      color: var(--text-kyy_color_text_1, #1a2139);
      text-overflow: ellipsis;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      /* 157.143% */
    }
  }

  .sjbox-left {
    img {
      width: 121px;
      height: 121px;
      border-radius: 8px;
    }
  }

  .iconhover {
    color: #516082;
    cursor: pointer;
  }

  .upd-box {
    display: flex;
    width: 78px;
    height: 78px;
    padding: 14px;
    cursor: pointer;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    gap: 4px;
    border-radius: 8px;
    border: 1px solid var(--kyy_color_upload_border_default, #d5dbe4);
    background: var(--kyy_color_upload_bg, #fff);

    span {
      color: var(--kyy_color_upload_text_default, #516082);
      text-align: center;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      /* 166.667% */
    }
  }

  .tips {
    background: #EAECFF;
    display: flex;
    align-items: center;
    padding: 8px 24px;
    gap: 8px;
    border-radius: 8px;
    color: #1A2139;
    margin-bottom: 16px;
  }

  .input-box {
    margin-top: 8px;

  }

  ::v-deep(.input-box .t-textarea__limit) {
    position: absolute;
    bottom: 8px;
    right: 12px;
  }
</style>
