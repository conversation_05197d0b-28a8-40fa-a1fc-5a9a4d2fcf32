// lss 加个滚动条样式
// @import "@renderer/views/engineer/less/common.less";

/*定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
  // height: 2px;
  // background-color: #f5f5f5;
}
/*定义滚动条轨道 内阴影+圆角*/
::-webkit-scrollbar-track {
  // background-color: #e3e6eb;
  // background-color: #fff;
}
:deep(.t-badge--circle){

  padding-right: calc((16px - 8px) / 2);
  padding-left: calc((16px - 8px) / 2);
  min-width: 8px;
  height: 16px;
  background-color: var(--td-error-color);
  line-height: 16px;
}
/*定义滑块 内阴影+圆角*/
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
  background-color: #1A21395C;
}


.member_produce {
  img {
    border-radius: 8px;
  }
}

:deep(.t-avatar-fonts2) {
  font-size: 14px !important;
}
.home {

  width: 100%;


  .bodyContent {
    // background: #fff;
    // height: 100%;
    // flex: 1;
    // display: flex;
    // flex-direction: column;
    .bodyc {
      // flex: 1;
      // height: calc(100% - 57px);
      // display: flex;
      // flex-direction: column;

      display: flex;
      flex-direction: column;
      align-items: center;
    }
  }
}

.scroll {
  overflow-y: overlay;
  height: calc(100vh - 104px);
  width: 100%;
  display: flex;
  justify-content: center;
  margin: 0 2px;
  width:  fill-available;
  width: -webkit-fill-available;

  // background-color: red;
  .area {
    max-width: 1168px;
    min-width: 1088px;
    width: 100%;
    // margin: 0 16px;
  }
}
.combine {
  display: flex;
  gap: 12px;
  .team-manage {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    flex: none;
    width: 136px;
    border-radius: 8px;
    background: linear-gradient(315deg, #A1D7FE -62.86%, #EAF5FF 75.49%);
    .text {
      color: var(--text-kyy-color-text-1, #1A2139);
      text-align: center;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
    }
    .svg {
      width: 40px;
      height: 40px;
    }
  }
}
.team-head {
  flex: 1;
  display: flex;
  width: 100%;
  padding: 24px;
  background: #fff;
  align-items: center;
  background-image: url('@renderer/assets/member/icon/vip_img_bg.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  object-fit: cover;
  height: 136px;

  .logo-box {
    width: 88px;
    height: 88px;
    margin-right: 16px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .info {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    gap: 8px;
    flex: 1 0 0;
    // width: 510px;
    .name {
      color: var(--text-kyy-color-text-1, #1a2139);
      display: flex;
      align-items: center;
      font-family: PingFang SC;
      font-size: 18px;
      font-style: normal;
      font-weight: 600;
      line-height: 26px;
      cursor: pointer;
      .iconarrowdown {
        color: #828da5;
        font-size: 18px;
        margin-left: 2px;
      }
    }

    .dp {
      color: var(--text-kyy-color-text-3, #828da5);

      /* kyy_fontSize_2/regular */
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
      display: flex;
      .tg {
        display: flex;
        height: 20px;
        min-height: 20px;
        max-height: 20px;
        padding: 0px 4px;
        justify-content: center;
        align-items: center;
        gap: 4px;
        border-radius: var(--kyy_radius_tag_s, 4px);
        background: var(--kyy_color_tag_bg_gray, #eceff5);
        // color: var(--kyy_color_tag_text_gray, #516082);
        color: var(--kyy_color_tag_text_brand, #4D5EFF);
        text-align: center;
        font-family: PingFang SC;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px; /* 166.667% */
      }
    }
    .tag1 {
      display: flex;
      height: 24px;
      min-height: 24px;
      max-height: 24px;
      padding: 0px 8px;
      justify-content: center;
      align-items: center;
      gap: 4px;
      color: var(--kyy_color_tag_text_purple, #CA48EB);
      text-align: center;
      border-radius: var(--kyy_radius_tag_s, 4px);
      background: var(--kyy_color_tag_bg_purple, #FAEDFD);
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: 22px; /* 157.143% */
    }
    .tag2 {
      display: flex;
      height: 24px;
      min-height: 24px;
      max-height: 24px;
      padding: 0px 8px;
      justify-content: center;
      align-items: center;
      gap: 4px;
      color: var(--kyy_color_tag_text_purple, #ca48eb);
      text-align: center;
      border-radius: var(--kyy_radius_tag_s, 4px);
      background: var(--kyy_color_tag_bg_purple, #faedfd);
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: 22px; /* 157.143% */
    }
  }
  .btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    gap: 8px;
    // padding-left: 24px;
    padding: 0 10px;

    position: relative;
    transition: all 0.15s linear ;

    &:hover {
      border-radius: 8px;
      background: var(--bg-kyy_color_bgBrand_hover, #EAECFF);
      transition: all 0.15s linear ;
    }
    &::before {
      content: " ";
      position: absolute;
      width: 1px;
      top: 0;
      bottom: 0;
      height: 88px;
      margin: auto;
      background: #fff;
      left: -24px;
    }

    .iconpeopleadd {
      font-size: 24px;
      color: #516082;
    }
    .text {
      color: var(--text-kyy-color-text-1, #1A2139);
      text-align: center;

      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
    }
    .svg {
      width: 32px;
      height: 32px;
    }
  }
  .btn_2 {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: all 0.15s linear ;

    width: 80px;
    height: 80px;
    gap: 3px;
    // padding-left: 24px;
    padding: 0 10px;

    position: relative;
    &:hover {
      border-radius: 8px;
      background: var(--bg-kyy_color_bgBrand_hover, #EAECFF);
      transition: all 0.15s linear ;
    }
    .svg {
      width: 32px;
      height: 32px;
    }


    .iconpeopleadd {
      font-size: 24px;
      color: #516082;
    }
    .text {
      color: var(--text-kyy-color-text-1, #1A2139);
      text-align: center;

      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
    }


  }
}

.entry {
  height: 136px;
}

.list-popup {
  .mer-list {
    display: flex;
    width: 267px;
    background: #fff;
    flex-direction: column;
    // align-items: center;
    .m-item {
      display: flex;
      width: 251px;
      height: 32px;
      padding: 8px 12px;
      align-items: center;
      gap: 12px;
      cursor: pointer;
      .se {
        margin-top: 5px;
      }
      .av {
        // margin: 0 12px;
      }
      .name {
        color: var(--lingke-black-90, #1a2139);

        font-family: PingFang SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
        max-width: 154px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}



.tabsTop {
  display: flex;
  height: 56px;
  align-items: center;
  gap: 32px;
  padding:  16px;


  &-item {
    color: var(--text-kyy_color_text_1, #1A2139);
    text-align: center;

    /* kyy_fontSize_3/regular */
    font-family: "PingFang SC";
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px; /* 150% */

  }

}
.banner {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid var(--divider-kyy_color_divider_light, #ECEFF5);

  .group {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 0 16px;
    &-item {
      height: 32px;
      display: flex;
      align-items: center;
      gap: 4px;
      border: 1px solid var(--color-button_border-kyy_color_buttonBorder_border_dedault, #D5DBE4);
      background: var(--color-button_border-kyy_color_buttonBorder_bg_default, #FFF);
      padding: 0 16px ;
      border-radius: var(--radius-kyy_radius_button_s, 4px);
      border: 1px solid var(--color-button_border-kyy_color_buttonBorder_border_dedault, #D5DBE4);
      transition: all 0.25s linear ;
      &:hover {
        border-radius: var(--radius-kyy_radius_button_s, 4px);
        border: 1px solid var(--border-kyy_color_border_hover, #707EFF);
        background: var(--bg-kyy_color_bgBrand_hover, #EAECFF);
        transition: all 0.25s linear ;
        .text {
          color: var(--brand-kyy_color_brand_hover, #707EFF);
        }
      }
      .svg {
        width: 20px;
        height: 20px;
      }
      .text {
        width: 56px;
        color: var(--color-button_border-kyy_color_buttonBorder_text_default, #516082);
        text-align: center;
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 22px; /* 157.143% */
      }
      .t-badge {
        display: flex;
        align-items: center;
        gap: 4px;
      }
    }
  }
}


.act-1 {
  position: absolute;
  bottom: 0px;
  left: 46px;
}
.act-2 {
  position: absolute;
  bottom: 0px;
  left: 139px;
}

.comp-box {
  width: 100%;
  background: #fff;
  margin-bottom: 24px;
  .regular {
    padding: 0 24px;
  }
  .edit-btn {
    position: fixed;
    left: 0;
    right: 0;

    bottom: 0px;
    z-index: 9;
    background-color: #fff;

    display: flex;
    height: 64px;
    padding: 16px 24px;
    justify-content: left;
    align-items: center;
    align-self: stretch;
    border-top: 1px solid var(--divider-kyy-color-divider-light, #eceff5);

    .cen {
      width: 1110px;
      margin: 0 auto;
      .b {
        // margin-left: 40%;
      }
    }

  }
  .btm24 {
    height: 24px;
    width: 100%;
    background: var(--kyy_color_tabbar_bg, #ebf1fc);
    color: #ebf1fc;
  }
}

.contact-box {
  width: 100%;
  background: #fff;
}

:deep(.t-badge--circle) {
  background-color: var(--kyy_color_badge_bg, #FF4AA1);
}



.backTop {
  position: fixed;
  right: -8px;
  bottom: 40px;
  opacity: 0;
  transition:all 0.25s linear;
  margin-right: calc((100% - 1168px) / 2);

  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  // border: 1px solid var(--border-kyy_color_border_default, #D5DBE4);
  background: var(--bg-kyy_color_bg_mask, rgba(0, 0, 0, 0.50));
  .iconarrowup {
    font-size: 14px;
    color: #1A2139;
  }
  .text {
    color: var(--text-kyy_color_text_1, #1A2139);
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px; /* 166.667% */
  }
}

.isOpacity {
  opacity: 1 !important;
  transition:all 0.25s linear;
}



.backRight {
  position: fixed;
  right: 0;
  bottom: 84px;
  z-index:101;
  .iconarrowup {
    width: 24px;
  }
}


.footerV {
  bottom: 84px !important;
}
.footer {
  position: fixed;
  right: 0;
  left: 0;
  z-index:100;
  margin: auto;
  bottom: 32px;
  max-width: 1320px;
  display: flex;
  justify-content: flex-end;
}
.tricks {
  position: fixed;
  right: -20px;
  // left: 0;
  bottom: 84px;
  margin-right: calc((100% - 1168px) / 2);
  z-index:100;
  transition:all 0.25s linear;
}

.backTopContact {
  position: fixed;
  right: -20px;
  // left: 0;
  bottom: 144px;

  z-index:100;

  opacity: 0;
  transition:all 0.25s linear;
  margin-right: calc((100% - 1168px) / 2);

  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  //width: 48px;
  //height: 48px;
  border-radius: 50%;
  // background: var(--icon-kyy_color_icon_transparent, rgba(26, 33, 57, 0.36));
  .iconarrowup {
    // font-size: 20px;
    // color: #1A2139;
    // width: 32px;
    width: 44px;
    // height: 24px;
  }
  .text {
    color: var(--text-kyy_color_text_1, #1A2139);
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px; /* 166.667% */
  }
  .shadowBox {
    position: absolute;
    width: 44px;
    height: 20px;
    border-radius: 8px;
    opacity: 1;
    bottom: 0;
    // background-color: red;

  }
}

@media screen and (min-width: 1270px) {
  .backTopContact {
    margin-right: calc((100% - 1270px) / 2);
  }
  .backTop {
    margin-right: calc((100% - 1270px) / 2);
  }
  .tricks {
    margin-right: calc((100% - 1270px) / 2);
  }
}


.tabs-item {
  position: relative;
}
.redpoint {
  position: absolute;
  top: 0px;
  right: 8px;
}
.sticky {
  // position: sticky;
  // top: 0;
  // z-index: 10;
  // background: var(--bg-kyy_color_bg_deep, #f5f8fe);
  padding-bottom: 16px;
  padding-top: 16px;
  display: flex;
  justify-content: center;
  width: 100%;
}

.operate {

}

.tabs {
  display: flex;
  // position: relative;
  // border-bottom: 1px solid var(--divider-kyy-color-divider-light, #eceff5);
  // height: 56px;
  width: 100%;
  align-items: center;
  justify-content: space-between;
  // padding: 0px 24px;

  // align-self: stretch;
  // margin-top: 16px;
  max-width: 1168px;
  min-width: 1088px;
  padding: 0 16px;

  .list {
    display: flex;
    gap: 12px;
    align-items: center;
  }
  .bot {
    display: flex;
    align-items: center;
    &-item {
      display: flex;
      // gap: 8px;
      align-items: center;
      .badge {
        display: flex;
        align-items: center;
        gap: 8px;
      }
      .svg {
        width: 28px;
        height: 28px;
      }
      .text {
        color: var(--text-kyy_color_text_1, #1A2139);
        text-align: center;

        /* kyy_fontSize_2/regular */
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
      }
    }
  }

  &-item {
    // margin-right: 44px;
    color: var(--text-kyy_color_text_1, #1A2139);

    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px; /* 150% */

    display: flex;
    align-items: center;
    gap: 8px;

    border-radius: 16px;
    // background: #fff;
    padding: 4px 12px;
    transition: all 0.25s linear;

    &:hover {
      // border-radius: 16px;
      background: var(--bg-kyy_color_bgBrand_hover, #EAECFF);

      /* kyy_shadow_m */
      // box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.12);
      transition: all 0.25s linear;

      color: var(--icon-kyy_color_icon_hover, #707EFF);

      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      line-height: 24px; /* 150% */
    }


    .icon {
      width: 20px;
      height: 20px;
    }
    .text {
      // min-width: 48px;
      // text-align: left;
      // color: var(--text-kyy_color_text_1, #1a2139);

      // /* kyy_fontSize_3/regular */
      // font-family: "PingFang SC";
      // font-size: 16px;
      // font-style: normal;
      // font-weight: 400;
      // line-height: 24px; /* 150% */
    }

    cursor: pointer;
    :v-deep(.t-badge--circle) {
      right: -3px;
    }
  }
  .active {
    color: var(--text-kyy_color_text_white, #FFF);
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px; /* 150% */
    border-radius: 16px;
    background: #1D63E9 !important;
  }
}

.info {
  border-radius: 12px;
  background: #FFF;
  padding: 16px;
  margin-bottom: 16px;
  display: flex;
  gap: 12px;
  height: 340px;
  .swiper_one {
    width: 608px;
    height: 308px;
    border-radius: 8px;
    background: var(--bg-kyy_color_bg_list_hover, #F3F6FA);
    display: flex;
    justify-content: center;
    align-items: center;
    .default {
      width: 106.199px;
      height: 128px;
    }
  }
  .swiper_two {
    display: flex;
    flex-direction: column;
    width: 224px;
    gap: 12px;
    img {
        width: 73.012px;
        height: 88px;
    }
    .top {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      background: var(--bg-kyy_color_bg_list_hover, #F3F6FA);
      border-radius: 8px;
    }
    .bottom {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      background: var(--bg-kyy_color_bg_list_hover, #F3F6FA);
      border-radius: 8px;
    }
  }
  .member {
    flex: 1;
    // border-radius: 8px;
    width: 280px;
    .defaultMember {
      width: 280px;
      border-radius: 8px;
    }
    &_info {
      border-radius: 8px;
      // background: #EFF3FF;
      background-image: url('@renderer/assets/member/svg/home_card_img_bg.svg');
      background-size: cover;
      &_title {
        padding: 12px;
        display: flex;
        justify-content: space-between;
        &_text {
          color: var(--text-kyy_color_text_1, #1A2139);
          /* kyy_fontSize_2/bold */
          font-family: "PingFang SC";
          font-size: 14px;
          font-style: normal;
          font-weight: 600;
          line-height: 22px; /* 157.143% */
        }
        &_btn {
          border-radius: 12px;
          border: 1px solid var(--color-button_border-kyy_color_buttonBorder_border_dedault, #D5DBE4);
          background: var(--color-button_border-kyy_color_buttonBorder_bg_default, #FFF);
          color: var(--color-button_border-kyy_color_buttonBorder_text_default, #516082);
          text-align: center;
          padding: 0 12px;

          /* kyy_fontSize_2/regular */
          font-family: "PingFang SC";
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px; /* 157.143% */
          transition: all 0.25s linear;

          &:hover {
            border: 1px solid var(--color-button_secondaryBrand-kyy_color_button_secondaryBrand_border_hover, #707EFF);
            background: var(--color-button_secondaryBrand-kyy_color_button_secondrayBorder_bg_hover, #DBDFFF);
            color: var(--color-button_border-kyy_color_buttonBorder_text_hover, #707EFF);
            transition: all 0.25s linear;
          }
        }
      }


      &_content {
        display: flex;
        flex-direction: column;
        padding: 12px;
        padding-bottom: 0;
        border-radius: 8px;
        background: rgba(255, 255, 255, 0.80);
        position: relative;
        .icon {
          position: absolute;
          right: 0;
          top: 0;
          border-radius: 0px 8px;
          border: 1px solid #FFF;
          background: var(--icon-kyy_color_icon_light, #EBF1FC);

          width: 28px;
          height: 28px;
          padding: 4px;
          transition: all 0.25s linear;

          display: flex;
          align-items: center;
          justify-content: center;
          .iconstanding {
            color:#828DA5;
            transition: all 0.25s linear;
            font-size: 20px;

          }
          &:hover {
            background: var(--bg-kyy_color_bgBrand_hover, #EAECFF);
            transition: all 0.25s linear;

            .iconstanding {
              color: #707EFF;
              transition: all 0.25s linear;
            }
          }
        }
        .person {
          display: flex;
          gap: 16px;
          align-items: flex-start;
          .logo {
            width: 76px;
            height: 106px;
            border-radius: 8px;
          }
          .infos {
            width: calc(100% - 76px - 16px);
            display: flex;
            flex-direction: column;
            background: none;

            .name {
              color: var(--text-kyy_color_text_1, #1A2139);

              /* kyy_fontSize_3/bold */
              font-family: "PingFang SC";
              font-size: 16px;
              font-style: normal;
              font-weight: 600;
              line-height: 24px; /* 150% */
              padding-right: 24px !important;
            }
            .level {
              color: var(--text-kyy_color_text_2, #516082);
              font-size: 14px;
              font-style: normal;
              font-weight: 400;
              line-height: 22px; /* 157.143% */
            }
            .tag {
              display: flex;
              gap: 4px;
              &-unit {
                padding: 0 4px;
                border-radius: var(--kyy_radius_tag_s, 4px);
                background: var(--kyy_color_tag_bg_purple, #FAEDFD);
                color: var(--kyy_color_tag_text_purple, #CA48EB);
                font-size: 12px;
                font-style: normal;
                font-weight: 400;
                line-height: 20px; /* 166.667% */
              }
              &-connect {
                flex: none;
                padding: 0 4px;
                color: var(--kyy_color_tag_text_success, #499D60);
                font-size: 12px;
                font-style: normal;
                font-weight: 400;
                line-height: 20px; /* 166.667% */
                border-radius: var(--kyy_radius_tag_s, 4px);
                background: var(--kyy_color_tag_bg_success, #E0F2E5);
              }
              &-unconnect {
                padding: 0 4px;
                color: var(--kyy_color_tag_text_gray, #516082);
                font-size: 12px;
                font-style: normal;
                font-weight: 400;
                line-height: 20px; /* 166.667% */
                border-radius: var(--kyy_radius_tag_s, 4px);
                background: var(--kyy_color_tag_bg_gray, #ECEFF5);
              }
            }
          }
        }
        .entry {
          flex: 1;
          display: flex;
          justify-content: space-between;
          padding: 12px;
          padding-bottom: 8px;
          &-item {
            width: 64px;
            padding: 4px;
            display: flex;
            flex-direction: column;
            gap: 4px;
            align-items: center;
            border-radius: 8px;
            transition: all 0.25s linear;


            &:hover {
              transition: all 0.25s linear;
              background: var(--bg-kyy_color_bgBrand_hover, #EAECFF);
            }

            .logo {
              width: 32px;
              height: 32px;
            }
            .text {
              color: var(--text-kyy_color_text_2, #516082);
              font-size: 14px;
              font-style: normal;
              font-weight: 400;
              line-height: 22px; /* 157.143% */
            }
          }
        }
      }

    }
    &_produce {
      img  {

        height: 48px;
      }
    }
  }

}

.waterfall {
  width: 100%;
  padding-bottom: 16px;
  // max-height: calc(100vh - 177px);
  // overflow-y: overlay;
  // overflow-x: hidden;
  &-box {
    break-inside: avoid;
    // width: calc((100% - 16px)/2)
    background: #fff;
    margin-bottom: 10px;
    border-radius: 8px;
    background: var(--bg-kyy_color_bg_light, #fff);
    // padding: 16px;
    .topSquare {
      border-radius: 8px;
      background: linear-gradient(104deg, #E5F9EB 9.81%, rgba(229, 249, 235, 0.30) 29.31%);
    }
    .topNotice {
      border-radius: 8px;
      background: linear-gradient(111deg, #D3D8FF 0.63%, rgba(211, 216, 255, 0.40) 28.93%);
    }
    .ex-apps {
      border-radius: 8px;
background: linear-gradient(111deg, #D1F6F4 0.63%, rgba(209, 246, 244, 0.40) 28.93%);
    }
    .topDynamics {
      border-radius: 8px;
      background: linear-gradient(104deg, #FEE9D8 9.81%, rgba(255, 234, 216, 0.30) 29.31%);
    }

    .topEbook {
      border-radius: 8px;
      background: linear-gradient(104deg, #D0F1FF 9.81%, #E5F6FF 29.31%);
    }

    .topRich {
      border-radius: 8px;
      background: linear-gradient(104deg, #D0F1FF 9.81%, #E5F6FF 29.31%);
    }

    .top {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px;
      height: 48px;
      .title {
        position: relative;

        color: var(--text-kyy_color_text_1, #1A2139);

        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 22px; /* 157.143% */


        display: flex;
        align-items: center;
        gap: 8px;
        img {
          width: 20px;
          height: 20px;
        }
        // &::before {
        //   position: absolute;
        //   left: 0;
        //   content: " ";
        //   border-radius: 8px;
        //   background: var(--brand-kyy_color_brand_default, #4d5eff);
        //   width: 3px;
        //   height: 16px;
        //   top: 0;
        //   bottom: 0;
        //   margin: auto;
        // }
      }
      .more {
        display: flex;
        align-items: center;
        color: var(--color-button_border-kyy_color_buttonBorder_text_default, #516082);
        text-align: center;

        /* kyy_fontSize_1/regular */
        font-family: "PingFang SC";
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px; /* 166.667% */
        border-radius: 12px;
        height: 24px;
        padding-left: 8px;
        padding-right: 4px;
        transition: all 0.25s linear;
        .icon {
          font-size: 20px;
          color: #828DA5;
          transition: all 0.25s linear;
        }

        &:hover {
          color: var(--brand-kyy_color_brand_hover, #707EFF);
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          line-height: 20px; /* 166.667% */
          background: var(--bg-kyy_color_bgBrand_hover, #EAECFF);
          transition: all 0.25s linear;
          .icon {
            color: #707EFF;
            transition: all 0.25s linear;
          }
        }
      }
    }
  }
  .entry {
    width: inherit;
  }
}

.conbox {
  display: flex;
  gap: 8px;
  padding: 16px;
  // padding-bottom: 0;
  flex-wrap: wrap;
  min-height: 200px;


  width: 100%;
  // flex-direction: column;

  .item {
    // flex: 1;
    width: calc((100% - 8px) / 2);
    // height: 208px;
    height: 96px;
    border-radius: 8px;
    // background: var(--bg-kyy_color_bg_deep, #F5F8FE);
    background: url('@renderer/assets/member/svg/card_square_bg.svg');
    position: relative;
    transition: all 0.15s linear;

    .visible {
      position: absolute;
      left: 0;
      top: 0;
      right: 0;
      bottom: 0;
      z-index: 10;
      border-radius: 8px;
      background: var(--bg-kyy_color_bg_mask, rgba(0, 0, 0, 0.50));
      display: flex;
      align-items: center;
      justify-content: center;

      .btn {
        display: flex;
        gap: 4px;
        width: 110px;

        .iconpreciewopen {
          font-size: 20px;
        }

        .text {
          color: var(--text-kyy_color_text_white, #FFF);
          text-align: center;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px;
          /* 157.143% */
        }
      }

      .tip {
        border-radius: 8px 0px;
        background: var(--icon-kyy_color_icon_transparent, rgba(26, 33, 57, 0.36));

        position: absolute;
        left: 0;
        top: 0;
        padding: 0 4px;
        color: var(--text-kyy_color_text_white, #FFF);
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
        /* 166.667% */
      }
    }

    &:hover {

      /*.hover {
        opacity: 1;
      }*/
      border-radius: 8px;
      box-shadow: inset 0 0 0 1px var(--border-kyy_color_border_hover, #4D5EFF);

      transition: all 0.15s linear;
    }

    .public {
      display: flex;
      // flex-direction: column;
      // justify-content: space-between;
      padding: 12px;
      align-items: flex-start;
      height: 100%;
      gap: 12px;


      .logo {
        width: 54px;
        // height: 48px;
        position: relative;
        display: flex;
        justify-content: center;
        flex-direction: column;
        align-items: center;
        gap: 4px;

        .img {
          width: 48px;
          height: 48px;
          border-radius: 48px;

        }
        .like {
          :deep(.t-button--theme-default) {
            border: 0 !important;
          }
          :deep(.t-button--variant-outline) {
            border: 0;
            height: 20px !important;
            padding: 0 4px;
            padding-right: 8px;
            &:hover {
              border: 0px !important;
            }
          }
          :deep(.t-button__text) {
            font-size: 12px;
          }
          :deep(.icon) {
            font-size: 16px;
          }
        }

      }

      .right {

        flex: 1;
        display: flex;
        flex-direction: column;
        // justify-content: space-between;
        // height: 100%;
        gap: 4px;
        width: calc(100% - 66px);

        .lin {
          display: flex;
          align-items: center;
          // max-width: 160px;

          .star {
            // position: absolute;
            // bottom: -2px;
            // right: -3px;
            display: flex;
            align-items: center;

            &-icon {
              font-size: 24px;
            }
          }

          .name {

            color: var(--text-kyy_color_text_1, #1A2139);
            text-align: center;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;
            /* 157.143% */

          }
        }

        .type {
          border-radius: var(--kyy_radius_tag_s, 4px);
          background: var(--kyy_color_tag_bg_kyyBlue, #E4F5FE);
          padding: 0 4px;
          color: var(--kyy_color_tag_text_kyyBlue, #21ACFA);
          text-align: center;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          line-height: 20px;
          /* 166.667% */
          width: fit-content;
        }

        .person {
          background: var(--kyy_color_tag_bg_success, #E0F2E5);
          color: var(--kyy_color_tag_text_success, #499D60);
        }

        .bottom {
          color: var(--text-kyy_color_text_3, #828DA5);
          // text-align: center;
          // max-width: 160px;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px;
          /* 157.143% */
        }
      }

    }
  }

}

.lists {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  width: 100%;
  &-item {
    padding: 12px;
    border-radius: 8px;
    background: var(--bg-kyy_color_bg_deep, #f5f8fe);
    width: calc((100% - 12px) / 2);

    display: flex;
    gap: 12px;
    transition: all 0.15s linear;
    &:hover {
      border-radius: 8px;
      background: var(--bg-kyy_color_bg_light, #fff);
      /* kyy_shadow_m */
      box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.12);
      transition: all 0.15s linear;
    }
    .left {
      width: 72px;
      height: 72px;
      position: relative;
      .lo {
        width: 72px;
        height: 72px;
        border-radius: 3.6px;
      }
      .ri {
        height: 20px;
        position: absolute;
        top: 0;
        left: 0;
      }
    }
    .right {
      display: flex;
      flex-direction: column;
      justify-content: center;
      gap: 8px;
      .top {
        .name {
          color: var(--text-kyy_color_text_1, #1a2139);
          /* kyy_fontSize_2/bold */
          font-family: "PingFang SC";
          font-size: 14px;
          font-style: normal;
          font-weight: 600;
          line-height: 22px; /* 157.143% */
          .tip {
            color: #11bdb2;
            text-align: center;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px; /* 166.667% */
            padding: 0 4px;
            border-radius: var(--kyy_radius_tag_s, 4px);
            background: var(--kyy_color_tag_bg_cyan, #e6f9f8);
          }
          .yellow {
            color: var(--kyy_color_tag_text_warning, #fc7c14);
            background: var(--kyy_color_tag_bg_warning, #ffe5d1);
          }
          .blue {
            color: #4d5eff;
            background: #eaecff;
            margin-right: 4px;
          }
        }
      }
      .bottom {
        display: flex;
        align-items: center;

        .company {
          flex: 1 1 auto;
          display: flex;
          align-items: center;
          gap: 4px;
          height: 20px;

          .av {
            flex: none;
          }
          .text {
            flex: 1;
            max-width: 140px;
            color: var(--text-kyy_color_text_3, #828da5);
            /* kyy_fontSize_1/regular */
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px; /* 166.667% */
          }
        }
        .area {
          flex: none;
          //  width: 72px;
          display: flex;
          align-items: center;
          .icon {
            color: #21acfa;
            font-size: 20px;
          }
          .text {
            width: 60px;
          }
        }
      }
    }
  }
}

// 会刊
.listsEbook {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  width: 100%;
  padding: 16px 8px;
  min-height: 200px;
  &-item {
    padding: 12px;
    background: var(--bg-kyy_color_bg_deep, #F5F8FE);
    border-radius: 8px;
    // background: var(--bg-kyy_color_bg_deep, #F5F8FE);
    // width: 182px;
    width: calc((100% - 24px) / 3);
    height: 262px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    transition: all 0.15s linear;

    .cover{
      display: flex;
      justify-content: center;
      padding: 0 5px;
      img {
        width: 156px;
        height: 208px;
        object-fit: cover;
      }
    }
    .name {
      padding: 0 5px;
      overflow: hidden;
      color: var(--text-kyy_color_text_1, #1a2139);
      text-overflow: ellipsis;
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: 22px; /* 157.143% */
      display: -webkit-box;
      width: 156px;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;
    }

    &:hover {
      border-radius: 8px;

      background: var(--bg-kyy_color_bg_light, #FFF);
      /* kyy_shadow_m */

      box-shadow: inset 0 0 0 1px var(--brand-kyy_color_brand_default, #4D5EFF);
      transition: all 0.15s linear;
    }
    .left {
      width: 72px;
      height: 72px;
      position: relative;
      .lo {
        width: 72px;
        height: 72px;
        border-radius: 3.6px;
      }
      .ri {
        height: 20px;
        position: absolute;
        top: 0;
        left: 0;
      }
    }
    .right {
      display: flex;
      flex-direction: column;
      justify-content: center;
      gap: 8px;
      .top {
        .name {
          color: var(--text-kyy_color_text_1, #1a2139);
          /* kyy_fontSize_2/bold */
          font-family: "PingFang SC";
          font-size: 14px;
          font-style: normal;
          font-weight: 600;
          line-height: 22px; /* 157.143% */
          .tip {
            color: var(--kyy_color_tag_text_brand, #4d5eff);
            text-align: center;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px; /* 166.667% */
            padding: 0 4px;
            border-radius: var(--kyy_radius_tag_s, 4px);
            background: var(--kyy_color_tag_bg_brand, #eaecff);
          }
          .yellow {
            color: var(--kyy_color_tag_text_warning, #fc7c14);
            background: var(--kyy_color_tag_bg_warning, #ffe5d1);
          }
          .blue {
            color: #4d5eff;
            background: #eaecff;
            margin-right: 4px;
          }
        }
      }
      .bottom {
        display: flex;
        align-items: center;

        .company {
          flex: 1 1 auto;
          display: flex;
          align-items: center;
          gap: 4px;
          height: 20px;

          .av {
            flex: none;
          }
          .text {
            flex: 1;
            max-width: 140px;
            color: var(--text-kyy_color_text_3, #828da5);
            /* kyy_fontSize_1/regular */
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px; /* 166.667% */
          }
        }
        .area {
          flex: none;
          //  width: 72px;
          display: flex;
          align-items: center;
          .icon {
            color: #21acfa;
            font-size: 20px;
          }
          .text {
            width: 60px;
          }
        }
      }
    }
  }
}

.default {
  width: 100%;
  height: 172.949px;
  img {
    width:inherit;
    // height: 172.949px;
    border-radius: 8px;
  }
}

.dynamic {
  // flex: 1;
  display: flex;
  // align-items: center;
  padding: 8px;
  gap: 12px;
  position: relative;
  transition: all 0.25s linear;
  &:hover {
    border-radius: 8px;
    background: var(--bg-kyy_color_bg_list_hover, #F3F6FA);
    transition: all 0.25s linear;
  }

  &:after {
    content: "";
    height: 1px;
    width: 100%;
    background: #eceff5;
    position: absolute;
    bottom: -5px;
    left: 0;
  }
  &:last-child::after {
    display: none;
  }
  .logo {
    position: relative;
    width: 72px;
    height: 72px;
    img {
      width: 72px;
      height: 72px;
      border-radius: 8px;
      object-fit: cover;
    }
    .timage {
      width: 72px;
      height: 72px;
      border-radius: 8px;
    }
    .vplay {
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      top: 0;
      width: 36px;
      height: 36px;
      margin: auto;

    }
    .iconimg {
      position: absolute;
      top: 0;
      right: 0;
      border-radius: 0px 8px;
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: var(--icon-kyy_color_icon_black, rgba(0, 0, 0, 0.08));
      .icon {
        font-size: 20px;
        color: #fff;
      }
    }
    .iconcount {
      border-radius: 4px 0px;
      background: rgba(21, 21, 21, 0.30);
      position: absolute;
      bottom: 0;
      right: 0;
      width: 20px;
      height: 20px;
      text-align: center;
      color: #FFF;

      text-align: center;
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: normal;
    }
  }
  .infok {
    display: flex;
    flex-direction: column;
    flex: 1;
    width: calc(100% - 88px);
    justify-content: space-between;
    .title {
      color: var(--text-kyy_color_text_3, #828DA5);
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
    }

    .team {
      display: flex;
      // justify-content: space-between;
      gap: 12px;
      color: var(--text-kyy_color_text_5, #ACB3C0);
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px; /* 166.667% */
      align-items: center;

      &-left {

      }
      .line {
        width: 1px;
        height: 12px;
        background: var(--divider-kyy_color_divider_light, #ECEFF5);
      }
      &-right {

      }
    }

    .content {
      color: var(--text-kyy_color_text_1, #1A2139);

      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
      // display: flex;
      // position: relative;

      .top {
        // border-radius: var(--kyy_radius_tag_s, 4px);
        // background: var(--kyy_color_tag_bg_brand, #EAECFF);
        // color: var(--kyy_color_tag_text_brand, #4D5EFF);
        // text-align: center;
        // font-size: 12px;
        // font-style: normal;
        // font-weight: 400;
        // line-height: 20px; /* 166.667% */
        height: 22px;
        // padding: 0 4px;
        margin-right: 4px;
        padding: 0;
        display: inline-flex;
        align-items: center;
        // line-height: 24px;
        float: left;
        img {
          // line-height: 24px;
        }
      }

      .content:after {
        content: "";
        display: table;
        clear: both;
      }

    }

  }
}


.dyBox {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  padding: 8px;
  gap: 9px;
}
