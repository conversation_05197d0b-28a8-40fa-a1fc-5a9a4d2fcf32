<template>
  <div class="star-rating">
    <div v-if="readonly" class="start-text">{{starsText}}</div>
    <div v-else class="stars-arr-box">
      <div class="stars-arr" v-for="(item) in ['非常差','差','一般','好','非常好']">
        {{item}}
      </div>
    </div>
    <div v-for="(star, index) in stars" :key="index" :class="{'star-item': true, 'selected-readonly':!readonly}" class="star-item">
      <iconpark-icon v-if="readonly" style="font-size: 22px;" :name="star.selected ? 'statedefault' : 'statedisabled'"
        :class="star.selected ? 'statedefault' : 'statedisabled'" />
      <iconpark-icon v-else style="font-size: 22px;" @click="handleClick(index)"
        :name="star.selected ? 'statedefault' : 'statedisabled'" :class="{  'updstar':true,'cursor-pointer': !readonly ,
        'statedisabled': !star.selected
      }" />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, watch } from "vue";

  const props = defineProps({
    modelValue: {
      type: Number,
      default: 0,
    },
    maxStars: {
      type: Number,
      default: 5,
    },
    readonly: {
      type: Boolean,
      default: false,
    },
  });

  const emit = defineEmits(["update:modelValue"]);
  const starsText = computed(() => {
    if (props.modelValue === 0) {
      return "未评价（0星）";
    }
    if (props.modelValue === 1) {
      return "非常差（1星）";
    }
    if (props.modelValue === 2) {
      return "差（2星）";
    }
    if (props.modelValue === 3) {
      return "一般（3星）";
    }
    if (props.modelValue === 4) {
      return "好（4星）";
    }
    if (props.modelValue === 5) {
      return "非常好（5星）";
    }
  });
  // 星星状态计算
  const stars = computed(() => {
    return Array.from({ length: props.maxStars }).map((_, index) => ({
      selected: index < props.modelValue,
    }));
  });

  // 点击事件处理
  const handleClick = (index: number) => {
    if (props.readonly) return;
    const newValue = index + 1;
    emit("update:modelValue", newValue);
  };
</script>

<style scoped>

  .stars-arr-box{
    width: 828px;
    display: flex;
    align-items: center;
    gap: 8px;
    color: #000;
    text-align: center;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    margin-bottom: 8px;
  }
  .selected-readonly{
    width: 42px !important;
    height: auto !important;
    font-size: 32px !important;
    margin-right: 3px;
    text-align: center;
  }
  .stars-arr{
    width: 42px;
  }
  .start-text {
    color: #516082;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    margin-right: 4px;
  }

  .statedisabled {
    color: #D5DBE4;
  }

  .star-rating {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;
  }

  .star-item {
    height: 22px;
  }
  .updstar {
    font-size: 33px !important;
  }
  .cursor-pointer {
    font-size: 22px;
    cursor: pointer;
  }
</style>
