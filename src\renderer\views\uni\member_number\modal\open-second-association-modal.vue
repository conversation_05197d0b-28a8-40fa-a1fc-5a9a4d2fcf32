<template>
  <t-dialog
    v-model:visible="visible"
    :header="null"
    :close-btn="false"
    width="480"
    attach="body"
    class="dialogSet dialogSet-noBodyPadding dialogSetDefault32"
    :close-on-overlay-click="false"
    :close-on-esc-keydown="false"
  >
    <template #body>
      <div class="box">
        <div class="box-title">
          <iconpark-icon name="iconinfo-b7dcijg4" class="iconpark"></iconpark-icon>
          {{$t('association.schools.qdkqszgx')}}
        </div>
        <div class="box-tip mt-16px ml-32px"> {{$t('association.workbench.u')}}</div>
        <div class="box-sure mt-12px ml-32px">
          <t-checkbox v-model="isRead"> {{$t('association.workbench.v')}}</t-checkbox>
          <span class="http cursor" @click="onOpenHttpModal"> {{$t('association.schools.szgxkqxy')}}</span>
        </div>
      </div>
    </template>
    <template #footer>
      <div class="footer mt-16px">
        <t-button
          class="btn confirm"
          variant="base"
          theme="default"
          style="width: 80px"
          @click="onClose"
        >  {{$t('association.workbench.o')}}</t-button>

        <t-button
          class="btn confirm"
          theme="primary"
          variant="base"
          style="width: 80px"
          @click="onNowOpen"
        >  {{$t('association.workbench.s')}}</t-button>
      </div>
    </template>
  </t-dialog>
  <AgreementDialog :header="$t('association.workbench.x')" ref="agreementDialogRef" />
</template>

<script setup lang="ts">
// import { useDigitalPlatformStore } from '@renderer/views/digital-platform/store/digital-platform-store';
// import { goToAdmin } from '@renderer/views/cbd/utils/auth';
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import AgreementDialog from '@/views/square/components/AgreementDialog.vue';
import { AgreementType } from "@/api/square/common";
import { MessagePlugin } from "tdesign-vue-next";
const agreementDialogRef = ref(null);
// const store = useDigitalPlatformStore();
const props = defineProps({
  teamId: {
    type: Boolean,
    default: false,
  },
});
const router = useRouter();
const visible = ref(false);
const emits = defineEmits(['onOk']);
const isRead = ref(false);
const onClose = () => {
  visible.value = false;
};

const onOpen = () => {
  isRead.value = false;
  visible.value = true;
};

const onOpenHttpModal = ()=> {

  agreementDialogRef.value.open(AgreementType.Association)
}
// 立即开启
const onNowOpen = () => {
  if(isRead.value) {
    onClose();
    emits('onOk')
  } else {
    MessagePlugin.error('请阅读并同意《数字高校开启协议》')
  }
}



defineExpose({
  onOpen,
  onClose
});
</script>

<style lang="less" scoped>


.box {
  &-title {
    color: #1A2139;

    /* kyy_fontSize_3/bold */
    font-family: "PingFang SC";
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px; /* 150% */

    display: flex;
    align-items: center;
    gap: 8px;
    .iconpark {
        font-size: 24px;
    }
  }
  &-tip {
    color: var(--kyy_color_modal_content, #516082);

    /* kyy_fontSize_2/regular */
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
  &-sure {
    color: var(--text-kyy_color_text_1, #1A2139);
    /* kyy_fontSize_2/regular */
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
    display: flex;
    align-items: center;
    .http {
      color: var(--brand-kyy_color_brand_default, #4D5EFF);

      /* kyy_fontSize_2/regular */
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
    }
  }
}
.suc-body {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  .suc-icon {

  }
  .tip {
    color: var(--text-kyy_color_text_1, #1A2139);
    text-align: center;

    /* kyy_fontSize_2/bold */
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px; /* 157.143% */
  }
  .descTip {
    color: var(--text-kyy_color_text_3, #828DA5);
    text-align: center;

    /* kyy_fontSize_2/regular */
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }

}
.footer {
  display: flex;
  justify-content: flex-end;
}
</style>
