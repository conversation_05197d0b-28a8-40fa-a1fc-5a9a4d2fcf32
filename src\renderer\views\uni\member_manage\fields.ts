import bianji from '@renderer/assets/approval/icons/<EMAIL>';
import number from '@renderer/assets/approval/icons/<EMAIL>';
import date from '@renderer/assets/approval/icons/icon_date.svg';
import others from '@renderer/assets/approval/icons/icon_more.svg';
import choose from '@renderer/assets/approval/icons/<EMAIL>';

export const filds = [
  {
    name: "文本",
    icon: bianji,
    children: [
      {
        type: "Input", // 控件类型，设计和运行时一一对应
        value: "", // 控件值
        name: "单行文本", // 控件名称，也是label
        typeName: "单行文本",
        placeholder: "请输入",
        required: false, // 是否必填
        systemDefault: false, // 是否系统默认字段
        passiveRelevance: false, // 是否被关联
        printable: true, // 是否打印
        editable: true // 是否可编辑
      },
      {
        type: "TextArea",
        name: "多行文本",
        typeName: "多行文本",
        value: "",
        placeholder: "请输入",
        passiveRelevance: false, // 是否被关联
        required: false,
        systemDefault: false,
        printable: true,
        editable: true
      },
      {
        type: "Note",
        name: "说明",
        typeName: "说明",
        passiveRelevance: false, // 是否被关联
        value: "说明文字",
        printable: true,
        editable: true
      },

      {
        type: "Divider", // 控件类型，设计和运行时一一对应
        value: "", // 控件值
        name: "分割线", // 控件名称，也是label
        typeName: "分割线",
        placeholder: "请输入",
        required: false,
        systemDefault: false, // 是否必填
        passiveRelevance: false, // 是否被关联
        printable: true, // 是否打印
        editable: true, // 是否可编辑
        lineType: "noBorder" // 线型类型: noBorder 无边框, border 有边框
      }
    ]
  },
  {
    name: "数值",
    icon: number,
    children: [
      {
        type: "NumberInput",
        name: "数字",
        typeName: "数字",
        value: null,
        placeholder: "请输入",
        unit: "",
        required: false,
        systemDefault: false,
        printable: true,
        editable: true,
        passiveRelevance: false, // 是否被关联
        process: true
      },
      {
        type: "MoneyInput",
        name: "金额",
        typeName: "金额",
        value: null,
        placeholder: "请输入",
        required: false,
        systemDefault: false,
        currency: "CNY",
        printable: true,
        editable: true,
        process: true,
        // 是否显示大写
        passiveRelevance: false, // 是否被关联
        showChinese: true,
        // 币种
        currencyOptions: [
          {
            label: "人民币",
            value: "CNY"
          },
          {
            label: "港元",
            value: "HKD"
          },
          {
            label: "澳门元",
            value: "MOP"
          }
        ]
      }
    ]
  },
  {
    name: "选项",
    icon: choose,
    children: [
      {
        type: "Select",
        name: "单选",
        typeName: "单选",
        value: null,
        placeholder: "请选择",
        required: false,
        systemDefault: false,
        host: [], // 存放关联了的控件id
        process: true,
        passiveRelevance: false, // 是否被关联
        printable: true,
        editable: true,
        searchable: false,
        hasBeenSet: false, // 是否设置选项关联其他控件
        options: []
      },
      {
        type: "MultiSelect",
        name: "多选",
        typeName: "多选",
        value: [],
        host: [], // 存放关联了的控件id
        placeholder: "请选择",
        required: false,
        systemDefault: false,
        passiveRelevance: false, // 是否被关联
        process: true,
        searchable: false,
        hasBeenSet: false, // 是否设置选项关联其他控件
        printable: true,
        editable: true,
        options: []
      }
    ]
  },
  {
    name: "日期",
    icon: date,
    children: [
      {
        type: "DatePicker",
        name: "日期",
        typeName: "日期",
        value: null,
        // 第二个值，如果dateType是1的话就是选择上午/下午，如果是2的话就是选择时：分
        value2: null,
        placeholder: "请选择",
        passiveRelevance: false, // 是否被关联
        required: false,
        systemDefault: false,
        printable: true,
        editable: true,
        dateType: 0
      }
    ]
  },
  {
    name: "其它",
    icon: others,
    children: [
      {
        type: "FileUpload",
        name: "附件",
        typeName: "附件",
        passiveRelevance: false, // 是否被关联
        value: [],
        required: false,
        systemDefault: false,
        printable: true,
        editable: true
      },
      {
        type: "ImageUpload",
        name: "图片",
        typeName: "图片",
        value: [],
        passiveRelevance: false, // 是否被关联
        required: false,
        systemDefault: false,
        printable: true,
        editable: true
      },
      {
        type: "Address",
        name: "地址",
        typeName: "地址",
        placeholder: "请选择",
        // 地址id集合
        passiveRelevance: false, // 是否被关联
        value: [],
        // 详细地址
        detail: "",
        detailrequired: false,
        systemDefault: false,
        detailPlaceholder: "请输入详细地址",
        // 完整的文本地址
        fullAddress: "",
        required: false,
        printable: true,
        editable: true,
        format: 0,
        process: true,
        autoLocate: true // 自动定位
      },

      {
        type: "FileUploadDown",
        name: "附件-下载模板",
        typeName: "附件-下载模板",
        passiveRelevance: false, // 是否被關聯
        value: [],
        models: [], // 用于存储示例模板
        required: false,
        systemDefault: false,
        printable: true,
        editable: true
      }
    ]
  }
];

export const filds_hk = [
  {
    name: "文本",
    icon: bianji,
    children: [
      {
        type: "Input",
        value: "",
        name: "單行文本",
        typeName: "單行文本",
        placeholder: "請輸入",
        required: false,
        systemDefault: false,
        passiveRelevance: false, // 是否被關聯
        printable: true,
        editable: true
      },
      {
        type: "TextArea",
        name: "多行文本",
        typeName: "多行文本",
        value: "",
        placeholder: "請輸入",
        passiveRelevance: false, // 是否被關聯
        required: false,
        systemDefault: false,
        printable: true,
        editable: true
      },
      {
        type: "Note",
        name: "說明",
        typeName: "說明",
        passiveRelevance: false, // 是否被關聯
        value: "說明文字",
        printable: true,
        editable: true
      },
      {
        type: "Divider", // 控件类型，设计和运行时一一对应
        value: "", // 控件值
        name: "分割線", // 控件名稱，也是label
        typeName: "分割線",
        placeholder: "請輸入",
        required: false,
        systemDefault: false, // 是否必填
        passiveRelevance: false, // 是否被关联
        printable: true, // 是否打印
        editable: true, // 是否可编辑
        lineType: "noBorder" // 线型类型: noBorder 无边框, border 有边框
      }
    ]
  },
  {
    name: "數值",
    icon: number,
    children: [
      {
        type: "NumberInput",
        name: "數字",
        typeName: "數字",
        value: null,
        placeholder: "請輸入",
        unit: "",
        required: false,
        systemDefault: false,
        printable: true,
        editable: true,
        passiveRelevance: false, // 是否被關聯
        process: true
      },
      {
        type: "MoneyInput",
        name: "金額",
        typeName: "金額",
        value: null,
        placeholder: "請輸入",
        required: false,
        systemDefault: false,
        currency: "CNY",
        printable: true,
        editable: true,
        process: true,
        // 是否顯示大寫
        passiveRelevance: false, // 是否被關聯
        showChinese: true,
        // 幣種
        currencyOptions: [
          {
            label: "人民幣",
            value: "CNY"
          },
          {
            label: "港元",
            value: "HKD"
          },
          {
            label: "澳門元",
            value: "MOP"
          }
        ]
      }
    ]
  },
  {
    name: "選項",
    icon: choose,
    children: [
      {
        type: "Select",
        name: "單選",
        typeName: "單選",
        value: null,
        placeholder: "請選擇",
        required: false,
        systemDefault: false,
        host: [], // 存放關聯了的控件id
        process: true,
        passiveRelevance: false, // 是否被關聯
        printable: true,
        editable: true,
        searchable: false,
        hasBeenSet: false, // 是否設置選項關聯其他控件
        options: []
      },
      {
        type: "MultiSelect",
        name: "多選",
        typeName: "多選",
        value: [],
        host: [], // 存放關聯了的控件id
        placeholder: "請選擇",
        required: false,
        systemDefault: false,
        passiveRelevance: false, // 是否被關聯
        process: true,
        searchable: false,
        hasBeenSet: false, // 是否設置選項關聯其他控件
        printable: true,
        editable: true,
        options: []
      }
    ]
  },
  {
    name: "日期",
    icon: date,
    children: [
      {
        type: "DatePicker",
        name: "日期",
        typeName: "日期",
        value: null,
        // 第二個值，如果dateType是1的話就是選擇上午/下午，如果是2的話就是選擇時：分
        value2: null,
        placeholder: "請選擇",
        passiveRelevance: false, // 是否被關聯
        required: false,
        systemDefault: false,
        printable: true,
        editable: true,
        dateType: 0
      }
    ]
  },
  {
    name: "其它",
    icon: others,
    children: [
      {
        type: "FileUpload",
        name: "附件",
        typeName: "附件",
        passiveRelevance: false, // 是否被關聯
        value: [],
        required: false,
        systemDefault: false,
        printable: true,
        editable: true
      },
      {
        type: "ImageUpload",
        name: "圖片",
        typeName: "圖片",
        value: [],
        passiveRelevance: false, // 是否被關聯
        required: false,
        systemDefault: false,
        printable: true,
        editable: true
      },
      {
        type: "Address",
        name: "地址",
        typeName: "地址",
        placeholder: "請選擇",
        // 地址id集合
        passiveRelevance: false, // 是否被關聯
        value: [],
        // 詳細地址
        detail: "",
        detailrequired: false,
        systemDefault: false,
        detailPlaceholder: "請輸入詳細地址",
        // 完整的文本地址
        fullAddress: "",
        required: false,
        printable: true,
        editable: true,
        format: 0,
        process: true,
        autoLocate: true // 自動定位
      },
      {
        type: "FileUploadDown",
        name: "附件-下載模板",
        typeName: "附件-下載模板",
        passiveRelevance: false, // 是否被關聯
        value: [],
        models: [], // 用于存储示例模板
        required: false,
        systemDefault: false,
        printable: true,
        editable: true
      }
    ]
  }
];
