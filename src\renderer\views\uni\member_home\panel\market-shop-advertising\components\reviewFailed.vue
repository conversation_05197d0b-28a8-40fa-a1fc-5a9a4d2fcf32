<template>
  <t-dialog
     v-model:visible="visible" :close-btn="true"
      @close="visible = false,editFormData.reason=''"
      class="dialogSet20240801 dialogSetHeader dialogSetFooter_top_0 dialogNoDefault"
      attach="body"
     width="480">
    <template #closeBtn>
      <!-- <iconpark-icon name="close" size="16"></iconpark-icon> -->
      <!-- <svg class="iconpark-icon" style="width: 16px; height: 16px">
        <use href="#close" />
      </svg> -->
      <iconpark-icon name="iconerror" style="font-size: 24px"></iconpark-icon>
    </template>
    <template #header>
      <div class="header">
        <!-- <svg class="iconpark-icon header-svg"><use href="#attention-6ebn71gl"></use></svg>
				<div class="header-title">提示</div> -->
        {{ $t("member.bolit.i") }}
      </div>
    </template>
    <div class="toBody">
      <t-form labelAlign="top" ref="editForm" :data="editFormData">
        <t-form-item :label="''" >
            <!-- <template #label>
                  <div class="diy-lable"> {{t('ad.btgyy')}}</div>
              </template> -->
          <div class="area reviewFailed">
            <t-textarea
              class="textarea"  v-model="editFormData.reason"
              placeholder="请输入拒绝原因，最多可支持200个字"
              :autosize="{ minRows: 4 }"
              :maxlength="200">
            </t-textarea>
            <span class="count">{{editFormData.reason?.length}}/200</span>
          </div>


        </t-form-item>
      </t-form>
    </div>
    <template #footer>
      <div class="footer">
        <t-button theme="default" variant="outline" @click="(visible = false), (editFormData.reason = '')">
          取消
        </t-button>
        <t-button :disabled="!editFormData.reason" theme="primary" @click="onSave"> 确定 </t-button>
      </div>
    </template>


  </t-dialog>
</template>
<script setup lang="ts">
import { admanageadreview } from "@renderer/api/member/api/ebookApi";

import { ref, onMounted } from "vue";
import { DialogPlugin, MessagePlugin } from "tdesign-vue-next";
import { useI18n } from "vue-i18n";
const editFormData = ref({
  reason: "",
});
const { t } = useI18n();
const cropperRef = ref(null);
const teamId = ref('');
const visible = ref(false);
const ad_id = ref('');
const emits = defineEmits(["callBack"]);
const onSave = () => {
  admanageadreview(
    {
      ad_id: ad_id.value,
      reason: editFormData.value.reason,
      status: 0,
    },
    teamId.value,
  )
    .then((res) => {

      console.log(res, "resssssssssss");
      MessagePlugin.success('操作成功');

    })
    .catch((err) => {
      MessagePlugin.error(err.message);
    }).finally(() => {
      editFormData.value.reason = "";
      visible.value = false;

      emits("callBack");
    });
};
const openWin = (id, val) => {
  console.log(id,111111111111);
  console.log(teamId,111111111111);
  ad_id.value = id;
  teamId.value = val;
  visible.value = true;
};
defineExpose({
  openWin,
});
</script>

<style lang="less" scoped>
.t-button + .t-button {
  margin-left: 0;
}
.footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  padding: 24px;
}
.toBody {
  padding: 0 24px;
}
.area {
  width: 100% !important;
  // margin-top: 10px;
  padding: 12px;
  padding-bottom: 4px;
  border-radius: var(--textarea-kyy_radius_textarea, 4px);
  border: 1px solid var(--textarea-kyy_color_textarea_border_default, #D5DBE4);
  // background: var(--textarea-kyy_color_textarea_bg_default, #FFF);
  :deep(.t-textarea__inner) {
    border: 0;
    padding: 0 !important
  }
  .count {
    display: flex;
    justify-content: flex-end;
    color: var(--text-kyy_color_text_5, #ACB3C0);
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px; /* 166.667% */
    width: 100%;

  }
}
.diy-lable {
  color: var(--text-kyy_color_text_3, #828da5);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  padding-left: 10px;
  position: relative;
  line-height: 22px;
}
.diy-lable::after {
  content: "*";
  position: absolute;
  top: 0;
  left: 0;
  color: var(--error-kyy_color_error_default, #d54941);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
}

</style>
