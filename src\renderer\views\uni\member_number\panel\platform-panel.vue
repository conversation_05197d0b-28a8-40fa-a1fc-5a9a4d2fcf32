<template>
  <div class="platform">
    <div class="box">
      <div class="title">
        请选择进入的端
      </div>
      <div class="desc">
        进入【{{ store?.activeAccount?.teamFullName }}商协会】
      </div>

      <div class="row mt-24px">
        <span class="row-item set-yellow cursor" @click="onSelectPlatform('member')">
          <img src="@renderer/assets/member/icon/icon_member.png" class="svg">
          <span class="name">成员端</span>
          <span class="tip">维护我的资料</span>
        </span>
        <span class="row-item set-blue cursor" @click="onSelectPlatform('manage')">
          <img src="@renderer/assets/member/icon/icon_web.png" class="svg">

          <span class="name">管理端</span>
          <span class="tip">管理成员的成员资料</span>
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useUniStore } from "@renderer/views/uni/store/uni";
import { getProfilesInfo } from '@renderer/utils/auth';

const store = useUniStore();
const profile = getProfilesInfo();
const emits = defineEmits(['selectPlatform']);
const onSelectPlatform = (type) => {

  // 选择端后记录该值
  selectApp(type);


  emits('selectPlatform', type);
};

const selectApp = (type) => {
  const arr = store.getStorePlatforms;
  const result = arr.find((v) => v.openId === profile.openid && v.teamId === store.activeAccount?.teamId);
  if (result) {
    result.apply = type;

  } else {
    arr.push({ openId: profile.openid, teamId: store.activeAccount?.teamId, apply: type });
  }
  store.setStorePlatforms(arr);
};

</script>

<style lang="less" scoped>
.platform {
    display: flex;
    align-items: center;
    justify-content: center;
    background-image: url('@renderer/assets/member/icon/bg_big.png');
    background-repeat: no-repeat;
    width: 100%;
    // height: 100%;
    margin: 16px;
    .box {
        margin-top: -40px;
        border-radius: 16px;
        background: var(--bg-kyy-color-bg-light, #FFF);

        /* kyy_shadow_l */
        box-shadow: 0px 12px 24px 0px rgba(0, 0, 0, 0.12);
        padding: 24px;
        .title {
            color: var(--kyy_color_modal_title, #1A2139);
            text-align: center;
            font-size: 16px;
            font-style: normal;
            font-weight: 600;
            line-height: 24px; /* 150% */
        }
        .desc {
            color: var(--font-kyy-font-gy-2, #516082);
            text-align: center;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
        }
        .row {
            display: flex;
            gap: 16px;
            &-item {
                display: flex;
                flex-direction: column;
                align-items: center;
                width: 320px;
                transition: all 0.25s linear ;


                border-radius: 16px;
                padding: 35px 16px;
                .name {
                    margin-top: 16px;
                }
                .tip {
                    margin-top: 4px;
                }
                .svg {
                    width: 64px;
                    height: 64px;
                }
                &:hover {
                    /* kyy_shadow_m */
                    box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.12);
                    transition: all 0.25s linear ;
                }
            }
            .set-yellow {
                background-image:  url('@renderer/assets/member/icon/bg_member.png');

                .name {
                    color: var(--yellow-kyy-color-yellow-active, #D9A213);
                    text-align: center;
                    font-size: 16px;
                    font-style: normal;
                    font-weight: 600;
                    line-height: 24px; /* 150% */
                }
                .tip {
                    color: var(--yellow-kyy-color-yellow-active, #D9A213);
                    text-align: center;
                    font-size: 14px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 22px; /* 157.143% */
                }
            }
            .set-blue {
                background-image:  url('@renderer/assets/member/icon/bg_web.png');
                .name {
                    color: var(--brand-kyy-color-brand-default, #4D5EFF);
                    text-align: center;

                    font-size: 16px;
                    font-style: normal;
                    font-weight: 600;
                    line-height: 24px; /* 150% */
                }
                .tip {
                    color: var(--brand-kyy-color-brand-default, #4D5EFF);
                    text-align: center;
                    font-size: 14px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 22px; /* 157.143% */
                }
            }
        }
    }
}

:deep(.t-badge--circle){

  padding-right: calc((16px - 8px) / 2);
  padding-left: calc((16px - 8px) / 2);
  min-width: 8px;
  height: 16px;
  background-color: var(--td-error-color);
  line-height: 16px;
}
</style>
