<template>
  <AppCard
    :is-loading="isLoading"
    :is-load-error="isLoadError"
    v-bind="$attrs"
  >
    <AppCardHeader style="font-size: 16px;" :theme="getHeaderTheme()">
      {{ title }}
    </AppCardHeader>

    <AppCardBody>
      <div class="w-full">
        <div class="flex flex-col gap-[7px] mb-[6px]">
          <div class="flex gap-[5px]">
            <t-image
              :key="0"
              fit="cover"
              position="center"
              :src="lodash.get(data, 'header.team.logo', '') || BizUnion"
              class="popularize-company-logo"
            />
            <span class="popularize-company-name">{{ lodash.get(data, 'header.team.name', '---') }}</span>
          </div>
          <div class="popularize-dynamic-info">
            {{ data?.content?.title }}
          </div>
        </div>
        <div class="w-full flex flex-col gap-[4px] mb-[12px]" style="width: 100%;">
          <h4 v-if="scene === 5037" class="popularize-dynamic-title">{{ t('member.eb.x') }}</h4>
          <h4 v-else class="popularize-dynamic-title">{{ t('square.post.extensionDynamics') }}</h4>
          <div v-if="postConfig.type === 'FORWARD'" class="bg-[#F5F8FE] rounded-[8px] p-4">
            <div class="ellipsis-1 mb-4 pl-4">{{ postConfig.forwardConfig.title ? postConfig.forwardConfig.title : '发布了动态' }}</div>
            <div class="popularize-msg-wrap p-4! bg-[#EBF1FC]!" @click="openPost">
              <div class="popularize-msg-img-wrap">
                <Avatar
                  v-if="postConfig?.forwardConfig?.isPersonal"
                  class="mr8"
                  :image-url="postConfig.forwardConfig.cover"
                  :user-name="postConfig.forwardConfig.square_name"
                  avatar-size="72px"
                />
                <t-image
                  :key="0"
                  fit="cover"
                  position="center"
                  :src="postConfig?.forwardConfig?.cover || BizUnion"
                  class="popularize-msg-img"
                />
              </div>
              <div class="popularize-msg-text-wrap">
                <div class="popularize-msg-text text-[#828DA5]! popularize-msg-text-line-1">{{ postConfig.forwardConfig.square_name }}</div>
                <p class="popularize-msg-text text-[#1A2139] ellipsis-2">{{ postConfig.forwardConfig.forWardcontent }}</p>
              </div>
            </div>
          </div>
          <div v-else class="popularize-msg-wrap" @click="openPost">
            <div v-if="postConfig.cover" class="popularize-msg-img-wrap">
              <t-image
                :key="0"
                fit="cover"
                position="center"
                :src="postConfig.cover"
                class="popularize-msg-img"
              />
              <!-- 动态 图片 -->
              <strong v-if="postConfig.type === 'PICTURE'" class="popularize-msg-img-num">
                {{ postConfig.num }}
              </strong>
              <!-- 动态 视频 -->
              <div v-if="postConfig.type === 'VIDEO'" class="popularize-msg-img-play">
                <img class="play-icon" src="@/assets/svg/msg-card-play.svg">
              </div>
              <!-- 动态 相册 -->
              <div v-if="isHasSelf(postConfig.type as PlatType)" class="popularize-msg-img-album-node">
                <img
                  v-if="postConfig.type === PlatType.ALBUM_NODE"
                  class="play-icon"
                  src="@/assets/svg/icon_popularize-msg-ALBUM_NODE.svg"
                >
                <img
                  v-if="postConfig.type === PlatType.PARTY_BUILDING"
                  class="play-icon"
                  src="@/assets/square/square_logo_party.svg"
                >
                <img
                  v-if="postConfig.type === PlatType.FENGCAI"
                  class="w-12 h-12"
                  src="@/assets/square/square_logo_fc.svg"
                >
                <img
                  v-if="[PlatType.TEAM_INTRO, PlatType.TEAM_HONOR_ROLL, PlatType.TEAM_HISTORY].includes(postConfig.type as PlatType)"
                  class="w-10 h-10"
                  src="@/assets/square/square_logo_about.svg"
                >
              </div>
              <!-- 动态 文章 -->
              <div v-if="postConfig.type === 'ARTICLE'" class="popularize-msg-img-books">
                <img class="books-icon" src="@/assets/svg/msg-card-books.svg">
              </div>
            </div>
            <div class="popularize-msg-text-wrap">
              <p
                v-for="item in postConfig.content"
                :key="item"
                :class="['popularize-msg-text', postConfig.content?.length >= 2 ? 'popularize-msg-text-line-1' : '']"
                :item="item"
              >
                {{ item }}
              </p>
            </div>
          </div>
        </div>
        <template v-if="(scene === 5037)">
          <div class="flex flex-col gap-[4px]">
            <h4 class="popularize-dynamic-title">{{ t('member.eb.y') }}</h4>
            <div class="popularize-dynamic-info">
              <span v-if="data.extend?.applicant_type === 1" class="tag-person">个人</span>
              <span>{{ lodash.get(data, 'extend.applicant', '---') }}</span>
            </div>
          </div>
          <div class="divider" />
        </template>
        <template v-else-if="(scene === 5038 || scene === 5039)">
          <div class="flex flex-col gap-[4px]">
            <h4 class="popularize-dynamic-title">{{ t('square.post.cancelDynamics2') }}</h4>
            <div class="popularize-dynamic-info">
              {{ lodash.get(data, 'extend.reason', '---') }}
            </div>
          </div>
        </template>
      </div>
    </AppCardBody>

    <AppCardFooter v-if="scene === 5037">
      <Button
        v-if="getBtnTxt(status)"
        class="w-full fw-600!"
        variant="outline"
        disabled
      >
        {{ getBtnTxt(status) }}
      </Button>
      <template v-else-if="status === 5">
        <Button
          class="w-full"
          variant="outline"
          theme="danger"
          @click.stop.prevent="clickReject"
        >
          {{ t('ebook.visrefu') }}
        </Button>
        <Button
          class="w-full btn-primary"
          variant="outline"
          theme="primary"
          @click.stop.prevent="clickPass"
        >
          {{ t('niche.pass') }}
        </Button>
      </template>
      <t-button
        v-else
        class="w-full fw-600"
        variant="outline"
        @click="handleClick"
      >
        {{ t("im.public.detail") }}
      </t-button>
    </AppCardFooter>
  </AppCard>
  <!-- 动态详情 -->
  <PostDetail
    v-if="post_id"
    :id="post_id"
    v-model="post_id"
    :is-preview="false"
    :from-outer="true"
    class-name="popularize-dialog-post-detail"
  />
  <Reject
    ref="rejectModalRef"
    @on-send="onSaveReject"
  />
</template>

<script setup lang="ts">

// api 文档：https://alidocs.dingtalk.com/i/nodes/2Amq4vjg89gzYGpmhvA5j9D1V3kdP0wQ?utm_scene=person_space
// 需求地址： https://www.figma.com/design/treWUNzUIQoJAAElq6Usfk/%E6%95%B0%E5%AD%97%E5%B9%B3%E5%8F%B0-%E6%A1%8C%E9%9D%A2%E7%AB%AF?node-id=1878-159205&t=nk5N3jjsi0I7nwwH-0
/**
* 5037: 申请
* 5038: 投放中取消
* 5039: 投放取消
*/
import lodash from 'lodash';
import { PropType, computed, onMounted, onUnmounted, ref } from 'vue';
import { AppCard, AppCardBody, AppCardHeader, AppCardFooter } from '@renderer/views/message/chat/MessageAppCard';
import { useI18n } from 'vue-i18n';
import { Button, MessagePlugin } from 'tdesign-vue-next';
import LynkerSDK from '@renderer/_jssdk';
import { goToAdmin as goToAdminMember } from '@renderer/views/member/utils/auth';
import { goToAdmin as goToAdminCBD } from '@renderer/views/cbd/utils/auth';
import { goToAdmin as goToAdminPolitics } from '@renderer/views/politics/utils/auth';
import { goToAdmin as goToAdminAssociation } from '@renderer/views/association/utils/auth';
import { DynamicsApplyRejectAxios as rejectCbdApi, DynamicsApplyAgreeAxios as passCbdApi } from '@renderer/api/cbd/api/businessApi';
import { DynamicsApplyRejectAxios as rejectPolitics, DynamicsApplyAgreeAxios as passPolitics } from '@renderer/api/politics/api/businessApi';
import { DynamicsApplyRejectAxios as rejectMember, DynamicsApplyAgreeAxios as passMember } from '@renderer/api/member/api/businessApi';
import { DynamicsApplyRejectAxios as rejectAssociation, DynamicsApplyAgreeAxios as passAssociation } from '@renderer/api/association/api/businessApi';
import { getDynamicStatus } from '@renderer/api/im/chat';
import textImg from '@renderer/assets/member/svg/dynamic.svg';
import to from 'await-to-js';
import { commonUpdateMsgStatus } from '@renderer/views/message/service/extend/statusUtils';
import { checkPostInvisible } from '@renderer/api/square/post';
import dayjs from 'dayjs';
import Avatar from '@renderer/components/kyy-avatar/index.vue';
import BizUnion from '@renderer/assets/svg/im_avatar_team.svg';
import Reject from '../../components/Reject.vue';
import { IMRefreshType, PlatType, isHasSelf } from '../../../../common/constant';
import PostDetail from '@/views/square/components/post/PostDetail.vue';
import { getBtnTxt, SceneType } from './constant';

const { ipcRenderer } = LynkerSDK;

const props = defineProps({
  msg: { type: Object as PropType<MessageToSave>, required: true },
});

const { t } = useI18n();
const passMap = {
  cbd: passCbdApi,
  government: passPolitics,
  member: passMember,
  association: passAssociation,
};
const rejectMap = {
  cbd: rejectCbdApi,
  government: rejectPolitics,
  member: rejectMember,
  association: rejectAssociation,
};

const isLoading = ref(true);
const isLoadError = ref(false);

const init = () => {
  const { status: statusCard } = data.value?.extend || {};
  // if (statusCard) {
  //   console.error('已经存在状态23', statusCard);
  status.value = statusCard;
  //   return;
  // }
  loadBatchStatus();
};

const title = computed(() => {
  const data = props.msg.contentExtra?.data;
  const type = data.extend.channel_type;
  if (type === 'cbd') {
    return t('application.digital_cbd');
  }
  if (type === 'government') {
    return t('application.government');
  }
  if (type === 'member') {
    return t('application.member');
  }
  if (type === 'association') {
    return t('niche.szsq');
  }
  if (channel_type === 'uni') {
    return i18nt('niche.szgx');
  }
  return t('application.member');
});

const post_id = ref('');
const openPost = async () => {
  // eslint-disable-next-line no-unsafe-optional-chaining
  if ([SceneType.CANCEL, SceneType.REJECT].includes(data.value?.scene)) {
    return;
  }
  const { origin_id } = data.value?.extend || {};
  const id = `${origin_id}`;
  const [err, res] = await to(
    checkPostInvisible(id, { square_id: undefined }),
  );
  if (!err) {
    const { invisible, deleted } = res.data;
    if (deleted) {
      await MessagePlugin.error(t('square.post.deleted'));
      return;
    }
    if (invisible) {
      await MessagePlugin.error(t('square.post.invisible'));
      return;
    }
    post_id.value = id;
  }
};
/** 动态详情 */
const postConfig = computed(() => {
  const data = props.msg.contentExtra?.data;
  const post = data.extend;
  // const push_flag = dayjs(post.push_flag).format('YYYY年MM月DD日 HH:mm');
  const push_flag = post.push_date;
  const content = post.title ? [post.title] : [];
  const forwardConfig: any = {};
  let cover = post.img;
  if (post.push_type === 'PICTURE') {
    post.content && content.push(post.content);
  }
  if (post.push_type === 'VIDEO') {
    cover += '?x-oss-process=video/snapshot,t_10000,f_jpg,w_0,h_0,m_fast';
    post.content && content.push(post.content);
  }
  if (post.push_type === 'ARTICLE') {
    post.content && content.unshift(post.content);
  }
  if (post.push_type === 'ALBUM_NODE') {
    post.content && content.push(post.content);
    content.push(dayjs(Number(post.time_node)).format('YYYY年MM月DD日 HH:mm'));
    // const text = post.push_date.split(',');
    // content.push(`${text[0]} ${text[1]}`);
  }
  if (post.push_type === 'TEXT') {
    cover = textImg;
    post.content && content.push(post.content);
  }
  if ([PlatType.TEAM_INTRO, PlatType.TEAM_HONOR_ROLL, PlatType.TEAM_HISTORY].includes(post.push_type as PlatType)) {
    if (!cover) {
      cover = textImg;
    }
    post.content && content.push(post.content);
  }
  if (post.push_type === 'FORWARD') {
    try {
      const { square_avatar, square_name, content: forWardcontent, square_type } = JSON.parse(post?.relay || '{}') || {};
      forwardConfig.cover = square_avatar;
      forwardConfig.square_name = square_name;
      forwardConfig.forWardcontent = forWardcontent || t('square.post.defaultMsg');
      forwardConfig.title = post?.content;
      forwardConfig.isPersonal = square_type === 'INDIVIDUAL';
    } catch (e) {
      console.error(e);
    }
  }
  if (!content.length) {
    content.push(t('square.post.defaultMsg'));
  }
  if (!post.content) {
    content.unshift(t('square.post.defaultMsg'));
  }
  return {
    /** 推广类型 */
    type: post.push_type,
    /** 封面 */
    cover,
    /** 封面数 */
    num: post.img_count,
    /** 发布时间 */
    push_flag,
    /** 内容 */
    content,
    /** 转发内容 */
    forwardConfig,
  };
});

const scene = computed(() => props.msg.contentExtra?.scene);
const data = computed(() => props.msg.contentExtra?.data);

const reason = ref('');
const rejectModalRef = ref(null);
const onSaveReject = (data) => {
  reason.value = data.reason;
  handleApply('reject');
};

const status = ref(5);
const clickReject = () => {
  rejectModalRef.value.onOpen();
};
const clickPass = () => {
  handleApply('agree');
};
const loadBatchStatus = async () => {
  const { team_id, push_id = '' } = data.value?.extend || {};
  const params = {
    push_ids: String(push_id || ''),
  };
  const [err, res] = await to(getDynamicStatus(params, team_id));

  if (err) {
    isLoadError.value = true;
    return;
  }
  const { code, data: data2 } = res.data || {};
  if (code === 0) {
    status.value = data2[0].status;
    isLoading.value = false;
    const extend = {
      ...data.value?.extend,
      status: data2[0]?.status,
    };
    commonUpdateMsgStatus(props.msg, extend, 'extend');
  }
};

const handleApply = async (opt) => {
  const { channel_type, application_dynamics_log_uuid, select_team_id } = data.value?.extend || {};
  const params = {
    application_dynamics_log_uuid,
  };
  if (opt === 'reject' && reason.value) {
    params.reason = reason.value;
  }
  const fn = opt === 'agree' ? () => passMap[channel_type]?.(params, select_team_id) : () => rejectMap[channel_type]?.(params, select_team_id);

  const [err, res] = await to(fn());
  if (err) {
    loadBatchStatus();
    MessagePlugin.error(err?.message);
    return;
  }
  const { code, message } = res.data || {};
  if (code !== 0) {
    loadBatchStatus();
    MessagePlugin.error(message || '暂无相关权限');
    return false;
  }
  let msg = '已通过申请';
  if (opt === 'reject') {
    msg = '已拒绝申请';
    rejectModalRef.value.onClose();
  }
  MessagePlugin.success(msg);
};

const getHeaderTheme = () => {
  /**
   * 5037: 申请
   * 5038: 投放中取消
   * 5039: 投放取消
   */
  const theme = { 5037: 'primary', 5038: 'primary', 5039: 'primary' }[scene.value] || 'primary';
  return theme as any;
};

const handleClick = () => {
  const url = '/PManage/TrendsPanel/PApply';
  const team_id = data.value.extend.select_team_id;
  const type = data.value.extend.channel_type;
  if (type === 'cbd') {
    goToAdminCBD(team_id, { origin: 'message', redirect: url });
  }
  if (type === 'government') {
    goToAdminPolitics(team_id, { origin: 'message', redirect: url });
  }
  if (type === 'member') {
    goToAdminMember(team_id, { origin: 'message', redirect: url });
  }
  if (type === 'association') {
    goToAdminAssociation(team_id, { origin: 'message', redirect: url });
  }
};
const updateAd = (e, args) => {
  const id = data.value?.extend?.application_dynamics_log_uuid;
  if (!id) {
    return;
  }
  const { type, data: { extend } } = args || {};
  if (type === IMRefreshType.IMRefreshDynamic && extend?.application_dynamics_log_uuid === id) {
    loadBatchStatus();
  }
};
onUnmounted(() => {
  ipcRenderer.off('IM-refresh', updateAd);
});

onMounted(() => {
  ipcRenderer.on('IM-refresh', updateAd);
  init();
});
</script>

<style lang="less" scoped>
.divider {
  margin-top: 8px;
  display: block;
  height: 1px;
  background: var(--divider-kyy_color_divider_light, #ECEFF5);
}

.square-link {
  color: #4d5eff;
  cursor: pointer;
}

.popularize-company-logo {
  display: block;
  width: 24px;
  height: 24px;
  border-radius: 100%;
  overflow: hidden;
}

.popularize-company-name {
  flex: 1;
  font-family: PingFang SC;
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  color: #1A2139;
}

.popularize-dynamic-title {
  font-family: PingFang SC;
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  color: #828DA5;
}

.popularize-dynamic-info {
  font-size: 14px;
  font-weight: 400;
  line-height: 26px;
  color: #1A2139;

  .tag-person {
    margin-right: 4px;
    padding: var(--checkbox-kyy_radius_checkbox, 2px) 4px;
    color: var(--kyy_color_tag_text_success, #499D60);
    justify-content: center;
    align-items: center;
    font-size: 12px;
    gap: 4px;
    border-radius: var(--kyy_radius_tag_s, 4px);
    background: var(--kyy_color_tag_bg_success, #E0F2E5);
  }
}

.popularize-msg-wrap {
  display: flex;
  flex-direction: row;
  padding: 8px;
  background-color: #F5F8FE;
  border-radius: 8px;
  justify-content: center;
  align-items: center;
  gap: 12px;
  // cursor: pointer;
}

.popularize-msg-img {
  display: block;
  width: 100%;
  height: 100%;
}

.popularize-msg-text-wrap {
  flex-grow: 2;
  display: flex;
  align-items: center;
  flex-direction: column;
  width: 0;
  gap: 4px;
}

.popularize-msg-text {
  width: 100%;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  font-family: PingFang SC;
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  color: #1A2139;
}

.popularize-msg-text-line-1 {
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  &+& {
    color: #828DA5;
  }
}

.popularize-msg-img-wrap {
  position: relative;
  width: 72px;
  height: 72px;
  border-radius: 6px;
  flex-shrink: 0;
  overflow: hidden;
}

.popularize-msg-img-num {
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  z-index: 2;
  bottom: 0;
  right: 0;
  height: 20px;
  min-width: 20px;
  padding: 0 5px;
  border-radius: 4px 0 0 0;
  font-family: PingFang SC;
  font-size: 14px;
  font-weight: 600;
  line-height: 19.6px;
  color: #fff;
  background-color: rgba(0, 0, 0, .3);
}

.popularize-msg-img-play {
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  z-index: 2;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  margin: auto;
  width: 36px;
  height: 36px;
  border-radius: 100%;
  background-color: rgba(0, 0, 0, .2);
}

.popularize-msg-img-album-node {
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  z-index: 2;
  top: 0;
  right: 0;
  margin: auto;
  width: 20px;
  height: 20px;
  background-color: rgba(0, 0, 0, .2);
  border-bottom-left-radius: 8px;
}

.play-icon,
.books-icon {
  width: 16px;
  height: 16px;
}

.popularize-msg-img-books {
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  z-index: 2;
  top: 0;
  bottom: 0;
  right: 0;
  width: 20px;
  height: 20px;
  left: 52px;
  border-radius: 0px 8px 0px 8px;
  background-color: rgba(0, 0, 0, .2);
}
</style>
