<template>
  <div ref="containerFlets" class="home">
    <!-- v-show="scrolledDistance > 1000" :class="{isOpacity: scrolledDistance > 1200}" -->
    <!-- <div class="backTop cursor" v-show="scrolledDistance > 180" :class="{isOpacity: scrolledDistance > 200}" @click="scrollToTop">
      <iconpark-icon class="iconarrowup" name="icontopinside"></iconpark-icon>

    </div> -->

    <!-- <div class="backTop cursor" :class="{isOpacity: scrolledDistance > 300}"   @click="scrollToTop">
      <iconpark-icon class="iconarrowup" name="icontopinside"></iconpark-icon>
    </div> -->

    <div class="boxView">
      <div class="bodyContent">
        <div class="sticky">
          <div class="banner">
            <div class="tabsTop">
              <!-- 会员广场号 -->
              <div
                class="tabsTop-item cursor"
                v-for="(tab, tabIndex) in tabsTop" :key="tabIndex"
                :class="{
                  active:  tab.value === currentTabTop.value
                }"
                v-show="tab.isShow"
                @click="setTapTop(tab)"
              >
                {{  tab.label }}
              </div>
              <!-- 会员商机 -->
              <!-- <div
                class="item"
                :class="{
                  active: 4 === currentTab
                }"
                @click="setTab(4)"
              >

                {{ $t('member.sv17.s_2') }}

              </div> -->
              <!-- 会员名录 -->
              <!-- <div
                class="item"
                :class="{
                  active: 5 === currentTab
                }"
                @click="setTab(5)"
              >

                {{ $t('member.sv17.s_3') }}

              </div> -->
              <!-- <div
                class="item"
                :class="{
                  active: 7 === currentTab
                }"
                @click="setTab(7)"
              >
                电子会刊

              </div> -->
              <!-- 会员活动 -->
              <!-- <div
                class="item"
                :class="{
                  active: 6 === currentTab
                }"
                @click="setTab(6)"
              >

                {{ $t('member.sv17.s_10') }}
              </div> -->
              <!-- 会员资料 -->
              <!-- <div
                class="item"
                :class="{
                  active: 1 === currentTab
                }"
                @click="setTab(1)"
              >

                {{ $t('member.sv17.s_4') }}
              </div> -->
              <!-- <div
                v-if="
                  !(currentMemberCard?.is_contact || currentMemberCard?.type === 2)
                "
                class="item"
                :class="{
                  active: 2 === currentTab
                }"
                @click="setTab(2)"
              >

                {{ $t('member.sv17.s_5') }}

              </div> -->
                <!-- 联系人 -->
              <!-- <div class="act-tag" :class="'act-' + currentTab"></div> -->
            </div>
            <div class="group">
              <div
                theme="default"
                class="group-item cursor"
                v-show="isShowSquare"
                @click="onActionSquare({team_id: currentTeamId})"
              >
                <img :src="square" class="svg">
                <span class="text" style="width: 72px !important;">{{ $t('member.squarek.n') }}</span>

              </div>

              <div
                v-show="isAdminValue?.member"
                theme="default"
                class="group-item cursor"
                @click="onInviteJobClub"
              >
                <img src="@renderer/assets/member/icon/icon_invite.png" class="svg">
                <span class="text">{{$t('member.digital.o')}} </span>
              </div>
              <!-- @click="onSelectPlatform('manage')" -->
              <div
                v-show="isManage"
                theme="default"
                class="group-item cursor"
                @click="goToAdmin(currentTeamId)"
              >
                <!-- <t-badge style="line-height: 20px;" :count="store.getAllTeamApplyCount + store.getAllTeamActiveCount" :offset="[-12, -2]">
                  <img src="@renderer/assets/member/icon/icon_web.png" class="svg">
                  <span class="text">{{ $t('member.sv17.admin') }}</span>
                </t-badge> -->
                <t-badge style="line-height: 20px;" :count="appCount" :offset="[-12, -2]">
                  <img src="@renderer/assets/member/icon/icon_web.png" class="svg">
                  <span class="text">{{ $t('member.sv17.admin') }}</span>
                </t-badge>

              </div>
            </div>
          </div>
        </div>

        <div ref="containerFlets" class="bodyc">
          <keep-alive>
            <component :is="panels[currentTabTop ? currentTabTop.component : '']" :key="currentTabTop?.component" @selectPanel="selectPanel" :platform="props.platform"/>
          </keep-alive>
        </div>
      </div>
    </div>
  </div>
  <!-- <div class="footer footerV" >

  </div> -->
  <div class="backTopContact cursor isOpacity" v-show="activeAccount?.user_ids?.platformStaff > 0 && (memberSettingInfo?.im_group || members > 0)"  @click="onLiaisonModal">
    <!-- <img class="iconarrowup" src="@renderer/assets/digital/icon/contacter.png"> -->
    <img  v-if="proxy.$i18n.locale === 'zh-cn'" class="iconarrowup" src="@renderer/assets/digital/svg/digtalk.svg">
    <img  v-else class="iconarrowup" src="@renderer/assets/digital/svg/digtalk_hk.svg">

  </div>

  <!-- <AddInMemberModal ref="addInMemberModalRef" /> -->
  <InviteQrcodeModal
    ref="inviteQrcodeModalRef"
    :header-text="$t('member.digital.o')"
    :way-tips="'通过以下方式邀请组织加入'"
    :member="currentMemberCard"
  />
  <liaisonModel ref="liaisonModelRef"/>
</template>

<script lang="ts" setup>
import {
  getMemberApplyLinkAxios,
  getMemberCardsAxios,
  getMemberSettingAxios,
  getRegularDetailAxios,
 checkIsAdminAxios,
getSharedSquaresAxios,
getMemberAdminAxios} from "@renderer/api/uni/api/businessApi";
import square from "@renderer/assets/member/svg/square.svg";
import {
  workShopAppAxios
} from "@renderer/api/uni/api/businessApi";

import InviteQrcodeModal from "@renderer/views/uni/member_number/modal/invite-qrcode-modal.vue";
import useRouterHelper from "@renderer/views/square/hooks/routerHelper";
// import AddInMemberModal from "@renderer/views/uni/member_home/panel/regular-member-panel/modal/add-in-member-modal.vue";
import Empty from "@renderer/components/common/Empty.vue";
import { getResponseResult } from "@renderer/utils/myUtils";
import { MessagePlugin } from "tdesign-vue-next";
import { ref, onMounted, onActivated, watch, Ref, toRaw, computed, getCurrentInstance, defineAsyncComponent } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useUniStore } from "@renderer/views/uni/store/uni";
// import lodash from "lodash";
import { panels } from "@renderer/views/uni/member_number/panel/member-panel/index";
import { useI18n } from "vue-i18n";
import { getProfilesInfo } from '@renderer/utils/auth';
import { getUniTeamID, goToAdmin } from "@renderer/views/uni/utils/auth";
import KyyAvatar from "@/components/kyy-avatar/index.vue";
import { ORG_DEFAULT_AVATAR } from "@/views/square/constant";
import { useDigitalPlatformStore } from "@renderer/views/digital-platform/store/digital-platform-store";
import { platform } from "@renderer/views/digital-platform/utils/constant";
import { onMountedOrActivated } from "@renderer/hooks/onMountedOrActivated";
import to from "await-to-js";
import { useApi } from "@renderer/views/member/hooks/api";
import liaisonModel from '@renderer/views/uni/member_number/modal/liaison-modal.vue';

const digitalPlatformStore = useDigitalPlatformStore();
// const RichComp = defineAsyncComponent(() => import("@renderer/views/uni/member_number/components/rich-comp.vue"));


const { menuList, routeList, roleFilter } = useRouterHelper("uniIndex");

const router = useRouter();
const { proxy } = getCurrentInstance() as any;
const liaisonModelRef = ref(null);
const { t } = useI18n();
const { onActionSquare } = useApi();
const currentTab = ref(4); // 1 会员资料 2联系人、3会员广场、4会员商机
const memberCards: Ref<any> = ref([]);
const currentMemberCard: Ref<any> = ref(null);
// const store = useUniStore();
const profile = getProfilesInfo();
const route = useRoute();

const props = defineProps({
  platform: {
    type: String,
    default: '',
  },
})

// 平台类型 目前只有digital-platform
const platformCpt = computed(() => {
  return props.platform || route.query?.platform
})


const currentTeamId = computed(() => {
  if(platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount?.teamId
  } else {
    return  getUniTeamID()
  }
})

const store: any = computed(()=> {
  if(platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore
  } else {
    return useUniStore();
  }
})

const activeAccount = computed(() => {
  if(platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount
  } else if(platformCpt.value === platform.digitalWorkbench)  {
    return route.query;
  } else {
    return store.value.activeAccount
  }
})

const tabsTop = ref([
  {
    label: t('member.squarek.i'),
    value: 'home',
    component: 'PHome',
    isShow: true,
  },
  // {
  //   label: '广场号',
  //   value: 'square',
  //   component: 'PSquare',
  //   isShow: true,
  // },
  {
    label: t('member.squarek.k'),
    value: 'my',
    isShow: false,
    component: 'PMy'
  },
]);
const currentTabTop = ref(tabsTop.value[0])


const isAdminValue = ref(null);
// 判断当前用户是否为管理员
const onCheckIsAdmin = async (params, teamId?) => {
  let res: any = null;
  try {
    res = await checkIsAdminAxios(params, teamId);
    res = getResponseResult(res);
    if (!res) return;

    isAdminValue.value = res.data;
    const my = tabsTop.value.find((v) => v.value === 'my');
    if(my) {
      if (isAdminValue.value?.member) {
        my.isShow = true;
      } else {
        my.isShow = false;
      }
    }
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    if (errMsg === 'Network Error') {

    } else {
      MessagePlugin.error(errMsg);
    }
  }
};

/**
 *
 * @param idStaff 获取应用统计
 */
const appCount = ref(0);
const onWorkShopAppAxios = async () => {
  let res: any = null;
  try {

    res = await workShopAppAxios({uuids: []}, currentTeamId.value);
    res = getResponseResult(res);
    if (!res) return;
    console.log(res)
    const {data} = res;
    console.log(data);
    // if (data && data.length > 0) {
    //   const kvObject = data.reduce((acc, item) => {
    //     acc[item.uuid] = item.count;
    //     return acc;
    //   }, {});
    //   appCount.value = kvObject;
    // }
    appCount.value = data || 0;

  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    if (errMsg === 'Network Error') {

    } else {
      // MessagePlugin.error(errMsg);
      console.log(errMsg)
    }
  }
};



const selectPanel = (val) => {
  if(val) {
    const tem = tabsTop.value.find(v=>v.value === val);
    if(tem) {
      currentTabTop.value = tem;
    }
  }
}

// const scrolledDistance = ref(0); // 滚动距离
// const containerFlets = ref(null);
// const handleScroll = (event) => {
//   console.log(event.target.scrollTop, 'e');
//   scrolledDistance.value = event.target.scrollTop;
//   console.log(scrolledDistance.value, 'few')
//   // scrolledDistance.value = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop;
// };
// let animationId = null;
// const scrollToTop = () => {
//   console.log(containerFlets.value.scrollTop);
//   // containerFlets.value.scrollTop = 0; // 滚动容器到顶部
//   cancelAnimationFrame(animationId); // 取消之前的动画

//       const scrollTop = containerFlets.value.scrollTop;
//       console.log(containerFlets.value.scrollTop);
//       const step = Math.ceil(scrollTop / 6); // 每帧滚动的步长
//       console.log(step);
//       const animate = () => {
//         if (containerFlets.value.scrollTop > 0) {
//           containerFlets.value.scrollTop -= step;
//           animationId = requestAnimationFrame(animate); // 请求下一帧动画
//         } else {
//           cancelAnimationFrame(animationId); // 动画结束，取消请求
//         }
//       };

//       animationId = requestAnimationFrame(animate); // 开始动画
// };

const setTapTop = (row) => {
  currentTabTop.value = row;
}

const goLeaf = () => {
  // router.push({
    //   path: "/memberIndex/member_manage",
    //   query: {
    //     // projectId: props.projectId
    //   }
    // });
    const searchMenu = routeList.find((v) => v.name === "uni_leaflets");
    // store.addTab(routeList.filter((v) => v.affix).filter(roleFilter));
    router.push({ path: searchMenu.fullPath, query: { } });
    store.value.addTab(toRaw(searchMenu));
};

const isManage = computed(() => store.value.activeAccount &&
        (isAdminValue.value?.super ||
          isAdminValue.value?.isAdmin ||
          isAdminValue.value?.superAdmin));


const emits = defineEmits(['selectPlatform']);
const onSelectPlatform = (type) => {
  selectApp(type);
  emits('selectPlatform', type);
};

const selectApp = (type) => {
  const arr = store.value.getStorePlatforms;
  const result = arr.find((v) => v.openId === profile.openid && v.teamId === store.value.activeAccount?.teamId);
  if (result) {
    result.apply = type;

  } else {
    arr.push({ openId: profile.openid, teamId: store.value.activeAccount?.teamId, apply: type });
  }
  store.value.setStorePlatforms(arr);
};




// 邀请入会
const inviteQrcodeModalRef = ref(null);
const onInviteJobClub = () => {
  getInviteLinkAxios().then((val) => {
    inviteQrcodeModalRef.value.onOpen(val);
  });
};

// 获取邀请链接
const getInviteLinkAxios = () => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await getMemberApplyLinkAxios({}, currentTeamId.value);
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      resolve(result.data.link);
    } catch (error) {
      reject();
      const errMsg = error instanceof Error ? error.message : error;
      if (errMsg !== 'Network Error') MessagePlugin.error(errMsg);
    }
  });
};

const onSearch = () => {
  // onLookDetail(toRaw(currentMemberCard.value));
};

const onChangeCurrentTab = (e) => {
  console.log(e);
  currentTab.value = e;

  if (e === 1) {
    onSearch();
  }
};
const addMemberModalRef = ref(null);
// 编辑会员
const onEditMember = (row) => {
  onGetRegularDetailAxios(row).then((res) => {
    addMemberModalRef.value.onOpen(res);
  });
};

const lookRegularModalRef = ref(null);
const settingInfo = ref(null);
const resData = ref(null); // 用来判定有无数据

const isNetworkError = ref(false);
const isLoading = ref(false);
// const onLookDetail = (row) => {
//   // onGetRegularDetailAxios(row).then((res) => {
//   //   console.log(res);
//   //   lookRegularModalRef.value.onOpen(res);
//   // });
//   resData.value = null;
//   console.log(row);
//   // 缓存信息存储
//   const caches = store.getStorageDatas;
//   const cache = caches.find((v) => v.teamId === getUniTeamID());
//   if (!cache) {
//     isLoading.value = true;

//   }
//   Promise.all([onGetRegularDetailAxios(row), onGetMemberSetting()]).then(
//     (res) => {
//       console.log(res);
//       resData.value = res;
//       isLoading.value = false;

//       isNetworkError.value = false;

//       // 缓存处理 start
//       const memberMeterials = {
//         items: res || [],
//       };
//       if (cache) {
//         cache.memberMeterials = memberMeterials;
//       } else {
//         caches.push({ teamId: getUniTeamID(), memberMeterials });
//       }
//       // 缓存处理end

//       settingInfo.value = res[1];
//       // 这里要座一层逻辑
//       let detailItem: any = res[0];
//       // 激活状态，1：已激活，2：未激活
//       if (detailItem.activate === 2) {
//         // 未激活，读取data数据
//         detailItem.submit_data = lodash.cloneDeep(detailItem.data);
//         console.log(detailItem.data);
//         res[0] = detailItem;
//       }
//       lookRegularModalRef.value?.onClose();
//       setTimeout(() => {
//         lookRegularModalRef.value?.onOpen(res);
//       });
//     }
//   ).catch((error) => {
//     console.log('memberInfo: ', error);
//     isLoading.value = false;

//     if (error === 'Network Error') {
//       isNetworkError.value = true;
//       if (!cache) { resData.value = null; return; }
//       const res = cache.memberMeterials?.items || [];
//       if (res.length < 1) {
//         resData.value = null;
//         return;
//       }
//       resData.value = res;
//       settingInfo.value = res[1];

//       // 这里要座一层逻辑
//       let detailItem: any = res[0];
//       // 激活状态，1：已激活，2：未激活
//       if (detailItem.activate === 2) {
//         // 未激活，读取data数据
//         detailItem.submit_data = lodash.cloneDeep(detailItem.data);
//         console.log(detailItem.data);
//         res[0] = detailItem;
//       }
//       lookRegularModalRef.value?.onClose();
//       setTimeout(() => {
//         lookRegularModalRef.value?.onOpen(res);
//       });

//     }
//   });
// };


const addInMemberModalRef = ref(null);
const onShowMemberFlow = () => {
  console.log('点击了吗');
  addInMemberModalRef.value?.onOpen();
};


const onGetRegularDetailAxios = async (row) => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {

      result = await getRegularDetailAxios(row?.id);
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      resolve(result.data);
    } catch (error) {
      const errMsg = error instanceof Error ? error.message : error;
      // MessagePlugin.error(errMsg);
      reject(errMsg);
    }
  });
};

const onGetMemberCardsAxios = () => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await getMemberCardsAxios({}, currentTeamId.value);
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      resolve(result.data);
    } catch (error) {
      reject();
      const errMsg = error instanceof Error ? error.message : error;
      if (errMsg !== 'Network Error') MessagePlugin.error(errMsg);
    }
  });
};




const memberSettingInfo = ref(null);
const onGetMemberSetting = () => {
  let result = null;
  return new Promise(async(resolve, reject)=> {
    try {
      result = await getMemberSettingAxios({}, currentTeamId.value);
      result = getResponseResult(result);

      if (!result) {
        reject();
        return;
      };
      result = result.data;
      memberSettingInfo.value = result;
      resolve(result)
    } catch (error) {
      const errMsg = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
      reject();
    }
  })
};


const onLiaisonModal = () => {

  if(activeAccount.value?.user_ids?.platformStaff > 0) {

    liaisonModelRef.value?.onOpen({...toRaw(memberSettingInfo.value),
      platformStaff: activeAccount.value?.user_ids?.platformStaff,
      teamId: activeAccount.value?.teamId,
      inCardID: activeAccount.value?.user_ids?.idStaff, // 内部身份

    });

  }
};




const onInitData = () => {
  onGetMemberCardsAxios().then(
    (res: any) => {
      if (res && res.length > 0) {
        memberCards.value = res.map((v) => {
          // v.label = v.type === 1 ? v.team_name : v.name;
          v.label = v.name;
          return v;
        });
        console.log(res);
        if (currentMemberCard.value) {
          console.log(currentMemberCard.value);
          currentMemberCard.value = memberCards.value.find(
            (v) => v.id === currentMemberCard.value?.id
          );
          console.log(currentMemberCard.value);
        } else {
          currentMemberCard.value = res[0];
          console.log(currentMemberCard.value);
        }
        console.log(currentMemberCard.value);
        // currentMemberCard.value = currentMemberCard.value
        //   ? currentMemberCard.value
        //   : res[0];
        // onLookDetail(res[0]);

        // onSearch();
      } else {
        currentMemberCard.value = null;
      }
    },
    (err) => {
      currentMemberCard.value = null;
    }
  );
};

onMountedOrActivated(() => {
  // containerFlets.value?.addEventListener('scroll', handleScroll); // 监听滚动事件
  currentMemberCard.value = null;
  setTimeout(async () => {
    // onCheckIsAdmin(val?.staffId);
    await onInitData();

  });

  setTimeout(() => {

    // if(platformCpt.value === platform.digitalPlatform) {

    // }
    onCheckIsAdmin({...store.value.activeAccount?.user_ids}, currentTeamId.value);
    // onWorkShopAppAxios();
    onActionSquareAxios(); // 用来判定有无本会广场

    onGetMemberSetting().then(() => {

    })
    onGetMemberGroup();
  });
});


const members = ref(0)
// 获取成员列表
const onGetMemberGroup = () => {
  let result = null;
  return new Promise(async(resolve, reject)=> {
    try {
      result = await getMemberAdminAxios({}, currentTeamId.value);
      result = getResponseResult(result);

      if (!result) {
        reject();
        return;
      };
      members.value = result?.data?.total || 0;
      resolve(result)
    } catch (error) {
      const errMsg = error instanceof Error ? error.message : error;
      // MessagePlugin.error(errMsg);
      console.log(errMsg)
      reject();
    }
  })
};

const isShowSquare = ref(false);
const onActionSquareAxios = async (val?) => {
  const params = {
    team_id: currentTeamId.value,
    // open_id: getOpenid(),
  };
  const [err, res] = await to(getSharedSquaresAxios(params));
  if (err) {
    console.log(err?.message);
    return;
  }
  console.log(res);
  const { data } = res;
  // squareInfo.value = data;
  isShowSquare.value = data.opened ? true : false;

};






// watch(
//   () => store.activeAccount,
//   (val) => {
//     if (val) {
//       currentMemberCard.value = null;
//       setTimeout(async () => {
//         // onCheckIsAdmin(val?.staffId);
//         await onInitData();

//       });

//       setTimeout(() => {
//         onCheckIsAdmin(val?.staffId);
//       });
//     }
//   },
//   {
//     deep: true,
//     immediate: true
//   }
// );
</script>

<style lang="less" scoped>
// :deep(.t-avatar-fonts2){
//   font-size: 20px !important;
// }

.home {
  max-width: 1184px;
  min-width: 1088px;
  margin: 0 16px;
  width: 100%;


  .box {

    width: 100%;
    // min-height: calc(100% - 40px);
    height: 100%;
    // background-color: #fff;
    display: flex;
    // align-items: center;
    // justify-content: center;
    // font-size: 20px;
    flex-direction: column;

    .header {
      background: #fff;
      display: flex;
      justify-content: space-between;
      padding-bottom: 10px;

      &-left {
        display: flex;
        gap: 10px;
        .column {
          display: flex;
          flex-direction: column;
          gap: 6px;
          .tag {
            padding: 0 10px;
            border: 1px solid red;
            width: fit-content;
          }
          .name {
            display: flex;
            align-items: center;
            gap: 10px;
            .tip {
              padding: 0 6px;
              border: 1px solid #d6d6d6;
            }
          }
        }
      }
    }
    .body {
      // background: #fff;
      height: 100%;
      .bodyc {
        height: calc(100% - 57px);
      }
    }
  }
}
.combine {
  display: flex;
  gap: 12px;
  .team-manage {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    flex: none;
    width: 136px;
    border-radius: 8px;
    background: linear-gradient(315deg, #A1D7FE -62.86%, #EAF5FF 75.49%);
    .text {
      color: var(--text-kyy-color-text-1, #1A2139);
      text-align: center;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
    }
    .svg {
      width: 40px;
      height: 40px;
    }
  }
}
.team-head {
  flex: 1;
  display: flex;
  width: 100%;
  padding: 24px;
  background: #fff;
  align-items: center;
  background-image: url('@renderer/assets/member/icon/vip_img_bg.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  object-fit: cover;
  height: 136px;

  .logo-box {
    width: 88px;
    height: 88px;
    margin-right: 16px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .info {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    gap: 8px;
    flex: 1 0 0;
    // width: 510px;
    .name {
      color: var(--text-kyy-color-text-1, #1a2139);
      display: flex;
      align-items: center;
      font-family: PingFang SC;
      font-size: 18px;
      font-style: normal;
      font-weight: 600;
      line-height: 26px;
      cursor: pointer;
      .iconarrowdown {
        color: #828da5;
        font-size: 18px;
        margin-left: 2px;
      }
    }

    .dp {
      color: var(--text-kyy-color-text-3, #828da5);

      /* kyy_fontSize_2/regular */
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
      display: flex;
      .tg {
        display: flex;
        height: 20px;
        min-height: 20px;
        max-height: 20px;
        padding: 0px 4px;
        justify-content: center;
        align-items: center;
        gap: 4px;
        border-radius: var(--kyy_radius_tag_s, 4px);
        background: var(--kyy_color_tag_bg_gray, #eceff5);
        // color: var(--kyy_color_tag_text_gray, #516082);
        color: var(--kyy_color_tag_text_brand, #4D5EFF);
        text-align: center;
        font-family: PingFang SC;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px; /* 166.667% */
      }
    }
    .tag1 {
      display: flex;
      height: 24px;
      min-height: 24px;
      max-height: 24px;
      padding: 0px 8px;
      justify-content: center;
      align-items: center;
      gap: 4px;
      color: var(--kyy_color_tag_text_purple, #CA48EB);
      text-align: center;
      border-radius: var(--kyy_radius_tag_s, 4px);
      background: var(--kyy_color_tag_bg_purple, #FAEDFD);
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: 22px; /* 157.143% */
    }
    .tag2 {
      display: flex;
      height: 24px;
      min-height: 24px;
      max-height: 24px;
      padding: 0px 8px;
      justify-content: center;
      align-items: center;
      gap: 4px;
      color: var(--kyy_color_tag_text_purple, #ca48eb);
      text-align: center;
      border-radius: var(--kyy_radius_tag_s, 4px);
      background: var(--kyy_color_tag_bg_purple, #faedfd);
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: 22px; /* 157.143% */
    }
  }
  .btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    gap: 8px;
    // padding-left: 24px;
    padding: 0 10px;

    position: relative;
    transition: all 0.15s linear ;

    &:hover {
      border-radius: 8px;
      background: var(--bg-kyy_color_bgBrand_hover, #EAECFF);
      transition: all 0.15s linear ;
    }
    &::before {
      content: " ";
      position: absolute;
      width: 1px;
      top: 0;
      bottom: 0;
      height: 88px;
      margin: auto;
      background: #fff;
      left: -24px;
    }

    .iconpeopleadd {
      font-size: 24px;
      color: #516082;
    }
    .text {
      color: var(--text-kyy-color-text-1, #1A2139);
      text-align: center;

      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
    }
    .svg {
      width: 32px;
      height: 32px;
    }
  }
  .btn_2 {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: all 0.15s linear ;

    width: 80px;
    height: 80px;
    gap: 3px;
    // padding-left: 24px;
    padding: 0 10px;

    position: relative;
    &:hover {
      border-radius: 8px;
      background: var(--bg-kyy_color_bgBrand_hover, #EAECFF);
      transition: all 0.15s linear ;
    }
    .svg {
      width: 32px;
      height: 32px;
    }


    .iconpeopleadd {
      font-size: 24px;
      color: #516082;
    }
    .text {
      color: var(--text-kyy-color-text-1, #1A2139);
      text-align: center;

      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
    }


  }
}

.entry {
  height: 136px;
}

.list-popup {
  .mer-list {
    display: flex;
    width: 267px;
    background: #fff;
    flex-direction: column;
    // align-items: center;
    .m-item {
      display: flex;
      width: 251px;
      height: 32px;
      padding: 8px 12px;
      align-items: center;
      gap: 12px;
      cursor: pointer;
      .se {
        margin-top: 5px;
      }
      .av {
        // margin: 0 12px;
      }
      .name {
        color: var(--lingke-black-90, #1a2139);

        font-family: PingFang SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
        max-width: 154px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}

.sticky {
  position: sticky;
  top: 0;
  z-index: 10;
  // background: var(--bg-kyy_color_bg_deep, #F5F8FE);
  // padding-bottom: 16px;

}

.tabsTop {
  display: flex;
  height: 56px;
  align-items: center;
  gap: 32px;
  padding: 0 16px;
  &-item {
    color: var(--text-kyy_color_text_1, #1A2139);
    text-align: center;

    /* kyy_fontSize_3/regular */
    font-family: "PingFang SC";
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px; /* 150% */

  }
  .active {
    position: relative;
    color: var(--brand-kyy_color_brand_default, #4D5EFF);

    /* kyy_fontSize_3/bold */
    font-family: "PingFang SC";
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px; /* 150% */

    &::after {
      content: " ";
      position: absolute;
      border-radius: 1.5px;
      bottom: -16px;
      height: 3px;
      width: 16px;
      left: 0;
      right: 0;
      margin: auto;
      background: var(--brand-kyy_color_brand_default, #4D5EFF);
    }
  }
}
.banner {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid var(--divider-kyy_color_divider_light, #ECEFF5);

  .group {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 0 16px;
    &-item {
      height: 32px;
      display: flex;
      align-items: center;
      gap: 4px;
      border: 1px solid var(--color-button_border-kyy_color_buttonBorder_border_dedault, #D5DBE4);
      background: var(--color-button_border-kyy_color_buttonBorder_bg_default, #FFF);
      padding: 0 16px ;
      border-radius: var(--radius-kyy_radius_button_s, 4px);
      border: 1px solid var(--color-button_border-kyy_color_buttonBorder_border_dedault, #D5DBE4);
      .svg {
        width: 20px;
        height: 20px;
      }
      .text {
        width: 56px;
        color: var(--color-button_border-kyy_color_buttonBorder_text_default, #516082);
        text-align: center;
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 22px; /* 157.143% */
      }
      .t-badge {
        display: flex;
        align-items: center;
        gap: 4px;
      }
    }
  }
}


.act-1 {
  position: absolute;
  bottom: 0px;
  left: 46px;
}
.act-2 {
  position: absolute;
  bottom: 0px;
  left: 139px;
}

.comp-box {
  width: 100%;
  background: #fff;
  margin-bottom: 24px;
  .regular {
    padding: 0 24px;
  }
  .edit-btn {
    position: fixed;
    left: 0;
    right: 0;

    bottom: 0px;
    z-index: 9;
    background-color: #fff;

    display: flex;
    height: 64px;
    padding: 16px 24px;
    justify-content: left;
    align-items: center;
    align-self: stretch;
    border-top: 1px solid var(--divider-kyy-color-divider-light, #eceff5);

    .cen {
      width: 1110px;
      margin: 0 auto;
      .b {
        // margin-left: 40%;
      }
    }

  }
  .btm24 {
    height: 24px;
    width: 100%;
    background: var(--kyy_color_tabbar_bg, #ebf1fc);
    color: #ebf1fc;
  }
}

.contact-box {
  width: 100%;
  background: #fff;
}

:deep(.t-badge--circle) {
  background-color: var(--kyy_color_badge_bg, #FF4AA1);
}



.backTop {
  position: fixed;
  right: 16px;
  bottom: 32px;
  opacity: 0;
  transition:all 0.25s linear;

  z-index: 99;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  width: 64px;
  height: 64px;
  border: 1px solid var(--border-kyy_color_border_default, #D5DBE4);
  background: var(--divider-kyy_color_divider_deep, #D5DBE4);
  .iconarrowup {
    font-size: 30px;
    color: #1A2139;
  }
  .text {
    color: var(--text-kyy_color_text_1, #1A2139);
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px; /* 166.667% */
  }
}
.isOpacity {
  opacity: 1 !important;
  transition:all 0.25s linear;
}






.footerV {
  bottom: 84px !important;
}
.footer {
  position: fixed;
  right: 0;
  left: 0;
  z-index:100;
  margin: auto;
  bottom: 32px;
  max-width: 1320px;
  display: flex;
  justify-content: flex-end;
}
.backTopContact {


  opacity: 0;
  transition:all 0.25s linear;
  margin-right: calc((100% - 1160px) / 2);
  position: fixed;
  right: 0;
  bottom: 84px;
  z-index:100;

  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  // width: 48px;
  // height: 48px;
  border-radius: 50%;
  // background: var(--icon-kyy_color_icon_transparent, rgba(26, 33, 57, 0.36));
  .iconarrowup {
    // font-size: 20px;
    // color: #1A2139;
    width: 32px;
    // height: 24px;
  }
  .text {
    color: var(--text-kyy_color_text_1, #1A2139);
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px; /* 166.667% */
  }
}

@media screen and (min-width: 1270px) {
  .backTopContact {
     margin-right: calc((100% - 1270px) / 2);
  }
}
:deep(.t-badge--circle){
  
  padding-right: calc((16px - 8px) / 2);
  padding-left: calc((16px - 8px) / 2);
  min-width: 8px;
  height: 16px;
  background-color: var(--td-error-color);
  line-height: 16px;
}
</style>
