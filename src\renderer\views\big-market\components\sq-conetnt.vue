<template>
  <div class="sqpage">
    <div class="cont-box">
      <div class="conetnt">
        <skeleton v-if="skeletonShow" />
        <div v-if="props.sqData.length && !skeletonShow" class="list-box">
          <template v-for="item in props.sqData" :key="item.id">
            <t-popup trigger="hover" placement="right" :overlayClassName="`sq-card-popup popup-tagid-${item.id}`">
              <template #content>
                <SquareDetailCard :data="item as RingkolMarketMarketSquare" @image-click="imgClick" />
              </template>

              <SquareItemCard
                :data="item as RingkolMarketMarketSquare"
                :is-follow-loading="follId === item.id"
                @card-click="cardJump"
                ref="cardRef"
                @follow-click="followRun"
              />
            </t-popup>
          </template>
        </div>
        <Empty
          v-if="!props.sqData.length && !skeletonShow"
          :name="keyword ? 'no-result' : 'no-data-new'"
          :tip="keyword ? '搜索无结果' : '暂无数据'"
        />
        <loading v-if="props.loadingStatus" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, nextTick, onActivated, onMounted, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { useNicheStore } from '@renderer/store/modules/niche';
import skeleton from '@/views/big-market/components/skeleton.vue';
import nores from '@/views/big-market/components/nores.vue';
import { clickReport, exposureReport, marketListGet, searchClassify } from '@/views/big-market/apis';
import loading from '@/views/big-market/components/loading.vue';
import LynkerSDK from '@renderer/_jssdk';
const { ipcRenderer } = LynkerSDK;
import { followToggle } from '@/api/square/fans';
import to from 'await-to-js';
import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next';
import Empty from '@renderer/components/common/Empty.vue';
import { toSquareHome } from '@/views/square/utils/ipcHelper';
import SquareDetailCard from '@/views/big-market/components/SquareDetailCard.vue';
import SquareItemCard from '@/views/big-market/components/SquareItemCard.vue';
import type { RingkolMarketMarketSquare } from '@/views/big-market/types';

const props = defineProps({
  sqData: {
    type: Array,
    default: [],
  },
  skeletonShow: {
    type: Boolean,
    default: true,
  },
  loadingStatus: {
    type: Boolean,
    default: false,
  },
  keyword: {
    type: String,
    default: '',
  },
  scrollContainer: {
    type: Object,
    default: null,
  },
});

const { t, locale } = useI18n();
const follId = ref(null);

const nicheStore = useNicheStore();
const supplyData = ref([]);
const total = ref(0);
const nicheClassId = ref(0);

onActivated(() => {});
onMounted(() => {
  initIntersectionObserver();
});

const initIntersectionObserver = () => {
  if (!props.scrollContainer) return;
  const pendingItems = [];

  const observer = new IntersectionObserver(
    (entries) => {
      const visibleItems = [];
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const item = entry.target.__itemData;
          if (item) {
            visibleItems.push(item);
            observer.unobserve(entry.target);
          }
        }
      });
      if (visibleItems.length > 0) {
        const params = {
          square: visibleItems.map((item) => ({ squareId: item.id, isAd: item.isAd })),
        };
        exposureReportRun(params);
      }
    },
    {
      root: props.scrollContainer,
      threshold: 1.0,
    },
  );

  nextTick(() => {
    document.querySelectorAll('.square-item-card').forEach((card, index) => {
      (card as any).__itemData = props.sqData[index];
      observer.observe(card);
    });
  });
};

watch(
  () => props.sqData,
  () => {
    initIntersectionObserver();
  },
  { deep: true },
);

const emit = defineEmits(['homeinit', 'up-title']);

defineExpose({});

const fixdHeaderShow = ref(false);
const onScroll = (e) => {
  if (e.target.scrollTop >= 30) {
    fixdHeaderShow.value = true;
  } else {
    fixdHeaderShow.value = false;
  }
};

const cardJump = (item) => {
  console.log(item);
  if (item.externalApp && item.externalApp.type) {
    let url = '';
    switch (item.externalApp.type) {
      case 'app':
        url = item?.externalApp?.shareLink;
        break;
      case 'h5':
        url = item?.externalApp?.desktopLink || item?.externalApp?.h5Link;
        break;
      case 'wechat_official':
        url = item?.externalApp?.articleLink;
        break;
      case 'mini_program':
        url = '';
        break;
      case 'wechat_mini':
      default:
        url = '';
    }
    if (url) {
      LynkerSDK.openExternalApp({
        url,
      });
    } else {
      MessagePlugin.error('该应用暂无分享链接');
    }
  }
  const params = {
    square: {
      squareId: item.id,
      isAd: item.isAd,
    },
  };
  clickReport(params).then((res) => {
    console.log(res);
  });
  // return;
  const etype = item.redirectPageType?.split('REDIRECT_PAGE_')[1];
  console.log('跳转广场参数', item.id, { entryType: etype });
  toSquareHome(item.id, { entryType: etype, externalAppId: item?.externalApp?.appId });
};
const exposureReportRun = async (params) => {
  exposureReport(params).then((res) => {
    console.log(res);
  });
};

const imgClick = (imgIndex, imgs) => {
  console.log(imgIndex, imgs);
  const temp = imgs.map((item) => ({
    title: item,
    url: item,
    type: 'png',
    size: 1024,
    officeId: null,
    imgIndex: imgIndex,
  }));
  ipcRenderer.invoke('view-img', JSON.stringify(temp));
};
const calFollow = (item) => {
  const myDialog = DialogPlugin({
    header: t('niche.tip'),
    theme: 'info',
    body: `你确定不再关注[${item.name}]？`,
    className: 'dialog-classp32',
    onConfirm: () => {
      myDialog.hide();
      followToggleReq(item);
    },
    onCancel: () => {
      myDialog.hide();
    },
  });
};
const cardRef = ref<HTMLElement>();
const followRun = async (item) => {
  if (item.followed) {
    //取消关注
    const cla = `popup-tagid-${item.id}`;
    const popupElement = document.querySelector(`.${cla}`);
    if (popupElement) {
      popupElement.style.display = 'none';
    }
    setTimeout(() => {
      calFollow(item);
    }, 10);
  } else {
    followToggleReq(item);
  }
};
const followToggleReq = async (item) => {
  follId.value = item.id;
  const [err] = await to(followToggle({ square_id: item.id, follow: !item.isFollow }, { teamId: -1 }));
  if (err) {
    console.log(err);
    return;
  }
  item.followed = !item.followed;
  follId.value = null;
  MessagePlugin.success(item.followed ? '关注成功' : '取关成功');
};
</script>

<style lang="less" scoped>
.sqpage {
  display: flex;
  width: 100%;
  flex-direction: column;
  align-items: center;
  background-position: center;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  min-height: 450px;

  .fixd-header-box {
    width: 100%;
    margin: 0 auto;
    background: #fff;
    z-index: 1000;

    .fixd-header {
      display: flex;
      width: 1216px;
      gap: 16px;
      background: #fff;
      padding: 16px 24px;
      align-items: center;
      margin: 0 auto;

      .logo-box {
        display: flex;
        align-items: center;
        gap: 12px;
        min-width: 140px;
        cursor: pointer;

        .logo {
          width: 32px;
          height: 32px;
        }

        .font_cn {
          width: 89px;
          height: 40px;
        }

        .name {
          color: var(--text-kyy_color_text_2, #516082);
          font-family: YouSheBiaoTiHei;
          font-size: 32px;
          font-style: normal;
          font-weight: 400;
          line-height: 40px;
          /* 125% */
        }
      }

      .line {
        display: flex;
        height: 24px;
        min-width: 1px;
        max-width: 1px;
        align-items: flex-start;
        gap: 4px;
        background: var(--divider-kyy_color_divider_deep, #d5dbe4);
      }

      .search-bar {
        display: flex;
        width: 424px;
        height: 32px;
        padding: 0px 2px 0px 7px;
        align-items: center;
        gap: 12px;
        flex-shrink: 0;
        border-radius: 4px;
        border: 2px solid var(--brand-kyy_color_brand_hover, #707eff);
        background: var(--input-kyy_color_input_bg_default, #fff);

        .search-btn {
          width: 48px;
          height: 24px;
          flex-shrink: 0;
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;
          border-radius: var(--radius-kyy_radius_button_s, 4px);
          background: var(--brand-kyy_color_brand_hover, #707eff);

          .iconsearch {
            font-size: 20px;
            color: #fff;
          }
        }
      }
    }
  }

  .cont-box::-webkit-scrollbar {
    width: 0px;
  }

  .cont-box {
    width: 100%;
    .conetnt {
      width: 100%;
      .types {
        display: flex;
        height: 32px;
        align-items: center;
        gap: 16px;
        margin-bottom: 12px;
        flex-wrap: wrap;
        .type-item-clay {
          display: flex;
          padding: 4px 16px;
          justify-content: center;
          align-items: center;
          gap: 10px;
          cursor: pointer;
          border-radius: 16px;
          background: var(--bg-kyy_color_bg_light, #fff);
          color: var(--text-kyy_color_text_1, #1a2139);
          text-align: center;
          font-family: 'PingFang SC';
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px;
        }
        .type-item-clay:hover {
          border-radius: 16px;
          color: var(--icon-kyy_color_icon_hover, #707eff);
          background: var(--bg-kyy_color_bgBrand_hover, #eaecff);
        }
        .typeItemAct {
          border-radius: 16px;
          background: var(--bg-kyy_color_bgBrand_foucs, #dbdfff);
          color: var(--icon-kyy_color_icon_active, #4d5eff);
          text-align: center;
          font-family: 'PingFang SC';
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px;
        }
        .more-b {
          display: flex;
          padding: 4px 8px 4px 12px;
          justify-content: center;
          align-items: center;
          gap: 4px;
          color: var(--text-kyy_color_text_2, #516082);
          text-align: center;
          border-radius: 16px;
          background: #fff;
          font-family: 'PingFang SC';
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px;
          cursor: pointer;
          position: relative;
          .iconarrowdwon {
            font-size: 20px;
          }
          .more-tag-box::-webkit-scrollbar {
            width: 4px;
          }
          .more-tag-box {
            display: inline-flex;
            padding: 4px;
            flex-direction: column;
            align-items: center;
            gap: 2px;
            position: absolute;
            right: 0px;
            top: 34px;
            border-radius: var(--kyy_radius_dropdown_m, 8px);
            background: var(--kyy_color_dropdown_bg_default, #fff);
            box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.12);
            z-index: 100;
            max-height: 278px;
            overflow-y: auto;
            .tag-li {
              display: flex;
              height: 32px;
              min-width: 136px;
              min-height: 32px;
              max-height: 32px;
              padding: 0px 8px;
              align-items: center;
              gap: 12px;
              align-self: stretch;
              color: var(--kyy_color_dropdown_text_default, #1a2139);
              font-family: 'PingFang SC';
              font-size: 14px;
              font-style: normal;
              font-weight: 400;
              line-height: 22px;
            }
            .tag-li:hover {
              border-radius: var(--kyy_radius_dropdown_s, 4px);
              background: var(--kyy_color_dropdown_bg_hover, #f3f6fa);
            }
            .tagLA {
              border-radius: var(--kyy_radius_dropdown_s, 4px);
              background: var(--kyy_color_dropdown_bg_active, #e1eaff);
              color: var(--kyy_color_dropdown_text_active, #4d5eff);
              font-family: 'PingFang SC';
              font-size: 14px;
              font-style: normal;
              font-weight: 400;
              line-height: 22px;
            }
          }
        }
        .more-b:hover {
          .iconarrowdwon {
            font-size: 20px;
            color: var(--icon-kyy_color_icon_hover, #707eff);
          }
          color: var(--icon-kyy_color_icon_hover, #707eff);
          border-radius: 16px;
          background: var(--bg-kyy_color_bgBrand_foucs, #eaecff);
        }
        .moreBA {
          .iconarrowdwon {
            font-size: 20px;
            color: var(--icon-kyy_color_icon_hover, #707eff);
          }
          color: var(--icon-kyy_color_icon_hover, #707eff);
          border-radius: 16px;
          background: var(--bg-kyy_color_bgBrand_foucs, #eaecff);
        }
        .moreBC {
          .iconarrowdwon {
            font-size: 20px;
            color: var(--icon-kyy_color_icon_hover, #4d5eff);
          }
          color: var(--icon-kyy_color_icon_hover, #4d5eff);
          border-radius: 16px;
          background: var(--bg-kyy_color_bgBrand_foucs, #dbdfff);
        }
      }
      .list-box {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        gap: 12px;
        column-gap: 12px;
        margin-bottom: 32px;
      }
    }
  }
}

.fontWeight6 {
  font-weight: 600 !important;
}

:deep(.t-back-top) {
  background: var(--bg-kyy_color_bg_mask, rgba(0, 0, 0, 0.5));
  border: none;
  box-shadow: none;
}

.headerboxshow {
  box-shadow: 0px 3px 8px 0px rgba(0, 0, 0, 0.12);
}

:global(.sq-card-popup .t-popup__content) {
  padding: 12px 3px 12px 12px;
  border-radius: 12px;
  border: 1px solid #bee2ff;
  background: linear-gradient(180deg, rgba(227, 249, 254, 0.5) 0%, rgba(245, 250, 253, 0.5) 100%), #fff;
  box-shadow: 0px 4px 8px 0px rgba(0, 7, 66, 0.16);
}
</style>
