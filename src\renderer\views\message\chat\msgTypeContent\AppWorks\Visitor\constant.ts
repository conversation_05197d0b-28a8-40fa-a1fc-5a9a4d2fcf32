import { i18nt } from '@renderer/i18n';

/**
 * 场景类型枚举
 */
export enum SceneType {
  /**
   * 入会申请
   */
  VISIT_APPLY_MEMBER = 5060,

  /**
   * 入会通过
   */
  VISIT_AUTO_PASS_MEMBER = 5061,
  /**
   * 入会申请
   */
  VISIT_APPLY_POLITICS = 14030,

  /**
   * 入会通过
   */
  VISIT_AUTO_PASS_POLITICS = 14031,
  /**
   * 入会申请
   */
  VISIT_APPLY_CBD = 16030,

  /**
   * 入会通过
   */
  VISIT_AUTO_PASS_CBD = 16031,
  /**
   * 入会申请
   */
  VISIT_APPLY_ASSOCIATION = 19030,
  /**
   * 入会申请
   */
  APPLY = 19030,
  /**
   * 入会通过
   */
  VISIT_AUTO_PASS_ASSOCIATION = 19031,
  /**
   * 入会通过
   */
  VISIT_APPLY_UNI= 51030,
    /**
   * 入会申请
   */
  VISIT_AUTO_PASS_UNI = 51031
}

/**
 * 展示团队logo和标题
 */
export const SHOW_DETAIL_BTN_SCENES = [
  SceneType.VISIT_APPLY_MEMBER,
  SceneType.VISIT_APPLY_POLITICS,
  SceneType.VISIT_APPLY_CBD,
  SceneType.VISIT_APPLY_ASSOCIATION,
  SceneType.VISIT_APPLY_UNI,

];
/**
 * 展示团队logo和标题
 */
export const titleMap = {
  [SceneType.VISIT_APPLY_MEMBER]: i18nt('im.public.biz'),
  [SceneType.VISIT_AUTO_PASS_MEMBER]: i18nt('im.public.biz'),
  [SceneType.VISIT_APPLY_POLITICS]: i18nt('im.public.government'),
  [SceneType.VISIT_AUTO_PASS_POLITICS]: i18nt('im.public.government'),
  [SceneType.VISIT_APPLY_CBD]: i18nt('application.digital_cbd'),
  [SceneType.VISIT_AUTO_PASS_CBD]: i18nt('application.digital_cbd'),
  [SceneType.VISIT_APPLY_ASSOCIATION]: i18nt('niche.szsq'),
  [SceneType.VISIT_AUTO_PASS_ASSOCIATION]: i18nt('niche.szsq'),
  [SceneType.VISIT_APPLY_UNI]: i18nt('niche.szgx'),
  [SceneType.VISIT_AUTO_PASS_UNI]: i18nt('niche.szgx'),
};

export const getBtnTxt = (status) => {
  const titleMap = {
    3: i18nt('ebook.vrefuse'),
    2: i18nt('ebook.vtype3'),
    5: i18nt('ebook.vtype6'),
  };
  return titleMap[status] || null;
};

export const SceneTypeArr = Object.values(SceneType).filter((value) => typeof value === 'number');
