<script setup lang="ts">
import { computed, ref } from 'vue';
import OrgAuth from '@renderer/components/orgAuth/orgAuth.vue';
import { onMountedOrActivated } from '@renderer/hooks/onMountedOrActivated';
import { useRoute, useRouter } from 'vue-router';
import ApplyInfo from './ApplyInfo.vue';
import { useTeamAuthStatus } from '../hooks/useMerchant';
import useMerchantFlow from '../hooks/useMerchantFlow';
import WechatActualNameDialog from './WechatActualNameDialog.vue';
import { useDebug } from '../hooks/useDebug';
import { useWorkBenchNavigate } from '../../hooks/useNavigate';

interface Props {
  /** 团队信息 */
  team?: Record<string, any>;
  /** 是否为分佣模式 */
  isCommission?: boolean;
  /** 是否显示微信实名认证按钮 */
  showWechatRealname?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  team: () => ({}),
  isCommission: false,
  showWechatRealname: false,
});

const route = useRoute();
const router = useRouter();
const teamId = computed(() => route.query?.teamId || props.team?.teamId || '');

// 组织认证
const { orgAuthDetail, orgAuthVisible, fetchTeamAuthStatus, loading: authLoading } = useTeamAuthStatus(teamId);
const orgAuthShowType = computed(() => (orgAuthDetail.value?.auth === 0 ? 'edit' : 'detail'));

// 入网流程状态流转
const { handleApplyClickFlow, isMerchantFlowReady, isCommissionFlowReady, initMerchantFlow } = useMerchantFlow();
const { navigateToMerchantApply } = useWorkBenchNavigate();

// 根据模式选择对应的流程状态
const isFlowReady = computed(() => (props.isCommission ? isCommissionFlowReady.value : isMerchantFlowReady.value));

// 提示文案
const tipConfig = computed(() => {
  if (props.isCommission) {
    return {
      title: '入网分佣',
      description: '需先开通商户入网以及商户分佣业务后，才获取佣金',
    };
  }
  return {
    title: '入网分账',
    description: '需先开通商户入网以及商户分账业务后，才可进行交易收款',
  };
});

// 申请处理
const handleApplyClick = async () => {
  const params: any = {
    teamId: teamId.value,
    fetchTeamAuthStatus,
    orgAuthVisible,
    loading: authLoading,
  };

  // 分佣模式需要传递 isCommission 参数
  if (props.isCommission) {
    params.isCommission = 'true';
  }

  await handleApplyClickFlow(params);
};

// 微信聚合支付实名认证弹窗状态
const isWechatDialogVisible = ref(false);

useDebug(undefined, {
  onCtrlD: () => {
    navigateToMerchantApply({ query: { teamId: props.team?.teamId, isCommission: String(props.isCommission) } });
  },
});

onMountedOrActivated(() => {
  initMerchantFlow(teamId);

  // 处理 IM 场景下的自动申请（仅限非分佣模式）
  if (!props.isCommission && route.query?.from === 'imNaas') {
    setTimeout(() => {
      // 清除from参数并保留其他查询参数
      router.replace({
        query: {
          ...route.query,
          from: '',
        },
      });
      handleApplyClick();
    }, 300);
  }
});
</script>

<template>
  <div class="page-wrap">
    <!-- 空状态页面 -->
    <div v-if="!isFlowReady" class="page-empty">
      <img src="@/assets/workbench/accounts_img.svg" alt="" class="w-320 h-264">
      <div class="tip-wrap">
        <p class="title">{{ tipConfig.title }}</p>
        <p>{{ tipConfig.description }}</p>
      </div>

      <t-button
        theme="primary"
        class="mt-12 min-w-80"
        :loading="authLoading"
        @click="handleApplyClick"
      >
        申请
      </t-button>
    </div>

    <!-- 流程成功状态 -->
    <div v-else class="py-16 pl-24 h-full">
      <!-- 微信聚合支付实名认证按钮（仅分账模式显示） -->
      <t-button
        v-if="showWechatRealname"
        theme="primary"
        variant="outline"
        size="small"
        shape="round"
        class="btn-realname"
        @click="isWechatDialogVisible = true"
      >
        微信聚合支付实名认证
      </t-button>

      <ApplyInfo :team-id="teamId" />
    </div>

    <!-- 组织认证弹窗 -->
    <OrgAuth
      v-if="orgAuthVisible"
      v-model:visible="orgAuthVisible"
      :show-type="orgAuthShowType"
      :region="orgAuthDetail.region"
      :team-id="orgAuthDetail.teamId"
      :org-type="orgAuthDetail.orgType"
      @success="fetchTeamAuthStatus"
    />

    <!-- 微信聚合支付实名认证弹窗 -->
    <WechatActualNameDialog
      v-if="showWechatRealname"
      v-model:visible="isWechatDialogVisible"
      source="settlement-join"
    />
  </div>
</template>

<style lang="less" scoped>
.page-wrap {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  border-radius: 8px;
  background: var(--bg-kyy_color_bg_light, #FFF);
  border: 1px solid var(--divider-kyy_color_divider_light, #ECEFF5);
  flex-direction: column;
  overflow: hidden;
}

.page-empty {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.tip-wrap {
  margin-top: 32px;
  display: flex;
  flex-direction: column;
  gap: 4px;
  color: var(--text-kyy_color_text_2, #516082);
  text-align: center;
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;

  .title {
    color: var(--text-kyy_color_text_1, #1A2139);
    font-weight: 600;
  }
}

.btn-realname {
  position: absolute;
  top: 16px;
  right: 24px;
  padding: 0 12px;
  z-index: 1;

  :deep(.t-button__text) {
    color: var(--color-button_border-kyy_color_buttonBorder_text_brand, #4D5EFF);
    font-size: 12px;
    font-weight: 400;
    line-height: 20px;
  }
}
</style>
