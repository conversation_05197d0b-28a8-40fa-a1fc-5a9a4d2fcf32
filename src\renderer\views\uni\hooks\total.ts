import {
  getMemberActivateTotalAxios,
  getMemberApplyTotalAxios
} from "@renderer/api/uni/api/businessApi";
import { getResponseResult } from "@renderer/utils/myUtils";
import { MessagePlugin } from "tdesign-vue-next";
import { useUniStore } from "@renderer/views/uni/store/uni";

const store = useUniStore();
export const onGetMemberActivateTotalAxios = async (teamId?) => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await getMemberActivateTotalAxios({}, teamId);
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      store.setActiveTotals(result.data);
      resolve(result.data);
    } catch (error) {
      const errMsg = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
      reject();
    }
  });
};
// 入会申请数量
export const onGetMemberApplyTotalAxios = async (teamId?) => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await getMemberApplyTotalAxios({}, teamId);
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      store.setApplyTotals(result.data);
      resolve(result.data);
    } catch (error) {
      const errMsg = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
      reject();
    }
  });
};

export const updateAllCount = (teamId?) =>
  // eslint-disable-next-line no-async-promise-executor
  new Promise<any>(async (resolve, reject) => {
    try {
      const res1:any = await onGetMemberActivateTotalAxios(teamId);
      const res2: any = await onGetMemberApplyTotalAxios(teamId);
      const result: any[] = [res1, res2];
      resolve(result);
      console.log('resolve: updateAllCount')

    } catch (error) {
      console.log('error: updateAllCount')
      reject(error)
    }
  });
