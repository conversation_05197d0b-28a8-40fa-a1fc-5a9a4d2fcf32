<template>
  <t-dialog class="stop-ad-dialog" v-model:visible="visible" :close-btn="false" @close="visible = false,editFormData.reason=''" :header="true" width="512">
    <template #header>
      <div style="display: flex; align-items: center; width: 100%; justify-content: space-between">
        <div>{{t('ad.confirmTerminate')}}?</div>
        <img
          style="width: 24px; cursor: pointer; height: 24px"
          src="@/assets/<EMAIL>"
          @click="visible = false,editFormData.reason=''"
        />
      </div>
    </template>
    <div class="content-box">
      <t-form labelAlign="top" ref="editForm" :data="editFormData">
        <t-form-item :label="t('ad.bz')"  style="margin-bottom: 24px">
            <template #label>
                  <div class="diy-lable">{{t('ad.terminationReason')}}</div>
              </template>
          <t-textarea  :autosize="{ minRows: 5, maxRows: 6 }"  v-model="editFormData.reason" :placeholder="t('ad.qsr')" :maxlength="200"></t-textarea>
        </t-form-item>
      </t-form>
    </div>
    <template #footer>
      <div class="footer">
        <t-button theme="default" variant="outline" @click="(visible = false), (editFormData.reason = '')">
          取消
        </t-button>
        <t-button :disabled="!editFormData.reason" theme="primary" @click="onSave"> {{t('ad.qd')}} </t-button>
      </div>
    </template>
  </t-dialog>
</template>
<script setup lang="ts">
import { admanageadstop } from "@renderer/api/member/api/ebookApi";

import { ref, onMounted } from "vue";
import { DialogPlugin, MessagePlugin } from "tdesign-vue-next";
import { useI18n } from "vue-i18n";

const editFormData = ref({
  reason: "",
});
const { t } = useI18n();
const cropperRef = ref(null);
const teamId = ref('');
const visible = ref(false);
const ad_id = ref('');
const emits = defineEmits(["callBack"]);
const onSave = () => {
  admanageadstop(
    {
      ad_id: ad_id.value,
      reason: editFormData.value.reason,
    },
    teamId.value,
  ).then((res) => {

      console.log(res, "resssssssssss");
      MessagePlugin.success('操作成功');

    })
    .catch((err) => {
      MessagePlugin.error(err.message);
    }).finally(() => {
      editFormData.value.reason = "";
      visible.value = false;

      emits("callBack");
    });
};
const openWin = (id, val) => {
  console.log(id,111111111111);
  console.log(teamId,111111111111);
  ad_id.value = id;
  teamId.value = val;
  visible.value = true;
};
defineExpose({
  openWin,
});
</script>

<style lang="less" scoped>
.stop-ad-dialog .t-dialog__body {
    padding: 0 !important;
}
.diy-lable {
  color: var(--text-kyy_color_text_3, #828da5);
  font-size: 14px;
  font-style: normal;
  margin-top: 24px;
  font-weight: 400;
  padding-left: 10px;
  position: relative;
  line-height: 22px;
}
.diy-lable::after {
  content: "*";
  position: absolute;
  top: 0;
  left: 0;
  color: var(--error-kyy_color_error_default, #d54941);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
}
:deep(.t-textarea__limit) {
  display: block !important;
  position: absolute;
  bottom: 0;
  right: 4px;
}
</style>
