<template>
  <div class="AdDetails">
    <div class="banner" :class="
        infoData?.status !== 3 &&
        infoData?.status !== 4 &&
        infoData?.status !== 7 &&
        infoData?.status !== 8 &&
        infoData?.status !== 5
          ? ''
          : 'mt0'
      ">
      <div class="headBox">
        <img class="img0" :src="infoData?.image_url" @click="openImg(infoData?.image_url)" />
        <div class="headInfo">
          <div class="headInfoTitle">
            <span class="tags" :style="{
                color: tagList.find((e) => e.value === infoData?.status)?.color,
                background: tagList.find((e) => e.value === infoData?.status)?.background,
              }">{{ tagList.find((e) => e.value === infoData?.status)?.lable }}</span>
            {{ infoData?.title }}
          </div>
          <div class="headInfoDis" style="margin-bottom: 12px">
            <img class="icons" style="width: 16px; height: 16px" src="@/assets/member/rl.svg" />
            <div class="lable-rq" style="width: 70px">{{ t("ad.tfrq") }}：</div>
            <div class="value-rq">{{ infoData?.begin_at }}~{{ infoData?.end_at }}(共{{ infoData?.day_count }}天)</div>
          </div>
          <div class="headInfoDis" style="margin-bottom: 12px">
            <img class="icons" style="width: 16px; height: 16px" src="@/assets/member/pay.svg" />
            <div class="lable-rq" style="width: 42px">{{ t("ad.fy") }}</div>
            <div class="value-rq">{{ infoData?.symbol === "¥" ? "¥" : "MOP" }}{{ infoData?.price_text }}</div>
            <div class="mx" @click="openMx(infoData?.id)">{{ t("ad.details") }}</div>
          </div>
        </div>
      </div>
      <div class="headBox" style="flex-wrap: wrap" v-if="infoData?.status === 4">
        <div class="tabLab">
          <span>{{ t("ad.terminationReason") }} </span>
          <span class="end-time-class">（{{ t("ad.zzsj") }}：{{ infoData?.stop_at }}）</span>
        </div>
        <div class="formDataView">
          <div class="formFlexItem" style="width: 100%">
            <div class="value-rq">{{ infoData?.stop_reason }}</div>
          </div>
        </div>
      </div>
      <div class="headBox" style="flex-wrap: wrap" v-if="infoData?.status === 7">
        <div class="tabLab">
          <span>{{ t("ad.btgyy") }} </span>
          <span class="end-time-class">（{{ t("ad.shsj") }}：{{ infoData?.review_at }}）</span>
        </div>
        <div class="formDataView">
          <div class="formFlexItem" style="width: 100%">
            <div class="value-rq">{{ infoData?.review_reason }}</div>
          </div>
        </div>
      </div>
      <div class="headBox" style="flex-wrap: wrap" v-if="[2, 3, 4].includes(infoData?.status)">
        <div class="tabLab">{{ t("ad.tfsj") }}</div>
        <div class="numBox">
          <div class="num-tf click3">
            <div class="num">{{ infoData?.show_count || 0 }}</div>
            <div class="lables">{{ t("ad.zss") }}</div>
          </div>
          <div class="num-tf click2">
            <div class="num">{{ infoData?.click_count || 0 }}</div>
            <div class="lables">{{ t("ad.djs") }}</div>
          </div>
          <div class="num-tf click1">
            <div class="num">{{ infoData?.percent || 0 }}</div>
            <div class="lables">{{ t("ad.djl") }}</div>
          </div>
        </div>
      </div>
      <div class="headBox" style="flex-wrap: wrap">
        <div class="tabLab">{{ t("ad.jcxx") }}</div>
        <div class="formDataView">
          <div class="formFlexItem">
            <div class="lable-rq">{{ t("ad.ggid") }}</div>
            <div class="value-rq">{{ infoData?.uuid }}</div>
          </div>
          <div class="formFlexItem">
            <div class="lable-rq">{{ t("ad.placementEndpoint") }}</div>
            <div class="value-rq">{{ infoData?.place_platform }}</div>
          </div>
          <div class="formFlexItem">
            <div class="lable-rq">{{ t("ad.adPosition") }}</div>
            <!-- 1PC轮播 2右上 3右下 4APP轮播 -->
            <div class="value-rq">{{ infoData?.place_name }}</div>
          </div>
          <div class="formFlexItem">
            <div class="lable-rq">{{ t("ad.cjsj") }}</div>
            <div class="value-rq">{{ infoData?.created_at }}</div>
          </div>
          <div class="formFlexItem">
            <div class="lable-rq">{{ t("ad.tfzt") }}</div>
            <div class="value-rq" style="float: left; line-height: 24px">
              <span class="tags" :class="infoData?.main_body_type === 1 ? 'pt' : 'pthy'">{{
                infoData?.main_body_type === 1 ? "平台" : "平台成员"
                }}</span>
              {{ infoData?.main_body_name }}
            </div>
          </div>
          <div class="formFlexItem">
            <div class="lable-rq">{{ t("ad.redirectType") }}</div>
            <div class="value-rq">
              {{
              infoData?.skip_type === 5
              ? "商品":
              infoData?.skip_type === 1
              ? "商机"
              : infoData?.skip_type === 2
              ? "广场号"
              : infoData?.skip_type === 3
              ? "自定义链接"
              : "无跳转"
              }}
            </div>
          </div>
          <div class="formFlexItem" style="width: 100%" v-if="infoData?.skip_type === 3">
            <div class="lable-rq">{{ t("ad.tzlj") }}</div>
            <div class="value-rq links">
              <span class="copy-link">{{ infoData?.skip_param }}</span>
              <iconpark-icon @click="copyLink(infoData?.skip_param)" class="copyicon" name="iconcopy"></iconpark-icon>
            </div>
          </div>
          <div class="formFlexItem" style="width: 100%" v-if="infoData?.skip_type === 5&&shopData"
            @click="onOpenRich(infoData?.niche_data)">
            <div class="lable-rq">跳转商品</div>
            <div class="ad-shop-box" v-if="shopData &&shopData?.imgUrls[0]">
              <div class="ad-shop-box-left">
                <img :src="shopData?.imgUrls[0]" />
              </div>
              <div class="ad-shop-info-box">
                <div class="ad-shop-box-info">
                  <div class="ad-shop-box-info-title">
                    {{shopData?.title}}
                  </div>
                  <div class="flexbox">
                    <div class="lsx-tags">{{ shopData?.typ ==='PRODUCT_TYPE_RETAIL' ? "零售型" : "核销型" }}</div>
                    <div
                    :class="shopData?.promotionViewState !== 'PROMOTION_VIEW_STATE_SALE'?'lsx-tags-status':'lsx-tags-status1'">{{
                    shopData?.promotionViewState === 'PROMOTION_VIEW_STATE_SALE' ? "已售卖" : "部分售罄" }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="formFlexItem" style="width: 100%" v-if="infoData?.skip_type === 1"
            @click="onOpenRich(infoData?.niche_data)">
            <div class="lable-rq">{{ t("ad.redirectOpportunity") }}</div>
            <!-- <div class="value-rq">XXXXXXXXXXXXXXX</div> -->
            <div class="sjbox" v-if="infoData && infoData.niche_data">
              <div class="sjbox-left">
                <img :src="infoData.niche_data?.images[0]?.file_name" />
              </div>
              <div class="sjbox-info-box">
                <div class="sjbox-info">
                  <div class="tags" style="margin-top: 0">{{ infoData.niche_data?.type === 1 ? "供应" : "需求" }}</div>
                  <div class="sjbox-title">{{ infoData.niche_data?.title }}</div>
                </div>
                <div class="sjbox-info">
                  <div class="info-lable">{{ t("ad.yxsj") }}</div>
                  <div class="info-value">
                    {{
                    infoData.niche_data?.effective_unlimited === 1
                    ? "长期有效"
                    : `${infoData.niche_data?.effective_begin} ~ ${infoData.niche_data?.effective_end}`
                    }}
                  </div>
                </div>
                <div class="sjbox-info">
                  <div class="info-lable">{{ t("ad.sjfl") }}</div>
                  <div class="info-value">{{ infoData.niche_data?.classify.map((e) => e.alias).join("/") }}</div>
                </div>
                <div class="sjbox-info">
                  <img class="sjbox-info-group" v-if="infoData?.niche_data.square_data?.avatar"
                    :src="infoData?.niche_data.square_data?.avatar" />
                  <img v-else class="sjbox-info-group" src="@/assets/svg/clouddisk/temaavatar.svg" />
                  <div class="info-value">{{ infoData?.niche_data.square_data?.name }}</div>
                </div>
              </div>
            </div>
          </div>
          <div class="formFlexItem" style="width: 100%" v-if="infoData?.skip_type === 2">
            <div class="lable-rq">{{ t("ad.redirectSquareNumber") }}</div>

            <div class="gchbox" v-if="infoData?.skip_type === 2"
              @click="onActionSquare({}, infoData?.square_data?.squareId)">
              <div class="gchbox-left">
                <img v-if="infoData?.square_data?.avatar" class="gchbox-img" :src="infoData?.square_data?.avatar" />
                <avatar v-else-if="infoData?.square_data?.squareType === 'INDIVIDUAL'" avatarSize="48px"
                  :image-url="infoData?.square_data?.avatar ?? ''" :user-name="infoData?.square_data?.name"
                  :round-radius="true" />
                <img v-else class="gchbox-img" src="@/assets/svg/clouddisk/temaavatar.svg" />
              </div>
              <div class="sjbox-info-box">
                <div class="sjbox-info">
                  <span class="star" v-if="infoData?.square_data?.squareType !== 'INDIVIDUAL'">
                    <iconpark-icon v-if="infoData?.square_data?.squareType === SquareType.Enterprise"
                      name="iconenterprise" class="star-icon"></iconpark-icon>
                    <img v-else-if="infoData?.square_data?.squareType === 'INDIVIDUAL'"
                      v-lazy="getSrcLogo(infoData?.square_data?.avatar)" class="star-icon" />
                    <iconpark-icon v-else-if="infoData?.square_data?.squareType === SquareType.BusinessAssociation"
                      name="iconbusiness" class="star-icon"></iconpark-icon>
                    <iconpark-icon v-else-if="infoData?.square_data?.squareType === SquareType.IndividualBusiness"
                      name="iconindividual" class="star-icon"></iconpark-icon>
                    <iconpark-icon v-else-if="infoData?.square_data?.squareType === SquareType.Other" name="iconother"
                      class="star-icon"></iconpark-icon>
                    <iconpark-icon v-else-if="infoData?.square_data?.squareType === SquareType.Government"
                      name="icongov" class="star-icon"></iconpark-icon>
                  </span>
                  <div class="gchbox-title">{{ infoData?.square_data?.name }}</div>
                </div>
                <div class="gchbox-lable">{{ t("ad.gchId") }}: {{ infoData?.square_data?.uuid }}</div>
                <div class="gchbox-tag">
                  {{ infoData?.square_data?.teamName ? infoData?.square_data?.teamName : "个人广场号" }}
                </div>
              </div>
            </div>
          </div>
          <div class="formFlexItem" style="width: 100%" v-if="infoData?.remark">
            <div class="lable-rq">{{ t("ad.bz") }}</div>
            <div class="value-rq">{{ infoData?.remark || "--" }}</div>
          </div>
        </div>
      </div>
      <div class="headBox" style="flex-wrap: wrap">
        <div class="tabLab">{{ t("ad.adDynamic") }}</div>
        <div class="table-box">
          <t-table row-key="index" :data="infoData?.log_list" :columns="columns" :hover="hover" :size="size"
            :pagination="pagination" cell-empty-content="-">
            <template #operator="{ row }">
              <div style="display: flex; align-items: center; gap: 12px">
                <avatar v-if="row.operator&&row.operator!=='--'" avatarSize="32px" :image-url="row.avatar ?? ''"
                  :user-name="row.operator" :round-radius="true" />
                <span>{{ row.operator }}</span>
              </div>
            </template>
          </t-table>
        </div>
      </div>
      <div class="headBox" style="justify-content: center; gap: 12px; position: fixed; bottom: 0" v-if="
          infoData?.status !== 3 &&
          infoData?.status !== 4 &&
          infoData?.status !== 7 &&
          infoData?.status !== 8 &&
          infoData?.status !== 5
        ">
        <t-button style="min-width: 80px;font-weight: 600;" v-if="infoData?.status === 6" theme="default" variant="outline" @click="qxAd">
          取消
        </t-button>
      </div>
    </div>
    <costDetails ref="costDetailsRef"> </costDetails>

    <stopAd @callBack="endCallBack" ref="stopAdRef"> </stopAd>
    <reviewFailed @callBack="endCallBack" ref="reviewFailedRef"> </reviewFailed>
    <editAdvertSingModle @callBackFormData="callBackFormData" ref="addAdvertSingModleRef"></editAdvertSingModle>
  </div>
</template>
<script setup lang="ts" name="AdDetails">
  import { ref, onMounted, onActivated, watch } from "vue";
  import { DialogPlugin, MessagePlugin } from "tdesign-vue-next";
  import avatar from "@renderer/components/kyy-avatar/index.vue";
  import { onMountedOrActivated } from "@/hooks/onMountedOrActivated";
  import stopAd from "@/views/member/member_home/panel/mark-advertising/components/stopAd.vue";
  import reviewFailed from "@/views/member/member_home/panel/mark-advertising/components/reviewFailed.vue";
  import { getSrcLogo } from "@renderer/views/message/service/msgUtils";
  import { SquareType } from "@/api/square/enums";

  import costDetails from "@/views/member/member_home/panel/mark-advertising/components/costDetails.vue";
  import editAdvertSingModle from "@/views/member/member_home/panel/mark-advertising/editAdvertSingModle.vue";

  import { frontaddetail, admanageadedit, admanageadreview, adfrontadcancel } from "@renderer/api/member/api/ebookApi";

  import { useI18n } from "vue-i18n";
  import useClipboard from "vue-clipboard3";
  import { useRouter, useRoute } from "vue-router";
  import { useApi } from "@renderer/views/cbd/hooks/api";
  import LynkerSDK from "@renderer/_jssdk";
  const { ipcRenderer } = LynkerSDK;

  const { onActionSquare } = useApi();
  const { toClipboard } = useClipboard();
  const router = useRouter();
  const { t } = useI18n();

  const stopAdRef = ref(null);
  const addAdvertSingModleRef = ref(null);
  const route = useRoute();
  const placeType = ref([
    {
      type: 1,
      name: "PC_平台_主轮播图",
    },
    {
      type: 2,
      name: "PC_平台_右上",
    },
    {
      type: 3,
      name: "PC_平台_右下",
    },
    {
      type: 4,
      name: "App_平台_主轮播图",
    },
    {
      type: 5,
      name: "PC_市场_主轮播图",
    },
    {
      type: 6,
      name: "PC_市场_右上",
    },
    {
      type: 7,
      name: "PC_市场_右下",
    },
    {
      type: 8,
      name: "App_市场_主轮播图",
    },
  ]);
  // 1未开始 2投放中 3结束 4终止 5取消 6待审核 7拒绝 8超时未审核
  const tagList = ref([
    {
      lable: t("ad.wks"),
      value: 1,
      color: "#4093E0",
      background: "#E8F0FB",
    },
    {
      lable: t("ad.tfz"),
      value: 2,
      color: "#FC7C14",
      background: "#FFE5D1",
    },
    {
      lable: t("ad.yjs"),
      value: 3,
      color: "#499D60",
      background: "#E0F2E5",
    },
    {
      lable: t("ad.yzz"),
      value: 4,
      color: "#D54941",
      background: "#F7D5DB",
    },
    {
      lable: t("ad.yqx"),
      value: 5,
      color: "#516082",
      background: "#ECEFF5",
    },
    {
      lable: t("ad.dsh"),
      value: 6,
      color: "#4D5EFF",
      background: "#EAECFF",
    },
    {
      lable: t("ad.shbtg"),
      value: 7,
      color: "#D54941",
      background: "#F7D5DB",
    },
    {
      lable: t("ad.cswsh"),
      value: 8,
      color: "#D54941",
      background: "#F7D5DB",
    },
  ]);
  const costDetailsRef = ref(null);
  const tableList = ref([
    {
      opttime: "2022-123-123",
      opter: "2022-123-123",
      opt: "2022-123-123",
    },
  ]);
  const copyLink = async (val) => {
    await toClipboard(val);
    MessagePlugin.success(t("ad.yfz"));
  };
  const endAd = () => {
    console.log(stopAdRef.value, "stopAdRefstopAdRef");
    stopAdRef.value.openWin(route.query.id, route.query.teamId);
    return;
    // admanageadstop(
    //   {
    //     ad_id: route.query.id,
    //   },
    //   route.query.teamId,
    // ).then((res) => {
    //   console.log(res, "呃呃我认为二位二位二位");
    //   infoData.value = res.data.data;
    //   let objs = {
    //     title: res.data.data.title,
    //     image_url: res.data.data.image_url,
    //     skip_type: res.data.data.skip_type,
    //     skip_param: res.data.data?.skip_param,
    //     remark: res.data.data.remark,
    //     businessItem: "",
    //     ad_id: route.query.id,
    //   };
    //   if (res.data.data.skip_type == "1") {
    //     objs.businessItem = res.data.data.niche_data;
    //     objs.skip_param = res.data.data.niche_data;
    //   }
    //   if (res.data.data.skip_type == "2") {
    //     objs.businessItem = objs.skip_param;
    //   }
    //   if (res.data.data.skip_type == "4") {
    //     objs.skip_param = null;
    //   }
    //   addAdvertSingModleRef.value.openWin(route.query.teamId, true, objs);
    // });
  };
  const editAd = async () => {
    if (!route.query.id) return;

    frontaddetail(route.query.id, route.query.teamId).then((res) => {
      console.log(res, "呃呃我认为二位二位二位");
      infoData.value = res.data.data;
      let objs = {
        title: res.data.data.title,
        image_url: res.data.data.image_url,
        skip_type: res.data.data.skip_type,
        skip_param: res.data.data?.skip_param,
        remark: res.data.data.remark,
        businessItem: "",
        ad_id: route.query.id,
      };
      if (res.data.data.skip_type == "2") {
        objs.businessItem = res.data.data.square_data;
        objs.skip_param = res.data.data.square_data;
      }
      if (res.data.data.skip_type == "1") {
        objs.businessItem = res.data.data.niche_data;
        objs.skip_param = res.data.data.niche_data;
      }
      if (res.data.data.skip_type == "4") {
        objs.skip_param = null;
      }
      if (res.data.data.skip_type == "5") {
        objs.skip_param = shopData.value.id;
      }
      console.log(infoData.value, "infoData.valueinfoData.value");
      addAdvertSingModleRef.value.openWin(route.query.teamId, true, objs, infoData.value);
    });
  };
  const reviewFailedRef = ref(null);
  const endCallBack = () => {
    getAd();
  };
  const isNot = () => {
    reviewFailedRef.value.openWin(route.query.id, route.query.teamId);
  };

  const qxAd = () => {
    const confirmDia = DialogPlugin.confirm({
      header: t("ad.qxshm"),
      theme: "info",
      confirmBtn: t("ad.qxsh"),
      cancelBtn: t("ad.zbqx"),
      onConfirm: async () => {
        adfrontadcancel(
          {
            ad_id: route.query.id,
          },
          route.query.teamId,
        )
          .then((res) => {
            if (res.data.code == 0) {
              MessagePlugin.success("操作成功");
              getAd();
            }
            confirmDia.hide();
          })
          .catch((err) => {
            MessagePlugin.error(err.message);
          });
      },
      onClose: () => {
        confirmDia.hide();
      },
    });
  };
  const isOk = () => {
    const confirmDia = DialogPlugin.confirm({
      header: t("ad.qdsgtg"),
      theme: "info",
      body: t("ad.tfkshggjzd"),
      closeBtn: null,
      onConfirm: async () => {
        admanageadreview(
          {
            ad_id: route.query.id,
            status: 1,
          },
          route.query.teamId,
        )
          .then((res) => {
            if (res.data.code == 0) {
              MessagePlugin.success("操作成功");

              getAd();
            }
            confirmDia.hide();
          })
          .catch((err) => {
            getAd();
            confirmDia.hide();
            MessagePlugin.error(err.message);
          });
      },
      onClose: () => {
        confirmDia.hide();
      },
    });
  };
  const columns = [
    {
      title: t("ad.czsj"),
      colKey: "created_at",
    },
    {
      title: "操作",
      colKey: "operate_text",
    },
    {
      title: "操作者",
      colKey: "operator",
    },
  ];
  const openImg = (val) => {
    ipcRenderer.invoke("view-img", JSON.stringify({ url: val }));
  };
  const infoData = ref(null);
  const openMx = (id) => {
    costDetailsRef.value.openWin(id, route.query.teamId);
  };
  import { useDigitalPlatformStore } from "@renderer/views/digital-platform/store/digital-platform-store";
  import useRouterHelper from "@renderer/views/square/hooks/routerHelper";
  import { getProductDetail } from "@/views/digital-platform/marketplace/apis"
  import { getNowDigitalType } from "@/views/digital-platform/marketplace/utils/index.ts";

  const digitalPlatformStore = useDigitalPlatformStore();
  const digitalRouter = useRouterHelper("digitalPlatformIndex");
  const onOpenRich = (row) => {
    if (infoData.value.skip_type === 5) {
      const url = LynkerSDK.getH5UrlWithParams(`shop/index.html#/product-detail/${shopData.value.id}`, { teamId: route.query.teamId }); console.log(url, 'urlurl');
      LynkerSDK.digitalPlatform.openTabForWebview({ title: `商品详情`, url: url,path_uuid:`productDetailTab-${row.skip_param}` })
      return;
    }
    const type = getNowDigitalType();
    const pageKey = `digital_platform_${type}_rich_detail`;
    const searchMenu = digitalRouter.routeList.find((v) => v.name.includes(pageKey));
    searchMenu.query = { uuid: row.uuid, platform: platform.digitalPlatform, from: type };
    router.push({ path: searchMenu.fullPath, query: searchMenu.query });
    searchMenu.title = row.title;
    digitalPlatformStore.addTab(toRaw(searchMenu), true);
  };

  const callBackFormData = (val) => {
    console.log(val, "vcallasdasdasd");
    let str = "";
    if (val.skip_type === 1) {
      str = val.skip_param.uuid;
    } else if (val.skip_type === 2) {
      str = val.skip_param.squareId;
    } else if (val.skip_type === 3) {
      str = val.skip_param;
    } else if (val.skip_type === 5) {
      str = val.skip_param?.spuId ? val.skip_param?.spuId : val.skip_param;
    }
    // 编辑提交代码
    admanageadedit(
      {
        ad_id: val.ad_id,
        title: val.title,
        skip_type: val.skip_type,
        skip_param: str,
        image_url: val.image_url,
        remark: val.remark,
      },
      route.query.teamId,
    )
      .then((res) => {
        console.log(res, "ressssss");
        if (res.data.code == 0) {
          MessagePlugin.success("编辑成功");
          getAd();
          addAdvertSingModleRef.value.closeWin();
        }
      })
      .catch((err) => {
        MessagePlugin.error(err.message);
        getAd();
        addAdvertSingModleRef.value.closeWin();
      });
  };
  const shopData = ref(null)
  const getAd = () => {
    if (!route.query.id) return;
    frontaddetail(route.query.id, route.query.teamId).then((res) => {
      console.log(res, "呃呃我认为二位二位二位");
      if (res.data.data.skip_type === 5) {
        // res.data.data.skip_param
        getProductDetail({ spuId: res.data.data.skip_param }).then(res => {
          console.log(res.data.data.spu, '阿是擦上次');
          shopData.value = {
            ...res.data.data.spu,
            spuId: res.data.data.spu.id
          };
        })
      }
      infoData.value = res.data.data;
    });
  };
  onActivated(() => {
    console.log("onActivatedonActivatedonActivated");
  });
  watch(
    () => route.query,
    (newValue) => {
      if (route.query.name === "uni_adDetails") {
        getAd();
      }
    },
  );

  onMountedOrActivated(() => {
    console.log(route, "啊实打实大师撒");
    getAd();
  });
</script>

<style lang="less" scoped>
  .ad-shop-box-info {
    .tags {
      border-radius: 4px;
      height: 20px;
      padding: 0px 4px;
      font-size: 12px;
      line-height: 20px;
    }

    .flexbox {
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .lsx-tags-status {
      border-radius: 4px;
      height: 20px;
      padding: 0px 4px;
      font-size: 12px;
      line-height: 20px;
      border-radius: 4px;
      color: #4d6eff;
      ;
      background: #EAECFF;
      width: fit-content;
    }

    .lsx-tags-status1 {
      border-radius: 4px;
      height: 20px;
      padding: 0px 4px;
      font-size: 12px;
      line-height: 20px;
      border-radius: 4px;
      color: #499D60;
      background: #E0F2E5;
      width: fit-content;
    }

    .lsx-tags {
      border-radius: 4px;
      height: 20px;
      padding: 0px 4px;
      font-size: 12px;
      line-height: 20px;
      border-radius: 4px;
      color: #CA48EB;
      background: #FAEDFD;
      width: fit-content;
    }
  }

  .flexbox {
    display: flex;
    align-items: center;
  }

  .ad-shop-box-info-title {
    width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    word-break: break-all;
    overflow: hidden;
    margin-bottom: 8px;
    color: #1A2139;
    text-overflow: ellipsis;

    /* kyy_fontSize_2/bold */
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px;
    /* 157.143% */
  }









  .sjbox-left {
    img {
      width: 121px;
      height: 121px;
      border-radius: 8px;
    }
  }

  .sjbox-info-box {
    display: flex;
    align-items: center;
    gap: 4px;
    flex-wrap: wrap;

    .sjbox-info {
      display: flex;
      width: 100%;
      align-items: center;
      gap: 8px;
    }

    .tags {
      display: flex;
      height: 20px;
      min-height: 20px;
      max-height: 20px;
      padding: 0px 4px;
      justify-content: center;
      align-items: center;
      gap: 4px;
      border-radius: var(--kyy_radius_tag_s, 4px);
      background: var(--kyy_color_tag_bg_cyan, #e6f9f8);
      color: var(--kyy_color_tag_text_cyan, #11bdb2);
      text-align: center;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      /* 166.667% */
    }

    .sjbox-title {
      overflow: hidden;
      color: var(--text-kyy_color_text_1, #1a2139);
      text-overflow: ellipsis;
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: 22px;
      /* 157.143% */
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      word-break: break-all;
      max-width: 560px;
    }

    .info-lable {
      overflow: hidden;
      color: var(--text-kyy_color_text_3, #828da5);
      text-overflow: ellipsis;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      /* 157.143% */
    }

    .sjbox-info-group {
      width: 24px;
      height: 24px;
      border-radius: 50%;
    }

    .info-value {
      overflow: hidden;
      color: var(--text-kyy_color_text_1, #1a2139);
      text-overflow: ellipsis;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      /* 157.143% */
    }
  }

  .sjbox {
    display: flex;
    padding: 12px;
    align-items: center;
    gap: 12px;
    align-self: stretch;
    width: 824px;
    border-radius: 8px;
    background: var(--bg-kyy_color_bg_deep, #f5f8fe);
  }

  .pthy {
    color: #499d60 !important;
    background: #e0f2e5 !important;
    margin-top: 2px !important;
  }

  .pt {
    color: #21acfa !important;
    background: #e4f5fe !important;
    margin-top: 2px !important;
  }

  .gchbox {
    align-items: center;
    border-radius: 8px;
    background: var(--bg-kyy_color_bg_deep, #f5f8fe);
    display: flex;
    width: 412px;
    cursor: pointer;
    padding: 12px;
    gap: 16px;
    border: 1px solid transparent;
  }

  .gchbox:hover {
    border-color: #4d5eff !important;
  }

  .gchbox-img {
    width: 48px;
    height: 48px;
    border-radius: 50%;
  }

  .gchbox-lable {
    overflow: hidden;
    color: var(--text-kyy_color_text_3, #828da5);
    text-overflow: ellipsis;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    /* 166.667% */
    width: 100%;
    margin: 0 4px;
  }

  .mt0 {
    margin-bottom: 0 !important;
  }

  .gcbox {
    display: flex;
    padding: 12px;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    gap: 12px;
    border-radius: 8px;
    border: 1px solid var(--brand-kyy_color_brand_default, #4d5eff);
    background: var(--bg-kyy_color_bg_deep, #f5f8fe);

    .gchbox-title {
      overflow: hidden;
      color: var(--text-kyy_color_text_1, #1a2139);
      text-overflow: ellipsis;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 24px;
      /* 150% */
    }
  }

  .gchbox-tag {
    display: flex;
    height: 20px;
    min-height: 20px;
    max-height: 20px;
    padding: 0px 4px;
    justify-content: center;
    align-items: center;
    gap: 4px;
    border-radius: var(--kyy_radius_tag_s, 4px);
    background: var(--kyy_color_tag_bg_kyyBlue, #e4f5fe);
    overflow: hidden;
    color: var(--kyy_color_tag_text_kyyBlue, #21acfa);
    text-align: center;
    text-overflow: ellipsis;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    /* 166.667% */
  }

  .AdDetails {
    background: url("@/assets/member/bg_big.svg") no-repeat 100%;
    background-size: cover;
    background-position: center;

    .banner {
      width: 872px;
      margin: 12px auto 88px;
    }

    .formDataView {
      display: flex;
      flex-wrap: wrap;
      gap: 24px;
    }

    .click3 {
      background: url("@/assets/member/click3.svg") no-repeat 100%;
    }

    .click2 {
      background: url("@/assets/member/click2.svg") no-repeat 100%;
    }

    .click1 {
      background: url("@/assets/member/click1.svg") no-repeat 100%;
    }

    .numBox {
      width: 100%;
      display: flex;
      align-items: center;
      gap: 24px;

      .num-tf {
        padding: 12px;
        display: flex;
        border-radius: 6px;
        width: 100%;
        flex-direction: column;

        .lables {
          width: 100%;
          color: var(--text-kyy_color_text_white, #fff);
          font-size: 12px;
          font-style: normal;
          font-weight: 500;
          line-height: 20px;
        }

        .num {
          width: 100%;
          color: var(--text-kyy_color_text_white, #fff);
          font-size: 24px;
          font-style: normal;
          font-weight: 600;
          line-height: normal;
        }
      }
    }

    .formFlexItem {
      display: flex;
      flex-direction: column;
      width: 258px;
      gap: 8px;
    }

    .numBox {
      display: flex;
      align-items: center;
      gap: 24px;
    }

    .tags {
      display: flex;
      height: 20px;
      min-height: 20px;
      max-height: 20px;
      padding: 0px 4px;
      justify-content: center;
      margin-right: 8px;
      align-items: center;
      gap: 4px;
      border-radius: var(--kyy_radius_tag_s, 4px);
      background: var(--kyy_color_tag_bg_kyyBlue, #e4f5fe);
      color: var(--kyy_color_tag_text_kyyBlue, #21acfa);
      text-align: center;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      float: left;
      width: fit-content;
      margin-top: 4px;
    }

    .headBox {
      display: flex;
      align-items: center;
      width: 872px;
      display: flex;
      width: 872px;
      margin-bottom: 12px;
      padding: 16px 24px;
      align-items: flex-start;
      border-radius: 8px;
      background: var(--bg-kyy_color_bg_light, #fff);

      .img0 {
        max-width: 465px;
        height: 160px;
        margin-right: 24px;
      }

      .headInfo {
        display: flex;
        flex-wrap: wrap;

        .headInfoTitle {
          margin-bottom: 16px;
          float: left;
          word-break: break-all;
          max-width: 50rem;
          height: 78px;
          color: var(--text-kyy_color_text_1, #1a2139);
          font-size: 18px;
          font-weight: 600;
          line-height: 26px;
        }
      }

      .headInfoDis {
        margin-bottom: 12px;
        display: flex;
        align-items: center;
        width: 100%;
      }

      .icons {
        width: 20px;
        height: 20px;
        font-size: 20px;
        color: #828da5;
        margin-right: 12px;
      }

      .lable-rq {
        color: var(--text-kyy_color_text_3, #828da5);
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        /* 157.143% */
      }

      .mx {
        color: var(--color-button_text_brand-kyy_color_button_text_brand_font_default, #4d5eff);
        text-align: center;
        font-size: 14px;
        font-style: normal;
        cursor: pointer;
        margin-left: 12px;
        font-weight: 400;
        line-height: 22px;
        /* 157.143% */
      }

      .value-rq {
        color: var(--text-kyy_color_text_1, #1a2139);
        font-size: 14px;
        font-style: normal;
        word-break: break-all;
        font-weight: 400;
        line-height: 22px;
        /* 157.143% */
      }

      .copyicon {
        width: 20px;
        height: 20px;
        font-size: 20px;
        margin-left: 8px;
        color: #828da5;
        cursor: pointer;
      }

      .links {
        display: flex;
        padding: 12px;
        justify-content: space-between;
        align-items: center;
        gap: 8px;
        align-self: stretch;
        border-radius: 8px;
        background: var(--bg-kyy_color_bg_deep, #f5f8fe);

        .copy-link {
          color: var(--brand-kyy_color_brand_default, #4d5eff);
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px;
          width: calc(100% - 28px);
          /* white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    word-break: break-all; */
        }
      }
    }

    .tabLab {
      color: var(--text-kyy_color_text_1, #1a2139);
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      line-height: 24px;
      width: 100%;
      margin-left: 11px;
      position: relative;
      margin-bottom: 16px;
    }

    .tabLab::after {
      content: "";
      width: 3px;
      position: absolute;
      top: 4px;
      left: -11px;
      height: 16px;
      border-radius: 8px;
      background: var(--brand-kyy_color_brand_default, #4d5eff);
    }

    .end-time-class {
      padding-right: 4px;
      color: var(--text-kyy_color_text_3, #828da5);
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 24px;
      /* 150% */
    }

    .star-icon {
      width: 24px;
      font-size: 24px;
    }
  }

  /* ui规定滚动条样式 */
  ::-webkit-scrollbar {
    width: 6px !important;
    background-color: transparent !important;
  }

  ::-webkit-scrollbar-track {
    background-color: transparent !important;
  }

  ::-webkit-scrollbar-thumb {
    background: var(--icon-kyy_color_icon_transparent, rgba(26, 33, 57, 0.36)) !important;
    background-color: var(--icon-kyy_color_icon_transparent, rgba(26, 33, 57, 0.36)) !important;
    border-radius: 4px !important;
  }
</style>