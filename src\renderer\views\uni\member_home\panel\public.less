@import "@renderer/views/engineer/less/common.less";

:deep(.t-dialog__ctx) .t-dialog__position {
    padding: 0 !important;
}
.container {
    height: calc(100vh - 104px);
    width: 100%;
    max-width:none;
    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-color: @kyy_color_bg_light;
        padding: 0 16px;

        height: 64px;
        &-title {
            font-size: 16px;

            font-weight: 700;
            text-align: left;
            color: #13161b;
            user-select: none;
        }
        &-buttons {
          display: flex;
          gap: 8px;
        }

    }
    .body {
        // min-height:100%;
        overflow-y: auto;
        height: calc(100vh - 106px);
        // width: 100%;
        // padding: 12px;

        &-content {
            height: max-content;
            background-color: @kyy_color_bg_light;
            height: fit-content;
            min-height: 94%;
        }
    }
}


.searchForm {
	max-width: 1300px;
    padding: 16px;
	.btns {
		display: flex;
		margin-left: 32px;
	}
	&-item {
		&-input {
			width: 200px;
		}
	}
    &-buttons {
        display: flex;
        justify-content: flex-start;
        gap:8px;
    }
}


.tabs {
    display: flex;
    padding: 16px;
    padding-top: 0;
    &-tab {
        padding: 12px 16px;
    }
    &-active {
        font-size: 14px;

        font-weight: 700;
        color: #2069e3;
    }
}

.table {
    padding: 0 16px;
    display:block;
}

:deep(.t-form__label) {
    padding-right: 5px;

    white-space: wrap !important;

}

:deep(.t-table table) {
    width: 100%;
    // min-width: 100%;
}

.default_icon {
  .iconset {
    font-size: 20px;
  }
  &:hover {
    .iconset {
      color: #707EFF !important;
    }
  }
}
.operates {
    display: flex;
    gap: 16px;
    &-item {

    }
}

.gray {
  border-radius: var(--kyy_radius_tag_full, 999px);
  background: var(--kyy_color_tag_bg_gray, #ECEFF5);
  color: var(--kyy_color_tag_text_gray, #516082);
  // text-align: right;


  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 22px; /* 157.143% */
  padding: 0 6px;
  display: flex;
  justify-content: center;
  width: fit-content;
}
.reject {
  display: flex;
  justify-content: center;
  border-radius: var(--kyy_radius_tag_full, 999px);
  background: #FBDDE3;
  color: var(--lingke-wrong, #D92F4D);
  text-align: right;

  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 22px;
  min-width: 58px;
  max-width: 58px;
  width: fit-content;
}
.wait {
    background: var(--kyy_color_tag_bg_brand, #eaecff);
    border-radius: var(--kyy_radius_tag_full, 999px);
  font-size: 14px;

  font-weight: 600;
  color: var(--color-button-text-brand-kyy-color-button-text-brand-font-default, #4D5EFF);
    padding: 0 8px;
    display: inline-block;
    min-width: 58px;
    max-width: 58px;
    width: fit-content;
}
.success {
  border-radius: var(--kyy_radius_tag_full, 999px);
  background: var(--kyy_color_tag_bg_success, #E0F2E5);
  font-size: 14px;

  font-weight: 600;
  color: var(--kyy_color_tag_text_success, #499D60);
  padding: 0 8px;
  display: inline-block;
  width: fit-content;

}
.default {


  border-radius: var(--kyy_radius_tag_full, 999px);
  background: var(--kyy_color_tag_bg_gray, #ECEFF5);

  color: var(--kyy_color_tag_text_gray, #516082);
  // text-align: right;

  /* kyy_fontSize_2/bold */
  font-family: "PingFang SC";
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 22px; /* 157.143% */
  padding: 0 8px;
  display: inline-block;
  width: fit-content;
}



.system {
    display: flex;
    flex-wrap: wrap;
}

.detail-control {
    margin-bottom: 24px;
    .lable {
      font-size: 14px;

      font-weight: 700;
      text-align: left;
      color: #13161b;
      display: flex;
      align-items: center;
      // &::before {
      //   content: " ";
      //   width: 2px;
      //   height: 14px;
      //   background: #2069e3;
      //   border-radius: 2px;
      //   // position: absolute;
      //   left: 0;
      //   top: 2px;
      // }
      .line {
        width: 2px;
        height: 14px;
background: var(--brand-kyy-color-brand-default, #4D5EFF);
        border-radius: 2px;
        margin-right: 10px;
      }
    }

    .subLable {
      color: var(--text-kyy-color-text-3, #828DA5);

      /* kyy_fontSize_2/regular */
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
    }
    .value {
      color: var(--text-kyy_color_text_1, #1A2139);

      word-break: break-all;
      padding-right: 10px;
      /* kyy_fontSize_2/regular */
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
      margin-top: 4px;
    }
  }
  .logoContact {
    width: 35px;
    height: 48px;
    border-radius: 3px;
  }

  .su-header {
    width: 100%;
    padding: 16px;
    padding-bottom: 24px;
    // height: 64px;
    background: #ffffff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .title {
      font-size: 16px;

      font-weight: 700;
      text-align: left;
      color: #13161b;
    }
    .opt {
      display: flex;
    }
  }
  .more-search {
    display: flex;
    .in-box {
    }
    .af-icon {
      margin-left: 8px;
      height: 32px;
      cursor: pointer;
    }
    .f-icon {
      display: flex;
      width: 32px;
      height: 32px;
      cursor: pointer;
      min-height: 32px;
      max-height: 32px;
      padding: 6px;
      justify-content: center;
      align-items: center;
      gap: 10px;
      margin-left: 8px;
      border-radius: var(--radius-kyy-radius-button-s, 4px);
      border: 1px solid
        var(--color-button-border-kyy-color-button-border-border-dedault, #d5dbe4);
      background: var(
        --color-button-border-kyy-color-button-border-bg-default,
        #fff
      );
    }
  }
  .more-right {
    display: flex;
    gap: 8px;
    .fw600 {
      font-weight: 600;
    }
  }
  .closeIcon {
    font-size: 20px;
    color: #828DA5;
    transition: all 0.25s linear;
  }
  .filter-res {
    display: flex;
    padding: 0 16px;
    padding-bottom: 16px;
    flex-wrap: wrap;
    .tit {
      color: var(--text-kyy-color-text-2, #516082);

      /* kyy_fontSize_2/bold */
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: 22px; /* 157.143% */
    }
    .ov-time {
      display: flex;
      // height: 24px;
      // min-height: 24px;
      // max-height: 24px;
      padding: 2px 8px;
      align-items: center;
      gap: 8px;
      &:hover {
        transition: all 0.25s linear;
        background: var(--kyy_color_tag_bg_brand, #EAECFF);
        .closeIcon {
          color: #4D5EFF;
          transition: all 0.25s linear;
        }
      }
    }
    .close2 {
      margin-left: 8px;
      display: flex;
      align-items: center;
      img {
        width: 12px;
        height: 12px;
      }
    }
    .te {
      color: var(--kyy-color-tag-text-black, #1a2139);
      cursor: pointer;
      /* kyy_fontSize_2/regular */
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
      border-radius: 4px;
      background: var(--kyy-color-tag-bg-gray, #eceff5);
      margin-right: 8px;
      margin-bottom: 8px;
    }
    .stat {
      display: flex;
      // height: 24px;
      // min-height: 24px;
      // max-height: 24px;
      padding: 2px 8px;
      align-items: center;
      gap: 8px;
    }
    .kword {
      display: flex;
      // height: 24px;
      // min-height: 24px;
      // max-height: 24px;
      padding: 2px 8px;
      align-items: center;
      gap: 8px;
    }
    .icon {
      display: flex;
      margin-left: 4px;
      cursor: pointer;
      img {
        width: 14px;
        height: 14px;
        margin-top: 4px;
        margin-right: 4px;
      }
      a {
        display: inline-block;
      }
    }
  }
  .form-boxxx {
    padding: 0 8px;
    .fitem {
      margin-bottom: 24px;
      .title {
        color: var(--text-kyy-color-text-3, #828da5);

        /* kyy_fontSize_2/regular */
        font-family: PingFang SC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px; /* 157.143% */
      }
      .ctl {
        margin-top: 8px;
      }
    }
  }
  .foot {
    width: 100%;
    display: flex;
    justify-content: end;
    .btn1 {
      display: flex;
      cursor: pointer;
      height: 32px;
      min-width: 80px;
      min-height: 32px;
      max-height: 32px;
      padding: 0px 16px;
      justify-content: center;
      align-items: center;
      gap: 4px;
      color: var(
        --color-button-border-kyy-color-button-border-text-default,
        #516082
      );
      text-align: center;
      border-radius: var(--radius-kyy-radius-button-s, 4px);
      border: 1px solid
        var(--color-button-border-kyy-color-button-border-border-dedault, #d5dbe4);
      background: var(
        --color-button-border-kyy-color-button-border-bg-default,
        #fff
      );
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: 22px; /* 157.143% */
      margin-right: 8px;
    }
    .btn2 {
      display: flex;
      cursor: pointer;
      width: 88px;
      height: 32px;
      min-height: 32px;
      max-height: 32px;
      padding: 6px 16px;
      justify-content: center;
      align-items: center;
      gap: 4px;
      border-radius: var(--radius-kyy-radius-button-s, 4px);
      background: var(
        --color-button-primary-kyy-color-button-primary-bg-disabled,
        #c9cfff
      );
      color: var(--color-button-primary-kyy-color-button-primary-text, #fff);
      text-align: center;

      /* kyy_fontSize_2/bold */
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: 22px; /* 157.143% */
    }
    .btn3 {
      display: flex;
      cursor: pointer;
      width: 88px;
      height: 32px;
      min-height: 32px;
      max-height: 32px;
      padding: 6px 16px;
      justify-content: center;
      align-items: center;
      gap: 4px;
      border-radius: var(--radius-kyy-radius-button-s, 4px);
      background: var(
        --color-button-primary-kyy-color-button-primary-bg-default,
        #4d5eff
      );

      color: var(--color-button-primary-kyy-color-button-primary-text, #fff);
      text-align: center;
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: 22px;
    }
  }

  .phone {
    color: var(--text-kyy-color-text-3, #828da5);

    /* kyy_fontSize_2/regular */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }



.f-icon {
  transition: all  0.25s linear ;
  .iconscreen {
    font-size: 24px;
    color: #828DA5;
  }

  &:hover {
    border: 1px solid var(--color-button-secondary-brand-kyy-color-button-secondary-brand-border-dedault, #4D5EFF) !important;
    background: var(--color-button-secondary-brand-kyy-color-button-secondray-brand-bg-default, #EAECFF) !important;
    transition: all  0.25s linear ;
    .iconscreen {
      color: #4D5EFF;
    }
  }
}
.factive {
  border: 1px solid var(--color-button-secondary-brand-kyy-color-button-secondray-brand-border-active, #3E4CD1) !important;
  background: var(--color-button-secondary-brand-kyy-color-button-secondray-brand-bg-active, #DBDFFF)!important;
  .iconscreen {
    color: #4D5EFF;
  }
}
