<template>
  <div class="identity-container no-hide-bv">
    <router-view v-slot="{ Component }">
      <!-- <keep-alive> -->
        <component :is="Component" />
      <!-- </keep-alive> -->
    </router-view>
  </div>
</template>

<script lang='ts' setup>
import { onMounted, ref, watch } from "vue";

// 当前身份卡展示类型
// 0 本人-整合，来源：点击桌面端顶部个人头像->弹窗->点击弹窗中的个人头像/名称
// 1 本人-组织，来源：本人使用组织身份进行会话、通讯录、业务等，点击本人头像
// 2 本人-个人，来源：本人使用个人身份进行会话、通讯录、业务等，点击本人头像
// 3 联系人-好友
// 4 联系人-商务关系
// 5 联系人-非好友(可发消息)
// 6 联系人-非好友(不可发消息,添加联系人)
// 7 联系人-非好友(关联组织,可发消息)
</script>

<style lang="less" scoped>
.identity-container {
  width: 100%;
  height: 100%;
  background: var(--bg-kyy-color-bg-deep, #f5f8fe);
  overflow: hidden;
  border-radius: 10px;
  border: 1px solid #ddd;
}
</style>
<style>
html,
body {
  border-radius: 10px;
  overflow: hidden;
}
</style>