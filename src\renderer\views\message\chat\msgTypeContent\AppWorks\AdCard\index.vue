<template>
  <AppCard
    :is-loading="isLoading"
    :is-load-error="isLoadError"
    :refresh-cb="refreshCb"
    v-bind="$attrs"
  >
    <AppCardHeader style="font-size: 16px" :theme="getHeaderTheme()">
      {{ title }}
    </AppCardHeader>

    <AppCardBody>
      <t-space direction="vertical" :size="16" style="width: 100%">
        <t-space direction="vertical" :size="4">
          <t-space class="popularize-value" :size="5">
            <t-image
              :key="0"
              fit="cover"
              position="center"
              :src="lodash.get(data, 'header.team.logo', '') || BizUnion"
              class="popularize-company-logo"
            />
            <span class="popularize-company-name">{{ lodash.get(data, "header.team.name", "---") }}</span>
          </t-space>
          <div class="popularize-dynamic-info">
            {{ data?.content?.title }}
          </div>
        </t-space>
        <!-- <template v-if="scene === 5038 || scene === 5039">
          <t-space direction="vertical" :size="4">
            <h4 class="popularize-dynamic-title">{{ lodash.get(data, "body[3].key", "---") }}</h4>
            <div class="popularize-dynamic-info">
              {{ lodash.get(data, "body[3].value", "---") }}
            </div>
          </t-space>
        </template> -->
      </t-space>
      <div class="divider" />
    </AppCardBody>

    <AppCardFooter>
      <t-button
        v-if="scene === 22001 && getBtnTxt(status)"
        class="w-full fw-600"
        variant="outline"
        disabled
      >
        {{ getBtnTxt(status) }}
      </t-button>
      <t-button
        v-else
        class="w-full fw-600"
        variant="outline"
        @click="handleClick"
      >
        {{ t("im.public.detail") }}
      </t-button>
    </AppCardFooter>
  </AppCard>
</template>

<script setup lang="ts">
// api 文档 https://app.apifox.com/project/2360511
// 需求地址： https://app.mockplus.cn/app/share-371f1ff252873c6e88707b971d8ceb1fshare-4pstxjE3T/comment/R0Br6N31p/q5L1O7p-yF6
// 22001: 平台成员新建待审核广告
// 22002: 平台成员取消广告
// 22003: 广告审核超时提醒
import lodash from 'lodash';
import { PropType, computed, onMounted, onUnmounted, ref } from 'vue';
import { AppCard, AppCardBody, AppCardHeader, AppCardFooter } from '@renderer/views/message/chat/MessageAppCard';
import { useI18n } from 'vue-i18n';
// import { vElementVisibility } from '@vueuse/components'
import to from 'await-to-js';
import LynkerSDK from '@renderer/_jssdk';
import { manageaddetail } from '@renderer/api/member/api/ebookApi';
import { getAdBatchStatus } from '@renderer/api/im/chat';
import { useChatExtendStore } from '@renderer/views/message/service/extend';
import { MessagePlugin } from 'tdesign-vue-next';
import { goWorkerToPlatform } from '@renderer/utils/auth';
import { IMRefreshType } from '@renderer/views/message/common/constant';
import BizUnion from '@/assets/im/biz_union.png';
import { getBtnTxt } from './constant';
// import { ipcRenderer } from "electron";

const { ipcRenderer } = LynkerSDK;

const extendStore = useChatExtendStore();
const { t } = useI18n();
const props = defineProps({
  msg: { type: Object as PropType<MessageToSave>, required: true },
});

const isLoading = ref(true);
const isLoadError = ref(false);
const status = ref < number >(1);

const scene = computed(() => props.msg.contentExtra?.scene);
const data = computed(() => props.msg.contentExtra?.data);

const refreshCb = () => {
  getData();
};

// 当前卡片是否在可视区内
// const isVisible = ref(false)
// const onElementVisibility = (state) => {
//   isVisible.value = state;
// }

const getData = async () => {
  const { team_id, ad_id = '' } = data.value?.extend || {};
  const [err, res] = await to(manageaddetail(ad_id, team_id));
  if (err) {
    MessagePlugin.error(err.message || '暂无相关权限');
    return false;
  }
  const { code, message } = res.data || {};
  if (code !== 0) {
    MessagePlugin.error(message || '暂无相关权限');
    return false;
  }
  return true;
};

const handleClick = async () => {
  console.log(scene.value, 'scene.value11111');

  // 不是申请卡片并且不是待申请状态
  if (scene.value !== 22001 && status.value !== 6) {
    goToAdAdmin();
    return;
  }

  const flag = await getData();
  if (!flag) {
    return;
  }
  extendStore.showChatDialog('ad-info', props.msg);
};

const loadAdBatchStatus = async () => {
  const { team_id, ad_id = '' } = data.value?.extend || {};
  const params = {
    ids: String(ad_id || ''),
  };
  const [err, res] = await to(getAdBatchStatus(params, team_id));

  if (err) {
    isLoadError.value = true;
    return;
  }
  const { code, data: data2 } = res.data || {};
  if (code === 0) {
    status.value = data2[0].status;
    isLoading.value = false;
  }
};

/** 标题 */
const title = computed(() => {
  /**
     * 1商协 2政企 3CBD
     */
  const platform_type = props.msg.contentExtra?.data.extend?.platform_type;
  let title = '';
  switch (platform_type) {
    case 1:
      title = t('im.public.biz');
      break;
    case 2:
      title = t('im.public.government');
      break;
    case 3:
      title = t('member.digital.c');
      break;
    case 4:
      title = t('niche.szsq');
      break;
    case 5:
      title = t('niche.szgx');
      break;
    default:
      break;
  }
  return title;
});

const goToAdAdmin = async () => {
  /**
     * 1商协 2政企 3CBD
     * munber 21 politics 20  cbd 23  association 28
     */
  // const platform_type = props.msg.contentExtra?.data.extend?.platform_type;
  const ad_id = data.value.extend.ad_id;
  const team_id = data.value.extend.team_id;
  const platform_type = data.value.extend.platform_type;
  let type = null;
  let uuid = null;
  switch (platform_type) {
    case 1:
      type = 21;
      uuid = 'munber';
      break;
    case 2:
      type = 20;
      uuid = 'politics';
      break;
    case 3:
      type = 23;
      uuid = 'cbd';
      break;
    case 4:
      type = 28;
      uuid = 'association';
      break;
    default:
      break;
  }
  goWorkerToPlatform(team_id, '/workBenchIndex/AdDetails', {
    id: ad_id,
    title: t('ad.ggxq'),
    type,
    uuid,
  }, 'workBench');
};
const getHeaderTheme = () => {
  // 22001: 平台成员新建待审核广告
  // 22002: 平台成员取消广告
  // 22003: 广告审核超时提醒
  const theme = { 22001: 'primary', 22002: 'primary', 22003: 'primary' }[scene.value] || 'primary';
  return theme as any;
};
const updateAd = (e, args) => {
  const ad_id = data.value?.extend?.ad_id;
  if (!ad_id) {
    return;
  }
  const { type, data: { extend } } = args || {};
  if (type === IMRefreshType.IMRefreshAd && extend?.ad_id === ad_id) {
    // if (type === IMRefreshType.IMRefreshAd && isVisible.value) {
    loadAdBatchStatus();
  }
};

onMounted(() => {
  loadAdBatchStatus();
  ipcRenderer.on('IM-refresh', updateAd);
});
onUnmounted(() => {
  ipcRenderer.off('IM-refresh', updateAd);
});
</script>

<style lang="less" scoped>
  .divider {
    margin-top: 8px;
    display: block;
    height: 1px;
    background: var(--divider-kyy_color_divider_light, #eceff5);
  }

  .square-link {
    color: #4d5eff;
    cursor: pointer;
  }

  .popularize-company-logo {
    display: block;
    width: 24px;
    height: 24px;
    border-radius: 100%;
    overflow: hidden;
  }

  .popularize-company-name {
    font-family: PingFang SC;
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    color: #1a2139;
  }

  .popularize-dynamic-title {
    font-family: PingFang SC;
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    color: #828da5;
  }

  .popularize-dynamic-info {
    font-size: 14px;
    font-weight: 400;
    line-height: 26px;
    color: #1a2139;
  }

  .popularize-msg-wrap {
    display: flex;
    flex-direction: row;
    padding: 12px 16px;
    background-color: #f5f8fe;
    border-radius: 8px;
    justify-content: center;
    align-items: center;
    gap: 15px;
    cursor: pointer;
  }

  .popularize-msg-img {
    display: block;
    width: 100%;
    height: 100%;
  }

  .popularize-msg-text-wrap {
    flex-grow: 2;
    display: flex;
    align-items: center;
    flex-direction: column;
    width: 0;
    gap: 4px;
  }

  .popularize-msg-text {
    width: 100%;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    font-family: PingFang SC;
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    color: #1a2139;
  }

  .popularize-msg-text-line-1 {
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    &+& {
      color: #828da5;
    }
  }

  .popularize-msg-img-wrap {
    position: relative;
    width: 72px;
    height: 72px;
    border-radius: 6px;
    flex-shrink: 0;
    overflow: hidden;
  }

  .popularize-msg-img-num {
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    z-index: 2;
    bottom: 0;
    right: 0;
    height: 20px;
    min-width: 20px;
    padding: 0 5px;
    border-radius: 4px 0 0 0;
    font-family: PingFang SC;
    font-size: 14px;
    font-weight: 600;
    line-height: 19.6px;
    color: #fff;
    background-color: rgba(0, 0, 0, 0.3);
  }

  .popularize-msg-img-play {
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    z-index: 2;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
    margin: auto;
    width: 36px;
    height: 36px;
    border-radius: 100%;
    background-color: rgba(0, 0, 0, 0.2);
  }

  .play-icon,
  .books-icon {
    width: 16px;
    height: 16px;
  }

  .popularize-msg-img-books {
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    z-index: 2;
    top: 0;
    bottom: 0;
    right: 0;
    width: 20px;
    height: 20px;
    left: 52px;
    border-radius: 0px 8px 0px 8px;
    background-color: rgba(0, 0, 0, 0.2);
  }
</style>
