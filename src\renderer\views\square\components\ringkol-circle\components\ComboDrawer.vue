<template>
  <div>
    <t-drawer
      v-model:visible="visible"
      :header="$t('square.ringkol.boothCombo')"
      size="472px"
      :close-btn="true"
      :footer="showFooter"
      v-bind="$attrs"
      class="combo-drawer"
    >
      <!--已购买且未到期时显示-->
      <t-tabs v-if="showTabs" v-model="currentTab" class="tabs">
        <t-tab-panel :value="TAB.purchase" :label="$t('square.ringkol.comboBuy')" />
        <t-tab-panel :value="TAB.renew" :label="$t('square.ringkol.comboRenew')" />
      </t-tabs>

      <!--已到期续费-->
      <Empty v-if="!store.isPersonal && isAnnualFeeExpired">
        <template #tip>
          <p>当前暂未续费广场年费，</p>
          <p>请续费广场年费后再续费展位</p>
          <div class="mt-12">
            <t-button theme="primary" @click="emit('square-renewal')">立即续费</t-button>
          </div>
        </template>
      </Empty>

      <template v-else>
        <t-alert theme="warning" class="alert mb-16!">
          <!-- <template #icon></template> -->
          <template #message>
            <div class="mr-4">
              <p>可用天数：<span class="color-danger">{{ usableDays }}</span>天，到期时间：<span class="color-danger">{{ expiredDate }}</span></p>
            </div>

            <BoothInfoTooltip />
          </template>
        </t-alert>

        <!--到期时间内续费-->
        <template v-if="!store.isPersonal && isAnnualFeeExpired">
          <Empty>
            <template #tip>
              <p>当前暂未续费广场年费，</p>
              <p>请续费广场年费后再续费展位</p>
              <div class="mt-12">
                <t-button theme="primary" @click="emit('square-renewal')">立即续费</t-button>
              </div>
            </template>
          </Empty>
        </template>

        <template v-else-if="comboList.length">
          <div class="sub-title">{{ $t('square.ringkol.boothCombo') }}</div>
          <div v-loading="loading" class="combo-wrap">
            <div
              v-for="(item, index) in comboList"
              :key="item.id"
              class="combo-item"
              :class="{ active: selectItems.includes(item.id)}"
              @click="comboItemClick(item)"
            >
              <!--              <t-checkbox :value="item.id" />-->
              <div class="header">
                {{ item.name }}
                <div v-if="item.tag" class="tag">{{ item.tag }}</div>
              </div>
              <div class="content">
                <t-row :gutter="[0, 8]">
                  <t-col :span="6">
                    <span class="color-text-3">{{ $t('square.ringkol.boothNum') }}：</span>
                    <span class="color-text-1">{{ item.num }}{{ $t('square.piece2') }}</span>
                  </t-col>
                  <t-col :span="6">
                    <span class="color-text-3">{{ $t('square.price') }}：</span>
                    <span class="color-text-1 font-600">{{ symbol }}{{ item.amount }}</span>
                  </t-col>
                  <t-col :span="6" @click.stop>
                    <span class="color-text-3">购买数量：</span>
                    <!--<span class="color-text-1">({{ $t('square.about') }}) {{ symbol }}{{ (+item.perPrice.value).toFixed(2) }}/{{ $t('square.day') }}</span>-->
                    <t-input-number
                      v-model="item.count"
                      theme="column"
                      align="center"
                      :max="item.limitNum || 99"
                      :min="0"
                      placeholder=""
                      class="w-72!"
                      @change="calc"
                      @validate="(e) => countValidate(e, index)"
                    />
                  </t-col>
                </t-row>
              </div>
            </div>
          </div>

          <div class="combo-total">
            <div>展位总数</div>
            <div>{{ boothTotal }}个</div>
          </div>

          <!-- <div class="sub-title">{{ $t('square.comboAmount') }}</div>
          <t-table row-key="id" :data="tableData" :columns="columns">
            <template #count="{ row, rowIndex }">
              <t-input-number
                v-model="row.count"
                theme="column"
                align="center"
                :max="row.limitNum || 99"
                :min="1"
                placeholder=""
                class="w-72!"
                @change="calc"
                @validate="(e) => countValidate(e, rowIndex)"
              />
            </template>
          </t-table>-->
        </template>

        <Empty v-else :tip="$t('square.ringkol.noBoothCombo')" />
      </template>

      <template #footer>
        <!--到期时间内购买，不能设置年限-->
        <div v-if="store.isPersonal && !isBuy" class="table-footer">
          <div>{{ $t('square.buyYearLimit') }}</div>
          <t-input-number
            v-model="years"
            theme="column"
            align="center"
            :max="yearsMaxLimit"
            :min="1"
            :disabled="isBuy && adventDays > 0 && currentTab === TAB.purchase"
            placeholder=""
            class="w-72!"
            @validate="yearsValidate"
          />
        </div>

        <div class="flex color-text-1">
          <div class="flex-1">
            <!--<span class="color-text-2">/{{ years }}{{ $t('square.year') }}</span>-->
            <p v-loading="calcLoading" class="mb-4">{{ $t('square.amount2') }}： <span class="money">{{ symbol }}{{ totalPrice || '--' }}</span></p>
            <!-- <p class="text-12 color-text-2">{{ $t('square.buyAsAgree') }} -->
            <t-radio v-model="agree" allow-uncheck>{{ $t('square.action.agree') }}</t-radio>
            <t-link
              theme="primary"
              hover="color"
              class="text-12"
              @click="agreementDialogRef.open(AgreementType.circleRingkolBooth)"
            >
              《{{ $t('square.ringkol.agreement') }}》
            </t-link>
          </div>
          <div class="flex-col-center">
            <t-button
              theme="primary"
              :disabled="!selectItems.length"
              :loading="submitting"
              @click="submitDebounce"
            >
              {{ $t('square.action.agreeBuy') }}
            </t-button>
          </div>
        </div>
      </template>
    </t-drawer>

    <AllPay
      ref="allPayRef"
      v-model:all-pay-dialog="allPayDialog"
      :custom-success-dialog="true"
      :row-data="payParam"
      @payment-callback="paymentSuccess"
    />
    <PaySuccessTip
      v-if="showPaySuccessTip"
      v-model="showPaySuccessTip"
      :symbol="symbol"
      :price="totalPrice"
      :pay-type="payType"
      @confirm="emit('paySuccess')"
      @close="onClosePaySuccessTipDialog"
    />

    <AgreementDialog ref="agreementDialogRef" />
  </div>
</template>

<script setup lang="tsx">
import {
  computed, onBeforeMount, ref, watch,
} from 'vue';
import to from 'await-to-js';
import moment from 'moment';
import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next';
import { AxiosResponse } from 'axios';
import { useI18n } from 'vue-i18n';
import debounce from 'lodash/debounce';
import PaySuccessTip from '@/views/square/components/ringkol-circle/components/PaySuccessTip.vue';
import {
  getCombosList, getGroupRingkolCircleInfo,
  getRingkolCircleInfo,
  groupRingkolCircleOrderCreate,
  priceCalc,
  ringkolCircleOrderCreate,
} from '@/api/square/ringkolCircle';
import { Combo, Kind, RingkolCircle } from '@/api/square/models/ringkolCircle';
import { DATE_FORMAT, formatDate } from '@/utils/date';
import { orderTip } from '@/views/square/hooks/tip';
import { AgreementType } from '@/api/square/common';
import { getCurrency } from '@/views/square/utils/currency';
import { useRingkolCircle } from '@/views/square/components/ringkol-circle/hooks';
import Empty from '@/components/common/Empty.vue';
import { useSquareStore } from '@/views/square/store/square';
import AllPay from '@/components/paymentDialog/allPay.vue';
import BoothInfoTooltip from '@/views/square/components/ringkol-circle/components/BoothInfoTooltip.vue';
import AgreementDialog from '@/views/square/components/AgreementDialog.vue';

const props = withDefaults(defineProps<{
  modelValue: boolean;
  // 若传了广场号id，从接口获取另可圈设置信息
  squareId?: string,
  data?: RingkolCircle;
  hasFreeCombo?: boolean;
  renew?: boolean;
  zero?: boolean;

  groupId?: string;
}>(), {
  modelValue: false,
});
const emit = defineEmits(['update:modelValue', 'success', 'square-renewal', 'paySuccess']);

const { t } = useI18n();
const store = useSquareStore();
const visible = computed({
  get() {
    return props.modelValue;
  },
  set(value) {
    emit('update:modelValue', value);
  },
});

const TAB = { purchase: 1, renew: 2 };
const currentTab = ref(props.renew ? TAB.renew : TAB.purchase);
const dataInfo = ref<RingkolCircle>({} as RingkolCircle);
const { isAdvent, adventDays, isBuy } = useRingkolCircle(dataInfo);
const agreementDialogRef = ref(null);

// 年费到期，或到期时间小于30天
// const isAnnualNearExpired = computed(() => moment(data.value?.expiredAt).diff(moment(), 'days') <= 30);
// 显示到期内续费
// const showNearExpiredRenew = computed(() => currentTab.value === TAB.renew && isAnnualNearExpired.value);
// 展位是否到期
const isExpired = computed(() => {
  if (!isBuy.value) return true;
  return new Date(dataInfo.value?.expiredAt).getTime() < new Date().getTime();
});
// 年费是否到期
const isAnnualFeeExpired = computed(() => {
  const expiredAt = store.squareInfo?.organizationProfile?.expiredAt;
  return new Date(expiredAt).getTime() < new Date().getTime();
});
// 可用天数
const usableDays = computed(() => {
  if (adventDays.value === -1) return 365 * years.value;
  return adventDays.value;
});
// 到期时间
const expiredDate = computed(() => {
  // 第一次购买 或 已到期，当前时间 + 购买年限
  if (!isBuy.value || adventDays.value < 0) return moment().add(years.value, 'years').format(DATE_FORMAT);

  // 到期时间内购买，第一次购买或续费时的到期时间
  if (currentTab.value === TAB.purchase) return formatDate(dataInfo.value?.expiredAt);

  if (currentTab.value === TAB.renew) {
    // 组织: 到期时间为年费续费到期时间
    if (!store.isPersonal) return moment(dataInfo.value?.expiredAt).format(DATE_FORMAT);

    // 临期续费，购买时的到期时间后一天 + 1年；到期时，当前时间 + 1年
    const date: moment.Moment = isExpired.value ? moment() : moment(dataInfo.value?.expiredAt);
    return date.add(years.value, 'years').format(DATE_FORMAT);
  }
  return formatDate(dataInfo.value?.expiredAt);
});

// 显示选项卡
const showTabs = computed(() => !isExpired.value && isAdvent.value);
// 显示页脚
const showFooter = computed(() => comboList.value.length > 0 && !isAnnualFeeExpired.value);

const getInfo = async () => {
  const api = props.groupId ? getGroupRingkolCircleInfo : getRingkolCircleInfo;
  const [err, res] = await to<AxiosResponse<RingkolCircle>>(api(props.groupId || props.squareId));
  if (err) return;

  dataInfo.value = res.data;
};

onBeforeMount(async () => {
  if (props.squareId || props.groupId) {
    await getInfo();
  } else {
    dataInfo.value = props.data;
  }

  await getList();
});

watch(() => currentTab.value, () => {
  getList();
});

interface ComboExt extends Combo {
  expiredAt: string,
  amount: string,
  count: number,
}

const selectItems = ref([]);
const comboList = ref<ComboExt[]>([]);
const loading = ref(true);
const showPaySuccessTip = ref(false);
const payType = ref();
const symbol = computed(() => {
  const first = comboList.value[0];
  if (!first) return '';
  return getCurrency(first.totalPrice.currencyCode);
});
const boothTotal = computed(() => comboList.value
  .filter((v) => selectItems.value.includes(v.id))
  .reduce((acc, pre) => acc + (pre.count || 0) * pre.num, 0));

// 获取套餐列表（renew：true 续费套餐）
const getList = async () => {
  const [err, res] = await to(getCombosList({
    'page.size': 100,
    'page.number': 1,
    renew: currentTab.value === TAB.renew,
    zero: props.zero,
    kind: props.groupId ? Kind.Group : Kind.Square,
    // 群另可圈
    groupId: props.groupId,
  }));
  loading.value = false;
  if (err) return;

  // 是否已购买
  const isActive = dataInfo.value.active;
  // 剩余天数
  const restDays = computed(() => moment(dataInfo.value?.expiredAt).diff(moment(), 'days'));

  comboList.value = res.data.combos.map((v) => ({
    ...v,
    expiredAt: isActive ? formatDate(dataInfo.value?.expiredAt) : moment().add(1, 'years').format(DATE_FORMAT),
    amount: (+v.perPrice.value * usableDays.value).toFixed(2),
    count: 0,
    // 总价：1. 若未购买过，则总价为后台设置套餐时的总价
    // 2. 若已购买过，则当前继续购买套餐时，总价为“平均每个展位每天的单价 * 展位数量 * 剩余天数（到期时间 - 当前时间）”，结果四舍五入，保留两位小数。
    realAmount: isActive ? String(+v.perPrice.value * restDays.value) : v.totalPrice.value,
  }));

  if (comboList.value.length) {
    // 选中免费的
    if (props.hasFreeCombo) {
      selectItems.value = comboList.value.filter((v) => +v.totalPrice.value === 0).map((v) => v.id);
      return;
    }

    // 默认选择第一个
    selectItems.value = [comboList.value[0].id];
  }
};

// 套餐总价
// const columns = ref([
//   { colKey: 'name', title: t('square.ringkol.comboName') },
//   { colKey: 'amount', title: t('square.ringkol.price'), cell: (h, { row }) => `${symbol.value}${row.amount}` },
//   { colKey: 'num', title: t('square.ringkol.boothNum'), cell: (h, { row }) => `${row.num}个` },
//   { colKey: 'count', cell: 'count', title: t('square.ringkol.buyNum') },
// ]);
// const tableData = ref([]);

const comboItemClick = (item) => {
  const idx = selectItems.value.indexOf(item.id);
  if (idx > -1) {
    selectItems.value.splice(idx, 1);
  } else {
    selectItems.value.push(item.id);
  }
};

const countValidate = ({ error }, index) => {
  if (!error) return;
  if (error === 'exceed-maximum') {
    if (!msg.value) {
      msg.value = MessagePlugin.warning(t('square.ringkol.tip1'));
      // 避免重复弹消息提示
      setTimeout(() => {
        MessagePlugin.close(msg.value);
        msg.value = null;
      }, 3000);
    }
  }
  setTimeout(() => {
    const item = comboList.value[index];
    item.count = error === 'below-minimum' ? 1 : item.limitNum || 99;
    calc();
  }, 800);
};

// 购买年限
const years = ref(1);
const yearsMaxLimit = computed(() => Math.min(...comboList.value.map((v) => (v.limitYear || 99))));
const msg = ref(null);

// 年限数据校验
const yearsValidate = ({ error }) => {
  if (!error) return;
  if (error === 'exceed-maximum') {
    if (!msg.value) {
      msg.value = MessagePlugin.warning(t('square.ringkol.tip2'));
      setTimeout(() => {
        MessagePlugin.close(msg.value);
        msg.value = null;
      }, 3000);
    }
  }
  setTimeout(() => {
    years.value = error === 'below-minimum' ? 1 : yearsMaxLimit.value;
  }, 800);
};

// 应付总额
const totalPrice = ref('');
const calcLoading = ref(false);
const calc = () => {
  comboList.value.forEach((item) => {
    if (item.count === '' || item.count === undefined) {
      item.count = 0;
    }
  });

  const combos = comboList.value
    .filter((v) => selectItems.value.includes(v.id) && v.count > 0)
    .map((v) => ({ combo_id: String(v.id), copies: String(v.count) }));
  const inValidData = combos.some((item) => item.copies === 'undefined');
  if (!combos.length || inValidData) {
    totalPrice.value = '0.00';
    return;
  }

  // 由后端计算支付金额
  priceCalc({
    combos,
    years: years.value,
    renew: currentTab.value === TAB.renew,
    kind: props.groupId ? Kind.Group : Kind.Square,
    groupId: props.groupId,
  }).then((res) => {
    totalPrice.value = res.data.price.value;
  }).finally(() => {
    calcLoading.value = false;
  });
};

watch([selectItems, years], () => {
  if (!selectItems.value.length) {
    totalPrice.value = '--';
    return;
  }
  // tableData.value = comboList.value.filter((v) => selectItems.value.includes(v.id)).map((v) => {
  //   const exist = tableData.value.find((t) => t.id === v.id);
  //   return {
  //     ...v,
  //     count: exist ? exist.count : 1,
  //   };
  // });

  // if (isExpired.value) return;
  calc();
}, { deep: true });

// 应付总额
// const totalAmount = computed(() => (tableData.value.reduce((acc, curr) => acc + +curr.realAmount * (curr.count || 1), 0) * years.value).toFixed(2));
// const totalAmountFormat = computed(() => formatNumberWithCommas(totalAmount.value || 0));

// // 支付
// const paymentDialogRef = ref(null);
// // 对公支付
// const corporatePaymentRef = ref(null);
const payParam = ref(null);
// const corporatePaymentFlag = ref(false);
const allPayDialog = ref(false);
const allPayRef = ref();

const agree = ref(false);
const submitting = ref(false);

const submit = async () => {
  if (submitting.value) return;
  if (!selectItems.value.length) return MessagePlugin.warning(t('square.ringkol.tip3'));

  // if (!store.isPersonal && boothTotal.value < data.value.boothUnusedNum) {
  //   await MessagePlugin.warning('所购买的展位数需大于等于当前可用展位数，请增加展位数量购买');
  //   return;
  // }

  if (currentTab.value === TAB.renew && dataInfo.value.boothNum !== boothTotal.value) {
    await MessagePlugin.warning(t('square.ringkol.tip4'));
    return;
  }

  // 未勾选协议则弹窗提示
  if (!agree.value) {
    needAgree();
    return;
  }

  const combos = comboList.value
    .filter((v) => selectItems.value.includes(v.id) && v.count > 0)
    .map((v) => ({ id: v.id, num: v.count }));

  if (!combos.length) {
    await MessagePlugin.warning('至少购买一个数量为1的套餐');
    return;
  }

  submitting.value = true;
  const api = props.groupId ? groupRingkolCircleOrderCreate : ringkolCircleOrderCreate;
  const [err, res] = await to(api({
    combos,
    years: years.value,
    renew: currentTab.value === TAB.renew,
    kind: props.groupId ? Kind.Group : Kind.Square,
    groupId: props.groupId,
  }));

  submitting.value = false;
  if (err) return;
  if (orderTip(res.data)) {
    visible.value = false;
    return;
  }

  if (+totalPrice.value === 0) {
    await MessagePlugin.success(t('square.paySuccessTip'));
    setTimeout(() => {
      visible.value = false;
      emit('success');
    }, 3000);
    return;
  }

  // if (orderTip(res.data)) {
  //   visible.value = false;
  //   return;
  // }

  // 防止重复提交
  submitting.value = true;
  setTimeout(() => {
    submitting.value = false;
  }, 2000);

  payParam.value = {
    sn: res.data.orderNum,
    region: res.data.regionCode || 0,
    amount: +totalPrice.value,
    teamId: store.squareInfo?.organizationProfile?.teamId,
  };

  const isCanPay = await allPayRef.value.checkPayInfo();
  if (!isCanPay) return;

  allPayDialog.value = true;
};
const submitDebounce = debounce(submit, 400);

const paymentSuccess = (_payType: string) => {
  store.isPersonal && MessagePlugin.success(t('square.buySuccessTip'));
  showPaySuccessTip.value = true;
  payType.value = _payType;
};

const onClosePaySuccessTipDialog = (() => {
  visible.value = false;
  emit('success');
});

const needAgree = () => {
  const confirmDia = DialogPlugin.confirm({
    header: t('square.tip'),
    body: () => (<div>{ t('square.readAndAgree') }
      <span class="t-link t-link--theme-primary t-link--hover-color" onClick={() => agreementDialogRef.value.open(AgreementType.circleRingkolBooth)}>{ t('square.buyAgreement') }</span>
    </div>),
    confirmBtn: t('square.action.agree'),
    cancelBtn: { content: t('square.action.cancel'), theme: 'default', variant: 'outline' },
    closeBtn: null,
    closeOnOverlayClick: false,
    theme: 'info',
    onConfirm: async () => {
      confirmDia.destroy();
      agree.value = true;
      // await submit();
    },
    onClose: () => {
      confirmDia.hide();
    },
  });
};
</script>

<style scoped lang="less">
:global(.combo-drawer .t-drawer__header) {
  border-bottom: none;
  padding: 16px 24px;
}

:global(.combo-drawer .t-drawer__body) {
  padding: 0 24px 12px;
}

:deep(.t-tabs) {
  //margin-top: -16px;
  margin-bottom: 8px;
  .t-tabs__bar {
    display: none;
  }
  .t-tabs__nav-item-wrapper {
    padding: 0;
    margin: 0 40px 0 0;
  }
  .t-tabs__nav-item:not(.t-is-disabled):not(.t-is-active):hover .t-tabs__nav-item-wrapper {
    background-color: inherit;
  }
}

:deep(.t-checkbox) {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  line-height: 40px;
  text-align: center;
  z-index: 999;
}
:deep(.t-checkbox__input) {
  opacity: 0;
}

:deep(.t-input-number .t-button) {
  padding: 0;
}

.sub-title {
  color: #000;
  font-size: 16px;
  font-weight: 400;
  line-height: 24px; /* 150% */
  margin-bottom: 12px;
}

.combo-wrap {
  margin-bottom: 24px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.combo-item {
  width: 100%;
  min-width: 416px;
  //height: 114px;
  border-radius: 8px;
  border: 1px solid var(--divider-kyy_color_divider_deep, #D5DBE4);
  background: var(--bg-kyy_color_bg_light, #FFF);
  position: relative;
  cursor: pointer;
  &.active {
    border-color: var(--border-kyy_color_border_active, #4D5EFF);
  }
  .header {
    position: relative;
    height: 38px;
    line-height: 38px;
    padding-left: 16px;
    border-radius: 8px 8px 0 0;
    background: var(--bg-kyy_color_bg_table_title, #E2E6F5);
    font-size: 14px;
    font-weight: 700;
    color: @kyy_font_1;
    .tag {
      position: absolute;
      top: 0;
      right: 0;
      height: 24px;
      padding: 0 8px;
      color: #fff;
      background: #da2d19;
      border-radius: 0 4px 0 4px;
      font-size: 12px;
      display: flex;
      align-items: center;
    }
  }
  .content {
    padding: 16px;
    color: @kyy_font_1;
  }
}

.combo-total {
  display: flex;
  padding: 12px;
  align-items: center;
  justify-content: space-between;
  border-radius: 8px;
  background: var(--bg-kyy_color_bg_deep, #F5F8FE);
  color: #000;
  font-size: 16px;
  font-weight: 400;
  line-height: 24px; /* 150% */
}

.table-footer {
  margin: 0 -24px 16px;
  margin-top: -16px;
  padding: 0 24px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #000;
  font-size: 16px;
  font-weight: 400;
  line-height: 24px; /* 150% */
  border-bottom: 1px solid var(--divider-kyy_color_divider_light, #ECEFF5);
  :deep(.t-input.t-is-disabled) {
    background: var(--inputNumber-kyy_color_input_bg_success, #FFF) !important;
  }
}

.money {
  color: var(--error-kyy_color_error_default, #D54941);
  font-size: 18px;
  font-weight: 600;
}

.alert {
  :deep(.t-alert__description) {
    display: flex;
    align-items: center;
  }
}

.tabs {
  :deep(.t-tabs__nav-item:not(.t-is-active)) {
    color: var(--text-kyy_color_text_1, #1A2139);
  }
}
</style>
