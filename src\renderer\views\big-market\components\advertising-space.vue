<template>
  <div class="advertising-space" ref="elementRef">
    <div class="swiper-box " :class="placeType1[current]?.source_type===1?'guanggao':''">
      <t-swiper :class="placeType1.length < 2 ? 'not-dian' : ''"
      animation="fade"
      @change="onChanges" trigger="click"
        v-if="placeType1.length > 0 && showFlag"
        :navigation="{ type: 'dots', showSlideBtn: placeType1.length < 2 ? 'never' : 'never' }" class="big-ad-swiper"
        :duration="600" v-model:current="current" :autoplay="autoplay" :interval="5000" :loop="true">
        <t-swiper-item style="border-radius: 8px;height: 308px;" @click="viewDetail(item,current)"
          v-for="(item, index) in placeType1" :key="item.id">
          <img class="swiper-ad-img" style="height: 308px;" :src="item.image_url" />
        </t-swiper-item>
      </t-swiper>
      <!-- 默认图片 -->
      <img v-if="placeType1.length === 0" class="swiper-ad-img" style="border-radius: 8px;"
        src="http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/workbech/zlb.svg" />

    </div>
    <div class="swiper-box1" :class="placeType4[current1]?.source_type===1?'guanggao':''">
      <!-- 附轮播 -->
      <t-swiper :class="placeType4.length < 2 ? 'not-dian' : ''"
      @change="onChanges1" trigger="click"
      animation="fade"
        v-if="placeType4.length > 0 && showFlag1"
        :navigation="{ type: 'dots', showSlideBtn: placeType4.length < 2 ? 'never' : 'never' }" class="big-ad-swiper1"
        :duration="600" v-model:current="current1" :autoplay="autoplay1" :interval="5000" :loop="true">
        <t-swiper-item style="border-radius: 8px;height: 308px;" @click="viewDetail(item,current1)"
          v-for="(item, index) in placeType4" :key="item.id">
          <img class="swiper-ad-img1" style="height: 308px;" :src="item.image_url" />
        </t-swiper-item>
      </t-swiper>
      <!-- 附轮播默认图片 -->
      <img v-if="placeType4.length === 0" class="swiper-ad-img1" style="border-radius: 8px;width: 232px;"
        src="http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/workbech/flb.svg" />
    </div>

    <div class="big-ad-img-box">
      <div :class="placeType2[setBigMarkertRightTopIndex]?.source_type===1?'guanggao':''" style="width: 200px; height: 150px;">
        <img v-if="placeType2.length > 0 && setBigMarkertRightTopIndex !== null"
          @click="viewDetail(placeType2[setBigMarkertRightTopIndex],setBigMarkertRightTopIndex)"
          :src="placeType2[setBigMarkertRightTopIndex]?.image_url" />
        <img v-else src="http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/workbech/dscys.svg" />
      </div>
      <div :class="placeType3[setBigMarkertRightBottomIndex]?.source_type===1?'guanggao':''" style="width: 200px; height: 150px;">
        <img v-if="placeType3.length > 0 && setBigMarkertRightBottomIndex !== null"
          @click="viewDetail(placeType3[setBigMarkertRightBottomIndex],setBigMarkertRightBottomIndex)"
          :src="placeType3[setBigMarkertRightBottomIndex]?.image_url" />
        <img v-else src="http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/workbech/dscyx.svg" />
      </div>
    </div>
  </div>
</template>
<script setup lang="ts" name="advertising-space">
  import { ref, watch, watchEffect, onMounted, onUnmounted } from "vue";
  import { useRoute, useRouter } from "vue-router";
  import { getIndividualSquare } from "@renderer/api/square/home";
  import { adfrontadmarketshow ,adCommonAddEvent} from "@/views/big-market/apis";
  import { getProfilesInfo } from "@renderer/utils/auth";
  import { configInfo,isNotMac } from '@renderer/views/setting/util';
  import { ClientSide } from "@renderer/types/enumer";
  import useNavigate from "@renderer/views/square/hooks/navigate";
  import { getOpenid } from "@renderer/utils/auth";
  import { toSquareHome } from "@renderer/views/square/utils/ipcHelper";
  import { getSquareByopenId } from "@/api/business/manage";
  const { goHomePage } = useNavigate();
  import to from "await-to-js";
  import { getLang } from "@renderer/utils/auth";
  import LynkerSDK from "@renderer/_jssdk";
  const { ipcRenderer, shell } = LynkerSDK;

  let curLang = getLang() == "zh-cn" || !getLang();
  const props = defineProps({
    addressData: {
      type: Object,
      default: [],
    },
  });
  const route = useRoute();
  const router = useRouter();
  const autoplay = ref(true);
  const autoplay1 = ref(true);
  const elementRef = ref(null);
  const isInViewport = ref(false);
  const isInViewport1 = ref(false);
  //
  let current = ref(0);
  let current1 =  ref(0);
  const placeType1Length=ref(0);
  const placeType2Length=ref(0);
  const placeType3Length=ref(0);
  const placeType4Length=ref(0);
  const defaultCurrents = ref(0);

  const profileInfo = getProfilesInfo();
  const setBigMarkertRightTopIndex = ref(null);
  const setBigMarkertRightBottomIndex = ref(null);
  const bigMarkertSwiper = ref(window.localStorage.getItem("bigMarkertSwiperIndex")); //轮播索引
  const placeType1 = ref([]);
  const placeType2 = ref([]);
  const placeType3 = ref([]);
  const placeType4 = ref([]);
  const onChanges = (val, val1) => {
    console.log(val, val1,"有触发吗11");
    current.value=val;
    if (autoplay.value) {
      setViewNumSwiper();
      setBigMarkertSwiper();
    }
  };
  const onChanges1 = (val, val1) => {
    console.log("有触发吗");
    current1.value=val;

    if (autoplay1.value) {
      setViewNumSwiper1();
      setBigMarkertSwiper1();
    }
  };
  const setViewNumSwiper1=()=>{
    if (placeType4Length.value.includes(current1.value)) {
      adCommonAddEvent(
        {
          ad_id: placeType4.value[current1.value].ad_id,
        version:configInfo.version,
        product_type:isNotMac?5:4,
        event_type:1,//1点击2展示
        serial_num:current1.value,
        occur_at:Math.round(new Date().getTime() / 1000),
        report_at:Math.round(new Date().getTime() / 1000),
        source_type: placeType4.value[current1.value]?.source_type,
        },
        props.teamId,
      );
      const index = placeType4Length.value.indexOf(current1.value);
      if (index > -1) {
        placeType4Length.value.splice(index, 1);
      }
    }
  }
  const setViewNumSwiper=()=>{
    if (placeType1Length.value.includes(current.value)) {
      adCommonAddEvent(
        {
          ad_id: placeType1.value[current.value].ad_id,
        version:configInfo.version,
        product_type:isNotMac?5:4,
        event_type:1,//1点击2展示
        serial_num:current.value,
        occur_at:Math.round(new Date().getTime() / 1000),
        report_at:Math.round(new Date().getTime() / 1000),
        source_type: placeType1.value[current.value]?.source_type,
        },
        props.teamId,
      );
      const index = placeType1Length.value.indexOf(current.value);
      if (index > -1) {
        placeType1Length.value.splice(index, 1);
      }
    }
  }

  const setBigMarkertSwiper = () => {
    window.localStorage.setItem("bigMarkertSwiperIndex", current.value);
  };
  const setBigMarkertSwiper1 = () => {
    window.localStorage.setItem("bigMarkertSwiper1Index", current1.value);
  };
  const setBigMarkertRightTop = () => {
    window.localStorage.setItem("setBigMarkertRightTopIndex", setBigMarkertRightTopIndex.value);
  };
  const setBigMarkertRightBottom = () => {
    window.localStorage.setItem("setBigMarkertRightBottomIndex", setBigMarkertRightBottomIndex.value);
  };
  onUnmounted(() => { });
  // ipcRenderer.on("setBigMarkertSwiperIndex-swiper", () => {
  //   setBigMarkertSwiper();
  //   console.log("setBigMarkertSwipersetBigMarkertSwiper");
  // });
  const isSelfSquare = async () => {
    const [err, res] = await to(getSquareByopenId(getOpenid()));
    if (err) return false;
    const { data } = res;
    console.log("data", data);
    return data.opened && data.selfOpened;
  };
  let showFlag = ref(false);
  let showFlag1 = ref(false);
  const viewDetail = async (row,itemIndex) => {
    if (row.skip_type === 5) {
      const url = LynkerSDK.getH5UrlWithParams(`shop/index.html#/product-detail/${row.skip_param}`, { teamId: props.teamId ,platformTeamId:props.teamId});
      LynkerSDK.digitalPlatform.openTabForWebview({ title: `商品详情`, url: url, path_uuid: `productDetailTab-${row.skip_param}` })
    }
    console.log(row, "riwwww");
    if (row.skip_type === 1) {
      console.log("viewDetail", row);
      const path = "/bigMarketIndex/bigMarketDetailReadOnly";
      const query = { uuid: row.skip_param.toString(), from: "big-market" };
      ipcRenderer.invoke("set-big-market-tab-item", {
        path: `/bigMarketIndex/bigMarketDetailReadOnly`,
        path_uuid: "bigMarket",
        title: "商机详情",
        addNew: true,
        query,
        name: "bigMarketDetailReadOnly",
        type: ClientSide.BIGMARKET,
      });
      router.push({ path, query });
    }
    if (row.skip_type === 2) {
      const data = { ip_region: "" };
      const res = await isSelfSquare();
      if (res) {
        toSquareHome(row.skip_param);
      } else {
        goHomePage({ squareId: row.skip_param });
      }
    }
    if (row.skip_type === 3) {
      shell.openExternal(row.skip_param);
    }
    adCommonAddEvent(
      {
        ad_id: row.ad_id,
        version:configInfo.version,
        product_type:isNotMac?5:4,
        event_type:2,//1点击2展示
        serial_num:itemIndex,
        occur_at:Math.round(new Date().getTime() / 1000),
        report_at:Math.round(new Date().getTime() / 1000),
        source_type:row.source_type,
      },
      props.teamId,
    );
  };
  onMounted(async () => {
    await getList();
    placeType1Length.value = [];
    placeType2Length.value = [];
    placeType3Length.value = [];
    placeType4Length.value = [];
    for (let index = 0; index < placeType1.value.length; index++) {
      placeType1Length.value.push(index);
    }
    for (let index = 0; index < placeType2.value.length; index++) {
      placeType2Length.value.push(index);
    }
    for (let index = 0; index < placeType3.value.length; index++) {
      placeType3Length.value.push(index);
    }
    for (let index = 0; index < placeType4.value.length; index++) {
      placeType4Length.value.push(index);
    }
    if (bigMarkertSwiper.value) {
      let num = bigMarkertSwiper.value - 0 + 1;
      if (num > placeType1.value.length - 1) {
        current.value = 0;
      } else {
        current.value = num;
      }
    } else {
      current.value = 0;
    }
    showFlag.value = true;

    if (bigMarkertSwiper1.value) {
      let num = bigMarkertSwiper1.value - 0 + 1;
      if (num > placeType4.value.length - 1) {
        current1.value = 0;
      } else {
        current1.value = num;
      }
    } else {
      current1.value = 0;
    }
    showFlag1.value = true;
    setViewNumSwiper();
    setBigMarkertSwiper();
    setViewNumSwiper1();
    setBigMarkertSwiper1();
    let numys = window.localStorage.getItem("setBigMarkertRightTopIndex");
    if (numys || numys == 0) {
      if (numys >= placeType2.value.length - 1) {
        setBigMarkertRightTopIndex.value = 0;
      } else {
        setBigMarkertRightTopIndex.value = numys - 0 + 1;
      }
    } else {
      setBigMarkertRightTopIndex.value = 0;
    }
    let numyx = window.localStorage.getItem("setBigMarkertRightBottomIndex");
    if (numyx || numyx == 0) {
      if (numyx >= placeType3.value.length - 1) {
        setBigMarkertRightBottomIndex.value = 0;
      } else {
        setBigMarkertRightBottomIndex.value = numyx - 0 + 1;
      }
    } else {
      setBigMarkertRightBottomIndex.value = 0;
    }
    setBigMarkertRightTop();
    setBigMarkertRightBottom();
    const observer = new IntersectionObserver((entries) => {
      const entry = entries[0];
      isInViewport1.value = entry.isIntersecting;
      isInViewport.value = entry.isIntersecting;
    });
    if (elementRef.value) {
      // 确保元素已经存在
      observer.observe(elementRef.value);
    }
  });
  const editAdSwiper = (val) => {
    console.log("开始或者暂停", val);
    if (isInViewport.value && val) {
      autoplay.value = true;
    } else {
      autoplay.value = false;
    }
  };
  const editAdSwiper1 = (val) => {
    console.log("开始或者暂停", val);
    if (isInViewport1.value && val) {
      autoplay1.value = true;
    } else {
      autoplay1.value = false;
    }
  };
  const bigMarkertSwiper1 = ref(window.localStorage.getItem("bigMarkertSwiper1Index"))
  const getList = async () => {
    console.log(props.addressData, "propspropsaddressDataaddressData");
    const res1 = await adfrontadmarketshow({
      place_type: 13
    });
    placeType1.value = res1.data.data.list;
    const res2 = await adfrontadmarketshow({
      place_type: 16,
    });
    placeType2.value = res2.data.data.list;

    const res3 = await adfrontadmarketshow({
      place_type: 15,
    });
    const res4 = await adfrontadmarketshow({
      place_type: 14,
    });
    placeType3.value = res3.data.data.list;
    placeType4.value = res4.data.data.list;
    console.log(placeType3.value, 'placeType1placeType1');
    console.log(placeType2.value, 'placeType1placeType1placeType1');
    console.log(placeType1.value, 'placeType1placeType1placeType1placeType1');
    console.log(placeType4.value, 'placeType1placeType1placeType1placeType1');

  };
  // watch(
  //   () => route.path,
  //   () => {
  //     console.log(" route.path", route.path);
  //     if (route.path === "/bigMarketIndex/home" && route.query?.refresh) {
  //       console.log("homeRefresh");
  //     }
  //   },
  // );
  watch(
    () => props.addressData,
    () => {
      getList();
      console.log(props.addressData, "props.addressDataprops.addressData");
    },
  );
  watchEffect(() => {
    if (!isInViewport.value) {
      // 不在可视区时
      autoplay.value = false;
    } else {
      autoplay.value = true;
    }
    console.log(route, "路由");
    console.log(isInViewport.value, "再可视区吗");
  });
  defineExpose({
    editAdSwiper,
  });
</script>
<style lang="less" scoped>
  .not-dian {
    :deep(.t-swiper__navigation) {
      display: none;
    }
  }

  .guanggao {
    position: relative;
  }

  .guanggao::after {
    content: "广告";
    position: absolute;
    width: 32px;
    height: 20px;
    z-index: 999;
    right: -1px;
    top: -1px;
    border-radius: 0px 8px;
    background: #F5F8FE;
    color: #ACB3C0;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    text-align: center;
    padding: 0 4px;
  }

  :deep(.t-swiper__content) {
    border-radius: 8px !important;
  }

  .advertising-space {
    display: flex;
    gap: 12px;
    height: 308px;
    -webkit-user-select: none;
    /* Chrome/Safari */
    -moz-user-select: none;
    /* Firefox */
    -ms-user-select: none;
    /* IE10+ */
    user-select: none;
    /* Standard */
  }

  .swiper-ad-img1 {
    object-fit: cover;
    width: 232px;
    height: 308px;
    transform: translateZ(0);
    /* border-radius: 8px; */
  }

  .swiper-ad-img {
    object-fit: cover;
    width: 548px;
    height: 308px;
    transform: translateZ(0);
    /* border-radius: 8px; */
  }

  .big-ad-img-box {
    display: flex;
    flex-wrap: wrap;
    cursor: pointer;
    gap: 12px;

    img {
      width: 200px;
      height: 150px;
      border-radius: 8px;
      flex-shrink: 0;
    }
  }

  .big-ad-swiper {
    width: 548px;
    height: 308px;
  }

  .big-ad-swiper1 {
    width: 232px;
    height: 308px;
  }

  .swiper-box {
    border-radius: 8px;
    min-width: 232px;
    height: 308px;
    background: url("@/assets/member/imgnotdata.svg") 100% no-repeat;
  }

  .swiper-box {
    border-radius: 8px;
    min-width: 548px;
    height: 308px;
    background: url("@/assets/member/imgnotdata.svg") 100% no-repeat;
  }

  :deep(.t-swiper__arrow-left) {
    width: 32px;
    height: 32px;
    background: var(--icon-kyy_color_icon_transparent, rgba(26, 33, 57, 0.36));
    border-radius: 50%;
    color: #fff;
  }

  :deep(.t-swiper__arrow-right) {
    width: 32px;
    height: 32px;
    color: #fff;
    border-radius: 50%;
    background: var(--icon-kyy_color_icon_transparent, rgba(26, 33, 57, 0.36));
  }
  :global(.big-ad-swiper .t-swiper__navigation-item){
    scale: 2;
    margin: 0 8px;
  }
  :global(.big-ad-swiper1 .t-swiper__navigation-item){
    scale: 2;
    margin: 0 8px;
  }
  
</style>