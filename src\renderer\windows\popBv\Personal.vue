<template>
  <Card :bordered="false" :class="['personal-card', props.showOtherType === 'changeAccount' ? 'changeAccount' : '']">
    <template #content>
      <div class="Epersonal-content">
        <div class="fvc" @click="viewCard">
          <avatar :image-url="imageUrl" :user-name="userName" :round-radius="true" shape="circle" avatar-size="44px" />
          <div class="user-name ellipsis-3">{{ userName }}</div>
        </div>
        <div class="cursor"  v-if="!notShowCodeVerification" @click="changeCodeVerification">
          <iconpark-icon name="iconverification" class="icon"></iconpark-icon>
          {{ t("account.codeVerification") }}
        </div>
        <div class="cursor" @click="myOrder">
          <iconpark-icon name="iconorder" class="icon"></iconpark-icon>
          {{ t("account.orders") }}
        </div>
        <div class="cursor" @click="myAddress">
          <iconpark-icon name="iconlocal2" class="icon"></iconpark-icon>
          {{ t("member.talk.address") }}
        </div>
        <div class="cursor" @click="myInvoice">
          <iconpark-icon name="icontransaction" class="icon"></iconpark-icon>
          {{ t("zx.account.invoiceCenter") }}
        </div>
        <div class="cursor" @click="myHelp">
          <iconpark-icon name="iconhelp" class="icon"></iconpark-icon>
          {{ t('member.bing.a') }}
        </div>
        <div class="cursor" @click="changeAccount">
          <iconpark-icon name="iconpeople" class="icon"></iconpark-icon>
          {{ t("account.changeAccount") }}
        </div>
        <div class="cursor" @click="openSetting">
          <iconpark-icon name="iconsetUp" class="icon"></iconpark-icon>
          {{ t("account.preference") }}
        </div>

        <div class="line " />
        <div class="cursor back" @click="quitAllAccount">
          <iconpark-icon name="iconexit" class="icon"></iconpark-icon>
          {{ t("account.logout") }}
        </div>
      </div>
    </template>
  </Card>
</template>

<script setup lang="ts">
  import { ref, onMounted, computed } from "vue";
  import { Card, DialogPlugin, MessagePlugin } from "tdesign-vue-next";
  import {
    getProfilesInfo,
    getOpenid,
    removeAppTeamId,
    setProfilesInfo,
    removeAccesstoken,
    emptyAndQuit,
    removeStore
  } from "@renderer/utils/auth";
  import { removeProjectTeamID } from "@renderer/views/engineer/utils/auth";
  import { useI18n } from "vue-i18n";
  import {
    removeMember,
    removeMemberTeamID
  } from "@renderer/views/member/utils/auth";
  import avatar from "@renderer/components/kyy-avatar/index.vue";
  import LynkerSDK from '@renderer/_jssdk';

  const { ipcRenderer, shell } = LynkerSDK;
  // import { msgJumpToCloudDisk } from '/service/msgUtils';
  const isMas = __APP_ENV__.VITE_APP_MAS;
  const props = defineProps({
    showOtherType: {
      type: String,
      default: '',
    },
  });


  const imageUrl = ref(getProfilesInfo()?.avatar);
  const userName = ref(getProfilesInfo()?.title);
  const account_mobile = ref(getProfilesInfo()?.account_mobile);
  ipcRenderer.on("refresh-profiles-info", (event, arg) => {
    imageUrl.value = arg?.avatar || getProfilesInfo()?.avatar;
    userName.value = arg?.title || getProfilesInfo()?.title;
    setProfilesInfo({
      ...getProfilesInfo(),
      avatar: imageUrl.value,
      title: userName.value
    });
  });
  const { t } = useI18n();
  const notShowCodeVerification = computed(() => {
    return isMas && ['***********', '***********'].includes(account_mobile.value);
  });
  const changeCodeVerification = async () => {
    await  ipcRenderer.invoke('set-popbv', { show: false });

    ipcRenderer.invoke('open-activation-code')
  }
  const myOrder = () => {
    LynkerSDK.openMyOrderWindow({teamId:''});
    // ipcRenderer.invoke(
    //   "my-order",
    //   JSON.stringify({
    //     name: 23123
    //   })
    // );
  };
  const myAddress = () => {
    LynkerSDK.openMyAddressWindow();
    // ipcRenderer.invoke("my-invoice");
  };

  const myInvoice = () => {
    LynkerSDK.openMyInvoiceWindow();
    // ipcRenderer.invoke("my-invoice");
  };
  const myHelp = async () => {
    // const data = await LynkerSDK.openSelectMember();
    // alert(`${data} 00000`)
    LynkerSDK.openMyHelpWindow();
    // ipcRenderer.invoke("my-help");
    // ipcRenderer.invoke("my-help", { id: 66, module_id: 27, directory_id: 67 });
  };

  onMounted(() => {
    userName.value = getProfilesInfo()?.title || '';
    imageUrl.value = getProfilesInfo()?.avatar || '';
    account_mobile.value = getProfilesInfo()?.account_mobile;
  });

  const openSetting = () => {
    // LynkerSDK.openSettingWindow();
    ipcRenderer.invoke("open-setting");
  };
  const changeAccount = () => {
    ipcRenderer.invoke('set-popbv', { show: false });
    ipcRenderer.invoke('set-popbv-other', { show: true, type: 'changeAccount', data: { dialogType: 'changeAccountVisible' } });
  };
  const quitAllAccount = () => {
    ipcRenderer.invoke('set-popbv', { show: false });
    ipcRenderer.invoke('set-popbv-other', { show: true, type: 'changeAccount', data: { dialogType: 'logoutVisible' } });
  };
  const viewCard = () => {
    ipcRenderer.invoke('set-popbv', { show: false });
    ipcRenderer.invoke("identity-card", {
      cardId: getOpenid(),
      myId: getOpenid(),
      showMoreCard: "showMoreCard"
    });
  };
</script>

<style lang="less" scoped>
  .Epersonal-content {
    color: var(--text-kyy_color_text_1, #1A2139);
  }

  .cursor {
    cursor: pointer;
  }

  .icon {
    width: 20px;
    height: 20px;
    font-size: 20px;
    color: var(--icon-kyy-color-icon-deepest, #1A2139);
  }

  .personal-card {
    position: fixed;
    left: 2px;
    top: 2px;
    width: 224px;
    z-index: 999999;
    border-radius: 8px;
    background: linear-gradient(135deg, #72EDF2 0%, #5151E5 100%);

    // box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.12);
    :deep(.t-card__body) {
      padding: 8px;
    }

    .user-name {
      font-size: 16px;
      font-weight: 700;
      line-height: 24px;
      margin-left: 10px;
    }

  }

  .line {
    width: 208px;
    height: 1px;
    background: var(--divider-kyy_color_divider_light, #ECEFF5);
  }

  .fvc {
    display: flex;
    align-items: center;
    cursor: pointer;
    margin-bottom: 12px;
    border-radius: 4px;
    padding: 4px;
  }

  .cursor {
    width: 100%;
    line-height: 24px;
    display: flex;
    padding: 10px 8px;
    align-items: center;
    gap: 8px;
    align-self: stretch;
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.60);
    box-shadow: 0px 2px 4px 0px rgba(77, 94, 255, 0.15);
    margin-bottom: 4px;
    font-size: 16px;
  }

  .cursor:hover {
    background: rgba(255, 255, 255, 0.80);
  }

  .cursor:active {
    background-color: rgba(225, 234, 255, 1);
  }

  .back {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: rgba(213, 73, 65, 1);
    margin-top: 4px;

    .icon {
      color: rgba(213, 73, 65, 1);
    }
  }
</style>
