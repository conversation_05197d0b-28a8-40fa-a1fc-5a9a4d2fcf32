{"name": "ringkol-desktop", "version": "3.2.12", "main": "./dist/electron/main/main.js", "license": "MIT", "encryptionLevel": 0, "scripts": {"preinstall": "npx only-allow yarn", "prepare": "husky", "build:sdk:zip": "npx only-allow yarn", "build:sdk:unzip": "npx only-allow yarn", "zip": "node .electron-vite/create-zip.ts", "lint": "eslint ./src --cache", "lint:fix": "eslint ./src --cache --fix", "dev:only:renderer": "cross-env __ONLY_RUN_RENDERER__=YES esno .electron-vite/dev-runner.ts -m development", "dev:only:main": "cross-env __ONLY_RUN_MAIN__=YES esno .electron-vite/dev-runner.ts -m development", "qa:only:renderer": "cross-env __ONLY_RUN_RENDERER__=YES esno .electron-vite/dev-runner.ts -m test", "qa:only:main": "cross-env __ONLY_RUN_MAIN__=YES esno .electron-vite/dev-runner.ts -m test", "pre:only:renderer": "cross-env __ONLY_RUN_RENDERER__=YES esno .electron-vite/dev-runner.ts -m pre", "pre:only:main": "cross-env __ONLY_RUN_MAIN__=YES esno .electron-vite/dev-runner.ts -m pre", "prod:only:renderer": "cross-env __ONLY_RUN_RENDERER__=YES esno .electron-vite/dev-runner.ts -m production", "prod:only:main": "cross-env __ONLY_RUN_MAIN__=YES esno .electron-vite/dev-runner.ts -m production", "prod:only:renderer:mas": "cross-env __ONLY_RUN_RENDERER__=YES esno .electron-vite/dev-runner.ts -m production mas", "prod:only:main:mas": "cross-env __ONLY_RUN_MAIN__=YES esno .electron-vite/dev-runner.ts -m production mas", "dev": "esno .electron-vite/dev-runner.ts -m development", "qa": "esno .electron-vite/dev-runner.ts -m test", "pre": "cross-env esno .electron-vite/dev-runner.ts -m pre", "prod": "cross-env esno .electron-vite/dev-runner.ts -m production", "dev:mas": "esno .electron-vite/dev-runner.ts -m development mas", "qa:mas": "esno .electron-vite/dev-runner.ts -m test mas", "svgo": "svgo -r -f src/renderer/assets -o src/renderer/assets --config svgo.config.js", "compile": "cross-env BUILD_TARGET=clean esno .electron-vite/build.ts", "winSignature": "node ./signature.ts", "只打包渲染进程 -【开发】": "", "build:renderer:dev": "cross-env NODE_OPTIONS=--max-old-space-size=8096 BUILD_TARGET=clean esno .electron-vite/build.ts -m development", "只打包渲染进程 -【测试】": "", "build:renderer:qa": "cross-env NODE_OPTIONS=--max-old-space-size=8096 BUILD_TARGET=clean esno .electron-vite/build.ts -m test", "只打包渲染进程 -【预发布】": "", "build:renderer:pre": "cross-env NODE_OPTIONS=--max-old-space-size=8096 BUILD_TARGET=clean esno .electron-vite/build.ts -m pre", "只打包渲染进程 -【生产】": "", "build:renderer:pred": "cross-env NODE_OPTIONS=--max-old-space-size=8096 BUILD_TARGET=clean esno .electron-vite/build.ts -m production", "只打包主进程 -【mac】": "", "build:main:mac": "electron-builder -c build.json --mac --universal", "只打包主进程 -【win】": "", "build:main:win": "electron-builder -c build.json --win --x64", "build": "cross-env NODE_OPTIONS=--max-old-space-size=8096 BUILD_TARGET=clean esno .electron-vite/build.ts -m production && electron-builder -c build.json", "build:win32": "cross-env NODE_OPTIONS=--max-old-space-size=8096 BUILD_TARGET=clean esno .electron-vite/build.ts  && electron-builder -c build.json --win  --ia32", "build:win32:dev": "cross-env NODE_OPTIONS=--max-old-space-size=8096 BUILD_TARGET=clean esno .electron-vite/build.ts  && electron-builder -c build.json --win  --ia32", "build:win32:pre": "cross-env NODE_OPTIONS=--max-old-space-size=8096 BUILD_TARGET=clean esno .electron-vite/build.ts -m pre  && electron-builder -c build.json --win  --ia32", "build:win32:prod": "cross-env NODE_OPTIONS=--max-old-space-size=8096 BUILD_TARGET=clean esno .electron-vite/build.ts  && electron-builder -c build.json --win  --ia32", "build:win64": "cross-env NODE_OPTIONS=--max-old-space-size=8096 BUILD_TARGET=clean esno .electron-vite/build.ts -m development && electron-builder -c build.json --win  --x64", "build:win64:dev": "cross-env NODE_OPTIONS=--max-old-space-size=8096 BUILD_TARGET=clean esno .electron-vite/build.ts -m development && electron-builder -c build.json --win  --x64", "build:win64:qa": "cross-env NODE_OPTIONS=--max-old-space-size=8096 BUILD_TARGET=clean esno .electron-vite/build.ts -m test && electron-builder -c build.json --win  --x64", "build:win64:test": "cross-env NODE_OPTIONS=--max-old-space-size=8096 BUILD_TARGET=clean esno .electron-vite/build.ts -m test && electron-builder -c build.json --win  --x64", "build:win64:pre": "cross-env NODE_OPTIONS=--max-old-space-size=8096 BUILD_TARGET=clean  esno .electron-vite/build.ts -m pre && electron-builder -c build.json --win  --x64", "build:win64:prod": "cross-env NODE_OPTIONS=--max-old-space-size=8096 BUILD_TARGET=clean esno .electron-vite/build.ts -m production && electron-builder -c build.json --win  --x64", "build:mac": "cross-env NODE_OPTIONS=--max-old-space-size=8096 BUILD_TARGET=clean esno .electron-vite/build.ts -m development && electron-builder -c build.json --mac --universal", "build:mac:dev": "cross-env NODE_OPTIONS=--max-old-space-size=8096 BUILD_TARGET=clean esno .electron-vite/build.ts -m development && electron-builder -c build.json --mac --universal", "build:mac:qa": "cross-env NODE_OPTIONS=--max-old-space-size=8096 BUILD_TARGET=clean esno .electron-vite/build.ts -m test && electron-builder -c build.json --mac --universal", "build:mac:test": "cross-env NODE_OPTIONS=--max-old-space-size=8096 BUILD_TARGET=clean esno .electron-vite/build.ts -m test && electron-builder -c build.json --mac --universal", "build:mac:pre": "cross-env NODE_OPTIONS=--max-old-space-size=8096 BUILD_TARGET=clean esno .electron-vite/build.ts -m pre && electron-builder -c build.json --mac --universal", "build:mac:prod": "cross-env NODE_OPTIONS=--max-old-space-size=8096 BUILD_TARGET=clean esno .electron-vite/build.ts -m production && electron-builder -c build.json --mac --universal", "build:mas": "cross-env NODE_OPTIONS=--max-old-space-size=8096 BUILD_TARGET=clean esno .electron-vite/build.ts -m production mas && electron-builder -c masbuild.json --mac --universal", "build:mas:dev": "cross-env NODE_OPTIONS=--max-old-space-size=8096 BUILD_TARGET=clean esno .electron-vite/build.ts -m test mas && electron-builder -c masbuild.json --mac --universal", "build:mas:test": "cross-env NODE_OPTIONS=--max-old-space-size=8096 BUILD_TARGET=clean esno .electron-vite/build.ts -m test mas && electron-builder -c masbuild.json --mac --universal", "build:dir": "cross-env BUILD_TARGET=clean esno .electron-vite/build.ts  && electron-builder -c build.json --dir", "build:clean": "cross-env BUILD_TARGET=onlyClean esno .electron-vite/build.ts", "build:web": "cross-env NODE_OPTIONS=--max-old-space-size=8096 BUILD_TARGET=web esno .electron-vite/build.ts", "build:all:dev": "cross-env NODE_OPTIONS=--max-old-space-size=8096 BUILD_TARGET=clean esno .electron-vite/build.ts -m development && electron-builder -c build.json --win  --x64 && electron-builder -c build.json --mac --universal", "build:all:qa": "cross-env NODE_OPTIONS=--max-old-space-size=8096 BUILD_TARGET=clean esno .electron-vite/build.ts -m test && electron-builder -c build.json --win  --x64 && electron-builder -c build.json --mac --universal", "build:all:test": "cross-env NODE_OPTIONS=--max-old-space-size=8096 BUILD_TARGET=clean esno .electron-vite/build.ts -m test && electron-builder -c build.json --win  --x64 && electron-builder -c build.json --mac --universal", "build:all:pre": "cross-env NODE_OPTIONS=--max-old-space-size=8096 BUILD_TARGET=clean esno .electron-vite/build.ts -m pre && electron-builder -c build.json --win  --x64 && electron-builder -c build.json --mac --universal", "build:all:only:main": "electron-builder -c build.json --win --x64 && electron-builder -c build.json --mac --universal", "pack:resources": "esno .electron-vite/hot-updater.ts", "pack:rustUpdater": "electron_updater_node_cli -p -c updateConfig.json", "dep:upgrade": "yarn upgrade-interactive --latest", "postinstall": "node ./.electron-vite/patch-package.js && electron-builder install-app-deps", "re-sqlite": "electron-rebuild -f -w better-sqlite3", "electron:generate-icons": "electron-icon-builder --input=./public/icon.png --output=build --flatten", "release": "node ./scripts/release/index.js", "stats": "node ./scripts/stats.js", "docs:sidebar": "ts-node docs/.vitepress/scripts/genSidebar.ts", "docs:dev": "pnpm docs:sidebar && vitepress dev docs", "docs:build": "pnpm docs:sidebar && vitepress build docs"}, "dependencies": {"@lynker-desktop/devtron": "0.0.0-alpha.9", "@lynker-desktop/electron-ipc": "0.0.8-alpha.8", "@lynker-desktop/electron-sdk": "0.0.8-alpha.8", "@rk/editor": "0.4.6", "@rk/im-sdk": "1.2.2", "@rk/unitPark": "0.1.1-beta.32", "@tanstack/vue-query": "4.36.1", "archiver": "^7.0.1", "axios": "^1.3.4", "better-sqlite3": "8.7.0", "devtron": "^1.4.0", "driver.js": "^1.3.1", "electron-devtools-vendor": "1.2.0", "electron-log": "^4.4.8", "electron-screenshots": "0.5.27", "electron-store": "^8.1.0", "electron-updater": "6.3.9", "exif-js": "^2.3.0", "hotkeys-js": "^3.13.7", "less": "^4.1.3", "lunar-calendar": "^0.1.4", "lunar-javascript": "^1.6.4", "nodejs-file-downloader": "^4.13.0", "raf": "^3.4.1", "rk-anchorme": "^3.0.9", "rollup": "^3.20.2", "sudo-prompt": "^9.2.1", "svgaplayerweb": "^2.3.2", "systeminformation": "^5.25.11", "tiff.js": "^1.0.0", "tree-kill": "^1.2.2", "vue-request": "^2.0.4", "vue3-baidu-map-gl": "^2.6.4"}, "devDependencies": {"@amap/amap-jsapi-types": "^0.0.13", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@better-scroll/core": "^2.5.1", "@better-scroll/mouse-wheel": "^2.5.1", "@better-scroll/scroll-bar": "^2.5.1", "@electron/notarize": "^2.3.0", "@electron/rebuild": "^3.2.12", "@floating-ui/vue": "^1.0.1", "@fullcalendar/core": "^6.1.8", "@fullcalendar/daygrid": "^6.1.8", "@fullcalendar/interaction": "^6.1.8", "@fullcalendar/list": "^6.1.8", "@fullcalendar/moment": "^6.1.8", "@fullcalendar/multimonth": "^6.1.8", "@fullcalendar/timegrid": "^6.1.8", "@fullcalendar/vue3": "^6.1.8", "@mapbox/node-pre-gyp": "^1.0.10", "@rollup/plugin-alias": "^4.0.3", "@rollup/plugin-commonjs": "^24.0.1", "@rollup/plugin-json": "^6.0.0", "@rollup/plugin-node-resolve": "^15.0.1", "@rollup/plugin-replace": "^5.0.2", "@types/ali-oss": "^6.16.7", "@types/better-sqlite3": "^7.6.4", "@types/fs-extra": "^11.0.1", "@types/md5": "^2.3.5", "@types/node": "^18.14.2", "@types/qs": "^6.9.16", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^4.29.3", "@typescript-eslint/parser": "^4.29.3", "@umengfe/apm": "^2.0.11", "@unocss/preset-rem-to-px": "^0.51.8", "@videojs-player/vue": "^1.0.0", "@vitejs/plugin-vue": "^4.0.0", "@vitejs/plugin-vue-jsx": "^3.0.0", "@vue/compiler-sfc": "^3.0.5", "@vue/eslint-config-typescript": "^11.0.0", "@vuemap/vue-amap": "2.0.13", "@vueuse/components": "^10.7.0", "@vueuse/core": "^10.2.1", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "@wangeditor/plugin-mention": "^1.0.0", "adm-zip": "^0.5.10", "ali-oss": "^6.17.1", "await-to-js": "^3.0.0", "cfonts": "^3.1.1", "chalk": "5.2.0", "cropperjs": "^1.6.1", "cross-env": "^7.0.3", "crypto-js": "^4.1.1", "dayjs": "^1.11.10", "del": "^7.0.0", "dotenv": "^16.0.3", "echarts": "^5.6.0", "electron": "24.8.8", "electron-builder": "^25.1.8", "electron-notarize": "^1.2.2", "electron_updater_node_cli": "^0.2.0", "electron_updater_node_core": "^0.3.3", "eslint": "^7.32.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.24.2", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^9.2.0", "eslint-plugin-vue-scoped-css": "^2.2.0", "esno": "^0.16.3", "express": "^4.18.2", "extract-zip": "^2.0.1", "fs-extra": "^11.1.0", "html-to-image": "^1.11.11", "html-to-text": "^9.0.5", "html2canvas": "^1.4.1", "husky": "^9.1.6", "iconv-lite": "^0.6.3", "inquirer": "8.2.5", "javascript-obfuscator": "^4.0.2", "js-md5": "^0.7.3", "lint-staged": "^15.2.10", "listr2": "^5.0.7", "lodash": "^4.17.21", "minimist": "^1.2.8", "mitt": "^3.0.1", "moment": "^2.29.4", "mp4box": "^0.5.2", "node-gyp": "^9.4.0", "patch-package": "^8.0.0", "pinia": "2.0.32", "pinia-plugin-persistedstate": "^3.1.0", "pinyin-pro": "^3.18.5", "portfinder": "^1.0.32", "prettier": "^2.4.1", "qrcode-parser": "^2.1.3", "qrcode.vue": "^3.4.0", "qs": "^6.11.2", "rollup-plugin-esbuild": "^5.0.0", "rollup-plugin-obfuscator": "^1.0.3", "rollup-plugin-visualizer": "^6.0.3", "sass": "^1.58.3", "semver": "^7.7.1", "string-width": "4.2.3", "svg-baker-runtime": "^1.4.7", "svgo": "^3.0.2", "tdesign-icons-vue-next": "^0.2.2", "tdesign-vue-next": "1.9.8", "tslib": "^2.5.0", "tvision-color": "^1.3.1", "typescript": "~4.8.4", "unocss": "0.51.12", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.7.0", "uuid": "^9.0.0", "video.js": "^8.3.0", "vite": "4.5.5", "vite-plugin-eslint": "^1.8.1", "vite-plugin-html": "^3.2.2", "vite-plugin-pwa": "^0.20.5", "vite-plugin-static-copy": "^1.0.0", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-setup-extend": "^0.4.0", "vue": "3.2.47", "vue-clipboard3": "^2.0.0", "vue-cropper": "^1.0.8", "vue-i18n": "^9.2.2", "vue-qr": "^4.0.9", "vue-router": "^4.1.6", "vue-tippy": "v6", "vue3-draggable-resizable": "^1.6.5", "vue3-emoji-picker": "^1.1.7", "vue3-infinite-scroll-better": "^2.2.0", "vue3-lazy": "^1.0.0-alpha.1", "vue3-tree-org": "^4.2.2", "vuedraggable": "^4.1.0", "wc-waterfall": "^0.1.1"}, "resolutions": {"vue": "3.2.47", "wrap-ansi": "7.0.0", "string-width": "4.2.3"}, "lint-staged": {"src/**/*.{js,ts,jsx,tsx,vue}": "npx eslint --cache --fix"}, "engines": {"node": ">=18.0.0 <21.0.0", "yarn": ">=1.22.0"}}