{"name": "newweb", "version": "0.6.0", "scripts": {"dev:mock": "vite --open --mode mock", "dev": "vite --open --mode  development", "qa": "cross-env  VITE_IM_ENV=QA vite --open --mode  development", "prod": "cross-env  VITE_IM_ENV=PROD vite --open --mode  development", "dev:linux": "vite --mode development", "mock": "vite --open --mode mock", "build:test": "cross-env NODE_OPTIONS=--max_old_space_size=10240 vite build", "build:render": "vite build", "build": "rimraf dist && npm run build:render", "preview": "vite preview", "lint": "eslint --ext .vue,.js,.jsx,.ts,.tsx ./ --max-warnings 0", "lint:fix": "eslint --ext .vue,.js,jsx,.ts,.tsx ./ --max-warnings 0 --fix", "stylelint": "stylelint src/**/*.{html,vue,sass,less}", "stylelint:fix": "stylelint --fix src/**/*.{html,vue,vss,sass,less}", "prepare": "node -e \"if(require('fs').existsSync('.git')){process.exit(1)}\" || is-ci || husky install", "site:preview": "npm run build && cp -r dist _site", "test": "echo \"no test specified,work in process\"", "test:coverage": "echo \"no test:coverage specified,work in process\"", "build:jenkins:test": "cross-env NODE_OPTIONS=--max_old_space_size=10240  VITE_BUILD_ENV=JENKINS vite build", "build:jenkins:pre": "cross-env  VITE_IM_ENV=PRE VITE_BUILD_ENV=JENKINS vite build", "build:jenkins:prod": "cross-env  VITE_IM_ENV=PROD VITE_BUILD_ENV=JENKINS vite build", "build:pre": "cross-env NODE_OPTIONS=--max_old_space_size=10240 VITE_IM_ENV=PRE vite build", "build:prod": "cross-env NODE_OPTIONS=--max_old_space_size=10240 VITE_IM_ENV=PROD vite build", "build:qa": "cross-env NODE_OPTIONS=--max_old_space_size=10240 VITE_IM_ENV=QA vite build"}, "resolutions": {"vue": "3.3.4"}, "dependencies": {"@ant-design/icons-vue": "^7.0.0", "@rk/editor": "0.3.2-alpha.10", "@rk/unitPark": "0.1.2-beta.10", "@videojs-player/vue": "^1.0.0", "@vueup/vue-quill": "^1.2.0", "@vueuse/core": "^10.11.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "ali-oss": "^6.17.1", "await-to-js": "^3.0.0", "axios": "^1.1.3", "axios-retry": "^3.4.0", "clipboard-all": "^1.0.8", "cropperjs": "^1.6.1", "crypto-js": "^4.2.0", "date-fns": "^3.6.0", "dayjs": "^1.11.7", "default-passive-events": "^2.0.0", "echarts": "~5.1.2", "exceljs": "^4.4.0", "html2canvas": "^1.4.1", "js-md5": "^0.7.3", "jszip": "^3.10.1", "jszip-utils": "^0.1.0", "lodash": "^4.17.21", "lunar-javascript": "^1.6.4", "moment": "^2.29.4", "mp4box": "^0.5.3", "nprogress": "^0.2.0", "pinia": "^2.1.4", "pinia-plugin-persistedstate": "^3.0.1", "qrcode-parser": "^2.1.3", "qrcode.vue": "^3.3.3", "qs": "^6.11.2", "resize-observer-polyfill": "^1.5.1", "tdesign-icons-vue-next": "^0.1.11", "tdesign-vue-next": "1.9.7", "tippy.js": "^6.3.7", "tvision-color": "^1.3.1", "uuid": "^9.0.0", "video.js": "^7.21.5", "vite-plugin-svg-icons": "^2.0.1", "vue": "3.3.4", "vue-clipboard3": "^2.0.0", "vue-cropper": "^1.0.8", "vue-i18n": "9.2.2", "vue-quill": "^1.5.1", "vue-router": "~4.1.5", "vue-tippy": "^6.4.1", "vuedraggable": "^4.1.0", "vuex-persistedstate": "^4.1.0", "yaml": "^2.6.1"}, "devDependencies": {"@amap/amap-jsapi-types": "^0.0.13", "@types/ali-oss": "^6.16.8", "@types/echarts": "^4.9.10", "@types/lodash": "^4.14.182", "@types/qs": "^6.9.7", "@types/ws": "^8.2.2", "@typescript-eslint/eslint-plugin": "^4.29.3", "@typescript-eslint/parser": "^4.29.3", "@unocss/preset-rem-to-px": "^0.53.1", "@vitejs/plugin-vue": "^2.3.1", "@vitejs/plugin-vue-jsx": "^1.1.7", "@vue/compiler-sfc": "^3.3.4", "@vue/eslint-config-typescript": "^11.0.0", "commitizen": "^4.2.4", "cross-env": "^7.0.3", "cz-conventional-changelog": "^3.3.0", "drag-tree-table": "^2.2.0", "eslint": "^7.32.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.24.2", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^9.2.0", "eslint-plugin-vue-scoped-css": "^2.2.0", "husky": "^8.0.3", "less": "^4.1.3", "lint-staged": "^12.1.2", "prettier": "^2.4.1", "stylelint": "~13.13.1", "stylelint-config-prettier": "~9.0.3", "stylelint-less": "1.0.1", "stylelint-order": "~4.1.0", "typescript": "~4.8.4", "unocss": "^0.53.1", "vite": "4.2.3", "vite-svg-loader": "^3.1.0", "vue-tsc": "^1.8.3"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"*.{js,jsx,vue,ts,tsx}": ["prettier --write", "npm run lint:fix", "git add ."], "*.{html,vue,vss,sass,less}": ["npm run stylelint:fix", "git add ."]}, "description": "Base on tdesign-starter-cli"}