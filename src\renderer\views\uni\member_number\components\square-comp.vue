<template>
  <t-loading size="medium" class="memberLoading" :loading="isLoading" show-overlay text="加载中...">
    <div class="page">
      <div class="comp">
        <div class="square">
          <div class="search">
            <t-input
              v-model="keyword"
              :placeholder="$t('member.second.q')"
              :maxlength="50"
              clearable
              style="width: 304px"
              @change="onSearch"
            >
              <template #prefix-icon>
                <iconpark-icon name="iconsearch-a961a3le" class="iconsearch"></iconpark-icon>
              </template>
            </t-input>
          </div>

          <div class="conbox mt-24px">
            <div
              v-for="(menu, menuIndex) in memberList"
              :key="menuIndex"
              class="item cursor"
              @click="onGoSquarePage(menu)"
            >
              <div class="public">
                <span class="logo">
                  <img
                    v-if="menu?.square?.squareType === 'INDIVIDUAL'"
                    v-lazy="getSrcLogo(menu?.square?.avatar)"
                    class="img"
                  />
                  <img
                    v-else
                    v-lazy="menu?.square?.avatar ? getSrcLogo(menu?.square?.avatar) : ORG_DEFAULT_AVATAR"
                    class="img"
                  />
                </span>
                <div class="right">
                  <span class="lin">
                    <span class="star">
                      <iconpark-icon
                        v-if="menu?.square?.squareType === SquareType.Enterprise"
                        name="iconenterprise"
                        class="star-icon"
                      ></iconpark-icon>
                      <iconpark-icon
                        v-else-if="menu?.square?.squareType === SquareType.BusinessAssociation"
                        name="iconbusiness"
                        class="star-icon"
                      ></iconpark-icon>
                      <iconpark-icon
                        v-else-if="menu?.square?.squareType === SquareType.IndividualBusiness"
                        name="iconindividual"
                        class="star-icon"
                      ></iconpark-icon>
                      <iconpark-icon
                        v-else-if="menu?.square?.squareType === SquareType.Other"
                        name="iconother"
                        class="star-icon"
                      ></iconpark-icon>
                      <iconpark-icon
                        v-else-if="menu?.square?.squareType === SquareType.Government"
                        name="icongov"
                        class="star-icon"
                      ></iconpark-icon>
                    </span>
                    <span class="name line-1">
                      {{ menu?.square?.name }}
                    </span>
                  </span>
                  <span v-if="menu?.square?.squareType === 'INDIVIDUAL'" class="type person line-2">个人广场号</span>
                  <span v-else class="type line-1">{{ menu?.certInfo?.industry }}</span>
                  <div v-show="menu?.square?.squareType !== 'INDIVIDUAL'" class="bottom line-1">
                    {{ menu?.certInfo?.name }}
                  </div>
                </div>
                <template v-if="!(menu.square.squareType === SquareType.Individual && openId === menu.square.originId)">
                  <LikeButton
                    v-model="menu.followed"
                    style="height: 16px"
                    :square-name="menu.square.name"
                    :square-id="menu.square.squareId"
                    :selfOpened="squareResult?.selfOpened"
                    simple
                    @reload="onReload"
                  />
                </template>
              </div>
              <div class="visible" v-show="menu.square.squareType !== SquareType.Individual && !menu.connected">
                <div class="tip">隐藏中</div>
                <t-button class="btn" theme="primary">
                  <iconpark-icon class="iconpreciewopen" name="iconpreciewopen"></iconpark-icon>
                  <span class="text">展示到平台</span>
                </t-button>
              </div>
              <!-- <div class="hover">
                <div class="top">
                  <span class="logo">
                    <img v-lazy="menu?.square?.avatar" class="img">
                    <span class="star">
                      <iconpark-icon v-if="menu?.square?.squareType === 'ENTERPRISE'" name="iconenterprise" class="star-icon"></iconpark-icon>
                      <iconpark-icon v-else-if="menu?.square?.squareType === 'BUSINESS_ASSOCIATION'" name="iconbusiness" class="star-icon"></iconpark-icon>
                      <iconpark-icon v-else-if="menu?.square?.squareType === 'INDIVIDUAL_BUSINESS'" name="iconother" class="star-icon"></iconpark-icon>
                      <iconpark-icon v-else-if="menu?.square?.squareType === 'OTHER'" name="iconother" class="star-icon"></iconpark-icon>
                    </span>
                  </span>
                  <span class="name line-2 mt-12px">{{ menu?.square?.name }}</span>
                  <span v-if="menu?.square?.squareType === 'INDIVIDUAL'" class="type person line-2 mt-8px">个人广场号</span>
                  <span v-else class="type line-2 mt-8px">{{ menu?.certInfo?.industry }}</span>
                </div>
                <t-button
                  theme="default"
                  variant="outline"
                  class="bottom mt-12px"
                  @click="onGoSquarePage(menu)"
                >
                  {{ $t('member.long.org_2') }}
                </t-button>
              </div> -->
            </div>
          </div>
          <div v-show="memberList.length > 0" class="example-more mt-24px">
            <span v-if="!page || (page && page.nextPageToken)" class="more cursor" @click="onMore">
              {{ isLoading ? "加载中..." : "加载更多" }}
            </span>
            <span v-else-if="memberList.length > pageSize" class="noempty">
              <span class="line"></span><span class="toText">{{ $t("member.second.l") }}</span>
              <span class="line"></span>
            </span>
          </div>
          <template v-if="!isNetworkError">
            <div v-show="!isLoading && memberList.length < 1" class="noEmpty">
              <Empty :tip="'暂无广场号'" />
            </div>
          </template>
          <template v-else>
            <div v-show="!isLoading && memberList.length < 1" class="noEmpty">
              <Empty name="offline">
                <template #tip>
                  <div class="tipEmpty">
                    <span class="text">网络链接失败，请检查网络后重试</span>
                    <t-button theme="primary" class="btn" @click="onSearch">点击重试</t-button>
                  </div>
                </template>
              </Empty>
            </div>
          </template>
        </div>
      </div>
    </div>
  </t-loading>
  <!-- 套餐升级（传 upgrade 标识） -->
  <!-- <AnnualFeeDialog
    v-if="annualFeeDialogUpgradeVisible"
    v-model="annualFeeDialogUpgradeVisible"
    upgrade
    :team-id="squareTeamId"
    @success="upgradeLoaded"
  /> -->
  <AnnualConnect ref="annualConnectRef" :activeAccount="activeAccount" @backType="onBackType"></AnnualConnect>
  <Tricks :offset="{ x: '-32', y: '-40' }" uuid="数字高校-广场号" />
</template>

<script setup lang="ts">
import { Ref, computed, reactive, ref, toRaw, watch } from "vue";
import { getMemberSquaresAxios } from "@renderer/api/uni/api/businessApi";
import { getResponseResult } from "@renderer/utils/myUtils";
import Empty from "@renderer/components/common/Empty.vue";
import { useUniStore } from "@renderer/views/uni/store/uni";
import { toSquareHome } from "@/views/square/utils/ipcHelper";
import { useI18n } from "vue-i18n";
import { ORG_DEFAULT_AVATAR } from "@/views/square/constant";
import { getUniTeamID } from "@renderer/views/uni/utils/auth";
import { useApi } from "@renderer/views/uni/hooks/api";
import { onMountedOrActivated } from "@renderer/hooks/onMountedOrActivated";
import { useDigitalPlatformStore } from "@renderer/views/digital-platform/store/digital-platform-store";
import { useRoute } from "vue-router";
import { platform } from "@renderer/views/digital-platform/utils/constant";
import LikeButton from "@renderer/views/digital-platform/components/LikeButton.vue";
import { getSrcLogo } from "@renderer/views/message/service/msgUtils";
import { SquareType } from "@renderer/api/square/enums";
import { getOpenid } from "@renderer/utils/auth";
import { onDisplayToPlatform } from "@renderer/views/digital-platform/utils/auth";
// import AnnualFeeDialog from "@/views/square/components/annual-fee/AnnualFeeDialog.vue";
import { MessagePlugin } from "tdesign-vue-next";
import AnnualConnect from '@renderer/views/digital-platform/components/AnnualConnect.vue';

const openId = getOpenid();
const annualFeeDialogUpgradeVisible: Ref<any> = ref(false);
const { t } = useI18n();
const route = useRoute();
const keyword = ref("");
const store = useUniStore();
const { onActionSquare } = useApi();
const isLoading = ref(false);
const isNetworkError = ref(false);
const memberList = ref([]);
const page = ref(null);
const pageSize = ref(25);
const digitalPlatformStore = useDigitalPlatformStore();
const props = defineProps({
  platform: {
    type: String,
    default: "",
  },
});
// 平台类型 目前只有digital-platform
const platformCpt = computed(() => {
  return props.platform || route.query?.platform;
});

const currentTeamId = computed(() => {
  if (platformCpt.value === platform.digitalPlatform) {
    return digitalPlatformStore.activeAccount?.teamId;
  } else {
    return getUniTeamID();
  }
});

const squareResult = ref(null);
const onReload = () => {
  onGetMemberSquaresAxios(false, true);
};
const onGetMemberSquaresAxios = async (isCover = false, isOnlyReloadSelfOpened = false) => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor

  // 缓存信息存储
  const caches = store.getStorageDatas;
  const cache = caches.find((v) => v.teamId === getUniTeamID());

  if (cache) {
    // memberList.value = cache.memberSquare?.items || [];
    // page.value = cache.memberSquare?.page;
  } else {
    isLoading.value = true;
  }
  console.log("getStorageDatas");
  try {
    result = await getMemberSquaresAxios(
      {
        keyword: keyword.value,
        "page.size": pageSize.value,
        "page.next_page_token": page.value ? page.value.nextPageToken : "",
      },
      currentTeamId.value,
    );
    result = getResponseResult(result);
    isNetworkError.value = false;
    if (!result) {
      isLoading.value = false;
      return;
    }
    result.items.forEach((v) => {
      v.square.avatar = v.square.avatar ? getSrcLogo(v.square.avatar) : "";
    });
    squareResult.value = result;
    if (isOnlyReloadSelfOpened) {
      console.log(squareResult.value);
    } else {
      if (isCover) {
        memberList.value = result.items;
      } else {
        memberList.value = memberList.value.concat(result.items);
      }
    }

    page.value = result.page;
    // 缓存处理 start
    const memberSquare = {
      items: toRaw(memberList.value),
      page: result.page,
    };
    if (cache) {
      cache.memberSquare = memberSquare;
    } else {
      caches.push({ teamId: getUniTeamID(), memberSquare });
    }
    console.log("caches: ", caches);
    store.setStorageDatas(caches);
    // 缓存处理 end
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    //   MessagePlugin.error(errMsg);
    console.log("dong:", errMsg);
    if (errMsg === "Network Error") {
      isNetworkError.value = true;
    }
  }
  isLoading.value = false;
};

const onMore = () => {
  onGetMemberSquaresAxios();
};

// 升级后的回调
const upgradeLoaded = () => {
  // console.log('upgradeLoaded')
  // MessagePlugin.success('购买成功')
  onSearch();
  // 升级后重新触发流程
  // upgradePackageRef.value.onClose();
  // onRunTeamAnnualFee()
};


const annualConnectRef = ref(null);
// const selectOrganizeModalRef = ref(null)
const onBackType = (res) => {
  if(res === TeamAnnualTypeError.Success) {
    MessagePlugin.success('连接成功')
    // 更新详情里面的数据
    // if(isShowNameDetail.value) {
    //   onGetMemberNameDetail(detailData.value).then((res:any) => {
    //     console.log(res)
    //     nameDetailModelRef.value?.onOpen(res.data);
    //   });
    // }

    // onSearch();
  }
}


const squareTeamId = ref("");
const onGoSquarePage = (square) => {
  if (square.connected) {
    // 如果已经链接
    onActionSquare({}, square?.square?.squareId);
  } else {
    if (square?.square?.squareType === SquareType.Individual) {
      onActionSquare({}, square?.square?.squareId);
      return;
    };
    annualConnectRef.value.onConnectPlatform({
      relation_team_id: square?.square?.originId
    })
    // const param = {
    //   squareId: square?.square?.squareId,
    //   belong_team_id: currentTeamId.value,
    //   consume_team_id:  square?.square?.originId
    // }
    // onDisplayToPlatform(param, currentTeamId.value).then((res)=> {
    //   if(res === 'update') {
    //     console.log('update')
    //     squareTeamId.value = square?.square?.originId;
    //     annualFeeDialogUpgradeVisible.value = true;
    //   } else if(res === 'success'){
    //     MessagePlugin.success('连接成功')
    //     onSearch();
    //   }
    // }).catch((err)=> {
    //   console.log('err:', err)
    //   if(err && err.message) {
    //     MessagePlugin.error(err.message);
    //   }
    // })

  }
};
const onSearch = () => {
  console.log("onSearch");
  page.value = null;
  memberList.value = [];
  onGetMemberSquaresAxios();
};

const onLoadCache = () => {
  // 缓存信息存储
  const caches = store.getStorageDatas;
  const cache = caches.find((v) => v.teamId === getUniTeamID());
  if (cache) {
    memberList.value = cache.memberSquare?.items || [];
    // page.value = cache.memberSquare?.page;
  }
  page.value = null;
  onGetMemberSquaresAxios(true);
};
// onLoadCache();
// watch(
//   () => store.activeAccount,
//   async (val) => {
//     if (val) {
//       onSearch();
//     }
//   },
//   // {
//   //   // deep: true,
//   //   immediate: true
//   // }
// );

onMountedOrActivated(() => {
  onLoadCache();
  // onSearch();
});
</script>

<style lang="less" scoped>
@import "@renderer/views/engineer/less/common.less";
.memberLoading {
  height: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.page {
  display: flex;
  justify-content: center;
  width: 100%;
  height: inherit;
  padding: 16px;
  background: var(--bg-kyy_color_bg_deep, #f5f8fe);
  height: 100%;
}
.comp {
  // padding-top: 16px;
  // padding-bottom: 16px;
  height: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.square {
  padding: 16px;
  // min-height: 50vh;
  max-width: 1184px;
  min-width: 1088px;
  height: 100%;
  border-radius: 8px;
  background: var(--bg-kyy_color_bg_light, #fff);
  overflow-y: overlay;
  flex: 1;
  .search {
  }
  .conbox {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
    width: 100%;
    .item {
      // flex: 1;
      width: calc((100% - 36px) / 4);
      // height: 208px;
      height: 96px;
      border-radius: 8px;
      background: var(--bg-kyy_color_bg_deep, #f5f8fe);
      position: relative;
      transition: all 0.15s linear;

      .visible {
        position: absolute;
        left: 0;
        top: 0;
        right: 0;
        bottom: 0;
        z-index: 10px;
        border-radius: 8px;
        background: var(--bg-kyy_color_bg_mask, rgba(0, 0, 0, 0.5));
        display: flex;
        align-items: center;
        justify-content: center;
        .btn {
          display: flex;
          gap: 4px;
          width: 110px;
          .iconpreciewopen {
            font-size: 20px;
          }
          .text {
            color: var(--text-kyy_color_text_white, #fff);
            text-align: center;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
          }
        }
        .tip {
          border-radius: 8px 0px;
          background: var(--icon-kyy_color_icon_transparent, rgba(26, 33, 57, 0.36));

          position: absolute;
          left: 0;
          top: 0;
          padding: 0 4px;
          color: var(--text-kyy_color_text_white, #fff);
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          line-height: 20px; /* 166.667% */
        }
      }

      &:hover {
        /*.hover {
          opacity: 1;
        }*/
        border-radius: 8px;
        background: var(--bg-kyy_color_bg_default, #fff);
        /* kyy_shadow_m */
        box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.12);
        transition: all 0.15s linear;
      }
      .public {
        display: flex;
        // flex-direction: column;
        // justify-content: space-between;
        padding: 12px;
        align-items: center;
        height: 100%;
        gap: 12px;

        .logo {
          width: 48px;
          height: 48px;
          position: relative;
          .img {
            width: 48px;
            height: 48px;
            border-radius: 48px;
          }
        }

        .right {
          flex: 1;
          display: flex;
          flex-direction: column;
          // justify-content: space-between;
          // height: 100%;
          gap: 4px;
          width: 100%;

          .lin {
            display: flex;
            align-items: center;
            max-width: 160px;
            .star {
              // position: absolute;
              // bottom: -2px;
              // right: -3px;
              display: flex;
              align-items: center;
              &-icon {
                font-size: 24px;
              }
            }
            .name {
              color: #000;
              text-align: center;
              font-size: 14px;
              font-style: normal;
              font-weight: 600;
              line-height: 22px; /* 157.143% */
            }
          }

          .type {
            border-radius: var(--kyy_radius_tag_s, 4px);
            background: var(--kyy_color_tag_bg_kyyBlue, #e4f5fe);
            padding: 0 4px;
            color: var(--kyy_color_tag_text_kyyBlue, #21acfa);
            text-align: center;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px; /* 166.667% */
            width: fit-content;
          }
          .person {
            background: var(--kyy_color_tag_bg_success, #e0f2e5);
            color: var(--kyy_color_tag_text_success, #499d60);
          }

          .bottom {
            color: var(--text-kyy_color_text_3, #828da5);
            // text-align: center;
            max-width: 160px;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px; /* 157.143% */
          }
        }
      }
    }
  }

  .example-more {
    display: flex;
    align-items: center;
    justify-content: center;

    .more {
      border-radius: var(--radius-kyy_radius_button_s, 4px);
      border: 1px solid var(--color-button_border-kyy_color_buttonBorder_border_dedault, #d5dbe4);
      background: var(--color-button_border-kyy_color_buttonBorder_bg_default, #fff);
      color: var(--color-button_border-kyy_color_buttonBorder_text_default, #516082);
      text-align: center;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
      padding: 4px 16px;
    }
    .noempty {
      color: var(--text-kyy_color_text_2, #516082);
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */
      display: flex;
      align-items: center;
      width: 100%;
      gap: 12px;
      padding-bottom: 16px;
      .toText {
        flex: none;
      }
      .line {
        height: 1px;
        background: var(--divider-kyy_color_divider_deep, #d5dbe4);
        width: 100%;
      }
    }
  }
}
</style>
