<template>
  <div class="admin-container">
    <t-tabs v-model="searchRole" @change="handleSearch">
      <t-tab-panel :value="0" :label="t('forum.platform')"></t-tab-panel>
      <t-tab-panel :value="1" :label="t('forum.user')"></t-tab-panel>

      <template #action>
        <div class="more-search">
          <div class="in-box">
            <t-input
              v-model="searchParams.keyword"
              :placeholder="t('forum.userManagerSearchPlaceholder')"
              :maxlength="50"
              clearable
              style="width: 304px"
              @change="handleSearch"
              class="inSearch"
            >
              <template #prefix-icon>
                <iconpark-icon name="iconsearch" class="iconsearch"></iconpark-icon>
              </template>
            </t-input>
          </div>
        </div>
      </template>
    </t-tabs>

    <div class="table-container" :style="`height: ${tableMaxHeight}px`">
      <t-table
        row-key="openid"
        :pagination="pagination.total > pagination.pageSize - 1 ? pagination : null"
        :columns="tableColumns"
        :data="tableData"
        :loading="loading"
      >
        <template #empty>
          <div class="empty" :style="`height: ${tableMaxHeight - 70}px`">
            <Empty :name="searchEmpty ? 'no-search-contact': 'no-data-new'" :tip="searchEmpty ? t('forum.searchEmpty') : t('forum.empty')" />
          </div>
        </template>

        <template #flag="{row}">
          <span>{{ flagEnum[row.flag] }}</span>
        </template>

        <template #operate="{ row }">
          <div class="operate-cell">
            <a v-if="row.can_post" @click.stop="togglePostPermission(row, false)">{{ t("forum.preventPost") }}</a>
            <a v-else @click.stop="togglePostPermission(row, true)">{{ t("forum.preventPostCl") }}</a>
            <a v-if="row.can_comment" @click.stop="toggleCommentPermission(row, false)">{{
                t("forum.preventComment")
              }}</a>
            <a v-else @click.stop="toggleCommentPermission(row, true)">{{ t("forum.preventCommentCl") }}</a>
          </div>
        </template>
      </t-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted, onBeforeUnmount } from "vue";
import { useI18n } from "vue-i18n";
import Empty from "@/components/common/Empty.vue";
import { CardInfo } from "@/api/forum/models/user";
import {
  getManagerPlatformUserList,
  setUserCommentPermission,
  setUserPostPermission,
} from "@/api/uni/api/forumAdminApi";
import { debounce } from "lodash";
import { MessagePlugin } from "tdesign-vue-next";

const { t } = useI18n();

const loading = ref(false);

// 角色检索值
const searchRole = ref(0);

// 搜索参数
const searchParams = reactive({
  keyword: null,
});

// 搜索结果为空
const searchEmpty = ref(false);

// 分页对象
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
  onChange: ({ current, pageSize }) => {
    pagination.current = current;
    pagination.pageSize = pageSize;
    loadTableData();
  },
});

// 成员身份类型枚举
const flagEnum = {
  staff_platform: t("forum.staffPlatform"),
  staff: t("forum.staff"),
  visitor: t("forum.visitor"),
};

// 表格高度
const tableMaxHeight = ref(document.body.clientHeight - 132);

// 表格列
const tableColumns = [
  {
    colKey: "name",
    title: t("forum.username"),
  },
  {
    colKey: "flag",
    title: t("forum.userFlag"),
  },
  {
    colKey: "telephone",
    title: t("forum.telephone"),
  },
  {
    colKey: "operate",
    title: t("forum.operate"),
  },
];

// 表格数据
const tableData = ref<CardInfo>([]);

// 加载表格数据
const loadTableData = async (isSearch = false) => {
  loading.value = true;

  const res = await getManagerPlatformUserList({
    page: pagination.current,
    pageSize: pagination.pageSize,
    keyword: searchParams.keyword,
    role: searchRole.value,
  });

  loading.value = false;

  const {
    data: { list, count },
  } = res.data;

  searchEmpty.value = isSearch ? count < 1 : false;

  tableData.value = list;

  pagination.total = count;
};

loadTableData();

// 切换禁言状态
const togglePostPermission = async ({ openid }, permission) => {
  await setUserPostPermission({ openid, permission });
  MessagePlugin.success(`${permission ? t("forum.preventPostCl") : t("forum.preventPost")}成功`);
  loadTableData();
};

// 切换禁止评论状态
const toggleCommentPermission = async ({ openid }, permission) => {
  await setUserCommentPermission({ openid, permission });
  MessagePlugin.success(`${permission ? t("forum.preventCommentCl") : t("forum.preventComment")}成功`);
  loadTableData();
};

const handleSearch = debounce(() => {
  pagination.current = 1;
  loadTableData(true);
}, 300);

// 重置表格最大高度
const resizeTableMaxHeight = () => {
  tableMaxHeight.value = document.body.clientHeight - 132;
};

onMounted(() => {
  window.addEventListener("resize", resizeTableMaxHeight);
});

onBeforeUnmount(() => {
  window.removeEventListener("resize", resizeTableMaxHeight);
});
</script>

<style lang="less" scoped>
.iconsearch {
  font-size: 20px;
}

.iconadd {
  color: #ffffff;
  font-size: 24px;
}

.admin-container {
  padding: 4px 16px 16px;

  :deep(.t-tabs__bar) {
    display: block;
    width: 16px !important;
    transform: translateX(24px);
  }

  :deep(.t-tabs__nav-container::after) {
    display: block;
    background: var(--divider-kyy_color_divider_light, #eceff5);
  }

  :deep(.t-tabs__nav-item-wrapper){
    color: var(--text-kyy_color_text_1, #1A2139);

    &.t-is-active{
      font-weight: 600;
      color: var(--brand-kyy_color_brand_default, #4D5EFF);
    }
  }

  :deep(.t-tabs__nav-item.t-size-m) {
    height: 56px;
  }

  :deep(.t-tabs__nav-item-text-wrapper) {
    font-size: 16px;
  }

  :deep(.t-tabs__operations) {
    border-bottom: none;
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
  }

  .table-container {
    margin-top: 16px;
    overflow: auto;

    :deep(.t-table__pagination) {
      padding-bottom: 0;
    }

    .operate-cell {
      display: flex;
      gap: 8px;

      a {
        padding: 4px;
        color: var(--brand-kyy_color_brand_default, #4d5eff);
        line-height: 22px;
        border-radius: 4px;

        &:hover {
          background: var(--bg-kyy_color_bgBrand_hover, #eaecff);
        }
      }
    }
  }
}
</style>
