<template>
  <div class="detail-page detail-page-top" :class="`detail-page-${from}`">
    <title-bar v-if="from === 'im'" />
    <loading v-if="loadingStatus" />
    <div class="detail-content niche-src-box" v-else>
      <template v-if="nicheDetail?.can_view" class="detail-box">
        <nicheDateil
          :review="false"
          :team-id="teamId"
          :show-cs-list="true"
          :new-jupm="from !== 'square'"
          :niche-detail="nicheDetail"
        />
      </template>
      <div v-else class="close">
        <div class="nores">
          <img src="/assets/business/nores.svg" alt="" />
          <div class="text">{{ t("niche.close_tip") }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="nicheDetailReadOnly">
import TitleBar from "@renderer/components/common/BusinessBar.vue";
import { ClientSide } from "@renderer/types/enumer";
import { onActivated, onMounted, ref, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
import nicheDateil from "@renderer/views/niche/components/nicheHome/nicheDateil.vue";
import loading from "@/views/big-market/components/loading.vue";
import { shareDetail } from "./apis";
import LynkerSDK from '@renderer/_jssdk';

const { ipcRenderer } = LynkerSDK;
const { t } = useI18n();

const route = useRoute();
const isSquare = route?.query?.platform === 'square';
// const teamId = ref(isSquare ? route?.query?.team_id :  
// const tabStore = useTabsStore(); // 广场那边用到


const props = defineProps({
  groupList: {
    type: Array,
    default: () => [],
  },
  tabList: {
    type: Array,
    default: () => [],
  },
  tabIndex: {
    type: Number,
    default: 0,
  },
  activationGroupItem: {
    type: Object,
    default: null,
  },
  cloudDiskType: {
    type: Object,
    default: null,
  },
});

// const settabItem = () => {
//   ipcRenderer.invoke("set-work-bench-tab-item", {
//     path: `/workBenchIndex/nicheDetailReadOnly`,
//     path_uuid: "niche",
//     name: 'nicheDetailReadOnly',
//     title: "商机详情",
//     query: route.query,
//     type: ClientSide.NICHE,
//   });
// };

const loadingStatus: any = ref(true);
const nicheDetail: any = ref({});
const router = useRouter();

const getDetailRun = () => {
  shareDetail(nicheId.value).then((res) => {
    if (res.data) {
      nicheDetail.value = res.data.data;
      loadingStatus.value = false;

    }
  });
};
const nicheId: any = ref(0);
const teamId: any = ref("");
const from: any = ref("");
onActivated(() => {
  init();
});
onMounted(() => {
  init();
  ipcRenderer.on("update-niche-uuid", (event, value) => {
    console.log(event, value);
    nicheId.value = value.uuid;
    teamId.value = value.teamId;
    from.value = value.from;
    getDetailRun();
  });
});
const init = () => {
  console.log("route", route);
  console.log("route.query", route.query);

  nicheId.value = route.query?.uuid;
  if(isSquare) {
    teamId.value = route.query?.team_id || null;
  } else {
    teamId.value = route.query?.teamId || null;
  }
  from.value = route.query?.from || "def";

  getDetailRun();
};

watch(
  () => route.query,
  (newValue, oldValue) => {
    console.log("route.query1111");
    console.log(route);
    console.log(route.query);
    console.log("route.query1111", route.query?.uuid);
    if (route.query?.uuid && (route.path.includes("DetailReadOnly") || route.path.includes("rich_detail"))) {
      init();
    }
  },
);
</script>

<style lang="less" scoped>
@import "./styles/common.less";
.detail-page {
  display: flex;
  width: 100%;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  border-radius: 0px 0px 16px 0px;
  // height: 100vh;
  background-image: url(@/assets/niche/bg_small.png);
  overflow-y: auto;
  background-position: center;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  .detail-content::-webkit-scrollbar {
    width: 0px;
  }
  .detail-content {
    width: 1168px;
    height: calc(100vh - 72px);
    overflow-y: auto;
    background: #fff;
    border-radius: 8px;
    margin-top: 16px;
    padding: 0px 24px;

    .tab {
      height: 56px;
      display: flex;
      justify-content: center;
      align-items: center;
      align-self: stretch;
      border-bottom: 1px solid var(--divider-kyy_color_divider_deep, #d5dbe4);
      gap: 44px;

      .item {
        color: var(--brand-kyy_color_brand_default, #4d5eff);
        font-family: "PingFang SC";
        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        line-height: 24px; /* 150% */
        position: relative;
        cursor: pointer;
        .tag {
          width: 16px;
          height: 3px;
          flex-shrink: 0;
          border-radius: 1.5px;
          background: var(--brand-kyy_color_brand_default, #4d5eff);
          position: absolute;
          top: 37px;
          left: 22px;
        }
      }
      .item2 {
        color: var(--text-kyy_color_text_1, #1a2139);
        text-align: center;
        font-family: "PingFang SC";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 24px; /* 150% */
        cursor: pointer;
      }
    }
    .create-info {
      display: flex;
      padding: 12px 16px;
      justify-content: center;
      align-items: flex-start;
      gap: 12px;
      align-self: stretch;
      border-radius: 8px;
      background: var(--bg-kyy_color_bg_light, #fff);
      width: 100%;
      flex-wrap: wrap;
      margin: 12px 0;
      .create-item {
        width: 412px;
        display: flex;
        align-items: center;
        gap: 12px;
        .label {
          width: 56px;
          color: var(--text-kyy_color_text_3, #828da5);

          /* kyy_fontSize_2/regular */
          font-family: "PingFang SC";
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px; /* 157.143% */
        }
        .text {
          color: var(--text-kyy_color_text_1, #1a2139);

          /* kyy_fontSize_2/regular */
          font-family: "PingFang SC";
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px; /* 157.143% */
        }
      }
    }
    // .detail-box{
    //   width: 100%;
    //   background: #fff;
    //   padding: 16px 24px;
    // }
    .placeholder {
      width: 100%;
      height: 100px;
      opacity: 0;
    }
  }

  .detail-extend {
    width: 872px;
    // height: calc(100vh - 64px);
    // height: 100%;
    min-height: 98%;
    padding-top: 12px;
  }
  .detail-footer {
    position: fixed;
    bottom: 0;
    display: flex;
    height: 64px;
    padding: 16px 304px;
    align-items: center;
    gap: 16px;
    align-self: stretch;
    background: var(--bg-kyy_color_bg_light, #fff);
    box-shadow: 0px -3px 8px 0px rgba(0, 0, 0, 0.08);
    width: 100vw;
    justify-content: center;
  }
}
.detail-page-cbd {
  background-image: url(@/assets/niche/cbdbg.png);
}
.detail-page-politics {
  background-image: url(@/assets/niche/zqbg.png);
}
.detail-page-member {
  background-image: url(@/assets/niche/bg_small.png);
}

.f-btn {
  width: 80px;
}
.nores {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 200px;
  img {
    width: 133px;
    height: 112px;
  }
  .text {
    color: var(--text-kyy-color-text-2, #516082);
    text-align: center;

    /* kyy_fontSize_2/regular */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
    margin-top: 12px;
  }
}
.detail-box{
  margin-top: 16px;
}

</style>
