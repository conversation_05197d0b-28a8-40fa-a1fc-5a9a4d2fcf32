/**
 * 广场类型
 *
 * SquareType，广场类型
 */
export enum SquareType {
  BusinessAssociation = 'BUSINESS_ASSOCIATION', // 商协
  Enterprise = 'ENTERPRISE', // cbd
  Individual = 'INDIVIDUAL',
  IndividualBusiness = 'INDIVIDUAL_BUSINESS',
  Organization = 'ORGANIZATION',
  Government = 'GOVERNMENT', // 政府
  Uni = 'UNI', // 高校
  Other = 'OTHER',
}

/**
 * 状态
 */
export enum Status {
  Deleted = 'DELETED',
  Normal = 'NORMAL',
}

/**
 * OrganizationType，组织类型
 */
export enum OrganizationType {
  BusinessAssociation = 'BUSINESS_ASSOCIATION',
  Enterprise = 'ENTERPRISE',
  IndividualBusiness = 'INDIVIDUAL_BUSINESS',
  Other = 'OTHER',
}

/**
 * 认证状态
 *
 * OrganizationCertStatus，组织认证状态
 *
 * 认证状态（组织广场号特有）
 */
export enum OrganizationCertStatus {
  Certified = 'CERTIFIED',
  Expired = 'EXPIRED',
  Pending = 'PENDING',
  Uncertified = 'UNCERTIFIED',
  Reject = 'REJECT',
}

/**
 * 关注和粉丝列表可见性
 *
 * IndividualFFVisibility，关注和粉丝列表可见性
 */
export enum IndividualFFVisibility {
  Private = 'PRIVATE',
  Public = 'PUBLIC',
}

/**
 * 性别
 *
 * Gender，性别
 */
export enum Gender {
  Female = 'FEMALE',
  Male = 'MALE',
}

/**
 * RegionCode, 地区代码
 */
export enum RegionCode {
  CN = 'CN',
  Hk = 'HK',
  Mo = 'MO',
  Tw = 'TW',
}

/**
 * ProfileType，资料类型
 */
export enum ProfileType {
  Avatar = 'AVATAR',
  CoverImage = 'COVER_IMAGE',
  Intro = 'INTRO',
  Name = 'NAME',
}
