<template>
  <div v-if="list.length > 0" class="waterfall-box" >
    <div class="top ex-apps">
      <!-- @click="gonotice" -->
      <div class="title" >
        <!-- <img src="@/assets/bench/external_app_mini.svg" /> -->
        <img :src="icon" />
        <span>{{ $t('member.eb.exapps') }}</span>
      </div>
      <!-- v-if="authority?.manage_auth" -->
    </div>

    <div class="notice-item-body">
      <div class="default" >
        <div v-for="item in list" :key="item.uuid" class="apps-item"
        @click="handleClick(item)"
        >
          <img class="apps-item-icon" :src="item.picture_linking || icon" />
          <span class="apps-item-name">{{ item.name }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref } from "vue";
import { useI18n } from "vue-i18n";
import { onMountedOrActivated } from "@renderer/hooks/onMountedOrActivated";
import { AllApp, externalApp } from "@renderer/api/workBench/index";
import LynkerSDK from "@renderer/_jssdk";
import { MessagePlugin } from "tdesign-vue-next";
const { t } = useI18n();

import icon from '@renderer/assets/bench/external_app_mini.svg'

const props = defineProps({
  activationGroupItem: {
    type: Object,
  },
});

const list = ref<any[]>([]);

const handleClick = (item: any) => {
  let url = '';
  debugger
  switch (item.type) {
    case 'app':
      url = item?.share_link;
      break;
    case 'h5':
      url = item?.desktop_link || item?.h5_link;
      break;
    case 'wechat_official':
      url = item?.article_link;
      break;
    case 'mini_program':
      url = '';
      break;
    case 'wechat_mini':
    default:
      url = '';
  }
  if (!url) {
    MessagePlugin.error('该应用暂无分享链接');
    return;
  }
  LynkerSDK.openExternalApp({
    url,
  })
}

const getList = async () => {
  try {
    const res = await externalApp(props.activationGroupItem?.teamId, {
      channel_type: 'digital',
    });
    const data = res.data.data.items;
    console.log(list, "ressssssssss");
    list.value = data;
  } catch (error) {
    console.log(error, "error");
  }
};
onMountedOrActivated(()=> {
  getList();
})
</script>
<style lang="less" scoped>
// lss 加个滚动条样式
@import "@renderer/views/member/member_number/panel/member-panel/less/home-panel.less";

.default {
  width: 560px;
  height: auto;
}
.notice-item:first-child {
  /* padding-top: 0 !important; */
}
.head-item-icon:hover {
  color: #707eff;
}
.head-item-icon {
  cursor: pointer;
  display: flex;
  align-items: center;
  color: #516082;
  text-align: center;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
  .icon-img {
    font-size: 20px;
    width: 20px;
    height: 20px;
    margin-right: 4px;
  }
}
.user-name {
  display: flex;
  align-items: center;
  justify-content: start;
  margin-top: 4px;
  color: var(--text-kyy_color_text_5, #acb3c0);
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  gap: 12px;
  line-height: 20px; /* 166.667% */
  .time {
    width: 106px;
    flex: none;
  }
  .line {
    width: 1px;
    height: 12px;

    background: var(--divider-kyy_color_divider_light, #ECEFF5);
  }
}
.dian {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  width: 100%;
  word-break: break-all;
}
.notice-content {
  color: var(--text-kyy_color_text_3, #828da5);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  width: 100%;
  word-break: break-all;
}
.notice-item::after {
  content: "";
  height: 1px;
  width: 100%;
  background: #eceff5;
  position: absolute;
  bottom: -5px;
  left: 0;
}
.tags {
  display: flex;
  height: 20px;
  width: fit-content;
  min-height: 20px;
  max-height: 20px;
  padding: 1px 8px;
  justify-content: center;
  align-items: center;
  color: var(--kyy_color_tag_text_warning, #fc7c14);
  text-align: right;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px; /* 166.667% */
  gap: 10px;
  border-radius: var(--kyy_radius_tag_full, 999px);
  background: var(--kyy_color_tag_bg_warning, #ffe5d1);
}
.nodata {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--text-kyy_color_text_2, #516082);
  text-align: center;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;

  line-height: 26px; /* 152.941% */
  img {
    width: 200px;
    height: 200px;
  }
}
.notice-item:hover {
  cursor: pointer;
  background: var(--bg-kyy_color_bg_list_hover, #f3f6fa);
  transition: all 0.25s linear;
}

.notice-item-text {
  color: var(--text-kyy_color_text_1, #1a2139);
  font-size: 16px;
  font-style: normal;
  padding-left: 8px;
  font-weight: 600;
  line-height: 24px; /* 150% */
  display: flex;
  height: 40px;
  width: 104px;
  gap: 8px;
  align-items: center;
  img {
    width: 20px;
    height: 20px;
  }
}
.notice-item-text:hover {
  border-radius: 8px;
  background: var(--bg-kyy_color_bg_list_hover, #f3f6fa);
  cursor: pointer;
}
.head-item-text {
  color: #1a2139;
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: 24px; /* 150% */
  position: relative;
  margin-left: 8px;
  margin-bottom: 12px;
}
.notice-box {
  // background-color: #fff;
  // border-radius: 8px;
  // padding: 8px;
  height: 432px;
  /* height: fit-content; */
  position: relative;
}
.notice-item:last-child {
  margin-bottom: 0 !important;


}
.notice-item:last-child::after {
display: none;

}
.notice-item:first-child::after {
  display: inline-block;

}
.notice-item-body {
  padding: 12px 24px;
}
.notice-item-list {
  // overflow: auto;
  .notice-item {
    margin-bottom: 9px;
    padding: 8px;
    border-radius: 8px;
    padding: 8px;
    cursor: pointer;
    position: relative;
    border-radius: 8px;
    transition: all 0.25s linear;
    cursor: pointer;
    .notice-title {
      gap:4px;
      display: flex;
      span {
        color: var(--text-kyy_color_text_1, #1a2139);
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 22px; /* 157.143% */
      }
    }
    .status {
      display: flex;
      height: 20px;
      min-height: 20px;
      max-height: 20px;
      padding: 1px 8px;
      justify-content: center;
      align-items: center;
      margin-right: 4px;
      color: var(--kyy_color_tag_text_warning, #fc7c14);
      text-align: right;
      font-size: 12px;
      font-weight: 400;
      border-radius: var(--kyy_radius_tag_full, 999px);
      background: var(--kyy_color_tag_bg_warning, #ffe5d1);
      line-height: 20px; /* 166.667% */
    }
    .tag {
      display: flex;
      height: 20px;
      min-height: 20px;
      margin-right: 4px;
      max-height: 20px;
      padding: 0px 4px;
      justify-content: center;
      align-items: center;
      color: var(--kyy_color_tag_text_kyyBlue, #21acfa);
      text-align: center;
      border-radius: var(--kyy_radius_tag_s, 4px);
      background: var(--kyy_color_tag_bg_kyyBlue, #e4f5fe);
      font-size: 12px;
      font-weight: 400;
      line-height: 20px; /* 166.667% */
    }
  }
}
.default {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 24px;
}
.apps-item {
  display: flex;
  flex-direction: row;
  width: 164px;
  padding: 12px;
  align-items: center;
  gap: 8px;
  border-radius: 12px;
  background: var(--bg-kyy_color_bg_deep, #F5F8FE);
  cursor: pointer;
  transition: all 0.25s linear;
  &:hover {
    border-radius: 12px;
    background: var(--bg-kyy_color_bg_light, #FFF);
    /* kyy_shadow_m */
    box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.12);
  }
  .apps-item-icon {
    display: flex;
    width: 36px;
    height: 36px;
    border-radius: 12px;
    justify-content: center;
    align-items: center;
    gap: 4px;
    flex-shrink: 0;
    object-fit: contain;
  }
  .apps-item-name {
    color: #000;
    /* kyy_fontSize_2/regular */
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

</style>
