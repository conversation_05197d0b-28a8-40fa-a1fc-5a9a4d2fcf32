<template>
  <t-drawer :header="typeFlag === 1 ? '个人标签' : '设置平台标签'" placement="right" :visible="visibleFactory" :close-btn="true"
    size="472px" :footer="false" class="tag-drawer-t-form" @close="handleClose">
    <div>
      <div class="head-text">
        {{
        typeFlag === 1
        ? '平台成员可自定义符合自己的个人标签，例如个人标签为车牌号，则平台成员可输入：粤C00001'
        : '平台成员可从列表中选择合适的平台标签，例如标签名称为“入学时间”时，可添加“2001年”，“2002年”...等标签'
        }}
      </div>
      <div class="label-text">
        {{ typeFlag === 1 ? '个人标签' : '平台标签名称' }}
      </div>
      <div class="value-text">
        <div v-if="editFlag" style="display: flex; align-items: center">
          <span style="

            color: #1a2139;
            font-size: 14px;
            font-weight: 400;
            line-height: 22px;
            margin-right: 8px;
          ">{{
            typeFlag === 1
            ? tagInfo.personal.name
            ? tagInfo.personal.name
            : '--'
            : tagInfo.platform.name
            ? tagInfo.platform.name
            : '--'
            }}</span>
          <iconpark-icon @click="editFlag = false" style="font-size: 20px; cursor: pointer;color: #4d5eff"
            name="iconedit"></iconpark-icon>
        </div>

        <t-input v-if="!editFlag && typeFlag === 1" @blur="subname" maxlength="8" class="input-name-8"
          v-model.trim="tagInfo.personal.name" placeholder="请输入名称，例如车牌号"></t-input>
        <t-input v-if="!editFlag && typeFlag === 2" @blur="subname" maxlength="8" class="input-name-8"
          v-model.trim="tagInfo.platform.name" placeholder="请输入名称，例如：籍贯、入学时间等"></t-input>
      </div>
      <div>
        <div class="label-text" style="margin-top: 24px">
          {{ typeFlag === 2 ? '标签列表' : '标签说明（填写标签规范说明，引导平台成员正确填写标签）' }}
        </div>
        <div class="value-text" v-if="typeFlag === 1">
          <div v-if="editdisFlag" style="display: flex; align-items: center">
            <span style="
              word-break: break-all;
              color: #1a2139;
              font-size: 14px;
              font-weight: 400;
              line-height: 22px;
              margin-right: 8px;
            ">{{ tagInfo.personal.explain ? tagInfo.personal.explain : '--' }}</span>

            <iconpark-icon @click="editdisFlag = false" name="iconedit"
              style="font-size: 20px; color: #4d5eff;cursor: pointer;"></iconpark-icon>
          </div>
          <t-textarea @blur="subname('dis')" maxlength="100" v-else v-model.trim="tagInfo.personal.explain"
            placeholder="请输入内容" />
        </div>

        <div v-else>
          <t-button theme="default" variant="outline" style="width: fit-content; margin-bottom: 8px" @click="addTag">
            <template #icon><add-icon /></template>

            添加标签</t-button>
          <div>
            <div></div>
          </div>
        </div>

        <draggable style="overflow: overlay;height: calc(100vh - 298px);padding-right: 8px;"
          :list="tagInfo.platform.value" animation="300" itemKey="value_id" @end="onEnd" v-if="typeFlag === 2">
          <template #item="{ element, index }">
            <div style="border: 1px solid #eceff5"
              :class="[index % 2 === 0 ? 'tag-box-item-even' : 'tag-box-item-odd', index === 0 ? 'tag-box-item0' : '']">
              <div class="tag-item">
                <div style="display: flex; align-items: center">
                  <iconpark-icon style="font-size: 20px; margin-right: 12px; color: #828da5; cursor: pointer"
                    class="drag-icon" name="icondrag"></iconpark-icon>
                  <div class="tag-color"
                    :style="{ background: TagColors.find((item) => item.intColor + '' === element.colour).color }">
                  </div>
                  <span style="color: #1a2139; font-size: 14px">{{ element.name }}</span>
                </div>
                <iconpark-icon @click="delitem(element.value_id)" class="delicon" name="icondelete"></iconpark-icon>
              </div>
            </div>
          </template>
        </draggable>
        <t-dialog :visible="addTagFlag" header="添加标签" attach="body" width="306" :closeOnOverlayClick="true"
          @close="addTagCloseFn" :closeBtn="false">
          <template #body>
            <div>
              <!-- <iconpark-icon name="iconerror-a961a3n0" style="font-size: 24px;width: 24px;height:24px;color:rgba(81, 96, 130, 1);position: absolute;right:24px;top:24px;" @click="close"></iconpark-icon> -->

              <t-input style="margin: 8px 0 24px" v-model.trim="addName.name" placeholder="请输入标签" :maxlength="8"
                show-limit-number />
              <div class="tag-box">
                <div class="tagItem" v-for="(item, index) in TagColors"
                  :style="{ background: selectColor === item.intColor ? item.bgColor : '' }"
                  :class="[item?.class, selectColor === item.intColor ? 'actaaa' : '']" :key="index"
                  @click="actColorFn(item)">
                  <div class="tagColor" :style="{ background: item.color }"></div>
                  <iconpark-icon class="isokact" :style="{ display: selectColor === item.intColor ? 'block' : 'none' }"
                    name="iconcorrect"></iconpark-icon>
                </div>
              </div>
            </div>
          </template>
          <template #footer>
            <div style="margin-right: 0px">
              <t-button class="btn cancel" variant="outline" theme="default" @click="addTagCloseFn()">{{
                t('account.cancel')
                }}</t-button>
              <t-button class="btn confirm" theme="primary" variant="base" :disabled="!addName?.name"
                @click="confirm">{{
                t('zx.note.confirm')
                }}</t-button>
            </div>
          </template>
        </t-dialog>
      </div>
    </div>
  </t-drawer>
</template>
<script lang="tsx" setup>
  import { AddIcon, Icon } from 'tdesign-icons-vue-next';

  import { useI18n } from 'vue-i18n';
  import { reactive, ref, toRaw, watch, computed, onMounted } from 'vue';
  import draggable from 'vuedraggable';
  import { TagColors } from '@renderer/views/digital-platform/utils/constant.ts';

  import {
    getLabelSetting,
    editLabelSettingid,
    addLabelSettingid,
    labelSort,
    deleteLabel,
  } from '@renderer/api/politics/api/businessApi';
  import { useRoute } from 'vue-router';

  const route = useRoute();

  import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next';
  import { error } from 'console';
  const { t } = useI18n();
  const editFlagDis = ref(true);
  const editdisFlag = ref(true);
  const editFlag = ref(true);
  const teamId = ref(null);

  const dis = ref('');
  const subname = (val) => {
    if (val === 'dis') {
      updateLabelSetting('explain', tagInfo.value.personal.explain);
      return
    }
    updateLabelSetting('name', typeFlag.value === 1 ? tagInfo.value.personal.name : tagInfo.value.platform.name);
  };

  const updateLabelSetting = (field, value) => {
    console.log(field, value, tagInfo.value, 'field, value');

    editLabelSettingid(
      pageType(),
      { [field]: value },
      teamId.value,
      typeFlag.value === 1 ? tagInfo.value.personal.setting_id : tagInfo.value.platform.setting_id,
    )
      .then((res) => {
        MessagePlugin.success('保存成功');
        if (field === 'name') {
          editFlag.value = true

        }
        if (field === 'explain') {
          editdisFlag.value = true
        }
        console.log(res, '阿达大大是的');
      })
      .catch((error) => {
        const errMsg = error instanceof Error ? error.message : error;
        MessagePlugin.error(errMsg);
      });
  };
  const selectColor = ref(Number.parseInt('0XFFD54941', 16));
  const addName = ref({
    name: '',
  });
  const actColorFn = (item) => {
    selectColor.value = item.intColor;
  };
  const close = () => { };
  const confirm = () => {
    addLabelSettingid(
      pageType(),
      {
        setting_id: tagInfo.value.platform.setting_id,
        colour: selectColor.value,
        name: addName.value.name,
      },
      teamId.value,
    )
      .then((res) => {
        addTagFlag.value = false;
        addName.value.name = '';
        getList();
        MessagePlugin.success('添加成功');

        selectColor.value = Number.parseInt('0XFFD54941', 16);
      })
      .catch((error) => {
        const errMsg = error instanceof Error ? error.message : error;
        MessagePlugin.error(errMsg);
      });
  };
  const tagList = ref([{}]);
  const changeColor = (item) => {
    selectColor.value = item.intColor;
  };
  const addTagFlag = ref(false);
  const addTagCloseFn = () => {
    addTagFlag.value = false;
    addName.value.name = '';
    selectColor.value = Number.parseInt('0XFFD54941', 16);
    addTagFlag.value = false;
    editFlagDis.value = true;
    editdisFlag.value = true;
    editFlag.value = true;
  };
  const visibleFactory = ref(false);
  const props = defineProps({
    dataInfo: {
      type: Object,
      default: () => { },
    },
  });
  const handleClose = () => {
    visibleFactory.value = false;
    editFlagDis.value = true;
    editdisFlag.value = true;
    editFlag.value = true;
    tagInfo.value.personal.name = '';
    dis.value = '';
  };
  const addTag = () => {
    addTagFlag.value = true;
  };
  const delitem = (val) => {
    const myDialog = DialogPlugin({
      header: '删除标签',
      theme: 'info',
      body: '确定删除后相关平台成员对应的标签会同步被删除',
      className: 'dialog-classp32',
      confirmBtn: '确定删除',
      cancelBtn: t('ebook.mset4'),
      closeOnOverlayClick: false,
      closeBtn: false,
      onConfirm: () => {
        deleteLabel(
          pageType(),
          {
            setting_id: tagInfo.value.platform.setting_id,
          },
          val,
          teamId.value,
        )
          .then((res) => {
            myDialog.hide();
            MessagePlugin.success('删除成功');

            getList();
          })
          .catch((error) => {
            const errMsg = error instanceof Error ? error.message : error;
            myDialog.hide();
            MessagePlugin.error(errMsg);
          });
      },
      onCancel: () => {
        myDialog.hide();
      },
    });
  };
  const typeFlag = ref(1);
  const tagInfo = ref({
    personal: {
      setting_id: '',
      enable: 0,
      name: '',
      explain: '',
    },
    platform: {
      setting_id: '',
      enable: 0,
      name: '',
      value: [],
    },
  });
  const pageType = () => {
    console.log(route.path, 'route.pathroute.path', route);

    if (route.path.includes('member')) {
      return 'member';
    }
    if (route.path.includes('politics')) {
      return 'government';
    }
    if (route.path.includes('cbd')) {
      return 'cbd';
    }
    if (route.path.includes('association')) {
      return 'association';
    }
     if (route.path.includes('uni')) {
      return 'uni';
    }
  };
  const openWin = (val, team) => {
    typeFlag.value = val;
    visibleFactory.value = true;
    console.log(props, 'ppppppp');
    teamId.value = team;
    getList();
  };
  const getList = () => {
    getLabelSetting(pageType(), teamId.value).then((res) => {
      console.log(res, 'ressssss');
      tagInfo.value = res.data.data;
    });
  };
  const onEnd = (val) => {
    console.log(tagInfo.value.platform.value, '222222');

    labelSort(
      pageType(),

      {
        setting_id: tagInfo.value.platform.setting_id,

        value_ids: tagInfo.value.platform.value.map((e) => e.value_id),
      },
      teamId.value,
    )
      .then((res) => {
        getList();
      })
      .catch((error) => {
        const errMsg = error instanceof Error ? error.message : error;
        MessagePlugin.error(errMsg);
      });
    // moveTagList(params).then(res => {
    //   MessagePlugin.success('操作成功');
    // })
  };
  defineExpose({
    openWin,
  });
</script>
<style>
  /*定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
  ::-webkit-scrollbar {
    width: 6px;
    background-color: #f5f5f5;
  }

  /*定义滚动条轨道 内阴影+圆角*/
  ::-webkit-scrollbar-track {
    background-color: transparent;
  }

  /*定义滑块 内阴影+圆角*/
  ::-webkit-scrollbar-thumb {
    border-radius: 4px;
    background: var(--icon-kyy_color_icon_transparent, rgba(26, 33, 57, 0.36));
  }

  .tag-drawer-t-form {

    .t-textarea__info_wrapper_align {
      position: absolute;
      bottom: 0;
      right: 4px;
    }

    .t-drawer__body {
      padding: 0 18px 24px 28px !important;
    }

    .t-drawer__header {
      padding-left: 24px;
    }
  }

  .textarea-name-8 {
    .t-textarea__info_wrapper_align {
      position: absolute;
      bottom: 0;
      right: 4px;
    }

  }

  .t-drawer__close-btn {
    right: 24px !important;
  }
</style>
<style scoped lang="less">
  .tag-box-item-odd {
    border-top: none !important;
  }

  .tag-box-item-even {
    border-top: none !important;
  }

  .tag-box-item0 {
    border-top: 1px solid #eceff5 !important;
    /* 第一项保留顶部边框 */
  }

  .tag-color {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    margin-right: 8px;
  }

  .isokact {
    font-size: 16px;
    color: rgb(255, 255, 255);
    position: absolute;
    top: 6px;
    left: 6px;
    display: none;
  }

  .actaaa {}

  .tag-box {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 24px;
  }

  .tagColor {
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }

  .tagItem {
    position: relative;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    padding: 4px;

    div {
      width: 100%;
      height: 100%;
    }
  }

  .f-between {
    display: flex;
    justify-content: space-between;
  }

  .tag-outer {
    position: relative;
    width: 32px;
    height: 32px;

    .tag-active {
      width: 32px;
      height: 32px;
      border-radius: 50%;
    }

    .tag-item {
      width: 22px;
      height: 22px;
      border-radius: 50%;
      cursor: pointer;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }

  .btn {
    min-width: 80px;
  }

  .delicon {
    font-size: 20px;
    padding: 4px;
    margin: 4px;
    border-radius: 4px;
    width: 28px;
    height: 28px;

    display: none;
  }

  .delicon:hover {
    display: inline;
    background: var(--bg-kyy_color_bgBrand_foucs, #dbdfff);
    color: #707eff !important;
  }

  .tag-item:hover .delicon {
    display: inline;
    color: #828da5;
  }

  .tag-item:hover {
    background: var(--bg-kyy_color_bg_list_hover, #f3f6fa);
  }

  .tag-item:last-child {
    border-bottom: none;
  }

  .t-textarea__limit {
    position: absolute;
    top: 10px;
    right: 8px;
  }

  .tag-item {
    height: 46px;
    border-bottom: 1px solid #eceff5;

    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
  }

  .value-text {
    color: #1a2139;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    /* 157.143% */
  }

  .label-text {
    margin-bottom: 8px;
    color: #828da5;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    /* 157.143% */
  }

  .head-text {
    color: var(--font-kyy_font_gy_2, #516082);
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    padding-right: 8px;
    /* 157.143% */
    margin-bottom: 24px;
  }
</style>
