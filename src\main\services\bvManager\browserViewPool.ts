import { <PERSON><PERSON><PERSON><PERSON>ie<PERSON> } from 'electron';
import { ViewConfig, PoolConfig } from './types';
import { createBrowserView } from './browserViewFactory';

export class BrowserViewPool {
  private static instance: BrowserViewPool;
  private readonly pools = new Map<string, BrowserView[]>();
  private readonly poolConfigs = new Map<string, number>();
  private static readonly DEFAULT_TYPE = 'default';
  private static readonly DEFAULT_SIZE = 1;

  private constructor() {
    this.initializeDefaultPool();
  }

  static getInstance(): BrowserViewPool {
    if (!BrowserViewPool.instance) {
      BrowserViewPool.instance = new BrowserViewPool();
    }
    return BrowserViewPool.instance;
  }

  private initializeDefaultPool(): void {
    this.poolConfigs.set(BrowserViewPool.DEFAULT_TYPE, BrowserViewPool.DEFAULT_SIZE);
    this.pools.set(BrowserViewPool.DEFAULT_TYPE, []);
  }

  setPoolConfig(configs: PoolConfig[]): void {
    configs.forEach(({ type, maxSize }) => {
      this.poolConfigs.set(type, maxSize);
      if (!this.pools.has(type)) {
        this.pools.set(type, []);
      }
    });
  }

  async addView(config: ViewConfig): Promise<void> {
    const type = config.type || BrowserViewPool.DEFAULT_TYPE;
    const maxSize = this.poolConfigs.get(type) || BrowserViewPool.DEFAULT_SIZE;
    const pool = this.pools.get(type) || [];
    console.log('addView:+++', config, type, maxSize, this.getAllPools(true));
    if (pool.length < maxSize) {
      const view = await createBrowserView(config);
      if (view) {
        pool.push(view);
        this.pools.set(type, pool);
      }
    }
  }

  getView(type = BrowserViewPool.DEFAULT_TYPE, isShift?: Boolean): BrowserView | null {
    const pool = this.pools.get(type);
    console.log('getView:+++', this.getAllPools(true), pool);
    const result = isShift ? pool?.shift() : pool[0];
    return result || null;
  }


  getPoolSize(type = BrowserViewPool.DEFAULT_TYPE): number {
    return this.pools.get(type)?.length || 0;
  }

  getMaxPoolSize(type = BrowserViewPool.DEFAULT_TYPE): number {
    return this.poolConfigs.get(type) || BrowserViewPool.DEFAULT_SIZE;
  }

  getAllPools(isArray?:boolean): any {
    if (isArray) {
      const poolsObject: Record<string, BrowserView[]> = {};
      this.pools.forEach((views, type) => {
        poolsObject[type] = views;
      });
      return poolsObject;
    }
    return this.pools;
  }
}
