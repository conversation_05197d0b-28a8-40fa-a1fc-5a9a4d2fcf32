<template>
  <!-- <div v-lkloading="{ show:loading, height:true, opacity:false }"> -->
    <component
      :is="panels[currentPanel ? currentPanel : '']"
      :current-row="currentRow"
      @on-set-current-row="onSetCurrentRow"
      @on-set-current-panel="onSetCurrentPanel"
      @on-page="onPage"
    />
  <!-- </div> -->
</template>

<script setup lang="ts">
import { panels } from "@renderer/views/uni/member_home/panel/trends-panel/panel";
import { reactive, ref, toRaw, watch } from "vue";
import { useRouter, useRoute } from "vue-router";
import { useUniStore } from "@renderer/views/uni/store/uni";
// import { updateAllCount } from "@renderer/views/member/hooks/total";
import { onMountedOrActivated } from "@renderer/hooks/onMountedOrActivated";

const loading = ref(false);
const emits = defineEmits(["onPage"]);
const currentPanel = ref("PTrends");
const currentRow = ref(null);
const route = useRoute();
const store = useUniStore();
// 进入入会或激活
// 用于判定来自其他bv跳转的问题
const goApplyOrActive = () => {
      console.log('routeaaaa',route.query)
      if (route.query && route.query.origin === 'message') {
      const { teamId, redirect } = route.query;
      if (teamId && redirect) {

        // if (redirect.includes('PActiveMemberPanel')) {
        //   currentPanel.value = 'PActiveMemberPanel';
        // } else if (redirect.includes('PApplyMemberPanel')) {
        //   currentPanel.value = 'PApplyMemberPanel';

        // }
        if (['PApply'].some((v) => redirect.includes(v))) {
          currentPanel.value = 'PBack';
        }
        route.query.origin = '';
      }
    }
};
onMountedOrActivated(()=> {
  goApplyOrActive();
})

const onSetCurrentRow = (row) => {
  currentRow.value = row;

};
const onSetCurrentPanel = (panel) => {
  // onLoadingShow();
  console.log(panel)
  currentPanel.value = panel;

};

const onLoadingShow = () => {
  loading.value = true;
  setTimeout(() => {
    loading.value = false;

  }, 900);
};

const onPage = (val) => {
  emits("onPage", val);

};
</script>

<style lang="less" scoped></style>
