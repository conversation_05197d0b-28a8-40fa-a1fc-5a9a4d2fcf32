<template>
  <t-drawer
    v-model:visible="visible"
    :z-index="2500"
    attach="body"
    size="472px"
    class="drawerSetForm"
    :header="$t('member.impm.hui_2')"
  >
    <div class="toBody">
      <!-- <t-alert theme="info">
          <template #message>
            <span class="info-text">入会会费设置：无需缴费入会 </span>
            <t-link theme="primary" hover="color">入会设置</t-link>
          </template>
        </t-alert> -->
      <t-form
        ref="form"
        label-align="top"
        :rules="FORM_RULES"
        :data="formData"
        :colon="false"
        class="form"
      >
        <!-- <t-form-item label="会费状态" name="name" class="form-item">
            <t-select v-replace-svg
              v-model="formData.pay_status"
              :options="optionsFeeStatus"
              :disabled="
                (settingValue.pay_setting === 2 &&
                  settingValue.level_money === 0) ||
                  settingValue.pay_setting === 2
              "
            />
          </t-form-item> -->
        <t-form-item label="组织名称" name="team_name" class="form-item">
          <t-input v-model="formData.team_name" placeholder="请输入" disabled />
        </t-form-item>
        <t-form-item label="会员级别" name="level" class="form-item">
          <!--          <t-input-->
          <!--            v-model="formData.level_name"-->
          <!--            placeholder="请输入"-->
          <!--          />-->
          <t-select
            v-model="formData.level"
            class="searchForm-item-input"
            :options="levelOptions"
            clearable
            @change="changeLevel"
          >  <template #suffixIcon>
            <img src="@/assets/svg/icon_arrow_down.svg" />
          </template>
        </t-select>
        </t-form-item>

        <t-form-item label="入会时间" name="join_time" class="form-item">
          <t-date-picker
            v-model="formData.join_time"
            :placeholder="'请选择'"
            :maxlength="500"
            :format="'YYYY-MM-DD'"
            style="width: 100%"
            clearable
            disabled
          />
        </t-form-item>
        <t-form-item label="到期时间" name="expire_time" class="form-item">
          <t-date-picker
            v-model="formData.expire_time"
            :placeholder="'请选择'"
            :maxlength="500"
            :format="'YYYY-MM-DD'"
            style="width: 100%"
            clearable
            disabled
          />
        </t-form-item>

        <t-form-item label="会费金额" name="money" class="form-item">
          <t-input v-model="formData.money" placeholder="请输入" />
        </t-form-item>

        <!-- <t-form-item label="币种" name="currency" class="form-item">
          <t-select v-replace-svg  v-model="formData.currency" :options="optionsCurrency" />
        </t-form-item> -->

        <t-form-item label="会员有效期" name="type" class="form-row">
          <div class="setRow">
            <div class="setRow-item">
              <t-radio-group
                v-model="formData.type"
                style="margin-bottom: 16px"
                :default-value="1"
                @change="changeRadio"
              >
                <t-radio :value="1"> {{$t('member.active.fixed_term')}} </t-radio>
                <t-radio :value="2"> {{$t('member.active.owner_me')}} </t-radio>
                <t-radio :value="3"> 长期有效 </t-radio>
              </t-radio-group>
            </div>
            <div class="setRow-item">
              <template v-if="formData.type === 1">
                <t-input-number v-model="formData.value" theme="column" :min="1" :decimalPlaces="0" @validate="onValidateNumber" @keyup="onListenNumberChange"/>
                <span class="pl-6px">年</span>
              </template>
              <t-date-picker
                v-else-if="formData.type === 2"
                v-model="formData.owner_value"
                :placeholder="'请选择'"
                :format="'YYYY-MM-DD'"
                :disableDate="disableDateOwner"
                @change="changeOwnDate"
                @blur="changeOwnDate"
              />
              <span
                v-show="[1, 2].includes(formData.type)"
                class="setRow-item-tips ml-16px"
              >
                下次到期时间为：{{ formData.show_tips }}
              </span>
            </div>
          </div>
        </t-form-item>
      </t-form>
    </div>
    <template #footer>
      <div class="footer">
        <t-button
          theme="default"
          variant="outline"
          @click="onClose"
        >取消</t-button>
        <t-button theme="primary" class="ml-8" @click="onSave">确定</t-button>
      </div>
    </template>
  </t-drawer>
</template>

<script lang="ts" setup>
import { ref, reactive, Ref, watch, toRaw } from "vue";
import dayjs from "dayjs";
import { getMemberJobsDetailAxios, getMemberJobsListAxios } from "@renderer/api/uni/api/businessApi";
import { getResponseResult, priceRecovery } from "@renderer/utils/myUtils";
import { MessagePlugin } from "tdesign-vue-next";
const FORM_RULES = {
  money: [{ required: true, message: "请输入会费金额" }],
  currency: [{ required: true, message: "请选择币种" }],
  level: [{ required: true, message: "请选择会员等级" }],
};
const optionsCurrency = [
  { label: "CNY", value: "CNY" },
  { label: "MOP", value: "MOP" },
  { label: "HKD", value: "HKD" },
];
const optionsFeeStatus = [
  // { label: "未缴费", value: 1 },
  { label: "已缴费", value: 2 },
  { label: "无需缴费", value: 3 },
];
const disableDateOwner = {
  before: dayjs().format('YYYY-MM-DD')
}
const levelOptions = ref([
  //   {
  //     label: "全部",
  //     value: 0
  //   }
]);

interface ApplyRequest {
  currency: string;
  /**
   * ID
   */
  id: number;
  money: number;
  /**
   * 有效期类型，1：固定年限，2：自定义，3：长期有效
   */
  type: number;
  /**
   * 有效期填写的值
   */
  value: string;

  // 入会时间
  join_time: string;
  // 到期时间
  expire_time: string;
  // 会员级别
  level: number;
  level_name: string;
  team_name: string;
  show_tips: string;
  owner_value: string;
}

const emits = defineEmits(["onSend"]);

// 从公司组织架构导入
// const onImportFromCompany = () => {
// 	emits('onImportFromCompany');
// };
// // 手工建立组织架构
// const onHandCreate = () => {
// 	emits('onHandCreate');
// };
const formObj: ApplyRequest = {
  currency: "",
  id: 0,
  money: 0,
  type: 0,
  value: "",

  join_time: "",
  expire_time: "",
  level: "",
  level_name: "",
  team_name: "",
  show_tips: "",
  owner_value: "", // 自定义时间
};
const formData = reactive(formObj);

watch(
  () => formData.value,
  (val) => {
    formData.show_tips = dayjs(formData.expire_time)
      .add(Number(val), "year")
      .format("YYYY-MM-DD");
  }
);

const visible = ref(false);

// const settingValue = ref(null);

// 有效期选择
const changeRadio = (value) => {
  if (value === 2) {
    formData.show_tips = formData.owner_value;
  } else if (value === 1) {
    formData.show_tips = dayjs(formData.expire_time)
      .add(Number(formData.value), "year")
      .format("YYYY-MM-DD");
  }
};

const changeOwnDate = (value, context) => {
  if (context.trigger === "clear") {
  }
  console.log(value);
  formData.show_tips = value;
};

const initForm = (data) => {
  formData.id = data.id;
  formData.currency = "";
  formData.money = 0;
  formData.type = data.no_expire ? 3 : 1;
  formData.value = "1";
  formData.owner_value = "";
  formData.expire_time = data.expire_time || "--";
  formData.level_name = data.level_name;
  formData.level = data.level;

  formData.team_name = data.type === 1 ? data.team_name : data.name;
  formData.join_time = data.join_time;

  formData.owner_value = dayjs(formData.expire_time)
    .add(1, "year")
    .format("YYYY-MM-DD");
  formData.show_tips = dayjs(formData.expire_time)
    .add(1, "year")
    .format("YYYY-MM-DD");
};

const onGetMemberJobsDetail = async (level) => {
  let result = null;
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      result = await getMemberJobsDetailAxios(level);
      result = getResponseResult(result);
      if (!result) {
        reject();
        return;
      }
      formData.currency = result.data.currency;
      formData.level_name = result.data.level_name;
      // formData.level = result.data.level;
      formData.money = Number(result.data.money / 100);
      resolve(result.data);
    } catch (error) {
      const errMsg = error instanceof Error ? error.message : error;
      MessagePlugin.error(errMsg);
      reject();
    }
  });
};

const onValidateNumber = (context) => {
  console.log(context)
  const { error } = context;

  if(error === 'below-minimum') {
      MessagePlugin.error('最小值为1');
      formData.value = '1';

  }
}

const onListenNumberChange = () => {
  console.log(formData.value)
  if(!formData.value) {
    formData.value = 1;
  }
}

const onOpen = (data: any) => {
  console.log(data);
  initForm(data);
  getMemberJobsList();
  console.log(formData);
  if (data.level) {
    onGetMemberJobsDetail(data.level);
  }
  visible.value = true;
};
const onClose = () => {
  visible.value = false;
};
const form = ref(null);
const onSave = () => {
  form.value.validate().then((validateResult) => {
    if (validateResult && Object.keys(validateResult).length) {
      const firstError = Object.values(validateResult)[0]?.[0]?.message;
      MessagePlugin.warning(firstError);
    } else {

      if (formData.type === 2) {
        // 自定义时
        if (dayjs(formData.owner_value).diff(dayjs(formData.join_time)) < 0) {
          return MessagePlugin.error('会员有效期的日期不得小于入会时间');
        }
        if(dayjs(formData.owner_value).diff(dayjs()) < 1) {
          return MessagePlugin.error('会员有效期的日期不得小于当前时间');
        }
      } else if(formData.type === 1) {
        // if(dayjs(formData.owner_value).diff(dayjs()) < 1) {
        //   return MessagePlugin.error('会员有效期的日期不得小于当前时间');
        // }
      }

      const params = {
        ...toRaw(formData),
      };
      params.money = priceRecovery(params.money);
      if (params.type === 2) {
        params.value = params.owner_value;
      }
      // console.log(params);
      emits("onSend", toRaw(params));
    }
  });
};

// 获取会员职务列表
const getMemberJobsList = async () => {
  // teamId
  // params.page = pagination.current;
  // params.pageSize = pagination.pageSize;
  try {
    let result: any = await getMemberJobsListAxios();
    console.log(result, 'resultresult');
    result = getResponseResult(result);
    if (!result) return;

    result.data = result.data.filter((v) => v.status != 1);

    levelOptions.value = [
      ...result.data.map((v) => ({
        label: v.level_name,
        value: v.id,
        data: v
      })),
    ];
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : error;
    MessagePlugin.error(errMsg);
  }
};

const changeLevel = (e, event) => {
  console.log(e, event, '内容123');
  formData.money = Number(event.option.data.money / 100);
  formData.level = event.option.data.id;
  formData.level_name = event.option.data.label;
};

defineExpose({
  onOpen,
  onClose,
});
</script>

<style lang="less" scoped>
:deep(.t-form__label) {
  // word-wrap: break-word;
  // white-space: wrap !important;
  label {
    display: flex;
    align-items: center;
    // margin-bottom: 8px;
  }
  color: var(--text-kyy-color-text-3, #828da5);

  /* kyy_fontSize_2/regular */
  font-family: PingFang SC;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
  // min-height: 22px;
}


// .t-form-inline .t-form__item {
//   margin-right: 24px;
//   &:nth-child(2n) {
//     margin-right: 0;
//   }
// }
.t-radio-group.t-radio-group__outline {
  column-gap: 24px;
}
.setRow {
  display: flex;
  flex-direction: column;
  &-item {
    display: flex;
    align-items: center;
    &-tips {
      font-size: 14px;

      font-weight: 400;
      color: #e66800;
    }
  }
}
.form {
  &-row {
    width: 100%;
  }
}
.info {
  &-text {
    font-size: 14px;

    font-weight: 400;
    color: #13161b;
  }
}
.t-button + .t-button {
  margin-left: 0;
}
.header {
  display: flex;
  &-svg {
    width: 22px;
    height: 22px;
    color: #2069e3;
    margin-right: 8px;
  }
  &-title {
    font-size: 16px;

    font-weight: 700;
    color: #13161b;
  }
}
.toBody {
}
// :deep(.t-dialog--default) {
// 	padding-bottom: 0;
// }
.footer {
  display: flex;
  justify-content: flex-end;
  gap: 5px;
}
.t-dialog__body {
}
</style>
