<template>
  <div>
    <t-dialog v-model:visible="visible" :close-btn="false" :header="true" width="872">
      <template #header>
        <div style="display: flex; align-items: center; width: 100%; justify-content: space-between">
          <div>{{ t("ad.bjnr") }}</div>
          <img style="width: 24px; cursor: pointer; height: 24px" src="@/assets/<EMAIL>" @click="onClose" />
        </div>
      </template>
      <div class="content-box">
        <editFormDataPage
          ref="editFormDataPageRef"
          v-if="visible"
          :page="false"
          @callBackFormData="callBackFormData"
        ></editFormDataPage>
      </div>
      <template #footer>
        <div class="footer">
          <t-button theme="default" variant="outline" @click="onClose"> 取消 </t-button>
          <t-button theme="primary" @click="onSave"> {{ t("ad.qr") }} </t-button>
        </div>
      </template>
    </t-dialog>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted ,nextTick} from "vue";
import { DialogPlugin, MessagePlugin } from "tdesign-vue-next";
import { debounce } from "lodash";
import { useImageUpload } from "@/views/square/hooks/upload";
import { getFileType } from "@/views/message/service/utils";
import { AddIcon, Icon } from "tdesign-icons-vue-next";
import CropperDialog from "@renderer/views/square/components/CropperDialog.vue";
import editFormDataPage from "@/views/member/member_home/panel/mark-advertising/components/editFormData.vue";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const visible = ref(false);
const openWin = async (teamId, flag, objs, infoData) => {
  console.log(infoData, "infoData----------------infoData----------------infoData");
  visible.value = true;
  await nextTick();
  if (flag) {
    editFormDataPageRef.value.setValueToEditFormData(objs, infoData);
  }
};
const onClose = () => {
  visible.value = false;
};
const editFormDataPageRef = ref(null);
const emits = defineEmits(["callBackFormData"]);
const callBackFormData = (val) => {
  //
  emits("callBackFormData", val);
  console.log(val, "valvalval编辑的时候");
};
const onSave = () => {
  editFormDataPageRef.value.onSave();
  console.log(editFormData, "editFormDataeditFormDataeditFormData");
};
defineExpose({
  openWin,
  closeWin: onClose,
});
</script>

<style lang="less" scoped>
/* ui规定滚动条样式 */
::-webkit-scrollbar {
 width: 6px !important;
 background-color: transparent !important;

}
::-webkit-scrollbar-track {
  background-color: transparent !important;
}
::-webkit-scrollbar-thumb {
  background: var(--icon-kyy_color_icon_transparent, rgba(26, 33, 57, 0.36))!important;
  background-color: var(--icon-kyy_color_icon_transparent, rgba(26, 33, 57, 0.36))!important;
  border-radius: 4px!important;
}

.content-box {
  height: 408px;
  overflow-y: overlay;
  margin-top: 24px;
}
.content-box-bgc {
  width: 808px;
}
.img1 {
  border-radius: 11.139px;
  position: absolute;
  top: 88px;
  left: 31px;
  width: 544px;
  height: 200px;
}
.img2 {
  position: absolute;
  top: 89px;
  border-radius: 11.139px;
  right: 31px;
  width: 208px;
  height: 96px;
}
.img3 {
  position: absolute;
  top: 192px;
  right: 31px;
  width: 208px;
  height: 96px;
}

.sjbox {
  display: flex;
  padding: 12px;
  align-items: center;
  gap: 12px;
  align-self: stretch;
  border-radius: 8px;
  background: var(--bg-kyy_color_bg_deep, #f5f8fe);
}
.gchbox {
  align-items: center;
  border-radius: 8px;
  background: var(--bg-kyy_color_bg_deep, #f5f8fe);
  display: flex;
  width: 412px;
  padding: 12px;
  gap: 16px;
}
.gchbox-img {
  width: 48px;
  height: 48px;
  border-radius: 50%;
}
.gchbox-lable {
  overflow: hidden;
  color: var(--text-kyy_color_text_3, #828da5);
  text-overflow: ellipsis;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px; /* 166.667% */
  width: 100%;
  margin: 0 4px;
}
.gcbox {
  display: flex;
  padding: 12px;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  gap: 12px;
  border-radius: 8px;
  border: 1px solid var(--brand-kyy_color_brand_default, #4d5eff);
  background: var(--bg-kyy_color_bg_deep, #f5f8fe);

  .gchbox-title {
    overflow: hidden;
    color: var(--text-kyy_color_text_1, #1a2139);
    text-overflow: ellipsis;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px; /* 150% */
  }
}
.gchbox-tag {
  display: flex;
  height: 20px;
  min-height: 20px;
  max-height: 20px;
  padding: 0px 4px;
  justify-content: center;
  align-items: center;
  gap: 4px;
  border-radius: var(--kyy_radius_tag_s, 4px);
  background: var(--kyy_color_tag_bg_kyyBlue, #e4f5fe);
  overflow: hidden;
  color: var(--kyy_color_tag_text_kyyBlue, #21acfa);
  text-align: center;
  text-overflow: ellipsis;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px; /* 166.667% */
}
.sjbox-info-box {
  display: flex;
  align-items: center;
  gap: 4px;
  flex-wrap: wrap;
  .sjbox-info {
    display: flex;
    width: 100%;
    align-items: center;
    gap: 8px;
  }
  .tags {
    display: flex;
    height: 20px;
    min-height: 20px;
    max-height: 20px;
    padding: 0px 4px;
    justify-content: center;
    align-items: center;
    gap: 4px;
    border-radius: var(--kyy_radius_tag_s, 4px);
    background: var(--kyy_color_tag_bg_cyan, #e6f9f8);
    color: var(--kyy_color_tag_text_cyan, #11bdb2);
    text-align: center;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px; /* 166.667% */
  }
  .sjbox-title {
    overflow: hidden;
    color: var(--text-kyy_color_text_1, #1a2139);
    text-overflow: ellipsis;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px; /* 157.143% */
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    word-break: break-all;
    max-width: 300px;
  }
  .info-lable {
    overflow: hidden;
    color: var(--text-kyy_color_text_3, #828da5);
    text-overflow: ellipsis;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
  .sjbox-info-group {
    width: 24px;
    height: 24px;
    border-radius: 50%;
  }
  .info-value {
    overflow: hidden;
    color: var(--text-kyy_color_text_1, #1a2139);
    text-overflow: ellipsis;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
  }
}
.sjbox-left {
  img {
    width: 121px;
    height: 121px;
    border-radius: 8px;
  }
}
.diy-lable {
  color: var(--text-kyy_color_text_3, #828da5);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  padding-left: 10px;
  position: relative;
  line-height: 22px;
}
.flexbox {
  display: flex;
  align-items: center;
}
.chang-lable {
  color: var(--color-button_text_brand-kyy_color_button_text_brand_font_default, #4d5eff);
  text-align: center;
  cursor: pointer;
  font-size: 14px;
  margin-left: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
}
.star-icon {
  font-size: 24px;
}
.star {
  font-size: 24px;
  width: 24px;
  height: 24px;
}
.diy-lable::after {
  content: "*";
  position: absolute;
  top: 0;
  left: 0;
  color: var(--error-kyy_color_error_default, #d54941);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
}

.content-bgc {
  width: 375px;
  height: 550px;
  border-radius: 17px 17px 0 0;
  border: 1px solid var(--border-kyy_color_border_default, #d5dbe4);
  border-bottom: none;
}

.changesj {
}
:deep(.assetUrlUpload) {
  display: flex;
  justify-content: space-between;
  align-items: end;
  .t-link {
    display: none !important;
  }

  .t-upload__tips {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: space-between;
    gap: 8px;
    margin-top: 0;
    margin-left: 16px;
    color: var(--text-kyy-color-text-5, #acb3c0);

    /* kyy_fontSize_1/regular */
    font-family: PingFang SC;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    /* 166.667% */

    .t-button {
      border-radius: var(--radius-kyy-radius-button-s, 4px);
      border: 1px solid var(--color-button-border-kyy-color-button-border-border-dedault, #d5dbe4);
      color: var(--color-button-border-kyy-color-button-border-text-default, #516082);
      text-align: center;

      /* kyy_fontSize_2/regular */
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      /* 157.143% */
    }
  }

  .t-upload__card-container {
    width: 128px;
    height: 96px;
    border-radius: 8px;
    border: 1px solid var(--kyy_color_upload_border_default, #d5dbe4);
  }

  .t-upload__card-content {
    display: inline-block;
    width: 128px;
    height: 96px;
    border-radius: 8px;
    border: 1px solid var(--kyy_color_upload_border_default, #d5dbe4);
    padding: 0;
  }
}
.img4 {
  position: absolute;
  top: 262px;
  left: 239px;
  width: 343px;
  height: 108px;
  border-radius: 11px;
}
.card-box {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
  .card-body-box {
    display: flex;
    padding: 24px 16px;
    align-items: center;
    gap: 16px;
    width: 460px;
    align-self: stretch;
  }
  .card-body-right {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    flex: 1;
    gap: 8px;
  }
  .card-body-right-item {
    width: 100%;
    display: flex;
    align-items: center;
    color: var(--text-kyy_color_text_1, #1a2139);

    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
  }
  .card-body-left:hover {
    border: 1px solid var(--brand-kyy_color_brand_hover, #707eff);
  }

  .card-body-left {
    padding: 12px;
    width: 184px;
    border-radius: 4px;
    background: var(--bg-kyy_color_bg_light, #fff);
    box-shadow: 0px 3px 8px 0px rgba(0, 0, 0, 0.12);
    border: 1px solid transparent;
    cursor: pointer;
    height: 158px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .item-card {
    display: flex;
    width: 464px;
    flex-direction: column;
    align-items: flex-start;
    border-radius: 8px;
    border: 1px solid var(--border-kyy_color_border_default, #d5dbe4);
    background: var(--bg-kyy_color_bg_light, #fff);
    .header-card {
      display: flex;
      padding: 12px 16px;
      width: 100%;
      align-items: center;
      gap: 24px;
      background: var(--bg-kyy_color_bg_deepest, #eceff5);
      justify-content: space-between;
      .card-title {
        color: var(--text-kyy_color_text_1, #1a2139);

        /* kyy_fontSize_2/bold */
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 22px; /* 157.143% */
        margin-right: 24px;
      }
    }
  }
}
.upload-box {
  cursor: pointer;
  display: flex;
  width: 128px;
  height: 96px;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  gap: 4px;
  border-radius: 8px;
  border: 1px solid var(--kyy_color_upload_border_default, #d5dbe4);
  background: var(--kyy_color_upload_bg, #fff);
  color: var(--kyy_color_upload_text_default, #516082);
  text-align: center;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;

  /* 157.143% */
  img {
    width: 48px;
    height: 48px;
  }
}

.upload-box-tips {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: space-between;
  gap: 8px;
  margin-top: 0;
  margin-left: 16px;
  color: var(--text-kyy-color-text-5, #acb3c0);

  /* kyy_fontSize_1/regular */
  font-family: PingFang SC;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  /* 166.667% */

  .t-button {
    border-radius: var(--radius-kyy-radius-button-s, 4px);
    border: 1px solid var(--color-button-border-kyy-color-button-border-border-dedault, #d5dbe4);
    color: var(--color-button-border-kyy-color-button-border-text-default, #516082);
    text-align: center;

    /* kyy_fontSize_2/regular */
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    /* 157.143% */
  }
}
</style>
