interface PostPreview {
  publishId: string,
  squareName: string
}

/**
 * PostDetail，动态详情
 */
interface PostShare {
  /**
   * 文章内容（动态类型为文章时才有该字段）
   */
  article?: SquareArticle;
  /**
   * 转发动态（动态类型为转发时才有该字段）
   */
  forwardPost?: ForwardPost;
  /**
   * 动态
   */
  post: Post;
  /**
   * 广场号
   */
  square: Square;

}

/**
 * 文章内容（动态类型为文章时才有该字段）
 *
 * SquareArticle，动态详情
 */
interface SquareArticle {
  /**
   * 文章内容（动态类型为文章时才有该字段）
   */
  article: Article;
  /**
   * 广场号信息
   */
  square: Square;
}

/**
 * 文章内容（动态类型为文章时才有该字段）
 *
 * Article，文章
 */
interface Article {
  /**
   * 封面图片
   */
  img: string;
  /**
   * 标题
   */
  title: string;
}

/**
 * 广场号信息
 *
 * Square，广场号基础信息
 */
interface Square {
  /**
   * 昵称
   */
  name: string;
  /**
   * 头像
   */
  avatar?: string;
}

/**
 * 转发动态（动态类型为转发时才有该字段）
 *
 * ForwardPost，动态详情
 */
interface ForwardPost {
  /**
   * 文章内容（动态类型为文章时才有该字段）
   */
  article?: SquareArticle;
  /**
   * 动态
   */
  post: Post;
  /**
   * 广场号信息
   */
  square: Square;
}

/**
 * 动态
 *
 * Post，动态
 */
interface Post {
  /**
   * 动态 ID
   */
  id?: string;
  /**
   * 图片
   */
  picture?: string[];
  /**
   * 内容类型
   */
  postType: PostType;
  /**
   * 文字内容
   */
  text?: string;
  /**
   * 视频
   */
  video?: string;
}

/**
 * 内容类型
 *
 * PostType，动态类型
 */
type PostType = 'ARTICLE' | 'FORWARD' | 'PICTURE' | 'TEXT' | 'VIDEO';

/**
 * 消息详情 Drawer 弹窗类型
 * @param square-helper 广场号助手
 * @param square-preview 广场号预览
 * @param square-moment 广场号动态
 * @param biz-opportunity 商机
 * @param biz-opportunity-helper 商机助手
 * @param order 订单
 * @param invoice 发票
 * @param invoice-refuse 发票拒绝
 * @param combo 套餐
 * @param square-invite 广场号邀请
 * @param album-expire 相册过期
 * @param zhixing-remind 执行提醒
 * @param zhixing-note 执行备注
 * @param zhixing-manifest 执行清单
 * @param scene 场景
 * @param service-content 服务内容
 * @param service-report 服务报告
 * @param service-appeal 服务申诉
 * @param service-appeal-detail 服务申诉详情
 * @param service-violation 服务违规
 * @param serviceContentDetailPanel 服务内容详情
 * @param send-vcard-chat-dialog 发送名片
 * @param approve 审批
 * @param approve-helper 审批助手
 * @param approve-comment 审批评论
 */
type MsgDetailDrawerType =
  | 'send-vcard-chat-dialog'
  | 'square-helper'
  | 'square-preview'
  | 'square-moment'
  | 'biz-opportunity'
  | 'biz-opportunity-helper'
  | 'order'
  | 'invoice'
  | 'invoice-refuse'
  | 'combo'
  | 'square-invite'
  | 'album-expire'
  | 'zhixing-remind'
  | 'zhixing-note'
  | 'zhixing-manifest'
  | 'scene'
  | 'service-content'
  | 'service-report'
  | 'service-appeal'
  | 'service-appeal-detail'
  | 'service-violation'
  | 'serviceContentDetailPanel'
  | 'approve'
  | 'approve-helper'
  | 'approve-comment'
  | 'cloud-disk-link'
  | 'merged-msg-detail'
  | 'security-verification-honor'
  | 'preview-release-honor'
  | 'security-verification-process'
  | 'security-verification-introduce'
  | 'security-verification-notice'
  | 'partner-order-info'
  | 'naas-result'
  | 'partner-refund-fill'
  | 'partner-refund-info'
  | 'consult-order-info'
  | 'consult-info'
  | 'ad-info'
  | 'receipt-withdraw-detail'
  | 'digital-apply-info'
  | 'activity-join-info'
  | 'popularize-post'
  | 'exclusive-renew'
  | 'policyExpross'
  | 'shop-apply'
  | 'shop-category-apply'
  | 'shop-check-detail'
  | 'culture-tourism'
  | 'culture-apply'

