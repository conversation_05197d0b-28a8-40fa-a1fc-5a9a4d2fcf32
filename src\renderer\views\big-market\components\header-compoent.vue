<template>
  <div
    class="index-header"
    :class="{
      'fixd-header-box2': fixdHeaderShow,
      'col-fixd-boxshow': props.colfixdBoolean,
      backfff: props.back,
    }"
  >
    <div class="logo-box" >
      <img src="http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/bigmarket/market.png" class="logo"  @click="homeRefresh" />
      <img
        src="http://ringkol-public.oss-cn-guangzhou.aliyuncs.com/web/electron/bigmarket/font_cn2.png"
        class="font_cn"
        @click="homeRefresh"
      />
      <div class="branch" v-if="branData.length" @click="homeRefresh">
        <div class="branch-item" :class="{ active: branData.length === 1 }" @click.stop="jumpIndex">
          {{ branData[0] }}
        </div>
        <template v-if="branData.length === 2">
          <iconpark-icon name="iconarrowright" class="iconarrowright" />
          <div class="branch-item" :class="{ active: branData.length === 2 }">
            {{ branData[1] }}
          </div>
        </template>
      </div>
    </div>
    <div
      class="search-bar no-border-search-bar"
      @click.stop="clickstop"
      @keyup.enter="toSeach"
      :class="{ searchBarTwo: dropdownShow, hidesearch: props.classify }"
    >
      <iconpark-icon name="iconsearch" class="iconsearch" />
      <t-input
        :maxlength="30"
        ref="inputRef"
        v-if="!props.classify"
        placeholder=""
        v-model="keyword"
        clearable
        @blur="handleBlur"
        @focus="handleFocus"
        @compositionstart="handleCompositionStart"
        @compositionend="handleCompositionEnd"
        style="height: 24px; width: 508px; margin: 0 12px 0 4px"
      />
      <div class="placeholder-container" v-if="!props.classify">
        <div
          v-for="(placeholderWrod, index) in placeholders"
          :key="index"
          :class="{
            show: currentPlaceholderIndex === index,
            hiddening: currentPlaceholderIndex !== index && oldIndex !== index,
            plhidden: oldIndex === index,
          }"
          class="placeholder"
          :style="{ zIndex: placeholders.length - index, color: dropdownShow ? '#828DA5' : '#ACB3C0' }"
        >
          {{ keyword || shouldHidePlaceholder ? '' : `大家都在搜：${placeholderWrod}` }}
        </div>
      </div>
      <div class="search-btn" @click="toSeach">搜索</div>

      <div class="dropdown-box" v-if="dropdownShow">
        <div class="dropdown-st">大家都在搜</div>
        <div class="word-list">
          <div
            class="word-item"
            v-for="(item, index) in placeholders"
            :key="'word' + index"
            @click.stop="serchKeywrod(item)"
          >
            {{ item }}
          </div>
        </div>
        <template v-if="hiswords.length">
          <div class="line"></div>
          <div class="opt" @click="drshowrun">
            <div class="dropdown-st">历史搜索</div>
            <div class="btns">
              <template v-if="!hisClearShow">
                <div class="btn btncl" @click="hisClearShow = true">
                  <iconpark-icon name="icondelete" class="icondelete" />
                  清除
                </div>
              </template>
              <template v-else>
                <div class="btn atv" @click="hisClearAll">清除全部</div>
                <div class="btn-line"></div>
                <div class="btn atv" @click="hisClearShow = false">完成</div>
              </template>
            </div>
          </div>
          <div class="his-list">
            <div
              class="word-tag"
              v-for="(item, index) in hiswords"
              :key="'word' + index"
              @click.stop="serchKeywrod(item)"
            >
              {{ item }}
              <iconpark-icon v-if="hisClearShow" name="iconerror" @click.stop="delhis(index)" class="icondelete" />
            </div>
          </div>
        </template>
      </div>
    </div>
    <div style="width: 216px; display: flex; justify-content: end;">
      <locationWidget
        ref="latWidgetRef"
        :address-text="addressText"
        :address-data="addressData"
        @click.stop="some"
        @edit-is-map-page-flag="editIsMapPageFlag"
        @edit-address-data="editAddressData"
        @edit-address-text="editAddressText"
      />
    </div>
  </div>
  <BMap :height="0" @initd="onMapInit" />
</template>

<script setup lang="ts">
import { onMounted, ref, watch, onUnmounted, computed } from 'vue';
import locationWidget from './location-widget.vue';
import { useBaiduMap } from '@renderer/components/common/map/hooks';
import { useNicheStore } from '@renderer/store/modules/niche';
import '@amap/amap-jsapi-types';
import { getWhatUWant } from '../apis';
import { getProfilesInfo } from '@renderer/utils/auth';
import { debounce } from 'lodash';

const profileInfo = getProfilesInfo();
const props = defineProps({
  fixdHeaderShow: {
    type: Boolean,
    default: false,
  },

  searchPage: {
    type: Boolean,
    default: false,
  },
  type: {
    type: Number,
    default: 1,
  },
  addressText: {
    type: String,
    default: '',
  },
  addressData: {
    type: Object,
    default: () => ({}),
  },
  keyword: {
    type: String,
    default: '',
  },
  colfixdBoolean: {
    type: Boolean,
    default: false,
  },
  back: {
    type: Boolean,
    default: false,
  },
  branData: {
    type: Array,
    default: () => [],
  },
  classify: {
    type: Boolean,
    default: false,
  },
});
const addressData = ref(null);
const keyword = ref(props.keyword);
watch(
  () => props.keyword,
  (newVal) => {
    keyword.value = newVal;
  },
);
const hiswords = ref([]);
const addressText = ref(null);
const nicheStore = useNicheStore();
const emit = defineEmits([
  'change-type',
  'toSaerch',
  'homeinit',
  'editIsMapPageFlag',
  'editAddressData',
  'editAddressText',
  'getData',
]);
// 地图 IP 定位
const { location, markerInfo, onMapInit } = useBaiduMap({
  onIPLocation() {
    const { point } = location.value;
    if (!nicheStore.positionTextDK) {
      addressData.value = [point.lng, point.lat];
      console.log(addressData.value, 'markerInfo', markerInfo.value);
      emit('editAddressData', addressData.value);
    }
  },
  onPointGeocoder() {
    console.log('onPointGeocode222r shop-header', markerInfo.value);
    if (!nicheStore.positionTextDK) {
      addressText.value = markerInfo.value.address;
      emit('getData', markerInfo.value.address);
    }
  },
});
const jumpIndex = () => {
  emit('homeinit');
};
const init = () => {
  const openid = profileInfo.openid;
  const key = `hisword-${openid}`;
  const hisword = localStorage.getItem(key);
  if (hisword) {
    hiswords.value = JSON.parse(hisword);
  }
  console.log('initprops.addressText', props.addressText);
  console.log('initprops.addressText', nicheStore.positionTextDK);
  addressText.value = nicheStore.positionTextDK;
  console.log('init', addressText.value);
  getWhatUWantReq();
};
onMounted(() => {
  console.log('props.colfixdBoolean', props);
  init();
  if (showHint.value && !props.classify) {
    startInterval();
  }
});
const getWhatUWantReq = async () => {
  const res = await getWhatUWant({});
  console.log('getWhatUWantReq', res);
  if (res.data) {
    placeholders.value = res.data.data?.names;
  }
};
watch(
  () => props.addressText,
  (newVal) => {
    addressText.value = newVal;
  },
);

const homeRefresh = () => {
  emit('homeinit');
};
const setHiswords = () => {
  const openid = profileInfo.openid;
  const key = `hisword-${openid}`;
  // 对 hiswords.value 进行去重处理
  const uniqueHiswords = [...new Set(hiswords.value)];
  // 只存去重后最新的6个
  const latestSixHiswords = uniqueHiswords.slice(0, 6);
  localStorage.setItem(key, JSON.stringify(latestSixHiswords));
};
const toSeach = async () => {
  console.log('toSeach');
  if (!keyword.value) {
    keyword.value = placeholders.value[currentPlaceholderIndex.value];
  }
  hiswords.value.unshift(keyword.value);
  hiswords.value = [...new Set(hiswords.value)]
  setHiswords();
  console.log('toSeachword', keyword.value);
  emit('toSaerch', keyword.value);
  // setTimeout(() => {
  //   handleBlur();
  // }, 100);
};
const editAddressData = (e) => {
  console.log('e', e);
  emit('editAddressData', e);
  addressText.value = e.title;
  addressData.value = [e.location.lng, e.location.lat];
  console.log('addressText', addressText.value);
  console.log('addressData', addressData.value);
};

const editIsMapPageFlag = (e, name) => {
  console.log('e', e);
  emit('editIsMapPageFlag', e, name);
  addressText.value = name;
  addressData.value = [e.location.lng, e.location.lat];
  console.log('addressText2', addressText.value);
  console.log('addressData3', addressData.value);
};
const some = () => {
  console.log('toSeach');
};

const editAddressText = (text) => {
  console.log('editAddressText333', text);
  addressText.value = text;
  nicheStore.positionTextDK = text;
};
const latWidgetRef = ref(null);
const latClose = () => {
  latWidgetRef.value?.close();
};

const showHint = ref(true);
const placeholders = ref([]);
const currentPlaceholderIndex = ref(0);
const oldIndex = ref(0);
const intervalId = ref(null);

const updatePlaceholder = () => {
  oldIndex.value = currentPlaceholderIndex.value;
  currentPlaceholderIndex.value = (currentPlaceholderIndex.value + 1) % placeholders.value.length;
  setTimeout(() => {
    oldIndex.value = 100;
  }, 2000);
};

const startInterval = () => {
  intervalId.value = setInterval(updatePlaceholder, 3000); // Change every 3 seconds
};

const stopInterval = () => {
  clearInterval(intervalId.value);
};
const dropdownShow = ref(false);
const handleFocus = () => {
  console.log('handleFocus');
  showHint.value = false;
  // stopInterval();
  dropdownShow.value = true;
};

const handleBlur = () => {
  setTimeout(() => {
    if (!opting.value) {
      showHint.value = true;
      dropdownShow.value = false;
      hisClearShow.value = false;
      // startInterval();
    }
  }, 200);
};
const opting = ref(false);
const drshowrun = () => {
  opting.value = true;
  showHint.value = false;
  // stopInterval();
  dropdownShow.value = true;
  setTimeout(() => {
    opting.value = false;
  }, 200);
};

onUnmounted(() => {
  stopInterval();
});
const hisClearShow = ref(false);
const hisClearAll = () => {
  hiswords.value = [];
  hisClearShow.value = false;
  setHiswords();
};
const delhis = (index) => {
  hiswords.value.splice(index, 1);
  setHiswords();
};

const serchKeywrod = (word) => {
  console.log('触发了明i', word);
  
  keyword.value = word;
  toSeach();
};
const inputRef = ref(null);

const closeSearchDr = debounce(() => {
  console.log('closeSearchDrcloseSearchDr');
  if (dropdownShow.value) {
    // showHint.value = true;
    dropdownShow.value = false;
    // startInterval();
    inputRef.value.focus();
    inputRef.value.blur();
  }
}, 3);

const clickstop = (e) => {
  console.log(e);
  if(!dropdownShow.value) {
    dropdownShow.value = true;
  }
};
const ichange = (e) => {
  console.log(e);
};
const isComposing = ref(false);
// 计算是否应该隐藏placeholder
const shouldHidePlaceholder = computed(() => {
  return isComposing.value || !!keyword.value.trim();
});

// 事件处理
const handleCompositionStart = () => {
  isComposing.value = true;
};

const handleCompositionEnd = () => {
  isComposing.value = false;
};

defineExpose({
  latClose,
  closeSearchDr,
});
</script>

<style lang="less" scoped>
.index-header {
  display: flex;
  justify-content: center;
  width: 100%;
  align-items: center;
  padding: 16px;

  .logo-box {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    min-width: 293px;
    display: flex;
    align-items: center;
    margin-right: 32px;

    .logo {
      width: 32px;
      height: 32px;
    }

    .font_cn {
      width: 89px;
      height: 40px;
    }

    .name {
      color: var(--text-kyy_color_text_2, #516082);
      font-family: YouSheBiaoTiHei;
      font-size: 32px;
      font-style: normal;
      font-weight: 400;
      line-height: 40px;
      /* 125% */
    }
  }

  .search-bar {
    display: flex;
    width: 600px;
    height: 32px;
    padding: 0px 2px 0px 4px;
    align-items: center;
    flex-shrink: 0;
    border-radius: 4px;
    border: 2px solid var(--brand-kyy_color_brand_hover, #707eff);
    background: var(--input-kyy_color_input_bg_default, #fff);
    position: relative;
    margin-right: 32px;
    .search-btn {
      width: 44px;
      height: 24px;
      flex-shrink: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      border-radius: 2px;
      background: #eaecff;
      color: var(--brand-kyy_color_brand_default, #4d5eff);
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;

      .iconsearch {
        font-size: 20px;
        color: #fff;
      }
    }
    .search-btn:hover{
      border-radius: 2px;
background: var(--bg-kyy_color_bgBrand_foucs, #DBDFFF);
color: var(--brand-kyy_color_brand_default, #4D5EFF);
font-family: "PingFang SC";
font-size: 14px;
font-style: normal;
font-weight: 400;
line-height: 22px; /* 157.143% */
    }
  }
}

.tag-mu {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-right: 24px;

  .tag-box {
    display: flex;
    width: 80px;
    padding: 4px 0px;
    text-align: center;
    justify-content: center;
    align-items: center;
    gap: 10px;
    cursor: pointer;
  }

  .tag-box:hover {
    border-radius: 4px;
    background: var(--bg-kyy_color_bgBrand_foucs, #dbdfff);
  }

  .tag-box-shop {
    border-radius: 4px;
    color: var(--warning-kyy_color_warning_active, #be5a00);
    text-align: center;
    font-family: 'PingFang SC';
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
    /* 150% */
  }

  .tag-box-shop:hover {
    color: var(--warning-kyy_color_warning_active, #be5a00);
    text-align: center;

    /* kyy_fontSize_3/bold */
    font-family: 'PingFang SC';
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
    /* 150% */
  }

  .tag-box-niche {
    border-radius: 4px;
    color: var(--cyan-kyy_color_cyan_default, #11bdb2);
    text-align: center;
    font-family: 'PingFang SC';
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
    /* 150% */
  }

  .tagBAct {
    border-radius: 4px;
    background: #707eff;
    color: var(--text-kyy_color_text_white, #fff);
    text-align: center;
    font-family: 'PingFang SC';
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
    /* 150% */
  }

  .tagBAct:hover {
    background: #707eff;
    color: var(--text-kyy_color_text_white, #fff);
  }
}

.line-geduan {
  width: 1px;
  height: 24px;
  background: var(--divider-kyy_color_divider_deep, #d5dbe4);
  margin: 0px 16px;
}

.fixd-header-box2 {
  width: 100%;
  margin: 0 auto;
  position: fixed;
  top: 40px;
  left: 0px;
  background: #fff;
  box-shadow: 0px 3px 8px 0px rgba(0, 0, 0, 0.12);
  z-index: 1000;
}

.col-fixd-boxshow {
  box-shadow: none;
}

.iconsearch {
  font-size: 20px;
  color: #4d5eff;
}

:deep(.search-bar .t-input) {
  padding: 0 !important;
}

.placeholder-container {
  position: absolute;
  top: 4px;
  left: 32px;
  pointer-events: none;
}

.placeholder {
  position: absolute;
  color: #828da5;
  transition: opacity 0.5s ease-in-out, transform 0.5s ease-in-out;
  white-space: nowrap;
}

.plhidden {
  opacity: 0;
  transform: translateY(-10px);
}

.show {
  opacity: 1;
  transform: translateY(0px);
}

.hiddening {
  opacity: 0;
  transform: translateY(10px);
}

.dropdown-box {
  display: flex;
  width: 600px;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
  position: absolute;
  top: 30px;
  background: #fff;
  left: -2px;
  z-index: 1001;
  border: 2px solid var(--brand-kyy_color_brand_default, #4d5eff);
  background: var(--bg-kyy_color_bg_light, #fff);
  box-shadow: 0px 9px 8px 0px rgba(0, 7, 66, 0.16);
  border-top: none;
  padding: 8px 6px;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;

  .dropdown-st {
    color: var(--text-kyy_color_text_3, #828da5);
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    padding: 0 4px;
  }

  .word-list {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    justify-content: flex-start;
    gap: 12px;
    row-gap: 4px;
.word-item:hover{
  border-radius: 4px;
background: var(--bg-kyy_color_bg_list_hover, #F3F6FA);
}
    .word-item {
      display: flex;
      padding: 4px;
      width: 286px;
      align-items: center;
      gap: 4px;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;
      overflow: hidden;
      color: var(--text-kyy_color_text_1, #1a2139);
      text-overflow: ellipsis;
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      cursor: pointer;
    }
  }

  .line {
    display: flex;
    min-height: 1px;
    max-height: 1px;
    padding: 0px 8px;
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
    align-self: stretch;
    background: var(--divider-kyy_color_divider_light, #eceff5);
  }

  .opt {
    display: flex;
    justify-content: space-between;
    width: 100%;

    .btns {
      display: flex;
      align-items: center;
      gap: 8px;

      .btn {
        display: flex;
        cursor: pointer;
      }

      .btn-line {
        display: flex;
        height: 16px;
        min-width: 1px;
        max-width: 1px;
        align-items: flex-start;
        gap: 4px;
        background: var(--divider-kyy_color_divider_light, #eceff5);
      }

      .btncl:hover {
        color: #707eff;

        .icondelete {
          color: #707eff;
        }
      }

      .atv {
        color: #4d5eff;
      }
    }
  }

  .his-list {
    display: flex;
    align-items: center;
    align-content: center;
    gap: 8px;
    align-self: stretch;
    flex-wrap: wrap;

    .word-tag {
      display: flex;
      padding: 4px 8px;
      align-items: center;
      gap: 4px;
      border-radius: 4px;
      border: 1px solid var(--divider-kyy_color_divider_light, #eceff5);
      color: var(--text-kyy_color_text_1, #1a2139);
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      cursor: pointer;
    }

    .word-tag:hover {
      border: 1px solid var(--border-kyy_color_border_hover, #707eff);
      color: var(--brand-kyy_color_brand_hover, #707eff);
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;

      .icondelete {
        color: #707eff;
      }
    }
  }
}

.searchBarTwo {
  border: 2px solid var(--brand-kyy_color_brand_default, #4d5eff) !important;
  border-bottom: none !important;
  border-bottom-left-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
}

.icondelete {
  color: #516082;
  font-size: 20px;
}

.backfff {
  background: #fff;
}

.branch {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 8px;
  .branch-item {
    color: var(--kyy_color_breadcrumb_text_default, #828da5);
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 157.143% */
    padding: 4px 8px;
  }
  .branch-item:hover{
    border-radius: 4px;
background: var(--bg-kyy_color_bg_list_hover, #F3F6FA);
  }
  .iconarrowright {
    font-size: 16px;
    color: #828da5;
  }
  .active {
    color: var(--kyy_color_breadcrumb_text_active, #1a2139);
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px; /* 157.143% */
  }
}

.hidesearch {
  opacity: 0;
}
</style>
