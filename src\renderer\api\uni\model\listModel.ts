// 获取leader
export interface Leader {
  id: number,
  name: string
}
export interface Positions {
  departmentName: string,
  jobName: string
}
export interface Role {
  roleName: string
}
// 成员信息
export interface MemberInfo {
  name: string,
  staffId: number,
  no: string,
  avatar: string,
  leader: number,
  leaderName: string,
  telCode: string,
  telephone: string,
  roles: Array<Role>,
  positions: Array<Positions>,
}

// 获取成员列表信息
export interface MemberInfoList {
  total: number,
  list: Array<MemberInfo>
}

