<template>
  <t-dialog
    v-model:visible="qrcodeDialogFlag"
    :close-btn="false"
    :header="true"
    :cancel-btn="null"
    :confirm-btn="null"
    :footer="true"
    :z-index="2503"
    width="728"
    attach="body"
    :close-on-esc-keydown="false"
    :close-on-overlay-click="false"
    class="qrcodeDialog"
  >
    <template #header>
      <div
        style="
          display: flex;
          align-items: center;
          width: 100%;
          justify-content: space-between;
        "
      >
        <div>{{t('order.zf')}}</div>
        <div v-if="rowData?.subTitle" class="sub-title">类目名称：{{ rowData?.subTitle }}</div>
        <img
          style="width: 16px; cursor: pointer; height: 16px"
          src="@/assets/<EMAIL>"
          @click="qrcodeDialogClose"
        />
      </div>
    </template>
    <div class="qrcodes">
      <div class="headM">
        <span>{{ t("payment.payableAmount") }}：</span>
        <span class="num">
          {{ rowData.pay === "CN" ? "￥ " : "MOP "
          }}{{ addCommasToNumber(rowData?.amount?.toFixed(2)) }}</span>
      </div>
      <div class="lins">
        <div class="lins-cneter" />
      </div>
      <div class="body-box">
        <div class="payWayStrclass">
          {{ payWayStr }}
        </div>
        <div v-if="times === 0" class="tips" style="color: #da2d19">
          {{ t("payment.qrCodeHasExpired") }},<span
            style="color: #2069e3; cursor: pointer"
            @click="refsqrCode"
          >刷新</span>{{ t("payment.qrCodeHasExpired1") }}
        </div>

        <div v-else class="tips">
          {{ t("payment.qrCodeExTime") }}<span>{{ times }}</span>
          {{ t("payment.qrCodeExTime1") }}
        </div>
        <div
          class="qrcode-img-box"
          :class="times === 0 ? 'expires' : ''"
          @click="refsqrCode"
        >
          <qrcode-vue
            style="width: 128px; height: 128px"
            :value="link"
            :size="128"
            level="H"
          />
        </div>
        <div v-if="payWayStr === '云闪付'" class="ysf-box">
          <div>
            <div>{{ t('order.plaisit') }}{{ payWayStr }}{{ t('order.saocode') }}</div>
            <div>{{ t("payment.scanQRCodePaymentForPayment") }}</div>
          </div>
        </div>

        <div v-else>
          <div class="tipsqrcod">
            {{ t("payment.scanQRCodePaymentForPayment") }}
          </div>

          <div class="zf-box">
            <div
              v-if="resPayRegionDataFlag.includes(3)"
              class="zfb-box"
              :class="resPayRegionDataFlag.includes(2)?'mr0':'mar40'"
            >
              <img src="@/assets/img/mypay.svg" />
            </div>
            <div
              v-if="resPayRegionDataFlag.includes(2)"
              class="zfb-box"
              style="margin-right: 40px"
            >
              <img src="@/assets/img/zfb.svg" />
            </div>
            <div v-if="resPayRegionDataFlag.includes(1)" class="zfb-box">
              <img src="@/assets/img/wx.svg" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </t-dialog>
  <t-dialog
    v-model:visible="tipDialogPayOkFlag"
    :close-btn="false"
    :header="true"
    :cancel-btn="null"
    :confirm-btn="null"
    :footer="true"
    width="384"
    :close-on-esc-keydown="false"
    :close-on-overlay-click="false"
    class="tipQrcodeDialog"
  >
    <div style="text-align: center">
      <img
        v-if="paySteta === '支付成功'"
        style="width: 48px; height: 48px; margin-bottom: 12px"
        src="@/assets/svg/clouddisk/icon_success.svg"
      />
      <div class="paysess">
        {{ paySteta }}
      </div>
      <img
        v-if="paySteta === t('payment.paymentErr')"
        style="width: 48px; height: 48px; margin-bottom: 12px"
        src="@/assets/img/icon_error.svg"
      />
      <img
        v-if="paySteta === t('payment.paymentClose')"
        style="width: 48px; height: 48px; margin-bottom: 12px"
        src="@/assets/img/icon_warning.svg"
      />
      <div v-if="paySteta === '支付成功'" class="paynum">
        {{ rowData.pay === "CN" ? "￥" : "MOP" }}
        {{ addCommasToNumber(rowData?.amount?.toFixed(2)) }}
      </div>
      <div
        v-if="paySteta === t('payment.paymentClose')"
        class="play-customer-service"
      >
        {{ t("payment.orderStatusHasChanged") }}
      </div>

      <div
        v-if="paySteta === t('payment.paymentErr')"
        class="play-customer-service"
      >
        {{ payStetaTip }}
      </div>

      <t-button style="width: 88px" @click="isOk">{{
        paySteta === t("payment.paymentClose") ? t("payment.close") : "知道了"
      }}</t-button>
    </div>
  </t-dialog>
  <t-dialog
    v-model:visible="PayPaddingOkFlag"
    :close-btn="false"
    :header="true"
    :cancel-btn="null"
    :confirm-btn="null"
    :footer="true"
    width="384"
    :close-on-esc-keydown="false"
    :close-on-overlay-click="false"
    class="tipQrcodeDialog"
    attach="body"
  >
    <div style="text-align: center; height: 170px">
      <img class="rotate" src="@/assets/loading.png" />
      <div class="pay-text">{{ t('order.paycximg') }}</div>
      <t-button
        style="margin-top: 20px; width: 88px"
        @click="(PayPaddingOkFlag = false), (qrcodeDialogFlag = false)"
      >{{ t('payment.close') }}</t-button>
    </div>
  </t-dialog>
  <TipsDialog
    v-model:showDialog="showTipsDialog"
    :info="tipsDialogInfo"
    @close-dialog="closeTipsDialog"
    @on-agin-buy="onAginBuy"
  />
  <t-loading v-if="isShowLoading" size="small" :loading="loading" showOverlay attach="body" zIndex="8000" />
</template>
<script setup lang="ts" name="paymentDialog">
import { ref, watch, onBeforeUnmount, withDefaults, defineProps } from 'vue';
import QrcodeVue from "qrcode.vue";
import { DialogPlugin, MessagePlugin } from "tdesign-vue-next";
import { useI18n } from "vue-i18n";
import TipsDialog from "./tipsDialog.vue";
import { payCreate, payStatus, payRegion } from "@/api/myOrder/api/index";
import LynkerSDK from '@renderer/_jssdk';
const props = defineProps({
  defaultInvoice:{
		type: Object,
		default: () => {},
	},
  invoiceFlag:{
		type: Number,
    default:1
  },
  customSuccessDialog:{
		type: Boolean,
		default: false,
	},
});
const link = ref("");
const { t } = useI18n();
const emits = defineEmits(["paymentCallback", "getDataList"]);
let timeData = null;
let downTimer = null;
const showTipsDialog = ref(false);
const tipsDialogInfo = ref({});
const resPayRegionDataFlag = ref([]);
const qrcodeDialogFlag = ref(false);
const PayPaddingOkFlag = ref(false);
const tipDialogPayOkFlag = ref(false);
const payWayStr = ref(t("payment.scanCodePayment"));
const times = ref(180);
const time180or300 = ref(180);
const isShowLoading = ref(false);
const paySteta = ref("支付成功");
const isMas = ref(__APP_ENV__.VITE_APP_MAS);
const InAppPurchaseInfo = ref({
  data: {},
  times: 0
})
const payResponseInfo = ref({});
const isApplePay = ref(false);
const addCommasToNumber = (str) => {
  let [integerPart, decimalPart] = str.split(".");
  integerPart = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  if (decimalPart) {
    decimalPart =
      decimalPart.length === 1 ? `${decimalPart}0` : decimalPart.slice(0, 2);
  } else {
    decimalPart = "00";
  }
  return `${integerPart}.${decimalPart}`;
};
const qrcodeDialogClose = () => {
  const confirmDia = DialogPlugin({
    header: "提示",
    theme: "info",
    class: "delmode",
    body: t("payment.orderTip"),
    closeBtn: null,
    zIndex: 9999999999999,

    confirmBtn: t("payment.continuePayment"),
    cancelBtn: t("payment.confirmDeparture"),
    onClose: () => {
      confirmDia.hide();
      qrcodeDialogFlag.value = false;
    },
    onConfirm: () => {
      confirmDia.hide();
    },
  });
};
const rowData = ref({
  sn: "",
  region: 0,
  pay: "",
  amount: 0,
  subTitle:''
  // 下单渠道, 0:其他,1:ios,2:mac,3:mac非应用市场,4:windows,5:安卓,6:web,7:客服
  // orderChannel: 0
});
const isOk = () => {
  emits("paymentCallback", paymentStatus.value);
  tipDialogPayOkFlag.value = false;
};
const outOrderNo = ref("");
const handleCountDown = () => {
  times.value--;
  downTimer = setTimeout(() => {
    if (times.value > 0) {
      handleCountDown();
    } else {
      clearTimeout(downTimer);
    }
  }, 1000);
};
const getLink = (sn, pay, order_type?: 0) =>
  new Promise((resolve, reject) => {
    console.log(props.defaultInvoice,'defaultpropspropsInvoicedefaultInvoicedefaultInvoice');
    payResponseInfo.value = {};
    isApplePay.value = false;
    // 是否是待支付订单
    const isHasOrder = typeof rowData.value.channel !== 'undefined'
    // 是不是mac应用商店的订单 订单为2 苹果应用内的订单
    const isMacOrder = isHasOrder && rowData.value.channel === 2
    // 默认是0
    let payment = 0;
    // 如果不是待支付订单 或者 是待支付订单在mac商店类型包下的单,且当前设备是mac商店类型
    if ((!isHasOrder|| isMacOrder) && isMas.value) {
      payment = 5
    }
    payCreate({
      order_sn: sn,
      client_type: 0,
      payment: payment,
      invoice_header:props.defaultInvoice&&props.invoiceFlag!==1?JSON.stringify(props.defaultInvoice):'',
      order_type
    })
      .then(async (res) => {
        console.log('payCreate:', res, sn, pay);
        if (res.status !== 200) {
          reject(res);
          emits("getDataList", sn);
          qrcodeDialogFlag.value = false;
        } else {
          outOrderNo.value = res.data.data.sn;
          // 苹果设备上处理为苹果支付的订单 或者 不是待支付订单在苹果设备上处理
          if ((isMacOrder || !isHasOrder) && isMas.value) {
            isApplePay.value = true
            const _data = res?.data?.data || {};
            _data.price = rowData.value.amount;
            _data.PRODUCT_IDS = _data.params?.product_ids;
            await handleInAppPurchase(_data);
          } else {
            isApplePay.value = false;
            if (res.data.data.channel === 4) {
              // 拉卡拉
              link.value = res.data.data.params.qr_code;
            } else if (res.data.data.channel === 3) {
              // mypay
              link.value = res.data.data.params.counter_url;
            } else {

            }
          }
          resolve(res);
        }
      })
      .catch((err) => {
        console.log('payCreate err:', err);
        // MessagePlugin.error({
        //   content:err.response?.data?.message || err.message,
        //   zIndex: 999999999,
        // });
        reject(err);
      });
  });

let clickFlag = false;
const openWin = async (row) => {
  if (clickFlag) {
    return;
  }
  clickFlag = true;
  rowData.value = row;
  rowData.value.pay = row.region;
  clearTimeout(downTimer);
  clearTimeout(timeData);
  try {
    const res = await payRegion(row.region);
    resPayRegionDataFlag.value = res.data.data[0].payments;
    if (res.data.data[0].channel === 4) {
      time180or300.value = 300;
    } else {
      time180or300.value = 180;
    }
    times.value = time180or300.value;

    handleCountDown();
    await getLink(row.sn, row.region, row.orderType);
    if (!isApplePay.value) {
      qrcodeDialogFlag.value = true;
    }
    return true;
  } catch (error) {
    if (error.response) {
      MessagePlugin.error(error.response.data.message);
    } else {
      MessagePlugin.error(error.data.message);
    }
    emits("getDataList", row.sn);
    return false;
  } finally {
    clickFlag = false;
  }
};
// 检查支付信息
// 目前只有mac应用内购买 下单前先查询商品是否存在
const checkPayInfo = async (row, isAginBuy?) => {
  if (isMas.value  && row.product_ids) {
    InAppPurchaseInfo.value.data = row
    // 如果不是重新购买 次数就清空
    if (!isAginBuy) {
      InAppPurchaseInfo.value.times = 0;
    }

    console.log('InAppPurchaseInfo12222', InAppPurchaseInfo.value)
    if (InAppPurchaseInfo.value.times >= 2){
      showTipsDialog.value = false;
      setTimeout(() =>{
        showTipsDialog.value = true;
        tipsDialogInfo.value = {
          code: 101
        }
      },0)
      return false;
    }

    const _data = {
      PRODUCT_IDS: row.product_ids,
      onlyCheckProducts: true
    }
    isShowLoading.value = true
    const inAppPurchase = await LynkerSDK.ipcRenderer.invoke('applePay-inAppPurchase', JSON.stringify(_data));
    isShowLoading.value = false
    console.log('checkProducts 查询 inAppPurchase:',  inAppPurchase)
    // 没有查到商品
    if (inAppPurchase.status === 'noProducts'){
      if (inAppPurchase.data.code) {
        showTipsDialog.value = true;
        tipsDialogInfo.value = {
          code: inAppPurchase.data.code
        }
      }
      return false
    }
    return inAppPurchase;
  } else {
    return true
  }
}

defineExpose({
  openWin,
  checkPayInfo
});
onBeforeUnmount(() => {
  clearTimeout(timeData);
  clearTimeout(downTimer);
});
const refsqrCode = async () => {
  if (times.value === 0) {
    await getLink(rowData.value.sn, rowData.value.pay);
    times.value = time180or300.value;

    clearTimeout(downTimer);
    clearTimeout(timeData);

    getLinkStart();
    handleCountDown();
    console.log(
      timeEnd.value - new Date().getTime(),
      "timeEnd.value-new Date().getTime()timeEnd.value-new Date().getTime()"
    );
  }
};
const payStetaTip = ref("");
const paymentStatus = ref(0); // 0待支付 1支付成功 2支付渠道已关闭 3转入退款 4扫码支付已取消 5支付中 6支付失败
const timeEnd = ref(null);
//最大次数
const maxTimes = 20;
// 计算的次数
let countTimes = 0;
const getLinkStart = (cb?) => {
  console.log('getLinkStart', isApplePay.value)
  if (times.value < 1 || (!isApplePay.value && !qrcodeDialogFlag.value)) {
    clearTimeout(timeData);
    return;
  }

  //mac商店类型 超过最大尝试次数
  if(isApplePay.value && countTimes >= maxTimes) {
    cb && cb({error: 'timeout'})
    return
  }
  const data:any = {}
  if (isApplePay.value) {
    data.receipt = payResponseInfo.value.receipt;
  }
  console.log('payStatus data:', data)
  countTimes++;
  payStatus(outOrderNo.value, data)
    .then((res) => {
      console.log('payStatus res:', res)
      const { status } = res.data.data;
      paymentStatus.value = status;
      if (status === 0) {
        timeData = setTimeout(() => {
          getLinkStart(cb);
        }, 3000);
      } else if ([1, 2, 4].includes(status)) {
        cb && cb({status});
        // 非苹果支付的
        if (!isApplePay.value) {
          if (status === 1) {
            paySteta.value = "支付成功";
            qrcodeDialogFlag.value = false;

            emits("paymentCallback", paymentStatus.value);

          } else if (status === 4) {
            paySteta.value = t("payment.paymentErr");
            qrcodeDialogFlag.value = false;
            tipDialogPayOkFlag.value = true;
            emits("paymentCallback", paymentStatus.value);

          } else {
            paySteta.value = t("payment.paymentErr");
            times.value = 0;
          }
        }

        PayPaddingOkFlag.value = false;

        clearTimeout(timeData);
      } else if (status === 5) {
        timeData = setTimeout(() => {
          times.value = time180or300.value;
          getLinkStart(cb);
        }, 3000);
        // 非苹果支付的
        if (!isApplePay.value) {
          if (qrcodeDialogFlag.value) {
            PayPaddingOkFlag.value = true; // 状态5
          }
        }
      } else {
        timeData = setTimeout(() => {
          getLinkStart(cb);
        }, 3000);
      }
    })
    .catch(() => {
      timeData = setTimeout(() => {
        getLinkStart(cb);
      }, 3000);
    });
};
const closeTipsDialog = () => {
  // todothing...
};
const onAginBuy = async () => {
  InAppPurchaseInfo.value.times++;
  await checkPayInfo(InAppPurchaseInfo.value.data, true)
}
// 处理苹果应用内支付
const handleInAppPurchase = async (data) => {
  console.log('苹果支付');
  countTimes = 0;
  isShowLoading.value = true

  // emits("update:allPayDialog", false);
  const inAppInfo = await LynkerSDK.ipcRenderer.invoke('applePay-inAppPurchase', JSON.stringify(data));
  console.log('inAppInfo', inAppInfo)
  if (inAppInfo) {
    if(inAppInfo.status === 'purchased') {
      payResponseInfo.value = {
        receipt: inAppInfo.data.receipt
      }
      // 交易完成
      getPayStatus().then((res:any) => {
        countTimes = 0;
        isShowLoading.value = false
        if (res) {
          if (res.status === 1) {
            showTipsDialog.value = true;
            tipsDialogInfo.value = {
              code: 1,
              pay: rowData.value.pay,
              amount: rowData.value.amount
            }
          } else {
            showTipsDialog.value = true;
            tipsDialogInfo.value = {
              code: 6
            }
          }
        }

      }).catch(e => {
        isShowLoading.value = false
      })
    } else {
      isShowLoading.value = false
      if (inAppInfo.data.code) {
        showTipsDialog.value = true;
        tipsDialogInfo.value = {
          code:  inAppInfo.data.code
        }
      }
    }
  }
}

const getPayStatus = () => {
  return new Promise((reslove, reject) => {
    getLinkStart(({status, error}) => {
      if (error) {
        reject(error)
      } else {
        reslove({
          status
        })
      }
    })
  })
};

watch(
  () => qrcodeDialogFlag.value,
  (newvalue) => {
    if (newvalue) {
      clearTimeout(timeData);
      getLinkStart();
    } else {
      clearTimeout(timeData);
      clearTimeout(downTimer);
      timeData = null;
      downTimer = null;
      times.value = time180or300.value;
    }
  }
);
</script>
<style scoped lang="less">
.payWayStrclass {
  height: 32px;
  width: 80px;
  background: var(--kyy_color_brand_default, #4d5eff);
  border-radius: 0px 0px 8px 0px;
  display: inline-block;
  font-size: 14px;

  font-weight: 400;
  text-align: center;
  color: #ffffff;
  line-height: 32px;
}
.ysf-box {
  margin: 16px auto 0;
  width: 200px;
  height: 58px;
  background: #eeffe8;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  img {
    width: 40px;
    height: 40px;
    margin-right: 8px;
  }
  div {
    font-size: 14px;

    font-weight: 400;
    color: #1c8710;
    line-height: 22px;
  }
}
.pay-text {
  text-align: center;
  width: 100%;
  height: 24px;
  font-size: 16px;

  font-weight: 700;
  text-align: center;
  color: #13161b;
  line-height: 24px;
}
.rotate {
  width: 48px;
  height: 48px;
  margin-bottom: 16px;
  animation: rotate 2s infinite linear;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
:global(.tipQrcodeDialog) {
    z-index: 999999 !important;
}
:deep(.tipQrcodeDialog) {
  :deep(.t-dialog) {
    padding-bottom: 0 !important;
  }
}
:deep(.qrcodeDialog) {
  :deep(.t-dialog__wrap) {
    top: 45px;
  }
}
.play-customer-service {
  height: 22px;
  font-size: 14px;
  font-weight: 400;
  text-align: center;
  color: #717376;
  line-height: 22px;
  margin-bottom: 24px;
}
.paynum {
  height: 24px;
  font-size: 16px;
  font-weight: 700;
  text-align: center;
  color: #da2d19;
  line-height: 24px;
  margin-bottom: 24px;
}
.paysess {
  height: 24px;
  font-size: 16px;
  font-weight: 700;
  text-align: center;
  color: #13161b;
  line-height: 24px;
  margin-bottom: 8px;
}
.qrcodes {
  .foot-boxs {
    width: 300px;
    cursor: pointer;
    height: 72px;
    border: 1px solid #e3e6eb;
    border-radius: 4px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 24px;
    margin-left: 16px;
    img {
      width: 32px;
      height: 32px;
      margin-right: 8px;
    }
    div {
      height: 22px;
      font-size: 14px;

      font-weight: 400;
      color: #13161b;
      line-height: 22px;
    }
  }
  .zf-box {
    display: flex;
    align-items: center;
    justify-content: center;

    .zfb-box {
      display: flex;
      align-items: center;
      img {
        width: 112px;
        height: 32px;
      }
      .labzfb {
        height: 12px;
        font-size: 8px;

        font-weight: 400;
        text-align: left;
        color: #13161b;
        line-height: 12px;
      }
    }
  }
  .tipsqrcod {
    text-align: center;
    font-size: 14px;
    margin-bottom: 8px;

    font-weight: 400;
    color: var(--text-kyy-color-text-2, #516082);
    line-height: 22px;
    margin-top: 24px;
  }
  .tips {
    text-align: center;
    height: 22px;
    font-size: 14px;
    margin-top: -10px;
    margin-bottom: 16px;

    font-weight: 400;
    color: var(--text-kyy-color-text-2, #516082);
    line-height: 22px;
    span {
      padding: 0 0 0 3px;
      color: #da2d19;
    }
  }
  .expires {
    position: relative;
  }
  .expires::after {
    content: "获取失败，点击重新获取二维码";
    width: 135px;
    height: 135px;
    background: var(
      --bg-kyy-color-bg-software-lose-foucs,
      rgba(20, 26, 66, 0.8)
    );
    text-align: center;
    color: #fff;
    position: absolute;
    top: 0;
    left: 0;
    border-radius: 4px;
    display: flex;
    align-items: center;
  }
  .qrcode-img-box {
    width: 136px;
    margin: 0 auto;
    height: 136px;
    border: 1px solid var(--border-kyy-color-border-default, #d5dbe4);
    border-radius: 4px;
    position: relative;
    /* text-align: center; */
    padding: 3px;
  }
}

.lins {
  width: 644px;
  height: 12px;
  background: #e3e6eb;
  border-radius: 5px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  .lins-cneter {
    width: 636px;
    height: 6px;
    background: #5f7292;
    border-radius: 3px;
  }
}
.foot-boxs:last-child {
  margin-left: 0;
}
.foot-boxs:first-child {
  margin-left: 0;
}


.body-box {
  width: 632px;
  margin: -5px auto;
  height: 360px;
  background: #ffffff;
  border-radius: 0 0 4px 4px;
  position: relative;
  padding-bottom: 24px;
  box-shadow: 0px 1px 4px 0px rgb(19 22 27 / 16%);
}

.headM {
  margin-bottom: 25px;
  text-align: center;
  .num {
    height: 24px;
    font-size: 16px;
    font-weight: 700;
    text-align: left;
    color: #da2d19;
    line-height: 24px;
  }
  span {
    height: 22px;
    font-size: 14px;

    font-weight: 400;
    text-align: right;
    color: #13161b;
    line-height: 22px;
  }
  .mar40{
    margin-right: 40px;
  }
  .mr0{
    margin-right: 0;
  }
}
</style>
