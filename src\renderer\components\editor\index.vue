<template>
  <div style="height: 100%">
    <!-- :title="props.title" -->
    <lk-editor
      ref="lkEditorRef"
      :hasTitle="hasTitle"
      :editor-type="props.editorType || 'A'"
      :options="props.options"
      :maxlength="props.maxlength === false ? undefined : (typeof props.maxlength === 'number' ? props.maxlength : 10000) "
      @image="onUploadImage"
      @goLink="onGoLink"
      @annex="onAnnex"
      @previewImage="onPreviewImage"
      @update="onContentUpdate"
      @annexEvent="onAnnexEvent"
      @copyFile="onCopyFile"
      @copyUrl="onCopyUrl"
      @offlineCopy="onOfflineCopy"
      @on-max-length="handleMaxLength"
      :on-cropper="onCropper"
    />

    <div class="upload-control">
      <image-upload ref="imageUploadRef" :rootDir="rootDir" @on-select-change="onUploadSelectChange" @success="onUploadImageSuccess" />
      <file-upload ref="fileUploadRef" :rootDir="rootDir" @on-select-change="onUploadSelectChange" @success="onUploadFileSuccess" />
    </div>
  </div>
</template>

<script setup lang="ts">
import lodash, { head } from 'lodash';
import { ref, onMounted, onUnmounted, PropType } from "vue";
import { isLink } from "@/views/zhixing/util";
import { MessagePlugin } from "tdesign-vue-next";
import { previewFileWithErrorTips } from "@/utils/wps";
import ImageUpload from "@/components/editor/components/ImageUpload.vue";
import FileUpload from "@/components/editor/components/FileUpload.vue";
import { blobToPngFile } from "@/views/square/utils/upload";
import { PutObjectResult } from "ali-oss";
import { useI18n } from "vue-i18n";
import { editorImageUpload } from "@/components/editor/utils";
import { getFilePreviewId } from "@/views/message/service/request";
import { fileTypes } from "@/views/zhixing/constant";
import { getImageBlobFromUrl } from "@/utils/imageHelper";
import LynkerSDK from "@renderer/_jssdk";


const { t } = useI18n();

const props = defineProps({
  editorType: {
    type: String as PropType<'A' | 'B' | 'C' | 'D'>,
    default: () => 'A',
  },
  maxlength: {
    type: [Number, Boolean] as PropType<number | boolean>,
    default: undefined,
  },
  rootDir: {
    required: true,
  },
  isTitle:{
    type: String,
    default: '',
  },
  options: {
    type: Object,
    default: () => ({}),
  },
  hasTitle: {
    type: Boolean,
    default: false,
  },
  type: {
    type: String,
    required: true,
  },
});

const emit = defineEmits(["update", "imgInsert", "fileInsert", "onMaxLength"]);

const lkEditorRef = ref(null);
const imageUploadRef = ref(null);
const fileUploadRef = ref(null);

// 触发图片上传
const onUploadImage = () => {
  console.error('11111')
  imageUploadRef.value.upload();
};

// 图片上传成功
const onUploadImageSuccess = (image) => {
  // 以下为0.3.0-alpha.30
  // lkEditorRef.value.insertImage(image.url);
  // 以下为0.3.1-alpha.9
  lkEditorRef.value.setImage({
    url: image.url,
  });
  emit("imgInsert", [image]);
};

// 触发附件上传
const onAnnex = () => {
  fileUploadRef.value.upload();
};

// 选择文件或图片之后，上传之前，触发该事件
const onUploadSelectChange = async () => {
  const win = await LynkerSDK.getCurrentWindow();
  const bvs = win?.getBrowserViews();
  bvs?.forEach((bv) => {
    bv?.webContents?.focus();
  });
  setTimeout(() => {
    lkEditorRef.value.getQuill().focus();
  }, 1000)
}

// 附件上传成功
const onUploadFileSuccess = async (file) => {
  // 以下为0.3.1-alpha.9
  const quillEle = lkEditorRef.value.getQuill();
  const length = quillEle.selection.savedRange.index;
  const scrollLength = length + 2;

  const { file_id } = await getFilePreviewId(file.url, file.name, file.size);
  const customObj = {
    status: 'success',
    progress: 100,
    name: file.name,
    type: fileTypes.find((fileType) => file.name.split(".").pop().toLowerCase() === fileType),
    size: file.size,
    url: file.url,
    file_id,
  };
  // quillEle.insertEmbed(length, "custom", customObj);
  // quillEle.setSelection(scrollLength);
  lkEditorRef.value.setFile(customObj, length)
  emit("fileInsert", [file]);

  // 以下为0.3.0-alpha.30
  // const quillEle = lkEditorRef.value.getQuill();
  // const length = quillEle.selection.savedRange.index;
  // const scrollLength = length + 2;

  // const { file_id } = await getFilePreviewId(file.url, file.name, file.size);
  // const customObj = {
  //   name: file.name,
  //   type: fileTypes.find((fileType) => file.name.split(".").pop().toLowerCase() === fileType),
  //   size: file.size,
  //   url: file.url,
  //   file_id,
  // };
  // quillEle.insertEmbed(length, "custom", customObj);
  // quillEle.setSelection(scrollLength);
  // emit("fileInsert", [file]);
};

// 触发预览图片
const onPreviewImage = ({ images, index, url }: { images: any[]; index: number; url: string }) => {
  const imgList = images.length
    ? images.map((image) => ({
        url: image,
        type: "jpg",
        imgIndex: index,
      }))
    : [{ url, type: "jpg" }];

  LynkerSDK.ipcRenderer.invoke("preview-file", JSON.stringify(imgList));
};

// 编辑器点击链接地址
const onGoLink = (link) => {
  // electron 跳转外部浏览器
  if (isLink(link)) return LynkerSDK.shell.openExternal(link);
  try {
    LynkerSDK.shell.openExternal("http://" + link);
  } catch (error) {
    console.log("error", error);
    MessagePlugin.error(t("editor.invalidLink"));
  }
};

// 附件事件注册
const onAnnexEvent = (event, type: string, data) => {
  const { url, name, size, file_id, type: dataType } = data;

  if (type === "preview") {
    // 预览事件
    if (dataType?.includes("video")) {
      LynkerSDK.ipcRenderer.invoke(
        "preview-file",
        JSON.stringify({
          url,
          type: "MP4", // Use 'MP4' directly as it seems intended
          title: name,
          size: Math.ceil(size / 1024),
        }),
      );
      return;
    }
    previewFileWithErrorTips(file_id, dataType);
  } else if (type === "download") {
    // 下载事件
    LynkerSDK.ipcRenderer.invoke("download-file", {
      title: name,
      url,
    });
  }
};

// 复制图片到富文本
const onCopyFile = async (event, type: string, base64: string, file: any) => {
  if (type === "image" || file.type.split("/")[0] === "image") {
    let url = '';
    if (base64) {
      console.error('aaaaaaa', base64)
      const data = await fetch(base64);
      const blob = await data.blob();
      const { response } = (await editorImageUpload(
        blobToPngFile(blob, new Date().getTime()),
        props.rootDir,
      )) as PutObjectResult;
      url = response.url;
    } else {
      const { response } = (await editorImageUpload(
        file,
        props.rootDir,
      )) as PutObjectResult;
      url = response.url;
    }


    const imgObj = {
      name: file.name,
      size: file.size,
      type: file.type.split("/")[1],
      url: url,
    };
    emit("imgInsert", [imgObj]);
    // 以下为0.3.0-alpha.30
    // lkEditorRef.value.insertImage(url);
    // 以下为0.3.1-alpha.9
    lkEditorRef.value.setImage({
      url: url,
    });
  }
};

// 复制图片链接到富文本
const onCopyUrl = (() => {
  const obj = {};
  return async (url, key) => {
    let imageUrl = ``;
    let imgObj = {
      name: '',
      type: "png",
      url: '',
    };
    if (/^(http|https|base)/i.test(url)) {
      const blob = await getImageBlobFromUrl(url);
      console.error('aaaaaaa', blob)
      const { response } = (await editorImageUpload(
        blobToPngFile(blob, new Date().getTime()),
        props.rootDir,
      )) as PutObjectResult;
      imageUrl = response.url;
      imgObj = {
        name: response.name,
        type: "png",
        url: response.url,
      };
    } else {
      await new Promise((resolve) => {
        setTimeout(() => {
          resolve(true)
        }, 500)
      })
      // return;
      /**
       * 如图无法上传，使用占未图
       */
      imageUrl = `https://kuaiyouyi.oss-cn-shenzhen.aliyuncs.com/introduce/ac85b54/1734504214746.png`
      imgObj = {
        name: 'default-image',
        type: "png",
        url: imageUrl,
      };
    }


    obj[key] = imageUrl;
    emit("imgInsert", [imgObj]);

    // 替换占位符
    const updatedDelta = lkEditorRef.value.getContents().ops.map((op) => {
      for (const _key in obj) {
        const element = obj[_key];
        console.error('xxaaaxx', op.insert?.["image-placeholder"], _key)
        if (op.insert?.["image-placeholder"] === _key) {
          // 找到占位符并替换为图片
          return {
            insert: { image: element },
          };
        }
      }
      // 检查是否为占位符并匹配唯一 key

      return op; // 其他内容不变
    });
    /**
     * fix https://www.tapd.cn/69781318/bugtrace/bugs/view/1169781318001046721
     */
    const index = lkEditorRef.value.getQuill().getSelection()?.index;
    setTimeout(() => {
      lkEditorRef.value.getQuillEditor().setContents(updatedDelta);
      lkEditorRef.value.getQuill().setSelection(index + 2);
      setTimeout(() => {
        lkEditorRef.value.getQuill().scrollSelectionIntoView();
      }, 200)
    }, 300)
  }
})();

const onCropper = async (url: string) => {
  let blob = null;
  // if (url.includes('data:image/png;base64,')) {
  //   blob = url;
  // } else if (/^(http|https|base)/i.test(url)) {
  //   blob = await getImageBlobFromUrl(url);
  // }
  blob = await getImageBlobFromUrl(url);
  const { response } = (await editorImageUpload(
    blobToPngFile(blob, new Date().getTime()),
    props.rootDir,
  )) as PutObjectResult;
  const imageUrl = response.url;
  return imageUrl;
}

// 断网情况下复制
const onOfflineCopy = async () => {
  if (!navigator.onLine) {
    await MessagePlugin.warning(t("editor.noNetwork"));
  }
};

const handleMaxLength = (() => {
  let key;
  return () => {
    emit("onMaxLength");
    if (!key) {
      key = MessagePlugin.error({
        content: t("zx.other.maxLength", {length: typeof props.maxlength === 'number' ? props.maxlength : 10000}),
        onClose: () => {
          key = undefined;
        }
      });
    }
  }
})()

// 富文本内容更新
const onContentUpdate = (delta) => {
  console.error('xxxxonContentUpdate', delta)
  delta.ops = delta.ops.filter((op) => !op.insert?.["image-placeholder"]); // 过滤图片占位元素
  emit("update", delta);
};

let resizeObserver = null;

const handleResize = (entry: ResizeObserverEntry) => {
  const target = entry.target;
  // HACK: 当将富文本内容滚动到底部，再压缩内容高度，根据滚动条计算的高度会有差值
  const tolerance = 30;

  // 使用 requestAnimationFrame 确保在浏览器下一次重绘前执行
  requestAnimationFrame(() => {
    // 检查滚动条是否在底部
    if (target.scrollTop + target.clientHeight >= target.scrollHeight - tolerance) {
      // 如果在底部，保持滚动条在底部
      target.scrollTop = target.scrollHeight - target.clientHeight;
    }
  });
};

onMounted(() => {
  setTimeout(() => {
    const editor = lkEditorRef.value?.$el.querySelector(".ql-editor");
    if (editor) {
      // 检测富文内容区的高度变化，并保持滚动条在合理位置
      resizeObserver = new ResizeObserver((entries) => {
        entries.forEach(handleResize);
      });
      resizeObserver.observe(editor);
    }
  }, 1000);
});

onUnmounted(() => {
  if (resizeObserver) {
    // 清理观察者，以防止内存泄漏
    resizeObserver.disconnect();
  }
});

defineExpose({
  getQuill: () => lkEditorRef.value.getQuill(),
  getHtml: () => lkEditorRef.value.getHtml(),
  setHtml: (content) => {
    lkEditorRef.value.setHtml(content);
  },
  restTitleInput:()=>{
    console.log(lkEditorRef.value,'啊实打实大师大');

    lkEditorRef.value.restTitleInput();
  },
  renderContent: (values: any, gotoLast = false) => {
    lkEditorRef.value.renderContent(values, gotoLast);
  },
  disableEditor: (isEnable: boolean) => {
    lkEditorRef.value.disableEditor(isEnable);
  },
});
</script>

<style lang="less" scoped>
.upload-control {
  position: absolute;
  left: 0;
  top: 0;
  z-index: -1;
  opacity: 0;
}
</style>
